// Minimal test server to verify WhatsApp webhook functionality
require('dotenv').config();

// Set minimal required environment variables
process.env.WHATSAPP_PHONE_NUMBER_ID = process.env.WHATSAPP_PHONE_NUMBER_ID || 'test-phone-id';
process.env.WHATSAPP_ACCESS_TOKEN = process.env.WHATSAPP_ACCESS_TOKEN || 'test-token';
process.env.WHATSAPP_VERIFY_TOKEN = process.env.WHATSAPP_VERIFY_TOKEN || 'test-verify-token';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret';
process.env.SESSION_SECRET = process.env.SESSION_SECRET || 'test-session-secret';

const express = require('express');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = process.env.TEST_PORT || 3001;

// Basic middleware
app.use(cookieParser());
app.use(express.json());

// Import WhatsApp routes
const whatsappRoutes = require('./routes/whatsapp');

// Register WhatsApp routes
app.use('/api/whatsapp', whatsappRoutes);

// Test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'Test server is running', timestamp: new Date().toISOString() });
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    message: error.message,
    path: req.path
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ 
    error: 'Not found',
    path: req.path,
    method: req.method
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`WhatsApp webhook available at: http://localhost:${PORT}/api/whatsapp/webhook`);
  console.log(`Test endpoint available at: http://localhost:${PORT}/test`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

module.exports = app;
