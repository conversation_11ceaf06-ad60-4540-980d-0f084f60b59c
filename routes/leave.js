const express = require("express");
const router = express.Router();
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const LeaveRequest = require("../models/LeaveRequest");
const { ensureAuthenticated, ensureEmployee } = require("../middleware/auth");
const csrf = require("csurf");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Get leave request page
router.get("/request", ensureAuthenticated, ensureEmployee, async (req, res) => {
  try {
    const employee = req.user;
    const company = req.company;

    // Get all active leave types for the company
    const leaveTypes = await LeaveType.find({
      company: company._id,
      isActive: true,
    }).sort("name");

    // Get leave balances for the employee
    const leaveBalances = await LeaveBalance.find({
      employee: employee._id,
      year: new Date().getFullYear(),
    }).populate("leaveType");

    res.render("leave/request", {
      company,
      employee,
      leaveTypes,
      leaveBalances,
      title: "Request Leave",
    });
  } catch (error) {
    console.error("Error loading leave request page:", error);
    req.flash("error", "Failed to load leave request page");
    res.redirect("/dashboard");
  }
});

// Get employee leave requests
router.get("/requests/:employeeId", ensureAuthenticated, async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { startDate, endDate } = req.query;

    // Ensure user can only access their own leave requests or has admin rights
    if (!req.user.isAdmin && req.user._id.toString() !== employeeId) {
      return res.status(403).json({
        success: false,
        message: "You are not authorized to access this resource",
      });
    }

    // Build query
    const query = { employee: employeeId };
    if (startDate && endDate) {
      query.startDate = { $gte: new Date(startDate) };
      query.endDate = { $lte: new Date(endDate) };
    }

    const leaveRequests = await LeaveRequest.find(query)
      .populate("leaveType")
      .sort("-createdAt");

    res.json({
      success: true,
      leaveRequests,
    });
  } catch (error) {
    console.error("Error fetching leave requests:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch leave requests",
    });
  }
});

// Submit leave request
router.post("/request", ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log("CSRF Token:", req.csrfToken());
  console.log(
    "User:",
    req.user ? { id: req.user._id, email: req.user.email } : "No user"
  );

  try {
    const {
      leaveType,
      startDate,
      endDate,
      reason,
      contactDetails,
      halfDayStart,
      halfDayEnd,
    } = req.body;

    // Validate inputs
    if (!leaveType || !startDate || !endDate) {
      req.flash("error", "Please provide all required fields");
      return res.redirect("/leave/request");
    }

    // Create new leave request
    const newLeaveRequest = new LeaveRequest({
      employee: req.user._id,
      leaveType,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      reason,
      contactDetails,
      halfDayStart: halfDayStart === "on",
      halfDayEnd: halfDayEnd === "on",
      status: "pending",
    });

    await newLeaveRequest.save();

    req.flash("success", "Leave request submitted successfully");
    res.redirect("/dashboard");
  } catch (error) {
    console.error("Error submitting leave request:", error);
    req.flash("error", "Failed to submit leave request");
    res.redirect("/leave/request");
  }
});

// Get leave overview page
router.get("/overview", ensureAuthenticated, async (req, res) => {
  try {
    const company = req.company;
    const employee = req.user;
    
    // Get leave types for the company
    const leaveTypes = await LeaveType.find({
      company: company._id,
      isActive: true,
    });
    
    // Get leave balances for the employee
    const leaveBalances = await LeaveBalance.find({
      employee: employee._id,
      year: new Date().getFullYear(),
    }).populate("leaveType");
    
    // Get pending leave requests
    const pendingRequests = await LeaveRequest.find({
      employee: employee._id,
      status: "pending",
    }).populate("leaveType");
    
    // Get approved leave requests
    const approvedRequests = await LeaveRequest.find({
      employee: employee._id,
      status: "approved",
    }).populate("leaveType");
    
    res.render("employeeLeave", {
      title: "Leave Overview",
      company,
      employee,
      leaveTypes,
      leaveBalances,
      pendingRequests,
      approvedRequests
    });
  } catch (error) {
    console.error("Error loading leave overview page:", error);
    req.flash("error", "Failed to load leave overview");
    res.redirect("/dashboard");
  }
});

// Export the router
module.exports = router;
