const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });

router.use(csrfProtection);

// Map form types to their templates
const formTemplates = {
  // Income Forms
  "annual-bonus": "onceOffAnnualBonus",
  "annual-payment": "onceOffAnnualPayment",
  "arbitration-award": "onceOffArbitrationAward",
  "dividends-subject": "onceOffDividendsSubject",
  "extra-pay": "onceOffExtraPay",
  "leave-paid-out": "onceOffLeavePaidOut",
  commission: "onceOffCommission",
  "restraint-of-trade": "onceOffRestraintOfTrade",

  // Allowance Forms
  "computer-allowance": "onceOffComputerAllowance",
  "phone-allowance": "onceOffPhoneAllowance",
  "tool-allowance": "onceOffToolAllowance",
  "uniform-allowance": "onceOffUniformAllowance",
  "relocation-allowance": "onceOffRelocationAllowance",
  "subsistence-international": "onceOffSubsistenceAllowanceInternational",
  "subsistence-local": "onceOffSubsistenceAllowanceLocal",
  "broad-based-share-plan": "onceOffBroadBasedEmployeeSharePlan",
  "expense-claim": "onceOffExpenseClaim",
  "gain-vesting": "onceOffGainVesting",

  // Benefit Forms
  "medical-costs": "onceOffMedicalCosts",
  "bursaries-scholarships": "onceOffBursariesAndScholarships",
  "employee-debt-benefit": "onceOffEmployeeDebtBenefit",

  // Deduction Forms
  donations: "onceOffDonations",
  "repayment-advance": "onceOffRepaymentOfAdvance",
  "staff-purchases": "onceOffStaffPurchases",

  // Other Forms
  "covid-relief": "onceOffCovid19DisasterRelief",
  "long-service-award": "onceOffLongServiceAward",
  "ters-payout": "onceOffTersPayout",
  "termination-lump-sums": "onceOffTerminationLumpSums",
};

// GET route for displaying forms
router.get(
  "/clients/:companyCode/employeeProfile/:employeeId/:formType",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { formType } = req.params;
      const template = formTemplates[formType];

      if (!template) {
        console.error(`Unknown form type: ${formType}`);
        req.flash("error", `Unsupported form type: ${formType}`);
        return res.redirect("/");
      }

      // Render the appropriate template
      res.render(template, {
        csrfToken: req.csrfToken(),
        formType,
        // ... other necessary data
      });
    } catch (error) {
      console.error("Error in GET form route:", error);
      req.flash("error", "An error occurred while loading the form");
      res.redirect("/");
    }
  }
);

// POST route for form submissions
router.post(
  "/clients/:companyCode/employeeProfile/:employeeId/:formType",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { formType } = req.params;

      if (!formTemplates[formType]) {
        throw new Error(`Unsupported form type: ${formType}`);
      }

      // Process the form data based on type
      // ... handle form submission logic

      res.json({
        success: true,
        message: "Form submitted successfully",
      });
    } catch (error) {
      console.error("Error in POST form route:", error);
      res.status(500).json({
        error: "An error occurred while processing the form",
        details: error.message,
      });
    }
  }
);

module.exports = router;
