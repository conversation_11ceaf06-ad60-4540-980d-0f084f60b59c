const express = require("express");
const router = express.Router();
const User = require("../models/user");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const speakeasy = require("speakeasy");
const QRCode = require("qrcode");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });

// Setup 2FA
router.post(
  "/2fa/setup",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id);
      if (!user) {
        return res
          .status(404)
          .json({ success: false, error: "User not found" });
      }

      // Generate new secret
      const secret = speakeasy.generateSecret({
        name: `Panda Software Solutions Group (${user.email})`,
      });

      // Generate QR code
      const qrCode = await QRCode.toDataURL(secret.otpauth_url);

      // Store secret temporarily (you might want to store this in session instead)
      user.twoFactorSecret = secret.base32;
      await user.save();

      res.json({
        success: true,
        qrCode,
        secret: secret.base32,
      });
    } catch (error) {
      console.error("Error setting up 2FA:", error);
      res.status(500).json({
        success: false,
        error: "Failed to setup 2FA",
      });
    }
  }
);

// Verify 2FA token
router.post(
  "/2fa/verify",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { token, secret } = req.body;
      const user = await User.findById(req.user._id);

      if (!user) {
        return res
          .status(404)
          .json({ success: false, error: "User not found" });
      }

      // Verify token
      const verified = speakeasy.totp.verify({
        secret: secret,
        encoding: "base32",
        token: token,
        window: 2,
      });

      if (verified) {
        user.twoFactorEnabled = true;
        await user.save();
        res.json({ success: true });
      } else {
        res.status(400).json({
          success: false,
          error: "Invalid verification code",
        });
      }
    } catch (error) {
      console.error("Error verifying 2FA:", error);
      res.status(500).json({
        success: false,
        error: "Failed to verify 2FA",
      });
    }
  }
);

// Disable 2FA
router.post(
  "/2fa/disable",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id);
      if (!user) {
        return res
          .status(404)
          .json({ success: false, error: "User not found" });
      }

      user.twoFactorEnabled = false;
      user.twoFactorSecret = null;
      await user.save();

      res.json({ success: true });
    } catch (error) {
      console.error("Error disabling 2FA:", error);
      res.status(500).json({
        success: false,
        error: "Failed to disable 2FA",
      });
    }
  }
);

module.exports = router;
