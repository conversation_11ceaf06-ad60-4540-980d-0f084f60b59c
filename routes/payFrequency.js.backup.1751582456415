const express = require("express");
const router = express.Router();
const Company = require("../models/Company");
const PayrollPeriod = require("../models/PayrollPeriod");
const { ensureAuthenticated } = require("../config/auth");
const moment = require("moment");
const PayFrequency = require("../models/PayFrequency");
const { generatePayrollPeriods } = require("../utils/payrollCalculations");
const DEFAULT_TIMEZONE = "Africa/Johannesburg";
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const csrf = require("csurf");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Apply CSRF protection to all routes
router.use(csrfProtection);

router.get("/new", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany)
      .lean()
      .maxTimeMS(3000);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/");
    }

    res.render("pay-frequency-new", {
      company,
      editMode: false,
      messages: req.flash(),
      csrfToken: req.csrfToken(),
    });
  } catch (error) {
    console.error("Error loading new pay frequency form:", error);
    req.flash("error", "Error loading form");
    res.redirect("/");
  }
});

router.post(
  "/create",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("Pay frequency creation request received:", req.body);

      const {
        frequency,
        lastDayOfPeriod,
        interimDay,
        firstPayrollPeriodEndDate,
        companyCode,
        _csrf,
      } = req.body;

      // Validate required fields
      if (!frequency) {
        return res.status(400).json({ error: "Frequency is required" });
      }
      if (!lastDayOfPeriod) {
        return res.status(400).json({ error: "Last day of period is required" });
      }
      if (!firstPayrollPeriodEndDate) {
        return res.status(400).json({ error: "First payroll period end date is required" });
      }
      if (!companyCode) {
        return res.status(400).json({ error: "Company code is required" });
      }

      // Find company by company code
      const company = await Company.findOne({ companyCode })
        .lean()
        .maxTimeMS(3000);
      if (!company) {
        console.error(`Company not found for code: ${companyCode}`);
        return res.status(404).json({ error: "Company not found" });
      }

      console.log(`Creating pay frequency for company: ${company.name} (${companyCode})`);

      const payFrequencyData = {
        company: company._id,
        companyCode: companyCode,
        name: `${
          frequency.charAt(0).toUpperCase() + frequency.slice(1)
        }, ending on ${lastDayOfPeriod}${
          frequency === "bi-weekly" ? ` and ${interimDay}` : ""
        }`,
        frequency,
        lastDayOfPeriod,
        interimDay: frequency === "bi-weekly" ? interimDay : undefined,
        firstPayrollPeriodEndDate: moment
          .tz(firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
          .endOf("day")
          .toDate(),
      };

      console.log("Pay frequency data to save:", payFrequencyData);

      const newPayFrequency = new PayFrequency(payFrequencyData);
      await newPayFrequency.save({ maxTimeMS: 3000 });

      console.log("Pay frequency created successfully:", newPayFrequency._id);

      res.status(200).json({
        message: "Pay frequency added successfully",
        redirect: `/clients/${companyCode}/settings/payroll/pay-frequencies`,
      });
    } catch (error) {
      console.error("Error adding pay frequency:", error);
      console.error("Error stack:", error.stack);

      // Provide more specific error messages
      let errorMessage = "An error occurred while adding pay frequency";
      let statusCode = 500;

      if (error.name === 'ValidationError') {
        errorMessage = "Validation failed: " + Object.values(error.errors).map(e => e.message).join(', ');
        statusCode = 400;
      } else if (error.message.includes('duplicate key')) {
        errorMessage = "A pay frequency with similar settings already exists";
        statusCode = 409;
      }

      res.status(statusCode).json({
        error: errorMessage,
        details: error.message,
      });
    }
  }
);

router.get("/periods", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findOne({ owner: req.user._id })
      .lean()
      .maxTimeMS(3000);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    const periods = await PayrollPeriod.find({ company: company._id })
      .sort({
        startDate: 1,
      })
      .lean()
      .maxTimeMS(3000);
    res.status(200).json({ periods });
  } catch (error) {
    console.error("Error fetching payroll periods:", error);
    res
      .status(500)
      .json({ error: "An error occurred while fetching payroll periods" });
  }
});

router.get("/view", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findOne({ owner: req.user._id })
      .lean()
      .maxTimeMS(3000);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    res.render("view-pay-frequency", { company });
  } catch (error) {
    console.error("Error fetching pay frequency settings:", error);
    res.status(500).json({
      error: "An error occurred while fetching pay frequency settings",
    });
  }
});

router.get("/edit", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findOne({ owner: req.user._id })
      .lean()
      .maxTimeMS(3000);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    res.render("pay-frequency-new", { company, editMode: false });
  } catch (error) {
    console.error("Error fetching pay frequency settings for edit:", error);
    res.status(500).json({
      error: "An error occurred while fetching pay frequency settings for edit",
    });
  }
});

// GET route for editing a specific pay frequency
router.get(
  "/edit/:id",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      // Set a timeout for database operations
      const payFrequency = await PayFrequency.findById(req.params.id)
        .lean()
        .maxTimeMS(3000);
        
      if (!payFrequency) {
        return res
          .status(404)
          .render("error", { message: "Pay frequency not found" });
      }

      const company = await Company.findById(payFrequency.company)
        .lean()
        .maxTimeMS(3000);
        
      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      res.render("pay-frequency-new", {
        company,
        payFrequency,
        editMode: true,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error fetching pay frequency for edit:", error);
      res
        .status(500)
        .render("error", { message: "Error loading pay frequency for edit" });
    }
  }
);

// POST route for updating a specific pay frequency
router.post("/edit/:id", ensureAuthenticated, async (req, res) => {
  try {
    const {
      frequency,
      lastDayOfPeriod,
      interimDay,
      firstPayrollPeriodEndDate,
    } = req.body;

    // Convert the date to end of day in the correct timezone
    const adjustedDate = moment
      .tz(firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
      .endOf("day")
      .toDate();

    const payFrequency = await PayFrequency.findByIdAndUpdate(
      req.params.id,
      {
        frequency,
        lastDayOfPeriod,
        interimDay,
        firstPayrollPeriodEndDate: adjustedDate, // Use the adjusted date
      },
      { new: true, maxTimeMS: 3000 }
    );

    if (!payFrequency) {
      return res.status(404).json({ error: "Pay frequency not found" });
    }

    const company = await Company.findById(payFrequency.company)
      .lean()
      .maxTimeMS(3000);

    res.status(200).json({
      message: "Pay frequency updated successfully",
      payFrequency,
      redirect: `/clients/${company.companyCode}/settings/payroll/pay-frequencies`,
    });
  } catch (error) {
    console.error("Error updating pay frequency:", error);
    res
      .status(500)
      .json({ error: "An error occurred while updating pay frequency" });
  }
});

// Add this new route for deleting a specific pay frequency
router.delete(
  "/delete/:id",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const payFrequency = await PayFrequency.findById(req.params.id)
        .lean()
        .maxTimeMS(3000);
      if (!payFrequency) {
        return res.status(404).json({ error: "Pay frequency not found" });
      }

      // Check if any employees are using this pay frequency
      const employeesUsingFrequency = await Employee.find({
        payFrequency: req.params.id,
      })
        .lean()
        .maxTimeMS(3000);
      if (employeesUsingFrequency.length > 0) {
        return res.status(400).json({
          error: "Cannot delete pay frequency that is being used by employees",
        });
      }

      await PayFrequency.findByIdAndDelete(req.params.id)
        .maxTimeMS(3000);
      res.json({ message: "Pay frequency deleted successfully" });
    } catch (error) {
      console.error("Error deleting pay frequency:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.get("/pay-frequencies", async (req, res) => {
  try {
    const payFrequencies = await PayFrequency.find({
      company: req.user.company,
    })
      .lean()
      .maxTimeMS(3000);
    res.render("settings/pay-frequencies", {
      payFrequencies,
      company: req.user.company, // Make sure to pass the company object if it's used in the template
    });
  } catch (error) {
    console.error("Error fetching pay frequencies:", error);
    res.status(500).send("An error occurred while fetching pay frequencies");
  }
});

router.post(
  "/update-employee-frequency",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, payFrequencyId } = req.body;

      // Validate the payFrequencyId
      if (!mongoose.Types.ObjectId.isValid(payFrequencyId)) {
        return res.status(400).json({ error: "Invalid pay frequency ID" });
      }

      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        { payFrequency: payFrequencyId },
        { new: true, runValidators: true, maxTimeMS: 3000 }
      );

      if (!updatedEmployee) {
        return res.status(404).json({ error: "Employee not found" });
      }

      res.json(updatedEmployee);
    } catch (error) {
      console.error("Error updating employee pay frequency:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// Add this route to handle the pay frequencies list page
router.get(
  "/clients/:companyCode/settings/payroll/pay-frequencies",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const payFrequencies = await PayFrequency.find({ company: company._id })
        .lean()
        .maxTimeMS(3000);

      res.render("settings/pay-frequencies", {
        company,
        payFrequencies,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error fetching pay frequencies:", error);
      req.flash("error", "Error loading pay frequencies");
      res.redirect("/");
    }
  }
);

// Duplicate route removed - using the one at the top of the file

module.exports = router;
