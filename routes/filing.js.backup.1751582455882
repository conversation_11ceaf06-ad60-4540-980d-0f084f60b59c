const express = require("express");
const router = express.Router({ mergeParams: true });
const Filing = require("../models/filing");
const Payroll = require("../models/Payroll");
const PayrollPeriod = require("../models/PayrollPeriod");
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const checkCompany = require("../middleware/checkCompany");
const dateUtils = require("../utils/dateUtils");
const payrollCalculations = require("../utils/payrollCalculations");
const { validationResult } = require("express-validator");
const createError = require("http-errors");
const mongoose = require("mongoose");
const helpers = require("../utils/helpers");
const moment = require("moment");
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

// Helper function to check for missing employee fields
function getMissingFields(employee) {
  const missingFields = [];

  if (!employee.taxNumber) {
    missingFields.push("Tax Number");
  }
  if (!employee.idNumber) {
    missingFields.push("ID Number");
  }
  if (!employee.dateOfBirth) {
    missingFields.push("Date of Birth");
  }
  if (!employee.residentialAddress) {
    missingFields.push("Residential Address");
  }

  return missingFields;
}

// Apply middleware to all routes
router.use(ensureAuthenticated);
// router.use(checkCompany); // Temporarily disabled for debugging

// Main filing page route for clients
router.get('/:companyCode/filing', ensureAuthenticated, async (req, res, next) => {
  try {
    const { companyCode } = req.params;
    console.log("\n=== Filing Route Debug - Extended ===");
    console.log("Company Code:", companyCode);
    console.log("User Object:", {
      _id: req.user._id,
      companies: req.user.companies.map(c => typeof c === 'object' ? {
        _id: c._id?.toString(),
        companyCode: c.companyCode,
        name: c.name
      } : c.toString())
    });

    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.log("Company not found:", companyCode);
      return res.status(404).render("error", { message: "Company not found" });
    }

    console.log("Found Company:", {
      _id: company._id.toString(),
      companyCode: company.companyCode,
      name: company.name
    });

    // Debug company access check
    console.log("\n=== Company Access Check Debug ===");
    const userCompanies = req.user.companies.map(c => typeof c === 'object' ? c._id.toString() : c.toString());
    const companyId = company._id.toString();
    
    console.log("User Companies (IDs):", userCompanies);
    console.log("Company ID to check:", companyId);
    console.log("Direct comparison results:");
    userCompanies.forEach((userCompanyId, index) => {
      console.log(`Company ${index + 1}: ${userCompanyId} === ${companyId} : ${userCompanyId === companyId}`);
    });

    // Ensure the user has access to this company
    const hasAccess = userCompanies.includes(companyId);
    console.log("Final access check result:", hasAccess);

    if (!hasAccess) {
      console.log("Access denied for user:", req.user._id);
      return res.status(403).render("error", { message: "Access denied" });
    }

    // Set the current company for the user
    req.user.currentCompany = company;

    // Fetch only finalized payrolls for the last 12 months
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const payrolls = await Payroll.find({
      company: company._id,
      month: { $gte: twelveMonthsAgo },
    })
      .sort({ month: -1 })
      .populate("employee");

    console.log("Found payrolls:", payrolls.length);

    // Process payroll data into monthly summaries
    const months = payrolls.reduce((acc, payroll) => {
      const monthKey = payroll.month.toISOString().slice(0, 7); // YYYY-MM format
      if (!acc[monthKey]) {
        acc[monthKey] = {
          _id: monthKey,
          month: payroll.month,
          totalEmployees: 0,
          finalizedEmployees: 0,
          unfinalizedEmployees: 0,
          totalPAYE: 0,
          totalUIF: 0,
          totalSDL: 0,
          status: "Draft",
          submissionDeadline: dateUtils.getSubmissionDeadline(payroll.month, "EMP201"),
          submissions: [],
        };
      }

      acc[monthKey].totalEmployees++;
      if (payroll.finalised) {
        acc[monthKey].finalizedEmployees++;
      } else {
        acc[monthKey].unfinalizedEmployees++;
      }

      // Calculate PAYE, UIF, and SDL only for finalized payrolls
      if (payroll.finalised) {
        const employeeAge = payroll.employee && payroll.employee.dateOfBirth ? 
          payrollCalculations.calculateAge(payroll.employee.dateOfBirth) : 0;

        const totalTaxableIncome = (payroll.basicSalary || 0) +
          (payroll.allowances || 0) +
          (payroll.overtime || 0) +
          (payroll.bonus || 0) +
          (payroll.commission || 0) +
          (typeof payroll.travelAllowance === 'number' ? payroll.travelAllowance * 0.8 : 0) +
          (payroll.otherEarnings || 0);

        const paye = payrollCalculations.calculateEnhancedPAYE({
          annualSalary: totalTaxableIncome * 12,
          frequency: "monthly",
          age: employeeAge
        });

        const totalRemuneration = totalTaxableIncome;
        const uif = Math.min(totalRemuneration * 0.01, 177.12);
        const sdl = totalRemuneration * 0.01;

        acc[monthKey].totalPAYE += paye?.monthlyPAYE || 0;
        acc[monthKey].totalUIF += uif;
        acc[monthKey].totalSDL += sdl;
      }

      return acc;
    }, {});

    // Fetch all EMP201 filings for this company
    const filings = await Filing.find({
      company: company._id,
      submissionType: "EMP201",
      submissionPeriod: { $gte: twelveMonthsAgo }
    });

    // Update month statuses based on filings
    filings.forEach(filing => {
      const monthKey = filing.submissionPeriod.toISOString().slice(0, 7);
      if (months[monthKey]) {
        months[monthKey].status = filing.status;
        months[monthKey].submissions.push({
          certificateNumber: filing.certificateNumber,
          submittedAt: filing.submittedAt,
          totalPAYE: filing.totalPAYE,
          totalUIF: filing.totalUIF,
          totalSDL: filing.totalSDL
        });
      }
    });

    // Convert the months object to an array and sort it
    const sortedMonths = Object.values(months).sort((a, b) => b.month - a.month);

    console.log("Rendering filing page with months:", sortedMonths.length);

    res.render("filing", {
      title: "Filing",
      user: req.user,
      companyCode: companyCode,
      currentCompany: company,
      company: company,
      months: sortedMonths,
      messages: req.flash(),
      helpers: helpers,
      csrfToken: req.csrfToken(),
    });
  } catch (error) {
    console.error("Error in filing route:", error);
    next(createError(500, "Error loading filing page: " + error.message));
  }
});

// Route to get filing data for UI updates
router.get(
  "/:companyCode/filing/data",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ success: false, message: "Company not found" });
      }

      // Fetch only finalized payrolls for the last 12 months
      const twelveMonthsAgo = new Date();
      twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

      const payrolls = await Payroll.find({
        company: company._id,
        month: { $gte: twelveMonthsAgo },
      })
        .sort({ month: -1 })
        .populate("employee");

      // Process payroll data into monthly summaries
      const monthsMap = payrolls.reduce((acc, payroll) => {
        const monthKey = payroll.month.toISOString().slice(0, 7); // YYYY-MM format
        if (!acc[monthKey]) {
          acc[monthKey] = {
            _id: monthKey,
            month: payroll.month,
            totalEmployees: 0,
            finalizedEmployees: 0,
            unfinalizedEmployees: 0,
            totalPAYE: 0,
            totalUIF: 0,
            totalSDL: 0,
            status: "Draft",
            submissionDeadline: dateUtils.getSubmissionDeadline(payroll.month, "EMP201"),
            submissions: [],
          };
        }

        acc[monthKey].totalEmployees++;
        if (payroll.finalised) {
          acc[monthKey].finalizedEmployees++;
        } else {
          acc[monthKey].unfinalizedEmployees++;
        }

        return acc;
      }, {});

      // Get submissions for each month
      const submissions = await Filing.find({
        company: company._id,
        submissionType: "EMP201",
        submissionPeriod: { $gte: twelveMonthsAgo }
      }).sort({ submissionPeriod: -1 });

      // Add submissions to months data
      submissions.forEach(submission => {
        const monthKey = submission.submissionPeriod.toISOString().slice(0, 7);
        if (monthsMap[monthKey]) {
          monthsMap[monthKey].submissions = monthsMap[monthKey].submissions || [];
          monthsMap[monthKey].submissions.push(submission);
          monthsMap[monthKey].status = "Submitted";
          // If submitted, all employees are finalized
          monthsMap[monthKey].finalizedEmployees = monthsMap[monthKey].totalEmployees;
        }
      });

      // Convert map to array and sort by month
      const months = Object.values(monthsMap).sort((a, b) => 
        new Date(b.month) - new Date(a.month)
      );

      res.json({ 
        success: true, 
        months 
      });

    } catch (error) {
      console.error("Error fetching filing data:", error);
      res.status(500).json({ 
        success: false, 
        message: "Error fetching filing data: " + error.message 
      });
    }
  }
);

// Bi-annual filing route
router.get(
  "/:companyCode/filing/bi-annual",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get current year and previous year
      const currentYear = new Date().getFullYear();
      const previousYear = currentYear - 1;

      // Get all payrolls for the current and previous year
      const payrolls = await Payroll.find({
        company: company._id,
        month: {
          $gte: new Date(previousYear, 0, 1),
          $lte: new Date(currentYear, 11, 31)
        }
      }).populate("employee");

      // Get all bi-annual filings for the company
      const biAnnualFilings = await Filing.find({
        company: company._id,
        submissionType: "EMP501",
        submissionPeriod: {
          $gte: new Date(previousYear, 0, 1),
          $lte: new Date(currentYear, 11, 31)
        }
      }).sort({ submissionPeriod: -1 });

      // Process payroll data into bi-annual periods
      const periods = {};
      const months = ["Feb", "Aug"];

      [previousYear, currentYear].forEach(year => {
        months.forEach(month => {
          const periodKey = `${year}-${month}`;
          const monthIndex = month === "Feb" ? 1 : 7;
          const startDate = new Date(year, monthIndex - 6, 1);
          const endDate = new Date(year, monthIndex, 0);

          periods[periodKey] = {
            id: periodKey,
            year,
            month,
            startDate,
            endDate,
            totalEmployees: 0,
            finalizedEmployees: 0,
            totalPAYE: 0,
            totalUIF: 0,
            totalSDL: 0,
            submissions: biAnnualFilings.filter(filing => {
              const filingDate = new Date(filing.submissionPeriod);
              return filingDate >= startDate && filingDate <= endDate;
            })
          };
        });
      });

      // Calculate totals for each period
      payrolls.forEach(payroll => {
        const payrollDate = new Date(payroll.month);
        const year = payrollDate.getFullYear();
        const month = payrollDate.getMonth();
        
        // Determine which period this payroll belongs to
        let periodMonth;
        if (month < 6) {
          periodMonth = "Feb";
        } else {
          periodMonth = "Aug";
        }

        const periodKey = `${year}-${periodMonth}`;
        if (periods[periodKey]) {
          periods[periodKey].totalEmployees++;
          if (payroll.finalised) {
            periods[periodKey].finalizedEmployees++;
          }
          periods[periodKey].totalPAYE += payroll.totalPAYE || 0;
          periods[periodKey].totalUIF += payroll.totalUIF || 0;
          periods[periodKey].totalSDL += payroll.totalSDL || 0;
        }
      });

      // Determine current season based on current date
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth(); // 0-11
      let currentSeason;
      
      if (currentMonth < 6) {
        // January to June: February filing season
        currentSeason = `${currentYear}-Feb`;
      } else {
        // July to December: August filing season
        currentSeason = `${currentYear}-Aug`;
      }
      
      // Check if season was specified in query params
      if (req.query.season) {
        currentSeason = req.query.season;
      }

      res.render("bi-annual-filing", {
        title: "Bi-Annual Filing",
        company,
        periods: Object.values(periods),
        currentSeason: currentSeason, // Pass the current season to the template
        messages: req.flash(), // Pass flash messages to the template
        helpers: {
          formatDate: (date) => moment(date).format("DD MMM YYYY"),
          formatCurrency: (amount) => amount.toLocaleString('en-ZA', {
            style: 'currency',
            currency: 'ZAR'
          }),
          calculateCompletion: (finalized, total) => {
            if (total === 0) return "0.0%";
            return ((finalized / total) * 100).toFixed(1) + "%";
          }
        }
      });

    } catch (error) {
      console.error("Error in bi-annual filing route:", error);
      req.flash("error", "Failed to load bi-annual filing data");
      res.redirect("/dashboard");
    }
  }
);

// Test route to verify routing is working
router.get(
  "/:companyCode/filing/bi-annual/test-route",
  ensureAuthenticated,
  async (req, res) => {
    console.log("🔥 TEST ROUTE HIT! This proves routing is working!");
    res.send("TEST ROUTE WORKING! If you see this, routing is functional.");
  }
);

// Pre-validate bi-annual filing (bypass checkCompany middleware temporarily)
router.get(
  "/:companyCode/filing/bi-annual/pre-validate",
  ensureAuthenticated,
  async (req, res) => {
    console.log("🚀 PRE-VALIDATION ROUTE HIT!");
    console.log("Request URL:", req.originalUrl);
    console.log("Request Path:", req.path);
    console.log("Request Params:", req.params);
    console.log("Request Query:", req.query);
    console.log("User Object:", {
      _id: req.user?._id,
      email: req.user?.email,
      currentCompany: req.user?.currentCompany,
      companies: req.user?.companies?.length || 0
    });

    try {
      const { companyCode } = req.params;
      const { period } = req.query;

      if (!period) {
        req.flash("error", "Period is required");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      const [year, month] = period.split("-");
      const monthIndex = month === "Feb" ? 1 : 7;
      const startDate = new Date(year, monthIndex - 6, 1);
      const endDate = new Date(year, monthIndex, 0);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get all payrolls for the period
      const payrolls = await Payroll.find({
        company: company._id,
        month: {
          $gte: startDate,
          $lte: endDate
        }
      }).populate("employee");

      // Check if there are any payrolls
      if (payrolls.length === 0) {
        req.flash("error", "No payroll records found for this period");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Check if all payrolls are finalized
      const unfinalizedPayrolls = payrolls.filter(p => !p.finalised);
      if (unfinalizedPayrolls.length > 0) {
        req.flash("error", `${unfinalizedPayrolls.length} payroll records are not finalized`);
        
        // Add details about unfinalized payrolls
        unfinalizedPayrolls.forEach(p => {
          const employeeName = p.employee ? `${p.employee.firstName} ${p.employee.lastName}` : 'Unknown';
          const month = moment(p.month).format("MMMM YYYY");
          req.flash("warning", `${employeeName} - ${month}`);
        });
        
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Check if all required tax information is present
      const employeesWithMissingInfo = payrolls.filter(p => {
        const employee = p.employee;
        return !employee.taxNumber || 
               !employee.idNumber || 
               !employee.dateOfBirth ||
               !employee.residentialAddress;
      });

      if (employeesWithMissingInfo.length > 0) {
        req.flash("error", `${employeesWithMissingInfo.length} employees have missing tax information`);
        
        // Add details about employees with missing info
        employeesWithMissingInfo.forEach(p => {
          const employeeName = p.employee ? `${p.employee.firstName} ${p.employee.lastName}` : 'Unknown';
          const missingFields = getMissingFields(p.employee).join(", ");
          req.flash("warning", `${employeeName} - Missing: ${missingFields}`);
        });
        
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Check if a submission already exists
      const existingSubmission = await Filing.findOne({
        company: company._id,
        submissionType: "EMP501",
        submissionPeriod: {
          $gte: startDate,
          $lte: endDate
        }
      });

      if (existingSubmission) {
        req.flash("error", "A submission already exists for this period");
        req.flash("warning", `Submitted on: ${moment(existingSubmission.submittedAt).format("DD MMM YYYY")}`);
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Get all employees for this company
      const employees = await Employee.find({ company: company._id });
      const season = new Date(year, monthIndex - 1, 1);

      // Prepare validation warnings for the template
      const warnings = {
        irp5: [],
        company: [],
        employees: {},
      };

      // Company validation
      if (!company.taxNumber) {
        warnings.company.push("Company tax number is missing");
      }
      if (!company.uifNumber) {
        warnings.company.push("UIF number is missing");
      }
      if (!company.payelNumber) {
        warnings.company.push("PAYE number is missing");
      }

      // Employee validation
      employees.forEach((employee) => {
        const employeeWarnings = getMissingFields(employee);
        if (employeeWarnings.length > 0) {
          warnings.employees[employee._id] = employeeWarnings;
        }
      });

      // IRP5 validation
      const finalizedPayrolls = payrolls.filter(p => p.finalised);
      if (finalizedPayrolls.length === 0) {
        warnings.irp5.push("No finalized payrolls found for this period");
      }

      // Render the pre-validation template with results
      res.render("pre-validation", {
        title: "e@syFile Pre-Validation",
        company,
        employees,
        warnings,
        season,
        user: req.user,
      });
    } catch (error) {
      console.error("Error in bi-annual pre-validation route:", error);
      req.flash("error", "An error occurred during pre-validation");
      return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
    }
  }
);

// Add the same route with /clients prefix for bi-annual pre-validation
router.get(
  "/clients/:companyCode/filing/bi-annual/pre-validate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { period } = req.query;

      console.log(`=== PRE-VALIDATION DEBUG ===`);
      console.log(`Company Code: ${companyCode}`);
      console.log(`Period: ${period}`);
      console.log(`Query params:`, req.query);

      if (!period) {
        console.log(`❌ REDIRECT: Period is required`);
        req.flash("error", "Period is required");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      const [year, month] = period.split("-");
      const monthIndex = month === "Feb" ? 1 : 7;
      const startDate = new Date(year, monthIndex - 6, 1);
      const endDate = new Date(year, monthIndex, 0);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log(`❌ REDIRECT: Company not found for code ${companyCode}`);
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      console.log(`✅ Company found: ${company.name}`);

      // Get all payrolls for the period
      const payrolls = await Payroll.find({
        company: company._id,
        month: {
          $gte: startDate,
          $lte: endDate
        }
      }).populate("employee");

      console.log(`📊 Found ${payrolls.length} payroll records for period`);
      console.log(`Date range: ${startDate} to ${endDate}`);

      // Check if there are any payrolls
      if (payrolls.length === 0) {
        console.log(`❌ REDIRECT: No payroll records found for this period`);
        req.flash("error", "No payroll records found for this period");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Check if all payrolls are finalized
      const unfinalizedPayrolls = payrolls.filter(p => !p.finalised);
      console.log(`📋 Finalized payrolls: ${payrolls.length - unfinalizedPayrolls.length}/${payrolls.length}`);

      if (unfinalizedPayrolls.length > 0) {
        console.log(`❌ REDIRECT: ${unfinalizedPayrolls.length} payroll records are not finalized`);
        unfinalizedPayrolls.forEach(p => {
          const employeeName = p.employee ? `${p.employee.firstName} ${p.employee.lastName}` : 'Unknown';
          const month = moment(p.month).format("MMMM YYYY");
          console.log(`  - Unfinalized: ${employeeName} - ${month}`);
        });

        req.flash("error", `${unfinalizedPayrolls.length} payroll records are not finalized`);

        // Add details about unfinalized payrolls
        unfinalizedPayrolls.forEach(p => {
          const employeeName = p.employee ? `${p.employee.firstName} ${p.employee.lastName}` : 'Unknown';
          const month = moment(p.month).format("MMMM YYYY");
          req.flash("warning", `${employeeName} - ${month}`);
        });

        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Validate employee information
      const employeesWithIssues = [];
      const uniqueEmployees = [...new Set(payrolls.map(p => p.employee))];
      console.log(`👥 Validating ${uniqueEmployees.length} unique employees`);

      for (const employee of uniqueEmployees) {
        const missingFields = getMissingFields(employee);
        if (missingFields.length > 0) {
          console.log(`  - ${employee.firstName} ${employee.lastName}: Missing ${missingFields.join(", ")}`);
          employeesWithIssues.push({
            id: employee._id,
            name: `${employee.firstName} ${employee.lastName}`,
            issues: missingFields
          });
        }
      }

      if (employeesWithIssues.length > 0) {
        console.log(`❌ REDIRECT: ${employeesWithIssues.length} employees have missing tax information`);
        req.flash("error", `${employeesWithIssues.length} employees have missing tax information`);

        // Add details about employees with missing info
        employeesWithIssues.forEach(employee => {
          req.flash("warning", `${employee.name} - Missing: ${employee.issues.join(", ")}`);
        });

        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Get all employees for this company
      const employees = await Employee.find({ company: company._id });
      const season = new Date(year, monthIndex - 1, 1);

      // Prepare validation warnings for the template
      const warnings = {
        irp5: [],
        company: [],
        employees: {},
      };

      // Company validation
      if (!company.taxNumber) {
        warnings.company.push("Company tax number is missing");
      }
      if (!company.uifNumber) {
        warnings.company.push("UIF number is missing");
      }
      if (!company.payelNumber) {
        warnings.company.push("PAYE number is missing");
      }
      if (!company.tradingName) {
        warnings.company.push("Company trading name is missing");
      }
      if (!company.registrationNumber) {
        warnings.company.push("Company registration number is missing");
      }

      // Employee validation - use the issues we already calculated
      employeesWithIssues.forEach(employee => {
        warnings.employees[employee.id] = employee.issues;
      });

      // IRP5 validation
      const finalizedPayrolls = payrolls.filter(p => p.finalised);
      if (finalizedPayrolls.length === 0) {
        warnings.irp5.push("No finalized payrolls found for this period");
      }

      console.log(`✅ SUCCESS: All validations passed, rendering pre-validation template`);
      console.log(`=== END PRE-VALIDATION DEBUG ===`);

      // Render the pre-validation template with results
      res.render("pre-validation", {
        title: "e@syFile Pre-Validation",
        company,
        employees,
        warnings,
        season,
        user: req.user,
      });
    } catch (error) {
      console.log(`❌ ERROR: Exception in pre-validation route:`, error);
      console.error("Error in bi-annual pre-validation route:", error);
      req.flash("error", "An error occurred during pre-validation");
      return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
    }
  }
);

// Route to get employees for bi-annual filing
router.get(
  "/:companyCode/filing/bi-annual/employees",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { period } = req.query;

      if (!period) {
        return res.status(400).json({
          success: false,
          error: "Period is required"
        });
      }

      const [year, month] = period.split("-");
      const monthIndex = month === "Feb" ? 1 : 7;
      const startDate = new Date(year, monthIndex - 6, 1);
      const endDate = new Date(year, monthIndex, 0);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found"
        });
      }

      // Get all employees who have payroll records in this period
      const payrolls = await Payroll.find({
        company: company._id,
        month: {
          $gte: startDate,
          $lte: endDate
        }
      }).populate("employee");

      // Get unique employees and their latest payroll
      const employeeMap = new Map();
      payrolls.forEach(payroll => {
        if (payroll.employee) {
          employeeMap.set(payroll.employee._id.toString(), payroll.employee);
        }
      });

      const employees = Array.from(employeeMap.values()).map(employee => ({
        _id: employee._id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        companyEmployeeNumber: employee.companyEmployeeNumber,
        taxNumber: employee.taxNumber,
        idNumber: employee.idNumber
      }));

      res.json({
        success: true,
        employees,
        period: {
          startDate,
          endDate,
          description: `${month} ${year}`
        }
      });

    } catch (error) {
      console.error("Error fetching bi-annual employees:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch employee data"
      });
    }
  }
);

// Main EMP201 preparation route
router.get(
  "/:companyCode/prepare-emp201/:monthId",
  ensureAuthenticated,
  async (req, res, next) => {
    try {
      const { companyCode, monthId } = req.params;
      console.log("\n=== Starting EMP201 Preparation ===");
      console.log("Parameters:", { companyCode, monthId });

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }
      console.log("Company found:", {
        id: company._id,
        name: company.name,
        exists: !!company,
      });

      // Calculate date range
      const startDate = moment(monthId).startOf("month").toDate();
      const endDate = moment(monthId).endOf("month").toDate();
      console.log("Date Range:", { startDate, endDate });

      // Get payroll periods
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $gte: startDate, $lte: endDate },
        isFinalized: true,
      });

      console.log("Payroll Periods:", {
        total: payrollPeriods.length,
        periodDates: payrollPeriods.map((p) => ({
          start: p.startDate,
          end: p.endDate,
          employeeId: p.employee,
        })),
      });

      // Get payrolls with detailed logging
      const payrolls = await Payroll.find({
        company: company._id,
        month: {
          $gte: startDate,
          $lte: endDate,
        },
      }).populate({
        path: "employee",
        select: "payFrequency firstName lastName employeeNumber dateOfBirth"
      }).select({
        basicSalary: 1,
        allowances: 1,
        overtime: 1,
        bonus: 1,
        commission: 1,
        travelAllowance: 1,
        otherEarnings: 1,
        employee: 1,
        month: 1,
        _id: 1
      });

      console.log("Raw Payroll Data:", payrolls.map((p) => ({
        id: p._id,
        basicSalary: p.basicSalary,
        allowances: p.allowances,
        overtime: p.overtime,
        bonus: p.bonus,
        commission: p.commission,
        travelAllowance: p.travelAllowance,
        otherEarnings: p.otherEarnings,
        frequency: p.employee?.payFrequency
      })));

      // First, let's get the frequency mapping
      const frequencies = await mongoose.model("PayFrequency").find({});
      const frequencyMap = new Map(
        frequencies.map((f) => [f._id.toString(), f.frequency])
      );

      console.log("Frequency Map:", frequencyMap);

      // Updated grouping logic
      const weeklyPayrolls = payrolls.filter((p) => {
        const frequencyId =
          p.employee?.payFrequency?._id || p.employee?.payFrequency;
        const frequencyType = frequencyMap.get(frequencyId?.toString());
        console.log("Checking frequency:", {
          payrollId: p._id,
          frequencyId,
          frequencyType,
        });
        return frequencyType?.toLowerCase() === "weekly";
      });

      const biWeeklyPayrolls = payrolls.filter((p) => {
        const frequencyId =
          p.employee?.payFrequency?._id || p.employee?.payFrequency;
        const frequencyType = frequencyMap.get(frequencyId?.toString());
        return frequencyType?.toLowerCase() === "bi-weekly";
      });

      const monthlyPayrolls = payrolls.filter((p) => {
        const frequencyId =
          p.employee?.payFrequency?._id || p.employee?.payFrequency;
        const frequencyType = frequencyMap.get(frequencyId?.toString());
        return !frequencyType || frequencyType.toLowerCase() === "monthly";
      });

      // Default all payrolls to monthly if no grouping was successful
      if (
        weeklyPayrolls.length === 0 &&
        biWeeklyPayrolls.length === 0 &&
        monthlyPayrolls.length === 0
      ) {
        monthlyPayrolls.push(...payrolls);
      }

      console.log("Updated Grouping Results:", {
        weekly: weeklyPayrolls.length,
        biWeekly: biWeeklyPayrolls.length,
        monthly: monthlyPayrolls.length,
      });

      async function calculatePayrollTotals(payrolls) {
        console.log("\n=== Starting Payroll Totals Calculation ===");
        console.log(`Processing ${payrolls.length} payrolls`);

        const totals = { PAYE: 0, UIF: 0, SDL: 0 };

        for (const payroll of payrolls) {
          try {
            // Get all the earnings components with proper null checks
            const basicSalary = Number(payroll.basicSalary || 0);
            const allowances = Number(payroll.allowances || 0);
            const overtime = Number(payroll.overtime || 0);
            const bonus = Number(payroll.bonus || 0);
            const commission = Number(payroll.commission || 0);
            
            // Handle travel allowance object structure
            let travelAllowanceAmount = 0;
            if (payroll.travelAllowance) {
              if (typeof payroll.travelAllowance === 'number') {
                travelAllowanceAmount = payroll.travelAllowance;
              } else if (typeof payroll.travelAllowance === 'object') {
                // Sum up the relevant travel allowance components
                travelAllowanceAmount = Number(payroll.travelAllowance.fixedAllowanceAmount || 0);
                if (payroll.travelAllowance.reimbursedPerKmTravelled) {
                  travelAllowanceAmount += Number(payroll.travelAllowance.ratePerKm || 0) * Number(payroll.travelAllowance.businessKilometers || 0);
                }
                if (payroll.travelAllowance.companyPetrolCard) {
                  travelAllowanceAmount += Number(payroll.travelAllowance.petrolCardValue || 0);
                }
              }
            }

            const otherEarnings = Number(payroll.otherEarnings || 0);

            // Calculate total taxable income (80% of travel allowance is taxable)
            const taxableTravelAllowance = travelAllowanceAmount * 0.8;
            const totalTaxableIncome = basicSalary + allowances + overtime + bonus + commission + taxableTravelAllowance + otherEarnings;
            
            console.log(`Processing payroll with components:`, {
              basicSalary,
              allowances,
              overtime,
              bonus,
              commission,
              travelAllowance: travelAllowanceAmount,
              taxableTravelAllowance,
              otherEarnings,
              totalTaxableIncome
            });

            // Calculate annual taxable income
            const annualTaxableIncome = totalTaxableIncome * 12;
            console.log(`Annual taxable income: ${annualTaxableIncome}`);

            if (annualTaxableIncome <= 0) {
              console.log('Skipping payroll with zero or negative annual income');
              continue;
            }

            // Get employee age for tax rebates
            const employeeAge = payroll.employee?.dateOfBirth ? 
              payrollCalculations.calculateAge(payroll.employee.dateOfBirth) : 0;

            // Calculate PAYE using the enhanced calculation
            const paye = payrollCalculations.calculateEnhancedPAYE({
              annualSalary: annualTaxableIncome,
              frequency: "monthly",
              travelAllowance: travelAllowanceAmount,
              bonus: bonus,
              commission: commission,
              overtime: overtime,
              allowances: allowances,
              otherEarnings: otherEarnings,
              age: employeeAge
            });

            // Calculate total remuneration for UIF and SDL
            const totalRemuneration = basicSalary + allowances + overtime + bonus + commission + travelAllowanceAmount + otherEarnings;

            // Calculate UIF (1% of total remuneration, capped at R177.12)
            const uif = Math.min(Math.max(0, totalRemuneration * 0.01), 177.12);

            // Calculate SDL (1% if annual salary > R500,000)
            const sdl = annualTaxableIncome > 500000 ? totalRemuneration * 0.01 : 0;

            // Add to totals, ensuring we're adding numbers
            totals.PAYE += Number(paye?.monthlyPAYE || 0);
            totals.UIF += Number(uif || 0);
            totals.SDL += Number(sdl || 0);

            console.log("Calculated amounts for payroll:", {
              employeeId: payroll.employee?._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              totalTaxableIncome,
              annualTaxableIncome,
              employeeAge,
              paye: {
                monthlyPAYE: paye?.monthlyPAYE,
                annualPAYE: paye?.annualPAYE,
                taxBracket: paye?.taxBracket,
                rebates: paye?.rebateDetails
              },
              uif,
              sdl,
              components: {
                basicSalary,
                allowances,
                overtime,
                bonus,
                commission,
                travelAllowance: travelAllowanceAmount,
                otherEarnings
              }
            });
          } catch (error) {
            console.error("Error processing payroll:", {
              payrollId: payroll._id,
              error: error.message,
              stack: error.stack
            });
          }
        }

        // Round all totals to 2 decimal places
        totals.PAYE = Math.round(totals.PAYE * 100) / 100;
        totals.UIF = Math.round(totals.UIF * 100) / 100;
        totals.SDL = Math.round(totals.SDL * 100) / 100;

        console.log("Final totals:", totals);
        return totals;
      }

      // Calculate totals with detailed logging
      console.log("\n=== Starting Calculations ===");

      console.log("Processing Weekly Payrolls...");
      const weeklyTotals = await calculatePayrollTotals(weeklyPayrolls);
      console.log("Weekly Totals:", weeklyTotals);

      console.log("Processing Bi-Weekly Payrolls...");
      const biWeeklyTotals = await calculatePayrollTotals(biWeeklyPayrolls);
      console.log("Bi-Weekly Totals:", biWeeklyTotals);

      console.log("Processing Monthly Payrolls...");
      const monthlyTotals = await calculatePayrollTotals(monthlyPayrolls);
      console.log("Monthly Totals:", monthlyTotals);

      // Calculate consolidated totals
      const consolidatedTotals = {
        PAYE: weeklyTotals.PAYE + biWeeklyTotals.PAYE + monthlyTotals.PAYE,
        UIF: weeklyTotals.UIF + biWeeklyTotals.UIF + monthlyTotals.UIF,
        SDL: weeklyTotals.SDL + biWeeklyTotals.SDL + monthlyTotals.SDL,
      };
      consolidatedTotals.total =
        consolidatedTotals.PAYE +
        consolidatedTotals.UIF +
        consolidatedTotals.SDL;

      console.log("Consolidated Totals:", consolidatedTotals);

      // Prepare summary
      const summary = {
        monthId,
        weeklyPayrolls,
        biWeeklyPayrolls,
        monthlyPayrolls,
        weeklyTotals,
        biWeeklyTotals,
        monthlyTotals,
        consolidatedTotals,
        totalEmployees: payrollPeriods.length,
      };

      console.log("\n=== Final Summary ===");
      console.log("Total Employees:", summary.totalEmployees);
      console.log("Total PAYE:", consolidatedTotals.PAYE);
      console.log("Total UIF:", consolidatedTotals.UIF);
      console.log("Total SDL:", consolidatedTotals.SDL);
      console.log("Grand Total:", consolidatedTotals.total);

      // Render template
      res.render("prepare-emp201", {
        companyCode,
        summary,
        formatDate: helpers.formatDate,
        formatCurrency: (amount) => Number(amount || 0).toFixed(2),
        helpers: {
          formatDate: helpers.formatDate,
          formatCurrency: (amount) => Number(amount || 0).toFixed(2),
        },
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("\n=== Error in EMP201 Preparation ===");
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      next(error);
    }
  }
);

// Submit EMP201 route
router.post(
  "/:companyCode/submit-emp201/:monthId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, monthId } = req.params;
      const { consolidatedTotals, totalEmployees } = req.body;

      console.log("\n=== EMP201 Submission ===");
      console.log("Company Code:", companyCode);
      console.log("Month ID:", monthId);
      console.log("Total Employees:", totalEmployees);
      console.log("Consolidated Totals:", consolidatedTotals);

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ success: false, message: "Company not found" });
      }

      // Get the start and end of the month
      const submissionMonth = new Date(monthId);
      const startOfMonth = new Date(submissionMonth.getFullYear(), submissionMonth.getMonth(), 1);
      const endOfMonth = new Date(submissionMonth.getFullYear(), submissionMonth.getMonth() + 1, 0);

      // Find and update all payrolls for this month to mark them as finalized
      await Payroll.updateMany(
        {
          company: company._id,
          month: {
            $gte: startOfMonth,
            $lte: endOfMonth
          }
        },
        {
          $set: {
            finalised: true,
            finalisedAt: new Date(),
            finalisedBy: req.user._id
          }
        }
      );

      // Generate certificate number
      const timestamp = Date.now().toString();
      const certificateNumber = `EMP201-${companyCode}-${monthId}-${timestamp}`;

      // Calculate submission deadline (7th of next month)
      const submissionDeadline = new Date(
        submissionMonth.getFullYear(),
        submissionMonth.getMonth() + 1,
        7
      );

      // Create new filing record
      const filing = new Filing({
        company: company._id,
        submissionType: "EMP201",
        submissionPeriod: new Date(monthId),
        status: "Submitted",
        submissionDeadline,
        submissionData: {
          consolidatedTotals,
          totalEmployees
        },
        certificateNumber,
        employeeCount: totalEmployees,
        submittedBy: req.user._id,
        submittedAt: new Date(),
        finalizedEmployees: totalEmployees, // Add this to track completion
        totalEmployees: totalEmployees
      });

      await filing.save();

      // Update any existing filings for this period to mark them as supplementary
      await Filing.updateMany(
        {
          company: company._id,
          submissionPeriod: new Date(monthId),
          _id: { $ne: filing._id }
        },
        {
          $set: {
            isSupplementary: true
          }
        }
      );

      res.json({ 
        success: true, 
        message: "EMP201 submitted successfully",
        certificateNumber,
        filing: {
          ...filing.toObject(),
          completionPercentage: 100 // Since all payrolls are now finalized
        }
      });
    } catch (error) {
      console.error("Error submitting EMP201:", error);
      res.status(500).json({ 
        success: false, 
        message: "Error submitting EMP201: " + error.message 
      });
    }
  }
);

// PDF download route
router.get(
  "/:companyCode/filing/download-pdf/:monthId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, monthId } = req.params;

      // Find company and filing record
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ success: false, message: "Company not found" });
      }

      const submissionDate = new Date(monthId);
      const filing = await Filing.findOne({
        company: company._id,
        submissionType: "EMP201",
        submissionPeriod: submissionDate
      });

      if (!filing) {
        return res.status(404).json({ success: false, message: "Filing record not found" });
      }

      // Create PDF document
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        info: {
          Title: `EMP201 Return - ${company.name}`,
          Author: 'Panda Software Solutions Group',
          Subject: 'Monthly PAYE Return'
        }
      });

      // Set response headers
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=EMP201-${companyCode}-${monthId}.pdf`);
      doc.pipe(res);

      // Document Header
      doc.rect(50, 50, 495, 60).stroke(); // Header box - reduced height
      doc.fontSize(16).text('EMP201', 60, 60); // Reduced from 22
      doc.fontSize(10).text('Monthly Employer Declaration', 60, 85); // Reduced from 12
      doc.fontSize(8).text('SARS', 480, 60); // Reduced from 10

      // Company Details Box
      doc.rect(50, 130, 495, 60).stroke(); // Moved up and reduced height
      doc.fontSize(11).text('Company Details', 60, 140); // Reduced from 14
      doc.fontSize(10) // Reduced from 12
         .text(`Company Name: ${company.name}`, 60, 155)
         .text(`PAYE Reference: ${company.payeReference || 'N/A'}`, 60, 170);

      // Period Box
      doc.rect(50, 210, 495, 45).stroke(); // Moved up and reduced height
      doc.fontSize(11).text('Submission Period', 60, 220); // Reduced from 14
      doc.fontSize(10).text( // Reduced from 12
        `${new Date(filing.submissionPeriod).toLocaleDateString('en-ZA', { 
          month: 'long', 
          year: 'numeric' 
        })}`,
        60,
        235
      );

      // Payment Details Box
      doc.rect(50, 275, 495, 160).stroke(); // Moved up and reduced height
      doc.fontSize(11).text('Payment Details', 60, 285); // Reduced from 14
      
      // Table Headers
      const tableTop = 310;
      doc.fontSize(10) // Reduced from 12
         .text('Description', 60, tableTop)
         .text('Amount', 400, tableTop);
      
      // Draw line under headers
      doc.moveTo(60, tableTop + 15)
         .lineTo(535, tableTop + 15)
         .stroke();

      // Payment Lines
      const lineHeight = 25; // Reduced from 30
      let currentY = tableTop + 25;

      // PAYE
      doc.text('PAYE', 60, currentY)
         .text(formatCurrency(filing.totalPAYE), 400, currentY);
      currentY += lineHeight;

      // SDL
      doc.text('SDL', 60, currentY)
         .text(formatCurrency(filing.totalSDL), 400, currentY);
      currentY += lineHeight;

      // UIF
      doc.text('UIF', 60, currentY)
         .text(formatCurrency(filing.totalUIF), 400, currentY);
      currentY += lineHeight;

      // Total line
      doc.moveTo(60, currentY)
         .lineTo(535, currentY)
         .stroke();
      currentY += 15;

      // Total amount
      doc.fontSize(11) // Reduced from 14
         .text('Total Payable', 60, currentY)
         .text(
           formatCurrency(filing.totalPAYE + filing.totalUIF + filing.totalSDL),
           400,
           currentY,
           { bold: true }
         );

      // Certificate Details Box
      doc.rect(50, 455, 495, 80).stroke(); // Moved up and reduced height
      doc.fontSize(11).text('Submission Details', 60, 465); // Reduced from 14
      doc.fontSize(10) // Reduced from 12
         .text(`Certificate Number: ${filing.certificateNumber}`, 60, 480)
         .text(`Submission Date: ${new Date(filing.submittedAt).toLocaleString('en-ZA')}`, 60, 495)
         .text(`Number of Employees: ${filing.employeeCount || 'N/A'}`, 60, 510);

      // Footer
      doc.fontSize(8)
         .text(
           'This document is computer generated and is valid without a signature.',
           50,
           550,
           { align: 'center', color: 'grey' }
         );

      // Page numbers
      doc.text(
        `Page 1 of 1`,
        50,
        570,
        { align: 'center', color: 'grey' }
      );

      // Finalize PDF
      doc.end();

    } catch (error) {
      console.error("Error downloading PDF:", error);
      res.status(500).json({ 
        success: false, 
        message: "Error downloading PDF: " + error.message 
      });
    }
  }
);

// Route for OID return page
router.get(
  "/:companyCode/filing/oid-return",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get current year and previous year
      const currentYear = new Date().getFullYear();
      const previousYear = currentYear - 1;

      // Get all OID returns for the company
      const oidReturns = await Filing.find({
        company: company._id,
        submissionType: "OID",
        submissionPeriod: {
          $gte: new Date(previousYear, 0, 1),
          $lte: new Date(currentYear, 11, 31)
        }
      }).sort({ submissionPeriod: -1 });

      // Get all payrolls with OID claims
      const payrolls = await Payroll.find({
        company: company._id,
        'oidClaims.0': { $exists: true }, // Only get payrolls with OID claims
        month: {
          $gte: new Date(previousYear, 0, 1),
          $lte: new Date(currentYear, 11, 31)
        }
      })
      .populate('employee')
      .populate('oidClaims')
      .sort({ month: -1 });

      // Process data into yearly periods
      const periods = {};
      [previousYear, currentYear].forEach(year => {
        periods[year] = {
          year,
          startDate: new Date(year, 0, 1),
          endDate: new Date(year, 11, 31),
          totalClaims: 0,
          pendingClaims: 0,
          submittedClaims: 0,
          totalAmount: 0,
          submissions: oidReturns.filter(filing => {
            const filingDate = new Date(filing.submissionPeriod);
            return filingDate.getFullYear() === year;
          }),
          claims: []
        };
      });

      // Calculate totals for each period
      payrolls.forEach(payroll => {
        const year = new Date(payroll.month).getFullYear();
        if (periods[year]) {
          payroll.oidClaims.forEach(claim => {
            periods[year].totalClaims++;
            periods[year].totalAmount += claim.amount || 0;
            
            if (claim.status === 'submitted') {
              periods[year].submittedClaims++;
            } else {
              periods[year].pendingClaims++;
            }

            // Add claim details
            periods[year].claims.push({
              employee: payroll.employee,
              month: payroll.month,
              amount: claim.amount,
              status: claim.status,
              submissionDate: claim.submissionDate,
              claimNumber: claim.claimNumber
            });
          });
        }
      });

      res.render("oid-return", {
        title: "OID Return",
        company,
        periods: Object.values(periods),
        helpers: {
          formatDate: (date) => moment(date).format("DD MMM YYYY"),
          formatCurrency: (amount) => amount.toLocaleString('en-ZA', {
            style: 'currency',
            currency: 'ZAR'
          }),
          calculateCompletion: (submitted, total) => {
            if (total === 0) return "0.0%";
            return ((submitted / total) * 100).toFixed(1) + "%";
          }
        }
      });

    } catch (error) {
      console.error("Error in OID return route:", error);
      req.flash("error", "Failed to load OID return data");
      res.redirect("/dashboard");
    }
  }
);

// Helper function to check leap year
function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

// Helper function to draw a line
function drawLine(doc, y) {
  doc.moveTo(50, y).lineTo(545, y).stroke();
}

// Helper function to format currency
function formatCurrency(amount) {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount).replace('ZAR', 'R');
}

// Route for viewing individual bi-annual certificates
router.get(
  "/:companyCode/filing/bi-annual/:period/certificate/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    console.log("\n=== IRP5 Certificate Download Debug Start ===");
    console.log("Request Parameters:", {
      companyCode: req.params.companyCode,
      period: req.params.period,
      employeeId: req.params.employeeId,
      testMode: req.query.test
    });
    console.log("Request URL:", req.originalUrl);
    console.log("User ID:", req.user?._id);

    try {
      const { companyCode, period, employeeId } = req.params;

      const [year, month] = period.split("-");
      console.log("Parsed Period:", { year, month });

      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("❌ Company not found:", companyCode);
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }
      console.log("✅ Company found:", {
        id: company._id,
        name: company.name,
        companyCode: company.companyCode,
        payelNumber: company.payelNumber
      });

      const employee = await Employee.findById(employeeId).populate('company');
      if (!employee) {
        console.error("❌ Employee not found:", employeeId);
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }
      console.log("✅ Employee found:", {
        id: employee._id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        employeeId: employee.employeeId,
        companyEmployeeNumber: employee.companyEmployeeNumber,
        hasCompany: !!employee.company,
        companyId: employee.company?._id
      });

      // Ensure employee has company populated and belongs to the correct company
      if (!employee.company) {
        employee.company = company;
        console.log("🔧 Company populated for employee");
      }

      // Validate that employee belongs to the requested company
      if (employee.company._id.toString() !== company._id.toString()) {
        req.flash("error", "Employee does not belong to the specified company");
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Calculate tax year from the period
      // South African tax year runs from March 1 to February 28
      // Aug period (e.g., 2025-Aug) is in the 2025/2026 tax year (Mar 2025 - Feb 2026)
      // Feb period (e.g., 2025-Feb) is in the 2024/2025 tax year (Mar 2024 - Feb 2025)
      const taxYear = month === "Feb" ? parseInt(year) : parseInt(year) + 1;
      console.log("📅 Tax Year Calculation:", {
        period: `${year}-${month}`,
        calculatedTaxYear: taxYear,
        logic: month === "Feb" ? "Feb period uses current year" : "Aug period uses next year"
      });

      console.log(`🚀 Generating individual IRP5 for employee ${employeeId}, tax year ${taxYear}`);

      // Check for test mode (allows non-finalized periods)
      const testMode = req.query.test === 'true' || process.env.NODE_ENV === 'development';
      console.log(`🧪 Test mode: ${testMode} (allows non-finalized periods)`);

      // Get payroll data for the tax year using IRP5Service
      console.log("📊 Fetching payroll data using IRP5Service...");
      const IRP5Service = require('../services/IRP5Service');
      const payrollData = await IRP5Service.getEmployeePayrollDataForTaxYear(
        employeeId,
        company._id,
        taxYear,
        testMode
      );
      console.log("📊 Payroll data retrieved:", {
        grossPay: payrollData?.grossPay || 0,
        totalPAYE: payrollData?.totalPAYE || 0,
        periodsCount: payrollData?.periods?.length || 0,
        hasData: !!(payrollData && (payrollData.grossPay > 0 || payrollData.periods?.length > 0))
      });

      if (!payrollData || payrollData.periods.length === 0) {
        console.log("❌ Validation failed: No payroll data found");

        // Check if there are any payroll periods at all (even non-finalized)
        const PayrollPeriod = require('../models/PayrollPeriod');
        const taxYearStart = moment(`${parseInt(taxYear) - 1}-03-01`);
        const taxYearEnd = moment(`${taxYear}-02-28`).endOf('day');

        console.log(`🔍 Checking for any periods in tax year ${taxYear} (${taxYearStart.format('YYYY-MM-DD')} to ${taxYearEnd.format('YYYY-MM-DD')})`);

        const anyPeriods = await PayrollPeriod.find({
          employee: employeeId,
          company: company._id,
          startDate: { $gte: taxYearStart.toDate() },
          endDate: { $lte: taxYearEnd.toDate() }
        });

        console.log(`📊 Found ${anyPeriods.length} total periods for employee in tax year ${taxYear}`);
        console.log("📋 Period details:", anyPeriods.map(p => ({
          id: p._id,
          startDate: p.startDate,
          endDate: p.endDate,
          status: p.status,
          finalised: p.finalised
        })));

        let errorMsg;
        if (anyPeriods.length > 0) {
          const finalizedCount = anyPeriods.filter(p => p.status === 'finalized').length;
          const openCount = anyPeriods.filter(p => p.status === 'open').length;

          console.log(`📊 Periods breakdown: ${finalizedCount} finalized, ${openCount} open`);

          if (finalizedCount === 0) {
            const testModeHint = testMode ? "" : " Or add ?test=true to the URL to generate with non-finalized periods for testing.";
            errorMsg = `Found ${anyPeriods.length} payroll period(s) for tax year ${taxYear}, but none are finalized. Please finalize the payroll periods before generating IRP5 certificates.${testModeHint}`;
            console.error("❌ Error: No finalized periods -", errorMsg);
          } else {
            errorMsg = `Found ${anyPeriods.length} payroll period(s) for tax year ${taxYear}, but only ${finalizedCount} are finalized. Please finalize all payroll periods before generating IRP5 certificates.`;
            console.error("❌ Error: Partially finalized periods -", errorMsg);
          }
        } else {
          errorMsg = `No payroll records found for employee in tax year ${taxYear} (${taxYearStart.format('MMM YYYY')} - ${taxYearEnd.format('MMM YYYY')}). Please ensure payroll has been processed for this employee.`;
          console.error("❌ Error: No payroll periods found -", errorMsg);
        }

        // Check if this is an AJAX request
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
          console.log("📤 Returning JSON error response for AJAX request");
          return res.status(400).json({
            success: false,
            error: errorMsg,
            details: {
              employeeId,
              taxYear,
              periodsFound: anyPeriods.length,
              finalizedCount: anyPeriods.filter(p => p.status === 'finalized').length
            }
          });
        }

        console.log("🔄 Redirecting back to bi-annual filing page due to validation failure");
        req.flash("error", errorMsg);
        return res.redirect(`/clients/${companyCode}/filing/bi-annual`);
      }

      // Generate individual IRP5 PDF using the existing generator
      console.log("🎯 Validation passed, generating IRP5 PDF...");
      const generateIRP5PdfLib = require('../utils/irp5PdfGenerator');

      console.log("📄 Calling PDF generator with:", {
        employeeId: employee._id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        taxYear,
        payrollDataSummary: {
          grossPay: payrollData.grossPay,
          totalPAYE: payrollData.totalPAYE,
          periodsCount: payrollData.periods.length
        }
      });

      const pdfBuffer = await generateIRP5PdfLib(employee, taxYear, payrollData);

      console.log("✅ PDF generated successfully:", {
        bufferSize: pdfBuffer?.length || 0,
        hasBuffer: !!pdfBuffer
      });

      // Set response headers for PDF download
      const filename = `IRP5_${employee.firstName}_${employee.lastName}_${taxYear}.pdf`;
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename=${filename}`);

      console.log("📤 Sending PDF response with headers:", {
        contentType: 'application/pdf',
        filename,
        bufferSize: pdfBuffer.length
      });

      // Send the PDF buffer
      res.send(pdfBuffer);
      console.log("✅ IRP5 Certificate Download Completed Successfully");

    } catch (error) {
      console.error("❌ Error generating individual IRP5 certificate:", {
        error: error.message,
        stack: error.stack,
        employeeId: req.params.employeeId,
        companyCode: req.params.companyCode,
        period: req.params.period
      });

      // Check if this is an AJAX request
      if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
        console.log("📤 Returning JSON error response for AJAX request");
        return res.status(500).json({
          success: false,
          error: `Failed to generate IRP5 certificate: ${error.message}`,
          details: {
            employeeId: req.params.employeeId,
            companyCode: req.params.companyCode,
            period: req.params.period
          }
        });
      }

      req.flash("error", `Failed to generate IRP5 certificate: ${error.message}`);
      res.redirect(`/clients/${companyCode}/filing/bi-annual`);
    }
  }
);

// Debug route for payroll data migration (admin only)
router.get(
  "/:companyCode/filing/debug/payroll-migration",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { action = 'validate', dryRun = 'true' } = req.query;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ success: false, error: "Company not found" });
      }

      const PayrollDataMigration = require('../utils/payrollDataMigration');
      let results;

      if (action === 'migrate') {
        results = await PayrollDataMigration.linkPayrollToPayrollPeriods(
          company._id,
          dryRun === 'true'
        );
      } else {
        results = await PayrollDataMigration.validatePayrollPeriodLinks(company._id);
      }

      res.json({
        success: true,
        action,
        dryRun: dryRun === 'true',
        companyCode,
        results
      });

    } catch (error) {
      console.error("Error in payroll migration debug route:", error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

module.exports = router;
