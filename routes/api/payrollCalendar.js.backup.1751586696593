const express = require('express');
const router = express.Router();
const PayrollCalendarEvent = require('../../models/PayrollCalendarEvent');
const { ensureAuthenticated } = require('../../middleware/auth');
const payrollReminderService = require('../../services/payrollReminderService');

// Get all payroll calendar events for a company
router.get('/events', ensureAuthenticated, async (req, res) => {

  try {
    const { startDate, endDate, type, status } = req.query;
    
    // Build query
    const query = { company: req.user.currentCompany };
    
    // Date range filter
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    // Type filter
    if (type && type !== 'all') {
      query.type = type;
    }
    
    // Status filter
    if (status && status !== 'all') {
      query.status = status;
    }

    const events = await PayrollCalendarEvent.find(query)
      .populate('assignedTo', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName email')
      .populate('completedBy', 'firstName lastName email')
      .sort({ date: 1 });



    res.json({
      success: true,
      data: events,
      count: events.length
    });
  } catch (error) {
    console.error('Error fetching payroll calendar events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll calendar events'
    });
  }
});

// Get upcoming events (next 30 days)
router.get('/upcoming', ensureAuthenticated, async (req, res) => {
  try {
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    const events = await PayrollCalendarEvent.find({
      company: req.user.currentCompany,
      date: { $gte: today, $lte: thirtyDaysFromNow },
      status: { $ne: 'completed' }
    })
    .populate('assignedTo', 'firstName lastName email')
    .sort({ date: 1 })
    .limit(10);

    res.json({
      success: true,
      data: events,
      count: events.length
    });
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch upcoming events'
    });
  }
});

// Create new payroll calendar event
router.post('/events', ensureAuthenticated, async (req, res) => {
  try {
    const eventData = {
      ...req.body,
      company: req.user.currentCompany,
      createdBy: req.user._id
    };

    const event = new PayrollCalendarEvent(eventData);
    await event.save();

    await event.populate('assignedTo', 'firstName lastName email');
    await event.populate('createdBy', 'firstName lastName email');



    res.status(201).json({
      success: true,
      data: event,
      message: 'Payroll calendar event created successfully'
    });
  } catch (error) {
    console.error('Error creating payroll calendar event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payroll calendar event'
    });
  }
});

// Update payroll calendar event
router.put('/events/:id', ensureAuthenticated, async (req, res) => {
  try {
    const event = await PayrollCalendarEvent.findOneAndUpdate(
      { _id: req.params.id, company: req.user.currentCompany },
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    )
    .populate('assignedTo', 'firstName lastName email')
    .populate('createdBy', 'firstName lastName email')
    .populate('completedBy', 'firstName lastName email');

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Payroll calendar event not found'
      });
    }

    res.json({
      success: true,
      data: event,
      message: 'Payroll calendar event updated successfully'
    });
  } catch (error) {
    console.error('Error updating payroll calendar event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update payroll calendar event'
    });
  }
});

// Mark event as completed
router.patch('/events/:id/complete', ensureAuthenticated, async (req, res) => {
  try {
    const event = await PayrollCalendarEvent.findOne({
      _id: req.params.id,
      company: req.user.currentCompany
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Payroll calendar event not found'
      });
    }

    await event.markCompleted(req.user._id);
    await event.populate('completedBy', 'firstName lastName email');

    res.json({
      success: true,
      data: event,
      message: 'Event marked as completed'
    });
  } catch (error) {
    console.error('Error marking event as completed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark event as completed'
    });
  }
});

// Delete payroll calendar event
router.delete('/events/:id', ensureAuthenticated, async (req, res) => {
  try {
    const event = await PayrollCalendarEvent.findOneAndDelete({
      _id: req.params.id,
      company: req.user.currentCompany
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Payroll calendar event not found'
      });
    }

    res.json({
      success: true,
      message: 'Payroll calendar event deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting payroll calendar event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete payroll calendar event'
    });
  }
});

// Initialize default compliance events for a company
router.post('/initialize-compliance', ensureAuthenticated, async (req, res) => {
  try {
    const { year } = req.body;
    const currentYear = year || new Date().getFullYear();

    // Check if compliance events already exist for this year
    const existingEvents = await PayrollCalendarEvent.find({
      company: req.user.currentCompany,
      date: {
        $gte: new Date(currentYear, 0, 1),
        $lte: new Date(currentYear, 11, 31)
      },
      'metadata.source': 'system_generated'
    });

    if (existingEvents.length > 0) {
      return res.json({
        success: true,
        message: 'Compliance events already exist for this year',
        data: existingEvents
      });
    }

    // Create default compliance events
    const defaultEvents = await PayrollCalendarEvent.createDefaultComplianceEvents(
      req.user.currentCompany,
      currentYear
    );

    // Add createdBy to each event
    const eventsWithCreator = defaultEvents.map(event => ({
      ...event,
      createdBy: req.user._id
    }));

    const createdEvents = await PayrollCalendarEvent.insertMany(eventsWithCreator);

    res.json({
      success: true,
      data: createdEvents,
      message: `Created ${createdEvents.length} default compliance events for ${currentYear}`
    });
  } catch (error) {
    console.error('Error initializing compliance events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize compliance events'
    });
  }
});

// Initialize comprehensive South African compliance events
router.post('/initialize-comprehensive-compliance', ensureAuthenticated, async (req, res) => {
  try {
    const { year, force = false } = req.body;
    const currentYear = year || new Date().getFullYear();

    // Enhanced duplicate detection - check for any compliance events for this year
    const existingComplianceEvents = await PayrollCalendarEvent.find({
      company: req.user.currentCompany,
      date: {
        $gte: new Date(currentYear, 0, 1),
        $lte: new Date(currentYear + 1, 11, 31) // Include next year for annual events
      },
      $or: [
        { 'metadata.source': 'comprehensive_compliance' },
        { 'metadata.source': 'system_generated' },
        { type: { $in: ['emp201', 'emp501', 'irp5', 'it3a', 'uif', 'sdl', 'eti', 'paye'] } }
      ]
    });

    // Group existing events by type for detailed feedback
    const existingByType = {};
    existingComplianceEvents.forEach(event => {
      if (!existingByType[event.type]) {
        existingByType[event.type] = [];
      }
      existingByType[event.type].push(event);
    });

    if (existingComplianceEvents.length > 0 && !force) {
      const existingTypes = Object.keys(existingByType);
      return res.json({
        success: false,
        isDuplicate: true,
        message: `Compliance events already exist for ${currentYear}. Found ${existingComplianceEvents.length} existing events.`,
        data: {
          existingCount: existingComplianceEvents.length,
          existingTypes: existingTypes,
          existingByType: existingByType,
          year: currentYear,
          suggestion: 'Use the force option to recreate events or manually add missing events.'
        }
      });
    }

    // If force is true, remove existing comprehensive compliance events
    if (force && existingComplianceEvents.length > 0) {
      await PayrollCalendarEvent.deleteMany({
        company: req.user.currentCompany,
        'metadata.source': 'comprehensive_compliance',
        date: {
          $gte: new Date(currentYear, 0, 1),
          $lte: new Date(currentYear + 1, 11, 31)
        }
      });
    }

    // Create comprehensive South African compliance events
    const comprehensiveEvents = await PayrollCalendarEvent.createComprehensiveComplianceEvents(
      req.user.currentCompany,
      currentYear
    );

    // Add createdBy to each event
    const eventsWithCreator = comprehensiveEvents.map(event => ({
      ...event,
      createdBy: req.user._id
    }));

    const createdEvents = await PayrollCalendarEvent.insertMany(eventsWithCreator);

    // Group created events by type for response
    const createdByType = {};
    createdEvents.forEach(event => {
      if (!createdByType[event.type]) {
        createdByType[event.type] = 0;
      }
      createdByType[event.type]++;
    });

    res.json({
      success: true,
      data: createdEvents,
      message: `Successfully created ${createdEvents.length} comprehensive South African compliance events for ${currentYear}`,
      details: {
        totalCreated: createdEvents.length,
        year: currentYear,
        createdByType: createdByType,
        wasForced: force
      }
    });
  } catch (error) {
    console.error('Error initializing comprehensive compliance events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize comprehensive compliance events',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get event statistics
router.get('/stats', ensureAuthenticated, async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const stats = await PayrollCalendarEvent.aggregate([
      { $match: { company: req.user.currentCompany } },
      {
        $facet: {
          totalEvents: [{ $count: "count" }],
          pendingEvents: [
            { $match: { status: 'pending' } },
            { $count: "count" }
          ],
          overdueEvents: [
            { $match: { status: { $ne: 'completed' }, date: { $lt: today } } },
            { $count: "count" }
          ],
          thisMonthEvents: [
            { $match: { date: { $gte: startOfMonth, $lte: endOfMonth } } },
            { $count: "count" }
          ],
          eventsByType: [
            { $group: { _id: "$type", count: { $sum: 1 } } }
          ],
          eventsByStatus: [
            { $group: { _id: "$status", count: { $sum: 1 } } }
          ]
        }
      }
    ]);

    const result = {
      total: stats[0].totalEvents[0]?.count || 0,
      pending: stats[0].pendingEvents[0]?.count || 0,
      overdue: stats[0].overdueEvents[0]?.count || 0,
      thisMonth: stats[0].thisMonthEvents[0]?.count || 0,
      byType: stats[0].eventsByType,
      byStatus: stats[0].eventsByStatus
    };

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching event statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch event statistics'
    });
  }
});

// Manual trigger for email reminders (admin only)
router.post('/send-reminders', ensureAuthenticated, async (req, res) => {
  try {
    // Check if user has admin privileges
    if (!req.user.roleName || !['owner', 'admin', 'manager'].includes(req.user.roleName)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges to trigger email reminders'
      });
    }

    const result = await payrollReminderService.processEmailReminders();

    res.json({
      success: true,
      message: 'Email reminders processed successfully',
      data: result
    });
  } catch (error) {
    console.error('Error processing email reminders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process email reminders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get reminder status for events
router.get('/reminder-status', ensureAuthenticated, async (req, res) => {
  try {
    const { eventId } = req.query;

    let query = { company: req.user.currentCompany };
    if (eventId) {
      query._id = eventId;
    }

    const events = await PayrollCalendarEvent.find(query)
      .select('title date type priority emailReminders reminderSettings')
      .sort({ date: 1 });

    const reminderStatus = events.map(event => {
      const today = new Date();
      const daysUntilEvent = Math.ceil((event.date - today) / (1000 * 60 * 60 * 24));

      const pendingReminders = [];
      const sentReminders = event.emailReminders || [];

      if (event.reminderSettings?.enabled && event.reminderSettings?.emailReminder) {
        event.reminderSettings.daysBeforeEvent.forEach(days => {
          const alreadySent = sentReminders.some(reminder => reminder.daysBeforeEvent === days);
          if (!alreadySent && daysUntilEvent <= days && daysUntilEvent >= 0) {
            pendingReminders.push(days);
          }
        });
      }

      return {
        eventId: event._id,
        title: event.title,
        date: event.date,
        type: event.type,
        priority: event.priority,
        daysUntilEvent,
        reminderSettings: event.reminderSettings,
        sentReminders: sentReminders.map(r => ({
          daysBeforeEvent: r.daysBeforeEvent,
          sentAt: r.sentAt,
          sentToCount: r.sentTo?.length || 0,
          emailStatus: r.emailStatus
        })),
        pendingReminders,
        hasPendingReminders: pendingReminders.length > 0
      };
    });

    res.json({
      success: true,
      data: reminderStatus,
      summary: {
        totalEvents: reminderStatus.length,
        eventsWithPendingReminders: reminderStatus.filter(e => e.hasPendingReminders).length,
        totalPendingReminders: reminderStatus.reduce((sum, e) => sum + e.pendingReminders.length, 0)
      }
    });
  } catch (error) {
    console.error('Error fetching reminder status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reminder status'
    });
  }
});

module.exports = router;
