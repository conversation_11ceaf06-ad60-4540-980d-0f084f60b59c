const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../../middleware/auth");
const LeaveType = require("../../models/LeaveType");
const LeaveRequest = require("../../models/LeaveRequest");
const LeaveBalance = require("../../models/LeaveBalance");
const Company = require("../../models/Company");
const Employee = require("../../models/Employee");
const whatsappService = require("../../services/whatsappService");
const {
  sendLeaveStatusNotification,
} = require("../../utils/emailNotifications");

// Helper function to format dates in a user-friendly way
function formatDateForDisplay(date) {
  if (!date) return '';
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'UTC'
  };
  return new Date(date).toLocaleDateString('en-US', options);
}

// Helper function to generate context-aware notification messages
function generateLeaveNotificationMessage(leaveRequest, action, rejectionReason = null) {
  const employeeName = `${leaveRequest.employee.firstName} ${leaveRequest.employee.lastName}`;
  const leaveTypeName = leaveRequest.leaveType.name;
  const leaveTypeCategory = leaveRequest.leaveType.category?.toLowerCase() || '';
  const startDate = formatDateForDisplay(leaveRequest.startDate);
  const endDate = formatDateForDisplay(leaveRequest.endDate);
  const dateRange = startDate === endDate ? startDate : `${startDate} to ${endDate}`;

  if (action === 'approved') {
    // Generate approval messages based on leave type category
    switch (leaveTypeCategory) {
      case 'annual':
        return {
          title: 'Annual Leave Approved! 🎉',
          message: `Good news! ${employeeName}'s Annual Leave request from ${dateRange} has been approved. Enjoy your time off!`,
          adminMessage: `${employeeName}'s Annual Leave request (${dateRange}) has been approved successfully.`
        };

      case 'sick':
        return {
          title: 'Sick Leave Approved',
          message: `${employeeName}'s Sick Leave request from ${dateRange} has been approved. We hope you feel better soon. Take care!`,
          adminMessage: `${employeeName}'s Sick Leave request (${dateRange}) has been approved successfully.`
        };

      case 'family':
        return {
          title: 'Family Leave Approved',
          message: `${employeeName}'s Family Responsibility Leave request from ${dateRange} has been approved. We hope everything goes well with your family matters.`,
          adminMessage: `${employeeName}'s Family Responsibility Leave request (${dateRange}) has been approved successfully.`
        };

      case 'unpaid':
        return {
          title: 'Unpaid Leave Approved',
          message: `${employeeName}'s ${leaveTypeName} request from ${dateRange} has been approved.`,
          adminMessage: `${employeeName}'s ${leaveTypeName} request (${dateRange}) has been approved successfully.`
        };

      default:
        // Check for maternity/paternity in the name
        if (leaveTypeName.toLowerCase().includes('maternity') || leaveTypeName.toLowerCase().includes('paternity')) {
          const leaveTypeDisplay = leaveTypeName.toLowerCase().includes('maternity') ? 'Maternity' : 'Paternity';
          return {
            title: `${leaveTypeDisplay} Leave Approved! 🎊`,
            message: `Congratulations! ${employeeName}'s ${leaveTypeDisplay} Leave request from ${dateRange} has been approved. Wishing you and your family all the best!`,
            adminMessage: `${employeeName}'s ${leaveTypeDisplay} Leave request (${dateRange}) has been approved successfully.`
          };
        }

        // Default message for other leave types
        return {
          title: 'Leave Request Approved',
          message: `${employeeName}'s ${leaveTypeName} request from ${dateRange} has been approved.`,
          adminMessage: `${employeeName}'s ${leaveTypeName} request (${dateRange}) has been approved successfully.`
        };
    }
  } else if (action === 'rejected') {
    return {
      title: 'Leave Request Rejected',
      message: `${employeeName}'s ${leaveTypeName} request from ${dateRange} has been rejected.${rejectionReason ? ` Reason: ${rejectionReason}` : ''}`,
      adminMessage: `${employeeName}'s ${leaveTypeName} request (${dateRange}) has been rejected successfully.`
    };
  }

  // Fallback message
  return {
    title: 'Leave Request Updated',
    message: `${employeeName}'s ${leaveTypeName} request has been ${action}.`,
    adminMessage: `${employeeName}'s ${leaveTypeName} request has been ${action} successfully.`
  };
}
const Holiday = require("../../models/Holiday");
const { getPublicHolidays } = require("../../utils/holidayCalculator");

// Debug middleware for all leave API routes
router.use((req, res, next) => {
  console.log("\n=== Leave API Route Handler ===");
  console.log("Path:", req.path);
  console.log("Method:", req.method);
  console.log("Full URL:", req.originalUrl);
  console.log("Body:", req.body);
  console.log(
    "User:",
    req.user ? { id: req.user._id, email: req.user.email } : "No user"
  );
  next();
});

// Get all leave types for a company
router.get("/types", ensureAuthenticated, async (req, res) => {
  try {
    const leaveTypes = await LeaveType.find({
      company: req.user.company,
    }).populate(
      "employeeAllocations.employee",
      "firstName lastName employeeNumber"
    );
    res.json(leaveTypes);
  } catch (error) {
    console.error("Error fetching leave types:", error);
    res.status(500).json({ message: "Error fetching leave types" });
  }
});

// Get a specific leave type
router.get("/types/:id", ensureAuthenticated, async (req, res) => {
  try {
    const leaveType = await LeaveType.findOne({
      _id: req.params.id,
      company: req.user.company,
    }).populate(
      "employeeAllocations.employee",
      "firstName lastName employeeNumber"
    );

    if (!leaveType) {
      return res.status(404).json({ message: "Leave type not found" });
    }

    res.json(leaveType);
  } catch (error) {
    console.error("Error fetching leave type:", error);
    res.status(500).json({ message: "Error fetching leave type" });
  }
});

// Create a new leave type
router.post("/types", ensureAuthenticated, async (req, res) => {
  try {
    const {
      name,
      category,
      description,
      daysPerYear,
      accrualRate,
      carryOverLimit,
      paidLeave,
      requiresApproval,
      requiresDocument,
      documentRequiredAfterDays,
      minDaysNotice,
      gender,
      allowEmployeeSpecificAllocation,
      companyCode,
    } = req.body;

    // Get company from company code
    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({ message: "Company not found" });
    }

    // Validate custom leave type requirements
    if (category === 'custom') {
      if (!name || !name.trim() || name.trim().length < 3) {
        return res.status(400).json({
          message: "Custom leave types require a meaningful name (at least 3 characters)."
        });
      }

      if (!/[a-zA-Z]/.test(name.trim())) {
        return res.status(400).json({
          message: "Custom leave type name must contain letters."
        });
      }
    }

    // Generate appropriate name based on category and custom input
    let leaveTypeName;
    if (category === 'custom' && name && name.trim()) {
      // For custom leave types, use the provided name
      leaveTypeName = name.trim();
    } else if (name && name.trim()) {
      // If a name is explicitly provided, use it
      leaveTypeName = name.trim();
    } else {
      // Generate default name based on category
      leaveTypeName = category.charAt(0).toUpperCase() + category.slice(1) + " Leave";
    }

    const leaveType = new LeaveType({
      company: company._id,
      name: leaveTypeName,
      category,
      description,
      daysPerYear,
      accrualRate,
      carryOverLimit,
      paidLeave,
      requiresApproval,
      requiresDocument,
      documentRequiredAfterDays,
      minDaysNotice,
      gender,
      allowEmployeeSpecificAllocation,
      createdBy: req.user._id,
    });

    try {
      const savedLeaveType = await leaveType.save();
      res.status(201).json(savedLeaveType);
    } catch (error) {
      console.error("Error creating leave type:", error);
      
      // Check for duplicate key error
      if (error.code === 11000 && error.keyPattern && error.keyPattern.company && error.keyPattern.name) {
        const duplicateName = error.keyValue.name;
        if (category === 'custom') {
          return res.status(400).json({
            message: `A leave type with the name "${duplicateName}" already exists. Please choose a unique name for your custom leave type.`
          });
        } else {
          return res.status(400).json({
            message: `A leave type with the name "${duplicateName}" already exists for this company. Please use a different name.`
          });
        }
      }
      
      res.status(500).json({ message: "Error creating leave type" });
    }
  } catch (error) {
    console.error("Error creating leave type:", error);
    res.status(500).json({ message: "Error creating leave type" });
  }
});

// Update a leave type
router.put("/types/:id", ensureAuthenticated, async (req, res) => {
  try {
    const leaveType = await LeaveType.findOneAndUpdate(
      { _id: req.params.id, company: req.user.company },
      {
        ...req.body,
        updatedBy: req.user._id,
      },
      { new: true }
    );

    if (!leaveType) {
      return res.status(404).json({ message: "Leave type not found" });
    }

    res.json(leaveType);
  } catch (error) {
    console.error("Error updating leave type:", error);
    res.status(500).json({ message: "Error updating leave type" });
  }
});

// Toggle leave type status
router.put("/types/:id/toggle", ensureAuthenticated, async (req, res) => {
  try {
    const leaveType = await LeaveType.findOne({
      _id: req.params.id,
      company: req.user.company,
    });

    if (!leaveType) {
      return res.status(404).json({ message: "Leave type not found" });
    }

    leaveType.active = !leaveType.active;
    leaveType.updatedBy = req.user._id;
    await leaveType.save();

    res.json(leaveType);
  } catch (error) {
    console.error("Error toggling leave type status:", error);
    res.status(500).json({ message: "Error toggling leave type status" });
  }
});

// Get all leave requests (for calendar view)
router.get("/requests/all", ensureAuthenticated, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Build query
    const query = { company: req.user.currentCompany };
    if (startDate && endDate) {
      query.startDate = { $gte: new Date(startDate) };
      query.endDate = { $lte: new Date(endDate) };
    }

    const leaveRequests = await LeaveRequest.find(query)
      .populate("employee", "firstName lastName")
      .populate("leaveType", "name category")
      .sort("-startDate");

    res.json({
      success: true,
      data: leaveRequests
    });
  } catch (error) {
    console.error("Error fetching all leave requests:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch leave requests"
    });
  }
});

// Get leave requests for an employee
router.get("/requests/:employeeId", ensureAuthenticated, async (req, res) => {
  try {
    const requests = await LeaveRequest.find({
      company: req.user.currentCompany,
      employee: req.params.employeeId,
    })
      .populate("leaveType")
      .sort({ startDate: -1 });

    res.json({ success: true, data: requests });
  } catch (error) {
    console.error("Error fetching leave requests:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to fetch leave requests" });
  }
});

// Create a new leave request
router.post("/request", ensureAuthenticated, async (req, res) => {
  console.log("\n=== Leave Request API Called ===");
  console.log("Request body:", req.body);
  console.log(
    "User:",
    req.user
      ? {
          id: req.user._id,
          email: req.user.email,
          currentCompany: req.user.currentCompany,
        }
      : "No user"
  );
  console.log("Headers:", req.headers);
  console.log("CSRF Token:", req.headers["x-csrf-token"]);
  console.log("Route matched: POST /api/leave/request");

  try {
    const {
      employee,
      leaveType: leaveTypeId,
      startDate,
      endDate,
      numberOfDays,
      reason,
    } = req.body;

    console.log("\nValidating request data:", {
      employee,
      leaveTypeId,
      startDate,
      endDate,
      numberOfDays,
      reason,
    });

    // Validate leave type
    const leaveType = await LeaveType.findOne({
      _id: leaveTypeId,
      company: req.user.currentCompany,
      active: true,
    }).populate("employeeAllocations.employee");

    if (!leaveType) {
      console.log("Leave type not found");
      return res.status(400).json({
        success: false,
        message: "Invalid or inactive leave type",
      });
    }

    // Validate gender restriction
    const employeeData = await Employee.findById(employee);
    if (!employeeData) {
      return res.status(400).json({
        success: false,
        message: "Employee not found",
      });
    }

    if (
      leaveType.gender !== "all" &&
      leaveType.gender !== employeeData.gender
    ) {
      return res.status(400).json({
        success: false,
        message: "This leave type is not available for your gender",
      });
    }

    // Validate minimum notice period
    const today = new Date();
    const requestStart = new Date(startDate);
    const noticeDays = Math.ceil(
      (requestStart - today) / (1000 * 60 * 60 * 24)
    );

    if (noticeDays < leaveType.minDaysNotice) {
      return res.status(400).json({
        success: false,
        message: `This leave type requires ${leaveType.minDaysNotice} days notice`,
      });
    }

    // Check for overlapping leave requests
    const overlappingRequests = await LeaveRequest.find({
      employee,
      status: { $in: ["pending", "approved"] },
      $or: [
        {
          startDate: { $lte: new Date(startDate) },
          endDate: { $gte: new Date(startDate) },
        },
        {
          startDate: { $lte: new Date(endDate) },
          endDate: { $gte: new Date(endDate) },
        },
      ],
    });

    if (overlappingRequests.length > 0) {
      return res.status(400).json({
        success: false,
        message: "You already have a leave request for these dates",
      });
    }

    // Calculate actual available balance
    let availableBalance = 0;
    const balance = await LeaveBalance.findOne({
      company: req.user.currentCompany,
      employee,
      leaveType: leaveTypeId,
      year: new Date(startDate).getFullYear(),
    });

    // Check for employee-specific allocation
    const specificAllocation = leaveType.employeeAllocations.find(
      (a) => a.employee._id.toString() === employee
    );

    const totalAllocation = specificAllocation
      ? specificAllocation.daysPerYear
      : leaveType.daysPerYear;

    if (balance) {
      if (leaveType.accrualRate === "yearly") {
        availableBalance = balance.remaining;
      } else if (leaveType.accrualRate === "monthly") {
        availableBalance = leaveType.calculateAccruedDays(
          employeeData.startDate,
          new Date()
        );
      }
    } else {
      // If no balance record exists, create it
      availableBalance =
        leaveType.accrualRate === "yearly"
          ? totalAllocation
          : leaveType.calculateAccruedDays(employeeData.startDate, new Date());

      await LeaveBalance.create({
        company: req.user.currentCompany,
        employee,
        leaveType: leaveTypeId,
        year: new Date(startDate).getFullYear(),
        totalAllocation,
        remaining: availableBalance,
        used: 0,
        pending: 0,
        createdBy: req.user._id,
      });
    }

    if (availableBalance < numberOfDays) {
      return res.status(400).json({
        success: false,
        message: `Insufficient leave balance. Available: ${availableBalance} days`,
      });
    }

    // Create leave request
    const leaveRequest = new LeaveRequest({
      company: req.user.currentCompany,
      employee,
      leaveType: leaveTypeId,
      startDate,
      endDate,
      numberOfDays,
      reason,
      status: "pending",
      createdBy: req.user._id,
    });

    await leaveRequest.save();

    // Update leave balance
    if (balance) {
      balance.pending += parseFloat(numberOfDays);
      balance.remaining = balance.calculateRemaining();
      await balance.save();
    }

    // Send email notification if enabled
    if (process.env.ENABLE_EMAIL_NOTIFICATIONS === "true") {
      try {
        await sendLeaveStatusNotification(leaveRequest, "submitted");
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
      }
    }

    res.status(201).json({
      success: true,
      message: "Leave request submitted successfully",
      data: leaveRequest,
    });
  } catch (error) {
    console.error("Error creating leave request:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create leave request",
      error: error.message,
    });
  }
});

// Approve or reject leave request
router.put("/request/:id/:action", ensureAuthenticated, async (req, res) => {
  let action; // Declare action outside try-catch for broader scope
  try {
    const { id } = req.params;
    action = req.params.action; // Assign value inside try block
    const { rejectionReason } = req.body;

    // Validate action
    if (!["approve", "reject"].includes(action)) {
      return res.status(400).json({
        success: false,
        message: "Invalid action. Must be either 'approve' or 'reject'",
      });
    }

    const leaveRequest = await LeaveRequest.findOne({
      _id: id,
      company: req.user.currentCompany,
    }).populate("employee leaveType");

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: "Leave request not found",
      });
    }

    // Debugging: Log leave type details
    console.log("DEBUG - LeaveType details for current request:", {
      name: leaveRequest.leaveType.name,
      daysPerYear: leaveRequest.leaveType.daysPerYear,
      accrualRate: leaveRequest.leaveType.accrualRate,
      monthlyAmount: leaveRequest.leaveType.accrualSettings?.monthlyAmount,
      proRateFirstYear: leaveRequest.leaveType.accrualSettings?.proRateFirstYear,
      waitingPeriodMonths: leaveRequest.leaveType.accrualSettings?.waitingPeriodMonths,
    });

    if (leaveRequest.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: "Can only approve/reject pending requests",
      });
    }

    // Fetch or create LeaveBalance
    let balance = await LeaveBalance.findOne({
      company: req.user.currentCompany,
      employee: leaveRequest.employee._id,
      leaveType: leaveRequest.leaveType._id,
      year: new Date(leaveRequest.startDate).getFullYear(),
    });

    if (action === "approve") {
      // Check for overlapping approved leaves
      const overlappingRequests = await LeaveRequest.find({
        company: req.user.currentCompany,
        employee: leaveRequest.employee._id,
        _id: { $ne: leaveRequest._id },
        status: "approved",
        $or: [
          {
            startDate: { $lte: leaveRequest.startDate },
            endDate: { $gte: leaveRequest.startDate },
          },
          {
            startDate: { $lte: leaveRequest.endDate },
            endDate: { $gte: leaveRequest.endDate },
          },
        ],
      });

      if (overlappingRequests.length > 0) {
        return res.status(400).json({
          success: false,
          message:
            "Employee has overlapping approved leave requests for these dates",
        });
      }

      // If no balance exists, create one
      if (!balance) {
        console.log("DEBUG - No existing LeaveBalance found, creating a new one.");
        let initialAllocation = leaveRequest.leaveType.daysPerYear;
        let initialRemaining = initialAllocation;

        // If accrual is monthly, calculate based on accrued days up to start of leave year
        // Ensure leaveRequest.employee.startDate is valid for calculation
        if (leaveRequest.leaveType.accrualRate === "monthly" && leaveRequest.employee && leaveRequest.employee.startDate) {
          // Assuming leaveType.calculateAccruedDays method exists on the LeaveType model
          initialRemaining = leaveRequest.leaveType.calculateAccruedDays(
            leaveRequest.employee.startDate,
            new Date(leaveRequest.startDate).getFullYear()
          );
          console.log(`DEBUG - Monthly accrual: Calculated initial remaining as ${initialRemaining}`);
        } else if (leaveRequest.leaveType.accrualRate === "monthly" && (!leaveRequest.employee || !leaveRequest.employee.startDate)) {
            console.warn("WARN - Monthly accrual requested but employee startDate is missing. Using full allocation.");
        }

        // If this is a new balance being created for a pending request,
        // the 'pending' amount should reflect the current request's days
        // to correctly calculate 'remaining' for the initial check.
        const pendingDaysForCurrentRequest = leaveRequest.status === 'pending' ? leaveRequest.numberOfDays : 0;
        
        balance = new LeaveBalance({
          company: req.user.currentCompany,
          employee: leaveRequest.employee._id,
          leaveType: leaveRequest.leaveType._id,
          year: new Date(leaveRequest.startDate).getFullYear(),
          totalAllocation: initialAllocation,
          used: 0,
          pending: pendingDaysForCurrentRequest, // Initialize pending with the current request's days
          remaining: initialRemaining - pendingDaysForCurrentRequest, // Adjust remaining accordingly
          createdBy: req.user._id,
        });
        await balance.save();
        console.log("DEBUG - New LeaveBalance created successfully:", balance);
      } else {
        console.log("DEBUG - Existing LeaveBalance found:", balance);
      }

      // Debugging: Log user and forceApprove status before balance check
      console.log('DEBUG - Leave Approval:');
      console.log('  User ID:', req.user._id);
      console.log('  User Role Name:', req.user.roleName); 
      console.log('  Force Approve Flag (from request):', req.body.forceApprove);
      console.log('  Leave Days Requested:', leaveRequest.numberOfDays);
      console.log('  Available Balance:', balance ? balance.remaining : 'No balance record found');
      
      const hasInsufficientBalance = !balance || balance.remaining < leaveRequest.numberOfDays;
      const isOverrideAllowed = req.body.forceApprove && req.user.roleName === 'owner'; // This is the core logic for bypass

      console.log('DEBUG - Has Insufficient Balance:', hasInsufficientBalance);
      console.log('DEBUG - Is Override Allowed (Force Approve by Owner):', isOverrideAllowed);

      // Check for insufficient balance, allow override by admin with forceApprove flag
      if (hasInsufficientBalance && !isOverrideAllowed) {
        // Debugging: Log when insufficient balance check is triggered
        console.log('DEBUG - Insufficient balance check triggered for leave request:', leaveRequest._id);
        return res.status(400).json({
          success: false,
          message: "Insufficient leave balance.",
          insufficientBalance: true, // Custom flag to indicate this specific error
          details: {
            required: leaveRequest.numberOfDays,
            available: balance ? balance.remaining : 0,
          },
        });
      }

      leaveRequest.status = "approved";
      leaveRequest.approvedBy = req.user._id;
      leaveRequest.approvalDate = new Date();
      // Update balance with proper validation to prevent negative values
      if (balance) {
        console.log('DEBUG - Balance before approval update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          requestDays: leaveRequest.numberOfDays
        });

        // Ensure pending doesn't go negative
        const pendingToDeduct = Math.min(balance.pending, leaveRequest.numberOfDays);
        const remainingToDeduct = leaveRequest.numberOfDays - pendingToDeduct;

        // Update balance fields safely
        balance.pending = Math.max(0, balance.pending - leaveRequest.numberOfDays);
        balance.used += leaveRequest.numberOfDays;
        balance.remaining = balance.calculateRemaining();

        console.log('DEBUG - Balance after approval update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          pendingDeducted: pendingToDeduct,
          remainingDeducted: remainingToDeduct
        });
      }
    } else {
      if (!rejectionReason) {
        return res.status(400).json({
          success: false,
          message: "Rejection reason is required",
        });
      }

      leaveRequest.status = "rejected";
      leaveRequest.rejectionReason = rejectionReason;
      leaveRequest.approvedBy = req.user._id;
      leaveRequest.approvalDate = new Date();

      // Update balance only if it exists, with validation to prevent negative values
      if (balance) {
        console.log('DEBUG - Balance before rejection update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          requestDays: leaveRequest.numberOfDays
        });

        // Ensure pending doesn't go negative during rejection
        balance.pending = Math.max(0, balance.pending - leaveRequest.numberOfDays);
        balance.remaining = balance.calculateRemaining();

        console.log('DEBUG - Balance after rejection update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining
        });
      }
    }

    // Save leave request and balance with validation
    await leaveRequest.save();
    if (balance) {
      console.log('DEBUG - Saving balance after approval/rejection:', {
        employee: balance.employee,
        leaveType: balance.leaveType,
        used: balance.used,
        pending: balance.pending,
        remaining: balance.remaining,
        totalAllocation: balance.totalAllocation
      });

      // Validate balance before saving
      const validation = balance.validateBalance();
      if (!validation.isValid) {
        console.error('Balance validation failed:', validation.errors);
        // Continue with save but log the issues
      }

      await balance.save();
    }

    // Send WhatsApp notification for both approval and rejection
    if (!leaveRequest.notificationSent) {
      try {
        if (action === "approve") {
          await whatsappService.sendLeaveApprovalNotification(leaveRequest);
          console.log("WhatsApp approval notification triggered.");
        } else if (action === "reject") {
          await whatsappService.sendLeaveRejectionNotification(leaveRequest, rejectionReason);
          console.log("WhatsApp rejection notification triggered.");
        }
      } catch (waError) {
        console.error(`Failed to send WhatsApp ${action} notification:`, waError);
      }
    }

    // Send email notification
    if (process.env.ENABLE_EMAIL_NOTIFICATIONS === "true") {
      try {
        await sendLeaveStatusNotification(
          leaveRequest,
          action === "approve" ? "approved" : "rejected",
          rejectionReason
        );
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
      }
    }

    // Generate context-aware notification message
    const notificationMessages = generateLeaveNotificationMessage(leaveRequest, action, rejectionReason);

    res.json({
      success: true,
      message: `Leave request ${action}ed successfully`,
      data: leaveRequest,
      notification: notificationMessages
    });
  } catch (error) {
    console.error(`Error ${action}ing leave request:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to ${action} leave request`,
      details: error.message,
    });
  }
});

// Cancel a leave request
router.put("/request/:id/cancel", ensureAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const leaveRequest = await LeaveRequest.findOne({
      _id: id,
      company: req.user.currentCompany,
    }).populate("employee leaveType");

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: "Leave request not found",
      });
    }

    // Only allow cancellation of pending requests
    if (leaveRequest.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: "Only pending leave requests can be cancelled",
      });
    }

    // Ensure only the employee who created the request or an admin can cancel it
    if (
      leaveRequest.employee._id.toString() !== req.user._id.toString() &&
      !req.user.isAdmin
    ) {
      return res.status(403).json({
        success: false,
        message: "Unauthorized to cancel this leave request",
      });
    }

    // Update leave balance
    const balance = await LeaveBalance.findOne({
      company: req.user.currentCompany,
      employee: leaveRequest.employee._id,
      leaveType: leaveRequest.leaveType._id,
      year: new Date(leaveRequest.startDate).getFullYear(),
    });

    if (balance) {
      console.log('DEBUG - Balance before cancellation update:', {
        used: balance.used,
        pending: balance.pending,
        remaining: balance.remaining,
        requestDays: leaveRequest.numberOfDays
      });

      // Ensure pending doesn't go negative during cancellation
      balance.pending = Math.max(0, balance.pending - leaveRequest.numberOfDays);
      balance.remaining = balance.calculateRemaining();

      console.log('DEBUG - Balance after cancellation update:', {
        used: balance.used,
        pending: balance.pending,
        remaining: balance.remaining
      });

      await balance.save();
    }

    // Update leave request
    leaveRequest.status = "cancelled";
    leaveRequest.rejectionReason = reason || "Cancelled by employee";
    leaveRequest.updatedBy = req.user._id;
    await leaveRequest.save();

    // Send email notification
    if (process.env.ENABLE_EMAIL_NOTIFICATIONS === "true") {
      try {
        await sendLeaveStatusNotification(leaveRequest, "cancelled", reason);
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
      }
    }

    res.json({
      success: true,
      message: "Leave request cancelled successfully",
      data: leaveRequest,
    });
  } catch (error) {
    console.error("Error cancelling leave request:", error);
    res.status(500).json({
      success: false,
      message: "Failed to cancel leave request",
      details: error.message,
    });
  }
});

// Delete a leave request
router.delete("/request/:id", ensureAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;

    const leaveRequest = await LeaveRequest.findOne({
      _id: id,
      company: req.user.currentCompany,
    }).populate("employee leaveType");

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: "Leave request not found",
      });
    }

    // Only allow deletion of pending or cancelled requests
    if (!["pending", "cancelled"].includes(leaveRequest.status)) {
      return res.status(400).json({
        success: false,
        message: "Only pending or cancelled leave requests can be deleted",
      });
    }

    // Ensure only the employee who created the request or an admin can delete it
    if (
      leaveRequest.employee._id.toString() !== req.user._id.toString() &&
      !req.user.isAdmin
    ) {
      return res.status(403).json({
        success: false,
        message: "Unauthorized to delete this leave request",
      });
    }

    // If the request was pending, update leave balance
    if (leaveRequest.status === "pending") {
      const balance = await LeaveBalance.findOne({
        company: req.user.currentCompany,
        employee: leaveRequest.employee._id,
        leaveType: leaveRequest.leaveType._id,
        year: new Date(leaveRequest.startDate).getFullYear(),
      });

      if (balance) {
        console.log('DEBUG - Balance before deletion update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          requestDays: leaveRequest.numberOfDays
        });

        // Ensure pending doesn't go negative during deletion
        balance.pending = Math.max(0, balance.pending - leaveRequest.numberOfDays);
        balance.remaining = balance.calculateRemaining();

        console.log('DEBUG - Balance after deletion update:', {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining
        });

        await balance.save();
      }
    }

    // Delete the leave request
    await LeaveRequest.findByIdAndDelete(id);

    res.json({
      success: true,
      message: "Leave request deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting leave request:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete leave request",
      details: error.message,
    });
  }
});

// Get leave balances for an employee
router.get("/balance/:employeeId", ensureAuthenticated, async (req, res) => {
  try {
    const balances = await LeaveBalance.find({
      company: req.user.currentCompany,
      employee: req.params.employeeId,
      year: new Date().getFullYear(),
    }).populate("leaveType");

    res.json({ success: true, data: balances });
  } catch (error) {
    console.error("Error fetching leave balances:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to fetch leave balances" });
  }
});

// Get updated leave types with current balances for an employee
router.get("/types-with-balances/:employeeId", ensureAuthenticated, async (req, res) => {
  try {
    const { employeeId } = req.params;

    // Get employee data
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).json({ success: false, message: "Employee not found" });
    }

    // Get leave types for the company
    const leaveTypes = await LeaveType.find({
      company: req.user.currentCompany,
      active: true,
      $or: [{ gender: "all" }, { gender: employee.gender }],
    });

    // Get leave balances
    const leaveBalances = await LeaveBalance.find({
      employee: employeeId,
      year: new Date().getFullYear(),
    }).populate("leaveType");

    // Calculate updated balances
    const leaveTypesWithBalances = leaveTypes.map((type) => {
      const balance = leaveBalances.find(
        (b) => b.leaveType && b.leaveType._id.toString() === type._id.toString()
      );

      let availableBalance = 0;
      let monthlyAmount = 0;

      // Check for employee-specific allocation
      const specificAllocation = type.employeeAllocations?.find(
        (a) => a.employee.toString() === employeeId
      );

      const totalAllocation = specificAllocation
        ? specificAllocation.daysPerYear
        : type.daysPerYear;

      if (balance) {
        if (type.accrualRate === "yearly") {
          // Use the calculateRemaining method to ensure accuracy
          availableBalance = balance.calculateRemaining();
        } else if (type.accrualRate === "monthly") {
          monthlyAmount = Number((totalAllocation / 12).toFixed(2));
          availableBalance = type.calculateAccruedDays(
            employee.startDate,
            new Date()
          );
        }
      } else {
        // If no balance record exists, create default values
        availableBalance =
          type.accrualRate === "yearly"
            ? totalAllocation
            : type.calculateAccruedDays(employee.startDate, new Date());
      }

      return {
        _id: type._id,
        name: type.name,
        icon: type.icon || "ph-calendar",
        balance: availableBalance,
        total: totalAllocation,
        accrualRate: type.accrualRate,
        monthlyAmount,
        minDaysNotice: type.minDaysNotice,
        requiresDocument: type.requiresDocument,
        documentRequiredAfterDays: type.documentRequiredAfterDays,
        gender: type.gender,
        // Include detailed balance info for debugging
        balanceDetails: balance ? {
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          calculated: balance.calculateRemaining()
        } : null
      };
    });

    res.json({ success: true, data: leaveTypesWithBalances });
  } catch (error) {
    console.error("Error fetching leave types with balances:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch leave types with balances",
      error: error.message
    });
  }
});

// Adjust leave balance
router.post("/balance/adjust", ensureAuthenticated, async (req, res) => {
  try {
    const { balanceId, adjustment, reason } = req.body;

    const balance = await LeaveBalance.findOne({
      _id: balanceId,
      company: req.user.currentCompany,
    });

    if (!balance) {
      return res.status(404).json({
        success: false,
        message: "Leave balance not found",
      });
    }

    balance.adjustments.push({
      amount: Number(adjustment),
      reason,
      adjustedBy: req.user._id,
    });

    balance.totalAllocation += Number(adjustment);
    balance.remaining = balance.calculateRemaining();
    await balance.save();

    res.json({ success: true, data: balance });
  } catch (error) {
    console.error("Error adjusting leave balance:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to adjust leave balance" });
  }
});

// Add employee-specific allocation
router.post("/types/:id/allocations", ensureAuthenticated, async (req, res) => {
  try {
    const {
      employeeId,
      employeeDaysPerYear,
      startDate,
      endDate,
      allocationReason,
    } = req.body;

    const leaveType = await LeaveType.findOne({
      _id: req.params.id,
      company: req.user.company,
    });

    if (!leaveType) {
      return res.status(404).json({ message: "Leave type not found" });
    }

    if (!leaveType.allowEmployeeSpecificAllocation) {
      return res.status(400).json({
        message:
          "Employee-specific allocations not allowed for this leave type",
      });
    }

    // Remove any existing allocation for this employee
    leaveType.employeeAllocations = leaveType.employeeAllocations.filter(
      (allocation) => allocation.employee.toString() !== employeeId
    );

    // Add new allocation
    leaveType.employeeAllocations.push({
      employee: employeeId,
      daysPerYear: employeeDaysPerYear,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : undefined,
      reason: allocationReason,
    });

    leaveType.updatedBy = req.user._id;
    await leaveType.save();

    // Update leave balance for the employee
    await LeaveBalance.findOneAndUpdate(
      {
        company: req.user.company,
        employee: employeeId,
        leaveType: leaveType._id,
        year: new Date().getFullYear(),
      },
      {
        $set: {
          totalAllocation: employeeDaysPerYear,
          remaining: employeeDaysPerYear,
        },
      },
      { upsert: true }
    );

    res.json(leaveType);
  } catch (error) {
    console.error("Error adding employee allocation:", error);
    res.status(500).json({ message: "Error adding employee allocation" });
  }
});

// Remove employee-specific allocation
router.delete(
  "/types/:id/allocations/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const leaveType = await LeaveType.findOne({
        _id: req.params.id,
        company: req.user.company,
      });

      if (!leaveType) {
        return res.status(404).json({ message: "Leave type not found" });
      }

      leaveType.employeeAllocations = leaveType.employeeAllocations.filter(
        (allocation) => allocation.employee.toString() !== req.params.employeeId
      );

      leaveType.updatedBy = req.user._id;
      await leaveType.save();

      // Reset leave balance to default
      await LeaveBalance.findOneAndUpdate(
        {
          company: req.user.company,
          employee: req.params.employeeId,
          leaveType: leaveType._id,
          year: new Date().getFullYear(),
        },
        {
          $set: {
            totalAllocation: leaveType.daysPerYear,
            remaining: leaveType.daysPerYear,
          },
        },
        { upsert: true }
      );

      res.json(leaveType);
    } catch (error) {
      console.error("Error removing employee allocation:", error);
      res.status(500).json({ message: "Error removing employee allocation" });
    }
  }
);

// Get comprehensive leave balances for all employees
router.get("/employee-balances", ensureAuthenticated, async (req, res) => {
  console.log("\n=== Employee Leave Balances API Called ===");
  console.log("Query params:", req.query);
  console.log(
    "User:",
    req.user ? { id: req.user._id, email: req.user.email } : "No user"
  );

  try {
    const currentYear = new Date().getFullYear();

    // Get all active employees for the company
    const employees = await Employee.find({
      company: req.user.currentCompany,
      status: "Active",
    }).select('firstName lastName email startDate gender').sort({ firstName: 1, lastName: 1 });

    console.log(`Found ${employees.length} active employees`);

    // Get all active leave types for the company
    const leaveTypes = await LeaveType.find({
      company: req.user.currentCompany,
      active: true,
    }).sort({ name: 1 });

    console.log(`Found ${leaveTypes.length} active leave types`);

    // Get all leave balances for the current year
    const leaveBalances = await LeaveBalance.find({
      company: req.user.currentCompany,
      year: currentYear,
    }).populate('employee', 'firstName lastName email')
      .populate('leaveType', 'name category daysPerYear accrualRate');

    console.log(`Found ${leaveBalances.length} leave balance records`);

    // Get company settings for leave year cycle
    const company = await Company.findById(req.user.currentCompany);
    const leaveYearStart = company?.leaveYearStart || 'january';

    // Calculate leave cycle dates
    const getLeaveYearDates = (year, startMonth) => {
      const monthMap = {
        january: 0, february: 1, march: 2, april: 3, may: 4, june: 5,
        july: 6, august: 7, september: 8, october: 9, november: 10, december: 11
      };

      const startMonthIndex = monthMap[startMonth.toLowerCase()] || 0;
      const startDate = new Date(year, startMonthIndex, 1);
      const endDate = new Date(year + 1, startMonthIndex, 0); // Last day of the cycle

      return { startDate, endDate };
    };

    const { startDate: cycleStart, endDate: cycleEnd } = getLeaveYearDates(currentYear, leaveYearStart);

    // Build comprehensive employee balance data
    const employeeBalanceData = [];

    for (const employee of employees) {
      for (const leaveType of leaveTypes) {
        // Check if leave type applies to this employee's gender
        if (leaveType.gender && leaveType.gender !== 'all' && leaveType.gender !== employee.gender) {
          continue;
        }

        // Find existing balance record
        const balanceRecord = leaveBalances.find(
          b => b.employee && b.leaveType &&
               b.employee._id.toString() === employee._id.toString() &&
               b.leaveType._id.toString() === leaveType._id.toString()
        );

        // Check for employee-specific allocation
        const specificAllocation = leaveType.employeeAllocations?.find(
          a => a.employee.toString() === employee._id.toString()
        );

        const totalAllocation = specificAllocation
          ? specificAllocation.daysPerYear
          : leaveType.daysPerYear;

        let used = 0;
        let pending = 0;
        let remaining = totalAllocation;
        let carryOver = 0;

        if (balanceRecord) {
          used = balanceRecord.used || 0;
          pending = balanceRecord.pending || 0;
          carryOver = balanceRecord.carryOver || 0;
          remaining = balanceRecord.calculateRemaining();
        }

        // Calculate renewal date (next cycle start)
        const renewalDate = new Date(cycleEnd);
        renewalDate.setDate(renewalDate.getDate() + 1);

        employeeBalanceData.push({
          employeeId: employee._id,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeEmail: employee.email,
          leaveTypeId: leaveType._id,
          leaveTypeName: leaveType.name,
          leaveTypeCategory: leaveType.category,
          totalAllocation: totalAllocation,
          used: used,
          pending: pending,
          remaining: remaining,
          carryOver: carryOver,
          cyclePeriod: `${cycleStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })} - ${cycleEnd.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`,
          renewalDate: renewalDate.toISOString().split('T')[0],
          accrualRate: leaveType.accrualRate || 'yearly'
        });
      }
    }

    console.log(`Generated ${employeeBalanceData.length} employee balance records`);

    res.json({
      success: true,
      data: employeeBalanceData,
      summary: {
        totalEmployees: employees.length,
        totalLeaveTypes: leaveTypes.length,
        totalRecords: employeeBalanceData.length,
        leaveYear: {
          start: cycleStart.toISOString().split('T')[0],
          end: cycleEnd.toISOString().split('T')[0],
          startMonth: leaveYearStart
        }
      }
    });
  } catch (error) {
    console.error("Error fetching employee leave balances:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to fetch employee leave balances" });
  }
});

// Get holidays for a specific year, date range, or multiple years
router.get("/holidays", ensureAuthenticated, async (req, res) => {
  console.log("\n=== Leave Holidays API Called ===");
  console.log("Query params:", req.query);
  console.log(
    "User:",
    req.user ? { id: req.user._id, email: req.user.email } : "No user"
  );

  try {
    const { year, startYear, endYear, startDate, endDate } = req.query;
    const currentYear = new Date().getFullYear();

    let yearsToGenerate = [];

    // Determine which years to generate holidays for
    if (startYear && endYear) {
      // Date range specified
      const start = parseInt(startYear);
      const end = parseInt(endYear);

      // Validate year range (allow reasonable range: current year ± 5 years)
      if (start < currentYear - 5 || end > currentYear + 5 || start > end) {
        return res.status(400).json({
          success: false,
          message: "Invalid year range. Please specify years within a reasonable range."
        });
      }

      for (let y = start; y <= end; y++) {
        yearsToGenerate.push(y);
      }
    } else if (year) {
      // Single year specified
      const targetYear = parseInt(year);
      if (targetYear < currentYear - 5 || targetYear > currentYear + 5) {
        return res.status(400).json({
          success: false,
          message: "Invalid year. Please specify a year within a reasonable range."
        });
      }
      yearsToGenerate.push(targetYear);
    } else if (startDate && endDate) {
      // Date range specified - extract years from dates
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid date format. Please use YYYY-MM-DD format."
        });
      }

      const startYearFromDate = start.getFullYear();
      const endYearFromDate = end.getFullYear();

      for (let y = startYearFromDate; y <= endYearFromDate; y++) {
        yearsToGenerate.push(y);
      }
    } else {
      // Default: current year and adjacent years for better calendar navigation
      yearsToGenerate = [currentYear - 1, currentYear, currentYear + 1];
    }

    console.log(`Generating holidays for years: ${yearsToGenerate.join(', ')}`);

    // Generate South African public holidays for all specified years
    let allPublicHolidays = [];
    for (const targetYear of yearsToGenerate) {
      const yearHolidays = getPublicHolidays(targetYear);
      allPublicHolidays = allPublicHolidays.concat(yearHolidays);
    }

    console.log(`Generated ${allPublicHolidays.length} public holidays for ${yearsToGenerate.length} year(s)`);

    // Fetch company-specific holidays for the same period
    const minYear = Math.min(...yearsToGenerate);
    const maxYear = Math.max(...yearsToGenerate);

    const companyHolidays = await Holiday.find({
      company: req.user.currentCompany,
      date: {
        $gte: new Date(`${minYear}-01-01`),
        $lte: new Date(`${maxYear}-12-31`),
      },
    }).sort("date");

    console.log(`Found ${companyHolidays.length} company-specific holidays for ${minYear}-${maxYear}`);

    // Convert company holidays to the same format as public holidays
    const formattedCompanyHolidays = companyHolidays.map(holiday => ({
      title: holiday.name,
      name: holiday.name,
      start: holiday.date.toISOString().split('T')[0],
      date: holiday.date.toISOString().split('T')[0],
      backgroundColor: '#059669',
      borderColor: '#047857',
      textColor: '#ffffff',
      allDay: true,
      className: 'company-holiday',
      extendedProps: {
        type: 'company-holiday',
        isCompanyHoliday: true,
        description: `Company Holiday: ${holiday.name}`
      }
    }));

    // Combine public holidays and company-specific holidays
    const allHolidays = [...allPublicHolidays, ...formattedCompanyHolidays];

    console.log(`Total holidays returned: ${allHolidays.length}`);

    // Set cache headers for better performance (cache for 1 hour)
    res.set({
      'Cache-Control': 'public, max-age=3600',
      'ETag': `"holidays-${yearsToGenerate.join('-')}-${Date.now()}"`
    });

    res.json({
      success: true,
      data: allHolidays,
      meta: {
        years: yearsToGenerate,
        publicHolidayCount: allPublicHolidays.length,
        companyHolidayCount: formattedCompanyHolidays.length,
        totalCount: allHolidays.length
      }
    });
  } catch (error) {
    console.error("Error fetching holidays:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to fetch holidays" });
  }
});

module.exports = router;
