const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../../config/ensureAuthenticated");
const RFICalculationService = require("../../services/rfiCalculationService");
const Employee = require("../../models/Employee");
const Payroll = require("../../models/Payroll");

// Debug middleware for RFI routes
router.use((req, res, next) => {
  next();
});

router.get("/preview/:employeeId", ensureAuthenticated, async (req, res) => {

  try {
    const { employeeId } = req.params;
    const companyCode = req.baseUrl.split("/")[2]; // Extract from baseUrl

    const employee = await Employee.findById(employeeId);

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      });
    }

    const payroll = await Payroll.findOne({ employee: employeeId });

    // Calculate RFI using the service
    const rfiCalculation = await RFICalculationService.calculateRFI(employeeId);

    res.json({
      success: true,
      data: rfiCalculation,
    });
  } catch (error) {
    console.error("RFI Preview Error:", error);
    console.error("Stack:", error.stack);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to generate RFI preview",
    });
  }
});

module.exports = router;
