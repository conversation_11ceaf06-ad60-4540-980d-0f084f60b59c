/**
 * JWT-based API Authentication Routes
 * 
 * These routes provide JWT authentication for API clients.
 * All routes return JSON responses and do not use CSRF protection.
 */

const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const { generateToken, verifyToken, extractToken, getCookieOptions } = require('../../utils/jwtUtils');
const User = require('../../models/user');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

/**
 * Dedicated public health check router without any auth middleware
 * This is registered separately to ensure it's truly public
 */
const healthRouter = express.Router();
// Export the health router
router.use('/health', healthRouter);

/**
 * @route   GET /api/auth/health
 * @desc    JWT authentication health check endpoint
 * @access  Public
 */
healthRouter.get('/', (req, res) => {
  // Prevent redirect to Vercel auth by setting header
  res.setHeader('X-Vercel-Skip-SSO', '1');
  
  try {
    // Force this to be truly public by bypassing all middleware
    console.log('\n=== JWT HEALTH CHECK ===');
    console.log('Request path:', req.path);
    console.log('Request URL:', req.originalUrl);
    console.log('Request method:', req.method);
    console.log('Request cookies:', req.cookies);
    console.log('Authorization header:', req.headers.authorization);
    console.log('Secure connection:', req.secure);
    console.log('X-Forwarded-Proto:', req.headers['x-forwarded-proto']);
    console.log('Host:', req.headers.host);
    
    const token = extractToken(req);
    console.log('Extracted token:', token ? 'Present' : 'Not found');
    
    const hasJwt = !!token;
    const isValidJwt = token ? !!verifyToken(token) : false;
    
    if (isValidJwt) {
      console.log('JWT is valid');
      const decoded = verifyToken(token);
      console.log('JWT user:', {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role
      });
    } else if (token) {
      console.log('JWT is invalid');
    }
    
    res.json({
      success: true,
      auth: {
        hasJwt,
        isValidJwt,
        cookiesPresent: !!req.cookies,
        cookieCount: req.cookies ? Object.keys(req.cookies).length : 0,
        cookieKeys: req.cookies ? Object.keys(req.cookies) : [],
        jwtCookiePresent: !!(req.cookies && req.cookies.jwt),
        authHeaderPresent: !!(req.headers.authorization && 
                            req.headers.authorization.startsWith('Bearer ')),
      },
      connection: {
        secure: req.secure,
        xForwardedProto: req.headers['x-forwarded-proto'],
        host: req.headers.host,
      },
      environment: process.env.NODE_ENV,
      serverTime: new Date().toISOString(),
      serverInfo: {
        version: process.version,
        platform: process.platform,
        memoryUsage: process.memoryUsage()
      }
    });
  } catch (error) {
    console.error('Auth health check failed:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Auth health check failed',
        name: error.name,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        details: process.env.NODE_ENV !== 'production' ? error.message : undefined
      },
      serverTime: new Date().toISOString()
    });
  }
});

/**
 * @route POST /api/auth/login
 * @desc Authenticate user and get token
 * @access Public
 * @param {Object} req.body - Contains email and password
 * @returns {Object} - Contains token and user data
 */
router.post('/login', async (req, res) => {
  try {
    // Only log in development
    if (process.env.NODE_ENV !== 'production') {
      console.log('\n=== API Login Attempt ===');
      console.log('Login attempt for:', req.body.email);
    }
    
    const { email, password } = req.body;
    
    // Validate request
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }
    
    // Find user by email
    const user = await User.findOne({ 
      $or: [
        { email: email.toLowerCase() },
        { username: email.toLowerCase() }
      ]
    });
    
    if (!user) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('User not found:', email);
      }
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    // Check if user is verified
    if (user.isVerified === false) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('User not verified:', email);
      }
      return res.status(401).json({
        success: false,
        message: 'Please verify your email before logging in',
        needsVerification: true
      });
    }
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('Invalid password for:', email);
      }
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('User authenticated successfully. Generating JWT...');
    }
    
    // Generate token
    const token = generateToken({
      id: user._id,
      username: user.username || user.email,
      email: user.email,
      role: user.roleName || 'user',
      firstName: user.firstName,
      lastName: user.lastName
    });
    
    // Set JWT cookie for browser clients
    console.log('Setting JWT cookie with options tailored for environment');
    res.cookie('jwt', token, getCookieOptions(isVercelEnvironment()));
    
    // Return success with token
    return res.json({
      success: true,
      message: 'Authentication successful',
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.roleName
      }
    });
  } catch (error) {
    console.error('API Login Error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * Helper function to determine if running in Vercel environment
 */
function isVercelEnvironment() {
  // If explicitly self-hosted, it's not a Vercel environment
  if (process.env.IS_SELF_HOSTED === 'true') {
    return false;
  }
  return process.env.VERCEL || process.env.VERCEL_ENV || 
         (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL);
}

/**
 * @route GET /api/auth/user
 * @desc Get user data
 * @access Private
 * @returns {Object} - Contains user data
 */
router.get('/user', apiJwtAuth, async (req, res) => {
  try {
    const user = await User.findById(req.jwtUser.id)
      .select('-password')
      .populate('companies')
      .populate('currentCompany');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    res.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.roleName,
        companies: user.companies,
        currentCompany: user.currentCompany
      }
    });
  } catch (error) {
    console.error('Get User Error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @route POST /api/auth/verify-token
 * @desc Verify JWT token
 * @access Public
 * @param {Object} req.body - Contains token
 * @returns {Object} - Contains success status and user data if valid
 */
router.post('/verify-token', (req, res) => {
  try {
    // Try to get token from cookie first, then from body
    let token = null;
    
    // Check for cookie first
    if (req.cookies && req.cookies.jwt) {
      token = req.cookies.jwt;
      console.log('Found token in cookie');
    } 
    // Fallback to request body
    else if (req.body && req.body.token) {
      token = req.body.token;
      console.log('Found token in request body');
    }
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'No token provided',
        cookies: {
          present: !!req.cookies,
          count: req.cookies ? Object.keys(req.cookies).length : 0,
          names: req.cookies ? Object.keys(req.cookies) : []
        }
      });
    }
    
    // Verify the token
    const decoded = verifyToken(token);
    
    if (!decoded) {
      return res.json({
        success: false,
        message: 'Invalid or expired token',
        cookies: {
          present: !!req.cookies,
          hasJwt: !!req.cookies?.jwt
        }
      });
    }
    
    // For sessions and cookies, refresh the token
    const refreshedToken = generateToken(decoded);
    res.cookie('jwt', refreshedToken, getCookieOptions(isVercelEnvironment()));
    
    // Return success with user data
    res.json({
      success: true,
      message: 'Token is valid',
      user: decoded,
      tokenRefreshed: true
    });
  } catch (error) {
    console.error('Token Verification Error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @route POST /api/auth/refresh
 * @desc Refresh token
 * @access Public
 * @param {Object} req.body - Contains token
 * @returns {Object} - Contains new token
 */
router.post('/refresh', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    // Verify existing token
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('Token refresh attempt for user:', decoded);
    }
    
    // Get fresh user data - handle potential ID format differences
    const userId = decoded.id || decoded._id;
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token format - missing user ID'
      });
    }
    
    // Get fresh user data
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Generate new token
    const newToken = generateToken({
      id: user._id,
      username: user.username || user.email,
      email: user.email,
      role: user.roleName || 'user',
      firstName: user.firstName,
      lastName: user.lastName
    });
    
    // Set the new token as a cookie
    console.log('Setting refreshed JWT cookie');
    res.cookie('jwt', newToken, getCookieOptions(isVercelEnvironment()));
    
    // Return new token
    res.json({
      success: true,
      message: 'Token refreshed',
      token: newToken
    });
  } catch (error) {
    console.error('Token Refresh Error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @route GET /api/auth/test
 * @desc Test protected route
 * @access Private
 * @returns {Object} - Contains success message and user data
 */
router.get('/test', apiJwtAuth, (req, res) => {
  res.json({
    success: true,
    message: 'Protected route accessed successfully',
    user: req.jwtUser
  });
});

/**
 * @route GET /api/auth/diagnostics
 * @desc Get system diagnostics information
 * @access Public
 * @returns {Object} - Contains diagnostic data
 */
router.get('/diagnostics', (req, res) => {
  try {
    console.log('\n=== DIAGNOSTICS ENDPOINT ===');
    console.log('Request IP:', req.ip);
    console.log('Request headers:', req.headers);
    
    // Get MongoDB connection state
    const mongoState = mongoose.connection.readyState;
    const stateMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
      99: 'uninitialized'
    };
    
    const diagnosticData = {
      success: true,
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL: process.env.VERCEL || false,
        VERCEL_ENV: process.env.VERCEL_ENV,
        VERCEL_URL: process.env.VERCEL_URL
      },
      mongoConnected: mongoState === 1,
      connectionState: stateMap[mongoState] || `unknown (${mongoState})`,
      mongoHost: mongoose.connection.host || 'Not connected',
      server: {
        platform: process.platform,
        nodeVersion: process.version,
        uptime: process.uptime()
      },
      request: {
        ip: req.ip,
        protocol: req.protocol,
        host: req.get('host'),
        userAgent: req.get('user-agent')
      }
    };
    
    console.log('Diagnostic data:', diagnosticData);
    
    res.json(diagnosticData);
  } catch (error) {
    console.error('Diagnostics error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

/**
 * @route GET /api/auth/test-mongo-connection
 * @desc Test MongoDB connection
 * @access Public
 * @returns {Object} - Contains connection test results
 */
router.get('/test-mongo-connection', async (req, res) => {
  const logs = [];
  const logEntry = (message) => {
    const timestamp = new Date().toISOString();
    const entry = `[${timestamp}] ${message}`;
    logs.push(entry);
    console.log(entry);
  };
  
  logEntry('Starting MongoDB connection test');
  
  try {
    // Check current connection state
    const mongoState = mongoose.connection.readyState;
    const stateMap = {
      0: 'disconnected',
      1: 'connected', 
      2: 'connecting',
      3: 'disconnecting',
      99: 'uninitialized'
    };
    
    logEntry(`Current MongoDB connection state: ${stateMap[mongoState] || `unknown (${mongoState})`}`);
    
    if (mongoState === 1) {
      logEntry('MongoDB is already connected');
      logEntry(`Connected to: ${mongoose.connection.host}`);
      logEntry(`Database name: ${mongoose.connection.name}`);
      
      // Attempt a simple ping operation
      const startTime = Date.now();
      const adminDb = mongoose.connection.db.admin();
      const serverInfo = await adminDb.serverInfo();
      const endTime = Date.now();
      
      logEntry(`MongoDB ping successful (${endTime - startTime}ms)`);
      logEntry(`Server info: MongoDB ${serverInfo.version}, ${serverInfo.gitVersion}`);
      
      return res.json({
        success: true,
        connected: true, 
        host: mongoose.connection.host,
        database: mongoose.connection.name,
        connectionTime: endTime - startTime,
        serverInfo: {
          version: serverInfo.version,
          gitVersion: serverInfo.gitVersion
        },
        logs: logs.join('\n')
      });
    }
    
    // If not connected, try to connect
    logEntry('MongoDB is not connected. Attempting connection...');
    
    // Define connection options
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Short timeout for test
      family: 4
    };
    
    // MongoDB URI from environment
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      logEntry('ERROR: MONGODB_URI environment variable is not set');
      return res.status(500).json({
        success: false,
        error: 'MongoDB URI not configured',
        details: 'The MONGODB_URI environment variable is not set',
        logs: logs.join('\n')
      });
    }
    
    logEntry('Attempting to connect to MongoDB...');
    const startTime = Date.now();
    
    try {
      // Try to connect with a new client to avoid affecting the main connection
      const { MongoClient } = require('mongodb');
      const client = new MongoClient(mongoUri, mongoOptions);
      
      await client.connect();
      const endTime = Date.now();
      
      logEntry(`MongoDB connection successful (${endTime - startTime}ms)`);
      logEntry(`Connected to: ${client.s.url}`);
      
      // Get server info
      const adminDb = client.db().admin();
      const serverInfo = await adminDb.serverInfo();
      
      logEntry(`Server info: MongoDB ${serverInfo.version}, ${serverInfo.gitVersion}`);
      
      // Close this test connection
      await client.close();
      logEntry('Test connection closed.');
      
      return res.json({
        success: true,
        connected: true,
        host: client.s.url,
        connectionTime: endTime - startTime,
        serverInfo: {
          version: serverInfo.version,
          gitVersion: serverInfo.gitVersion
        },
        logs: logs.join('\n')
      });
    } catch (connError) {
      logEntry(`Connection attempt failed: ${connError.message}`);
      logEntry(`Error name: ${connError.name}`);
      logEntry(`Stack trace: ${connError.stack}`);
      
      // Try to extract useful details for debugging
      let details = connError.message;
      
      if (connError.name === 'MongoNetworkError') {
        logEntry('Network error detected - possibly IP whitelisting or firewall issue');
        details = 'Network error - Vercel serverless function IP may not be whitelisted in MongoDB Atlas';
      } else if (connError.name === 'MongoServerSelectionError') {
        logEntry('Server selection error - possibly timeout or unavailable replica set');
        details = 'Server selection error - MongoDB server may be unavailable or requests timed out';
      }
      
      return res.status(500).json({
        success: false,
        error: connError.message,
        errorName: connError.name,
        details: details,
        logs: logs.join('\n')
      });
    }
  } catch (error) {
    logEntry(`Unexpected error during test: ${error.message}`);
    logEntry(`Error name: ${error.name}`);
    logEntry(`Stack trace: ${error.stack}`);
    
    res.status(500).json({
      success: false,
      error: error.message,
      details: 'An unexpected error occurred during the connection test',
      logs: logs.join('\n')
    });
  }
});

module.exports = router; 