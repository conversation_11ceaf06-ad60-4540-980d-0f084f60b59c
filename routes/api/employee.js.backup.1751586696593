const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../../config/ensureAuthenticated");
const Employee = require("../../models/Employee");
const Payroll = require("../../models/Payroll");

// Get hourly paid status
router.get(
  "/:employeeId/hourly-status",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      res.json({
        success: true,
        hourlyPaid: employee.regularHours?.hourlyPaid || false,
      });
    } catch (error) {
      console.error("Error fetching hourly paid status:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch hourly paid status",
      });
    }
  }
);

// Update hourly paid status
router.post(
  "/:employeeId/hourly-status",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const { hourlyPaid } = req.body;

      // Update Employee model
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Update regularHours
      if (!employee.regularHours) {
        employee.regularHours = {};
      }
      employee.regularHours.hourlyPaid = hourlyPaid;

      // Save changes
      await employee.save();

      // Also update Payroll model if it exists
      const payroll = await Payroll.findOne({ employee: employeeId });
      if (payroll) {
        payroll.hourlyPaid = hourlyPaid;
        await payroll.save();
      }

      res.json({
        success: true,
        message: "Hourly paid status updated successfully",
      });
    } catch (error) {
      console.error("Error updating hourly paid status:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update hourly paid status",
      });
    }
  }
);

module.exports = router;
