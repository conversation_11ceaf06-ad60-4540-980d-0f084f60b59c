
            if (pendingPayslips === 0) {
                payrollPeriod.status = 'finalized';
                payrollPeriod.finalizedAt = new Date();
                payrollPeriod.finalizedBy = user._id;
                await payrollPeriod.save({ session });
                console.log('Payroll period finalized');
            }
        }

        await session.commitTransaction();
        console.log('Transaction committed successfully');

        res.json({
            success: true,
            message: 'Payslip finalized successfully',
            payslip: {
                id: payslip._id,
                status: payslip.status,
                finalizedAt: payslip.finalizedAt
            }
        });

    } catch (error) {
        console.error('Error finalizing payslip:', error);
        await session.abortTransaction();
        res.status(500).json({
            success: false,
            message: 'Failed to finalize payslip',
            error: error.message
        });
    } finally {
        session.endSession();
    }
});

module.exports = router;
