const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../../config/ensureAuthenticated");
const RFICalculationService = require("../../services/rfiCalculationService");
const Employee = require("../../models/Employee");
const Payroll = require("../../models/Payroll");

// Debug middleware for RFI routes
router.use((req, res, next) => {
  console.log("\n=== RFI Route Handler Debug ===");
  console.log("Route path:", req.path);
  console.log("Full URL:", req.originalUrl);
  console.log("Company Code:", req.params.companyCode);
  console.log("Employee ID:", req.params.employeeId);
  next();
});

router.get("/preview/:employeeId", ensureAuthenticated, async (req, res) => {
  console.log("\n=== RFI Preview Route ===");
  console.log("Params:", req.params);
  console.log("Company Code from parent:", req.baseUrl.split("/")[2]);

  try {
    const { employeeId } = req.params;
    const companyCode = req.baseUrl.split("/")[2]; // Extract from baseUrl

    console.log("Looking up employee:", employeeId);
    const employee = await Employee.findById(employeeId);
    console.log("Employee found:", !!employee);

    if (!employee) {
      console.log("Employee not found");
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      });
    }

    console.log("Fetching payroll data");
    const payroll = await Payroll.findOne({ employee: employeeId });
    console.log("Payroll found:", !!payroll);

    // Calculate RFI using the service
    const rfiCalculation = await RFICalculationService.calculateRFI(employeeId);
    console.log("RFI Calculation result:", rfiCalculation);

    res.json({
      success: true,
      data: rfiCalculation,
    });
  } catch (error) {
    console.error("RFI Preview Error:", error);
    console.error("Stack:", error.stack);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to generate RFI preview",
    });
  }
});

module.exports = router;
