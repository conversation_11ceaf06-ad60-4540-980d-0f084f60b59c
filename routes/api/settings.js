const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const EmployeeNumberSettings = require('../../models/employeeNumberSettings');
const Company = require('../../models/Company');
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });

// Update employee number mode
router.post('/employee-number-mode', isAuthenticated, csrfProtection, async (req, res) => {
    try {

        const { mode } = req.body;

        if (!['automatic', 'manual'].includes(mode)) {
            return res.status(400).json({ message: 'Invalid mode specified. Mode must be either "automatic" or "manual".' });
        }

        // Get company context - try multiple sources
        let companyId = null;

        // Method 1: From user's current company
        if (req.user && req.user.currentCompany) {
            companyId = req.user.currentCompany;
        }

        // Method 2: From session
        if (!companyId && req.session && req.session.companyCode) {
            const company = await Company.findOne({ companyCode: req.session.companyCode });
            if (company) {
                companyId = company._id;
            }
        }

        // Method 3: From referer URL (extract company code from URL)
        if (!companyId && req.headers.referer) {
            const refererMatch = req.headers.referer.match(/\/clients\/([^\/]+)\//);
            if (refererMatch) {
                const companyCode = refererMatch[1];
                const company = await Company.findOne({ companyCode });
                if (company) {
                    companyId = company._id;
                }
            }
        }

        if (!companyId) {
            return res.status(400).json({ message: 'No company context found' });
        }

        // Find and update settings
        let settings = await EmployeeNumberSettings.findOne({
            company: companyId
        });
        
        if (!settings) {
            settings = new EmployeeNumberSettings({
                company: companyId,
                mode: mode,
                firstCompanyEmployeeNumber: '0001'
            });
        } else {
            settings.mode = mode;
        }

        await settings.save();

        // Return success response
        res.json({
            message: 'Employee number mode updated successfully',
            mode: settings.mode
        });

    } catch (error) {
        console.error('Error updating employee number mode:', error);
        res.status(500).json({ 
            message: 'Failed to update employee number mode',
            error: error.message 
        });
    }
});

module.exports = router;
