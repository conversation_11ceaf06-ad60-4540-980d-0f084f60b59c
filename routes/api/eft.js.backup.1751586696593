const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../../config/ensureAuthenticated');
const EFTDetails = require('../../models/eftDetails');
const Company = require('../../models/company');

// POST /api/eft-settings
router.post('/settings/eft', ensureAuthenticated, async (req, res) => {
  try {
    console.log('=== EFT Settings Debug ===');
    console.log('Request body:', req.body);
    
    const { eftFormat, bank, accountNumber, branchCode, accountType, accountHolder } = req.body;
    
    // Get current company
    const company = await Company.findById(req.user.currentCompany);
    if (!company) {
      console.error('Company not found for user:', req.user._id);
      return res.status(404).json({ error: 'Company not found' });
    }

    console.log('Company found:', company._id);

    // Update or create EFT details
    const eftDetails = await EFTDetails.findOneAndUpdate(
      { company: company._id },
      {
        eftFormat,
        bank,
        accountNumber,
        branchCode,
        accountType,
        accountHolder,
        lastUpdated: new Date()
      },
      { upsert: true, new: true }
    );

    console.log('EFT details saved:', eftDetails);

    res.json({ success: true, data: eftDetails });
  } catch (error) {
    console.error('Error saving EFT settings:', error);
    res.status(500).json({ error: 'Failed to save EFT settings' });
  }
});

module.exports = router;
