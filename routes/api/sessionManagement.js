const express = require("express");
const router = express.Router();
const SessionManagementService = require("../../services/SessionManagementService");
const TokenService = require("../../services/TokenService");
const DeviceManagementService = require("../../services/DeviceManagementService");
const { apiSessionCheck, extendSession, getSessionStatus } = require("../../middleware/sessionTimeout");
const { logoutAllDevices } = require("../../middleware/enhancedLogout");

// Apply session check to all routes
router.use(apiSessionCheck);

/**
 * GET /api/session/status
 * Get current session status and information
 */
router.get("/status", getSessionStatus);

/**
 * POST /api/session/extend
 * Extend current session expiration
 */
router.post("/extend", extendSession);

/**
 * GET /api/session/active
 * Get all active sessions for the current user
 */
router.get("/active", async (req, res) => {
  try {
    const userId = req.user._id;
    const sessions = await SessionManagementService.getUserActiveSessions(userId);

    // Add current session indicator
    const sessionsWithCurrent = sessions.map(session => ({
      ...session.toObject(),
      isCurrent: session.sessionId === req.sessionID,
      timeUntilExpiry: session.expiresAt.getTime() - Date.now(),
      minutesUntilExpiry: Math.floor((session.expiresAt.getTime() - Date.now()) / (1000 * 60)),
    }));

    res.json({
      success: true,
      sessions: sessionsWithCurrent,
      count: sessionsWithCurrent.length,
    });
  } catch (error) {
    console.error("Error getting active sessions:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving active sessions",
    });
  }
});

/**
 * DELETE /api/session/:sessionId
 * Invalidate a specific session
 */
router.delete("/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user._id;

    // Verify the session belongs to the current user
    const sessions = await SessionManagementService.getUserActiveSessions(userId);
    const targetSession = sessions.find(s => s.sessionId === sessionId);

    if (!targetSession) {
      return res.status(404).json({
        success: false,
        message: "Session not found or does not belong to you",
      });
    }

    // Don't allow invalidating current session through this endpoint
    if (sessionId === req.sessionID) {
      return res.status(400).json({
        success: false,
        message: "Cannot invalidate current session. Use logout instead.",
      });
    }

    const result = await SessionManagementService.invalidateSession(
      sessionId,
      "Invalidated by user"
    );

    if (result.success) {
      res.json({
        success: true,
        message: "Session invalidated successfully",
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.reason,
      });
    }
  } catch (error) {
    console.error("Error invalidating session:", error);
    res.status(500).json({
      success: false,
      message: "Error invalidating session",
    });
  }
});

/**
 * POST /api/session/logout-all
 * Logout from all devices
 */
router.post("/logout-all", logoutAllDevices);

/**
 * GET /api/devices
 * Get all trusted devices for the current user
 */
router.get("/devices", async (req, res) => {
  try {
    const userId = req.user._id;
    const devices = await DeviceManagementService.getUserDevices(userId, true);

    // Add additional information for each device
    const devicesWithInfo = devices.map(device => ({
      ...device.toObject(),
      trustScore: device.calculateTrustScore(),
      daysSinceLastUse: device.daysSinceLastUse,
      deviceAge: Math.floor(device.deviceAge / (1000 * 60 * 60 * 24)), // in days
      isSuspicious: device.isSuspicious(),
    }));

    res.json({
      success: true,
      devices: devicesWithInfo,
      count: devicesWithInfo.length,
    });
  } catch (error) {
    console.error("Error getting user devices:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving devices",
    });
  }
});

/**
 * DELETE /api/devices/:deviceId
 * Revoke a trusted device
 */
router.delete("/devices/:deviceId", async (req, res) => {
  try {
    const { deviceId } = req.params;
    const userId = req.user._id;

    // Verify the device belongs to the current user
    const devices = await DeviceManagementService.getUserDevices(userId, true);
    const targetDevice = devices.find(d => d._id.toString() === deviceId);

    if (!targetDevice) {
      return res.status(404).json({
        success: false,
        message: "Device not found or does not belong to you",
      });
    }

    const result = await DeviceManagementService.revokeDevice(
      deviceId,
      userId,
      "Revoked by user"
    );

    if (result.success) {
      res.json({
        success: true,
        message: "Device revoked successfully",
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.reason,
      });
    }
  } catch (error) {
    console.error("Error revoking device:", error);
    res.status(500).json({
      success: false,
      message: "Error revoking device",
    });
  }
});

/**
 * POST /api/devices/revoke-all
 * Revoke all trusted devices
 */
router.post("/devices/revoke-all", async (req, res) => {
  try {
    const userId = req.user._id;

    const result = await DeviceManagementService.revokeAllUserDevices(
      userId,
      userId,
      "All devices revoked by user"
    );

    if (result.success) {
      res.json({
        success: true,
        message: "All devices revoked successfully",
        revokedCount: result.revokedCount,
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error,
      });
    }
  } catch (error) {
    console.error("Error revoking all devices:", error);
    res.status(500).json({
      success: false,
      message: "Error revoking all devices",
    });
  }
});

/**
 * GET /api/tokens
 * Get all active remember me tokens for the current user
 */
router.get("/tokens", async (req, res) => {
  try {
    const userId = req.user._id;
    const tokens = await TokenService.getUserActiveTokens(userId);

    // Remove sensitive information and add display info
    const tokensWithInfo = tokens.map(token => ({
      id: token._id,
      deviceName: token.trustedDevice?.deviceName || "Unknown Device",
      deviceType: token.trustedDevice?.deviceType || "unknown",
      lastUsed: token.lastUsed,
      expiresAt: token.expiresAt,
      usageCount: token.usageCount,
      daysUntilExpiration: token.daysUntilExpiration,
      ipAddress: token.ipAddress,
      location: token.trustedDevice?.location || {},
      securityFlags: token.securityFlags,
    }));

    res.json({
      success: true,
      tokens: tokensWithInfo,
      count: tokensWithInfo.length,
    });
  } catch (error) {
    console.error("Error getting user tokens:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving tokens",
    });
  }
});

/**
 * DELETE /api/tokens/:tokenId
 * Revoke a specific remember me token
 */
router.delete("/tokens/:tokenId", async (req, res) => {
  try {
    const { tokenId } = req.params;
    const userId = req.user._id;

    // Verify the token belongs to the current user
    const tokens = await TokenService.getUserActiveTokens(userId);
    const targetToken = tokens.find(t => t._id.toString() === tokenId);

    if (!targetToken) {
      return res.status(404).json({
        success: false,
        message: "Token not found or does not belong to you",
      });
    }

    const result = await TokenService.revokeRememberMeToken(
      tokenId,
      userId,
      "Revoked by user"
    );

    if (result.success) {
      res.json({
        success: true,
        message: "Token revoked successfully",
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.reason,
      });
    }
  } catch (error) {
    console.error("Error revoking token:", error);
    res.status(500).json({
      success: false,
      message: "Error revoking token",
    });
  }
});

/**
 * POST /api/tokens/revoke-all
 * Revoke all remember me tokens
 */
router.post("/tokens/revoke-all", async (req, res) => {
  try {
    const userId = req.user._id;

    const result = await TokenService.revokeAllUserTokens(
      userId,
      userId,
      "All tokens revoked by user"
    );

    if (result.success) {
      res.json({
        success: true,
        message: "All tokens revoked successfully",
        revokedCount: result.revokedCount,
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error,
      });
    }
  } catch (error) {
    console.error("Error revoking all tokens:", error);
    res.status(500).json({
      success: false,
      message: "Error revoking all tokens",
    });
  }
});

/**
 * GET /api/security/statistics
 * Get security statistics for the current user
 */
router.get("/security/statistics", async (req, res) => {
  try {
    const userId = req.user._id;

    const [sessionStats, tokenStats, deviceStats] = await Promise.all([
      SessionManagementService.getSessionStatistics(),
      TokenService.getTokenStatistics(),
      DeviceManagementService.getDeviceStatistics(),
    ]);

    // Get user-specific counts
    const userSessions = await SessionManagementService.getUserActiveSessions(userId);
    const userTokens = await TokenService.getUserActiveTokens(userId);
    const userDevices = await DeviceManagementService.getUserDevices(userId, true);

    res.json({
      success: true,
      statistics: {
        sessions: {
          ...sessionStats,
          userActiveSessions: userSessions.length,
        },
        tokens: {
          ...tokenStats,
          userActiveTokens: userTokens.length,
        },
        devices: {
          ...deviceStats,
          userActiveDevices: userDevices.length,
        },
      },
    });
  } catch (error) {
    console.error("Error getting security statistics:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving security statistics",
    });
  }
});

/**
 * GET /api/security/settings
 * Get current user's security settings
 */
router.get("/security/settings", async (req, res) => {
  try {
    const userId = req.user._id;
    const User = require("../../models/user");

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      settings: {
        rememberMeSettings: user.rememberMeSettings || {
          enabled: true,
          maxDevices: 5,
          tokenExpirationDays: 30,
        },
        sessionSecurity: user.sessionSecurity || {
          maxConcurrentSessions: 3,
          sessionTimeoutMinutes: 480,
          inactivityTimeoutMinutes: 60,
          requireReauthForSensitive: true,
        },
        securityPreferences: user.securityPreferences || {
          notifyOnNewDevice: true,
          notifyOnSuspiciousActivity: true,
          notifyOnSessionExpiry: true,
          autoLogoutAllDevices: false,
        },
      },
    });
  } catch (error) {
    console.error("Error getting security settings:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving security settings",
    });
  }
});

/**
 * POST /api/security/settings
 * Update user's security settings
 */
router.post("/security/settings", async (req, res) => {
  try {
    const userId = req.user._id;
    const { rememberMeSettings, sessionSecurity, securityPreferences } = req.body;
    const User = require("../../models/user");

    const updateData = {};

    if (rememberMeSettings) {
      updateData.rememberMeSettings = {
        enabled: Boolean(rememberMeSettings.enabled),
        maxDevices: Math.min(Math.max(parseInt(rememberMeSettings.maxDevices) || 5, 1), 20),
        tokenExpirationDays: Math.min(Math.max(parseInt(rememberMeSettings.tokenExpirationDays) || 30, 1), 365),
      };
    }

    if (sessionSecurity) {
      updateData.sessionSecurity = {
        maxConcurrentSessions: Math.min(Math.max(parseInt(sessionSecurity.maxConcurrentSessions) || 3, 1), 10),
        sessionTimeoutMinutes: Math.min(Math.max(parseInt(sessionSecurity.sessionTimeoutMinutes) || 480, 15), 1440),
        inactivityTimeoutMinutes: Math.min(Math.max(parseInt(sessionSecurity.inactivityTimeoutMinutes) || 60, 5), 480),
        requireReauthForSensitive: Boolean(sessionSecurity.requireReauthForSensitive !== false),
      };
    }

    if (securityPreferences) {
      updateData.securityPreferences = {
        notifyOnNewDevice: Boolean(securityPreferences.notifyOnNewDevice !== false),
        notifyOnSuspiciousActivity: Boolean(securityPreferences.notifyOnSuspiciousActivity !== false),
        notifyOnSessionExpiry: Boolean(securityPreferences.notifyOnSessionExpiry !== false),
        autoLogoutAllDevices: Boolean(securityPreferences.autoLogoutAllDevices),
      };
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }


    res.json({
      success: true,
      message: "Security settings updated successfully",
      settings: {
        rememberMeSettings: updatedUser.rememberMeSettings,
        sessionSecurity: updatedUser.sessionSecurity,
        securityPreferences: updatedUser.securityPreferences,
      },
    });
  } catch (error) {
    console.error("Error updating security settings:", error);
    res.status(500).json({
      success: false,
      message: "Error updating security settings",
    });
  }
});

module.exports = router;
