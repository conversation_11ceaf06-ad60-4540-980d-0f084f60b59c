const express = require('express');
const router = express.Router();
const whatsappService = require('../../services/whatsappService');
const sessionService = require('../../services/sessionService');
const { verifyToken } = require('../../utils/jwtUtils');

// Verify webhook
router.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  if (mode === 'subscribe' && token === process.env.WHATSAPP_VERIFY_TOKEN) {
    res.status(200).send(challenge);
  } else {
    res.sendStatus(403);
  }
});

// Handle incoming messages
router.post('/webhook', express.json(), async (req, res) => {
  try {
    if (req.body.object !== 'whatsapp_business_account') {
      return res.sendStatus(404);
    }

    const messages = req.body.entry?.[0]?.changes?.[0]?.value?.messages;
    if (!messages || !messages.length) {
      return res.sendStatus(200);
    }

    for (const message of messages) {
      await processMessage(message);
    }

    res.sendStatus(200);
  } catch (error) {
    console.error('WhatsApp Webhook Error:', error);
    res.sendStatus(500);
  }
});

async function processMessage(message) {
  try {
    const from = message.from; // WhatsApp number
    const text = message.text?.body?.toLowerCase() || '';
    
    // Check if user is authenticated
    let employeeSession = await getEmployeeSession(from);
    
    if (!employeeSession) {
      // First time user, try to authenticate
      const auth = await whatsappService.authenticateEmployee(from);
      
      if (!auth) {
        await whatsappService.sendMessage(from, 
          'Welcome to Panda Payroll! Your phone number is not registered in our system. ' +
          'Please contact your HR department to update your contact details.'
        );
        return;
      }
      
      employeeSession = auth;
      await whatsappService.sendMessage(from, 
        `Welcome ${auth.employee.firstName}! How can I help you today?\n\n` +
        '1. Request Payslip\n' +
        '2. Request IRP5\n' +
        '3. Apply for Leave\n\n' +
        'Reply with the number of your choice.'
      );
      return;
    }

    // Process authenticated user requests
    if (text.includes('payslip')) {
      const result = await whatsappService.handlePayslipRequest(employeeSession.employee);
      
      if (result.success) {
        await whatsappService.sendDocument(from, result.documentUrl, result.caption);
      } else {
        await whatsappService.sendMessage(from, result.message);
      }
    }
    else if (text.includes('irp5')) {
      const result = await whatsappService.handleIRP5Request(employeeSession.employee);
      
      if (result.success) {
        await whatsappService.sendDocument(from, result.documentUrl, result.caption);
      } else {
        await whatsappService.sendMessage(from, result.message);
      }
    }
    else if (text.toLowerCase().startsWith('leave request:')) {
      const result = await whatsappService.handleLeaveRequest(employeeSession.employee, text);
      await whatsappService.sendMessage(from, result.message);
    }
    else {
      await whatsappService.sendMessage(from,
        'Please choose from the following options:\n\n' +
        '1. To request a payslip, send "payslip" or "payslip YYYY-MM"\n' +
        '2. To request an IRP5, send "irp5" or "irp5 YYYY"\n' +
        '3. To apply for leave, send "Leave request: [type] from YYYY-MM-DD to YYYY-MM-DD reason: [your reason]"\n\n' +
        'Example: "Leave request: Annual from 2024-03-01 to 2024-03-05 reason: Family vacation"'
      );
    }
  } catch (error) {
    console.error('Message Processing Error:', error);
    await whatsappService.sendMessage(message.from, 
      'Sorry, there was an error processing your request. Please try again later.'
    );
  }
}

async function getEmployeeSession(phoneNumber) {
  const session = await sessionService.getSession(phoneNumber);
  if (session) {
    // Refresh session duration
    await sessionService.refreshSession(phoneNumber);
  }
  return session;
}

module.exports = router; 