const express = require('express');
const router = express.Router();
const Employee = require('../../models/Employee');
const { isAuthenticated } = require('../../middleware/auth');
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });

// POST /api/employees/reinstate
router.post('/reinstate', isAuthenticated, csrfProtection, async (req, res) => {
    try {
        const { updates } = req.body;

        if (!updates || !Array.isArray(updates) || updates.length === 0) {
            return res.status(400).json({ message: 'No valid updates provided' });
        }

        const results = [];
        const errors = [];

        // Process each update
        for (const update of updates) {
            try {
                const employee = await Employee.findOne({ 
                    companyEmployeeNumber: update.employeeNumber,
                    company: req.user.company
                });

                if (!employee) {
                    errors.push(`Employee ${update.employeeNumber} not found`);
                    continue;
                }

                // Update employee status and add new service period
                employee.employmentStatus = 'Active';
                employee.servicePeriods.push({
                    startDate: new Date(update.fromDate),
                    endDate: null
                });

                // Save the changes
                await employee.save();
                results.push(`Employee ${update.employeeNumber} reinstated successfully`);

            } catch (error) {
                errors.push(`Error reinstating employee ${update.employeeNumber}: ${error.message}`);
            }
        }

        // Return response based on results
        if (errors.length > 0) {
            return res.status(400).json({
                message: 'Some reinstatements failed',
                errors,
                successes: results
            });
        }

        return res.json({
            message: 'All reinstatements completed successfully',
            successes: results
        });

    } catch (error) {
        console.error('Error in reinstate endpoint:', error);
        res.status(500).json({ message: 'Server error processing reinstatements' });
    }
});

// POST /api/employees/undo-end-of-service
router.post('/undo-end-of-service', isAuthenticated, csrfProtection, async (req, res) => {
    try {
        const { updates } = req.body;

        if (!updates || !Array.isArray(updates) || updates.length === 0) {
            return res.status(400).json({ message: 'No valid updates provided' });
        }

        const results = [];
        const errors = [];

        // Process each update
        for (const update of updates) {
            try {
                const employee = await Employee.findOne({ 
                    companyEmployeeNumber: update.employeeNumber,
                    company: req.user.company
                });

                if (!employee) {
                    errors.push(`Employee ${update.employeeNumber} not found`);
                    continue;
                }

                // Update employee status and add new service period
                employee.status = 'Active';
                employee.employmentStatus = true;
                employee.terminationNoticeDate = null;
                employee.lastDayOfService = null;

                // Add new service period
                if (employee.servicePeriods && employee.servicePeriods.length > 0) {
                    const lastPeriod = employee.servicePeriods[employee.servicePeriods.length - 1];
                    lastPeriod.endDate = null; // Remove the end date from the last period
                }

                // Save the changes
                await employee.save();
                results.push(`Employee ${update.employeeNumber} termination undone successfully`);

            } catch (error) {
                console.error('Error undoing termination:', error);
                errors.push(`Error undoing termination for employee ${update.employeeNumber}: ${error.message}`);
            }
        }

        // Return response based on results
        if (errors.length > 0) {
            return res.status(400).json({
                message: 'Some updates failed',
                errors,
                results
            });
        }

        res.json({
            message: 'All updates completed successfully',
            results
        });

    } catch (error) {
        console.error('Error in bulk undo termination:', error);
        res.status(500).json({
            message: 'Internal server error',
            error: error.message
        });
    }
});

module.exports = router;
