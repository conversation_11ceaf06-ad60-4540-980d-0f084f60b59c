            const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureCompanyAccess } = require('../../middleware/auth');

// Import controller functions
const {
  generateReturn,
  saveDraft,
  getDraft,
  generateDownload
} = require('../../controllers/oidReturnController');

// CSRF token validation middleware
const validateCSRFToken = (req, res, next) => {
  const token = req.headers['x-csrf-token'] || req.headers['csrf-token'];
  const cookieToken = req.cookies['XSRF-TOKEN'];
  const metaToken = req.headers['x-xsrf-token'];

  // Check all possible token locations
  const validToken = token || metaToken;
  const storedToken = cookieToken || req.csrfToken();

  if (!validToken || !storedToken || validToken !== storedToken) {
    console.error('CSRF Token Validation Failed:', {
      headerToken: token,
      metaToken: metaToken,
      cookieToken: cookieToken,
      storedToken: storedToken
    });
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Invalid CSRF token'
    });
  }
  next();
};

// Apply middleware to all routes
router.use(validateCSRFToken);
router.use((req, res, next) => {
  // Enhanced request logging
  console.log('\n=== OID Returns API Request ===');
  console.log('Path:', req.path);
  console.log('Method:', req.method);
  console.log('Session ID:', req.sessionID);
  console.log('User:', req.user?.email);
  console.log('Company:', req.user?.currentCompany);
  console.log('CSRF Token:', req.headers['x-csrf-token'] || req.headers['csrf-token']);
  console.log('Cookie Token:', req.cookies['XSRF-TOKEN']);
  console.log('Meta Token:', req.headers['x-xsrf-token']);
  next();
});

// Generate OID return
router.post('/generate', 
  ensureAuthenticated,
  ensureCompanyAccess,
  async (req, res) => {
    try {
      const { year } = req.body;
      const companyId = req.companyId; // Use companyId from middleware
      
      if (!year) {
        return res.status(400).json({ 
          error: 'Bad Request',
          message: 'Year is required' 
        });
      }

      const returnData = await generateReturn(companyId, year);
      res.json(returnData);

    } catch (error) {
      console.error('Error generating OID return:', error);
      res.status(500).json({ 
        error: 'Internal Server Error',
        message: error.message || 'Failed to generate OID return'
      });
    }
  }
);

// Save draft
router.post('/draft',
  ensureAuthenticated,
  ensureCompanyAccess,
  async (req, res) => {
    try {
      const { returnData } = req.body;
      const companyId = req.companyId; // Use companyId from middleware
      
      if (!returnData) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Return data is required'
        });
      }

      await saveDraft(companyId, returnData);
      res.json({ message: 'Draft saved successfully' });
      
    } catch (error) {
      console.error('Error saving draft:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message || 'Failed to save draft'
      });
    }
  }
);

// Get draft
router.get('/draft/:year',
  ensureAuthenticated,
  ensureCompanyAccess,
  async (req, res) => {
    try {
      const { year } = req.params;
      const companyId = req.companyId; // Use companyId from middleware
      
      if (!year) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Year is required'
        });
      }

      const draft = await getDraft(companyId, year);
      if (!draft) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'No draft found for the specified year'
        });
      }

      res.json(draft);
      
    } catch (error) {
      console.error('Error retrieving draft:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message || 'Failed to retrieve draft'
      });
    }
  }
);

// Download return
router.post('/download',
  ensureAuthenticated,
  ensureCompanyAccess,
  async (req, res) => {
    try {
      const { returnData } = req.body;
      const companyId = req.companyId; // Use companyId from middleware
      
      if (!returnData) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Return data is required'
        });
      }

      const file = await generateDownload(companyId, returnData);
      res.download(file);
      
    } catch (error) {
      console.error('Error downloading return:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message || 'Failed to download return'
      });
    }
  }
);

module.exports = router;
