const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");

// Basic user routes
router.get("/profile", ensureAuthenticated, async (req, res) => {
  try {
    res.render("profile", {
      user: req.user,
    });
  } catch (error) {
    console.error("Error accessing profile:", error);
    res.status(500).send("Error loading profile");
  }
});

// Add more user routes as needed...

module.exports = router;
