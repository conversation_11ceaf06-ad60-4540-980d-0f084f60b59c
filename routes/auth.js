const express = require("express");
const router = express.Router();
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const passport = require("passport");
const User = require("../models/user");
const Employee = require("../models/Employee");
const nodemailer = require("nodemailer");
const { check, validationResult } = require("express-validator");
const saltRounds = 12;
const successMessages = ["Login successful"];
const errorMessages = ["Invalid email", "Incorrect password"];
const Company = require("../models/Company");
const util = require("util");
const ejs = require("ejs");
const path = require("path");
const EmployerDetails = require("../models/employerDetails");
const Role = require("../models/role");
const fs = require("fs");
const speakeasy = require("speakeasy");
const TwoFactorVerification = require("../models/twoFactorVerification");
const Redis = require("redis");
const RedisStore = require("connect-redis").default;
const redisClient = require("../config/redis");
const jwt = require("jsonwebtoken");
const {
  initializeTwoFactor,
  verifyTwoFactor,
} = require("../middleware/twoFactorMiddleware");
const TwoFactorService = require("../services/twoFactorService");
const TwoFactorStateManager = require("../services/twoFactorStateManager");
const RedisManager = require("../config/redis");
const authService = require("../services/authService");
const TokenManager = require("../services/tokenManager");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const { ensureAuthenticated } = require("../config/auth");
const PayFrequency = require("../models/PayFrequency");
const LoginHistory = require("../models/loginHistory");
const mongoose = require("mongoose");
const { getAppUrl } = require("../utils/url");
const { generateToken } = require('../utils/jwtUtils');

// Role-based redirection middleware
const handleRoleRedirect = async (user) => {
  try {

    // Populate role and company information
    const populatedUser = await User.findById(user._id)
      .populate("role")
      .populate("currentCompany")
      .lean();



    if (!populatedUser.role) {
      console.error("No role found for user:", populatedUser.email);
      throw new Error("User role not configured");
    }

    // Handle employee role
    if (
      populatedUser.role.name === "employee" ||
      populatedUser.roleName === "employee"
    ) {
      const employee = await Employee.findOne({ user: populatedUser._id });

      if (!employee) {
        console.error(
          "No employee record found for user:",
          populatedUser.email
        );
        throw new Error("Employee record not found");
      }

      const redirectUrl = `/employee-portal/${populatedUser._id}`;
      return redirectUrl;
    }

    // For owners/admins, skip onboarding check temporarily
    if (populatedUser.currentCompany) {
      const redirectUrl = `/clients/${populatedUser.currentCompany.companyCode}/dashboard`;
      return redirectUrl;
    }

    return "/companies";
  } catch (error) {
    console.error("Role redirect error:", error);
    throw error;
  }
};

// Base URL configuration
const BASE_URL =
  process.env.NODE_ENV === "production"
    ? "https://payroll.pss-group.co.za"
    : "http://localhost:3002";

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  tls: {
    rejectUnauthorized: false, // Only use this in development
  },
});

// Silent verification
transporter.verify().catch((error) => {
  console.error("SMTP Verification Error:", error);
});

transporter.on("token", (token) => {
});

const sendMail = async (mailOptions) => {
  return new Promise((resolve, reject) => {
    const updatedMailOptions = {
      ...mailOptions,
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER, // This <NAME_EMAIL>
      },
      headers: {
        "X-Priority": "1",
        "X-MSMail-Priority": "High",
        Importance: "high",
        "Message-ID": `<${Date.now()}.${Math.random()
          .toString(36)
          .substring(2)}@pss-group.co.za>`,
        "List-Unsubscribe": `<mailto:${process.env.EMAIL_USER}?subject=unsubscribe>`,
        "X-Mailer": "Panda Solutions Mailer/1.0",
        "X-Entity-Ref-ID": crypto.randomBytes(32).toString("hex"),
      },
    };

    console.log("Sending email with options:", {
      from: updatedMailOptions.from,
      to: updatedMailOptions.to,
      subject: updatedMailOptions.subject,
    });

    transporter.sendMail(updatedMailOptions, (error, info) => {
      if (error) {
        console.error("Send mail error:", error);
        reject(error);
      } else {
        console.log("Email sent successfully:", {
          messageId: info.messageId,
          response: info.response,
        });
        resolve(info);
      }
    });
  });
};

// Add this helper function at the top of your file
const validateEmailConfig = () => {
  const required = [
    "EMAIL_HOST",
    "EMAIL_PORT",
    "EMAIL_USER",
    "EMAIL_FROM_NAME",
  ];
  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    console.error("Missing email configuration:", missing);
    return false;
  }

  return true;
};

// Add this function near the top of auth.js, after the imports and before the routes
const testEmailConfig = async () => {
  try {


    await transporter.verify();
    return true;
  } catch (error) {
    console.error("Email configuration error:", error);
    return false;
  }
};

// Debug logging middleware disabled to reduce terminal noise

// Login routes
router.get("/login", csrfProtection, (req, res) => {
  // If user is already authenticated, redirect to appropriate page
  if (req.isAuthenticated()) {
    const companyCode = req.user.currentCompany?.companyCode;
    if (companyCode) {
      return res.redirect(`/clients/${companyCode}/dashboard`);
    }
    return res.redirect("/companies");
  }

  // Render login page for unauthenticated users
  res.render("login.ejs", {
    messages: {
      success: req.flash("success"),
      error: req.flash("error"),
    },
    csrfToken: req.csrfToken(),
  });
});

// Update the login POST route
router.post("/login", (req, res, next) => {
  console.log('Is Vercel?', isVercelEnvironment());
  
  passport.authenticate("local", async (err, user, info) => {
    try {
      if (err) {
        console.error('Passport authentication error:', err);
        throw err;
      }

      if (!user) {
        req.flash("error", info?.message || "Authentication failed");
        return res.redirect("/login");
      }

      // Check if user is verified
      if (user.isVerified === false) {
        req.flash("error", "Please verify your email before logging in.");
        return res.redirect("/login?needsVerification=true");
      }

      // Log in the user (creates session)
      req.logIn(user, async (err) => {
        if (err) {
          console.error('Session login error:', err);
          throw err;
        }

        try {
          // Generate JWT token with user information
          const token = generateToken({
            id: user._id,
            username: user.username || user.email,
            email: user.email,
            role: user.roleName || 'user',
            firstName: user.firstName,
            lastName: user.lastName
          });

          // Get proper cookie options for the environment
          const cookieOptions = getCookieOptions();

          // Set JWT in cookie with proper options
          res.cookie('jwt', token, cookieOptions);
          
          // Record login history
          try {
            const loginHistory = new LoginHistory({
              user: user._id,
              ipAddress: req.ip,
              userAgent: req.headers['user-agent'],
              loginType: 'password', // or 'oauth', '2fa', etc.
              successful: true
            });
            await loginHistory.save();
          } catch (historyError) {
            console.error('Failed to record login history:', historyError);
            // Non-critical error, continue with authentication
          }

          // Log success and redirect
          
          // Determine redirect URL based on user role and state
          try {
            const redirectUrl = await handleRoleRedirect(user);
            return res.redirect(redirectUrl);
          } catch (redirectError) {
            console.error('Redirect error:', redirectError);
            // Fallback to dashboard if role redirect fails
            return res.redirect("/dashboard");
          }
        } catch (jwtError) {
          console.error('JWT generation/setting error:', jwtError);
          // Continue with session-only auth if JWT fails
          try {
            const redirectUrl = await handleRoleRedirect(user);
            return res.redirect(redirectUrl);
          } catch (e) {
            return res.redirect("/dashboard");
          }
        }
      });
    } catch (error) {
      console.error('Login route error:', error);
      req.flash("error", "An unexpected error occurred");
      return res.redirect("/login");
    }
  })(req, res, next);
});

// Helper function to determine if running in Vercel environment
function isVercelEnvironment() {
  // If explicitly self-hosted, it's not a Vercel environment
  if (process.env.IS_SELF_HOSTED === 'true') {
    return false;
  }
  return process.env.VERCEL || process.env.VERCEL_ENV || 
         (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL);
}

// Helper function to get cookie options optimized for the current environment
function getCookieOptions() {
  // Base options that work in all environments
  const options = {
    httpOnly: true,
    path: '/'  // Critical - must be set to / for all routes
  };
  
  // For production/Vercel, set secure and SameSite appropriately
  if (process.env.NODE_ENV === 'production' || isVercelEnvironment()) {
    options.secure = true;
    options.sameSite = 'none';  // Important for cross-domain in Vercel
  } else {
    options.secure = false;
    options.sameSite = 'lax';
  }
  
  // Set cookie expiration (24 hours)
  options.maxAge = 24 * 60 * 60 * 1000;
  
  // If domain is explicitly configured, use it
  if (process.env.COOKIE_DOMAIN) {
    options.domain = process.env.COOKIE_DOMAIN;
  }
  
  return options;
}

// 2FA verification routes
router.get("/verify-2fa", async (req, res) => {
  try {

    // Get token from cookie
    const token = req.cookies["2fa_token"];

    if (!token) {
      return res.redirect("/login");
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get state from Redis
    const state = await RedisManager.get2FAState(decoded.userId);

    if (!state || state.userId.toString() !== decoded.userId.toString()) {
      res.clearCookie("2fa_token");
      return res.redirect("/login");
    }

    // Render verify-2fa page
    res.render("verify-2fa", {
      email: decoded.email,
      csrfToken: req.csrfToken(),
      messages: req.flash(),
    });
  } catch (error) {
    console.error("2FA verification error:", error);
    res.clearCookie("2fa_token");
    res.redirect("/login");
  }
});

router.post("/verify-2fa", csrfProtection, async (req, res) => {
  try {
    const { token } = req.body;

    // Get token from cookie
    const jwtToken = req.cookies["2fa_token"];
    if (!jwtToken) {
      if (req.xhr || req.headers.accept.indexOf("json") > -1) {
        return res.status(401).json({
          success: false,
          error: "Invalid verification session",
        });
      } else {
        req.flash("error", "Invalid verification session");
        return res.redirect("/login");
      }
    }

    // Verify token
    const decoded = jwt.verify(jwtToken, process.env.JWT_SECRET);

    // Get state from Redis
    const state = await RedisManager.get2FAState(decoded.userId);

    if (!state) {
      if (req.xhr || req.headers.accept.indexOf("json") > -1) {
        return res.status(401).json({
          success: false,
          error: "Verification session expired",
        });
      } else {
        req.flash("error", "Verification session expired");
        return res.redirect("/login");
      }
    }

    // Get user
    const user = await User.findById(decoded.userId);
    if (!user) {
      if (req.xhr || req.headers.accept.indexOf("json") > -1) {
        return res.status(401).json({
          success: false,
          error: "User not found",
        });
      } else {
        req.flash("error", "User not found");
        return res.redirect("/login");
      }
    }

    // Verify 2FA token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: "base32",
      token: token,
      window: 1, // Allow 30 seconds of time drift
    });

    if (!verified) {
      // Increment failed attempts
      state.attempts = (state.attempts || 0) + 1;
      await RedisManager.set2FAState(decoded.userId, state);

      if (state.attempts >= 3) {
        // Too many failed attempts
        await RedisManager.clear2FAState(decoded.userId);
        res.clearCookie("2fa_token");
        if (req.xhr || req.headers.accept.indexOf("json") > -1) {
          return res.status(401).json({
            success: false,
            error: "Too many failed attempts",
            redirect: "/login",
          });
        } else {
          req.flash("error", "Too many failed attempts");
          return res.redirect("/login");
        }
      }

      if (req.xhr || req.headers.accept.indexOf("json") > -1) {
        return res.status(401).json({
          success: false,
          error: "Invalid verification code",
        });
      } else {
        req.flash("error", "Invalid verification code");
        return res.redirect("/login");
      }
    }

    // Mark as verified in Redis
    state.verified = true;
    await RedisManager.set2FAState(decoded.userId, state);

    // Get return URL if any
    const returnTo = await RedisManager.getReturnTo(decoded.userId);
    await RedisManager.clearReturnTo(decoded.userId);

    // Clear 2FA cookie
    res.clearCookie("2fa_token");

    // Handle response based on request type
    if (req.xhr || req.headers.accept.indexOf("json") > -1) {
      return res.json({
        success: true,
        message: "2FA verification successful",
        redirect: returnTo || "/dashboard",
      });
    } else {
      req.flash("success", "2FA verification successful");
      return res.redirect(returnTo || "/dashboard");
    }
  } catch (error) {
    console.error("2FA verification error:", error);
    if (req.xhr || req.headers.accept.indexOf("json") > -1) {
      return res.status(500).json({
        success: false,
        error: "Verification failed",
      });
    } else {
      req.flash("error", "Verification failed");
      return res.redirect("/login");
    }
  }
});

// Session monitoring middleware disabled to reduce terminal noise

// Auth debugging middleware disabled to reduce terminal noise

// Debug session route disabled to reduce terminal noise

// Registration route
router.get("/register", (req, res) => {
  res.render("register", {
    errorMessages: req.flash("error"),
    successMessages: req.flash("success"),
    csrfToken: req.csrfToken(),
  });
});

router.post("/register", async (req, res) => {
  try {

    // Test email configuration first
    const emailConfigTest = await testEmailConfig();

    // Find the Owner role
    const ownerRole = await Role.findOne({
      name: { $regex: new RegExp("^Owner$", "i") },
    });

    if (!ownerRole) {
      throw new Error("Owner role not found");
    }

    // Check if user exists
    const existingUser = await User.findOne({
      $or: [
        { email: req.body.email.toLowerCase() },
        { username: req.body.email.toLowerCase() },
      ],
    });

    if (existingUser) {
      return res.render("register", {
        errorMessages: "Email already registered",
        successMessages: null,
        csrfToken: req.csrfToken(),
      });
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(20).toString("hex");
    const companyCode = crypto.randomBytes(4).toString("hex").toUpperCase();

    // Create new user with explicit isVerified false
    const newUser = new User({
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email.toLowerCase(),
      username: req.body.email.toLowerCase(),
      phone: req.body.phone,
      password: req.body.password,
      verificationToken,
      isVerified: false, // Explicitly set to false
      role: ownerRole._id,
      roleName: "owner",
      onboardingComplete: false,
      onboardingStep: 1,
    });

    // Force isVerified to false
    newUser.$set("isVerified", false);

    // Create company and associate with user
    const company = new Company({
      name: req.body.companyName,
      owner: newUser._id,
      companyCode: companyCode,
    });

    const employerDetails = new EmployerDetails({
      company: company._id,
      tradingName: req.body.companyName,
    });

    company.employerDetails = employerDetails._id;
    await company.save();
    await employerDetails.save();

    newUser.companies.push(company._id);
    newUser.currentCompany = company._id;
    newUser.defaultCompany = company._id;

    // Save user with isVerified explicitly set to false
    await User.create({
      ...newUser.toObject(),
      isVerified: false,
    });


    // Update the verification URL generation to use our utility
    const verificationUrl = getAppUrl(req, `verify-email/${verificationToken}`);


    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/register-email.ejs"),
      {
        firstName: req.body.firstName,
        verificationUrl: verificationUrl,
      }
    );

    const mailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER,
      },
      to: req.body.email.toLowerCase(),
      subject: "Welcome to Panda Solutions - Email Verification",
      html: emailHtml,
      attachments: [
        {
          filename: "logo.png",
          path: ensureLogoExists(),
          cid: "logo",
        },
      ],
    };

    try {
      await sendMail(mailOptions);

      // Double check user's verified status
      const savedUser = await User.findById(newUser._id);

      res.render("login", {
        successMessages:
          "Registration successful! Please check your email to verify your account.",
        errorMessages: null,
        csrfToken: req.csrfToken(),
      });
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Create support ticket or alert admin about email failure
      req.flash(
        "warning",
        "Account created but verification email failed to send. Please contact support."
      );
      res.redirect("/login");
    }
  } catch (error) {
    console.error("Registration error:", error);
    res.render("register", {
      errorMessages: error.message || "Registration failed",
      successMessages: null,
      csrfToken: req.csrfToken(),
    });
  }
});

// Add a route to handle the verification with redirect
router.get("/login", async (req, res) => {
  const { verified, token } = req.query;

  if (verified && token) {
    try {
      const user = await User.findOne({ verificationToken: token });
      if (user) {
        user.isVerified = true;
        user.verificationToken = undefined;
        await user.save();
        req.flash(
          "success",
          "Email verified successfully. You can now log in."
        );
      }
    } catch (error) {
      console.error("Verification error:", error);
      req.flash("error", "Error verifying email.");
    }
  }

  res.render("login", {
    errorMessages: req.flash("error"),
    successMessages: req.flash("success"),
  });
});

//forgot_password Route

router.get("/forgot_password", async (req, res) => {
  try {

    // Get flash messages
    const errorMessages = req.flash("error");
    const successMessages = req.flash("success");

    res.render("forgot_password", {
      csrfToken: req.csrfToken(),
      errorMessages,
      successMessages,
      messages: req.flash(),
    });
  } catch (error) {
    console.error("Error in forgot password route:", error);
    res.render("error", {
      message: "An error occurred",
      error: { status: 500 },
    });
  }
});

// Add this function near the top with other helper functions
const sendPasswordResetEmail = async (email, resetUrl) => {
  try {

    // Render email template
    const emailTemplate = await ejs.renderFile(
      path.join(__dirname, "../views/emails/resetPassword.ejs"),
      {
        resetUrl,
      }
    );

    // Send email
    const info = await transporter.sendMail({
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER,
      },
      to: email,
      subject: "Password Reset Request",
      html: emailTemplate,
    });

    console.log("Password reset email sent:", {
      messageId: info.messageId,
      to: email,
      timestamp: new Date(),
    });

    return info;
  } catch (error) {
    console.error("Error sending password reset email:", error);
    throw error;
  }
};

// Update the forgot password POST route
router.post("/forgot_password", async (req, res) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.render("forgot_password", {
        errorMessages: "Email not found",
        successMessages: null,
        csrfToken: req.csrfToken(),
      });
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString("hex");
    user.resetPasswordToken = token;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();

    // Send reset email using environment-aware URL generation
    const resetUrl = getAppUrl(req, `reset-password/${token}`);
    await sendPasswordResetEmail(user.email, resetUrl);

    req.flash(
      "success",
      "Password reset instructions have been sent to your email."
    );
    res.redirect("/login");
  } catch (error) {
    console.error("Error in forgot password submission:", error);
    req.flash("error", "An error occurred while processing your request.");
    res.redirect("/forgot_password");
  }
});

router.get("/reset_password", async (req, res) => {
  try {
    const token = req.query.token; // Get the token from the query parameters

    // Find the user by reset token and ensure it's not expired
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      // If the token is invalid or has expired, redirect to an error message
      return res.status(400).send("Invalid or expired token");
    }

    // Render the reset password form with the token passed as a parameter
    res.render("reset_password", { token });
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
});

router.post("/reset_password", async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    // Check if passwords match
    if (password !== confirmPassword) {
      return res.render("reset_password", {
        token,
        error: "Passwords do not match",
      });
    }

    // Find the user by the reset password token
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).send("Invalid or expired token");
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 12); // Updated to 12

    // Update user's password and clear reset token fields
    user.password = hashedPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    // Flash success message
    req.flash("success", "Your password has been successfully updated.");

    // Redirect to the login page
    res.redirect("/login");
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
});

// Route to handle the password reset form submission
router.post("/reset/:token", async (req, res) => {
  try {
    const token = req.params.token;
    const { password, confirmPassword } = req.body;

    // Check if passwords match
    if (password !== confirmPassword) {
      return res.render("reset_password", {
        token,
        error: "Passwords do not match",
      });
    }

    // Find the user by the reset password token
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });
    if (!user) {
      return res.render("error", { message: "Invalid or expired token" });
    }

    // Update the user's password and clear the reset password fields
    const hashedPassword = await bcrypt.hash(password, 12); // Updated to 12
    user.password = hashedPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    // Redirect to the login page or any other relevant page
    res.redirect("/login");
  } catch (error) {
    console.error(error);
    res.status(500).render("error", { message: "Internal Server Error" });
  }
});

// Logout route
router.get("/logout", (req, res) => {

  // Clear JWT cookie first with proper options for Vercel
  if (req.cookies.jwt) {
    const cookieOptions = {
      httpOnly: true,
      path: '/',
      // For production/Vercel environments
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
    };
    
    res.clearCookie("jwt", cookieOptions);
  }

  // Then log the user out of the session
  req.logout((err) => {
    if (err) {
      console.error("Error logging out:", err);
      return res.status(500).send("Error logging out");
    }

    // Then destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error("Error destroying session:", err);
        return res.status(500).send("Error logging out");
      }

      // Clear the session cookie
      res.clearCookie("connect.sid");


      // Redirect to login page
      res.redirect("/login");
    });
  });
});

// Also add this route to handle /auth/logout
router.get("/auth/logout", (req, res) => {

  // First, log the user out
  req.logout((err) => {
    if (err) {
      console.error("Error logging out:", err);
      return res.status(500).send("Error logging out");
    }

    // Then destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error("Error destroying session:", err);
        return res.status(500).send("Error logging out");
      }

      // Clear the cookie
      res.clearCookie("connect.sid");


      // Redirect to login page
      res.redirect("/login");
    });
  });
});

router.get("/check-session", (req, res) => {
  res.json({
    isAuthenticated: req.isAuthenticated(),
    user: req.user ? { id: req.user._id, email: req.user.email } : null,
  });
});

// Email verification route
router.get("/verify-email/:token", async (req, res) => {
  try {
    console.log("Request host:", req.get("host"));
    console.log("Request full URL:", `${req.protocol}://${req.get("host")}${req.originalUrl}`);
    console.log("Request timestamp:", new Date().toISOString());
    
    const { token } = req.params;

    const user = await User.findOne({ verificationToken: token });
    
    if (!user) {
      const usersWithTokens = await User.find({ verificationToken: { $exists: true } }, 'email verificationToken');
      console.log("Users with verification tokens:", usersWithTokens.map(u => ({ email: u.email, token: u.verificationToken })));
      
      req.flash("error", "Invalid or expired verification token");
      return res.redirect("/login");
    }
    
    console.log("User found:", {
      id: user._id,
      email: user.email,
      isVerified: user.isVerified,
      firstName: user.firstName,
      lastName: user.lastName,
      verificationToken: user.verificationToken
    });

    // Update user verification status
    const wasVerified = user.isVerified;
    
    user.isVerified = true;
    user.verificationToken = undefined;

    console.log("User before saving:", {
      id: user._id,
      email: user.email,
      isVerified: user.isVerified,
      isModified: {
        isVerified: user.isModified("isVerified")
      },
      _previousIsVerified: user._previousIsVerified,
      _sendWelcomeEmail: user._sendWelcomeEmail
    });
    
    try {
      await user.save();
      console.log("User after saving:", {
        id: user._id,
        email: user.email,
        isVerified: user.isVerified,
        _previousIsVerified: user._previousIsVerified,
        _sendWelcomeEmail: user._sendWelcomeEmail
      });
    } catch (saveError) {
      console.error("Error saving user:", saveError);
      console.error("Save error stack:", saveError.stack);
      throw saveError; // Re-throw to be caught by the outer try/catch
    }

    // Check for the welcome email flag to ensure it was set
    if (!user._sendWelcomeEmail && !wasVerified && user.isVerified) {
      
      // Let's try to trigger the welcome email manually
      if (mongoose.connection && mongoose.connection.emit) {
        mongoose.connection.emit("userVerified", {
          userId: user._id,
          email: user.email,
          firstName: user.firstName || user.email.split('@')[0]
        });
      } else {
      }
    } else if (user._sendWelcomeEmail) {
    } else {
      console.log("No welcome email needed (user was already verified or flag not set)");
    }

    // Set success flash message and redirect
    req.flash("success", "Email verified successfully. You can now log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error during email verification:", error);
    console.error("Stack trace:", error.stack);
    req.flash("error", "An error occurred during email verification");
    res.redirect("/login");
  }
});

// Add this test route to verify email configuration
router.get("/test-email", async (req, res) => {
  try {
    const testMailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME,
        address: process.env.EMAIL_USER,
      },
      to: process.env.EMAIL_USER, // Send to yourself for testing
      subject: "Test Email - Zoho Configuration",
      html: "<h1>This is a test email</h1><p>If you receive this, the Zoho email configuration is working.</p>",
    };

    await sendMail(testMailOptions);
    res.send("Test email sent successfully. Check your inbox.");
  } catch (error) {
    console.error("Test email error:", error);
    res.status(500).send(`Failed to send test email: ${error.message}`);
  }
});

// Add this test route
router.get("/test-smtp", async (req, res) => {
  try {
    // Test SMTP connection
    await transporter.verify();

    // Try sending a test email
    const testMailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME,
        address: process.env.EMAIL_USER,
      },
      to: process.env.EMAIL_USER,
      subject: "SMTP Test Email",
      text: "If you receive this email, the SMTP configuration is working correctly.",
    };

    const info = await transporter.sendMail(testMailOptions);

    res.json({
      success: true,
      message: "SMTP test successful",
      emailInfo: info,
    });
  } catch (error) {
    console.error("SMTP test failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        command: error.command,
      },
    });
  }
});

// Add this test route
router.get("/test-zoho", async (req, res) => {
  try {

    const testMailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME,
        address: process.env.EMAIL_USER,
      },
      to: process.env.EMAIL_USER,
      subject: "Test Email from Zoho",
      text: "This is a test email to verify Zoho SMTP configuration.",
      html: "<h1>Test Email</h1><p>This is a test email to verify Zoho SMTP configuration.</p>",
    };

    const info = await sendMail(testMailOptions);

    res.json({
      success: true,
      message: "Test email sent successfully",
      info: info,
    });
  } catch (error) {
    console.error("Test email failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: error,
    });
  }
});

// Add this test route
router.get("/test-zoho", async (req, res) => {
  try {

    const testMailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_USER}>`,
      to: "<EMAIL>", // Use your test email
      subject: "Test Email from Zoho",
      html: "<h1>Test Email</h1><p>This is a test email from Zoho.</p>",
    };


    const info = await sendMail(testMailOptions);

    res.json({
      success: true,
      message: "Test email sent successfully",
      info: info,
    });
  } catch (error) {
    console.error("Test email failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: {
        code: error.code,
        command: error.command,
        response: error.response,
      },
    });
  }
});

// Add this test route
router.get("/test-zoho-pro", async (req, res) => {
  try {

    const testMailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_USER}>`,
      to: "<EMAIL>", // Your test email
      subject: "Test Email from Zoho Pro",
      html: "<h1>Test Email</h1><p>This is a test email from Zoho Pro SMTP.</p>",
    };


    const info = await sendMail(testMailOptions);

    res.json({
      success: true,
      message: "Test email sent successfully",
      info: info,
    });
  } catch (error) {
    console.error("Test email failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: {
        code: error.code,
        command: error.command,
        response: error.response,
      },
    });
  }
});

// Add this test route
router.get("/test-email-direct", async (req, res) => {
  try {

    const testMailOptions = {
      to: "<EMAIL>",
      subject: "Test Email from Zoho",
      text: "This is a test email.",
      html: "<h1>Test Email</h1><p>This is a test email sent directly.</p>",
    };


    const info = await sendMail(testMailOptions);

    res.json({
      success: true,
      message: "Test email sent successfully",
      info: info,
    });
  } catch (error) {
    console.error("Test email error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: {
        code: error.code,
        command: error.command,
        response: error.response,
      },
    });
  }
});

router.get("/test-email-delivery", async (req, res) => {
  try {
    const testEmail = "<EMAIL>"; // Your test email

    const mailOptions = {
      to: testEmail,
      subject: "Test Email Delivery",
      html: "<h1>Test Email</h1><p>This is a test email to verify delivery.</p>",
      headers: {
        "X-Test-Email": "true",
      },
    };

    const info = await sendMail(mailOptions);

    res.json({
      success: true,
      message: "Test email sent",
      info: info,
      instructions: "Please check your inbox and spam folder",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

router.get("/test-email-providers", async (req, res) => {
  const testEmails = [
    "<EMAIL>",
    // Add other test emails
  ];

  try {
    const results = await Promise.all(
      testEmails.map(async (email) => {
        const mailOptions = {
          to: email,
          subject: `Test Email Delivery to ${email.split("@")[1]}`,
          html: `
          <h1>Test Email</h1>
          <p>This is a test email sent to ${email}</p>
          <p>Time sent: ${new Date().toISOString()}</p>
        `,
        };

        try {
          const info = await sendMail(mailOptions);
          return {
            email,
            success: true,
            messageId: info.messageId,
          };
        } catch (error) {
          return {
            email,
            success: false,
            error: error.message,
          };
        }
      })
    );

    res.json({
      success: true,
      results,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Function to ensure logo exists and return the correct path
const ensureLogoExists = () => {
  try {
    // Check common locations for the logo
    const possiblePaths = [
      path.join(__dirname, "../public/images/pandalogo.png"),
      path.join(__dirname, "../public/images/logo.png"),
      path.join(__dirname, "../public/img/logo.png"),
      path.join(__dirname, "../public/assets/images/logo.png")
    ];

    // Return the first path that exists
    for (const logoPath of possiblePaths) {
      if (fs.existsSync(logoPath)) {
        return logoPath;
      }
    }

    // Fallback to the SVG logo if exists
    const svgPath = path.join(__dirname, "../public/images/pandalogo.svg");
    if (fs.existsSync(svgPath)) {
      return svgPath;
    }

    // Return a default path that might be created later, rather than failing
    return path.join(__dirname, "../public/images/pandalogo.png");
  } catch (error) {
    console.error("Error in ensureLogoExists:", error);
    // Return a path anyway to avoid breaking the email sending process
    return path.join(__dirname, "../public/images/pandalogo.png");
  }
};

// Add this debugging route
router.get("/test-email-template", async (req, res) => {
  try {
    const emailTemplatePath = path.join(
      __dirname,
      "../views/register-email.ejs"
    );
    const verificationUrl = "https://example.com/verify";

    const emailHtml = await ejs.renderFile(emailTemplatePath, {
      firstName: "Test User",
      verificationUrl: verificationUrl,
    });

    res.send(emailHtml); // This will show you how the template renders
  } catch (error) {
    console.error("Template rendering error:", error);
    res.status(500).send(`Error rendering template: ${error.message}`);
  }
});

// Add this test route to verify template rendering
router.get("/test-template", async (req, res) => {
  try {
    const templatePath = path.join(__dirname, "../views/register-email.ejs");
    const html = await ejs.renderFile(templatePath, {
      firstName: "Test",
      verificationUrl: "http://example.com/verify",
    });
    res.send(html);
  } catch (error) {
    console.error("Template test error:", error);
    res.status(500).send(error.message);
  }
});

// Fix the syntax error in the test-user route
router.get("/test-user", async (req, res) => {
  try {
    const testUser = await User.findOne({
      email: "<EMAIL>",
    });
    if (testUser) {
      res.json({
        exists: true,
        hasPassword: !!testUser.password,
        email: testUser.email,
        passwordLength: testUser.password ? testUser.password.length : 0,
      });
    } else {
      res.json({ exists: false });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add this test route to create a test user
router.get("/create-test-user", async (req, res) => {
  try {
    // First check if user already exists
    let user = await User.findOne({
      email: "<EMAIL>",
    });

    if (user) {
      return res.json({
        message: "User already exists",
        user: {
          email: user.email,
          hasPassword: !!user.password,
        },
      });
    }

    // Find the 'owner' role
    const ownerRole = await Role.findOne({ name: "owner" });
    if (!ownerRole) {
      throw new Error("Owner role not found");
    }

    // Create new user
    user = new User({
      firstName: "Test",
      lastName: "User",
      email: "<EMAIL>",
      password: "Password123!", // This will be hashed by the pre-save hook
      role: ownerRole._id,
      roleName: "owner",
      isVerified: true, // Set to true for testing
    });

    await user.save();

    res.json({
      message: "Test user created successfully",
      user: {
        email: user.email,
        hasPassword: !!user.password,
      },
    });
  } catch (error) {
    console.error("Error creating test user:", error);
    res.status(500).json({ error: error.message });
  }
});

// Add a password reset route for testing
router.get("/reset-test-password", async (req, res) => {
  try {
    const user = await User.findOne({
      email: "<EMAIL>",
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Update password
    user.password = "Password123!"; // Will be hashed by pre-save hook
    await user.save();

    res.json({ success: true, message: "Password has been reset" });
  } catch (error) {
    console.error("Error resetting password:", error);
    res.status(500).json({ error: error.message });
  }
});

// Add this route to verify password hash
router.get("/verify-password-hash", async (req, res) => {
  try {
    const user = await User.findOne({
      email: "<EMAIL>",
    });

    if (!user) {
      return res.json({ exists: false });
    }

    // Test password verification
    const testPassword = "Password123!";
    const isMatch = await user.comparePassword(testPassword);

    res.json({
      exists: true,
      hasPassword: !!user.password,
      passwordVerifies: isMatch,
      hashLength: user.password ? user.password.length : 0,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/test-password", async (req, res) => {
  try {
    const user = await User.findOne({
      email: "<EMAIL>",
    });

    if (!user) {
      return res.json({ exists: false });
    }

    // Test with the known password
    const testPassword = "Password123!";
    const isMatch = await user.comparePassword(testPassword);

    res.json({
      exists: true,
      hasPassword: !!user.password,
      passwordHash: user.password,
      testPasswordWorks: isMatch,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update the reset password routes
router.get("/reset-password/:token", async (req, res) => {
  try {
    const { token } = req.params;

    // Find user by reset token and ensure it's not expired
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      req.flash("error", "Password reset token is invalid or has expired.");
      return res.redirect("/login");
    }


    // Generate CSRF token
    const csrfToken = req.csrfToken();

    // Render the reset password form
    res.render("reset_password", {
      token,
      csrfToken,
      errorMessages: req.flash("error"),
      successMessages: req.flash("success"),
    });
  } catch (error) {
    console.error("Error in reset password route:", error);
    req.flash("error", "An error occurred");
    res.redirect("/login");
  }
});

// Add POST route for password reset
router.post("/reset-password/:token", async (req, res) => {
  try {
    const { token } = req.params;
    const { password, confirmPassword } = req.body;


    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect(`/reset-password/${token}`);
    }

    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      req.flash("error", "Password reset token is invalid or has expired.");
      return res.redirect("/login");
    }


    // Update password
    user.password = password; // The pre-save hook will hash it
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    user.passwordLastChanged = new Date();
    await user.save();


    req.flash("success", "Your password has been reset. Please log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error in reset password submission:", error);
    req.flash("error", "An error occurred while resetting your password.");
    res.redirect("/login");
  }
});

// Middleware to check onboarding status
const checkOnboarding = async (req, res, next) => {
  try {
    // Skip onboarding check for employees
    const user = await User.findById(req.user._id).populate("role");

    if (user.role.name === "employee" || user.roleName === "employee") {
      return next();
    }

    // Only check onboarding for owners/admins
    if (!user.onboardingComplete) {
      return res.redirect(
        `/clients/${user.currentCompany.companyCode}/onboarding/welcome`
      );
    }
    next();
  } catch (error) {
    console.error("Error in checkOnboarding middleware:", error);
    next(error);
  }
};

// Onboarding routes
router.get("/onboarding/step1", ensureAuthenticated, async (req, res) => {
  try {
    res.render("onboarding-layout", {
      currentStep: 1,
      body: await ejs.renderFile("views/employer-details.ejs", {
        user: req.user,
        company: req.user.currentCompany,
        isOnboarding: true,
      }),
    });
  } catch (error) {
    console.error("Error rendering onboarding step 1:", error);
    res.redirect("/login");
  }
});

router.get("/onboarding/step2", ensureAuthenticated, async (req, res) => {
  try {
    res.render("onboarding-layout", {
      currentStep: 2,
      body: await ejs.renderFile("views/pay-frequency.ejs", {
        user: req.user,
        company: req.user.currentCompany,
        isOnboarding: true,
      }),
    });
  } catch (error) {
    console.error("Error rendering onboarding step 2:", error);
    res.redirect("/onboarding/step1");
  }
});

router.get("/onboarding/step3", ensureAuthenticated, async (req, res) => {
  try {
    res.render("onboarding-layout", {
      currentStep: 3,
      body: await ejs.renderFile("views/employeeManagement.ejs", {
        user: req.user,
        company: req.user.currentCompany,
        isOnboarding: true,
      }),
    });
  } catch (error) {
    console.error("Error rendering onboarding step 3:", error);
    res.redirect("/onboarding/step2");
  }
});

router.post("/onboarding/complete", ensureAuthenticated, async (req, res) => {
  try {
    await User.findByIdAndUpdate(req.user._id, {
      onboardingComplete: true,
      onboardingStep: 3,
    });

    res.json({ success: true });
  } catch (error) {
    console.error("Error completing onboarding:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Modify your login post route to handle onboarding redirect
router.post(
  "/login",
  passport.authenticate("local", {
    failureRedirect: "/login",
    failureFlash: true,
  }),
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id).populate("currentCompany");

      if (!user.currentCompany) {
        req.flash("error", "No company associated with account");
        return res.redirect("/login");
      }

      const companyCode = user.currentCompany.companyCode;

      // Redirect based on onboarding status
      if (user.onboardingComplete) {
        res.redirect(`/clients/${companyCode}/dashboard`);
      } else {
        res.redirect(`/clients/${companyCode}/onboarding/welcome`);
      }
    } catch (error) {
      console.error("Error in login redirect:", error);
      req.flash("error", "An error occurred during login");
      res.redirect("/login");
    }
  }
);

// Add this route to handle direct access to onboarding steps
router.get(
  "/onboarding/step:step(1|2|3)",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const stepNumber = parseInt(req.params.step);
      const user = await User.findById(req.user._id);

      // Prevent skipping steps
      if (stepNumber > (user.onboardingStep || 1)) {
        return res.redirect(`/onboarding/step${user.onboardingStep || 1}`);
      }

      // Update current step
      if (user.onboardingStep !== stepNumber) {
        user.onboardingStep = stepNumber;
        await user.save();
      }

      let templateName;
      switch (stepNumber) {
        case 1:
          templateName = "employer-details";
          break;
        case 2:
          templateName = "pay-frequency";
          break;
        case 3:
          templateName = "employeeManagement";
          break;
      }

      const company = await Company.findById(user.currentCompany);

      res.render("onboarding-layout", {
        currentStep: stepNumber,
        body: await ejs.renderFile(`views/${templateName}.ejs`, {
          user,
          company,
          isOnboarding: true,
          csrfToken: req.csrfToken(),
        }),
      });
    } catch (error) {
      console.error("Error in onboarding route:", error);
      res.redirect("/login");
    }
  }
);

// Apply the middleware to dashboard route
router.get(
  "/dashboard",
  ensureAuthenticated,
  checkOnboarding, // Use checkOnboarding instead of ensureOnboardingComplete
  (req, res) => {
    // Your existing dashboard route code
  }
);

// Add this test route to easily access onboarding layout
router.get("/test-onboarding", ensureAuthenticated, async (req, res) => {
  try {
    // Force user into onboarding mode
    await User.findByIdAndUpdate(req.user._id, {
      onboardingComplete: false,
      onboardingStep: 1,
    });

    res.redirect("/onboarding/step1");
  } catch (error) {
    console.error("Error accessing test onboarding:", error);
    res.redirect("/login");
  }
});

// Add this route to reset onboarding status (for testing)
router.get("/reset-onboarding", ensureAuthenticated, async (req, res) => {
  try {
    await User.findByIdAndUpdate(req.user._id, {
      onboardingComplete: false,
      onboardingStep: 1,
    });

    req.flash("success", "Onboarding status reset. Please log in again.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error resetting onboarding:", error);
    res.redirect("/dashboard");
  }
});

// Onboarding route debugging disabled to reduce terminal noise

// Add the onboarding welcome route
router.get(
  "/clients/:companyCode/onboarding/welcome",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      const user = await User.findById(req.user._id)
        .populate("role")
        .populate("currentCompany");

      // Skip onboarding for non-owner roles
      if (user.role.name !== "owner") {
        // Redirect based on role
        if (user.role.name === "employee") {
          return res.redirect(`/employee-portal/${user._id}`);
        } else {
          // For other roles, redirect to dashboard or appropriate page
          return res.redirect("/dashboard");
        }
      }

      // Check if company exists and user is the owner
      const company = await Company.findOne({ companyCode });
      if (!company || company.owner.toString() !== user._id.toString()) {
        return res.redirect("/dashboard");
      }

      console.log({
        user: user._id,
        company: company._id,
        onboardingStep: user.onboardingStep,
      });

      res.render("onboarding-welcome", {
        user,
        company,
        steps: [
          {
            title: "Employer Details",
            icon: "📋",
            time: "5 minutes",
            items: ["Company Info", "Registration Numbers", "Contact Details"],
            step: 1,
          },
          {
            title: "Pay Frequency",
            icon: "📅",
            time: "3 minutes",
            items: ["Pay Periods", "Payment Dates", "Processing Schedule"],
            step: 2,
          },
        ],
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error in onboarding welcome:", error);
      req.flash("error", "Error loading welcome page");
      res.redirect("/login");
    }
  }
);

// Step 1 route
router.get(
  "/clients/:companyCode/onboarding/step1",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      const user = await User.findById(req.user._id).populate("currentCompany");

      if (!user) {
        return res.redirect("/login");
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.redirect("/dashboard");
      }

      // Get or create employer details
      let employerDetails = await EmployerDetails.findOne({
        company: company._id,
      });
      if (!employerDetails) {
        employerDetails = new EmployerDetails({
          company: company._id,
          tradingName: company.name,
          physicalAddress: {},
        });
      }



      return res.render("employer-details", {
        user,
        company,
        employerDetails, // Pass employer details to template
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        isOnboarding: true,
        currentStep: 1,
      });
    } catch (error) {
      console.error("Error in onboarding step 1:", error);
      req.flash("error", "Error loading employer details");
      return res.redirect("/login");
    }
  }
);

// Update the POST route to handle employer details and return JSON
router.post(
  "/clients/:companyCode/onboarding/step1",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const user = await User.findById(req.user._id);
      const company = await Company.findOne({ companyCode });


      // Update or create employer details
      let employerDetails = await EmployerDetails.findOne({
        company: company._id,
      });
      if (!employerDetails) {
        employerDetails = new EmployerDetails({
          company: company._id,
        });
      }

      // Update employer details with form data
      employerDetails.tradingName = req.body.tradingName;
      employerDetails.physicalAddress = {
        unitNumber: req.body.physicalAddress?.unitNumber,
        complex: req.body.physicalAddress?.complex,
        streetNumber: req.body.physicalAddress?.streetNumber,
        street: req.body.physicalAddress?.street,
        suburbDistrict: req.body.physicalAddress?.suburbDistrict,
        cityTown: req.body.physicalAddress?.cityTown,
        code: req.body.physicalAddress?.code,
      };

      await employerDetails.save();

      // Update user's onboarding step
      user.onboardingStep = 2;
      await user.save();

      // Return JSON response
      return res.json({
        success: true,
        message: "Employer details saved successfully",
        nextStep: `/clients/${companyCode}/onboarding/step2`,
      });
    } catch (error) {
      console.error("Error saving step 1:", error);
      return res.status(500).json({
        success: false,
        error: "Error saving employer details",
        details: error.message,
      });
    }
  }
);

// Step 2 route
router.get(
  "/clients/:companyCode/onboarding/step2",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const user = await User.findById(req.user._id).populate("currentCompany");

      if (!user || user.onboardingStep < 2) {
        return res.redirect(`/clients/${companyCode}/onboarding/step1`);
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.redirect("/dashboard");
      }

      res.render("pay-frequency", {
        user,
        company,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        isOnboarding: true,
        currentStep: 2,
      });
    } catch (error) {
      console.error("Error in step 2:", error);
      res.redirect(`/clients/${req.params.companyCode}/onboarding/step1`);
    }
  }
);

// Step 3 route
router.get(
  "/clients/:companyCode/onboarding/step3",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const user = await User.findById(req.user._id).populate("currentCompany");

      if (!user || user.onboardingStep < 3) {
        return res.redirect(`/clients/${companyCode}/onboarding/step2`);
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.redirect("/dashboard");
      }

      res.render("employee-setup", {
        user,
        company,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        isOnboarding: true,
        currentStep: 3,
      });
    } catch (error) {
      console.error("Error in step 3:", error);
      res.redirect(`/clients/${req.params.companyCode}/onboarding/step2`);
    }
  }
);

// Add POST routes to handle form submissions for each step
router.post(
  "/clients/:companyCode/onboarding/step1",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const user = await User.findById(req.user._id);

      if (user.onboardingStep > 1) {
        // If user has already completed step1, redirect to employeeManagement
        return res.json({
          success: true,
          message: "Redirecting to Employee Management",
          nextStep: `/clients/${companyCode}/employeeManagement`,
        });
      }

      // Normal step1 processing
      // ... existing code ...

      return res.json({
        success: true,
        message: "Employer details saved successfully",
        nextStep: `/clients/${companyCode}/onboarding/step2`,
      });
    } catch (error) {
      // ... error handling ...
    }
  }
);

// Similar POST routes for steps 2 and 3

// Add this POST route for step2
router.post(
  "/clients/:companyCode/onboarding/step2",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      const user = await User.findById(req.user._id);
      const company = await Company.findOne({ companyCode });

      if (!user || !company) {
        return res.status(404).json({
          success: false,
          error: "User or company not found",
        });
      }

      // Create or update pay frequency settings
      let payFrequency = await PayFrequency.findOne({ company: company._id });
      if (!payFrequency) {
        payFrequency = new PayFrequency({
          company: company._id,
          frequency: req.body.frequency,
          lastDayOfPeriod: req.body.lastDayOfPeriod || "monthend",
          lastDayOfMonth: req.body.lastDayOfMonth || "monthend",
          biWeeklyLastDay: req.body.biWeeklyLastDay || "monthend",
          biWeeklyInterimDay: req.body.biWeeklyInterimDay || "15",
          firstPayrollPeriodEndDate: new Date(
            req.body.firstPayrollPeriodEndDate || Date.now()
          ),
          goFurtherBack: req.body.goFurtherBack || false,
          goBackYears: req.body.goBackYears || 1,
        });
      } else {
        // Update existing pay frequency
        payFrequency.frequency = req.body.frequency;
        payFrequency.lastDayOfPeriod =
          req.body.lastDayOfPeriod || payFrequency.lastDayOfPeriod;
        payFrequency.lastDayOfMonth =
          req.body.lastDayOfMonth || payFrequency.lastDayOfMonth;
        payFrequency.biWeeklyLastDay =
          req.body.biWeeklyLastDay || payFrequency.biWeeklyLastDay;
        payFrequency.biWeeklyInterimDay =
          req.body.biWeeklyInterimDay || payFrequency.biWeeklyInterimDay;
        payFrequency.firstPayrollPeriodEndDate = new Date(
          req.body.firstPayrollPeriodEndDate
        );
        payFrequency.goFurtherBack = req.body.goFurtherBack;
        payFrequency.goBackYears = req.body.goBackYears;
      }

      await payFrequency.save();

      // Mark onboarding as complete instead of going to step 3
      user.onboardingComplete = true;
      await user.save();

      // Return JSON response with redirect to employeeManagement
      return res.json({
        success: true,
        message: "Pay frequency settings saved successfully",
        nextStep: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error saving step 2:", error);
      return res.status(500).json({
        success: false,
        error: "Error saving pay frequency settings",
        details: error.message,
      });
    }
  }
);

// Add middleware to check 2FA status
const ensure2FAVerified = async (req, res, next) => {
  try {
    if (!req.isAuthenticated()) {
      return res.redirect("/login");
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.redirect("/login");
    }

    if (user.twoFactorEnabled) {
      // Check Redis for 2FA verification status
      const twoFAState = await RedisManager.get2FAState(user._id.toString());

      if (!twoFAState || !twoFAState.verified) {
        // Store intended URL
        await RedisManager.setReturnTo(user._id.toString(), req.originalUrl);
        return res.redirect("/verify-2fa");
      }
    }

    next();
  } catch (error) {
    console.error("2FA verification check error:", error);
    res.redirect("/login");
  }
};

// Apply ensure2FAVerified middleware to protected routes
router.use("/clients/:companyCode/*", ensure2FAVerified);
router.use("/dashboard", ensure2FAVerified);
router.use("/settings", ensure2FAVerified);

// Add profile route using existing employee-profile view
router.get(
  "/profile",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id)
        .populate("role")
        .populate("companies")
        .populate("currentCompany");

      // Get login history
      const loginHistory = await LoginHistory.find({ user: req.user._id })
        .sort({ timestamp: -1 })
        .limit(10);

      // Get the current company
      const company = user.currentCompany;
      if (!company) {
        req.flash("error", "No company selected");
        return res.redirect("/dashboard");
      }

      // Map user data to employee format expected by the view
      const employee = {
        fullName: `${user.firstName} ${user.lastName}`,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        role: user.role,
        company: company,
        _id: user._id,
        ...user._doc, // spread the rest of user properties
      };

      // Create billing data from company information
      const billingData = {
        trialExpiryDate:
          company.trialEndDate ||
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now if not set
        trialStartDate: company.trialStartDate || new Date(),
        billingStartDate: company.billingStartDate,
        subscriptionStatus: company.subscriptionStatus || "trial",
        isTrialExpired: company.trialEndDate
          ? new Date(company.trialEndDate) < new Date()
          : false,
      };

      res.render("employee-profile", {
        user: user,
        employee: employee,
        company: company,
        billingData: billingData,
        loginHistory: loginHistory,
        moment: require("moment"),
        csrfToken: req.csrfToken(),
        messages: req.flash(),
        req: req,
      });
    } catch (error) {
      console.error("Error fetching profile:", error);
      req.flash("error", "Error loading profile");
      res.redirect("/dashboard");
    }
  }
);

// Add profile update route
router.post(
  "/profile/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { firstName, lastName, phone, bio } = req.body;

      const user = await User.findByIdAndUpdate(
        req.user._id,
        { firstName, lastName, phone, bio },
        { new: true, runValidators: true }
      );

      req.flash("success", "Profile updated successfully");
      res.redirect("/profile");
    } catch (error) {
      console.error("Error updating profile:", error);
      req.flash("error", "Error updating profile");
      res.redirect("/profile");
    }
  }
);

// Add route to force reset password (temporary)
router.post("/force-reset-password", async (req, res) => {
  try {
    const email = "<EMAIL>";
    const newPassword = "12345678";

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Set new password (will be hashed by pre-save hook)
    user.password = newPassword;
    await user.save();

    res.json({ success: true, message: "Password has been reset" });
  } catch (error) {
    console.error("Error in force reset:", error);
    res.status(500).json({ error: error.message });
  }
});

// Resend verification email route
router.post("/resend-verification", async (req, res) => {
  try {
    const { email } = req.body;

    // Find the user
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      req.flash("error", "No account found with that email address.");
      return res.redirect("/login");
    }

    // Check if already verified
    if (user.isVerified) {
      req.flash("success", "Your email is already verified. You can now log in.");
      return res.redirect("/login");
    }

    // Generate new verification token if not exists
    if (!user.verificationToken) {
      user.verificationToken = crypto.randomBytes(20).toString("hex");
      await user.save();
    }

    // Update the verification URL generation to use our utility
    const verificationUrl = getAppUrl(req, `verify-email/${user.verificationToken}`);


    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/verification.ejs"),
      {
        firstName: user.firstName,
        verificationUrl: verificationUrl,
      }
    );

    const mailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER,
      },
      to: user.email,
      subject: "Verify Your Email - Panda Solutions",
      html: emailHtml,
      attachments: [
        {
          filename: "logo.png",
          path: ensureLogoExists(),
          cid: "logo",
        },
      ],
    };

    // Send verification email
    await sendMail(mailOptions);

    req.flash("success", "Verification email resent. Please check your inbox.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error resending verification email:", error);
    req.flash("error", "Failed to resend verification email. Please try again.");
    res.redirect("/login");
  }
});

// Add a test route for debugging welcome email functionality
router.get("/test-welcome-email", async (req, res) => {
  try {
    
    // Get a test email from query parameter or use a default
    const testEmail = req.query.email || process.env.EMAIL_USER;
    const testName = req.query.name || "Test User";
    
    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: "No test email provided and no EMAIL_USER environment variable set"
      });
    }
    
    console.log("Test email parameters:", {
      email: testEmail,
      name: testName
    });
    
    
    // Load the welcome email template
    const welcomeTemplatePath = path.join(__dirname, "../views/emails/welcome.ejs");
    
    // Check if welcome email template exists
    if (!fs.existsSync(welcomeTemplatePath)) {
      console.error("Welcome email template not found at:", welcomeTemplatePath);
      return res.status(500).json({
        success: false,
        message: "Welcome email template not found",
        path: welcomeTemplatePath
      });
    }
    
    
    // Render welcome email template
    const emailHtml = await ejs.renderFile(
      welcomeTemplatePath,
      {
        firstName: testName,
        email: testEmail
      }
    );
    
    // Check if logo exists
    const logoPath = ensureLogoExists();
    if (!logoPath) {
    }
    
    const mailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER,
      },
      to: testEmail,
      subject: "Welcome to Panda Solutions - Getting Started (TEST)",
      html: emailHtml,
      attachments: [
        {
          filename: "logo.png",
          path: logoPath,
          cid: "logo",
        },
      ],
    };
    
    const directResult = await sendMail(mailOptions);
    
    
    // Simulate the userVerified event through MongoDB connection
    mongoose.connection.emit("userVerified", {
      userId: "test-user-id",
      email: testEmail,
      firstName: testName
    });
    
    
    // Return success response
    res.json({
      success: true,
      message: "Welcome email test initiated via both approaches",
      directResult: {
        messageId: directResult.messageId,
        response: directResult.response,
      },
      eventEmitted: true,
      testEmail: testEmail,
      note: "Check server logs for event-based email results as they happen asynchronously"
    });
  } catch (error) {
    console.error("Welcome email test failed:", error);
    console.error("Stack trace:", error.stack);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add a test route for debugging user model verification hooks
router.get("/test-user-verification", async (req, res) => {
  try {
    
    // Get test parameters from query
    const userId = req.query.userId;
    const createNew = req.query.createNew === 'true';
    const testEmail = req.query.email || `test-${Date.now()}@example.com`;
    

    
    let user;
    
    // Either create a new test user or find an existing one
    if (createNew) {
      
      user = new User({
        email: testEmail,
        username: testEmail,
        firstName: "Test",
        lastName: "User",
        password: "password123", // This will get hashed by the pre-save hook
        isVerified: false // Start unverified
      });
      
      console.log("New test user created (not yet saved):", {
        id: user._id,
        email: user.email,
        isVerified: user.isVerified
      });
    } else if (userId) {
      user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found with provided ID"
        });
      }
      
      console.log("Found existing user:", {
        id: user._id,
        email: user.email,
        isVerified: user.isVerified
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Either userId or createNew=true must be provided"
      });
    }
    
    // Log user state before any changes
    console.log("User state before verification:", {
      id: user._id,
      email: user.email,
      isVerified: user.isVerified,
      _previousIsVerified: user._previousIsVerified,
      _sendWelcomeEmail: user._sendWelcomeEmail
    });
    
    // Toggle verification status to trigger hooks
    if (user.isVerified) {
      user.isVerified = false;
      await user.save();
    }
    
    // Now set to verified to trigger the hooks
    user.isVerified = true;
    
    console.log("User before save with isVerified = true:", {
      isVerified: user.isVerified,
      isModified: user.isModified("isVerified"),
      _previousIsVerified: user._previousIsVerified
    });
    
    await user.save();
    
    console.log("User saved successfully. State after save:", {
      id: user._id,
      email: user.email,
      isVerified: user.isVerified,
      _previousIsVerified: user._previousIsVerified,
      _sendWelcomeEmail: user._sendWelcomeEmail
    });
    
    // Return success response
    res.json({
      success: true,
      message: "User verification test completed successfully",
      user: {
        id: user._id,
        email: user.email,
        isVerified: user.isVerified,
        _previousIsVerified: user._previousIsVerified,
        _sendWelcomeEmail: user._sendWelcomeEmail
      },
      note: "Check server logs for detailed hook execution information"
    });
  } catch (error) {
    console.error("User verification test failed:", error);
    console.error("Stack trace:", error.stack);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add a test route for debugging Mongoose events
router.get("/test-mongoose-events", async (req, res) => {
  try {
    
    // Get event name from query or use default
    const eventName = req.query.event || "userVerified";
    const testEmail = req.query.email || process.env.EMAIL_USER || "<EMAIL>";
    const testName = req.query.name || "Test User";
    

    
    // Check if connection has event emitter
    if (!mongoose.connection.emit) {
      return res.status(500).json({
        success: false,
        message: "mongoose.connection.emit is not available"
      });
    }
    

    
    // Create event payload
    const eventData = {
      userId: "test-user-id",
      email: testEmail,
      firstName: testName,
      timestamp: new Date().toISOString()
    };
    
    
    // Register a test listener first
    const testListener = (data) => {
    };
    
    mongoose.connection.once(eventName, testListener);
    
    // Emit the event
    mongoose.connection.emit(eventName, eventData);
    

    
    // Return success response
    res.json({
      success: true,
      message: `Mongoose event "${eventName}" emitted successfully`,
      eventData,
      listeners: mongoose.connection.listenerCount(eventName),
      eventNames: Object.keys(mongoose.connection._events || {}),
      note: "Check server logs for event processing results"
    });
  } catch (error) {
    console.error("Mongoose event test failed:", error);
    console.error("Stack trace:", error.stack);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add a diagnostic test route for MongoDB event system
router.get("/test-mongoose-event-system", async (req, res) => {
  try {
    
    // Check if mongoose is properly initialized
    

    
    // Check for userVerified event listener
    const userVerifiedListeners = events['userVerified'] ? 
      (Array.isArray(events['userVerified']) ? events['userVerified'].length : 1) : 
      0;
    
    
    if (userVerifiedListeners === 0) {
    }
    
    // Add a test event listener for userVerified that will log but not send email
    
    const diagnosticListener = function(userData) {
      console.log("Event data:", JSON.stringify(userData, null, 2));
      console.log("Event timestamp:", new Date().toISOString());
    };
    
    // Add listener with a unique name to avoid duplicates
    mongoose.connection.on("userVerified", diagnosticListener);
    

    
    // Test event emission
    mongoose.connection.emit("userVerified", {
      userId: "test-diagnostic-id",
      email: req.query.email || "<EMAIL>",
      firstName: req.query.name || "Diagnostic Test"
    });
    
    // Add a direct event handler on app.js
    
    // Import required modules for direct test
    const path = require("path");
    const fs = require("fs");
    const ejs = require("ejs");
    
    // Check if the email template exists
    const welcomeTemplatePath = path.join(__dirname, "../views/emails/welcome.ejs");
    
    let templateExists = false;
    if (fs.existsSync(welcomeTemplatePath)) {
      templateExists = true;
    } else {
      
      // List contents of views/emails directory if it exists
      const emailsDirPath = path.join(__dirname, "../views/emails");
      if (fs.existsSync(emailsDirPath)) {
        console.log("Contents of emails directory:", fs.readdirSync(emailsDirPath));
      } else {
        
        // Check content of views directory
        const viewsPath = path.join(__dirname, "../views");
        if (fs.existsSync(viewsPath)) {
          console.log("Views directory exists, contents:", fs.readdirSync(viewsPath));
        } else {
        }
      }
    }
    
    // Also check the MongoDB connection object to see if event emission works
    if (typeof mongoose.connection.emit === 'function') {
    } else {
    }
    
    return res.json({
      success: true,
      diagnosticResults: {
        mongooseState: {
          readyState: mongoose.connection.readyState,
          hasEventEmitter: typeof mongoose.connection.emit === 'function',
          registeredEvents: Object.keys(events),
          userVerifiedListeners: userVerifiedListeners,
          listenerCountAfterTest: mongoose.connection.listenerCount("userVerified")
        },
        templateDiagnostics: {
          templatePath: welcomeTemplatePath,
          templateExists: templateExists,
        },
        eventEmissionTest: "Event emission attempted - check logs for results"
      },
      recommendations: [
        userVerifiedListeners === 0 ? 
          "The 'userVerified' event has no listeners. Check app.js to ensure the listener is added after MongoDB connects." : 
          "Event listeners are properly registered.",
        !templateExists ? 
          "The welcome email template doesn't exist. Create the file at " + welcomeTemplatePath : 
          "Welcome email template exists.",
        typeof mongoose.connection.emit !== 'function' ?
          "The mongoose.connection.emit function doesn't exist. This is a critical issue." :
          "Event emission capability is working."
      ],
      nextSteps: "Check server logs for more detailed diagnostic information"
    });
  } catch (error) {
    console.error("Error in MongoDB event system diagnostic:", error);
    console.error("Stack trace:", error.stack);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add a direct welcome email test without using events
router.get("/test-direct-welcome-email", async (req, res) => {
  try {
    
    // Get a test email from query parameter or use a default
    const testEmail = req.query.email || process.env.EMAIL_USER;
    const testName = req.query.name || "Test User";
    
    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: "No test email provided and no EMAIL_USER environment variable set"
      });
    }
    
    console.log("Test parameters:", {
      email: testEmail,
      name: testName
    });
    
    // Import required modules
    const path = require("path");
    const fs = require("fs");
    const ejs = require("ejs");
    const nodemailer = require("nodemailer");
    
    // Create directory and template if needed
    const welcomeTemplatePath = path.join(__dirname, "../views/emails/welcome.ejs");
    const emailsDir = path.join(__dirname, "../views/emails");
    
    // Ensure directory exists
    if (!fs.existsSync(emailsDir)) {
      try {
        fs.mkdirSync(emailsDir, { recursive: true });
      } catch (dirError) {
        console.error("Error creating emails directory:", dirError);
        throw dirError;
      }
    }
    
    // Create template if it doesn't exist
    if (!fs.existsSync(welcomeTemplatePath)) {
      const defaultTemplate = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Welcome to Panda Solutions</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .logo { max-width: 200px; }
    h1 { color: #2c3e50; }
    .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="cid:logo" alt="Panda Solutions Logo" class="logo">
      <h1>Welcome to Panda Solutions!</h1>
    </div>
    
    <p>Hello <%= firstName %>,</p>
    
    <p>Thank you for joining Panda Solutions. Your account has been verified and is now ready to use.</p>
    
    <p>Here's what you need to know to get started:</p>
    
    <ul>
      <li>Our support team is available via <NAME_EMAIL> with a 48-hour turnaround time.</li>
      <li>Check out our <a href="https://www.youtube.com/watch?v=yourvideolink">video guide</a> for help navigating the platform.</li>
      <li>For urgent matters, contact us at +27 00 000 0000.</li>
    </ul>
    
    <p>We're excited to have you on board!</p>
    
    <p>Best regards,<br>
    The Panda Solutions Team</p>
    
    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Panda Solutions. All rights reserved.</p>
      <p>This email was sent to <%= email %>.</p>
    </div>
  </div>
</body>
</html>`;
      
      try {
        fs.writeFileSync(welcomeTemplatePath, defaultTemplate);
      } catch (templateError) {
        console.error("Error creating welcome template:", templateError);
        throw templateError;
      }
    } else {
    }
    
    // Helper function to find logo
    const ensureLogoExists = () => {
      try {
        // Check common locations for the logo
        const possiblePaths = [
          path.join(__dirname, "../public/images/pandalogo.png"),
          path.join(__dirname, "../public/images/logo.png"),
          path.join(__dirname, "../public/img/logo.png"),
          path.join(__dirname, "../public/assets/images/logo.png")
        ];
    
        // Return the first path that exists
        for (const logoPath of possiblePaths) {
          if (fs.existsSync(logoPath)) {
            return logoPath;
          } else {
          }
        }
    
        // Fallback to the SVG logo if exists
        const svgPath = path.join(__dirname, "../public/images/pandalogo.svg");
        if (fs.existsSync(svgPath)) {
          return svgPath;
        } else {
        }
    
        
        // Create public/images directory if needed
        const imagesDir = path.join(__dirname, "../public/images");
        if (!fs.existsSync(imagesDir)) {
          try {
            fs.mkdirSync(imagesDir, { recursive: true });
          } catch (dirError) {
            console.error("Error creating images directory:", dirError);
          }
        }
        
        // Create a very basic logo as SVG
        const logoSvgPath = path.join(__dirname, "../public/images/pandalogo.svg");
        const basicSvg = `<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="20" y="35" font-family="Arial" font-size="24" fill="#333">Panda Solutions</text>
</svg>`;
        
        try {
          fs.writeFileSync(logoSvgPath, basicSvg);
          return logoSvgPath;
        } catch (logoError) {
          console.error("Error creating basic logo:", logoError);
          return null;
        }
      } catch (error) {
        console.error("Error finding logo:", error);
        return null;
      }
    };
    
    // Log environment variables
    console.log("Email configuration:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === "true",
      user: process.env.EMAIL_USER ? "Set" : "Not set",
      pass: process.env.EMAIL_PASS ? "Set" : "Not set"
    });
    
    // Create transporter with detailed logging
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === "true",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false, // Only use this in development
      },
      debug: true, // Enable debug output
      logger: true, // Log information about mail being sent
    });
    
    // Test transporter connection
    try {
      const verifyResult = await transporter.verify();
    } catch (verifyError) {
      console.error("Transporter verification failed:", verifyError);
      return res.status(500).json({
        success: false,
        message: "Email transporter verification failed",
        error: verifyError.message
      });
    }
    
    // Render welcome email template
    const emailHtml = await ejs.renderFile(
      welcomeTemplatePath,
      {
        firstName: testName,
        email: testEmail
      }
    );
    
    // Find logo
    const logoPath = ensureLogoExists();
    
    // Prepare mail options
    const mailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
        address: process.env.EMAIL_USER,
      },
      to: testEmail,
      subject: "Welcome to Panda Solutions - Getting Started (DIRECT TEST)",
      html: emailHtml,
      attachments: logoPath ? [
        {
          filename: "logo.png",
          path: logoPath,
          cid: "logo",
        },
      ] : [],
      headers: {
        "X-Priority": "1",
        "X-MSMail-Priority": "High",
        Importance: "high",
        "Message-ID": `<${Date.now()}.${Math.random().toString(36).substring(2)}@pss-group.co.za>`,
        "List-Unsubscribe": `<mailto:${process.env.EMAIL_USER}?subject=unsubscribe>`,
        "X-Mailer": "Panda Solutions Mailer/1.0",
        "X-Entity-Ref-ID": require("crypto").randomBytes(32).toString("hex"),
      },
    };
    
    // Send email
    try {
      const info = await transporter.sendMail(mailOptions);

      
      return res.json({
        success: true,
        message: "Direct welcome email test completed successfully",
        emailInfo: {
          messageId: info.messageId,
          response: info.response,
          envelope: info.envelope,
          accepted: info.accepted,
          rejected: info.rejected
        }
      });
    } catch (sendError) {
      console.error("Error sending email:", sendError);
      console.error("Error stack:", sendError.stack);
      if (sendError.response) console.error("SMTP Response:", sendError.response);
      
      return res.status(500).json({
        success: false,
        message: "Failed to send welcome email",
        error: sendError.message,
        stack: sendError.stack
      });
    }
  } catch (error) {
    console.error("Error in direct welcome email test:", error);
    console.error("Stack trace:", error.stack);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add a direct verification test route
router.get("/test-verification/:email", async (req, res) => {
  try {
    const { email } = req.params;
    
    // Find the user
    const user = await User.findOne({ email });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found with provided email"
      });
    }
    

    
    // Record initial state
    const wasVerified = user.isVerified;
    
    // Set user to verified
    user.isVerified = true;
    
    if (user.verificationToken) {
      user.verificationToken = undefined;
    }
    
    // Save the user to trigger hooks
    await user.save();
    
    
    return res.json({
      success: true,
      message: `User ${email} has been directly verified`,
      wasVerified: wasVerified,
      isVerified: user.isVerified,
      welcomeEmailTriggered: !wasVerified && user.isVerified
    });
  } catch (error) {
    console.error("Error in direct verification test:", error);
    console.error("Stack trace:", error.stack);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Add CSRF token endpoint for testing
router.get('/csrf-token', csrfProtection, (req, res) => {
  res.json({ csrfToken: req.csrfToken() });
});

// Test endpoint to generate JWT token (only for testing)
router.get('/test-jwt', (req, res) => {
  try {
    // Create a test user object
    const testUser = {
      _id: '123456789',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    };
    
    // Generate JWT
    const token = generateToken(testUser);
    
    // Set JWT in cookie
    res.cookie('jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', 
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    // Return token
    res.json({ 
      success: true, 
      message: 'Test JWT generated',
      token 
    });
  } catch (error) {
    console.error('Test JWT Generation Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to verify JWT token (only for testing)
router.get('/verify-jwt', (req, res) => {
  try {
    
    // Extract JWT from request
    const token = req.cookies.jwt || 
                 (req.headers.authorization && req.headers.authorization.startsWith('Bearer ') 
                  ? req.headers.authorization.substring(7) : null);
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'No token provided',
        auth: {
          session: req.isAuthenticated(),
          jwt: false,
          sessionID: req.sessionID
        }
      });
    }
    
    // Verify JWT
    const decoded = require('../utils/jwtUtils').verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token',
        auth: {
          session: req.isAuthenticated(),
          jwt: false,
          sessionID: req.sessionID
        }
      });
    }
    
    // Return decoded token
    res.json({
      success: true,
      message: 'Token verified',
      user: decoded,
      auth: {
        session: req.isAuthenticated(),
        jwt: true,
        sessionID: req.sessionID
      }
    });
  } catch (error) {
    console.error('Test JWT Verification Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Public test routes (no authentication required)
router.get('/public-test-jwt', (req, res) => {
  try {
    // Create a test user object
    const testUser = {
      _id: '123456789',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    };
    
    // Generate JWT
    const token = generateToken(testUser);
    
    // Set JWT in cookie
    res.cookie('jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', 
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    // Return token
    res.json({ 
      success: true, 
      message: 'Test JWT generated',
      token 
    });
  } catch (error) {
    console.error('Test JWT Generation Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to verify JWT token (for testing only)
router.get('/public-verify-jwt', (req, res) => {
  try {
    
    // Extract JWT from request
    const token = req.cookies.jwt || 
                 (req.headers.authorization && req.headers.authorization.startsWith('Bearer ') 
                  ? req.headers.authorization.substring(7) : null);
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'No token provided',
        auth: {
          session: req.isAuthenticated?.(),
          jwt: false,
          sessionID: req.sessionID
        }
      });
    }
    
    // Verify JWT
    const decoded = require('../utils/jwtUtils').verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token',
        auth: {
          session: req.isAuthenticated?.(),
          jwt: false,
          sessionID: req.sessionID
        }
      });
    }
    
    // Return decoded token
    res.json({
      success: true,
      message: 'Token verified',
      user: decoded,
      auth: {
        session: req.isAuthenticated?.(),
        jwt: true,
        sessionID: req.sessionID
      }
    });
  } catch (error) {
    console.error('Test JWT Verification Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to check authentication status (for testing only)
router.get('/public-auth-status', (req, res) => {
  try {
    // Extract JWT from request
    const token = req.cookies.jwt || 
                 (req.headers.authorization && req.headers.authorization.startsWith('Bearer ') 
                  ? req.headers.authorization.substring(7) : null);
    
    // Verify JWT if present
    let jwtUser = null;
    if (token) {
      jwtUser = require('../utils/jwtUtils').verifyToken(token);
    }
    
    // Return auth status
    res.json({
      success: true,
      auth: {
        session: {
          isAuthenticated: req.isAuthenticated?.(),
          sessionID: req.sessionID,
          user: req.user ? {
            id: req.user._id,
            username: req.user.username,
            email: req.user.email,
            role: req.user.role
          } : null
        },
        jwt: {
          hasToken: !!token,
          isValid: !!jwtUser,
          user: jwtUser
        }
      }
    });
  } catch (error) {
    console.error('Auth Status Check Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
