const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const User = require("../models/user");
const Role = require("../models/role");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const bcrypt = require("bcrypt");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const debug = require("debug")("app:selfService");

// Debug middleware
router.use((req, res, next) => {
  debug("\n=== Self Service Route Handler ===");
  debug("Full URL:", req.originalUrl);
  debug("Base URL:", req.baseUrl);
  debug("Path:", req.path);
  debug("Method:", req.method);
  debug("Query:", req.query);
  debug("Params:", req.params);
  debug("Body:", req.body);
  debug("Headers:", req.headers);
  next();
});

// Setup route
router.get("/setup/:token", csrfProtection, async (req, res) => {
  debug("\n=== Processing Setup Request ===");
  debug("Token:", req.params.token);

  try {
    const { token } = req.params;
    debug("Looking for employee with token");

    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: new Date() },
    });

    debug(
      "Employee lookup result:",
      employee
        ? {
            id: employee._id,
            email: employee.email,
            tokenExpiration: employee.selfServiceTokenExpiration,
          }
        : "Not found"
    );

    if (!employee) {
      debug("Invalid or expired token");
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/auth/login");
    }

    debug("Rendering setup form");
    res.render("self-service-setup", {
      csrfToken: req.csrfToken(),
      token,
      email: employee.email,
      messages: req.flash(),
    });
  } catch (error) {
    debug("Error in setup route:", error);
    console.error("Error in self-service setup route:", error);
    req.flash("error", "An error occurred during setup");
    res.redirect("/auth/login");
  }
});

// Handle setup form submission
router.post("/setup/:token", csrfProtection, async (req, res) => {
  try {
    console.log("Processing setup submission for token:", req.params.token);
    const { token } = req.params;
    const { password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect(`/self-service/setup/${token}`);
    }

    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: new Date() },
    });

    if (!employee) {
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/auth/login");
    }

    const user = await User.findById(employee.user);
    if (!user) {
      req.flash("error", "User account not found");
      return res.redirect("/auth/login");
    }

    // Update password and verify user
    const hashedPassword = await bcrypt.hash(password, 12);
    user.password = hashedPassword;
    user.isVerified = true;
    await user.save();

    // Clear setup token
    employee.selfServiceToken = undefined;
    employee.selfServiceTokenExpiration = undefined;
    await employee.save();

    req.flash(
      "success",
      "Account setup complete. Please login with your new password."
    );
    res.redirect("/auth/login");
  } catch (error) {
    console.error("Error in setup completion:", error);
    req.flash("error", "An error occurred during setup");
    res.redirect("/auth/login");
  }
});

module.exports = router;
