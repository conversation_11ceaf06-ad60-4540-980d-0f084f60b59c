const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const Payroll = require("../models/Payroll");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const checkCompany = require("../middleware/checkCompany");
const mongoose = require("mongoose");

router.post("/", ensureAuthenticated, async (req, res) => {
  const maxRetries = 5;
  let retries = 0;

  while (retries < maxRetries) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {

      if (!req.user || !req.user.currentCompany) {
        throw new Error("Current company not set for user");
      }

      const employeeData = req.body;
      employeeData.company = req.user.currentCompany;

      let settings = await EmployeeNumberSettings.findOne({
        company: req.user.currentCompany,
      }).session(session);

      if (!settings) {
        settings = new EmployeeNumberSettings({
          company: req.user.currentCompany,
          mode: "automatic",
          firstEmployeeNumber: "0001",
          currentNumber: 0,
        });
      }

      const company = await Company.findById(req.user.currentCompany).session(
        session
      );
      if (!company) {
        throw new Error("Company not found");
      }

      settings.currentNumber += 1;
      await settings.save({ session });

      const companyEmployeeNumber = settings.currentNumber
        .toString()
        .padStart(4, "0");
      const globalEmployeeId = `${company.companyCode}-${companyEmployeeNumber}`;

      employeeData.companyEmployeeNumber = companyEmployeeNumber;
      employeeData.globalEmployeeId = globalEmployeeId;

      if (!employeeData.email || employeeData.email.trim() === "") {
        delete employeeData.email;
      }


      const newEmployee = new Employee(employeeData);
      await newEmployee.save({ session });

      await Company.findByIdAndUpdate(req.user.currentCompany, {
        $push: { employees: newEmployee._id },
      }).session(session);

      await session.commitTransaction();
      session.endSession();


      return res.redirect(`/employeeProfile/${newEmployee._id}`);
    } catch (error) {
      await session.abortTransaction();
      session.endSession();

      if (error.code === 112 && retries < maxRetries - 1) {
        console.log(`Retrying operation (attempt ${retries + 1})`);
        retries++;
        continue;
      }

      console.error("Error adding employee:", error);
      req.flash("error", `Failed to add employee: ${error.message}`);
      return res.redirect("/employeeManagement");
    }
  }

  console.error("Max retries reached. Failed to add employee.");
  req.flash(
    "error",
    "Failed to add employee after multiple attempts. Please try again."
  );
  return res.redirect("/employeeManagement");
});

module.exports = router;
