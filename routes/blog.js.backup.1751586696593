const express = require("express");
const router = express.Router();
const BlogPost = require("../models/blogPost");
const User = require("../models/user");
const { ensureAuthenticated, ensureAdmin } = require("../config/auth");
const { createNotification } = require("../utils/notificationUtils");

// Get all blog posts
router.get("/", ensureAuthenticated, async (req, res) => {
  try {
    const blogPosts = await BlogPost.find().populate(
      "author",
      "firstName lastName"
    );
    res.render("blog/index", { blogPosts });
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Create new blog post form (admin only)
router.get("/create", ensureAuthenticated, ensureAdmin, (req, res) => {
  res.render("blog/create");
});

// Submit new blog post (admin only)
router.post("/create", ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { title, content } = req.body;
    const newPost = new BlogPost({
      title,
      content,
      author: req.user._id,
    });
    await newPost.save();

    // Create notifications for all users
    const users = await User.find();
    for (const user of users) {
      await createNotification(
        user._id,
        `New blog post: "${title}" has been published!`,
        "Blog Post"
      );
    }

    res.redirect("/blog");
  } catch (error) {
    console.error("Error creating blog post:", error);
    res.status(500).send("Internal Server Error");
  }
});

// View single blog post
router.get("/:id", async (req, res) => {
  try {
    const blogPost = await BlogPost.findById(req.params.id).populate(
      "author",
      "firstName lastName"
    );
    if (!blogPost) {
      return res.status(404).send("Blog post not found");
    }
    res.render("blog/view", { blogPost });
  } catch (error) {
    console.error("Error fetching blog post:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Create notification for all users
router.post("/api/create-notification", async (req, res) => {
  try {
    const { title, content } = req.body;
    // Create notifications for all users (you'll need to implement user management or fetch users from the main app)
    const users = await User.find();
    for (const user of users) {
      await createNotification(
        user._id,
        `New system update: "${title}"`,
        "System Update"
      );
    }
    res.status(200).json({ message: "Notification created successfully" });
  } catch (error) {
    console.error("Error creating notification:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

module.exports = router;
