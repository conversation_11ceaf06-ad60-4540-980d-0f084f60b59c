const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayFrequency = require("../models/PayFrequency");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/Company");
const checkCompany = require("../middleware/checkCompany");
const {
  ensureAuthenticated,
  ensureOwner,
} = require("../config/ensureAuthenticated");
const PayrollPeriod = require("../models/PayrollPeriod");
const beneficiaries = require("../models/beneficiaries");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const RFIConfig = require("../models/rfiConfig");
const mongoose = require("mongoose");
const fs = require("fs");
const moment = require("moment-timezone");
const dateUtils = require("../utils/dateUtils");
const transporter = require("../config/emailConfig");
const crypto = require("crypto");
const EmployeeCredential = require("../models/employeeCredential");
const ejs = require("ejs");
const path = require("path");
const PayrollService = require("../services/PayrollService");
const RFICalculationService = require("../services/rfiCalculationService");
const payrollCalculations = require("../utils/payrollCalculations");
const EmployeeNumberService = require("../services/employeeNumberService");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const Role = require("../models/role");
const bcrypt = require("bcrypt");
const EmployeeTemplateGenerator = require("../utils/excel-templates");
const XLSX = require("xlsx");
const PayComponent = require("../models/payComponent");
const AuditLog = require("../models/auditLog");
const PensionFundBeneficiary = require("../models/pensionFundBeneficiary");
const Beneficiary = require("../models/beneficiaries");
const WorkedHours = require("../models/WorkedHours");
const { calculatePeriodTotals } = require("../services/PayrollService");
const {
  formatDateForDisplay,
  isProratedMonth,
  getEndOfMonthDate,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
  getCurrentOrNextLastDayOfMonth,
  getSundaysInPeriod,
} = dateUtils;
const { isValidObjectId } = mongoose;
const ExcelJS = require("exceljs");
const multer = require("multer");
const xlsx = require('xlsx');
const HourlyRate = require("../models/HourlyRate");
const Busboy = require('busboy');

// Define default timezone for the application
const DEFAULT_TIMEZONE = "Africa/Johannesburg"; // South African timezone

// Import the upload router
const uploadRouter = require('./upload');

// Debug middleware for multipart requests
const debugMultipart = (req, res, next) => {
  console.log('\n=== Multipart Debug ===');
  console.log('Request URL:', req.url);
  console.log('Content-Type:', req.headers['content-type']);
  console.log('Content-Length:', req.headers['content-length']);
  console.log('Boundary:', req.headers['content-type']?.split('boundary=')[1]);
  
  // Track request data chunks
  let dataSize = 0;
  let chunks = [];
  
  // Capture raw request data
  req.on('data', chunk => {
    dataSize += chunk.length;
    chunks.push(chunk);
    console.log('Received chunk size:', chunk.length, 'Total size:', dataSize);
  });

  req.on('end', () => {
    console.log('Request data complete. Total size:', dataSize);
    if (dataSize !== parseInt(req.headers['content-length'])) {
      console.error('Size mismatch! Expected:', req.headers['content-length'], 'Received:', dataSize);
    }
  });

  req.on('error', error => {
    console.error('Request stream error:', error);
  });

  next();
};

// Configure multer with detailed logging
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    console.log('\n=== Multer Storage: Destination ===');
    console.log('File field name:', file.fieldname);
    console.log('Original name:', file.originalname);
    
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      console.log('Creating upload directory:', uploadDir);
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    console.log('Upload directory:', uploadDir);
    console.log('File details:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype
    });
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    console.log('\n=== Multer Storage: Filename ===');
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const filename = uniqueSuffix + '-' + file.originalname;
    console.log('Generated filename:', filename);
    cb(null, filename);
  }
});

// Configure busboy options
const busboyOptions = {
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    fields: 5, // Allow up to 5 non-file fields
    files: 1, // Allow only 1 file
    parts: 6, // fields + files + potential metadata
  },
  preservePath: false
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    fieldSize: 10 * 1024 * 1024 // 10MB for field size
  },
  fileFilter: function (req, file, cb) {
    console.log('\n=== Multer File Filter ===');
    console.log('Incoming file:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype
    });

    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    if (!allowedTypes.includes(file.mimetype)) {
      console.error('Invalid mime type:', file.mimetype);
      return cb(new Error('Invalid file type'));
    }

    if (!file.originalname.match(/\.(xlsx|xls)$/)) {
      console.error('Invalid file extension:', file.originalname);
      return cb(new Error('Invalid file extension'));
    }

    console.log('File validation passed');
    cb(null, true);
  }
}).single('file');

// Add debug middleware
router.use((req, res, next) => {
  console.log('=== Employee Management Route Debug ===');
  console.log('Request URL:', req.originalUrl);
  console.log('Request Method:', req.method);
  console.log('Content Type:', req.get('content-type'));
  console.log('Content Length:', req.get('content-length'));
  next();
});

// Add debug middleware for request tracking
router.use((req, res, next) => {
  console.log('\n=== Employee Management Request Debug ===');
  console.log('Full URL:', req.originalUrl);
  console.log('Base URL:', req.baseUrl);
  console.log('Path:', req.path);
  console.log('Query:', req.query);
  console.log('Params:', req.params);
  console.log('Method:', req.method);
  console.log('Content Type:', req.get('content-type'));
  console.log('Content Length:', req.get('content-length'));
  next();
});

// Debug logging middleware
router.use((req, res, next) => {
  const requestId = uuidv4();
  req.requestId = requestId;
  const startTime = Date.now();
  
  // Log request details
  console.log(`[${requestId}] 🚀 Request started: ${req.method} ${req.originalUrl}`);
  console.log(`[${requestId}] Headers:`, JSON.stringify(req.headers, null, 2));
  if (req.body && Object.keys(req.body).length > 0) {
    const sanitizedBody = { ...req.body };
    // Remove sensitive data
    delete sanitizedBody.password;
    delete sanitizedBody.token;
    console.log(`[${requestId}] Body:`, JSON.stringify(sanitizedBody, null, 2));
  }
  
  // Track response
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`[${requestId}] ✅ Request completed in ${duration}ms with status ${res.statusCode}`);
  });

  // Track errors
  res.on('error', (error) => {
    console.error(`[${requestId}] ❌ Response error:`, error);
  });
  
  next();
});

// Global error handler middleware
const handleError = (error, req, res, next) => {
  const errorId = uuidv4();
  console.error(`[${req.requestId || 'NO_REQ_ID'}] [ErrorID: ${errorId}] ❌ Error:`, {
    message: error.message,
    stack: error.stack,
    code: error.code,
    path: req.path,
    method: req.method,
    params: req.params,
    query: req.query,
    user: req.user ? { id: req.user.id, email: req.user.email } : null
  });
  
  // Send appropriate error response
  res.status(error.status || 500).json({
    error: error.message,
    errorId,
    requestId: req.requestId
  });
};

// Database operation logging middleware
const logDatabaseOperation = (operation) => async (req, res, next) => {
  console.log(`[${req.requestId}] 📝 Database operation: ${operation}`, {
    user: req.user ? { id: req.user.id, email: req.user.email } : null,
    params: req.params,
    method: req.method
  });
  next();
};

// Transaction logging middleware
const logTransaction = (operation) => async (req, res, next) => {
  console.log(`[${req.requestId}] 🔄 Transaction started: ${operation}`, {
    user: req.user ? { id: req.user.id, email: req.user.email } : null,
    params: req.params
  });
  next();
};

// Add this near the top of the file with other middleware imports
const checkAuth = (req, res, next) => {
  if (!req.isAuthenticated()) {
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }
  next();
};

function isNewEmployee(doa, currentMonth) {
  const doaMoment = moment(doa, "YYYY-MM-DD");
  const currentMoment = moment(currentMonth, "YYYY-MM-DD");

  if (doaMoment.isValid() && currentMoment.isValid()) {
    return doaMoment.isSame(currentMoment, "month");
  }

  console.error("Invalid date format:", { doa, currentMonth });
  return false;
}

// Add age calculation utility function
const calculateAge = (dob) => {
  if (!dob) return null;
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// Add this middleware to check if the user is authenticated
router.use(ensureAuthenticated);

// Apply the middleware to all routes in this file
router.use(ensureAuthenticated);
router.use(checkCompany);

// Add this new route for /employeeManagement
router.get(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Session ID:", req.sessionID);

    try {
      // Assuming the user has a currentCompany field
      const company = await Company.findById(req.user.currentCompany);

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the current company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

// Keep the existing route for /:companyIdentifier/employeeManagement
router.get(
  "/:companyIdentifier/employeeManagement",
  ensureAuthenticated,
  ensureOwner,
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Company identifier:", req.params.companyIdentifier);
    console.log("Session ID:", req.sessionID);

    try {
      const companyIdentifier = req.params.companyIdentifier;
      let company;

      if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
        company = await Company.findById(companyIdentifier);
      } else {
        company = await Company.findOne({ companyCode: companyIdentifier });
      }

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the selected company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

router.post("/setActiveTab", ensureAuthenticated, (req, res) => {
  req.session.activeTab = req.body.tab;
  res.sendStatus(200);
});

// Update the API route to fetch employees for the specific company
router.get(
  "/:companyCode/employeeManagement/api/employees",
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status"
      );
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      res
        .status(500)
        .json({ error: "An error occurred while fetching employees" });
    }
  }
);

// Add this new route to handle the update request for take-on tax totals
router.post("/update-take-on-tax-totals", async (req, res) => {
  try {
    const { employees } = req.body;
    console.log("Received employees data:", employees);
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {
        const updatedEmployee = await Employee.findByIdAndUpdate(
          employeeData._id,
          { $set: employeeData },
          { new: true }
        );

        if (updatedEmployee) {
          results.updated++;
        } else {
          results.errors.push({
            id: employeeData._id,
            error: "Employee not found",
          });
        }
      } catch (error) {
        results.errors.push({
          id: employeeData._id,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Add this new route to handle the update request for employees
router.post("/update-essentials", async (req, res) => {
  try {
    const { employees } = req.body;
    console.log("Received employees data:", employees);
    const results = { updated: 0, created: 0, errors: [] };
    const settings = await EmployeeNumberSettings.findOne();

    for (const employeeData of employees) {
      try {
        console.log("Processing employee data:", employeeData);

        // Handle date fields
        ["dob", "doa"].forEach((dateField) => {
          if (employeeData[dateField]) {
            employeeData[dateField] = moment
              .utc(employeeData[dateField])
              .startOf("day")
              .toDate();
          } else {
            employeeData[dateField] = null;
          }
        });

        console.log("Processed date fields:", employeeData);

        // Set email to null if it's blank
        if (employeeData.email === "") {
          employeeData.email = null;
        }

        if (settings && settings.mode === "automatic") {
          try {
            employeeData.employeeNumber = await settings.generateNextNumber();
          } catch (error) {
            console.error("Error generating employee number:", error);
            results.errors.push(
              `Error generating employee number for ${employeeData.email}: ${error.message}`
            );
            continue;
          }
        }

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          // If there's no _id, create a new employee
          const newEmployee = new Employee(employeeData);
          await newEmployee.save();
          results.created++;
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Download Excel Template Route
router.get(
  "/:companyCode/employeeManagement/download-template",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Check user permissions
      if (!req.user.companies.includes(company._id)) {
        req.flash(
          "error",
          "You don't have permission to access this company's data"
        );
        return res.redirect("/clients");
      }

      const generator = new EmployeeTemplateGenerator();
      const workbook = await generator.generateTemplate();

      // Set security headers
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader(
        "Cache-Control",
        "no-store, no-cache, must-revalidate, private"
      );

      // Set download headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=employee-import-template-${companyCode}-${Date.now()}.xlsx`
      );

      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error("Error generating template:", error);
      req.flash("error", "Failed to generate template");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Bulk upload preview route
router.post('/:companyCode/employeeManagement/preview-upload',
  ensureAuthenticated,
  csrfProtection,
  (req, res, next) => {
    const fileUpload = multer({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
        files: 1
      },
      fileFilter: function (req, file, cb) {
        console.log('\n=== Multer File Filter ===');
        console.log('Incoming file:', {
          fieldname: file.fieldname,
          originalname: file.originalname,
          encoding: file.encoding,
          mimetype: file.mimetype
        });

        const filetypes = /xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
          return cb(null, true);
        }
        cb(new Error('Only Excel files (.xlsx, .xls) are allowed'));
      }
    }).single('file');

    fileUpload(req, res, async function(err) {
      if (err instanceof multer.MulterError) {
        console.error("Multer error:", err);
        return res.status(400).json({
          success: false,
          message: `File upload error: ${err.message}`
        });
      } else if (err) {
        console.error("Upload error:", err);
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      next();
    });
  },
  async (req, res) => {
    console.log('=== [1] Upload Request Received ===');
    console.log('Headers:', req.headers);
    console.log('File:', req.file);
    
    try {
      const { companyCode } = req.params;
      
      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log('=== [Error] Company Not Found ===');
        return res.status(404).json({
          success: false,
          error: 'Company not found'
        });
      }

      // Validate file presence
      if (!req.file) {
        console.log('=== [Error] No File Uploaded ===');
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      // Log file details
      console.log('=== [2] File Details ===', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        buffer: req.file.buffer ? 'Present' : 'Missing'
      });

      // Validate file type
      if (!req.file.originalname.match(/\.(xlsx|xls)$/)) {
        console.log('=== [Error] Invalid File Type ===');
        return res.status(400).json({
          success: false,
          error: 'Please upload an Excel file (.xlsx or .xls)'
        });
      }

      // Process Excel file
      const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
      const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
      
      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(firstSheet, { 
        header: 1,
        raw: false,
        dateNF: 'yyyy-mm-dd'
      });

      // Validate data
      if (!jsonData || jsonData.length < 2) {
        console.log('=== [Error] Invalid Excel Data ===');
        return res.status(400).json({
          success: false,
          error: 'Excel file is empty or invalid'
        });
      }

      // Extract headers and data
      const headers = jsonData[0];
      const previewRows = jsonData.slice(1, 6); // Show first 5 rows

      console.log('=== [3] Preview Data Generated ===', {
        totalRows: jsonData.length - 1,
        headers,
        previewRowCount: previewRows.length
      });

      // Send success response
      res.json({
        success: true,
        headers,
        preview: previewRows.map(row => {
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = row[index] || '';
          });
          return rowData;
        }),
        totalRows: jsonData.length - 1
      });

    } catch (error) {
      console.error('=== [Error] File Processing Error ===', {
        message: error.message,
        stack: error.stack
      });
      
      res.status(500).json({
        success: false,
        error: 'Failed to process file: ' + error.message
      });
    }
});

// Confirm upload endpoint
router.post(
  "/:companyCode/employeeManagement/confirm-upload",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employeeRecords = req.body;

      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found",
        });
      }

      const results = {
        successful: 0,
        failed: 0,
        successList: [],
        errorList: [],
      };

      for (const record of employeeRecords) {
        try {
          // Generate employee number
          const employeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);

          // Get current pay period
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth();
          const currentYear = currentDate.getFullYear();
          const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);

          // Create employee object
          const employee = new Employee({
            globalEmployeeId: uuidv4(),
            companyEmployeeNumber: employeeNumber,
            firstName: record.firstName,
            lastName: record.lastName,
            idNumber: record.idNumber,
            personalDetails: {
              idType: record.idType,
            },
            dob: moment(record.dob).toDate(),
            doa: moment(record.doa).toDate(),
            company: company._id,
            firstPayrollPeriodEndDate: lastDayOfMonth,
            lastDayOfPeriod: lastDayOfMonth,
            bank: record.paymentMethod === "EFT" ? record.bank : undefined,
            accountType:
              record.paymentMethod === "EFT" ? record.accountType : undefined,
            accountNumber:
              record.paymentMethod === "EFT" ? record.accountNumber : undefined,
            branchCode:
              record.paymentMethod === "EFT" ? record.branchCode : undefined,
            holderRelationship:
              record.paymentMethod === "EFT" ? "self" : undefined,
          });

          await employee.save();
          results.successful++;
          results.successList.push({
            row: record.row,
            name: `${record.firstName} ${record.lastName}`,
            employeeNumber,
          });
        } catch (error) {
          results.failed++;
          results.errorList.push({
            row: record.row,
            error: error.message,
          });
        }
      }

      res.json(results);
    } catch (error) {
      console.error("Upload error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error during upload",
      });
    }
  }
);

// Handle file upload
router.post(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  (req, res) => {
    console.log("\n=== Bulk Employee Upload Request ===");
    console.log("Request headers:", req.headers);
    
    const fileUpload = multer({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
        files: 1
      },
      fileFilter: function (req, file, cb) {
        console.log('\n=== Multer File Filter ===');
        console.log('Incoming file:', {
          fieldname: file.fieldname,
          originalname: file.originalname,
          encoding: file.encoding,
          mimetype: file.mimetype
        });

        const filetypes = /xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
          return cb(null, true);
        }
        cb(new Error('Only Excel files (.xlsx, .xls) are allowed'));
      }
    }).single('file');

    fileUpload(req, res, async function(err) {
      if (err instanceof multer.MulterError) {
        console.error("Multer error:", err);
        return res.status(400).json({
          success: false,
          message: `File upload error: ${err.message}`
        });
      } else if (err) {
        console.error("Upload error:", err);
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      
      try {
        const { companyCode } = req.params;
        console.log("Company Code:", companyCode);

        // Validate company
        const company = await Company.findOne({ companyCode });
        if (!company) {
          console.error("Company not found:", companyCode);
          return res.status(404).json({
            success: false,
            message: "Company not found"
          });
        }

        // Check if file was uploaded
        if (!req.file) {
          console.error("No file uploaded");
          return res.status(400).json({
            success: false,
            message: "No file uploaded"
          });
        }

        console.log('File upload details:', {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size
        });

        try {
          // Read Excel file from buffer
          console.log('=== Excel Validation Start ===');
          const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
          
          if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('Invalid Excel file structure');
          }

          console.log('Workbook structure:', {
            sheetNames: workbook.SheetNames,
            numberOfSheets: workbook.SheetNames.length
          });

          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          if (!firstSheet || !firstSheet['!ref']) {
            throw new Error('Excel file is empty or corrupted');
          }

          // Convert to JSON
          const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });
          
          if (rows.length < 2) {
            throw new Error('Excel file must contain headers and at least one data row');
          }

          // Validate headers
          const headers = rows[0].map(h => h?.toString().trim() || '');
          const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
          const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

          if (missingHeaders.length > 0) {
            throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
          }

          console.log('Excel validation successful');
          console.log('Found headers:', headers);
          console.log('Number of data rows:', rows.length - 1);

          res.json({
            success: true,
            message: 'File uploaded and validated successfully',
            file: {
              name: req.file.originalname,
              size: req.file.size,
              rows: rows.length - 1 // Exclude header row
            }
          });

        } catch (error) {
          console.error('Excel validation error:', error);
          return res.status(400).json({
            success: false,
            message: error.message || 'Invalid Excel file format'
          });
        }

      } catch (error) {
        console.error("Error in bulk employee upload:", error);
        res.status(error.status || 400).json({
          success: false,
          message: error.message || 'Error uploading file'
        });
      }
    });
  }
);

// Mount upload router with proper prefix
router.use('/:companyCode/employeeManagement', (req, res, next) => {
  console.log('\n=== Upload Router Mount Debug ===');
  console.log('Original URL:', req.originalUrl);
  console.log('Base URL:', req.baseUrl);
  console.log('Path:', req.path);
  next();
}, uploadRouter);

// Update payment and banking information route
router.post(
  "/:companyCode/employeeManagement/update-payment-banking-info",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employees } = req.body;

      console.log("Processing payment & banking info update request:", {
        companyCode,
        employeeCount: employees?.length || 0
      });

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      // Validate request body
      if (!Array.isArray(employees)) {
        return res.status(400).json({
          success: false,
          message: "Invalid request format: employees must be an array"
        });
      }

      const results = { updated: 0, errors: [] };

      for (const employeeData of employees) {
        try {
          // Validate required fields
          if (!employeeData._id) {
            results.errors.push("Employee ID not provided");
            continue;
          }

          // Validate banking info fields
          const requiredFields = ['bank', 'accountNumber', 'accountType', 'branchCode', 'holderRelationship'];
          const missingFields = requiredFields.filter(field => !employeeData[field]);

          if (missingFields.length > 0) {
            results.errors.push(
              `Missing required banking fields for employee ${employeeData._id}: ${missingFields.join(', ')}`
            );
            continue;
          }

          // Extract only banking-related fields
          const updateData = {
            bank: employeeData.bank,
            accountNumber: employeeData.accountNumber,
            accountType: employeeData.accountType,
            branchCode: employeeData.branchCode,
            holderRelationship: employeeData.holderRelationship,
            accountHolder: employeeData.accountHolder
          };

          // Update employee with company validation
          const updatedEmployee = await Employee.findOneAndUpdate(
            { 
              _id: employeeData._id,
              company: company._id // Ensure employee belongs to the company
            },
            { $set: updateData },
            { 
              new: true,
              runValidators: true
            }
          );

          if (updatedEmployee) {
            results.updated++;
            console.log("Successfully updated employee banking info:", {
              id: updatedEmployee._id,
              bank: updateData.bank,
              accountType: updateData.accountType
            });
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found in company ${companyCode}`
            );
          }
        } catch (error) {
          console.error("Error processing employee banking update:", {
            employeeId: employeeData._id,
            error: error.message
          });
          
          results.errors.push(
            `Error updating employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      // Send response
      res.json({
        success: results.updated > 0,
        message: `${results.updated} employee(s) updated successfully`,
        results: results
      });
    } catch (error) {
      console.error("Error in bulk banking info update:", error);
      res.status(500).json({
        success: false,
        message: "Error processing updates",
        error: error.message
      });
    }
  }
);

// Update residential address route
router.post(
  "/:companyCode/employeeManagement/update-residential-address",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employees } = req.body;

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      console.log("Processing residential address updates:", {
        companyCode,
        employeeCount: employees?.length || 0
      });

      const results = { updated: 0, errors: [] };

      // Validate employees array
      if (!Array.isArray(employees)) {
        return res.status(400).json({
          success: false,
          message: "Invalid request format: employees must be an array"
        });
      }

      for (const employeeData of employees) {
        try {
          // Validate required fields
          if (!employeeData._id) {
            results.errors.push("Employee ID not provided");
            continue;
          }

          // Validate residential address data
          if (!employeeData.residentialAddress || typeof employeeData.residentialAddress !== 'object') {
            results.errors.push(`Invalid residential address data for employee ${employeeData._id}`);
            continue;
          }

          // Extract only residential address fields
          const updateData = {
            residentialAddress: {
              streetAddress: employeeData.residentialAddress.streetAddress,
              suburb: employeeData.residentialAddress.suburb,
              city: employeeData.residentialAddress.city,
              province: employeeData.residentialAddress.province,
              postalCode: employeeData.residentialAddress.postalCode,
              country: employeeData.residentialAddress.country || 'South Africa'
            }
          };

          // Validate required address fields
          const requiredFields = ['streetAddress', 'suburb', 'city', 'postalCode'];
          const missingFields = requiredFields.filter(field => 
            !updateData.residentialAddress[field]
          );

          if (missingFields.length > 0) {
            results.errors.push(
              `Missing required address fields for employee ${employeeData._id}: ${missingFields.join(', ')}`
            );
            continue;
          }

          // Update employee with company validation
          const updatedEmployee = await Employee.findOneAndUpdate(
            { 
              _id: employeeData._id,
              company: company._id // Ensure employee belongs to the company
            },
            { $set: updateData },
            { 
              new: true,
              runValidators: true
            }
          );

          if (updatedEmployee) {
            results.updated++;
            console.log("Successfully updated employee address:", {
              id: updatedEmployee._id,
              address: updateData.residentialAddress
            });
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found in company ${companyCode}`
            );
          }
        } catch (error) {
          console.error("Error processing employee address update:", {
            employeeId: employeeData._id,
            error: error.message
          });
          
          results.errors.push(
            `Error updating employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      // Send response
      res.json({
        success: results.updated > 0,
        message: `${results.updated} employee(s) updated successfully`,
        results: results
      });
    } catch (error) {
      console.error("Error in bulk address update:", error);
      res.status(500).json({
        success: false,
        message: "Error processing updates",
        error: error.message
      });
    }
  }
);

// Add the bulk add employees route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulkAddEmployees", {
        company,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash("error", "Failed to load bulk add employees page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Configure multer for temporary storage
const fileUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: function (req, file, cb) {
    const filetypes = /xlsx|xls/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only Excel files (.xlsx, .xls) are allowed'));
  }
}).single('file'); // Integrate single file upload here

// Get bulk add employees page
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      console.log("=== Bulk Add Employees Page Request ===");
      console.log("Company Code:", companyCode);

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        return res.status(404).render('error', {
          message: 'Company not found',
          error: { status: 404 }
        });
      }

      console.log("Company found:", company.name);
      console.log("Rendering bulk-add-employees page");

      // Render the bulk add employees page
      res.render('bulkAddEmployees', {
        title: 'Bulk Add Employees',
        company,
        companyCode,
        csrfToken: req.csrfToken(),
        user: req.user,
        currentPage: 'employeeManagement'
      });
    } catch (error) {
      console.error("Error rendering bulk add employees page:", error);
      res.status(500).render('error', {
        message: 'Error loading page',
        error: { status: 500 }
      });
    }
  }
);

// Handle file upload
router.post(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  (req, res) => {
    console.log("\n=== Bulk Employee Upload Request ===");
    console.log("Request headers:", req.headers);
    
    const fileUpload = multer({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
        files: 1
      },
      fileFilter: function (req, file, cb) {
        const filetypes = /xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
          return cb(null, true);
        }
        cb(new Error('Only Excel files (.xlsx, .xls) are allowed'));
      }
    }).single('file');

    fileUpload(req, res, async function(err) {
      if (err instanceof multer.MulterError) {
        console.error("Multer error:", err);
        return res.status(400).json({
          success: false,
          message: `File upload error: ${err.message}`
        });
      } else if (err) {
        console.error("Upload error:", err);
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      
      try {
        const { companyCode } = req.params;
        console.log("Company Code:", companyCode);

        // Validate company
        const company = await Company.findOne({ companyCode });
        if (!company) {
          console.error("Company not found:", companyCode);
          return res.status(404).json({
            success: false,
            message: "Company not found"
          });
        }

        // Check if file was uploaded
        if (!req.file) {
          console.error("No file uploaded");
          return res.status(400).json({
            success: false,
            message: "No file uploaded"
          });
        }

        console.log('File upload details:', {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size
        });

        try {
          // Read Excel file from buffer
          console.log('=== Excel Validation Start ===');
          const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
          
          if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('Invalid Excel file structure');
          }

          console.log('Workbook structure:', {
            sheetNames: workbook.SheetNames,
            numberOfSheets: workbook.SheetNames.length
          });

          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          if (!firstSheet || !firstSheet['!ref']) {
            throw new Error('Excel file is empty or corrupted');
          }

          // Convert to JSON
          const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });
          
          if (rows.length < 2) {
            throw new Error('Excel file must contain headers and at least one data row');
          }

          // Validate headers
          const headers = rows[0].map(h => h?.toString().trim() || '');
          const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
          const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

          if (missingHeaders.length > 0) {
            throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
          }

          console.log('Excel validation successful');
          console.log('Found headers:', headers);
          console.log('Number of data rows:', rows.length - 1);

          res.json({
            success: true,
            message: 'File uploaded and validated successfully',
            file: {
              name: req.file.originalname,
              size: req.file.size,
              rows: rows.length - 1 // Exclude header row
            }
          });

        } catch (error) {
          console.error('Excel validation error:', error);
          return res.status(400).json({
            success: false,
            message: error.message || 'Invalid Excel file format'
          });
        }

      } catch (error) {
        console.error("Error in bulk employee upload:", error);
        res.status(error.status || 400).json({
          success: false,
          message: error.message || 'Error uploading file'
        });
      }
    });
  }
);



// Add this new route to handle the update request for payment & banking info
router.post("/update-payment-banking-info", logDatabaseOperation('update-banking-info'), async (req, res) => {
  try {
    const { employees } = req.body;
    console.log(`[${req.requestId}] 📝 Processing banking info update for ${employees.length} employees`);
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {
        console.log(`[${req.requestId}] Processing employee ID: ${employeeData.employeeId}`);
        const employee = await Employee.findById(employeeData.employeeId);
        
        if (!employee) {
          console.warn(`[${req.requestId}] ⚠️ Employee not found: ${employeeData.employeeId}`);
          results.errors.push({
            employeeId: employeeData.employeeId,
            error: "Employee not found"
          });
          continue;
        }

        // Update banking info with validation
        const updates = {
          bankName: employeeData.bankName,
          accountNumber: employeeData.accountNumber,
          accountType: employeeData.accountType
        };

        console.log(`[${req.requestId}] Updating banking details for employee: ${employee._id}`);
        await Employee.findByIdAndUpdate(employee._id, { $set: updates });
        
        // Log the audit
        await new AuditLog({
          user: req.user._id,
          action: 'UPDATE_BANKING_INFO',
          entity: 'Employee',
          entityId: employee._id,
          changes: updates
        }).save();

        results.updated++;
        console.log(`[${req.requestId}] ✅ Successfully updated banking info for employee: ${employee._id}`);
      } catch (error) {
        console.error(`[${req.requestId}] ❌ Error updating employee ${employeeData.employeeId}:`, error);
        results.errors.push({
          employeeId: employeeData.employeeId,
          error: error.message
        });
      }
    }

    console.log(`[${req.requestId}] 📊 Update summary:`, {
      totalProcessed: employees.length,
      successful: results.updated,
      failed: results.errors.length
    });

    res.json(results);
  } catch (error) {
    console.error(`[${req.requestId}] ❌ Fatal error in banking info update:`, error);
    res.status(500).json({
      error: "Failed to process banking information updates",
      requestId: req.requestId
    });
  }
});

// Update residential address route
router.post(
  "/:companyCode/employeeManagement/update-residential-address",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employees } = req.body;
      console.log("Received employees data:", employees);
      const results = { updated: 0, errors: [] };

      for (const employeeData of employees) {
        try {
          if (employeeData._id) {
            const updatedEmployee = await Employee.findByIdAndUpdate(
              employeeData._id,
              { residentialAddress: employeeData.residentialAddress },
              { new: true, runValidators: true }
            );
            if (updatedEmployee) {
              results.updated++;
            } else {
              results.errors.push(
                `Employee with id ${employeeData._id} not found`
              );
            }
          } else {
            results.errors.push("Employee ID not provided");
          }
        } catch (error) {
          console.error("Error processing employee:", employeeData, error);
          results.errors.push(
            `Error processing employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      res.json({
        success: true,
        message: "Employees processed",
        results: results,
      });
    } catch (error) {
      console.error("Error updating employees:", error);
      res.status(500).json({
        success: false,
        message: "Error updating employees",
        error: error.message,
      });
    }
  }
);

// Keep existing routes...
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Update the existing skills-equity route
router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});

router.get("/api/employee-status/:employeeId", async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.employeeId);

    if (!employee) {
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    res.json({
      success: true,
      isTerminated: employee.employmentStatus === "Terminated",
      lastDayOfService: employee.endServiceDate,
      employmentStatus: employee.employmentStatus,
      terminationReason: employee.terminationReason,
    });
  } catch (error) {
    console.error("Error fetching employee status:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
});

router.get("/employee-list", ensureAuthenticated, (req, res) => {
  // ... fetch necessary data ...
  res.render("employeeListTab", {
    /* ... pass necessary data ... */
  });
});

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      console.log("Available Pay Frequencies:", {
        count: payFrequencies.length,
        frequencies: payFrequencies.map((pf) => ({
          id: pf._id,
          name: pf.name,
          frequency: pf.frequency,
        })),
      });

      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Create an empty employee object for the template
      const employee = {
        bank: "",
        accountNumber: "",
        accountType: "",
        branchCode: "",
        accountHolder: "",
      };

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        employee, // Pass the empty employee object
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get("/bulk-actions", ensureAuthenticated, (req, res) => {
  res.render("bulkActionsTab");
});

router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

// Add a new route for company selection
router.get("/select-company", async (req, res) => {
  try {
    console.log("\n=== Select Company GET Route Start ===");

    // Fetch user with populated companies
    const user = await User.findById(req.user._id).populate("companies");
    if (!user) {
      console.error("User not found");
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    console.log("User companies:", user.companies?.length || 0);

    // Generate CSRF token
    const csrfToken = req.csrfToken();
    console.log("Generated CSRF token:", csrfToken);

    // Render with all necessary data
    res.render("select-company", {
      user,
      companies: user.companies || [], // Pass companies array
      messages: req.flash(),
      csrfToken,
      partialsDir: "partials",
    });
  } catch (error) {
    console.error("Error loading select-company page:", error);
    req.flash("error", "Error loading companies");
    res.redirect("/");
  }
});

router.post(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        throw new Error("Company not found");
      }

      const employeeData = req.body;

      // Parse dates
      employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();

      // Format working days properly
      if (employeeData.workingDays) {
        // Convert single value to array if needed
        const workingDaysArray = Array.isArray(employeeData.workingDays)
          ? employeeData.workingDays
          : [employeeData.workingDays];

        // Format each working day as an object
        employeeData.regularHours = {
          ...employeeData.regularHours,
          workingDays: workingDaysArray.map((day) => ({
            day: day,
            type: "Normal Day",
          })),
        };
      }

      // Fetch PayFrequency details
      const payFrequency = await PayFrequency.findById(
        employeeData.payFrequency
      );
      if (!payFrequency) {
        throw new Error("Invalid pay frequency selected");
      }

      // Set PayFrequency related fields
      employeeData.payFrequency = payFrequency._id;
      employeeData.lastDayOfPeriod = payFrequency.lastDayOfPeriod;
      employeeData.firstPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;
      employeeData.initialPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;

      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        throw new Error("Employee number settings not found");
      }

      if (settings.mode === "manual") {
        if (!employeeData.companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }
        // Check for uniqueness in manual mode
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: employeeData.companyEmployeeNumber,
        });

        if (existingEmployee) {
          throw new Error("Employee number already exists");
        }
      } else {
        // Automatic mode
        try {
          employeeData.companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      }

      const newEmployee = new Employee({
        ...employeeData,
        globalEmployeeId: uuidv4(),
        company: company._id,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });

      console.log("Employee data before creating model:", employeeData);
      console.log("New employee object:", newEmployee);

      await newEmployee.validate(); // This will throw a ValidationError if there are issues
      await newEmployee.save();

      res.status(200).json({
        message: "Employee added successfully",
        employeeId: newEmployee._id,
      });
    } catch (error) {
      console.error("Error adding new employee:", error);
      res.status(400).json({ error: error.message });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;

      console.log("=== Regular Hours Update ===");
      console.log("Request body:", req.body);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      console.log("Updated employee regular hours:", employee.regularHours);
      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

async function generateEmployeeNumber(companyId) {
  try {
    return await EmployeeNumberService.generateEmployeeNumber(companyId);
  } catch (error) {
    throw new Error(`Failed to generate employee number: ${error.message}`);
  }
}

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode: companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const currentDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Set default values if no payroll exists
      const defaultBasicSalary = 0;
      const basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // Get firstPayrollPeriodEndDate from the payFrequency model
      const firstPayrollPeriodEndDate = moment.tz(
        employee.payFrequency.firstPayrollPeriodEndDate, // Use the value from payFrequency model
        DEFAULT_TIMEZONE
      );

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        console.log("Generating periods with PayFrequency settings:", {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
          firstPayrollPeriodEndDate: moment
            .tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
            .format("YYYY-MM-DD HH:mm:ss"),
        });

        // Use moment.tz for all dates
        const employeeStartDate = moment
          .tz(employee.doa, DEFAULT_TIMEZONE)
          .startOf("day");
        const currentEndDate = moment
          .tz(currentDate, DEFAULT_TIMEZONE)
          .endOf("day");

        console.log("Period Generation Dates:", {
          employeeStartDate: employeeStartDate.format("YYYY-MM-DD"),
          currentEndDate: currentEndDate.format("YYYY-MM-DD"),
        });

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          const proratedResult = payrollCalculations.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            period.startDate,
            period.endDate
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency
          );

          return {
            ...period.toObject(),
            basicSalary: Number(proratedResult.proratedSalary),
            basicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // When finding the current period
      let currentPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        currentPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the normal logic for current/next period
        currentPeriod =
          payPeriodsWithDetails.find((p) => !p.isFinalized) ||
          payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
      }

      // Calculate initial period values
      const currentPeriodCalculations =
        await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod?.endDate ||
            moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
          company._id
        );

      console.log("Initial Period Calculations:", currentPeriodCalculations);
      // After fetching the currentPeriod, add this code to fetch hourly rates
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          }).lean()
        : null;

      // Ensure to log the hourly rates for debugging
      console.log("Hourly rates found:", {
        found: !!hourlyRates,
        normalHours: hourlyRates?.normalHours || 0,
        overtimeHours: hourlyRates?.overtimeHours || 0,
        sundayHours:
          hourlyRates?.weeks?.reduce(
            (total, week) => total + (Number(week.sundayHours) || 0),
            0
          ) || 0,
      });
      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );

      const totalIncome = Number(
        currentPeriodCalculations.totalIncome || proratedSalary
      );

      console.log("Salary Calculations:", {
        basicSalary,
        proratedSalary,
        totalIncome,
        isProrated: currentPeriodCalculations.proRataDetails?.isProrated,
      });

      // Initialize payroll totals with the correct values
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome,
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0)
        ),
      };

      console.log("Final Payroll Totals:", payrollTotals);

      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        hourlyRates, // Ensure this line is included
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(
            currentPeriodCalculations.basicSalary?.full ||
              currentPeriodCalculations.basicSalary ||
              0
          ),
          proratedSalary: Number(
            currentPeriodCalculations.basicSalary?.prorated ||
              currentPeriodCalculations.proratedSalary ||
              0
          ),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(
            currentPeriodCalculations.proRataDetails?.percentage || 100
          ),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(
            currentPeriodCalculations.totalDeductions || 0
          ),
          netPay: Number(currentPeriodCalculations.netPay || 0),
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const employeeAge = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;
      const pensionFundDeduction =
        payroll?.pensionFund?.fixedContributionEmployee || 0;
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // Find the current payroll period
      let payrollPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        payrollPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the current period
        payrollPeriod = currentPeriod;
      }

      // If no period is found, create a default one
      if (!payrollPeriod) {
        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month");
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary,
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };
      }

      // Get current payroll data
      // Get current payroll data
      const taxableIncome = payrollCalculations.calculateTotalTaxableIncome(
        payroll || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly"
      );

      // Calculate all deductions using new function
      const deductionsResult = payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome
      );

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });

      console.log("Detailed Payroll Calculations Debug", {
        paye: {
          amount: currentPeriodCalculations.paye,
          monthlyTax: currentPeriodCalculations.paye,
        },
        uif: {
          amount: currentPeriodCalculations.uif,
        },
        deductions: {
          statutory: {
            paye: currentPeriodCalculations.paye,
            uif: currentPeriodCalculations.uif,
          },
          total: currentPeriodCalculations.totalDeductions,
        },
        fullCalculations: currentPeriodCalculations,
      });

      // Travel Allowance Calculation
      console.log("Travel Allowance Debug - Payroll Object", {
        travelAllowance: payroll?.travelAllowance,
        hasFixedAllowance: !!payroll?.travelAllowance?.fixedAllowance,
        hasReimbursedExpenses: !!payroll?.travelAllowance?.reimbursedExpenses,
      });

      if (
        payroll?.travelAllowance?.fixedAllowance ||
        payroll?.travelAllowance?.reimbursedExpenses
      ) {
        const travelAllowanceResult =
          await PayrollService.processTravelAllowance(employee, payroll);

        console.log(
          "Travel Allowance Processing Result",
          travelAllowanceResult
        );

        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;

          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: 20,
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: 80,
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes,
          };
        }
      }

      // Log employee age details
      console.log("🎂 Employee Profile Age Debug:", {
        "Employee ID": employeeId,
        "Date of Birth": employee.dob
          ? moment(employee.dob).format("YYYY-MM-DD")
          : "N/A",
        "Calculated Age": calculateAge(employee.dob),
        "Company Code": companyCode,
      });

      // Add debug logging
      console.log("Render Data:", {
        currentPeriod: !!currentPeriod,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        ...renderData.payrollTotals,
      });

      // Enhanced Payroll Details Calculation
      const payrollDetails = await PayrollService.calculatePayrollDetails(
        employee,
        payroll,
        currentPeriod
      );

      renderData.payrollDetails = payrollDetails;

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      console.log('=== Employee Profile Debug ===', {
        employeeId: employee._id,
        currentDate: currentDate,
        payFrequency: employee.payFrequency,
        doa: employee.doa
      });

      // Get payroll details and calculations
      const payrollDetails = await PayrollService.calculatePayrollDetails(
        employee._id,
        currentDate,
        company._id
      );

      console.log('=== Payroll Details Debug ===', {
        payrollDetails: {
          basicSalary: payrollDetails?.basicSalary,
          proRataDetails: payrollDetails?.proRataDetails,
          isProrated: payrollDetails?.proRataDetails?.isProrated,
          percentage: payrollDetails?.proRataDetails?.percentage,
          daysWorked: payrollDetails?.proRataDetails?.daysWorked,
          totalDays: payrollDetails?.proRataDetails?.totalDays
        }
      });

      // Calculate prorated values
      const proratedSalary = payrollDetails?.basicSalary?.prorated || 0;
      const basicSalary = payrollDetails?.basicSalary?.full || 0;
      const isProrated = payrollDetails?.proRataDetails?.isProrated || false;

      console.log('=== Final Values Debug ===', {
        proratedSalary,
        basicSalary,
        isProrated,
        proRataDetails: payrollDetails?.proRataDetails
      });

      renderData.proratedSalary = payrollDetails?.basicSalary?.prorated || 0;
      renderData.basicSalary = payrollDetails?.basicSalary?.full || 0;
      renderData.isProrated = payrollDetails?.proRataDetails?.isProrated || false;
      renderData.payrollDetails = payrollDetails;

      console.log('=== Final Render Data Debug ===', {
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        isProrated: renderData.isProrated,
        proRataDetails: payrollDetails?.proRataDetails
      });

      res.render("employeeProfile", renderData);
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "Error processing employee profile",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);


router.post(
  "/api/employees/:employeeId/enableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Get base URL for email
      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user if needed
        if (
          !user.role ||
          user.role.toString() !== employeeRole._id.toString()
        ) {
          user.role = employeeRole._id;
          await user.save();
        }
      } else {
        // Create new user account
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          password: crypto.randomBytes(20).toString("hex"), // Temporary password
        });

        await user.save();
      }

      // Link user and role to employee
      employee.user = user._id;
      employee.role = employeeRole._id;
      employee.selfServiceEnabled = true;

      // Generate and send setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      await employee.save();

      res.json({
        success: true,
        message: "Self-service enabled successfully",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/disableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/resendInvite",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Resend setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      res.json({
        success: true,
        message: "Invitation resent successfully",
      });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend invite",
        error: error.message,
      });
    }
  }
);

// Self Service Routes
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Disable self-service route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing disable request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();
      console.log("Employee self-service disabled successfully");

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Update the enable self-service route
router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Enable self-service route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing enable request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        console.log("Employee role not found");
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });
      console.log("Existing user found:", user ? "yes" : "no");

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();
      console.log("User saved successfully");

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      await employee.save();
      console.log("Employee updated successfully");

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email sent successfully");

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Resend invite route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing resend request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Generate new token
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );
      console.log("Generated new token for employee");

      await employee.save();
      console.log("Employee updated with new token");

      // Send new setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account setup link has been renewed. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email resent successfully");

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);

module.exports = router;


// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Processing delete request for employee:", employeeId);

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Delete page route hit:", { companyCode, employeeId });

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("\n=== Calculating First Payroll Period End Date ===");
      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);
router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      const currentPeriod = {
        startDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        ),
        endDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        ),
      };

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(currentDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(currentDate),
          basicSalary: 0,
        });
        await payroll.save();
      }

      // Format the date directly
      const formattedDate = currentPeriod.endDate.toLocaleDateString("en-GB");

      console.log("Rendering addBasicSalary with:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll._id,
        currentPeriod,
      });

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod,
        formattedDate,
        payroll,
        relevantDate: currentDate,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);


module.exports = router;
