const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayrollPeriod = require("../models/PayrollPeriod");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const cookieParser = require('cookie-parser');
const csrf = require("csurf");
const flash = require('connect-flash');

// Configure middleware
router.use(cookieParser());
router.use(express.urlencoded({ extended: true }));
router.use(express.json());
router.use(flash());

// CSRF Protection setup
const csrfProtection = csrf({ 
  cookie: {
    key: '_csrf',
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

// Apply CSRF protection to all routes
router.use(csrfProtection);

// Make CSRF token available to all views
router.use((req, res, next) => {
  res.locals.csrfToken = req.csrfToken();
  next();
});

// Middleware to ensure company code is available
router.use('/:companyCode/*', (req, res, next) => {
  req.companyCode = req.params.companyCode;
  next();
});

// Middleware to ensure employee can only access their own profile
const ensureOwnProfile = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    
    // Check if the logged-in user is the employee
    if (req.user.role === 'employee' && req.user._id.toString() !== employeeId) {
      return res.status(403).send('Unauthorized access to employee profile');
    }
    
    next();
  } catch (error) {
    console.error('Profile access error:', error);
    res.status(500).send('Server error');
  }
};

// Apply authentication middleware
router.use(ensureAuthenticated);

// GET travel expenses form
router.get('/:companyCode/employeeProfile/:employeeId/travel-expenses', ensureOwnProfile, async (req, res) => {
  try {
    const { employeeId, companyCode } = req.params;

    // Get current date
    const currentDate = new Date();
    
    // Find current period
    const currentPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: { $lte: currentDate },
      endDate: { $gte: currentDate }
    }).lean();

    console.log('Current Period:', {
      id: currentPeriod?._id,
      startDate: currentPeriod?.startDate,
      endDate: currentPeriod?.endDate
    });

    if (!currentPeriod) {
      req.flash('error', 'No active payroll period found');
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }

    // Get the employee
    const employee = await Employee.findById(employeeId).populate('company');
    
    if (!employee) {
      req.flash('error', 'Employee not found');
      return res.redirect(`/clients/${companyCode}`);
    }

    // Calculate month range for the current period
    const periodStartMonth = new Date(currentPeriod.startDate.getFullYear(), currentPeriod.startDate.getMonth(), 1);
    const periodEndMonth = new Date(currentPeriod.endDate.getFullYear(), currentPeriod.endDate.getMonth() + 1, 0);

    console.log('Payroll Query Params:', {
      employeeId,
      companyId: employee.company._id,
      periodStartMonth,
      periodEndMonth
    });

    // Get the payroll document for the current period's month
    const payroll = await Payroll.findOne({
      employee: employeeId,
      company: employee.company._id,
      month: {
        $gte: periodStartMonth,
        $lte: periodEndMonth
      }
    }).lean();

    console.log('Current Payroll:', {
      id: payroll?._id,
      month: payroll?.month,
      travelAllowance: payroll?.travelAllowance,
      companyPetrolCard: payroll?.travelAllowance?.companyPetrolCard
    });

    // If no payroll found, try to find the latest payroll
    if (!payroll) {
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id
      }).sort({ month: -1 }).lean();

      console.log('Latest Payroll (fallback):', {
        id: latestPayroll?._id,
        month: latestPayroll?.month,
        travelAllowance: latestPayroll?.travelAllowance
      });

      // Use the latest payroll if found
      if (latestPayroll) {
        res.render('editTravelExpenses', {
          employee,
          currentPeriod,
          payroll: latestPayroll,
          componentName: 'Travel Expenses',
          relevantDate: currentDate.toISOString(),
          csrfToken: req.csrfToken(),
          messages: {
            success: req.flash('success'),
            error: req.flash('error'),
            warning: ['Using travel allowance settings from the latest payroll period']
          }
        });
        return;
      }
    }

    // Render the form with the current period's payroll
    res.render('editTravelExpenses', {
      employee,
      currentPeriod,
      payroll,
      componentName: 'Travel Expenses',
      relevantDate: currentDate.toISOString(),
      csrfToken: req.csrfToken(),
      messages: {
        success: req.flash('success'),
        error: req.flash('error')
      }
    });

  } catch (error) {
    console.error('Error loading travel expenses form:', error);
    req.flash('error', 'Error loading form');
    res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
  }
});

// POST travel expenses
router.post('/:companyCode/employeeProfile/:employeeId/payslipInputs/travel-expenses', ensureOwnProfile, async (req, res) => {
  try {
    const { employeeId, companyCode } = req.params;
    const { expenses, petrolCardSpend, kmsTravelled } = req.body;

    // Get current payroll period
    const currentDate = new Date();
    const currentPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: { $lte: currentDate },
      endDate: { $gte: currentDate }
    });

    if (!currentPeriod) {
      return res.status(400).send('No active payroll period found');
    }

    // Get current payroll to check travel allowance settings
    const payroll = await Payroll.findOne({
      employee: employeeId,
      company: currentPeriod.company,
      month: {
        $gte: new Date(currentPeriod.startDate.getFullYear(), currentPeriod.startDate.getMonth(), 1),
        $lte: new Date(currentPeriod.endDate.getFullYear(), currentPeriod.endDate.getMonth() + 1, 0)
      }
    });

    // Update travel expenses
    const travelExpensesData = {
      expenses: parseFloat(expenses) || 0,
      lastModified: new Date()
    };

    // Only include petrol card spend if company petrol card is enabled
    if (payroll?.travelAllowance?.companyPetrolCard) {
      travelExpensesData.petrolCardSpend = parseFloat(petrolCardSpend) || 0;
    }

    // Only include kilometers travelled if reimbursed per km is enabled
    if (payroll?.travelAllowance?.reimbursedPerKmTravelled) {
      travelExpensesData.kmsTravelled = parseFloat(kmsTravelled) || 0;
      // Calculate the reimbursement amount based on rate per km
      travelExpensesData.kmReimbursement = (parseFloat(kmsTravelled) || 0) * (payroll.travelAllowance.ratePerKm || 0);
    }

    currentPeriod.data.set('travelExpenses', travelExpensesData);
    await currentPeriod.save();

    req.flash('success', 'Travel expenses updated successfully');
    res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

  } catch (error) {
    console.error('Error updating travel expenses:', error);
    req.flash('error', error.message || 'Error updating travel expenses');
    res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
  }
});

// Remove travel expenses
router.post('/:companyCode/payslipInputs/:employeeId/remove/travel-expenses', ensureOwnProfile, async (req, res) => {
  try {
    const { employeeId } = req.params;

    // Get current payroll period
    const currentDate = new Date();
    const currentPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: { $lte: currentDate },
      endDate: { $gte: currentDate }
    });

    if (!currentPeriod) {
      return res.status(400).send('No active payroll period found');
    }

    // Remove travel expenses from period data
    if (currentPeriod.data && currentPeriod.data.has('travelExpenses')) {
      currentPeriod.data.delete('travelExpenses');
      await currentPeriod.save();
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Error removing travel expenses:', error);
    res.status(500).json({ error: 'Error removing travel expenses' });
  }
});

module.exports = router;
