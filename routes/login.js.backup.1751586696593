const express = require("express");
const router = express.Router();
const bcrypt = require("bcryptjs");
const User = require("../models/user");

// Login Route
router.post("/", async (req, res) => {
  const { email, password } = req.body;

  try {
    // Find the user by email
    const user = await User.findOne({ email });

    // Check if user exists
    if (!user) {
      return res.render("login", {
        errorMessages: ["Invalid email or password."],
        email,
      });
    }

    // Check if the user's email is verified
    if (!user.isVerified) {
      return res.render("login", {
        errorMessages: ["Please verify your email before logging in."],
        email,
      });
    }

    // User exists and is verified, now check the password
    const passwordMatch = await user.comparePassword(password);
    if (!passwordMatch) {
      return res.render("login", {
        errorMessages: ["Invalid email or password."],
        email,
      });
    }

    // Passwords match, login successful
    req.session.user = user; // Store user data in session
    res.redirect("/dashboard"); // Redirect to the dashboard
  } catch (error) {
    console.error("Error during login:", error);
    res
      .status(500)
      .render("error", { message: "An error occurred during login." });
  }
});

module.exports = router;
