const express = require("express");
const { XeroClient } = require("xero-node");
const Integration = require("../models/Integration");
const Company = require("../models/Company");
const XeroAccountMapping = require("../models/xeroAccountMapping");
const PayRun = require("../models/payRun");
const XeroTokenManager = require("../services/xeroTokenManager");
const { ensureAuthenticated } = require("../middleware/auth");

// Create router with mergeParams to access parent router params
const router = express.Router({ mergeParams: true });

// Initialize token manager instance
const tokenManager = new XeroTokenManager();

// State management utilities
const crypto = require("crypto");

// Import axios for direct API calls
const axios = require('axios');

const generateState = () => {
  return crypto.randomBytes(32).toString("hex");
};

// Generate code verifier and challenge for PKCE
const generateCodeVerifier = () => {
  // Generate a random string of 43-128 characters
  // Using characters: A-Z, a-z, 0-9, and "-", ".", "_", "~"
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  const length = 64; // Use a fixed length for consistency
  
  let result = '';
  const randomValues = crypto.randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    result += chars[randomValues[i] % chars.length];
  }
  
  return result;
};

const generateCodeChallenge = async (verifier) => {
  const hash = crypto.createHash("sha256");
  hash.update(verifier);
  return hash.digest("base64url");
};

const validateState = (savedState, receivedState) => {
  console.log("[Xero Integration] Starting state validation process", {
    savedState,
    receivedState,
    savedLength: savedState?.length,
    receivedLength: receivedState?.length,
    savedType: typeof savedState,
    receivedType: typeof receivedState,
    savedBuffer: savedState
      ? Buffer.from(savedState, "hex").toString("hex")
      : null,
    receivedBuffer: receivedState
      ? Buffer.from(receivedState, "hex").toString("hex")
      : null,
  });

  if (!savedState || !receivedState) {
    console.error("[Xero Integration] Missing state parameters", {
      hasSavedState: !!savedState,
      hasReceivedState: !!receivedState,
      savedStateType: typeof savedState,
      receivedStateType: typeof receivedState,
    });
    return false;
  }

  try {
    // Convert states to buffers for comparison
    const savedBuffer = Buffer.from(savedState, "hex");
    const receivedBuffer = Buffer.from(receivedState, "hex");

    console.log("[Xero Integration] State buffers created", {
      savedBufferLength: savedBuffer.length,
      receivedBufferLength: receivedBuffer.length,
      savedBufferHex: savedBuffer.toString("hex"),
      receivedBufferHex: receivedBuffer.toString("hex"),
    });

    // Use timing-safe comparison
    const isValid = crypto.timingSafeEqual(savedBuffer, receivedBuffer);

    console.log("[Xero Integration] State validation complete", {
      isValid,
      savedState,
      receivedState,
      savedBufferHex: savedBuffer.toString("hex"),
      receivedBufferHex: receivedBuffer.toString("hex"),
    });

    return isValid;
  } catch (error) {
    console.error("[Xero Integration] State validation error", {
      error: error.message,
      stack: error.stack,
      savedState,
      receivedState,
      savedStateType: typeof savedState,
      receivedStateType: typeof receivedState,
    });
    return false;
  }
};

// Create Xero client instance with proper scopes
const getXeroClient = () => {
  // Use a consistent redirect URI that matches Xero app settings
  const redirectUri = "http://localhost:3002/xero/callback";

  console.log(
    "[Xero Integration] Creating client with redirect URI:",
    redirectUri
  );

  const config = {
    clientId: process.env.XERO_CLIENT_ID,
    clientSecret: process.env.XERO_CLIENT_SECRET,
    redirectUris: [redirectUri],
    scopes: process.env.XERO_SCOPES.split(" "),
    // Configure OpenID Connect parameters
    defaultChecks: ["state", "pkce"], // Default checks to perform
    clockTolerance: 5, // Allow for some clock skew
    responseTypes: ["code"],
    tokenEndpointAuthMethod: "client_secret_post",
    grantTypes: ["authorization_code", "refresh_token"],
    // Add timeout and retry configuration
    timeout: 60000, // 60 seconds timeout
    retry: {
      retries: 3,
      factor: 2,
      minTimeout: 1000,
      maxTimeout: 60000,
      randomize: true,
    },
  };

  console.log("[Xero Integration] Client configuration:", {
    ...config,
    clientSecret: "***REDACTED***",
  });

  // Create a new client instance with proper state validation
  const client = new XeroClient(config);

  // Initialize the client and its API instances
  client.initialize().then(() => {
    // Add request interceptor for timeout handling after initialization
    if (client.accountingApi && client.accountingApi.apiClient) {
      client.accountingApi.apiClient.axiosInstance.interceptors.request.use(config => {
        config.timeout = 60000; // 60 seconds timeout
        return config;
      });
    }
  }).catch(error => {
    console.error("[Xero Integration] Error initializing client:", error);
  });

  return client;
};

// Connect route
router.get("/connect", ensureAuthenticated, async (req, res) => {
  // Extract company code from the URL path
  // The URL pattern is /clients/:companyCode/settings/accounting/xero/connect
  const urlParts = req.originalUrl.split('/');
  const companyCodeIndex = urlParts.indexOf('clients') + 1;
  const companyCode = urlParts[companyCodeIndex] || 'unknown';
  
  const timestamp = Date.now();
  const expiresAt = new Date(timestamp + 5 * 60 * 1000); // 5 minutes from now
  const requestId = crypto.randomBytes(8).toString("hex");
  
  const logContext = {
    requestId,
    route: "/connect",
    companyCode,
    sessionId: req.sessionID,
    timestamp: new Date(timestamp).toISOString(),
  };
  
  console.log("[Xero Integration] Connect route entry", {
    ...logContext,
    query: req.query,
    userAgent: req.headers["user-agent"],
    referrer: req.headers.referer,
  });
  
  try {
    console.log("[Xero Integration] Starting OAuth flow", {
      ...logContext,
      timestamp,
      expiresAt: expiresAt.toISOString(),
    });
    
    // Generate state
    const state = crypto.randomBytes(32).toString("hex");
    
    // Generate PKCE verifier and challenge
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = await generateCodeChallenge(codeVerifier);
    
    // Store state and PKCE values in session
    req.session.xeroState = state;
    req.session.xeroConnectStarted = timestamp;
    req.session.codeVerifier = codeVerifier;
    req.session.companyCode = companyCode;
    
    // Also store in a cookie as backup
    res.cookie('xero_state', state, { 
      httpOnly: true, 
      maxAge: 300000, // 5 minutes
      sameSite: 'lax'
    });
    res.cookie('xero_code_verifier', codeVerifier, { 
      httpOnly: true, 
      maxAge: 300000, // 5 minutes
      sameSite: 'lax'
    });
    res.cookie('xero_company_code', companyCode, { 
      httpOnly: true, 
      maxAge: 300000, // 5 minutes
      sameSite: 'lax'
    });
    
    // Save session immediately and wait for it to complete
    await new Promise((resolve, reject) => {
      req.session.save((err) => {
        if (err) {
          console.error("[Xero Integration] Failed to save session", {
            ...logContext,
            error: err.message,
          });
          reject(err);
          return;
        }
        console.log("[Xero Integration] Session saved successfully", {
          ...logContext,
          state,
          sessionState: req.session.xeroState,
        });
        resolve();
      });
    });
    
    console.log("[Xero Integration] Generated state", {
      ...logContext,
      state,
    });
    
    // Use consistent redirect URI
    const redirectUri = "http://localhost:3002/xero/callback";
    
    // Define required scopes with valid Xero scopes
    const scopes = [
      'offline_access',
      'openid',
      'profile',
      'email',
      'accounting.transactions',
      'accounting.settings',
      'accounting.contacts'
    ];
    
    // Build URL with same state
    const params = new URLSearchParams({
      client_id: process.env.XERO_CLIENT_ID,
      scope: scopes.join(" "),
      response_type: "code",
      redirect_uri: redirectUri,
      state: state,
      code_challenge: codeChallenge,
      code_challenge_method: "S256",
    });
    
    const consentUrl = `https://login.xero.com/identity/connect/authorize?${params.toString()}`;
    
    // Store parameters in session using same state
    req.session.xeroAuthParams = {
      client_id: process.env.XERO_CLIENT_ID,
      scope: scopes.join(" "),
      response_type: "code",
      redirect_uri: redirectUri,
      state: state,
    };
    
    // Save session again to ensure auth params are persisted
    await new Promise((resolve, reject) => {
      req.session.save((err) => {
        if (err) {
          console.error("[Xero Integration] Failed to save auth params", {
            ...logContext,
            error: err.message,
          });
          reject(err);
          return;
        }
        console.log("[Xero Integration] Auth params saved successfully", {
          ...logContext,
          state,
          sessionState: req.session.xeroState,
        });
        resolve();
      });
    });
    
    // Verify URL state matches session state
    const urlParams = new URLSearchParams(new URL(consentUrl).search);
    const urlState = urlParams.get("state");
    
    console.log("[Xero Integration] URL State Verification", {
      ...logContext,
      urlState,
      sessionState: req.session.xeroState,
      stateMatch: urlState === req.session.xeroState,
    });
    
    if (urlState !== req.session.xeroState) {
      throw new Error("State parameter in URL does not match session state");
    }
    
    console.log("[Xero Integration] Built consent URL", {
      ...logContext,
      state: req.session.xeroState,
      sessionState: req.session.xeroState,
      url: consentUrl.replace(process.env.XERO_CLIENT_ID, "***CLIENT_ID***"),
    });
    
    // Skip the final session verification that was causing issues
    // Just redirect to the consent URL
    console.log("[Xero Integration] Redirecting to Xero", {
      ...logContext,
      state: req.session.xeroState,
      url: "REDACTED_URL",
    });
    
    return res.redirect(consentUrl);
  } catch (error) {
    console.error("Error in connect route:", error);
    req.flash("error", "Failed to connect to Xero. Please try again.");
    return res.redirect(`/clients/${companyCode}/settings/accounting/accounting-integrations`);
  }
});

// Xero OAuth callback route
router.get("/callback", async (req, res) => {
  const requestId = crypto.randomBytes(8).toString("hex");
  const logContext = {
    requestId,
    route: "/callback",
    sessionId: req.sessionID,
    timestamp: new Date().toISOString(),
  };

  console.log("\n=== Xero Callback Debug Start ===");
  console.log("Request ID:", requestId);
  console.log("Full URL:", req.originalUrl);
  console.log("Query Parameters:", JSON.stringify(req.query, null, 2));
  
  // Get data from session or cookies
  const sessionState = req.session.xeroState;
  const sessionCompanyCode = req.session.companyCode;
  const sessionCodeVerifier = req.session.codeVerifier;
  
  // Get backup data from cookies
  const cookieState = req.cookies.xero_state;
  const cookieCompanyCode = req.cookies.xero_company_code;
  const cookieCodeVerifier = req.cookies.xero_code_verifier;
  
  // Use session data if available, otherwise use cookie data
  const state = sessionState || cookieState;
  const companyCode = sessionCompanyCode || cookieCompanyCode;
  const codeVerifier = sessionCodeVerifier || cookieCodeVerifier;
  
  console.log("Session Data:", {
    state: sessionState,
    companyCode: sessionCompanyCode,
    codeVerifier: sessionCodeVerifier ? "Present (length: " + sessionCodeVerifier.length + ")" : "Missing"
  });
  
  console.log("Cookie Data:", {
    state: cookieState,
    companyCode: cookieCompanyCode,
    codeVerifier: cookieCodeVerifier ? "Present (length: " + cookieCodeVerifier.length + ")" : "Missing"
  });
  
  console.log("Final Data:", {
    state,
    companyCode,
    codeVerifier: codeVerifier ? "Present (length: " + codeVerifier.length + ")" : "Missing"
  });

  try {
    // Extract parameters from query
    const { code, state: queryState, error, error_description } = req.query;
    
    // Check for OAuth errors
    if (error) {
      console.error("\n=== OAuth Error ===");
      console.error("Error:", error);
      console.error("Description:", error_description);
      req.flash("error", `Xero connection error: ${error_description || error}`);
      return res.redirect("/clients");
    }
    
    // Validate code and state from query
    if (!code || !queryState) {
      console.error("\n=== Missing URL Parameters Error ===");
      console.error({
        hasCode: !!code,
        hasState: !!queryState,
      });
      req.flash("error", "Missing required parameters from Xero");
      return res.redirect("/clients");
    }
    
    // Log session data
    console.log("\n=== Auth Data ===");
    console.log({
      hasState: !!state,
      hasCompanyCode: !!companyCode,
      hasCodeVerifier: !!codeVerifier,
      stateMatch: state === queryState,
      codeLength: code ? code.length : 0,
      codeVerifierLength: codeVerifier ? codeVerifier.length : 0
    });
    
    // Validate state
    if (!state || state !== queryState) {
      console.error("\n=== State Mismatch Error ===");
      console.error({
        expectedState: state,
        receivedState: queryState,
        match: state === queryState
      });
      req.flash("error", "Security validation failed. Please try again.");
      return res.redirect("/clients");
    }
    
    // Validate code verifier
    if (!codeVerifier) {
      console.error("\n=== Missing Code Verifier Error ===");
      req.flash("error", "Security data missing. Please try again.");
      return res.redirect("/clients");
    }
    
    // Initialize Xero client
    console.log("\n=== Initializing Xero Client ===");
    console.log("Client ID:", process.env.XERO_CLIENT_ID ? "Present" : "Missing");
    console.log("Client Secret:", process.env.XERO_CLIENT_SECRET ? "Present" : "Missing");
    console.log("Redirect URI:", process.env.XERO_REDIRECT_URI || "http://localhost:3002/xero/callback");
    
    const xero = new XeroClient({
      clientId: process.env.XERO_CLIENT_ID,
      clientSecret: process.env.XERO_CLIENT_SECRET,
      redirectUris: [process.env.XERO_REDIRECT_URI || "http://localhost:3002/xero/callback"],
      scopes: [
        'offline_access',
        'openid',
        'profile',
        'email',
        'accounting.transactions',
        'accounting.settings',
        'accounting.contacts'
      ],
      state: queryState,
      httpTimeout: 10000, // 10 seconds
    });
    
    // Exchange code for token
    console.log("\n=== Exchanging Code for Token ===");
    console.log("Code:", code ? code.substring(0, 5) + "..." : "Missing");
    console.log("Code Verifier:", codeVerifier ? codeVerifier.substring(0, 5) + "..." : "Missing");
    console.log("Code Length:", code ? code.length : 0);
    console.log("Code Verifier Length:", codeVerifier ? codeVerifier.length : 0);
    
    // Try manual token exchange
    console.log("\n=== Attempting Manual Token Exchange ===");
    try {
      // Prepare token request
      const tokenEndpoint = 'https://identity.xero.com/connect/token';
      const tokenRequestBody = new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: process.env.XERO_REDIRECT_URI || "http://localhost:3002/xero/callback",
        code_verifier: codeVerifier,
        client_id: process.env.XERO_CLIENT_ID,
        client_secret: process.env.XERO_CLIENT_SECRET
      });
      
      console.log("Token Request Body:", {
        grant_type: 'authorization_code',
        code: code ? code.substring(0, 5) + "..." : "Missing",
        redirect_uri: process.env.XERO_REDIRECT_URI || "http://localhost:3002/xero/callback",
        code_verifier: codeVerifier ? codeVerifier.substring(0, 5) + "..." : "Missing",
        client_id: process.env.XERO_CLIENT_ID ? "Present" : "Missing",
        client_secret: process.env.XERO_CLIENT_SECRET ? "Present" : "Missing"
      });
      
      // Make token request
      const fetch = require('node-fetch');
      const tokenResponse = await fetch(tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: tokenRequestBody
      });
      
      // Parse token response
      const tokenData = await tokenResponse.json();
      console.log("Token Response Status:", tokenResponse.status);
      console.log("Token Response Headers:", JSON.stringify(Object.fromEntries([...tokenResponse.headers]), null, 2));
      
      if (tokenResponse.ok) {
        console.log("Token Exchange Successful");
        console.log("Access Token:", tokenData.access_token ? "Present" : "Missing");
        console.log("Refresh Token:", tokenData.refresh_token ? "Present" : "Missing");
        console.log("Expires In:", tokenData.expires_in);
        
        // Save the integration
        const company = await Company.findOne({ companyCode });
        if (!company) {
          throw new Error("Company not found");
        }
        
        const integration = await Integration.findOneAndUpdate(
          { company: company._id, type: "accounting", provider: "xero" },
          {
            company: company._id,
            type: "accounting",
            provider: "xero",
            credentials: {
              accessToken: tokenData.access_token,
              refreshToken: tokenData.refresh_token,
              expiresAt: new Date(Date.now() + tokenData.expires_in * 1000),
              idToken: tokenData.id_token,
              scope: tokenData.scope,
              tokenType: tokenData.token_type,
              tenantId: tokenData.tenant_id || null,
              tenantName: tokenData.tenant_name || null
            },
            status: "active",
            lastSync: new Date()
          },
          { upsert: true, new: true }
        );
        
        console.log(`Integration saved for company: ${company.name} (${company.companyCode})`);
        
        // Clear cookies
        res.clearCookie('xero_state');
        res.clearCookie('xero_code_verifier');
        res.clearCookie('xero_company_code');
        
        // Redirect to the accounting integrations page
        req.flash("success", "Successfully connected to Xero");
        return res.redirect(`/clients/${company.companyCode}/settings/accounting/accounting-integrations`);
      } else {
        console.error("Token Exchange Failed");
        console.error("Error:", tokenData);
        throw new Error(`Token exchange failed: ${JSON.stringify(tokenData)}`);
      }
    } catch (tokenError) {
      console.error("Token Exchange Error:", tokenError);
      throw tokenError;
    }
  } catch (error) {
    console.error("Error in callback route:", error);
    req.flash("error", "Failed to complete Xero integration: " + error.message);
    return res.redirect("/clients");
  }
});

// Error handler route
router.get("/error", (req, res) => {
  const { message } = req.query;
  res.status(400).json({
    error: "Xero Integration Error",
    message: message || "An unknown error occurred",
    timestamp: new Date().toISOString(),
  });
});

// Test route to get CSRF token
router.get("/test", (req, res) => {
  res.json({
    csrfToken: req.csrfToken(),
    message: "Use this token in the X-CSRF-Token header for POST requests",
  });
});

// Test route to verify session functionality
router.get("/test-session", (req, res) => {
  // Generate a random value
  const testValue = crypto.randomBytes(8).toString("hex");
  
  // Store it in the session
  req.session.testValue = testValue;
  
  // Save the session
  req.session.save((err) => {
    if (err) {
      console.error("Error saving session:", err);
      return res.status(500).json({ error: "Failed to save session", details: err.message });
    }
    
    // Redirect to the verification route
    res.redirect("/xero/verify-session");
  });
});

// Verification route
router.get("/verify-session", (req, res) => {
  // Get the test value from the session
  const testValue = req.session.testValue;
  
  if (!testValue) {
    return res.status(500).json({ 
      error: "Session test failed", 
      message: "Test value not found in session",
      session: {
        id: req.sessionID,
        cookie: req.session.cookie ? "Present" : "Missing",
        keys: Object.keys(req.session)
      }
    });
  }
  
  // Success
  res.json({ 
    success: true, 
    message: "Session test passed", 
    testValue,
    session: {
      id: req.sessionID,
      cookie: req.session.cookie ? "Present" : "Missing",
      keys: Object.keys(req.session)
    }
  });
});

// Disconnect route
router.post("/disconnect", ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.body;
    
    if (!companyCode) {
      req.flash("error", "Company code is required");
      return res.redirect("/clients");
    }
    
    console.log(`[Xero Integration] Disconnecting Xero for company ${companyCode}`);
    
    // Find the company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/clients");
    }
    
    // Find and update the integration
    const integration = await Integration.findOneAndUpdate(
      {
        company: company._id,
        provider: "xero",
        type: "accounting"
      },
      {
        $set: {
          status: "inactive",
          lastError: null,
          "credentials.accessToken": null,
          "credentials.refreshToken": null,
          "credentials.expiresAt": null,
          "credentials.tenantId": null,
          "credentials.tenantName": null
        }
      }
    );
    
    if (!integration) {
      req.flash("info", "No Xero integration found to disconnect");
    } else {
      req.flash("success", "Successfully disconnected from Xero");
      console.log(`[Xero Integration] Successfully disconnected Xero for company ${companyCode}`);
    }
    
    // Delete any account mappings
    await XeroAccountMapping.deleteMany({ company: company._id });
    
    return res.redirect(`/clients/${companyCode}/settings/accounting/accounting-integrations`);
  } catch (error) {
    console.error("[Xero Integration] Disconnect error:", error);
    req.flash("error", `Failed to disconnect from Xero: ${error.message}`);
    return res.redirect("/clients");
  }
});

// Get Xero accounts
router.get("/accounts", async (req, res) => {
  try {
    console.log("\n=== Xero Get Accounts Debug Start ===");
    
    // Extract company code from the URL path
    // The URL pattern is /clients/:companyCode/settings/accounting/xero/accounts
    const urlParts = req.originalUrl.split('/');
    const companyCodeIndex = urlParts.indexOf('clients') + 1;
    const companyCode = urlParts[companyCodeIndex] || req.params.companyCode;
    
    console.log("Company Code:", companyCode);

    // First get the company using the companyCode from params
    const company = await Company.findOne({
      companyCode: companyCode,
    });
    console.log("Company found:", {
      found: !!company,
      id: company?._id,
      companyCode: company?.companyCode,
    });

    if (!company) {
      throw new Error("Company not found");
    }

    // Now find the integration using the company ID
    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting",
    });

    if (!integration || integration.status !== "active") {
      throw new Error("Xero integration not found or not active");
    }

    // Get token data using standardized token manager
    const tokenData = tokenManager.getTokenData(integration);

    console.log("Integration found:", {
      found: !!integration,
      status: integration?.status,
      hasAccessToken: !!tokenData.accessToken,
      hasRefreshToken: !!tokenData.refreshToken,
      hasTenantId: !!tokenData.tenantId,
      tenantId: tokenData.tenantId,
      expiresAt: tokenData.expiresAt,
      timeUntilExpiry: tokenManager.getTimeUntilExpiry(integration)
    });

    if (!tokenData.accessToken) {
      throw new Error("Xero integration not connected - no access token");
    }

    // Initialize Xero client using standardized token manager
    const xero = tokenManager.createXeroClient();
    await xero.initialize();
    console.log("Xero client initialized");

    // Check if token needs refresh using standardized logic
    if (tokenManager.needsRefresh(integration)) {
      console.log(
        "Token needs refresh, current expiry:",
        tokenData.expiresAt ? tokenData.expiresAt.toISOString() : 'unknown'
      );

      // Use enhanced error handling for token refresh
      const companyId = company._id.toString();
      const refreshResult = await tokenManager.refreshTokenWithErrorHandling(integration, companyId);

      if (refreshResult.success) {
        console.log("Token refresh successful", {
          handled_by_other_process: refreshResult.handled_by_other_process,
          newExpiresAt: refreshResult.expiresAt,
          timeUntilExpiry: refreshResult.timeUntilExpiry
        });

        // Update tokenData for subsequent use
        if (refreshResult.refreshed) {
          Object.assign(tokenData, tokenManager.getTokenData(integration));
        } else if (refreshResult.handled_by_other_process) {
          // Reload integration to get updated tokens
          const updatedIntegration = await Integration.findById(integration._id);
          Object.assign(tokenData, tokenManager.getTokenData(updatedIntegration));
        }
      } else {
        console.error("Token refresh failed:", refreshResult.error.message);

        if (refreshResult.needsReauthentication) {
          throw new Error(
            "Your Xero connection needs to be renewed. Please disconnect and connect again."
          );
        } else {
          throw new Error(`Token refresh failed: ${refreshResult.error.message}`);
        }
      }
    }

    // Set token set with current (or newly refreshed) tokens using standardized format
    const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);

    console.log("Setting token set:", {
      hasAccessToken: !!xeroTokenSet.access_token,
      hasRefreshToken: !!xeroTokenSet.refresh_token,
      expiresAt: xeroTokenSet.expires_at ? new Date(xeroTokenSet.expires_at * 1000).toISOString() : null,
      expiresIn: xeroTokenSet.expires_at ?
        Math.floor((xeroTokenSet.expires_at * 1000 - Date.now()) / 1000) + " seconds" : null,
    });

    xero.setTokenSet(xeroTokenSet);

    // Set tenant ID
    console.log("Tenant ID check:", {
      tenantId: tokenData.tenantId,
      hasTenantId: !!tokenData.tenantId,
    });

    // If no tenant ID, try to get one
    if (!tokenData.tenantId) {
      console.log("No tenant ID found, fetching connections");
      const connections = await xero.updateTenants(true);
      console.log("Connections:", connections);

      if (connections && connections.length > 0) {
        // Use the first connection's tenant ID
        const newTenantId = connections[0].tenantId;
        console.log("Found tenant ID:", newTenantId);

        // Save the tenant ID using standardized format
        tokenData.tenantId = newTenantId;
        tokenManager.setTokenData(integration, tokenData);
        await integration.save();

        // Use the new tenant ID
        console.log("Using new tenant ID:", newTenantId);
        const accountsResponse = await xero.accountingApi.getAccounts(newTenantId);
        return res.json(accountsResponse.body.accounts);
      } else {
        throw new Error("No Xero connections found");
      }
    }

    // Get accounts using the tenant ID
    console.log("Getting accounts with tenant ID:", tokenData.tenantId);
    const accountsResponse = await xero.accountingApi.getAccounts(tokenData.tenantId);
    console.log("Accounts retrieved:", {
      count: accountsResponse.body.accounts.length,
      sample: accountsResponse.body.accounts.slice(0, 2),
    });

    return res.json(accountsResponse.body.accounts);
  } catch (error) {
    console.error("\n=== Xero Get Accounts Error ===");
    console.error("Error:", error.message);
    console.error("Stack:", error.stack);
    return res.status(500).json({ error: error.message });
  }
});

// Get account mappings
router.get("/mappings", async (req, res) => {
  try {
    console.log("\n=== Xero Get Mappings Debug Start ===");
    
    // Extract company code from the URL path
    // The URL pattern is /clients/:companyCode/settings/accounting/xero/mappings
    const urlParts = req.originalUrl.split('/');
    const companyCodeIndex = urlParts.indexOf('clients') + 1;
    const companyCode = urlParts[companyCodeIndex] || req.params.companyCode;
    
    console.log("Company Code:", companyCode);

    // Get company from companyCode
    const company = await Company.findOne({
      companyCode: companyCode,
    });
    if (!company) {
      throw new Error("Company not found");
    }

    // Get mappings for this company
    const mappings = await XeroAccountMapping.find({
      company: company._id,
    }).lean();

    console.log("Retrieved mappings:", {
      count: mappings.length,
      mappings: mappings,
    });

    // Map backend keys to frontend keys
    const mappingKeyMap = {
      'BASIC_SALARY': 'salary',
      'COMMISSION': 'commission',
      'BONUS': 'bonus',
      'MEDICAL_AID_LIABILITY': 'medical',
      'PENSION_FUND_TOTAL': 'pension',
      'PAYE_LIABILITY': 'paye',
      'UIF_TOTAL': 'uif',
      'SDL_LIABILITY': 'sdl'
    };

    // Convert to object format for frontend
    const mappingObject = {};
    mappings.forEach((mapping) => {
      const frontendKey = mappingKeyMap[mapping.payrollAccountId] || mapping.payrollAccountId.toLowerCase();
      mappingObject[frontendKey] = mapping.xeroAccountId;
    });

    res.json(mappingObject);
  } catch (error) {
    console.error("Error fetching account mappings:", error);
    res.status(500).json({ error: error.message });
  }
});

// Save account mappings
router.post("/mappings", async (req, res) => {
  try {
    console.log("\n=== Xero Save Mappings Debug Start ===");
    console.log("Received mappings:", req.body);
    
    // Extract company code from the URL path
    // The URL pattern is /clients/:companyCode/settings/accounting/xero/mappings
    const urlParts = req.originalUrl.split('/');
    const companyCodeIndex = urlParts.indexOf('clients') + 1;
    const companyCode = urlParts[companyCodeIndex] || req.params.companyCode;
    
    console.log("Company Code:", companyCode);

    // Get company from companyCode
    const company = await Company.findOne({
      companyCode: companyCode,
    });
    if (!company) {
      throw new Error("Company not found");
    }

    // Transform frontend mapping format to backend format
    const frontendMappings = req.body.mappings || {};
    const backendMappings = {};
    
    // Map frontend keys to backend keys
    const mappingKeyMap = {
      'salary': 'BASIC_SALARY',
      'commission': 'COMMISSION',
      'bonus': 'BONUS',
      'medical': 'MEDICAL_AID_LIABILITY',
      'pension': 'PENSION_FUND_TOTAL',
      'paye': 'PAYE_LIABILITY',
      'uif': 'UIF_TOTAL',
      'sdl': 'SDL_LIABILITY'
    };
    
    // Transform the mappings
    Object.entries(frontendMappings).forEach(([frontendKey, value]) => {
      const backendKey = mappingKeyMap[frontendKey];
      if (backendKey && value) {
        backendMappings[backendKey] = value;
      }
    });
    
    console.log("Transformed mappings:", backendMappings);

    // Validate required mappings
    const requiredItems = [
      "BASIC_SALARY",
      "PAYE_LIABILITY",
      "UIF_TOTAL"
    ];
    const missingRequired = requiredItems.filter((item) => !backendMappings[item]);

    if (missingRequired.length > 0) {
      return res.status(400).json({
        error: "Missing required mappings",
        missingItems: missingRequired,
      });
    }

    // Update or create mappings
    const operations = Object.entries(backendMappings).map(
      ([payrollAccountId, xeroAccountId]) => ({
        updateOne: {
          filter: { company: company._id, payrollAccountId },
          update: {
            $set: {
              xeroAccountId,
              updatedAt: new Date(),
            },
          },
          upsert: true,
        },
      })
    );

    console.log("Performing bulk write operations:", {
      operationCount: operations.length,
      sampleOperation: operations[0],
    });

    const result = await XeroAccountMapping.bulkWrite(operations);

    console.log("Bulk write result:", {
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
      upsertedCount: result.upsertedCount,
    });

    // Verify the mappings were saved
    const savedMappings = await XeroAccountMapping.find({
      company: company._id,
    }).lean();

    console.log("Verified saved mappings:", {
      count: savedMappings.length,
      mappings: savedMappings,
    });

    res.json({
      message: "Mappings saved successfully",
      savedCount: savedMappings.length,
    });
  } catch (error) {
    console.error("Error saving account mappings:", error);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    console.log("\n=== Xero Health Check Start ===");
    const { companyCode } = req.params;

    // Get company and integration
    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting",
    });

    if (!integration) {
      return res.json({
        status: "inactive",
        message: "No Xero integration found",
        timestamp: new Date().toISOString(),
      });
    }

    // Check integration health using standardized token manager
    const tokenData = tokenManager.getTokenData(integration);
    const health = {
      status: integration.status,
      lastSync: integration.lastSync,
      lastError: integration.lastError,
      tokenExpiry: tokenData.expiresAt,
      tenantId: tokenData.tenantId,
      timestamp: new Date().toISOString(),
    };

    // Add time until expiry and refresh status using standardized logic
    health.timeUntilExpiry = tokenManager.getTimeUntilExpiry(integration);
    health.needsRefresh = tokenManager.needsRefresh(integration);

    // If active, verify token works by making a test API call
    if (integration.status === "active") {
      try {
        const xero = getXeroClient();
        await xero.initialize();

        // Set the token set using standardized format
        const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);
        xero.setTokenSet(xeroTokenSet);

        // Try to get a single account to verify connection
        const response = await xero.accountingApi.getAccounts(
          tokenData.tenantId,
          1
        );
        health.connectionTest = "success";
        health.lastSuccessfulTest = new Date().toISOString();

        // Update last sync time
        await Integration.findByIdAndUpdate(integration._id, {
          $set: { lastSync: new Date() },
        });
      } catch (error) {
        health.connectionTest = "failed";
        health.testError = error.message;

        // If test failed, try to refresh token using enhanced error handling
        if (health.needsRefresh) {
          const companyId = company._id.toString();
          const refreshResult = await tokenManager.refreshTokenWithErrorHandling(integration, companyId);

          if (refreshResult.success) {
            health.tokenRefresh = "success";
            health.tokenRefreshTime = new Date().toISOString();
            health.timeUntilExpiry = refreshResult.timeUntilExpiry || tokenManager.getTimeUntilExpiry(integration);

            if (refreshResult.handled_by_other_process) {
              health.tokenRefresh = "handled_by_another_process";
            }
          } else {
            health.tokenRefresh = "failed";
            health.refreshError = refreshResult.error.message;
            health.needsReauthentication = refreshResult.needsReauthentication;
            health.isRetryable = refreshResult.isRetryable;
            health.retryAfter = refreshResult.retryAfter;
          }
        }
      }
    }

    res.json(health);
  } catch (error) {
    console.error("Health check error:", error);
    res.status(500).json({
      status: "error",
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Force token refresh endpoint
router.post("/refresh", async (req, res) => {
  try {
    console.log("\n=== Xero Force Refresh Start ===");
    const { companyCode } = req.params;

    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting",
    });

    if (!integration || integration.status !== "active") {
      throw new Error("No active Xero integration found");
    }

    // Use standardized token manager for refresh
    const companyId = company._id.toString();
    const shouldRefresh = await tokenManager.acquireRefreshMutex(companyId);

    if (!shouldRefresh) {
      return res.json({
        status: "success",
        message: "Token refresh handled by another process",
        timestamp: new Date().toISOString(),
      });
    }

    // Use enhanced error handling for token refresh
    const refreshResult = await tokenManager.refreshTokenWithErrorHandling(integration, companyId);

    if (refreshResult.success) {
      res.json({
        status: "success",
        message: "Token refreshed successfully",
        expiresAt: refreshResult.expiresAt,
        timeUntilExpiry: refreshResult.timeUntilExpiry,
        handled_by_other_process: refreshResult.handled_by_other_process || false,
        timestamp: new Date().toISOString(),
      });
    } else {
      // Return structured error response
      const errorResponse = tokenManager.errorHandler.createErrorResponse(refreshResult.error);
      res.status(refreshResult.needsReauthentication ? 401 : 500).json(errorResponse);
    }
  } catch (error) {
    console.error("Force refresh error:", error);
    res.status(500).json({
      status: "error",
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Helper function to create journal entry payload
const createJournalEntryPayload = (payrollData, mappings, description) => {
  console.log("[Xero Integration] Creating journal entry payload with data:", {
    hasBasicSalary: !!payrollData.basicSalary,
    basicSalaryAmount: payrollData.basicSalary,
    hasPayeLiability: !!payrollData.payeLiability,
    payeLiabilityAmount: payrollData.payeLiability,
    hasUifTotal: !!payrollData.uifTotal,
    uifTotalAmount: payrollData.uifTotal,
    hasSdlLiability: !!payrollData.sdlLiability,
    sdlLiabilityAmount: payrollData.sdlLiability,
  });
  
  console.log("[Xero Integration] Using mappings:", mappings);
  
  const manualJournalLines = [];
  let totalDebit = 0;
  let totalCredit = 0;

  // Helper function to get account code or ID
  const getAccountCode = (mappingKey) => {
    const accountIdOrCode = mappings[mappingKey];
    // If it's a short code (like "720"), use it directly
    // If it's a UUID (like "2e769b24-6529-4d84-9c3d-3a01db51bd80"), it will be converted later
    return accountIdOrCode;
  };

  // Map basic salary (debit)
  if (payrollData.basicSalary && mappings.BASIC_SALARY) {
    manualJournalLines.push({
      lineAmount: payrollData.basicSalary,
      accountCode: getAccountCode('BASIC_SALARY'),
      description: "Basic Salary",
      taxType: "NONE",
      tracking: [],
    });
    totalDebit += payrollData.basicSalary;
    console.log(`[Xero Integration] Added basic salary line: ${payrollData.basicSalary} to account ${mappings.BASIC_SALARY}`);
  } else {
    console.log(`[Xero Integration] Skipping basic salary line - basicSalary: ${payrollData.basicSalary}, mapping: ${mappings.BASIC_SALARY}`);
  }

  // Map PAYE liability (credit)
  if (payrollData.payeLiability && mappings.PAYE_LIABILITY) {
    manualJournalLines.push({
      lineAmount: -payrollData.payeLiability,
      accountCode: getAccountCode('PAYE_LIABILITY'),
      description: "PAYE Liability",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += payrollData.payeLiability;
    console.log(`[Xero Integration] Added PAYE liability line: -${payrollData.payeLiability} to account ${mappings.PAYE_LIABILITY}`);
  } else {
    console.log(`[Xero Integration] Skipping PAYE liability line - payeLiability: ${payrollData.payeLiability}, mapping: ${mappings.PAYE_LIABILITY}`);
  }

  // Map UIF total (credit)
  if (payrollData.uifTotal && mappings.UIF_TOTAL) {
    manualJournalLines.push({
      lineAmount: -payrollData.uifTotal,
      accountCode: getAccountCode('UIF_TOTAL'),
      description: "UIF Total",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += payrollData.uifTotal;
    console.log(`[Xero Integration] Added UIF total line: -${payrollData.uifTotal} to account ${mappings.UIF_TOTAL}`);
  } else {
    console.log(`[Xero Integration] Skipping UIF total line - uifTotal: ${payrollData.uifTotal}, mapping: ${mappings.UIF_TOTAL}`);
  }

  // Map SDL liability (credit)
  if (payrollData.sdlLiability && mappings.SDL_LIABILITY) {
    manualJournalLines.push({
      lineAmount: -payrollData.sdlLiability,
      accountCode: getAccountCode('SDL_LIABILITY'),
      description: "SDL Liability",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += payrollData.sdlLiability;
    console.log(`[Xero Integration] Added SDL liability line: -${payrollData.sdlLiability} to account ${mappings.SDL_LIABILITY}`);
  } else {
    console.log(`[Xero Integration] Skipping SDL liability line - sdlLiability: ${payrollData.sdlLiability}, mapping: ${mappings.SDL_LIABILITY}`);
  }

  // Optional: Map medical aid if present
  if (payrollData.medicalAid && mappings.MEDICAL_AID_LIABILITY) {
    manualJournalLines.push({
      lineAmount: -payrollData.medicalAid,
      accountCode: getAccountCode('MEDICAL_AID_LIABILITY'),
      description: "Medical Aid",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += payrollData.medicalAid;
    console.log(`[Xero Integration] Added medical aid line: -${payrollData.medicalAid} to account ${mappings.MEDICAL_AID_LIABILITY}`);
  } else if (payrollData.medicalAid) {
    console.log(`[Xero Integration] Skipping medical aid line - medicalAid: ${payrollData.medicalAid}, mapping: ${mappings.MEDICAL_AID_LIABILITY}`);
  }

  // Optional: Map pension fund if present
  if (payrollData.pensionFund && mappings.PENSION_FUND_TOTAL) {
    manualJournalLines.push({
      lineAmount: -payrollData.pensionFund,
      accountCode: getAccountCode('PENSION_FUND_TOTAL'),
      description: "Pension Fund",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += payrollData.pensionFund;
    console.log(`[Xero Integration] Added pension fund line: -${payrollData.pensionFund} to account ${mappings.PENSION_FUND_TOTAL}`);
  } else if (payrollData.pensionFund) {
    console.log(`[Xero Integration] Skipping pension fund line - pensionFund: ${payrollData.pensionFund}, mapping: ${mappings.PENSION_FUND_TOTAL}`);
  }
  
  // Add net salary line if needed to balance the journal
  const netSalary = totalDebit - totalCredit;
  if (netSalary > 0 && mappings.NET_SALARY) {
    manualJournalLines.push({
      lineAmount: -netSalary,
      accountCode: mappings.NET_SALARY,
      description: "Net Salary",
      taxType: "NONE",
      tracking: [],
    });
    totalCredit += netSalary;
    console.log(`[Xero Integration] Added balancing net salary line: -${netSalary} to account ${mappings.NET_SALARY}`);
  }
  
  console.log(`[Xero Integration] Journal entry created with ${manualJournalLines.length} lines, total debit: ${totalDebit}, total credit: ${totalCredit}`);

  return {
    narration: description || `Payroll Journal Entry - ${new Date().toISOString().split("T")[0]}`,
    manualJournalLines: manualJournalLines,
    date: new Date().toISOString().split("T")[0],
  };
};

// Sync payroll to Xero
router.post("/:companyCode/sync-payroll", async (req, res) => {
  const { payrollId } = req.body; // Move this to the top level scope
  
  try {
    console.log("\n=== Xero Payroll Sync Start ===");
    const { companyCode } = req.params;
    const { payrollData, description } = req.body;

    console.log("Payroll sync request:", {
      companyCode,
      payrollId,
      description,
      payrollDataSummary: {
        totalAmount: payrollData?.basicSalary,
        employeeCount: payrollData?.employeeCount,
        period: payrollData?.period,
      },
    });

    // Validate request
    if (!payrollId || !payrollData) {
      throw new Error("Missing required payroll data");
    }

    // Get company and check integration
    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting",
      status: "active",
    });

    if (!integration) {
      throw new Error("No active Xero integration found");
    }
    
    // Log integration details
    console.log("[Xero Integration] Found integration:", {
      id: integration._id,
      status: integration.status,
      hasCredentials: !!integration.credentials,
      credentialsKeys: integration.credentials ? Array.from(integration.credentials.keys()) : [],
      hasAccessToken: integration.credentials && integration.credentials.has('accessToken'),
      hasRefreshToken: integration.credentials && integration.credentials.has('refreshToken'),
      hasTenantId: integration.credentials && integration.credentials.has('tenantId'),
      expiresAt: integration.credentials && integration.credentials.has('expiresAt') ? 
        integration.credentials.get('expiresAt') : 'not set'
    });

    // Fetch complete payroll data from the database
    const completePayrollData = await PayRun.findById(payrollId).lean();
    if (!completePayrollData) {
      throw new Error("Payroll data not found");
    }
    
    console.log("[Xero Integration] Fetched complete payroll data:", {
      id: completePayrollData._id,
      hasFinancialData: !!(completePayrollData.basicSalary || completePayrollData.totalSalary),
      totalSalary: completePayrollData.totalSalary,
      basicSalary: completePayrollData.basicSalary,
      payeLiability: completePayrollData.payeLiability,
      uifTotal: completePayrollData.uifTotal,
      sdlLiability: completePayrollData.sdlLiability,
      employeeCount: completePayrollData.employees?.length || 0
    });
    
    // If no financial data is available, calculate totals from employee data
    if (!completePayrollData.basicSalary && completePayrollData.employees && completePayrollData.employees.length > 0) {
      console.log("[Xero Integration] Calculating financial totals from employee data");
      
      completePayrollData.basicSalary = completePayrollData.employees.reduce((sum, emp) => sum + (emp.basicSalary || 0), 0);
      completePayrollData.payeLiability = completePayrollData.employees.reduce((sum, emp) => sum + (emp.paye || 0), 0);
      completePayrollData.uifTotal = completePayrollData.employees.reduce((sum, emp) => sum + (emp.uif || 0), 0);
      completePayrollData.sdlLiability = completePayrollData.employees.reduce((sum, emp) => sum + (emp.sdl || 0), 0);
      
      console.log("[Xero Integration] Calculated totals:", {
        basicSalary: completePayrollData.basicSalary,
        payeLiability: completePayrollData.payeLiability,
        uifTotal: completePayrollData.uifTotal,
        sdlLiability: completePayrollData.sdlLiability
      });
    }
    
    // If still no financial data, create a dummy entry for testing
    if (!completePayrollData.basicSalary) {
      console.log("[Xero Integration] No financial data found, creating dummy data for testing");
      completePayrollData.basicSalary = 10000;
      completePayrollData.payeLiability = 2000;
      completePayrollData.uifTotal = 100;
      completePayrollData.sdlLiability = 100;
    }

    // Get account mappings
    const mappings = await XeroAccountMapping.find({
      company: company._id,
    }).lean();

    if (!mappings.length) {
      throw new Error("No account mappings found");
    }

    // Convert mappings array to object
    const mappingObject = mappings.reduce((acc, mapping) => {
      acc[mapping.payrollAccountId] = mapping.xeroAccountId;
      return acc;
    }, {});

    // Initialize Xero client
    const xero = getXeroClient();
    await xero.initialize();

    // Function to refresh token
    const refreshToken = async () => {
      try {
        console.log("[Xero Integration] Refreshing token...");
        
        // Initialize the client
        await xero.initialize();
        
        // Set the token set with the correct format
        const accessToken = integration.credentials.get('accessToken');
        const refreshToken = integration.credentials.get('refreshToken');
        const expiresAt = integration.credentials.get('expiresAt');
        const idToken = integration.credentials.get('idToken');
        const scope = integration.credentials.get('scope');
        const tokenType = integration.credentials.get('tokenType') || 'Bearer';
        
        console.log("[Xero Integration] Setting token set with:", {
          hasAccessToken: !!accessToken,
          accessTokenLength: accessToken ? accessToken.length : 0,
          hasRefreshToken: !!refreshToken,
          refreshTokenLength: refreshToken ? refreshToken.length : 0,
          hasIdToken: !!idToken,
          hasScope: !!scope,
          expiresAt: expiresAt
        });
        
        await xero.setTokenSet({
          id_token: idToken,
          access_token: accessToken,
          refresh_token: refreshToken,
          token_type: tokenType,
          scope: scope,
          expires_at: Math.floor(new Date(expiresAt).getTime() / 1000)
        });
        
        // Refresh the token
        const newTokenSet = await xero.refreshToken();
        console.log("[Xero Integration] Token refreshed successfully");
        
        // Update integration with new tokens using the set method
        integration.credentials.set('accessToken', newTokenSet.access_token);
        integration.credentials.set('refreshToken', newTokenSet.refresh_token);
        integration.credentials.set('expiresAt', new Date(
          Date.now() + newTokenSet.expires_in * 1000
        ).toString());
        
        // Update other token properties if present
        if (newTokenSet.id_token) {
          integration.credentials.set('idToken', newTokenSet.id_token);
        }
        if (newTokenSet.scope) {
          integration.credentials.set('scope', newTokenSet.scope);
        }
        if (newTokenSet.token_type) {
          integration.credentials.set('tokenType', newTokenSet.token_type);
        }
        
        await integration.save();
        
        console.log("[Xero Integration] Integration updated with new tokens, expires:", 
          integration.credentials.get('expiresAt')
        );
        return newTokenSet;
      } catch (error) {
        console.error("[Xero Integration] Token refresh error:", error);
        throw error;
      }
    };

    // Function to attempt journal creation with retries
    const createJournalWithRetry = async (attempt = 1, maxAttempts = 3) => {
      try {
        // Get credentials
        const accessToken = integration.credentials.get('accessToken');
        const refreshToken = integration.credentials.get('refreshToken');
        const tenantId = integration.credentials.get('tenantId');
        const expiresAt = integration.credentials.get('expiresAt');
        
        console.log("[Xero Integration] Using credentials:", {
          hasAccessToken: !!accessToken,
          accessTokenLength: accessToken ? accessToken.length : 0,
          hasTenantId: !!tenantId,
          tenantId: tenantId,
          expiresAt: expiresAt
        });
        
        if (!accessToken || !tenantId) {
          throw new Error("Missing required credentials");
        }

        // First, fetch accounts from Xero to get the account codes
        console.log("[Xero Integration] Fetching accounts from Xero to get account codes...");
        const accountsResponse = await axios({
          method: 'get',
          url: 'https://api.xero.com/api.xro/2.0/Accounts',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Xero-Tenant-Id': tenantId,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        // Create a mapping from account ID to account code
        const accountIdToCodeMap = {};
        if (accountsResponse.data && accountsResponse.data.Accounts) {
          accountsResponse.data.Accounts.forEach(account => {
            accountIdToCodeMap[account.AccountID] = account.Code;
          });
          
          console.log("[Xero Integration] Fetched account codes:", {
            accountCount: accountsResponse.data.Accounts.length,
            mappedCount: Object.keys(accountIdToCodeMap).length
          });
        } else {
          console.log("[Xero Integration] No accounts found in response:", accountsResponse.data);
        }

        // Create journal entry payload
        const journalEntry = createJournalEntryPayload(
          completePayrollData,
          mappingObject,
          description ||
            `Payroll Journal Entry - ${new Date().toISOString().split("T")[0]}`
        );

        console.log(
          "Attempting to create journal entry (attempt " + attempt + "):",
          {
            narration: journalEntry.narration,
            lineCount: journalEntry.manualJournalLines.length,
            date: journalEntry.date,
            totalDebit: journalEntry.manualJournalLines.reduce((sum, line) => line.lineAmount > 0 ? sum + line.lineAmount : sum, 0),
            totalCredit: journalEntry.manualJournalLines.reduce((sum, line) => line.lineAmount < 0 ? sum + Math.abs(line.lineAmount) : sum, 0),
          }
        );
        
        // Create the API journal entry object
        const apiJournalEntry = {
          Date: journalEntry.date,
          Narration: journalEntry.narration.substring(0, 200), // Limit narration length
          Status: "DRAFT",
          LineAmountTypes: "Exclusive",
          ManualJournalLines: journalEntry.manualJournalLines.map(line => {
            // Try to get the account code from the mapping, or use the account ID as fallback
            const accountId = line.accountCode;
            const accountCode = accountIdToCodeMap[accountId] || accountId;
            
            console.log(`[Xero Integration] Mapping account ID ${accountId} to code ${accountCode}`);
            
            return {
              LineAmount: line.lineAmount,
              AccountCode: accountCode,
              Description: line.description.substring(0, 200), // Limit description length
              TaxType: "NONE"
            };
          })
        };
        
        // Verify that the journal entry is balanced
        const totalDebit = apiJournalEntry.ManualJournalLines.reduce(
          (sum, line) => line.LineAmount > 0 ? sum + line.LineAmount : sum, 
          0
        );
        
        const totalCredit = apiJournalEntry.ManualJournalLines.reduce(
          (sum, line) => line.LineAmount < 0 ? sum + Math.abs(line.LineAmount) : sum, 
          0
        );
        
        console.log("[Xero Integration] Journal balance check:", {
          totalDebit,
          totalCredit,
          difference: Math.abs(totalDebit - totalCredit),
          isBalanced: Math.abs(totalDebit - totalCredit) < 0.01
        });
        
        // If not balanced, adjust the largest line to balance the entry
        if (Math.abs(totalDebit - totalCredit) >= 0.01) {
          console.log("[Xero Integration] Journal is not balanced, adjusting...");
          
          // Find the largest line
          let largestLine = apiJournalEntry.ManualJournalLines[0];
          apiJournalEntry.ManualJournalLines.forEach(line => {
            if (Math.abs(line.LineAmount) > Math.abs(largestLine.LineAmount)) {
              largestLine = line;
            }
          });
          
          // Adjust the largest line to balance the entry
          if (totalDebit > totalCredit) {
            // Need to increase credit
            if (largestLine.LineAmount < 0) {
              // Adjust the largest credit line
              largestLine.LineAmount -= (totalDebit - totalCredit);
            } else {
              // Find the largest credit line
              let largestCreditLine = null;
              apiJournalEntry.ManualJournalLines.forEach(line => {
                if (line.LineAmount < 0 && (!largestCreditLine || Math.abs(line.LineAmount) > Math.abs(largestCreditLine.LineAmount))) {
                  largestCreditLine = line;
                }
              });
              
              if (largestCreditLine) {
                largestCreditLine.LineAmount -= (totalDebit - totalCredit);
              } else {
                // No credit line found, add a new one
                apiJournalEntry.ManualJournalLines.push({
                  LineAmount: -(totalDebit - totalCredit),
                  AccountCode: apiJournalEntry.ManualJournalLines[0].AccountCode,
                  Description: "Balancing Entry",
                  TaxType: "NONE"
                });
              }
            }
          } else {
            // Need to increase debit
            if (largestLine.LineAmount > 0) {
              // Adjust the largest debit line
              largestLine.LineAmount += (totalCredit - totalDebit);
            } else {
              // Find the largest debit line
              let largestDebitLine = null;
              apiJournalEntry.ManualJournalLines.forEach(line => {
                if (line.LineAmount > 0 && (!largestDebitLine || line.LineAmount > largestDebitLine.LineAmount)) {
                  largestDebitLine = line;
                }
              });
              
              if (largestDebitLine) {
                largestDebitLine.LineAmount += (totalCredit - totalDebit);
              } else {
                // No debit line found, add a new one
                apiJournalEntry.ManualJournalLines.push({
                  LineAmount: (totalCredit - totalDebit),
                  AccountCode: apiJournalEntry.ManualJournalLines[0].AccountCode,
                  Description: "Balancing Entry",
                  TaxType: "NONE"
                });
              }
            }
          }
          
          // Verify balance again
          const newTotalDebit = apiJournalEntry.ManualJournalLines.reduce(
            (sum, line) => line.LineAmount > 0 ? sum + line.LineAmount : sum, 
            0
          );
          
          const newTotalCredit = apiJournalEntry.JournalLines.reduce(
            (sum, line) => line.LineAmount < 0 ? sum + Math.abs(line.LineAmount) : sum, 
            0
          );
          
          console.log("[Xero Integration] Journal balance after adjustment:", {
            totalDebit: newTotalDebit,
            totalCredit: newTotalCredit,
            difference: Math.abs(newTotalDebit - newTotalCredit),
            isBalanced: Math.abs(newTotalDebit - newTotalCredit) < 0.01
          });
        }
        
        // Prepare request payload
        const requestPayload = {
          ManualJournals: [apiJournalEntry]
        };
        
        console.log("[Xero Integration] Request payload:", JSON.stringify(requestPayload, null, 2));

        // Make the API call using axios
        console.log("[Xero Integration] Making API call to create journal entry...");
        const response = await axios({
          method: 'post',
          url: 'https://api.xero.com/api.xro/2.0/ManualJournals',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Xero-Tenant-Id': tenantId,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          data: requestPayload
        });
        
        console.log("[Xero Integration] Journal entry created successfully:", {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        });

        // Return the response in a format compatible with the existing code
        return {
          body: {
            ManualJournals: [{
              ManualJournalID: response.data.ManualJournals[0].ManualJournalID,
              Date: response.data.ManualJournals[0].Date
            }]
          },
          response: {
            statusCode: response.status
          }
        };
      } catch (error) {
        console.error(`Journal creation attempt ${attempt} failed:`, error.message);
        
        // Log more details about the error
        if (error.response) {
          console.error("Response error details:", {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data
          });
          
          // Log the full error details if available
          if (error.response.data && error.response.data.Elements) {
            console.error("Validation errors:", JSON.stringify(error.response.data.Elements, null, 2));
          }
        }

        // Handle token refresh if needed
        if (error.response?.status === 401 && attempt < maxAttempts) {
          console.log("Attempting token refresh...");
          await refreshToken();
          return createJournalWithRetry(attempt + 1, maxAttempts);
        }

        // Retry with exponential backoff
        if (attempt < maxAttempts) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          console.log(`Retrying in ${delay}ms...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
          return createJournalWithRetry(attempt + 1, maxAttempts);
        }

        throw error;
      }
    };

    // Attempt to create journal with retries
    const response = await createJournalWithRetry();

    console.log("Xero journal entry created:", {
      success: true,
      journalId: response.body.ManualJournals[0].ManualJournalID,
      date: response.body.ManualJournals[0].Date,
    });

    // Update payroll record with Xero sync status
    await PayRun.findByIdAndUpdate(payrollId, {
      $set: {
        xeroSync: {
          synced: true,
          syncedAt: new Date(),
          journalEntryId: response.body.ManualJournals[0].ManualJournalID,
          status: "completed",
        },
      },
    });

    res.json({
      success: true,
      message: "Successfully synced payroll to Xero",
      journalEntryId: response.body.ManualJournals[0].ManualJournalID,
    });
  } catch (error) {
    console.error("Payroll sync error:", error);

    // Update payroll record with error status
    if (payrollId) {
      try {
        await PayRun.findByIdAndUpdate(payrollId, {
          $set: {
            xeroSync: {
              synced: false,
              syncedAt: new Date(),
              status: "failed",
              error: error.message,
            },
          },
        });
      } catch (updateError) {
        console.error("Error updating payroll status:", updateError);
      }
    }

    res.status(500).json({
      success: false,
      error: error.message || "Failed to sync payroll to Xero",
      details: error.response?.data || {},
    });
  }
});

// Get sync status
router.get("/sync-status/:payrollId", async (req, res) => {
  try {
    const { companyCode, payrollId } = req.params;

    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    const payroll = await PayRun.findOne({
      _id: payrollId,
      company: company._id,
    });

    if (!payroll) {
      throw new Error("Payroll not found");
    }

    res.json({
      payrollId: payroll._id,
      syncStatus: payroll.xeroSync || { synced: false, status: "not_synced" },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching sync status:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Check integration status
router.get("/status/:companyCode", async (req, res) => {
  try {
    const { companyCode } = req.params;
    
    // Find the company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }
    
    // Find the integration
    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting"
    });
    
    if (!integration) {
      return res.json({
        status: "inactive",
        message: "No Xero integration found"
      });
    }
    
    // Return the integration status
    return res.json({
      status: integration.status,
      lastSync: integration.lastSync,
      hasCredentials: !!integration.credentials,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error checking integration status:", error);
    return res.status(500).json({ error: error.message });
  }
});

// Create a journal entry in Xero
const createJournalEntry = async (req, res) => {
  console.log("\n=== Xero Create Journal Entry ===");
  const companyCode = extractCompanyCode(req);
  console.log("Company Code:", companyCode);

  try {
    // Find the company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.log("Company not found");
      return res.status(404).json({ error: "Company not found" });
    }

    // Find the integration
    const integration = await Integration.findOne({
      company: company._id,
      provider: "xero",
      type: "accounting",
      status: "active",
    });

    if (!integration) {
      console.log("No active Xero integration found");
      return res.status(404).json({ error: "No active Xero integration found" });
    }

    // Extract credentials
    const accessToken = integration.credentials.get('accessToken');
    const refreshToken = integration.credentials.get('refreshToken');
    const tenantId = integration.credentials.get('tenantId');
    const expiresAt = integration.credentials.get('expiresAt');

    if (!accessToken || !tenantId) {
      console.log("Missing required credentials");
      return res.status(400).json({ error: "Missing required credentials" });
    }

    console.log("Using credentials:", {
      hasAccessToken: !!accessToken,
      accessTokenLength: accessToken ? accessToken.length : 0,
      hasTenantId: !!tenantId,
      tenantId: tenantId
    });

    // Get journal data from request body
    const { journalData } = req.body;
    if (!journalData || !journalData.JournalLines || journalData.JournalLines.length < 2) {
      console.log("Invalid journal data:", journalData);
      return res.status(400).json({ error: "Invalid journal data. Must include at least 2 journal lines." });
    }

    // Ensure required fields are present
    if (!journalData.Narration) {
      journalData.Narration = "Journal Entry";
    }
    if (!journalData.Date) {
      journalData.Date = new Date().toISOString().split("T")[0];
    }
    if (!journalData.Status) {
      journalData.Status = "DRAFT";
    }
    if (!journalData.LineAmountTypes) {
      journalData.LineAmountTypes = "Exclusive";
    }

    // Prepare request payload
    const requestPayload = {
      ManualJournals: [journalData]
    };

    console.log("Journal entry to create:", {
      narration: journalData.Narration,
      lineCount: journalData.JournalLines.length,
      date: journalData.Date,
      status: journalData.Status,
      lineAmountTypes: journalData.LineAmountTypes,
      totalDebit: journalData.JournalLines.reduce((sum, line) => line.LineAmount > 0 ? sum + line.LineAmount : sum, 0),
      totalCredit: journalData.JournalLines.reduce((sum, line) => line.LineAmount < 0 ? sum + Math.abs(line.LineAmount) : sum, 0)
    });

    console.log("Request payload:", JSON.stringify(requestPayload, null, 2));

    // Make the API call using axios
    console.log("Making API call to create journal entry...");
    const response = await axios({
      method: 'post',
      url: 'https://api.xero.com/api.xro/2.0/ManualJournals',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Xero-Tenant-Id': tenantId,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      data: requestPayload
    });

    console.log("Journal entry created successfully:", {
      status: response.status,
      statusText: response.statusText,
      data: response.data
    });

    // Return the created journal entry
    return res.status(200).json({
      success: true,
      journalEntry: response.data
    });
  } catch (error) {
    console.error("Error creating journal entry:", error.message);
    if (error.response) {
      console.error("Response error details:", {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }
    return res.status(500).json({ error: "Failed to create journal entry", details: error.message });
  }
};

// Add the route for creating journal entries
router.post("/clients/:companyCode/journal", createJournalEntry);

module.exports = router;
