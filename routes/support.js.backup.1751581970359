const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../middleware/auth");
const nodemailer = require("nodemailer");
const csrf = require("csurf");
const cookieParser = require("cookie-parser");
const Company = require("../models/Company");
const crypto = require('crypto');
const SupportAccess = require('../models/SupportAccess');
const transporter = require('../config/emailConfig');
const { getAppUrl } = require('../utils/url');

// Configure middleware
router.use(cookieParser());
router.use(express.urlencoded({ extended: true }));
router.use(express.json());

// CSRF Protection setup
const csrfProtection = csrf({
  cookie: {
    key: "_csrf",
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  },
});

// Debug middleware for support routes
router.use((req, res, next) => {
  console.log("\n=== Support Route Debug ===");
  console.log({
    path: req.path,
    fullUrl: req.originalUrl,
    method: req.method,
    baseUrl: req.baseUrl,
    user: req.user
      ? {
          id: req.user._id,
          email: req.user.email,
        }
      : null,
    isAuthenticated: req.isAuthenticated(),
    sessionID: req.sessionID,
  });
  next();
});

// Make CSRF token available to all views
router.use((req, res, next) => {
  res.locals.csrfToken = req.csrfToken();
  next();
});

// Enhanced Navigation Debug Middleware - Add at the top of middleware chain
router.use((req, res, next) => {
  console.log("\n=== Support Navigation Chain Debug ===");
  console.log({
    timestamp: new Date().toISOString(),
    stage: "Navigation Chain",
    request: {
      originalUrl: req.originalUrl,
      baseUrl: req.baseUrl,
      path: req.path,
      method: req.method,
      headers: {
        referer: req.headers.referer,
        host: req.headers.host,
      },
    },
    user: req.user
      ? {
          id: req.user._id,
          email: req.user.email,
          currentCompany: req.user.currentCompany,
          companyCode: req.user.currentCompany?.companyCode,
        }
      : null,
    navigation: {
      previousUrl: req.headers.referer,
      intendedDestination: req.originalUrl,
      isClientRoute: req.originalUrl.startsWith("/clients"),
      isDashboardRoute: req.originalUrl === "/dashboard",
      isRootRoute: req.originalUrl === "/",
    },
    session: {
      id: req.sessionID,
      isAuthenticated: req.isAuthenticated(),
      hasFlash: !!req.flash && Object.keys(req.flash()).length > 0,
      flashMessages: req.flash(),
    },
    companyContext: {
      hasCompany: !!req.user?.currentCompany,
      companyId: req.user?.currentCompany?._id,
      companyCode: req.user?.currentCompany?.companyCode,
      companyName: req.user?.currentCompany?.name,
    },
  });

  // Enhance redirect to track navigation chain
  const originalRedirect = res.redirect;
  res.redirect = function (url) {
    console.log("\n=== Support Navigation Redirect Chain ===");
    console.log({
      timestamp: new Date().toISOString(),
      stage: "Pre-Redirect",
      navigationChain: {
        source: req.headers.referer,
        current: req.originalUrl,
        destination: url,
        isClientRedirect: url.startsWith("/clients"),
        isDashboardRedirect: url === "/dashboard",
        isRootRedirect: url === "/",
      },
      companyContext: {
        hasCompany: !!req.user?.currentCompany,
        companyId: req.user?.currentCompany?._id,
        companyCode: req.user?.currentCompany?.companyCode,
        companyName: req.user?.currentCompany?.name,
      },
      routeMatching: {
        matchedInSupport: router.stack.some(
          (layer) =>
            layer.route?.path === req.path &&
            layer.route?.methods[req.method.toLowerCase()]
        ),
        availableRoutes: router.stack
          .filter((layer) => layer.route)
          .map((layer) => ({
            path: layer.route.path,
            methods: Object.keys(layer.route.methods),
          })),
      },
      session: {
        id: req.sessionID,
        hasFlash: !!req.flash && Object.keys(req.flash()).length > 0,
        flashMessages: req.flash(),
      },
    });

    return originalRedirect.call(this, url);
  };

  next();
});

// Add this before other middleware
router.use((req, res, next) => {
  console.log("\n=== Support Route Entry Debug ===");
  console.log({
    timestamp: new Date().toISOString(),
    stage: "Route Entry",
    request: {
      originalUrl: req.originalUrl,
      baseUrl: req.baseUrl,
      path: req.path,
      method: req.method,
      query: req.query,
      params: req.params,
      headers: {
        referer: req.headers.referer,
        host: req.headers.host,
        "x-requested-with": req.headers["x-requested-with"],
      },
    },
    navigation: {
      source: req.headers.referer,
      destination: req.originalUrl,
      intendedPath: req.path,
      isExternalRedirect:
        req.headers.referer &&
        new URL(req.headers.referer).pathname !== "/support",
    },
    session: {
      id: req.sessionID,
      isAuthenticated: req.isAuthenticated(),
      hasFlash: !!req.flash && Object.keys(req.flash()).length > 0,
      flashMessages: req.flash(),
    },
  });

  // Enhanced navigation tracking
  const originalRedirect = res.redirect;
  res.redirect = function (url) {
    console.log("\n=== Support Navigation Redirect Chain ===");
    console.log({
      timestamp: new Date().toISOString(),
      stage: "Pre-Redirect",
      navigationChain: {
        source: req.headers.referer,
        current: req.originalUrl,
        destination: url,
        isClientRedirect: url.startsWith("/clients"),
        isDashboardRedirect: url === "/dashboard",
        isRootRedirect: url === "/",
      },
      routeAnalysis: {
        matchedInSupport: router.stack.some(
          (layer) =>
            layer.route?.path === req.path &&
            layer.route?.methods[req.method.toLowerCase()]
        ),
        availableRoutes: router.stack
          .filter((layer) => layer.route)
          .map((layer) => ({
            path: layer.route.path,
            methods: Object.keys(layer.route.methods),
            matched: layer.match && layer.match(req.path),
          })),
        requestPath: req.path,
        requestMethod: req.method,
      },
      session: {
        id: req.sessionID,
        hasFlash: !!req.flash && Object.keys(req.flash()).length > 0,
        flashMessages: req.flash(),
        isAuthenticated: req.isAuthenticated(),
      },
    });

    // Log the complete navigation state
    console.log("\n=== Support Navigation State ===");
    console.log({
      timestamp: new Date().toISOString(),
      stage: "Navigation State",
      app: {
        baseUrl: req.baseUrl,
        mountPath: router.mountpath,
        parentApp: req.app ? "Present" : "Missing",
      },
      request: {
        originalUrl: req.originalUrl,
        path: req.path,
        method: req.method,
      },
      user: req.user
        ? {
            id: req.user._id,
            email: req.user.email,
            role: req.user.roleName,
          }
        : null,
      redirect: {
        url,
        isExternal: !url.startsWith("/"),
        isClientRoute: url.startsWith("/clients"),
        isDashboard: url === "/dashboard",
      },
    });

    return originalRedirect.call(this, url);
  };

  next();
});

// @route   GET /support
// @desc    Display support page
// @access  Private
router.get("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log("\n=== Support GET Route Debug ===");
  try {
    // Get the user's current company with full details
    const company = await Company.findById(req.user.currentCompany);

    if (!company) {
      console.log("Company not found, redirecting to /clients");
      req.flash("error", "Company not found");
      return res.redirect("/clients");
    }

    // Get active support access tokens
    const activeTokens = await SupportAccess.find({
      company: company._id,
      isRevoked: false,
      expiresAt: { $gt: new Date() }
    }).sort({ createdAt: -1 });

    console.log("Attempting to render support page with company:", {
      id: company._id,
      code: company.companyCode,
      name: company.name,
      activeTokens: activeTokens.length
    });

    res.render("support", {
      title: "Support",
      user: req.user,
      company: company,
      currentPath: req.path,
      csrfToken: req.csrfToken(),
      messages: req.flash(),
      activeTokens: activeTokens
    });
  } catch (error) {
    console.error("Error in support route:", error);
    req.flash("error", "Something went wrong");
    return res.redirect("/dashboard");
  }
});

// @route   POST /support/send
// @desc    Handle support message submission
// @access  Private
router.post("/send", ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log("\n=== Support Email Process Start ===");
  try {
    const { subject, message } = req.body;
    const user = req.user;
    const company = user.currentCompany?.name || "Not specified";

    // Generate request ID first
    const requestId = `REQ-${Date.now()}`;
    console.log("0. Generated Request ID:", {
      timestamp: new Date().toISOString(),
      requestId,
      sessionId: req.sessionID,
    });

    // Log request details
    console.log("1. Request Details:", {
      timestamp: new Date().toISOString(),
      requestId,
      sessionId: req.sessionID,
      user: {
        id: user._id,
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        company,
      },
      subject,
      messageLength: message.length,
      body: {
        hasSubject: !!subject,
        hasMessage: !!message,
        subjectLength: subject?.length,
        messagePreview: message?.substring(0, 50) + "...",
      },
    });

    // Create email content with requestId
    const emailContent = `
Support Request from Panda User

From: ${user.email}
Name: ${user.firstName} ${user.lastName}
Company: ${company}
Subject: ${subject}
Request ID: ${requestId}

Message:
${message}

---
Sent from Panda Support System
Reference: ${requestId}
    `.trim();

    console.log("2. Email Content Preview:", {
      timestamp: new Date().toISOString(),
      requestId,
      contentLength: emailContent.length,
      hasRequestId: emailContent.includes(requestId),
      sections: {
        header: "Present",
        userInfo: "Present",
        message: "Present",
        footer: "Present",
      },
    });

    // Check environment variables
    const envDebug = {
      timestamp: new Date().toISOString(),
      hasSmtpPass: !!process.env.SMTP_PASS,
      smtpPassLength: process.env.SMTP_PASS ? process.env.SMTP_PASS.length : 0,
      envKeys: Object.keys(process.env).filter((key) =>
        key.startsWith("SMTP_")
      ),
      nodeEnv: process.env.NODE_ENV,
      envVarsLoaded: !!process.env,
      dotenvPath: process.env.DOTENV_PATH || "Not specified",
      processEnvKeys: Object.keys(process.env).length,
      dotEnvLocation: require("path").resolve(process.cwd(), ".env"),
      envFileContents: require("fs").existsSync(".env")
        ? require("fs")
            .readFileSync(".env", "utf8")
            .split("\n")
            .filter((line) => line.startsWith("SMTP_"))
            .map((line) => line.split("=")[0])
        : [],
    };

    console.log("2. Environment Check:", envDebug);
    console.log("2a. Environment Load Path:", {
      timestamp: new Date().toISOString(),
      currentWorkingDir: process.cwd(),
      nodeModulesExists: require("fs").existsSync("./node_modules"),
      dotenvExists: require("fs").existsSync(".env"),
      dotenvExampleExists: require("fs").existsSync(".env.example"),
      absoluteEnvPath: require("path").resolve(process.cwd(), ".env"),
      envLoadAttempt: require("dotenv").config({ path: ".env" }),
    });

    console.log("2b. Email Configuration Check:", {
      timestamp: new Date().toISOString(),
      fromAddress: {
        email: user.email,
        configured: true,
        type: "User Email",
        userName: `${user.firstName} ${user.lastName}`.trim(),
      },
      toAddress: {
        email: "<EMAIL>",
        configured: true,
        type: "Support Inbox",
      },
      smtpDetails: {
        host: "smtppro.zoho.com",
        port: 587,
        secure: false,
        authUser: "<EMAIL>",
        hasAuthPass: !!process.env.SMTP_PASS,
        authPassLength: process.env.SMTP_PASS?.length || 0,
      },
    });

    // SMTP Configuration for Zoho
    const smtpConfig = {
      host: process.env.EMAIL_HOST || "smtppro.zoho.com",
      port: parseInt(process.env.EMAIL_PORT || "587"),
      secure: process.env.EMAIL_SECURE === "true",
      auth: {
        user: process.env.EMAIL_USER || "<EMAIL>",
        pass: process.env.EMAIL_PASS,
      },
      debug: true,
      logger: true,
    };

    // Enhanced environment logging
    console.log("Environment Variables Check:", {
      timestamp: new Date().toISOString(),
      emailHost: process.env.EMAIL_HOST,
      emailPort: process.env.EMAIL_PORT,
      emailUser: process.env.EMAIL_USER,
      emailPass: process.env.EMAIL_PASS ? "Present" : "Missing",
      emailPassLength: process.env.EMAIL_PASS?.length,
      envFile: require("fs").existsSync(".env") ? "Found" : "Missing",
      envPath: require("path").resolve(process.cwd(), ".env"),
    });

    // Log SMTP configuration (safely)
    const smtpDebug = {
      timestamp: new Date().toISOString(),
      host: smtpConfig.host,
      port: smtpConfig.port,
      user: smtpConfig.auth.user,
      secure: smtpConfig.secure,
      hasPassword: !!smtpConfig.auth.pass,
      passwordLength: smtpConfig.auth.pass ? smtpConfig.auth.pass.length : 0,
      tls: smtpConfig.tls,
      authMethod: "PLAIN",
      requiresAuth: true,
      usingTLS: true,
      zohoSpecific: {
        isZohoHost: smtpConfig.host.includes("zoho"),
        recommendedPort: 587,
        portMatch: smtpConfig.port === 587,
        tlsRecommended: true,
        tlsConfigured: !smtpConfig.secure && !!smtpConfig.tls,
      },
    };

    console.log("3. SMTP Configuration:", smtpDebug);
    console.log("3a. SMTP Connection Details:", {
      timestamp: new Date().toISOString(),
      dnsResolved: await new Promise((resolve) => {
        require("dns").resolve(smtpConfig.host, (err, addresses) => {
          resolve({
            success: !err,
            addresses: err ? null : addresses,
            error: err ? err.message : null,
          });
        });
      }),
      portOpen: await new Promise((resolve) => {
        const net = require("net");
        const socket = new net.Socket();
        socket.setTimeout(5000);
        socket.on("connect", () => {
          socket.destroy();
          resolve(true);
        });
        socket.on("timeout", () => {
          socket.destroy();
          resolve(false);
        });
        socket.on("error", () => {
          socket.destroy();
          resolve(false);
        });
        socket.connect(smtpConfig.port, smtpConfig.host);
      }),
    });

    // Create transporter with detailed logging
    console.log("4. Creating email transporter...");
    const transporter = nodemailer.createTransport(smtpConfig);

    // Add transport event listeners
    transporter.on("token", (token) => {
      console.log("Transport token update:", token);
    });

    transporter.on("log", (log) => {
      console.log("Transport log:", log);
    });

    // Verify SMTP connection with timeout
    console.log("5. Verifying SMTP connection...");
    try {
      const verification = await Promise.race([
        transporter.verify(),
        new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("SMTP verification timeout")),
            10000
          )
        ),
      ]);
      console.log("SMTP Verification Success:", verification);
    } catch (verifyError) {
      console.error("SMTP Verification Failed:", {
        error: verifyError.message,
        code: verifyError.code,
        command: verifyError.command,
        stack: verifyError.stack,
        authData: {
          hasUser: !!smtpConfig.auth.user,
          hasPass: !!smtpConfig.auth.pass,
          userLength: smtpConfig.auth.user?.length,
          passLength: smtpConfig.auth.pass?.length,
        },
      });
      throw verifyError;
    }

    // Prepare email options
    const mailOptions = {
      from: {
        name: "Panda Support",
        address: "<EMAIL>",
      },
      replyTo: {
        name: `${user.firstName} ${user.lastName}`.trim(),
        address: user.email,
      },
      to: "<EMAIL>",
      subject: `[Panda Support] ${subject} (Ref: ${requestId})`,
      text: emailContent,
      headers: {
        "X-Priority": "High",
        "X-Company": company,
        "X-User-Email": user.email,
        "X-User-Name": `${user.firstName} ${user.lastName}`.trim(),
        "X-Support-Request-ID": requestId,
        "X-Session-ID": req.sessionID,
      },
    };

    // Log complete email configuration
    console.log("5. Complete Email Configuration:", {
      timestamp: new Date().toISOString(),
      requestId,
      mailOptions: {
        from: mailOptions.from,
        replyTo: mailOptions.replyTo,
        to: mailOptions.to,
        subject: mailOptions.subject,
        headers: mailOptions.headers,
        contentLength: mailOptions.text.length,
      },
    });

    console.log("6. Sending email with options:", {
      timestamp: new Date().toISOString(),
      from: {
        name: mailOptions.from.name,
        address: mailOptions.from.address,
      },
      replyTo: mailOptions.replyTo,
      to: mailOptions.to,
      subject: mailOptions.subject,
      textLength: mailOptions.text.length,
      headers: mailOptions.headers,
    });

    // Send email with timeout
    const info = await Promise.race([
      transporter.sendMail(mailOptions),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Email send timeout")), 30000)
      ),
    ]);

    console.log("7. Email Sent Successfully:", {
      timestamp: new Date().toISOString(),
      messageId: info.messageId,
      response: info.response,
      accepted: info.accepted,
      rejected: info.rejected,
      pending: info.pending,
      envelope: info.envelope,
    });

    // Function to send acknowledgment email
    async function sendAcknowledgmentEmail(user, subject, requestId) {
      console.log("8. Preparing Acknowledgment Email:", {
        timestamp: new Date().toISOString(),
        requestId,
        recipient: {
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
        },
      });

      const acknowledgmentContent = `
Dear ${user.firstName} ${user.lastName},

Thank you for contacting Panda Support. We have received your support request:

Reference Number: ${requestId}
Subject: ${subject}

Our support team has been notified and will review your request shortly. We aim to respond within 24 hours during business days.

Please keep this reference number for future correspondence.

Best regards,
Panda Support Team
`.trim();

      const ackMailOptions = {
        from: {
          name: "Panda Support",
          address: "<EMAIL>",
        },
        to: {
          name: `${user.firstName} ${user.lastName}`.trim(),
          address: user.email,
        },
        subject: `[Panda Support] Request Received - ${requestId}`,
        text: acknowledgmentContent,
        headers: {
          "X-Priority": "High",
          "X-Support-Request-ID": requestId,
          "X-Auto-Response": "true",
          "X-Session-ID": req.sessionID,
        },
      };

      console.log("9. Acknowledgment Email Configuration:", {
        timestamp: new Date().toISOString(),
        requestId,
        config: {
          to: ackMailOptions.to,
          subject: ackMailOptions.subject,
          contentLength: acknowledgmentContent.length,
          headers: ackMailOptions.headers,
        },
      });

      return await transporter.sendMail(ackMailOptions);
    }

    // Send acknowledgment email
    console.log("10. Sending acknowledgment email to user...");
    try {
      const ackResult = await sendAcknowledgmentEmail(user, subject, requestId);
      console.log("11. Acknowledgment Email Sent Successfully:", {
        timestamp: new Date().toISOString(),
        messageId: ackResult.messageId,
        response: ackResult.response,
        recipient: user.email,
        requestId,
      });
    } catch (ackError) {
      console.error("Acknowledgment Email Error:", {
        timestamp: new Date().toISOString(),
        error: {
          name: ackError.name,
          message: ackError.message,
          code: ackError.code,
          command: ackError.command,
        },
        recipient: user.email,
        requestId,
      });
      // Continue execution even if acknowledgment fails
    }

    req.flash(
      "success_msg",
      "Your message has been sent to our support team. You will receive a confirmation email shortly."
    );
    return res.redirect("/dashboard");
  } catch (error) {
    console.error("\n=== Support Email Error ===", {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        code: error.code,
        command: error.command,
        responseCode: error.responseCode,
        response: error.response,
        stack: error.stack,
        authError: error.code === "EAUTH",
        connectionError: error.code === "ECONNECTION",
        timeoutError: error.message.includes("timeout"),
      },
    });

    // Log environment state on error
    console.error("Error Environment State:", {
      timestamp: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
      hasSmtpPass: !!process.env.SMTP_PASS,
      smtpPassLength: process.env.SMTP_PASS ? process.env.SMTP_PASS.length : 0,
    });

    req.flash(
      "error",
      "Failed to send message. <NAME_EMAIL> directly."
    );
    return res.redirect("/dashboard");
  }
});

// @route   POST /support/grant-access
// @desc    Grant temporary support access
// @access  Private
router.post('/grant-access', ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log('\n=== Support Access Grant Process Start ===');
  try {
    // Get company details
    const company = await Company.findById(req.user.currentCompany);
    if (!company) {
      req.flash('error', 'Company not found');
      return res.redirect('/support');
    }

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Calculate expiration (48 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 48);

    // Create new support access record
    const supportAccess = new SupportAccess({
      company: req.user.currentCompany,
      grantedBy: req.user._id,
      token,
      expiresAt
    });

    await supportAccess.save();

    // Generate access URL using environment-aware URL utility
    const accessUrl = getAppUrl(req, `/clients/${company.companyCode}?supportToken=${token}`);

    // Prepare email content
    const emailHtml = `
      <h2>New Support Access Request</h2>
      <p>A client has granted temporary support access to their portal.</p>
      
      <h3>Details:</h3>
      <ul>
        <li><strong>Company:</strong> ${company.name}</li>
        <li><strong>Company Code:</strong> ${company.companyCode}</li>
        <li><strong>Granted By:</strong> ${req.user.firstName} ${req.user.lastName} (${req.user.email})</li>
        <li><strong>Expires:</strong> ${expiresAt.toLocaleString()}</li>
      </ul>

      <h3>Access Instructions:</h3>
      <p>Click the link below or copy it to your browser to access the client's portal:</p>
      <p><a href="${accessUrl}">${accessUrl}</a></p>

      <p style="color: #666; margin-top: 20px;">
        <strong>Note:</strong> This access is read-only and will expire in 48 hours.
      </p>
    `;

    // Send email notification
    await transporter.sendMailWithRetry({
      to: '<EMAIL>',
      subject: `Support Access Granted - ${company.name}`,
      html: emailHtml,
      headers: {
        'X-Priority': 'High',
        'X-Company': company.name,
        'X-Company-Code': company.companyCode,
        'X-Granted-By': req.user.email,
        'X-Access-Type': 'Temporary Support Access',
        'X-Expires': expiresAt.toISOString()
      }
    });

    req.flash('success', 'Support access has been granted for 48 hours');
    res.redirect('/support');
  } catch (error) {
    console.error('Error granting support access:', error);
    req.flash('error', 'Failed to grant support access');
    res.redirect('/support');
  }
});

// @route   POST /support/revoke-access
// @desc    Revoke support access
// @access  Private
router.post('/revoke-access', ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    await SupportAccess.updateMany(
      { company: req.user.currentCompany, isRevoked: false },
      { isRevoked: true }
    );

    req.flash('success', 'Support access has been revoked');
    res.redirect('/support');
  } catch (error) {
    console.error('Error revoking support access:', error);
    req.flash('error', 'Failed to revoke support access');
    res.redirect('/support');
  }
});

// Add this before the 404 handler
router.use((req, res, next) => {
  console.log("\n=== Support Route Matching Debug ===");
  console.log({
    timestamp: new Date().toISOString(),
    stage: "Route Matching",
    request: {
      originalUrl: req.originalUrl,
      baseUrl: req.baseUrl,
      path: req.path,
      method: req.method,
      headers: {
        referer: req.headers.referer,
        "x-requested-with": req.headers["x-requested-with"],
      },
    },
    routeAnalysis: {
      availableRoutes: router.stack
        .filter((layer) => layer.route)
        .map((layer) => ({
          path: layer.route.path,
          methods: Object.keys(layer.route.methods),
          regexp: layer.regexp?.toString(),
          keys: layer.keys?.map((k) => k.name),
        })),
      matchAttempts: router.stack
        .filter((layer) => layer.route)
        .map((layer) => ({
          path: layer.route.path,
          matched: layer.match?.(req.path),
          methodMatch: layer.route?.methods[req.method.toLowerCase()],
        })),
    },
    mountPoint: {
      baseUrl: req.baseUrl,
      mountpath: router.mountpath,
      parentApp: req.app?.mountpath,
    },
  });

  // Enhanced navigation state tracking
  const originalRedirect = res.redirect;
  res.redirect = function (url) {
    console.log("\n=== Support Navigation State Debug ===");
    console.log({
      timestamp: new Date().toISOString(),
      stage: "Pre-Redirect",
      navigation: {
        source: {
          url: req.headers.referer,
          path: req.path,
          baseUrl: req.baseUrl,
        },
        destination: {
          url,
          isAbsolute: url.startsWith("http"),
          isRoot: url === "/",
          isDashboard: url === "/dashboard",
          isClient: url.startsWith("/clients"),
        },
      },
      routeState: {
        currentPath: req.path,
        matchedRoute:
          router.stack.find(
            (layer) =>
              layer.route?.path === req.path &&
              layer.route?.methods[req.method.toLowerCase()]
          )?.route?.path || "none",
        availableRoutes: router.stack
          .filter((layer) => layer.route)
          .map((layer) => layer.route.path),
      },
      appState: {
        mountPath: router.mountpath,
        parentApp: req.app?.mountpath,
        baseUrl: req.baseUrl,
      },
    });

    // Log the complete redirect chain
    console.log("\n=== Support Redirect Chain Debug ===");
    console.log({
      timestamp: new Date().toISOString(),
      stage: "Redirect Chain",
      chain: {
        origin: req.headers.referer,
        current: req.originalUrl,
        target: url,
        baseUrl: req.baseUrl,
      },
      routing: {
        matchedInSupport: router.stack.some(
          (layer) =>
            layer.route?.path === req.path &&
            layer.route?.methods[req.method.toLowerCase()]
        ),
        shouldHandleLocally: url.startsWith("/support"),
        isExternalRedirect: !url.startsWith("/support"),
        targetType:
          url === "/dashboard"
            ? "dashboard"
            : url.startsWith("/clients")
            ? "clients"
            : url === "/"
            ? "root"
            : "other",
      },
    });

    return originalRedirect.call(this, url);
  };

  next();
});

// Update the 404 handler with more detailed logging
router.use((req, res) => {
  console.log("\n=== Support 404 Analysis ===");
  console.log({
    timestamp: new Date().toISOString(),
    stage: "404 Handler",
    request: {
      originalUrl: req.originalUrl,
      path: req.path,
      method: req.method,
      baseUrl: req.baseUrl,
      headers: {
        referer: req.headers.referer,
        "x-requested-with": req.headers["x-requested-with"],
      },
    },
    routeAnalysis: {
      supportRoutes: router.stack
        .filter((layer) => layer.route)
        .map((layer) => ({
          path: layer.route.path,
          methods: Object.keys(layer.route.methods),
          wouldMatch: layer.match?.(req.path),
        })),
      mountPoint: router.mountpath,
      parentApp: req.app?.mountpath,
    },
    navigationChain: {
      source: req.headers.referer,
      attempted: req.originalUrl,
      isClientAttempt: req.originalUrl.startsWith("/clients"),
      isDashboardAttempt: req.originalUrl === "/dashboard",
      isRootAttempt: req.originalUrl === "/",
    },
  });

  // Instead of redirecting to dashboard, let's redirect to the referrer
  const referrer = req.headers.referer;
  if (referrer && !referrer.includes("/support")) {
    return res.redirect(referrer);
  }

  // If no valid referrer, redirect to dashboard
  return res.redirect("/dashboard");
});

module.exports = router;
