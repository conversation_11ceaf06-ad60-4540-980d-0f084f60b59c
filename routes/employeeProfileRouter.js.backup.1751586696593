const express = require("express");
const router = express.Router();
const User = require("../models/user");
const LoginHistory = require("../models/loginHistory");
const { ensureAuthenticated } = require("../config/auth");

router.get("/", ensureAuthenticated, async (req, res) => {
  try {
    // Get the user's login history
    const loginHistory = await LoginHistory.find({ user: req.user._id })
      .sort({ timestamp: -1 })
      .limit(10); // Get last 10 login attempts

    // Get the user's active sessions (you'll need to implement this based on your session storage)
    const activeSessions = []; // This should be populated from your session store

    res.render("employee-profile", {
      user: req.user,
      employee: req.user,
      loginHistory,
      activeSessions,
      moment: require("moment"),
    });
  } catch (error) {
    console.error("Error fetching employee profile:", error);
    req.flash("error", "Error loading profile");
    res.redirect("/dashboard");
  }
});

// ... rest of your routes

module.exports = router;
