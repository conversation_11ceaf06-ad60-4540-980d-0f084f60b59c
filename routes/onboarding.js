const express = require("express");
const { ensureAuthenticated, ensure2FA } = require("../config/auth");
const User = require("../models/user");
const Company = require("../models/Company");

module.exports = function createOnboardingRouter(options = {}) {
  const router = express.Router({ mergeParams: true });

  // Extract options
  const companyCode = String(options.companyCode || "").trim();
  const debug = options.debug || false;

  if (debug) {
  }

  // // Welcome route
  // router.get("/welcome", async (req, res) => {
  //   try {
  //     if (debug) {
  //       
  //       
  //       
  //     }

  //     const user = await User.findById(req.user._id).populate("currentCompany");
  //     if (!user) {
  //       
  //       return res.redirect("/login");
  //     }

  //     const company = await Company.findOne({ companyCode });
  //     if (!company) {
  //       
  //       return res.redirect("/dashboard");
  //     }

  //     const onboardingSteps = [
  //       {
  //         step: 1,
  //         title: "Company Details",
  //         description: "Set up your company information",
  //         icon: "🏢",
  //         route: `/clients/${companyCode}/onboarding/step1`,
  //       },
  //       {
  //         step: 2,
  //         title: "Employee Setup",
  //         description: "Add your employees",
  //         icon: "👥",
  //         route: `/clients/${companyCode}/onboarding/step2`,
  //       },
  //       {
  //         step: 3,
  //         title: "Payroll Settings",
  //         description: "Configure your payroll preferences",
  //         icon: "💰",
  //         route: `/clients/${companyCode}/onboarding/step3`,
  //       },
  //     ];

  //     if (debug) {
  //       console.log("Rendering welcome page with data:", {
  //         userExists: !!user,
  //         companyExists: !!company,
  //         stepsCount: onboardingSteps.length,
  //         csrfToken: !!req.csrfToken(),
  //       });
  //     }

  //     // Add debug logging for view resolution
  //     
  //     
  //     );

  //     res.render("onboarding-welcome", {
  //       user,
  //       company,
  //       onboardingSteps,
  //       companyCode,
  //       messages: req.flash(),
  //       csrfToken: req.csrfToken(),
  //     });
  //   } catch (error) {
  //     console.error("Error in onboarding welcome:", error);
  //     req.flash("error", "Error loading welcome page");
  //     res.redirect("/login");
  //   }
  // });

  // // Add other onboarding routes here
  // router.get("/step1", (req, res) => {
  //   res.render("employer-details", {
  //     user: req.user,
  //     companyCode,
  //     messages: req.flash(),
  //     csrfToken: req.csrfToken(),
  //   });
  // });

  // router.get("/step2", (req, res) => {
  //   res.render("employee-setup", {
  //     user: req.user,
  //     companyCode,
  //     messages: req.flash(),
  //     csrfToken: req.csrfToken(),
  //   });
  // });

  // router.get("/step3", (req, res) => {
  //   res.render("payroll-settings", {
  //     user: req.user,
  //     companyCode,
  //     messages: req.flash(),
  //     csrfToken: req.csrfToken(),
  //   });
  // });

  // Return the configured router
  return router;
};
