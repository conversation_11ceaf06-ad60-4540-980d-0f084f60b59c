const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const User = require("../models/user");
const Role = require("../models/role"); 
const bcrypt = require("bcrypt");
const csrf = require("csurf");
const csrfProtection = csrf({
  cookie: {
    key: '_csrf',
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  }
});

const SALT_ROUNDS = 12; // Add consistent salt rounds

// Debug middleware
router.use((req, res, next) => {
  next();
});

// GET route for setup page
router.get("/setup/:token", csrfProtection, async (req, res) => {
  try {
    const { token } = req.params;

    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/login");
    }

    res.render("employee-setup", {
      employee,
      token,
      csrfToken: req.csrfToken(),
    });
  } catch (error) {
    console.error("Error in setup GET route:", error);
    req.flash("error", "An error occurred while processing your request");
    res.redirect("/login");
  }
});

// POST route for handling password setup
router.post("/setup/:token", csrfProtection, async (req, res) => {
  try {
    const { token } = req.params;
    const { password, confirmPassword } = req.body;

    // Validate passwords match
    if (password !== confirmPassword) {
      return res.render("employee-setup", {
        error: "Passwords do not match",
        token,
        csrfToken: req.csrfToken(),
      });
    }

    // Find employee with valid token
    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    }).populate('user');

    if (!employee) {
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/login");
    }

    // Find or create user account
    let user = await User.findOne({ email: employee.email.toLowerCase() });

    if (!user) {
      user = new User({
        email: employee.email.toLowerCase(),
        firstName: employee.firstName,
        lastName: employee.lastName,
        username: employee.email.toLowerCase(),
      });
    }

    // Update user password and verify
    user.password = password; // Let the User model's pre-save hook handle the hashing
    user.isVerified = true;

    // Set default role if not set
    if (!user.role) {
      const defaultRole = await Role.findOne({ name: 'employee' });
      if (defaultRole) {
        user.role = defaultRole._id;
        user.roleName = 'employee';
      }
    }

    try {
      await user.save();
    } catch (error) {
      if (error.code === 11000) {
        // Handle duplicate key error
        console.log("Duplicate key error - attempting to find existing user");
        user = await User.findOne({ email: employee.email.toLowerCase() });
        if (!user) {
          throw new Error("Failed to create or find user");
        }
      } else {
        throw error;
      }
    }

    // Update employee record
    employee.selfServiceToken = undefined;
    employee.selfServiceTokenExpiration = undefined;
    employee.user = user._id;
    await employee.save();

    req.flash("success_msg", "Account setup complete. Please log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error in setup POST route:", error);
    res.render("error", {
      message: "An error occurred while processing your setup request.",
      error: { status: 500 },
    });
  }
});

module.exports = router;
