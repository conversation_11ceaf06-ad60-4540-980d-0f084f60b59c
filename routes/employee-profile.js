const express = require("express");
const router = express.Router();
const User = require("../models/user");
const Company = require("../models/Company");
const Employee = require("../models/Employee");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const ExcelJS = require("exceljs");
const PDFDocument = require("pdfkit");
const Invoice = require("../models/invoice");
const speakeasy = require("speakeasy");
const QRCode = require("qrcode");

// Debug middleware
router.use((req, res, next) => {
  next();
});

// Main profile route - handle both /profile and / paths
router.get(
  ["/", "/profile"],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Fetch user with populated companies and current company
      const user = await User.findById(req.user._id)
        .populate("companies")
        .populate("currentCompany");

      if (!user) {
        return res.status(404).send("User not found");
      }

      // Get current company
      const company = user.currentCompany;
      if (!company) {
        return res.redirect("/companies/select");
      }

      // Calculate trial expiry date (30 days from registration)
      const registrationDate = user.createdAt;
      const trialExpiryDate = new Date(registrationDate);
      trialExpiryDate.setDate(trialExpiryDate.getDate() + 30);

      // Calculate billing amount
      const activeEmployees = await Employee.countDocuments({
        company: company._id,
        employmentStatus: true,
      });

      let billingAmount = 0;
      if (new Date() > trialExpiryDate) {
        billingAmount = calculateBillingAmount(activeEmployees);
      }

      // Calculate billing period dates
      const today = new Date();
      const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
      const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      // Fetch invoices for the current company
      const invoices = await Invoice.find({
        company: company._id,
        // You might want to add date filtering here
      }).sort({ date: -1 }); // Sort by date descending

      // Prepare billing data
      const billingData = {
        trialExpiryDate: trialExpiryDate.toISOString(),
        currentBalance: billingAmount.toFixed(2),
        activeEmployees,
        isTrialPeriod: new Date() <= trialExpiryDate,
        invoices: invoices.map((invoice) => ({
          number: invoice.number,
          date: invoice.date,
          amount: invoice.amount,
          status: invoice.status,
          downloadUrl: `/employee-profile/download-invoice/${invoice._id}`,
          viewUrl: `/employee-profile/view-invoice/${invoice._id}`,
        })),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        ratePerEmployee: calculateRatePerEmployee(activeEmployees),
        tierName: getTierName(activeEmployees),
      };

      // Handle AJAX requests
      if (req.xhr || req.headers.accept.indexOf("json") > -1) {
        return res.json(billingData);
      }

      // Create employee object for template
      const employee = {
        fullName: `${user.firstName} ${user.lastName}`,
        email: user.email,
        bio: user.bio || "Tell us about yourself",
      };

      // Render the profile page with CSRF token
      res.render("employee-profile", {
        user,
        company,
        employee,
        billingData,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error in profile route:", error);
      req.flash("error", "Error loading profile");
      res.status(500).redirect("/dashboard");
    }
  }
);

// Helper function to calculate billing amount
function calculateBillingAmount(activeEmployees) {
  let amount = 0;
  if (activeEmployees <= 10) {
    amount = 40 + ((activeEmployees - 1) * 11.50); // Starter tier
  } else if (activeEmployees <= 50) {
    amount = 40 + ((activeEmployees - 1) * 7.50); // Growth tier
  } else if (activeEmployees <= 100) {
    amount = 40 + ((activeEmployees - 1) * 6.75); // Business tier
  } else {
    amount = 40 + ((activeEmployees - 1) * 5.75); // Enterprise tier
  }
  return Math.max(amount, 40); // Minimum billing amount is R40 (base price)
}

// Helper function to calculate rate per employee
function calculateRatePerEmployee(activeEmployees) {
  if (activeEmployees <= 10) {
    return 11.50; // Starter tier
  } else if (activeEmployees <= 50) {
    return 7.50; // Growth tier
  } else if (activeEmployees <= 100) {
    return 6.75; // Business tier
  } else {
    return 5.75; // Enterprise tier
  }
}

// Helper function to get tier name
function getTierName(activeEmployees) {
  if (activeEmployees <= 10) {
    return 'Starter';
  } else if (activeEmployees <= 50) {
    return 'Growth';
  } else if (activeEmployees <= 100) {
    return 'Business';
  } else {
    return 'Enterprise';
  }
}

// Security settings routes
router.post(
  "/security/update-password",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("Body content keys:", Object.keys(req.body));
      
      const { currentPassword, newPassword } = req.body;
      
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      // Verify current password
      const isMatch = await user.comparePassword(currentPassword);
      if (!isMatch) {
        return res.status(400).json({
          success: false,
          message: "Current password is incorrect",
        });
      }

      // Update password
      user.password = newPassword;
      user.passwordLastChanged = new Date();
      await user.save();

      
      res.json({
        success: true,
        message: "Password updated successfully",
      });
    } catch (error) {
      console.error("Error updating security settings:", error);
      console.error("Stack trace:", error.stack);
      res.status(500).json({
        success: false,
        message: "Server error while updating security settings",
      });
    }
  }
);

// Add more routes for handling 2FA enablement and other security features

// Add this route to handle profile updates
router.post(
  "/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { fullName, email, bio } = req.body;
      const [firstName, ...lastNameParts] = fullName.split(" ");
      const lastName = lastNameParts.join(" ");

      // Update user profile including bio
      await User.findByIdAndUpdate(req.user._id, {
        firstName,
        lastName,
        email,
        bio,
      });

      req.flash("success", "Profile updated successfully");
      res.redirect("/employee-profile");
    } catch (error) {
      console.error("Error updating profile:", error);
      req.flash("error", "Failed to update profile");
      res.redirect("/employee-profile");
    }
  }
);

// Download Excel Statement
router.get("/download-statement", ensureAuthenticated, async (req, res) => {
  try {
    const { fromDate, toDate } = req.query;
    const user = await User.findById(req.user._id).populate("currentCompany");

    if (!user || !user.currentCompany) {
      return res.status(404).send("Company not found");
    }

    // Create a new Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Billing Statement");

    // Add company header
    worksheet.mergeCells("A1:C1");
    worksheet.getCell(
      "A1"
    ).value = `${user.currentCompany.name} - Billing Statement`;
    worksheet.getCell("A1").font = { size: 16, bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add date range
    worksheet.mergeCells("A2:C2");
    worksheet.getCell("A2").value = `Period: ${new Date(
      fromDate
    ).toLocaleDateString()} - ${new Date(toDate).toLocaleDateString()}`;
    worksheet.getCell("A2").alignment = { horizontal: "center" };

    // Add headers
    worksheet.addRow([
      "Description",
      "Details", 
      "Amount"
    ]);

    // Style headers
    worksheet.getRow(3).font = { bold: true };
    worksheet.getRow(3).alignment = { horizontal: "center" };

    // Calculate billing details
    const activeEmployees = await Employee.countDocuments({
      company: user.currentCompany._id,
      employmentStatus: true,
    });

    const rate = calculateRatePerEmployee(activeEmployees);
    const amount = calculateBillingAmount(activeEmployees);
    const tierName = getTierName(activeEmployees);
    const additionalEmployees = activeEmployees - 1;

    // Add base price row
    worksheet.addRow([
      "Base Price (First Employee)",
      "1 employee",
      "R 40.00"
    ]);

    // Add additional employees row if applicable
    if (additionalEmployees > 0) {
      worksheet.addRow([
        `Additional Employees (${tierName} Tier)`,
        `${additionalEmployees} @ R${rate.toFixed(2)}`,
        `R ${(additionalEmployees * rate).toFixed(2)}`
      ]);
    }

    // Add empty row
    worksheet.addRow([]);

    // Add total
    const totalRow = worksheet.addRow([
      "Total",
      "",
      `R ${amount.toFixed(2)}`
    ]);
    totalRow.font = { bold: true };

    // Add empty row
    worksheet.addRow([]);

    // Add subscription details
    worksheet.addRow(["SUBSCRIPTION DETAILS"]).font = { bold: true };
    worksheet.addRow(["Current Tier", tierName]);
    worksheet.addRow(["Active Employees", activeEmployees.toString()]);
    worksheet.addRow([
      "Average Rate per Employee",
      `R ${(amount / activeEmployees).toFixed(2)}`
    ]);

    // Set column widths
    worksheet.columns = [
      { width: 35 }, // Description
      { width: 25 }, // Details
      { width: 15 }, // Amount
    ];

    // Set content type and headers for Excel download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=billing-statement-${fromDate}-${toDate}.xlsx`
    );

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Error generating Excel statement:", error);
    res.status(500).send("Error generating statement");
  }
});

// Download PDF Statement
router.get("/download-statement-pdf", ensureAuthenticated, async (req, res) => {
  try {
    const { fromDate, toDate } = req.query;
    const user = await User.findById(req.user._id).populate("currentCompany");

    if (!user || !user.currentCompany) {
      return res.status(404).send("Company not found");
    }

    // Create PDF document with custom page size and margins
    const doc = new PDFDocument({
      size: "A4",
      margin: 50,
    });

    // Set response headers for PDF download
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=billing-statement-${fromDate}-${toDate}.pdf`
    );

    // Pipe PDF to response
    doc.pipe(res);

    // Add company logo (replace with actual logo path)
    // doc.image('path/to/logo.png', 50, 45, { width: 150 });

    // Add company info
    doc
      .fontSize(20)
      .fillColor("#2B3674")
      .text("INVOICE", 50, 50, { align: "right" })
      .fontSize(10)
      .fillColor("#666")
      .text(`Invoice Date: ${new Date().toLocaleDateString()}`, {
        align: "right",
      })
      .text(`Invoice #: INV-${Date.now().toString().slice(-6)}`, {
        align: "right",
      })
      .moveDown(2);

    // Add company details
    doc
      .fontSize(12)
      .fillColor("#2B3674")
      .text(user.currentCompany.name, 50, 150)
      .fontSize(10)
      .fillColor("#666")
      .text(user.currentCompany.address || "Company Address")
      .text(user.currentCompany.phone || "Company Phone")
      .text(user.currentCompany.email || "Company Email")
      .moveDown(2);

    // Add billing period
    doc
      .fontSize(10)
      .fillColor("#666")
      .text("BILLING PERIOD", 50, doc.y)
      .fontSize(12)
      .fillColor("#2B3674")
      .text(
        `${new Date(fromDate).toLocaleDateString()} - ${new Date(
          toDate
        ).toLocaleDateString()}`
      )
      .moveDown(2);

    // Calculate billing details
    const activeEmployees = await Employee.countDocuments({
      company: user.currentCompany._id,
      employmentStatus: true,
    });

    const rate = calculateRatePerEmployee(activeEmployees);
    const amount = calculateBillingAmount(activeEmployees);

    // Add table headers with background
    const tableTop = doc.y;
    doc.fillColor("#F4F7FE").rect(50, tableTop, 495, 20).fill();

    doc
      .fontSize(10)
      .fillColor("#2B3674")
      .text("Description", 60, tableTop + 5)
      .text("Details", 300, tableTop + 5)
      .text("Amount", 450, tableTop + 5);

    // Add table rows
    let rowTop = tableTop + 25;
    
    // Base price row
    doc
      .fillColor("#666")
      .text("Base Price (First Employee)", 60, rowTop)
      .text("1 employee", 300, rowTop)
      .text("R 40.00", 450, rowTop);

    // Additional employees row
    rowTop += 25;
    const additionalEmployees = activeEmployees - 1;
    const tierName = getTierName(activeEmployees);
    
    if (additionalEmployees > 0) {
      doc
        .fillColor("#666")
        .text(`Additional Employees (${tierName} Tier)`, 60, rowTop)
        .text(`${additionalEmployees} @ R${rate.toFixed(2)}`, 300, rowTop)
        .text(`R ${(additionalEmployees * rate).toFixed(2)}`, 450, rowTop);
    }

    // Add total
    const totalTop = rowTop + 30;
    doc.fillColor("#F4F7FE").rect(370, totalTop, 175, 25).fill();

    doc
      .fontSize(12)
      .fillColor("#2B3674")
      .text("Total:", 380, totalTop + 5)
      .text(`R ${amount.toFixed(2)}`, 450, totalTop + 5);

    // Add tier information
    doc
      .moveDown(3)
      .fontSize(10)
      .fillColor("#2B3674")
      .text("SUBSCRIPTION DETAILS", 50, doc.y)
      .moveDown(0.5);

    const subscriptionDetails = [
      { label: "Current Tier:", value: tierName },
      { label: "Active Employees:", value: activeEmployees.toString() },
      { label: "Average Rate per Employee:", value: `R ${(amount / activeEmployees).toFixed(2)}` },
    ];

    subscriptionDetails.forEach((detail) => {
      doc
        .fontSize(10)
        .fillColor("#666")
        .text(detail.label, 50)
        .fillColor("#2B3674")
        .text(detail.value, 150, doc.y - 10)
        .moveDown(0.5);
    });

    // Add payment details
    doc
      .moveDown(4)
      .fontSize(10)
      .fillColor("#2B3674")
      .text("PAYMENT DETAILS", 50, doc.y)
      .moveDown(0.5);

    const paymentDetails = [
      { label: "Bank Name:", value: "Your Bank Name" },
      { label: "Account Number:", value: "XXXX-XXXX-XXXX-XXXX" },
      { label: "Account Type:", value: "Business Account" },
      { label: "Branch Code:", value: "XXXXX" },
    ];

    paymentDetails.forEach((detail) => {
      doc
        .fontSize(10)
        .fillColor("#666")
        .text(detail.label, 50)
        .fillColor("#2B3674")
        .text(detail.value, 150, doc.y - 10)
        .moveDown(0.5);
    });

    // Add footer
    const footerTop = doc.page.height - 100;
    doc
      .fontSize(10)
      .fillColor("#666")
      .text("Thank you for your business", 50, footerTop, { align: "center" })
      .moveDown(0.5)
      .text("For any queries, <NAME_EMAIL>", {
        align: "center",
      });

    // Add page numbers
    const pageCount = doc.bufferedPageRange().count;
    doc
      .fontSize(10)
      .fillColor("#666")
      .text(`Page 1 of ${pageCount}`, 50, doc.page.height - 50, {
        align: "center",
      });

    // Finalize PDF
    doc.end();
  } catch (error) {
    console.error("Error generating PDF statement:", error);
    res.status(500).send("Error generating PDF statement");
  }
});

// Add routes to handle invoice viewing and downloading
router.get("/view-invoice/:id", ensureAuthenticated, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id).populate("company");

    if (!invoice) {
      return res.status(404).send("Invoice not found");
    }

    // Check if user has access to this invoice
    if (invoice.company._id.toString() !== req.user.currentCompany.toString()) {
      return res.status(403).send("Unauthorized");
    }

    res.render("view-invoice", { invoice });
  } catch (error) {
    console.error("Error viewing invoice:", error);
    res.status(500).send("Error viewing invoice");
  }
});

router.get("/download-invoice/:id", ensureAuthenticated, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id).populate("company");

    if (!invoice) {
      return res.status(404).send("Invoice not found");
    }

    // Check if user has access to this invoice
    if (invoice.company._id.toString() !== req.user.currentCompany.toString()) {
      return res.status(403).send("Unauthorized");
    }

    // Generate and send PDF invoice
    // You can reuse your existing PDF generation code here
    // ... PDF generation code ...
  } catch (error) {
    console.error("Error downloading invoice:", error);
    res.status(500).send("Error downloading invoice");
  }
});

// Add these routes for 2FA management
router.post(
  "/security/2fa/disable",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id);

      if (!user) {
        return res
          .status(404)
          .json({ success: false, message: "User not found" });
      }

      // Disable 2FA
      user.twoFactorEnabled = false;
      user.twoFactorSecret = undefined;
      await user.save();

      res.json({ success: true, message: "2FA has been disabled" });
    } catch (error) {
      console.error("Error disabling 2FA:", error);
      res
        .status(500)
        .json({ success: false, message: "Failed to disable 2FA" });
    }
  }
);

router.post(
  "/security/2fa/setup",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const user = await User.findById(req.user._id);
      if (!user) {
        return res
          .status(404)
          .json({ success: false, message: "User not found" });
      }

      // Generate new secret
      const secret = speakeasy.generateSecret();
      const otpauth_url = speakeasy.otpauthURL({
        secret: secret.base32,
        label: user.email,
        issuer: "Your App Name",
      });

      // Generate QR code
      const qrCode = await QRCode.toDataURL(otpauth_url);

      // Store temporary secret
      user.tempTwoFactorSecret = secret.base32;
      await user.save();

      res.json({
        success: true,
        qrCode,
        secret: secret.base32,
      });
    } catch (error) {
      console.error("Error setting up 2FA:", error);
      res.status(500).json({ success: false, message: "Failed to setup 2FA" });
    }
  }
);

router.post(
  "/security/2fa/verify",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { token, secret } = req.body;
      const user = await User.findById(req.user._id);

      if (!user) {
        return res
          .status(404)
          .json({ success: false, message: "User not found" });
      }

      // Verify token
      const verified = speakeasy.totp.verify({
        secret: secret,
        encoding: "base32",
        token: token,
      });

      if (verified) {
        user.twoFactorEnabled = true;
        user.twoFactorSecret = secret;
        user.tempTwoFactorSecret = undefined;
        await user.save();
        res.json({ success: true });
      } else {
        res
          .status(400)
          .json({ success: false, message: "Invalid verification code" });
      }
    } catch (error) {
      console.error("Error verifying 2FA:", error);
      res.status(500).json({ success: false, message: "Failed to verify 2FA" });
    }
  }
);

// Export the router
module.exports = router;
