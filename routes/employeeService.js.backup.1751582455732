const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const { PDFDocument, StandardFonts, rgb, PDFTextField } = require("pdf-lib");
const csrf = require("csurf");
const cookieParser = require("cookie-parser");
const path = require("path");
const fs = require("fs");
const moment = require("moment-timezone");
const EmployerFilingDetails = require("../models/employerFilingDetails");

// Set default timezone
const DEFAULT_TIMEZONE = "Africa/Johannesburg";
const EmployerDetails = require("../models/employerDetails");
const Payroll = require("../models/Payroll");
const TerminationCertificate = require("../models/TerminationCertificate");
const fileUpload = require("express-fileupload");
const UI19 = require("../models/UI19");

// Configure middleware
router.use(cookieParser());
router.use(express.urlencoded({ extended: true }));
router.use(express.json());
router.use(
  fileUpload({
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    createParentPath: true,
    useTempFiles: true,
    tempFileDir: "/tmp/",
  })
);

// CSRF Protection setup
const csrfProtection = csrf({
  cookie: {
    key: "_csrf",
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  },
});

// Apply CSRF protection to all routes
router.use(csrfProtection);

// Debug logging middleware
router.use((req, res, next) => {
  console.log("\n=== Employee Service Route Debug ===");
  console.log("Attempting to match:", req.path);
  console.log("Route params:", req.params);
  console.log("Body:", req.body);
  next();
});

// Add specific end-service route debugging
router.use(
  "/:companyCode/employeeProfile/:employeeId/end-service",
  (req, res, next) => {
    console.log("\n=== End Service Route Hit ===");
    console.log("Method:", req.method);
    console.log("Params:", req.params);
    console.log("Body:", req.body);
    next();
  }
);

// POST route for end-service action
router.post(
  "/:companyCode/employeeProfile/:employeeId/end-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { lastDayOfService, uifStatusCode, temporaryAbsence } = req.body;

      // Find the employee first
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      const today = new Date();
      const lastDay = new Date(lastDayOfService);
      const doa = new Date(employee.doa);

      // Validate termination date is not before DOA
      if (lastDay < doa) {
        req.flash(
          "error",
          "Last day of service cannot be before date of appointment"
        );
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
        );
      }

      // Validate dates for temporary absence
      if (temporaryAbsence && temporaryAbsence.expectedReturnDate) {
        const returnDate = new Date(temporaryAbsence.expectedReturnDate);
        if (returnDate <= lastDay) {
          req.flash(
            "error",
            "Expected return date must be after last day of service"
          );
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
          );
        }
      }

      // Use findByIdAndUpdate to update only necessary fields
      const updates = {
        status: lastDay > today ? "Serving Notice" : "Inactive",
        terminationNoticeDate: today,
        lastDayOfService: lastDay,
        uifStatusCode: uifStatusCode,
      };

      // Add temporary absence details if applicable
      if (["9", "10", "18", "19"].includes(uifStatusCode)) {
        updates.temporaryAbsence = {
          expectedReturnDate: temporaryAbsence.expectedReturnDate,
          paidDuringAbsence: temporaryAbsence.paidDuringAbsence === "on",
        };
      } else {
        updates.temporaryAbsence = {
          expectedReturnDate: null,
          paidDuringAbsence: false,
        };
      }

      // Update the employee with findByIdAndUpdate
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        { $set: updates },
        {
          new: true,
          runValidators: false, // Disable validation for this update
        }
      );

      if (!updatedEmployee) {
        req.flash("error", "Failed to update employee status");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
        );
      }

      // Handle service periods
      if (
        !updatedEmployee.servicePeriods ||
        !Array.isArray(updatedEmployee.servicePeriods)
      ) {
        updatedEmployee.servicePeriods = [];
      }

      // Update or create service period
      const servicePeriod = {
        startDate: updatedEmployee.doa || today,
        endDate: lastDay,
        terminationReason: getTerminationReasonFromCode(uifStatusCode),
      };

      if (updatedEmployee.servicePeriods.length === 0) {
        updatedEmployee.servicePeriods.push(servicePeriod);
      } else {
        updatedEmployee.servicePeriods[
          updatedEmployee.servicePeriods.length - 1
        ] = servicePeriod;
      }

      // Save the service period updates without validation
      await Employee.findByIdAndUpdate(
        employeeId,
        { $set: { servicePeriods: updatedEmployee.servicePeriods } },
        { runValidators: false }
      );

      req.flash("success", "Employee service ended successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error in end-service POST route:", error);
      req.flash(
        "error",
        "An error occurred while processing the end service request"
      );
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/end-service`
      );
    }
  }
);

// GET route for end-service page
router.get(
  "/:companyCode/employeeProfile/:employeeId/end-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("\n=== End Service GET Route Start ===");
      const { companyCode, employeeId } = req.params;

      // Verify CSRF token is available
      if (!req.csrfToken) {
        throw new Error("CSRF token not available");
      }
      const csrfToken = req.csrfToken();

      // Fetch company and employee (populate payFrequency for consistency with employeeProfile)
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate("payFrequency");

      if (!company || !employee) {
        req.flash(
          "error",
          company ? "Employee not found" : "Company not found"
        );
        return res.redirect(
          company ? `/clients/${companyCode}/employeeManagement` : "/clients"
        );
      }

      // Format the DOA date for display and validation
      const formattedDOA = employee.doa
        ? new Date(employee.doa).toLocaleDateString()
        : "Not set";
      const isoFormattedDOA = employee.doa
        ? new Date(employee.doa).toISOString().split("T")[0]
        : "";

      // Calculate date of birth formatting (consistent with employeeProfile)
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Calculate pay frequency display (consistent with employeeProfile)
      const payFrequency = employee.payFrequency?.name || "N/A";

      // Update status if needed
      const statusUpdated = employee.updateStatusBasedOnLastDayOfService();
      if (statusUpdated) {
        await employee.save();
      }

      // Render with formatted dates and missing variables
      res.render("end-service", {
        company,
        employee,
        formattedDOA,
        isoFormattedDOA,
        dobFormatted,
        payFrequency,
        messages: req.flash(),
        csrfToken,
        partialsDir: "partials",
      });
    } catch (error) {
      console.error("\n=== End Service GET Route Error ===", error);
      req.flash(
        "error",
        "An error occurred while loading the end service page"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add error handler
router.use((err, req, res, next) => {
  console.error("\n=== Employee Service Error ===");
  console.error("Error:", err);
  console.error("URL:", req.url);
  console.error("Method:", req.method);
  next(err);
});

// GET route for updating termination details
router.get(
  "/:companyCode/employeeProfile/:employeeId/update-termination-details",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("update-termination", {
        company,
        employee,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error in update-termination GET route:", error);
      req.flash(
        "error",
        "An error occurred while loading the update termination page"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for updating termination details
router.post(
  "/:companyCode/employeeProfile/:employeeId/update-termination-details",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { lastDayOfService, uifStatusCode, terminationReason } = req.body;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      employee.lastDayOfService = new Date(lastDayOfService);
      employee.uifStatusCode = uifStatusCode;
      employee.terminationReason = terminationReason;

      if (employee.servicePeriods && employee.servicePeriods.length > 0) {
        const currentPeriod =
          employee.servicePeriods[employee.servicePeriods.length - 1];
        currentPeriod.endDate = new Date(lastDayOfService);
        currentPeriod.terminationReason = terminationReason;
      }

      await employee.save();
      req.flash("success", "Termination details updated successfully");
      res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
      );
    } catch (error) {
      console.error("Error in update-termination POST route:", error);
      req.flash(
        "error",
        "An error occurred while updating termination details"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// GET route for undo end of service
router.get(
  "/:companyCode/employeeProfile/:employeeId/undo-end-of-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("undo-end-of-service", {
        company,
        employees: [employee],
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error in undo-end-of-service GET route:", error);
      req.flash(
        "error",
        "An error occurred while loading the undo end of service page"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST routes for service actions
router.post(
  "/:companyCode/employeeProfile/:employeeId/undo-end-of-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { _csrf } = req.body;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      employee.status = "Active";
      employee.lastDayOfService = null;
      employee.uifStatusCode = null;
      employee.terminationReason = null;
      employee.terminationNoticeDate = null;

      if (employee.servicePeriods && employee.servicePeriods.length > 0) {
        const currentPeriod =
          employee.servicePeriods[employee.servicePeriods.length - 1];
        currentPeriod.endDate = null;
        currentPeriod.terminationReason = null;
      }

      await employee.save();
      req.flash("success", "Termination has been successfully undone");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error in undo-end-of-service POST route:", error);
      req.flash("error", "An error occurred while undoing the termination");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// GET route for reinstating an employee
router.get(
  "/:companyCode/employeeProfile/:employeeId/reinstate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("reinstate-employee", {
        company,
        employee,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error in reinstate GET route:", error);
      req.flash(
        "error",
        "An error occurred while loading the reinstate employee page"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for reinstating an employee
router.post(
  "/:companyCode/employeeProfile/:employeeId/reinstate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { reinstatementDate, reinstatementReason } = req.body;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      employee.status = "Active";
      employee.reinstatementDate = new Date(reinstatementDate);
      employee.reinstatementReason = reinstatementReason;

      employee.servicePeriods.push({
        startDate: new Date(reinstatementDate),
        endDate: null,
      });

      await employee.save();
      req.flash("success", "Employee reinstated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error in reinstate POST route:", error);
      req.flash("error", "An error occurred while reinstating the employee");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add this route to your employeeService.js
router.get(
  "/:companyCode/employeeProfile/:employeeId/termination-certificate/:periodIndex",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId, periodIndex } = req.params;

      // Fetch company and employee
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the UI19 form template
      res.render("ui19-form", {
        company,
        employee,
        periodIndex: parseInt(periodIndex),
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error generating UI19 form:", error);
      req.flash("error", "Error generating termination certificate");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// GET route for editing termination certificate
router.get(
  "/:companyCode/employeeProfile/:employeeId/termination-certificate/:periodIndex/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId, periodIndex } = req.params;

      // Fetch required data
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get service period
      const servicePeriod = employee.servicePeriods[periodIndex];
      if (!servicePeriod) {
        req.flash("error", "Service period not found");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
        );
      }

      // Get payroll for the period
      const payroll = await Payroll.findOne({
        company: company._id,
        employee: employee._id,
        month: {
          $lte: servicePeriod.endDate,
          $gte: moment(servicePeriod.endDate).startOf("month").toDate(),
        },
      }).sort({ month: -1 });

      // Get existing certificate data if any
      const certificateData = await TerminationCertificate.findOne({
        company: company._id,
        employee: employee._id,
        periodIndex: parseInt(periodIndex),
      });

      // Get CSRF token
      const csrfToken = req.csrfToken();
      console.log("CSRF Token for edit form:", csrfToken);

      res.render("editTerminationCertificate", {
        title: "Edit UI19 Certificate",
        company,
        employee,
        periodIndex,
        servicePeriod,
        payroll,
        certificateData,
        csrfToken,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error in edit certificate route:", error);
      req.flash("error", "Error loading certificate edit page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// POST route for updating termination certificate
router.post(
  "/:companyCode/employeeProfile/:employeeId/termination-certificate/:periodIndex/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("\n=== Certificate Update Debug ===");

      // Log raw signature data
      console.log("Raw signature data:", {
        signatureExists: !!req.body.signature,
        signatureType: req.body.signatureType,
        signatureLength: req.body.signature?.length,
        signaturePreview: req.body.signature?.substring(0, 50) + "...",
        isBase64: req.body.signature?.includes("base64"),
      });

      // Log form data
      console.log("Form Data:", {
        hasSignature: !!req.body.signature,
        hasStamp: !!req.files?.stamp,
        bodyKeys: Object.keys(req.body),
        filesKeys: req.files ? Object.keys(req.files) : [],
      });

      const { companyCode, employeeId, periodIndex } = req.params;
      const {
        basicSalary,
        totalHours,
        employerName,
        employerIdNumber,
        signature,
        signatureType,
      } = req.body;

      // Get company and employee
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        console.log("Error: Company or Employee not found");
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Prepare update data
      const updateData = {
        basicSalary: parseFloat(basicSalary),
        totalHours: parseFloat(totalHours),
        employerName: employerName || "",
        employerIdNumber: employerIdNumber || "",
        modifiedBy: req.user.id,
        lastModified: new Date(),
      };

      // Add signature if provided
      if (signature) {
        console.log("\n=== Processing Signature Data ===");
        console.log("Raw signature data:", {
          type: signatureType,
          hasData: !!signature,
          dataLength: signature.length,
          isBase64: signature.includes("base64"),
          dataPreview: signature.substring(0, 50) + "...",
        });

        // For drawn or uploaded signatures, verify the data format
        if (signatureType === "draw" || signatureType === "upload") {
          console.log("Validating signature format");
          if (!signature.startsWith("data:image")) {
            console.log("Invalid signature data format");
            throw new Error("Invalid signature data format");
          }
        }

        updateData.signature = {
          type: signatureType || "draw",
          data: signature,
          date: new Date(),
        };

        console.log("Prepared signature data:", {
          type: updateData.signature.type,
          hasData: !!updateData.signature.data,
          dataLength: updateData.signature.data.length,
          date: updateData.signature.date,
          isBase64: updateData.signature.data.includes("base64"),
        });
      }

      // Handle stamp upload
      if (req.files && req.files.stamp) {
        console.log("Processing stamp upload");
        const stampFile = req.files.stamp;

        // Convert stamp to base64
        const stampData = stampFile.data.toString("base64");

        updateData.stamp = {
          data: stampData,
          uploadDate: new Date(),
        };
      }

      console.log("Update data prepared:", {
        hasSignature: !!updateData.signature,
        signatureType: updateData.signature?.type,
        signatureDataLength: updateData.signature?.data?.length,
        hasStamp: !!updateData.stamp,
      });

      // Perform update
      const updatedCertificate = await TerminationCertificate.findOneAndUpdate(
        {
          company: company._id,
          employee: employee._id,
          periodIndex: parseInt(periodIndex),
        },
        updateData,
        { upsert: true, new: true }
      );

      console.log("Certificate updated:", {
        success: !!updatedCertificate,
        hasSignature: !!updatedCertificate?.signature?.data,
        signatureType: updatedCertificate?.signature?.type,
        signatureDataLength: updatedCertificate?.signature?.data?.length,
        signatureDate: updatedCertificate?.signature?.date,
        hasStamp: !!updatedCertificate?.stamp?.data,
      });

      // Verify the saved data
      const verifiedCertificate = await TerminationCertificate.findOne({
        company: company._id,
        employee: employee._id,
        periodIndex: parseInt(periodIndex),
      });

      console.log("Verified saved certificate:", {
        found: !!verifiedCertificate,
        hasSignature: !!verifiedCertificate?.signature?.data,
        signatureType: verifiedCertificate?.signature?.type,
        signatureDataLength: verifiedCertificate?.signature?.data?.length,
        signatureDate: verifiedCertificate?.signature?.date,
      });

      req.flash("success", "Certificate details updated successfully");
      res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/end-service`
      );
    } catch (error) {
      console.error("Certificate update error:", error);
      req.flash("error", "Error updating certificate details");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/end-service`
      );
    }
  }
);

// Update the PDF download route
router.get(
  "/:companyCode/employeeProfile/:employeeId/termination-certificate/:periodIndex/download",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("\n=== UI19 PDF Generation Start ===");
      const { companyCode, employeeId, periodIndex } = req.params;
      console.log("Request parameters:", {
        companyCode,
        employeeId,
        periodIndex,
      });

      // Fetch company and employee
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      console.log("Data lookup results:", {
        companyFound: !!company,
        employeeFound: !!employee,
        companyCode: company?.companyCode,
        companyName: company?.name,
        employeeName: employee
          ? `${employee.firstName} ${employee.lastName}`
          : null,
        employeeId: employee?.idNumber,
      });

      if (!company || !employee) {
        console.log("Company or Employee not found");
        req.flash("error", "Company or Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get service period
      const servicePeriod = employee.servicePeriods[periodIndex];
      console.log("Service period:", {
        found: !!servicePeriod,
        startDate: servicePeriod?.startDate,
        endDate: servicePeriod?.endDate,
        terminationReason: servicePeriod?.terminationReason,
        uifStatusCode: servicePeriod?.uifStatusCode,
      });

      // Get certificate data with detailed logging
      console.log("\n=== Fetching Certificate Data ===");
      const certificateData = await TerminationCertificate.findOne({
        company: company._id,
        employee: employee._id,
        periodIndex: parseInt(periodIndex),
      });

      console.log("Certificate data raw:", {
        _id: certificateData?._id,
        signature: certificateData?.signature,
        signatureType: certificateData?.signature?.type,
        signatureDataExists: !!certificateData?.signature?.data,
        signatureDataLength: certificateData?.signature?.data?.length,
        signatureDate: certificateData?.signature?.date,
      });

      try {
        console.log("Loading UI19 template from:", "/templates/UI19new.pdf");
        const templatePath = path.join(__dirname, "../templates/UI19new.pdf");
        console.log("Full template path:", templatePath);

        // Load the template PDF
        const templateBytes = fs.readFileSync(templatePath);
        const pdfDoc = await PDFDocument.load(templateBytes);
        console.log("Template loaded successfully");

        // Get the form from the PDF
        const form = pdfDoc.getForm();
        console.log("Got form from PDF");

        // Get all form fields to see what's available
        const fields = form.getFields();
        console.log(
          "Available form fields:",
          fields.map((f) => f.getName())
        );

        // Get employer filing details
        const employerFilingDetails = await EmployerFilingDetails.findOne({
          company: company._id,
        });
        console.log("Employer filing details found:", !!employerFilingDetails);

        // Get employer details
        const employerDetails = await EmployerDetails.findOne({
          company: company._id,
        });
        console.log("Employer details found:", !!employerDetails);

        // Get payroll details with more specific query and logging
        console.log("\n=== Fetching Payroll Details ===");
        const payrollQuery = {
          company: company._id,
          employee: employee._id,
          month: {
            $lte: servicePeriod.endDate,
            $gte: moment(servicePeriod.endDate).startOf("month").toDate(),
          },
        };

        console.log("Payroll query:", {
          companyId: company._id.toString(),
          employeeId: employee._id.toString(),
          periodEndDate: servicePeriod.endDate,
          monthRange: {
            start: moment(servicePeriod.endDate)
              .startOf("month")
              .format("YYYY-MM-DD"),
            end: moment(servicePeriod.endDate).format("YYYY-MM-DD"),
          },
        });

        let payroll = await Payroll.findOne(payrollQuery).sort({ month: -1 });

        console.log("Initial payroll lookup results:", {
          found: !!payroll,
          payrollId: payroll?._id?.toString(),
          month: payroll?.month,
          basicSalary: payroll?.basicSalary,
          rawBasicSalary: payroll?.get("basicSalary"),
          payrollObject: payroll
            ? {
                ...payroll.toObject(),
                _id: payroll._id.toString(),
                company: payroll.company.toString(),
                employee: payroll.employee.toString(),
              }
            : null,
        });

        // If no payroll found, try to get the last available payroll
        if (!payroll) {
          console.log(
            "No payroll found for period, attempting to find last available payroll"
          );
          const lastPayrollQuery = {
            company: company._id,
            employee: employee._id,
          };

          console.log("Last payroll query:", lastPayrollQuery);

          const lastPayroll = await Payroll.findOne(lastPayrollQuery)
            .sort({ month: -1 })
            .lean();

          console.log("Last available payroll lookup:", {
            found: !!lastPayroll,
            payrollId: lastPayroll?._id?.toString(),
            month: lastPayroll?.month,
            basicSalary: lastPayroll?.basicSalary,
            fullPayroll: lastPayroll,
          });

          if (lastPayroll) {
            payroll = lastPayroll;
            console.log(
              "Using last available payroll with basic salary:",
              payroll.basicSalary
            );
          } else {
            console.log("No payroll records found at all for this employee");
          }
        }

        // Additional salary debugging
        console.log("\n=== Basic Salary Debug ===");
        if (payroll) {
          console.log("Payroll found:", {
            id: payroll._id?.toString(),
            basicSalaryDirect: payroll.basicSalary,
            basicSalaryGet: payroll.get
              ? payroll.get("basicSalary")
              : "get not available",
            hasBasicSalary: "basicSalary" in payroll,
            payrollKeys: Object.keys(payroll),
            dataType: typeof payroll.basicSalary,
            isNumber: !isNaN(payroll.basicSalary),
          });
        } else {
          console.log("No payroll record available for salary");
        }

        // Format salary with additional logging
        const formatSalary = (salary) => {
          console.log("Formatting salary:", {
            input: salary,
            type: typeof salary,
            isNull: salary === null,
            isUndefined: salary === undefined,
            isNumber: !isNaN(salary),
          });

          if (!salary && salary !== 0) return "";
          try {
            const formatted = salary.toFixed(2).toString();
            console.log("Formatted salary result:", formatted);
            return formatted;
          } catch (error) {
            console.error("Error formatting salary:", error);
            return "";
          }
        };

        // Format address
        const formatAddress = (details) => {
          if (!details?.physicalAddress) return "";
          const addr = details.physicalAddress;
          return [
            addr.unitNumber,
            addr.complex,
            addr.streetNumber,
            addr.street,
            addr.suburbDistrict,
            addr.cityTown,
            addr.code,
          ]
            .filter(Boolean)
            .join(", ");
        };

        // Get employee initials
        const getInitials = (firstName) => {
          return firstName ? firstName.charAt(0) : "";
        };

        // Get current month and year
        const currentMonthYear = moment().format("MMMM YYYY");

        // Get current date for FIELD_26_NTOD
        const currentDate = moment().format("DD/MM/YYYY");

        // Use certificate data if available, otherwise fallback to payroll
        const basicSalary =
          certificateData?.basicSalary || payroll?.basicSalary;
        console.log("\n=== Final Salary Selection ===");
        console.log("Selected basic salary:", {
          value: basicSalary,
          source: certificateData?.basicSalary ? "certificate" : "payroll",
          certificateValue: certificateData?.basicSalary,
          payrollValue: payroll?.basicSalary,
        });

        // Get total hours from certificate data or use default
        const totalHours = certificateData?.totalHours || 173.33;

        // Get UIF contributor status from employee
        const uifContributorStatus = employee?.isUifExempt ? "No" : "Yes";

        // Check for signature and stamp
        console.log("\n=== Checking Signature and Stamp ===");
        console.log("Certificate data:", {
          hasSignature: !!certificateData?.signature?.data,
          signatureDate: certificateData?.signature?.date,
          hasStamp: !!certificateData?.stamp?.data,
          stampDate: certificateData?.stamp?.uploadDate,
        });

        console.log("\n=== Detailed Signature Debug ===");
        console.log("Certificate signature data:", {
          exists: !!certificateData?.signature,
          type: certificateData?.signature?.type,
          hasData: !!certificateData?.signature?.data,
          dataLength: certificateData?.signature?.data?.length,
          date: certificateData?.signature?.date,
        });

        console.log("\n=== Signature Data Verification ===");
        if (certificateData?.signature) {
          console.log("Signature found:", {
            type: certificateData.signature.type,
            hasData: !!certificateData.signature.data,
            dataLength: certificateData.signature.data?.length,
            dataStartsWith: certificateData.signature.data?.substring(0, 30),
            date: certificateData.signature.date,
          });
        } else {
          console.log("No signature data in certificate");
        }

        // Prepare text data with the correct mappings
        const textData = {
          text_3mdwz: currentMonthYear,
          text_1przd: "FIELD_1_PRZD",
          text_27cryu: employerFilingDetails?.payeNumber || "",
          text_2xjqf: "FIELD_2_XJQF",
          text_4gcjl: employerDetails?.tradingName || "",
          text_5wpyr: formatAddress(employerDetails) || "",
          text_10evlg: "FIELD_10_EVLG",
          text_11okhn: employerFilingDetails?.contactPerson?.telephone || "",
          text_12htbs: employerFilingDetails?.contactPerson
            ? `${employerFilingDetails.contactPerson.firstName} ${employerFilingDetails.contactPerson.lastName}`
            : "",
          text_14hev: employee?.lastName || "",
          text_15nmdn: getInitials(employee?.firstName) || "",
          text_16w: employee?.idNumber || "",
          text_17vvks: formatSalary(basicSalary),
          text_18xljx: totalHours.toFixed(2),
          text_19cbys: employee?.doa
            ? moment(employee.doa).format("DD/MM/YYYY")
            : "",
          text_20zrhm: employee?.lastDayOfService
            ? moment(employee.lastDayOfService).format("DD/MM/YYYY")
            : "",
          text_21ennq: employee?.terminationReason || "",
          text_22ogaa: uifContributorStatus,
          text_23puom: "FIELD_23_PUOM",
          text_24fyyd: certificateData?.employerName || "",
          text_25cyxi: certificateData?.employerIdNumber || "",
          text_26ntod: currentDate,
        };

        console.log("Prepared text data:", textData);

        try {
          console.log("\n=== Filling Form Fields with Data ===");

          // Fill each field with the corresponding data
          for (const [fieldName, value] of Object.entries(textData)) {
            try {
              console.log(`\nProcessing field: ${fieldName}`);
              console.log("Value to set:", value);

              const field = form.getTextField(fieldName);

              if (field) {
                console.log("Field found, setting value");
                field.setText(value);

                // Verify the value was set
                const verifiedValue = field.getText();
                console.log("Value verification:", {
                  expected: value,
                  actual: verifiedValue,
                  matches: verifiedValue === value,
                });
              } else {
                console.log(`Field ${fieldName} not found or not accessible`);
              }
            } catch (fieldError) {
              console.error(`Error processing field ${fieldName}:`, {
                error: fieldError.message,
                stack: fieldError.stack,
              });
            }
          }

          // Enhanced signature debugging
          console.log("\n=== Detailed Signature Processing ===");
          if (certificateData?.signature?.data) {
            console.log("Processing signature for PDF:", {
              type: certificateData.signature.type,
              dataLength: certificateData.signature.data.length,
              isBase64: certificateData.signature.data.includes("base64"),
              dataStartsWith: certificateData.signature.data.substring(0, 50),
              date: certificateData.signature.date,
            });

            try {
              const page = pdfDoc.getPages()[0];
              const { width, height } = page.getSize();

              console.log("Page dimensions for signature:", {
                width,
                height,
                signatureX: width * 0.68,
                signatureY: height * 0.22,
                signatureWidth: width * 0.15,
                signatureHeight: height * 0.06,
              });

              if (
                certificateData.signature.type === "draw" ||
                certificateData.signature.type === "upload"
              ) {
                console.log("Processing image signature");

                try {
                  const base64Data =
                    certificateData.signature.data.split(",")[1] ||
                    certificateData.signature.data;

                  console.log("Base64 extraction:", {
                    originalLength: certificateData.signature.data.length,
                    extractedLength: base64Data.length,
                    startsWithData:
                      certificateData.signature.data.startsWith("data:"),
                    containsComma: certificateData.signature.data.includes(","),
                    previewExtracted: base64Data.substring(0, 50),
                  });

                  const signatureBytes = Buffer.from(base64Data, "base64");
                  console.log("Signature buffer created:", {
                    bytesLength: signatureBytes.length,
                    isBuffer: Buffer.isBuffer(signatureBytes),
                  });

                  const signatureImage = await pdfDoc.embedPng(signatureBytes);
                  console.log("Signature embedded:", {
                    width: signatureImage.width,
                    height: signatureImage.height,
                    ref: signatureImage.ref,
                  });

                  page.drawImage(signatureImage, {
                    x: 100,
                    y: 150,
                    width: width * 0.15,
                    height: height * 0.06,
                  });
                  console.log("Signature drawn to PDF");
                } catch (imageError) {
                  console.error("Image signature processing error:", {
                    name: imageError.name,
                    message: imageError.message,
                    stack: imageError.stack,
                  });
                }
              }
            } catch (error) {
              console.error("Signature processing error:", {
                name: error.name,
                message: error.message,
                stack: error.stack,
              });
            }
          } else {
            console.log("No signature data available:", {
              certificateExists: !!certificateData,
              hasSignatureObject: !!certificateData?.signature,
              signatureType: certificateData?.signature?.type,
              dataExists: !!certificateData?.signature?.data,
            });
          }

          // Add stamp if available
          if (certificateData?.stamp?.data) {
            try {
              console.log("\n=== Adding Stamp to PDF ===");

              // Convert base64 to bytes
              const stampBytes = Buffer.from(
                certificateData.stamp.data,
                "base64"
              );

              // Embed the stamp image
              const stampImage = await pdfDoc.embedPng(stampBytes);

              // Get the first page
              const page = pdfDoc.getPages()[0];

              // Add the stamp image (adjust dimensions and position as needed)
              page.drawImage(stampImage, {
                x: 450, // Adjust X position
                y: 100, // Adjust Y position
                width: 100, // Adjust width
                height: 100, // Adjust height
              });

              console.log("Stamp added successfully");
            } catch (stampError) {
              console.error("Error adding stamp:", stampError);
            }
          }

          console.log("\n=== Form Field Data Filling Complete ===");

          // Don't flatten form yet for testing
          // form.flatten();
        } catch (formError) {
          console.error("\n=== Form Processing Error ===", {
            name: formError.name,
            message: formError.message,
            stack: formError.stack,
            context: "During form field processing",
          });
          throw formError;
        }

        // Serialize the PDFDocument to bytes
        const pdfBytes = await pdfDoc.save();
        console.log("PDF document saved to bytes");

        // Set response headers
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename=UI19_${
            employee.companyEmployeeNumber || "certificate"
          }.pdf`
        );

        // Send the PDF bytes
        res.send(Buffer.from(pdfBytes));
        console.log("PDF sent to client successfully");
      } catch (pdfError) {
        console.error("PDF Generation Error:", {
          name: pdfError.name,
          message: pdfError.message,
          stack: pdfError.stack,
        });
        throw pdfError;
      }
    } catch (error) {
      console.error("Error in termination certificate route:", error);
      req.flash("error", "Error generating termination certificate");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// UI19 form submission route
router.post(
  "/:companyCode/employeeProfile/:employeeId/ui19/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== UI19 Form Submission Debug ===");
    console.log("Form Data Received:", {
      employerName: req.body.employerName,
      employerIdNumber: req.body.employerIdNumber,
      basicSalary: req.body.basicSalary,
      totalHours: req.body.totalHours,
      timestamp: new Date().toISOString(),
    });

    console.log("Request body validation:", {
      hasSignatureData: !!req.body.signatureData,
      signatureDataLength: req.body.signatureData?.length,
      signatureType: req.body.signatureType,
      timestamp: new Date().toISOString(),
    });

    try {
      const { companyCode, employeeId } = req.params;
      console.log("Route parameters:", { companyCode, employeeId });

      // Validate signature data
      if (!req.body.signatureData) {
        console.log("Error: Missing signature data");
        return res.status(400).json({ error: "Signature is required" });
      }

      // Get employee and company data
      console.log("\n=== Looking up Employee and Company ===");
      const employee = await Employee.findById(employeeId);
      const company = await Company.findOne({ companyCode });

      console.log("Lookup results:", {
        employeeFound: !!employee,
        companyFound: !!company,
        employeeId: employee?._id,
        companyCode: company?.companyCode,
        timestamp: new Date().toISOString(),
      });

      if (!employee || !company) {
        console.log("Error: Employee or company not found");
        return res.status(404).json({ error: "Employee or company not found" });
      }

      // Process signature type (handle array case)
      const signatureType = Array.isArray(req.body.signatureType)
        ? req.body.signatureType[0]
        : req.body.signatureType || "draw";

      console.log("Processing signature type:", {
        rawValue: req.body.signatureType,
        processedValue: signatureType,
        isArray: Array.isArray(req.body.signatureType),
        timestamp: new Date().toISOString(),
      });

      // Create or update UI19 document
      const ui19Data = {
        company: company._id,
        employee: employeeId,
        signature: {
          type: signatureType,
          data: req.body.signatureData,
          date: new Date(),
        },
        formData: {
          employerName: req.body.employerName,
          employerIdNumber: req.body.employerIdNumber,
          basicSalary: req.body.basicSalary,
          totalHours: req.body.totalHours,
          startDate: employee.doa,
          endDate: employee.lastDayOfService,
          terminationReason: getTerminationReasonFromCode(
            employee.uifStatusCode
          ),
        },
        modifiedBy: req.user._id,
      };

      console.log("\n=== UI19 Document Creation ===");
      console.log("Document structure:", {
        hasCompany: !!ui19Data.company,
        hasEmployee: !!ui19Data.employee,
        formData: ui19Data.formData,
        timestamp: new Date().toISOString(),
      });

      // Save the document
      const ui19Doc = await UI19.findOneAndUpdate(
        { company: company._id, employee: employeeId },
        ui19Data,
        { upsert: true, new: true }
      )
        .populate("company")
        .populate("employee");

      console.log("\n=== UI19 Document After Save ===");
      console.log("Saved document details:", {
        id: ui19Doc._id,
        hasFormData: !!ui19Doc.formData,
        formDataFields: ui19Doc.formData ? Object.keys(ui19Doc.formData) : [],
        companyPopulated: !!ui19Doc.company,
        employeePopulated: !!ui19Doc.employee,
        timestamp: new Date().toISOString(),
      });

      // Generate and send PDF
      console.log("\n=== Generating PDF ===");
      const pdfBuffer = await generateUI19PDF(ui19Doc);

      console.log("PDF generated:", {
        size: pdfBuffer.length,
        timestamp: new Date().toISOString(),
      });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=UI19_${companyCode}_${employeeId}.pdf`
      );

      res.send(pdfBuffer);
    } catch (error) {
      console.error("\n=== UI19 Form Submission Error ===", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      res.status(500).json({ error: "Error saving UI19 form" });
    }
  }
);

// Generate UI19 PDF function
async function generateUI19PDF(certificate) {
  console.log("\n=== PDF Generation Start ===");
  try {
    // Load the PDF template
    const templatePath = path.join(__dirname, "../templates/UI19new.pdf");
    const templateBytes = fs.readFileSync(templatePath);
    const pdfDoc = await PDFDocument.load(templateBytes);
    const page = pdfDoc.getPages()[0];
    const form = pdfDoc.getForm();
    const fields = form.getFields();

    console.log("\n=== Available PDF Fields ===");
    // Fill each field with its own name for mapping
    fields.forEach((field) => {
      const fieldName = field.getName();
      console.log(`Filling field: ${fieldName}`);
      if (field instanceof PDFTextField) {
        field.setText(fieldName);
      }
    });

    // Add the signature if present
    if (certificate.signature?.data) {
      try {
        console.log("\n=== Processing Signature for PDF ===");
        const signatureData = certificate.signature.data;

        // Remove the data:image/png;base64 prefix if present
        const base64Data = signatureData.replace(
          /^data:image\/png;base64,/,
          ""
        );

        console.log("Signature processing details:", {
          originalLength: signatureData.length,
          processedLength: base64Data.length,
          hasBase64Prefix: signatureData.startsWith("data:image/png;base64,"),
          type: certificate.signature.type,
        });

        // Convert base64 to bytes
        const signatureBytes = Buffer.from(base64Data, "base64");
        const signatureImage = await pdfDoc.embedPng(signatureBytes);

        // Get image dimensions
        const imageDims = signatureImage.scale(1);
        console.log("Original image dimensions:", {
          width: imageDims.width,
          height: imageDims.height,
        });

        // Calculate signature dimensions maintaining aspect ratio
        const maxWidth = 100;
        const maxHeight = 50;
        let signatureWidth = maxWidth;
        let signatureHeight = (maxWidth / imageDims.width) * imageDims.height;

        // Adjust if height exceeds maximum
        if (signatureHeight > maxHeight) {
          signatureHeight = maxHeight;
          signatureWidth = (maxHeight / imageDims.height) * imageDims.width;
        }

        // Position the signature (adjusted coordinates)
        const signatureX = 160; // Moved left from 400 to 100
        const signatureY = 185; // Moved lower from 200 to 150

        console.log("Final signature placement:", {
          x: signatureX,
          y: signatureY,
          width: signatureWidth,
          height: signatureHeight,
        });

        // Draw the signature with adjusted transparency settings
        page.drawImage(signatureImage, {
          x: signatureX,
          y: signatureY,
          width: signatureWidth,
          height: signatureHeight,
          opacity: 0.9, // Increased opacity from 0.3 to 0.8
          blendMode: "Multiply",
          colorSpace: "DeviceRGB",
          alphaChannel: true,
          graphicsState: {
            opacity: 2, // Increased opacity from 0 to 0.8
            blendMode: "Normal",
          },
        });

        console.log("Signature added successfully with transparency");
      } catch (signatureError) {
        console.error("Error processing signature:", {
          name: signatureError.name,
          message: signatureError.message,
          stack: signatureError.stack,
        });
      }
    }

    // Don't flatten the form for testing
    // form.flatten();

    const pdfBytes = await pdfDoc.save();
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error("\n=== PDF Generation Error ===", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

// Helper function to get termination reason from UIF code
function getTerminationReasonFromCode(code) {
  const reasons = {
    2: "Deceased",
    3: "Retired",
    4: "Dismissed",
    5: "Contract Expired",
    6: "Resigned",
    7: "Constructive Dismissal",
    8: "Insolvency/Liquidation",
    9: "Maternity/Adoption",
    10: "Long-term illness",
    11: "Retrenched/Staff Reduction",
    12: "Transfer to another Branch",
    13: "Absconded",
    14: "Business Closed",
    15: "Death of Domestic Employer",
    16: "Voluntary Severance Package (VSP)",
    17: "Reduced Working Time",
    18: "Commissioning Parental",
    19: "Parental",
  };
  return reasons[code] || "Unknown reason";
}

module.exports = router;
