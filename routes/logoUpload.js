const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { S3Client, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const Company = require('../models/Company');
const EmployerDetails = require('../models/employerDetails');
const { ensureAuthenticated } = require('../middleware/auth');

// Initialize S3 Client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'eu-north-1'
});
const s3BucketName = process.env.S3_BUCKET_NAME || 'payrollpanda';

// Ensure uploads directory exists for fallback
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for memory storage (for S3 upload)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    
    // Check file type
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPG, PNG, GIF, WebP) are allowed'));
    }
  },
});

// Function to upload to S3
async function uploadToS3(buffer, filename, mimetype) {
  const key = `employer-logos/${filename}`;
  
  const uploadParams = {
    Bucket: s3BucketName,
    Key: key,
    Body: buffer,
    ContentType: mimetype,
    ACL: 'public-read'
  };

  try {
    const command = new PutObjectCommand(uploadParams);
    await s3Client.send(command);
    
    const publicUrl = `https://${s3BucketName}.s3.${process.env.AWS_REGION || 'eu-north-1'}.amazonaws.com/${key}`;
    return publicUrl;
  } catch (error) {
    console.error('S3 upload failed:', error);
    throw error;
  }
}

// Function to save to local storage (fallback)
async function saveToLocal(buffer, filename) {
  const filePath = path.join(uploadsDir, filename);
  
  try {
    await fs.promises.writeFile(filePath, buffer);
    const publicUrl = `/uploads/${filename}`;
    return publicUrl;
  } catch (error) {
    console.error('Local storage failed:', error);
    throw error;
  }
}

// Function to delete from S3
async function deleteFromS3(logoUrl) {
  if (!logoUrl || !logoUrl.includes(s3BucketName)) {
    return;
  }
  
  try {
    const key = logoUrl.split('.amazonaws.com/')[1];
    const deleteParams = {
      Bucket: s3BucketName,
      Key: key
    };
    
    const command = new DeleteObjectCommand(deleteParams);
    await s3Client.send(command);
  } catch (error) {
    console.error('S3 deletion failed:', error);
  }
}

// Logo upload endpoint
router.post('/:companyCode/upload-logo', ensureAuthenticated, (req, res) => {
  
  upload.single('logo')(req, res, async function(err) {
    if (err) {
      console.error('Multer error:', err);
      return res.status(400).json({
        success: false,
        message: err.message || 'File upload failed',
        error: err.toString()
      });
    }
    
    if (!req.file) {
      console.error('No file uploaded');
      return res.status(400).json({
        success: false,
        message: 'No file was uploaded'
      });
    }
    
    try {
      console.log('File details:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });
      
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      
      if (!company) {
        return res.status(404).json({
          success: false,
          message: 'Company not found'
        });
      }
      
      // Generate unique filename
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const filename = `logo-${uniqueSuffix}${path.extname(req.file.originalname)}`;
      
      let logoUrl;
      
      // Try S3 upload first, fallback to local storage
      try {
        logoUrl = await uploadToS3(req.file.buffer, filename, req.file.mimetype);
      } catch (s3Error) {
        logoUrl = await saveToLocal(req.file.buffer, filename);
      }
      
      // Get existing employer details to delete old logo
      const existingDetails = await EmployerDetails.findOne({ company: company._id });
      const oldLogoUrl = existingDetails?.logo;
      
      // Update employer details with new logo URL
      const employerDetails = await EmployerDetails.findOneAndUpdate(
        { company: company._id },
        { logo: logoUrl },
        { new: true, upsert: true, runValidators: true }
      );
      
      // Delete old logo if it exists and is different
      if (oldLogoUrl && oldLogoUrl !== logoUrl) {
        if (oldLogoUrl.includes(s3BucketName)) {
          await deleteFromS3(oldLogoUrl);
        } else if (oldLogoUrl.startsWith('/uploads/')) {
          // Delete local file
          const oldFilePath = path.join(uploadsDir, path.basename(oldLogoUrl));
          try {
            await fs.promises.unlink(oldFilePath);
          } catch (deleteError) {
          }
        }
      }
      
      
      return res.json({
        success: true,
        message: 'Logo uploaded successfully',
        logoUrl: logoUrl,
        file: {
          name: req.file.originalname,
          size: req.file.size,
          type: req.file.mimetype
        }
      });
      
    } catch (error) {
      console.error('Logo upload error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to upload logo',
        error: error.message
      });
    }
  });
});

// Logo delete endpoint
router.delete('/:companyCode/delete-logo', ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;
    const company = await Company.findOne({ companyCode });
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }
    
    const employerDetails = await EmployerDetails.findOne({ company: company._id });
    
    if (!employerDetails || !employerDetails.logo) {
      return res.status(404).json({
        success: false,
        message: 'No logo found to delete'
      });
    }
    
    const logoUrl = employerDetails.logo;
    
    // Delete from storage
    if (logoUrl.includes(s3BucketName)) {
      await deleteFromS3(logoUrl);
    } else if (logoUrl.startsWith('/uploads/')) {
      const filePath = path.join(uploadsDir, path.basename(logoUrl));
      try {
        await fs.promises.unlink(filePath);
      } catch (deleteError) {
      }
    }
    
    // Remove logo from database
    await EmployerDetails.findOneAndUpdate(
      { company: company._id },
      { $unset: { logo: 1 } }
    );
    
    return res.json({
      success: true,
      message: 'Logo deleted successfully'
    });
    
  } catch (error) {
    console.error('Logo deletion error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete logo',
      error: error.message
    });
  }
});

module.exports = router;
