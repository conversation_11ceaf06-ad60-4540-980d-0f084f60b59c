const express = require('express');
const router = express.Router();
const oidReturnController = require('../controllers/oidReturnController');
const { ensureAuthenticated } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(ensureAuthenticated);

// Generate OID return for a specific period
router.post('/generate', oidReturnController.generateReturn);

// Get OID return summary
router.get('/summary', oidReturnController.getReturnSummary);

module.exports = router;
