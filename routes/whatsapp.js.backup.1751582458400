const express = require('express');
const router = express.Router();
const whatsappService = require('../services/whatsappService');
const { validateSignature } = require('../middleware/whatsappMiddleware');

// Verify webhook
router.get('/webhook', (req, res) => {
  const VERIFY_TOKEN = process.env.WHATSAPP_VERIFY_TOKEN || 'panda_payroll_secure_token_2025';
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];
  
  console.log('=== WhatsApp Webhook Verification ===');
  console.log('Mode:', mode);
  console.log('Token received:', token);
  console.log('Expected token (environment variable):', VERIFY_TOKEN);
  console.log('Challenge:', challenge);
  
  if (mode && token && mode === 'subscribe' && token === VERIFY_TOKEN) {
    console.log('Webhook verified successfully.');
    res.status(200).send(challenge);
  } else {
    console.warn('Webhook verification failed.');
    res.sendStatus(403);
  }
});

// Handle incoming messages with signature validation
router.post('/webhook', async (req, res) => {
  // CRITICAL: Respond to WhatsApp IMMEDIATELY to prevent webhook deactivation
  // WhatsApp requires response within 20 seconds or will disable webhook
  res.sendStatus(200);

  console.log('WhatsApp webhook received:', {
    from: req.body.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.from,
    messageType: req.body.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.type,
    timestamp: new Date().toISOString(),
    hasSignature: !!req.headers['x-hub-signature-256'],
    hasAppSecret: !!process.env.WHATSAPP_APP_SECRET
  });

  // Validate signature for security (but don't block processing if it fails)
  let signatureValid = false;
  const signature = req.headers['x-hub-signature-256'];
  const appSecret = process.env.WHATSAPP_APP_SECRET;

  // Attempt signature validation if both signature and secret are available
  if (appSecret && signature) {
    try {
      const crypto = require('crypto');
      // For signature validation, we need the raw body as string
      const rawBodyString = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      const expectedSignature = 'sha256=' + crypto
        .createHmac('sha256', appSecret)
        .update(rawBodyString, 'utf8')
        .digest('hex');

      if (signature === expectedSignature) {
        console.log('WhatsApp webhook: Signature validated successfully');
        signatureValid = true;
      } else {
        console.warn('WhatsApp webhook: Invalid signature - processing anyway to prevent webhook deactivation');
      }
    } catch (error) {
      console.error('WhatsApp webhook signature validation error:', error);
      console.warn('WhatsApp webhook: Signature validation failed - processing anyway to prevent webhook deactivation');
    }
  } else {
    if (process.env.NODE_ENV === 'production') {
      console.warn('WhatsApp webhook: Missing signature or app secret in production - processing anyway to prevent webhook deactivation');
    } else {
      console.log('WhatsApp webhook: No signature validation in development mode');
    }
  }

  // Log signature validation status but continue processing
  console.log(`WhatsApp webhook: Processing request (signature valid: ${signatureValid})`)

  // Ensure we have a valid body object
  let parsedBody;
  if (typeof req.body === 'object' && req.body !== null) {
    // Body is already parsed (normal case)
    parsedBody = req.body;
  } else if (typeof req.body === 'string') {
    // Body is a string, need to parse it
    try {
      parsedBody = JSON.parse(req.body);
    } catch (error) {
      console.error('WhatsApp webhook: Invalid JSON body', error.message);
      return; // Already sent 200 response, just return
    }
  } else {
    console.error('WhatsApp webhook: Unexpected body type:', typeof req.body);
    return; // Already sent 200 response, just return
  }

  // Use the parsed body
  req.body = parsedBody;

  try {
    if (!req.body.entry?.[0]?.changes?.[0]?.value?.messages?.[0]) {
      // This wasn't a message event, nothing more to do after acknowledging
      return; // Or return res.sendStatus(200) if not sent at the top
    }

    const message = req.body.entry[0].changes[0].value.messages[0];
    const from = message.from; // Sender's phone number
    const messageBody = message.text?.body?.toLowerCase() || '';

    console.log('Received message:', {
      from,
      messageBody,
      timestamp: new Date().toISOString()
    });

    // First, try to authenticate the user
    const auth = await whatsappService.authenticateEmployee(from);
    if (!auth) {
      await whatsappService.sendMessage(from,
        'Sorry, I could not find an employee account with this phone number. ' +
        'Please contact your HR department to verify your phone number.'
      );
      return;
    }

    // 1. Handle "hi" / "hello" to always allow resetting to main menu
    if (messageBody === 'hi' || messageBody === 'hello') {
      await whatsappService.sendInteractiveMenu(from, auth.employee.firstName);
      return;
    }
    // 2. Handle interactive menu selections (like choosing a leave type or payslip period)
    else if (message.type === 'interactive' && message.interactive?.list_reply) {
      const selectedId = message.interactive.list_reply.id;
      const isObjectId = (id) => /^[0-9a-fA-F]{24}$/.test(id);

      if (selectedId === 'get_payslip') {
        const result = await whatsappService.handlePayslipRequest(auth.employee);
        if (result.message) {
          await whatsappService.sendMessage(from, result.message);
        }
      } else if (selectedId === 'irp5') {
        const result = await whatsappService.handleIRP5Request(auth.employee);
        if (result.message) {
          await whatsappService.sendMessage(from, result.message);
        }
      } else if (selectedId.startsWith('irp5_')) {
        const taxYear = selectedId.replace('irp5_', '');
        const result = await whatsappService.handleIRP5Request(auth.employee, taxYear);
        if (!result.success && result.message) {
          await whatsappService.sendMessage(from, result.message);
        }
      } else if (selectedId === 'leave_request') {
        await whatsappService.sendLeaveTypeList(auth.employee);
      } else if (selectedId.startsWith('leave_type_')) {
        const leaveTypeId = selectedId.replace('leave_type_', '');
        await whatsappService.handleLeaveTypeSelection(auth.employee, leaveTypeId);
      } else if (isObjectId(selectedId)) {
        const result = await whatsappService.handlePayslipRequest(auth.employee, selectedId);
        if (!result.success && result.message) {
          await whatsappService.sendMessage(from, result.message);
        }
      }
      return;
    }
    // 3. Handle ongoing conversational flows (e.g., date or reason input for leave request)
    // This must come BEFORE other general plain text commands to ensure multi-step inputs are captured.
    const session = await require('../services/sessionService').getSession(from);
    if (session && session.leaveRequest) {
      await whatsappService.handleModernLeaveRequest(auth.employee, message.text?.body || '');
      return;
    }

    // Handle text-based leave type selection (fallback when interactive lists fail)
    if (session && session.step === 'awaiting_leave_type_text') {
      const messageText = message.text?.body || '';
      const selectedNumber = parseInt(messageText.trim());

      if (selectedNumber && selectedNumber > 0 && selectedNumber <= session.leaveTypes.length) {
        const selectedLeaveType = session.leaveTypes[selectedNumber - 1];
        await whatsappService.handleLeaveTypeSelection(auth.employee, selectedLeaveType._id);
      } else {
        await whatsappService.sendMessage(from,
          `Please enter a valid number between 1 and ${session.leaveTypes.length}.`
        );
      }
      return;
    }
    // 4. Handle other specific plain text commands (initial triggers or other features)
    else if (messageBody === 'payslip' || messageBody === '1') {
      const result = await whatsappService.handlePayslipRequest(auth.employee);
      if (result.success) {
        console.log('Payslip PDF processed successfully after plain text command.');
      } else if (result.message) {
        await whatsappService.sendMessage(from, result.message);
      }
      return;
    }
    else if (messageBody.startsWith('payslip ')) {
      const parts = messageBody.split(' ');
      const period = parts[1];
      const result = await whatsappService.handlePayslipRequest(auth.employee, period);
      if (!result.success && result.message) {
        await whatsappService.sendMessage(from, result.message);
      }
      return;
    }
    else if (messageBody === '2' || messageBody === 'irp5') {
      const result = await whatsappService.handleIRP5Request(auth.employee);
      if (result.success) {
        console.log('IRP5 request processed successfully after plain text command.');
      } else if (result.message) {
        await whatsappService.sendMessage(from, result.message);
      }
      return;
    }
    else if (messageBody.startsWith('irp5 ')) {
      const parts = messageBody.split(' ');
      const year = parts[1];
      const result = await whatsappService.handleIRP5Request(auth.employee, year);
      if (!result.success && result.message) {
        await whatsappService.sendMessage(from, result.message);
      }
      return;
    }
    // Initial trigger for leave request if not through interactive menu, after session check
    else if (messageBody === '3' || messageBody === 'leave' || messageBody === 'apply for leave') {
      await whatsappService.sendLeaveTypeList(auth.employee);
      return;
    }
    else if (messageBody === 'leave balance') {
      await whatsappService.sendMessage(from, 'Leave balance feature coming soon!');
      return;
    }
    // 5. Fallback for unrecognized commands
    else {
      await whatsappService.sendMessage(from,
        'Sorry, I didn\'t understand that command. Send "hi" to see the main menu options.'
      );
    }

  } catch (error) {
    console.error('Webhook Error:', error);
    // Consider sending a generic error message to the user here
    // await whatsappService.sendMessage(from, 'An internal error occurred.');
    // Do NOT send res.sendStatus(500) here as 200 was already sent.
  }
});

module.exports = router; 