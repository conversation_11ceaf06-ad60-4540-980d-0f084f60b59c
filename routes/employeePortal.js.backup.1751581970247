const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const User = require("../models/user");
const Payroll = require("../models/Payroll");
const { ensureAuthenticated } = require("../middleware/auth");
const bcrypt = require("bcrypt");
const EmployeeCredential = require("../models/employeeCredential");
const passport = require("passport");

// Middleware to ensure user is an employee
const ensureEmployeeRole = async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id).populate('role');
    if (!user || user.role.name !== 'employee') {
      req.flash("error", "Access denied. Employee role required.");
      return res.redirect("/auth/login");
    }
    next();
  } catch (error) {
    console.error("Error in ensureEmployeeRole middleware:", error);
    req.flash("error", "An error occurred");
    res.redirect("/auth/login");
  }
};

// Middleware to check if the employee is accessing their own portal
const ensureOwnPortal = async (req, res, next) => {
  try {
    if (req.params.userId !== req.user._id.toString()) {
      console.log('User attempting to access another user\'s portal');
      return res.status(403).redirect('/auth/login');
    }
    next();
  } catch (error) {
    console.error('Error in ensureOwnPortal:', error);
    res.status(500).redirect('/auth/login');
  }
};

// Main employee portal page
router.get(
  "/:userId",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  async (req, res) => {
    try {
      console.log('Accessing employee portal for user:', req.params.userId);
      
      const employee = await Employee.findOne({ user: req.user._id })
        .populate('company')
        .populate('payFrequency');
        
      if (!employee) {
        console.error('No employee record found for user:', req.user._id);
        return res.status(404).redirect('/auth/login');
      }

      console.log('Found employee record:', employee._id);

      // Get the last 6 months of payroll data for the chart
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      const payrollHistory = await Payroll.find({
        employee: employee._id,
        month: { $gte: sixMonthsAgo }
      }).sort({ month: 1 });

      const chartData = payrollHistory.map(payroll => ({
        month: payroll.month,
        basicSalary: payroll.basicSalary || 0
      }));
      
      res.render("employeePortal", {
        user: req.user,
        employee: employee,
        company: employee.company,
        pageTitle: 'Employee Portal',
        latestPayroll: payrollHistory[payrollHistory.length - 1] || null,
        chartData: JSON.stringify(chartData),
      });
    } catch (error) {
      console.error('Error in employee portal route:', error);
      res.status(500).redirect('/auth/login');
    }
  }
);

// Profile page
router.get(
  "/:employeeId/profile",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  (req, res) => {
    res.render("employeeProfile", { employee: req.employee });
  }
);

// Payslips page
router.get(
  "/:employeeId/payslips",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  (req, res) => {
    res.render("employeePayslips", { employee: req.employee });
  }
);

// Leave requests page
router.get(
  "/:employeeId/leave",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  (req, res) => {
    res.render("employeeLeave", { employee: req.employee });
  }
);

// Documents page
router.get(
  "/:employeeId/documents",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  (req, res) => {
    res.render("employeeDocuments", { employee: req.employee });
  }
);

// Settings page
router.get(
  "/:employeeId/settings",
  ensureAuthenticated,
  ensureEmployeeRole,
  ensureOwnPortal,
  (req, res) => {
    res.render("employeeSettings", { employee: req.employee });
  }
);

// Self-service setup routes
router.get("/self-service/setup/:token", async (req, res) => {
  try {
    const { token } = req.params;
    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      return res.status(400).send("Invalid or expired token");
    }

    res.render("selfServiceSetup", { token });
  } catch (error) {
    console.error("Error in self-service setup:", error);
    res.status(500).send("An error occurred during the setup process");
  }
});

router.post("/self-service/setup/:token", async (req, res) => {
  try {
    const { token } = req.params;
    const { password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      return res.status(400).send("Passwords do not match");
    }

    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      return res.status(400).send("Invalid or expired token");
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const employeeCredential = new EmployeeCredential({
      employee: employee._id,
      email: employee.email,
      password: hashedPassword,
    });

    await employeeCredential.save();

    employee.selfServiceToken = undefined;
    employee.selfServiceTokenExpiration = undefined;
    employee.selfServiceActivated = true;
    await employee.save();

    req.flash(
      "success",
      "Your account has been set up successfully. You can now log in."
    );
    res.redirect("/employee/login");
  } catch (error) {
    console.error("Error in self-service setup:", error);
    res.status(500).send("An error occurred during the setup process");
  }
});

router.get("/employee/login", (req, res) => {
  res.render("employeeLogin");
});

router.post(
  "/employee/login",
  passport.authenticate("employee-local", {
    successRedirect: "/employee/portal",
    failureRedirect: "/employee/login",
    failureFlash: true,
  })
);

router.get(
  "/employee/portal",
  ensureAuthenticated,
  ensureEmployeeRole,
  (req, res) => {
    res.render("employeePortal", { user: req.user, employee: req.user });
  }
);

module.exports = router;
