const express = require("express");
const router = express.Router();
const mongoose = require("mongoose"); // Add this import
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayrollPeriod = require("../models/PayrollPeriod");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const {
  formatDateForMongoDB,
  formatDateForDisplay,
  getCurrentOrNextLastDayOfMonth,
} = require("../utils/dateUtils");
const { calculateAnnualBonusTax } = require("../utils/payrollCalculations");
const PayrollService = require("../services/PayrollService");
const moment = require("moment");
const Company = require("../models/Company");
const OnceOffPayment = require("../models/OnceOffPayment");
const Beneficiary = require("../models/Beneficiary");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Apply CSRF protection to all routes in this router
router.use(csrfProtection);

// Debug middleware
router.use((req, res, next) => {
  console.log("\n=== Forms Route Debug ===");
  console.log("Request path:", req.path);
  console.log(
    "Route stack:",
    router.stack.map((layer) => ({
      path: layer.route?.path,
      methods: layer.route?.methods,
    }))
  );
  next();
});

// Add at the top of the router
router.use((req, res, next) => {
  console.log("\n=== Forms Router Debug ===");
  console.log({
    originalUrl: req.originalUrl,
    path: req.path,
    method: req.method,
    params: req.params,
    body: req.method === "POST" ? req.body : undefined,
    headers: {
      "csrf-token": req.headers["csrf-token"],
      "content-type": req.headers["content-type"],
    },
    session: req.session
      ? {
          csrfSecret: req.session._csrf,
          hasPassport: !!req.session.passport,
        }
      : "No session",
  });
  next();
});

// Remove component route - Moving this before other routes to ensure it matches first
router.post(
  [
    "/clients/:companyCode/regularInputs/:employeeId/remove/:componentType",
    "/:companyCode/regularInputs/:employeeId/remove/:componentType",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Remove Component Route Hit ===");
    console.log("Full request details:", {
      url: req.originalUrl,
      method: req.method,
      params: req.params,
      body: req.body,
      headers: req.headers,
      isAuthenticated: req.isAuthenticated(),
      user: req.user?._id,
      csrf: {
        token: req.headers["csrf-token"],
        bodyToken: req.body._csrf,
        hasSession: !!req.session,
        sessionSecret: req.session?._csrf,
      },
    });

    const { employeeId, companyCode, componentType } = req.params;
    console.log("\n=== Remove Component Parameters ===");
    console.log({
      employeeId,
      companyCode,
      componentType,
      url: req.originalUrl,
    });

    try {
      console.log("\n=== Starting Component Removal ===");
      let result;

      console.log("Processing component type:", componentType);
      switch (componentType) {
        case "travel-allowance":
          console.log(
            "Calling PayrollService.removeComponent for travel allowance"
          );
          result = await PayrollService.removeComponent(
            employeeId,
            componentType
          );
          console.log("Remove component result:", result);
          break;
        case "maintenance-order":
          result = await PayrollService.updateCourtOrder(
            employeeId,
            "maintenance-order",
            0,
            new Date()
          );
          break;
        default:
          result = await PayrollService.removeComponent(
            employeeId,
            componentType
          );
      }

      console.log("\n=== Component Removal Complete ===");
      console.log(
        "Redirecting to:",
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );

      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("\n=== Component Removal Error ===");
      console.error({
        message: error.message,
        stack: error.stack,
        componentType,
        employeeId,
      });

      req.flash("error", `Failed to remove ${componentType}: ${error.message}`);
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

// GET route for payslip input
router.get(
  "/:companyCode/employeeProfile/:employeeId/payslipInput",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Get current payroll period
      const thisMonth = getCurrentOrNextLastDayOfMonth();

      // Form type mapping for onceOff forms
      const formTypes = [
        "annual-bonus",
        "annual-payment",
        "arbitration-award",
        "dividends-subject",
        "extra-pay",
        "leave-paid-out",
        "commission",
        "restraint-of-trade",
        "broad-based-share-plan",
        "computer-allowance",
        "expense-claim",
        "gain-vesting",
        "phone-allowance",
        "relocation-allowance",
        "subsistence-international",
        "subsistence-local",
        "tool-allowance",
        "uniform-allowance",
        "bursaries-scholarships",
        "employee-debt-benefit",
        "medical-costs",
        "donations",
        "repayment-advance",
        "staff-purchases",
        "garnishee",
        "maintenance-order",
        "pension-fund",
        "provident-fund",
        "retirement-annuity-fund",
        "income-protection",
        "union-fee",
        "savings",
        "employer-loan",
        "loss-of-income",
        "covid-relief",
        "long-service-award",
        "ters-payout",
        "termination-lump-sums",
        "voluntary-tax",
        "foreign-service",
        "tax-directive",
        "computer-allowance",
        "phone-allowance",
        "tool-allowance",
        "uniform-allowance",
        "relocation-allowance",
        "subsistence-international",
        "subsistence-local",
        "broad-based-share-plan",
        "expense-claim",
        "gain-vesting",
        "travel-allowance",
        "accommodation-benefit",
      ];

      // Get existing payroll data
      const existingPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(thisMonth),
      });

      // Create display names and template names
      const displayNames = {};
      const templateNames = {};
      formTypes.forEach((type) => {
        // Create display name (capitalize words)
        displayNames[type] = type
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");

        // Create template name
        templateNames[type] = `onceOff${displayNames[type].replace(/\s/g, "")}`;
      });

      res.render("add-once-off-payslips", {
        employee,
        company: employee.company,
        companyCode,
        thisMonth,
        formTypes,
        displayNames,
        templateNames,
        existingPayroll,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        formatDateForDisplay,
        currentPath: req.path,
      });
    } catch (error) {
      console.error("Error loading payslip input form:", error);
      req.flash("error", "Error loading payslip input form");
      res.redirect("/");
    }
  }
);

// GET route for once-off forms list
router.get(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/onceOff",
    "/:companyCode/employeeProfile/:employeeId/onceOff",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Template names mapping (used in URLs)
      const templateNames = {
        "annual-bonus": "onceOffAnnualBonus",
        "annual-payment": "onceOffAnnualPayment",
        "arbitration-award": "onceOffArbitrationAward",
        "dividends-subject": "onceOffDividendsSubject",
        "extra-pay": "onceOffExtraPay",
        "leave-paid-out": "onceOffLeavePaidOut",
        commission: "onceOffCommission",
        "restraint-of-trade": "onceOffRestraintOfTrade",
        "broad-based-share-plan": "onceOffBroadBasedEmployeeSharePlan",
        "computer-allowance": "onceOffComputerAllowance",
        "expense-claim": "onceOffExpenseClaim",
        "gain-vesting": "onceOffGainVesting",
        "phone-allowance": "onceOffPhoneAllowance",
        "relocation-allowance": "onceOffRelocationAllowance",
        "subsistence-international": "onceOffSubsistenceAllowanceInternational",
        "subsistence-local": "onceOffSubsistenceAllowanceLocal",
        "tool-allowance": "onceOffToolAllowance",
        "uniform-allowance": "onceOffUniformAllowance",
        "bursaries-scholarships": "onceOffBursariesAndScholarships",
        "employee-debt-benefit": "onceOffEmployeeDebtBenefit",
        "medical-costs": "onceOffMedicalCosts",
        donations: "onceOffDonations",
        "repayment-advance": "onceOffRepaymentOfAdvance",
        "staff-purchases": "onceOffStaffPurchases",
        "covid-relief": "onceOffCovidRelief",
        "long-service-award": "onceOffLongServiceAward",
        "ters-payout": "onceOffTersPayout",
        "termination-lump-sums": "onceOffTerminationLumpSums",
      };

      // Display names mapping (shown in UI)
      const displayNames = {
        "annual-bonus": "Annual Bonus",
        "annual-payment": "Annual Payment",
        "arbitration-award": "Arbitration Award",
        "dividends-subject": "Dividends Subject",
        "extra-pay": "Extra Pay",
        "leave-paid-out": "Leave Paid Out",
        commission: "Commission",
        "restraint-of-trade": "Restraint of Trade",
        "broad-based-share-plan": "Broad Based Share Plan",
        "computer-allowance": "Computer Allowance",
        "expense-claim": "Expense Claim",
        "gain-vesting": "Gain Vesting",
        "phone-allowance": "Phone Allowance",
        "relocation-allowance": "Relocation Allowance",
        "subsistence-international": "Subsistence (International)",
        "subsistence-local": "Subsistence (Local)",
        "tool-allowance": "Tool Allowance",
        "uniform-allowance": "Uniform Allowance",
        "bursaries-scholarships": "Bursaries & Scholarships",
        "employee-debt-benefit": "Employee Debt Benefit",
        "medical-costs": "Medical Costs",
        donations: "Donations",
        "repayment-advance": "Repayment Advance",
        "staff-purchases": "Staff Purchases",
        "covid-relief": "COVID-19 Relief",
        "long-service-award": "Long Service Award",
        "ters-payout": "TERS Payout",
        "termination-lump-sums": "Termination Lump Sums",
      };

      res.render("add-once-off-payslips", {
        employee,
        company: employee.company,
        templateNames,
        displayNames,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        currentPath: req.path,
      });
    } catch (error) {
      console.error("Error loading once-off forms list:", error);
      req.flash("error", "Error loading once-off forms list");
      res.redirect("/");
    }
  }
);

// GET route for regular inputs
router.get(
  "/:companyCode/employeeProfile/:employeeId/regularInputs",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("\n=== Regular Inputs Route ===");
      console.log("Params:", { companyCode, employeeId });

      // Find employee and company
      const employee = await Employee.findById(employeeId).populate("company");
      const company = await Company.findOne({ companyCode });

      if (!employee || !company) {
        req.flash(
          "error",
          company ? "Employee not found" : "Company not found"
        );
        return res.redirect(
          company ? `/clients/${companyCode}/employeeManagement` : "/clients"
        );
      }

      // Define form type mapping
      const formTypeMapping = {
        commission: "commission",
        "loss-of-income": "loss-of-income",
        "travel-allowance": "travel-allowance",
        "accommodation-benefit": "accommodation-benefit",
        "bursaries-and-scholarships": "bursaries-and-scholarships",
        "company-car": "company-car",
        "company-car-under-operating-lease":
          "company-car-under-operating-lease",
        garnishee: "garnishee",
        "income-protection": "income-protection",
        "maintenance-order": "maintenance-order",
        "medical-aid": "medical-aid",
        "pension-fund": "pension-fund",
        "provident-fund": "provident-fund",
        "retirement-annuity-fund": "retirement-annuity-fund",
        union_membership_fee: "union-membership-fee",
        "voluntary-tax-over-deduction": "voluntary-tax-over-deduction",
        "employer-loan": "employer-loan",
        "foreign-service-income": "foreign-service-income",
        savings: "savings",
        "tax-directive": "tax-directive",
      };

      console.log("Rendering regularInputs template with:", {
        employeeId: employee._id,
        companyCode,
        formTypes: Object.keys(formTypeMapping),
      });

      // Render the regularInputs template
      res.render("regularInputs", {
        employee,
        company,
        companyCode,
        formTypeMapping,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        partialsDir: "partials",
        req: req,
        currentPath: req.path,
      });
    } catch (error) {
      console.error("Error in regular inputs route:", error);
      return res
        .status(500)
        .json({ error: "An error occurred loading the regular inputs page" });
    }
  }
);

// GET route for regular inputs API
router.get(
  "/:companyCode/api/employee/:employeeId/regularInputs",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        return res.status(404).json({ error: "Employee not found" });
      }

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(getCurrentOrNextLastDayOfMonth()),
      });

      res.json({
        success: true,
        data: {
          payroll,
          employee,
        },
      });
    } catch (error) {
      console.error("Error fetching regular inputs:", error);
      return res.status(500).json({ error: "Error fetching regular inputs" });
    }
  }
);

// POST route for updating basic salary
router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, relevantDate, basicSalary } = req.body;
      const { companyCode } = req.params;

      console.log("Updating basic salary:", {
        employeeId,
        relevantDate,
        basicSalary,
        companyCode,
      });

      // Find employee and company
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = new Date(2024, 11, 31); // Set to December 31, 2024
      const currentPeriod = {
        startDate: new Date(2024, 11, 1), // December 1, 2024
        endDate: new Date(2024, 11, 31), // December 31, 2024
      };

      const formattedDate = formatDateForMongoDB(currentPeriod.endDate);

      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formattedDate,
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: formattedDate,
        });
      }

      payroll.basicSalary = parseFloat(basicSalary);
      await payroll.save();

      req.flash("success", "Basic salary updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating basic salary:", error);
      return res
        .status(500)
        .json({ error: "An error occurred while updating the basic salary" });
    }
  }
);



// GET route for annual bonus form
router.get(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/onceOff/annual-bonus",
    "/:companyCode/employeeProfile/:employeeId/onceOff/annual-bonus",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Get current period or generate a new one
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: employee.company._id,
        isFinalized: false,
      }).sort({ endDate: 1 }); // Get earliest non-finalized period

      if (!currentPeriod) {
        // Generate a new period using PayrollService
        currentPeriod = await PayrollService.generateNextPeriod(
          employee,
          null // No previous period
        );
      }

      // Get existing annual bonus data if any
      const existingPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        "annualBonus.date": { $exists: true },
      }).sort({ "annualBonus.date": -1 });

      const existingData = {
        amount: existingPayroll?.annualBonus?.amount || 0,
      };

      res.render("onceOffAnnualBonus", {
        employee,
        companyCode,
        currentPeriod,
        existingData,
        moment,
        csrfToken: req.csrfToken(),
        messages: {
          success: req.flash("success"),
          error: req.flash("error"),
        },
      });
    } catch (error) {
      console.error("Error loading annual bonus form:", error);
      req.flash("error", "Error loading annual bonus form");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// GET route for once-off forms
router.get(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/onceOff/:formType",
    "/:companyCode/employeeProfile/:employeeId/onceOff/:formType",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, companyCode, formType } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Get current payroll period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: employee.company._id,
      }).sort({ endDate: -1 });

      if (!currentPeriod) {
        req.flash("error", "No active payroll period found");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // Map form types to their template names
      const formTypeToTemplate = {
        "annual-bonus": "onceOffAnnualBonus",
        "annual-payment": "onceOffAnnualPayment",
        "arbitration-award": "onceOffArbitrationAward",
        "dividends-subject": "onceOffDividendsSubject",
        "extra-pay": "onceOffExtraPay",
        "leave-paid-out": "onceOffLeavePaidOut",
        commission: "onceOffCommission",
        "restraint-of-trade": "onceOffRestraintOfTrade",
        "broad-based-share-plan": "onceOffBroadBasedSharePlan",
        "computer-allowance": "onceOffComputerAllowance",
        "expense-claim": "onceOffExpenseClaim",
        "gain-vesting": "onceOffGainVesting",
        "phone-allowance": "onceOffPhoneAllowance",
        "relocation-allowance": "onceOffRelocationAllowance",
        "subsistence-international": "onceOffSubsistenceInternational",
        "subsistence-local": "onceOffSubsistenceLocal",
        "tool-allowance": "onceOffToolAllowance",
        "uniform-allowance": "onceOffUniformAllowance",
        "bursaries-scholarships": "onceOffBursariesScholarships",
        "employee-debt-benefit": "onceOffEmployeeDebtBenefit",
        "medical-costs": "onceOffMedicalCosts",
        donations: "onceOffDonations",
        "repayment-advance": "onceOffRepaymentAdvance",
        "staff-purchases": "onceOffStaffPurchases",
        garnishee: "onceOffGarnishee",
        "maintenance-order": "onceOffMaintenanceOrder",
        "pension-fund": "onceOffPensionFund",
        "provident-fund": "onceOffProvidentFund",
        "retirement-annuity-fund": "onceOffRetirementAnnuityFund",
        "income-protection": "onceOffIncomeProtection",
        "union-fee": "onceOffUnionFee",
        savings: "onceOffSavings",
        "employer-loan": "onceOffEmployerLoan",
        "loss-of-income": "onceOffLossOfIncome",
        "covid-relief": "onceOffCovidRelief",
        "long-service-award": "onceOffLongServiceAward",
        "ters-payout": "onceOffTersPayout",
        "termination-lump-sums": "onceOffTerminationLumpSums",
        "voluntary-tax": "onceOffVoluntaryTax",
        "foreign-service": "onceOffForeignService",
        "tax-directive": "onceOffTaxDirective",
      };

      const templateName = formTypeToTemplate[formType];
      if (!templateName) {
        req.flash("error", "Invalid form type");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // Get existing data if any
      const existingPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        [`${formType}.date`]: { $exists: true },
      }).sort({ [`${formType}.date`]: -1 });

      const existingData = {
        amount: existingPayroll?.[formType]?.amount || 0,
        ...(existingPayroll?.[formType] || {}),
      };

      res.render(templateName, {
        employee,
        companyCode,
        currentPeriod,
        existingData,
        moment,
        formType,
        csrfToken: req.csrfToken(),
        messages: {
          success: req.flash("success"),
          error: req.flash("error"),
        },
      });
    } catch (error) {
      console.error(`Error loading ${req.params.formType} form:`, error);
      req.flash("error", `Error loading ${req.params.formType} form`);
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// GET route for once-off forms list
router.get(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/onceOff",
    "/:companyCode/employeeProfile/:employeeId/onceOff",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Define available once-off forms
      const onceOffForms = [
        { id: "annual-bonus", name: "Annual Bonus" },
        { id: "annual-payment", name: "Annual Payment" },
        { id: "arbitration-award", name: "Arbitration Award" },
        { id: "dividends-subject", name: "Dividends Subject" },
        { id: "extra-pay", name: "Extra Pay" },
        { id: "leave-paid-out", name: "Leave Paid Out" },
        { id: "commission", name: "Commission" },
        { id: "restraint-of-trade", name: "Restraint of Trade" },
        { id: "broad-based-share-plan", name: "Broad Based Share Plan" },
        { id: "computer-allowance", name: "Computer Allowance" },
        { id: "expense-claim", name: "Expense Claim" },
        { id: "gain-vesting", name: "Gain Vesting" },
        { id: "phone-allowance", name: "Phone Allowance" },
        { id: "relocation-allowance", name: "Relocation Allowance" },
        { id: "subsistence-international", name: "Subsistence International" },
        { id: "subsistence-local", name: "Subsistence Local" },
        { id: "tool-allowance", name: "Tool Allowance" },
        { id: "uniform-allowance", name: "Uniform Allowance" },
        { id: "bursaries-scholarships", name: "Bursaries & Scholarships" },
        { id: "employee-debt-benefit", name: "Employee Debt Benefit" },
        { id: "medical-costs", name: "Medical Costs" },
        { id: "donations", name: "Donations" },
        { id: "repayment-advance", name: "Repayment Advance" },
        { id: "staff-purchases", name: "Staff Purchases" },
        { id: "garnishee", name: "Garnishee" },
        { id: "maintenance-order", name: "Maintenance Order" },
        { id: "pension-fund", name: "Pension Fund" },
        { id: "provident-fund", name: "Provident Fund" },
        { id: "retirement-annuity-fund", name: "Retirement Annuity Fund" },
        { id: "income-protection", name: "Income Protection" },
        { id: "union-fee", name: "Union Fee" },
        { id: "savings", name: "Savings" },
        { id: "employer-loan", name: "Employer Loan" },
        { id: "loss-of-income", name: "Loss of Income" },
        { id: "covid-relief", name: "COVID Relief" },
        { id: "long-service-award", name: "Long Service Award" },
        { id: "ters-payout", name: "TERS Payout" },
        { id: "termination-lump-sums", name: "Termination Lump Sums" },
        { id: "voluntary-tax", name: "Voluntary Tax" },
        { id: "foreign-service", name: "Foreign Service" },
        { id: "tax-directive", name: "Tax Directive" },
      ];

      res.render("onceOffForms", {
        employee,
        companyCode,
        onceOffForms,
        csrfToken: req.csrfToken(),
        messages: {
          success: req.flash("success"),
          error: req.flash("error"),
        },
      });
    } catch (error) {
      console.error("Error loading once-off forms list:", error);
      req.flash("error", "Error loading once-off forms list");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// GET route for handling different form types
router.get(
  "/:companyCode/employeeProfile/:employeeId/:formType",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, formType } = req.params;
      const companyCode = req.params.companyCode;

      console.log("\n=== Travel Allowance Form Debug ===");
      console.log("Route params:", { companyCode, employeeId, formType });

      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Get current month's end date
      const thisMonth = getCurrentOrNextLastDayOfMonth();
      let relevantDate = req.query.relevantDate || thisMonth;

      console.log("Date information:", {
        thisMonth,
        relevantDate,
        queryDate: req.query.relevantDate,
        type: typeof relevantDate,
        formattedThisMonth: moment(thisMonth).format("YYYY-MM-DD HH:mm:ss"),
        formattedRelevantDate: moment(relevantDate).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
      });

      // Find the latest payroll for this employee
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        status: { $ne: "finalised" }, // Only get non-finalised payrolls
      })
        .sort({ month: -1 })
        .lean(); // Get the latest payroll

      console.log("Payroll record:", {
        found: !!payroll,
        id: payroll?._id,
        month: payroll?.month,
        travelAllowance: payroll?.travelAllowance,
      });

      // Map form types to their respective templates and component names
      const formTypeMapping = {
        // Income
        commission: { template: "editCommission", componentName: "Commission" },
        commissions: {
          template: "editCommission",
          componentName: "Commission",
        },
        "loss-of-income": {
          template: "editLossofIncome",
          componentName: "Loss of Income",
        },

        // Allowances
        "travel-allowance": {
          template: "editTravelAllowance",
          componentName: "Travel Allowance",
        },

        // Benefits
        "accommodation-benefit": {
          template: "editAccommodationBenefit",
          componentName: "Accommodation Benefit",
        },
        "bursaries-and-scholarships": {
          template: "editBursariesAndScholarships",
          componentName: "Bursaries and Scholarships",
        },
        "company-car": {
          template: "editCompanyCar",
          componentName: "Company Car",
        },
        "company-car-under-operating-lease": {
          template: "editCompanyCarUnderOperatingLease",
          componentName: "Company Car Operating Lease",
        },

        // Deductions
        garnishee: { template: "editGarnishee", componentName: "Garnishee" },
        "income-protection": {
          template: "editIncomeProtection",
          componentName: "Income Protection",
        },
        "maintenance-order": {
          template: "editMaintenanceOrder",
          componentName: "Maintenance Order",
        },
        "medical-aid": {
          template: "editMedicalAid",
          componentName: "Medical Aid",
        },
        "pension-fund": {
          template: "editPensionFund",
          componentName: "Pension Fund",
        },
        "provident-fund": {
          template: "editProvidentFund",
          componentName: "Provident Fund",
        },
        "retirement-annuity-fund": {
          template: "editRetirementAnnuityFund",
          componentName: "Retirement Annuity Fund",
        },
        "union-membership-fee": {
          template: "editUnionMembershipFee",
          componentName: "Union Membership Fee",
        },
        union_membership_fee: {
          template: "editUnionMembershipFee",
          componentName: "Union Membership Fee",
        },
        "voluntary-tax-over-deduction": {
          template: "editVoluntaryTaxOverDeduction",
          componentName: "Voluntary Tax Over Deduction",
        },

        // Other Deductions
        "employer-loan": {
          template: "editEmployerLoan",
          componentName: "Employer Loan",
        },
        "foreign-service-income": {
          template: "editForeignServiceIncome",
          componentName: "Foreign Service Income",
        },
        savings: { template: "editSavings", componentName: "Savings" },
        "tax-directive": {
          template: "editTaxDirective",
          componentName: "Tax Directive",
        },
        "travel-expenses": {
          template: "editTravelExpenses",
          componentName: "Travel Expenses",
        },
      };

      console.log("Form type validation:", {
        requestedType: formType,
        isValid: !!formTypeMapping[formType],
        mappingFound: formTypeMapping[formType],
      });

      const formTypeInfo = formTypeMapping[formType];
      if (!formTypeInfo) {
        console.error("Unknown form type:", formType);
        req.flash("error", "Invalid form type");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // Get current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        isFinalized: false,
      }).sort({ startDate: 1 });

      if (!currentPeriod) {
        console.error("No open period found for employee:", employeeId);
        req.flash("error", "No open period found");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      console.log("Current period:", {
        startDate: moment(currentPeriod.startDate).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        endDate: moment(currentPeriod.endDate).format("YYYY-MM-DD HH:mm:ss"),
      });

      // Find or create payroll for current period
      const formattedDate = formatDateForMongoDB(currentPeriod.endDate);

      if (!payroll) {
        payroll = await Payroll.findOne({
          employee: employeeId,
          company: employee.company._id,
          month: formattedDate,
        });

        if (!payroll) {
          payroll = new Payroll({
            employee: employeeId,
            company: employee.company._id,
            month: formattedDate,
          });
          await payroll.save();
        }
      }

      // Add form-specific data based on the form type
      let templateData = {
        employee,
        payroll: payroll || {},
        thisMonth,
        relevantDate,
        formattedDate: moment(relevantDate).format("YYYY-MM-DD"),
        csrfToken: req.csrfToken(),
        company: employee.company,
        companyCode,
        formType,
        componentName: formTypeInfo.componentName,
        moment: require("moment"),
        req: req,
        currentPath: req.path,
        currentPeriod,
      };

      console.log("Template data prepared:", {
        hasEmployee: !!templateData.employee,
        hasPayroll: !!templateData.payroll,
        hasCurrentPeriod: !!templateData.currentPeriod,
        periodDates: {
          start: moment(templateData.currentPeriod.startDate).format(
            "YYYY-MM-DD"
          ),
          end: moment(templateData.currentPeriod.endDate).format("YYYY-MM-DD"),
        },
      });

      // Add any form-specific data
      if (formType === "travel-allowance") {
        console.log("Travel allowance specific data:", {
          travelAllowanceData: payroll?.travelAllowance || {},
          hasExistingData: !!payroll?.travelAllowance,
        });
        templateData.travelAllowanceData = payroll?.travelAllowance || {};
      } else if (formType === "pension-fund") {
        console.log("Pension fund specific data:", {
          pensionFundData: payroll?.pensionFund || {},
          hasExistingData: !!payroll?.pensionFund,
        });
        
        try {
          // Get beneficiaries for pension fund
          const beneficiaries = await Beneficiary.find({
            company: employee.company._id,
            type: "pension-fund",
            status: "active"
          }).lean();
          
          templateData.beneficiaries = beneficiaries;
          templateData.effectiveRelevantDate = moment(relevantDate).format("YYYY-MM-DD");
        } catch (beneficiaryError) {
          console.error("Error fetching beneficiaries:", beneficiaryError);
          templateData.beneficiaries = [];
        }
      } else if (formType === "provident-fund") {
        console.log("Provident fund specific data:", {
          providentFundData: payroll?.providentFund || {},
          hasExistingData: !!payroll?.providentFund,
        });
        
        try {
          // Get beneficiaries for provident fund
          const beneficiaries = await Beneficiary.find({
            company: employee.company._id,
            type: "provident-fund",
            status: "active"
          }).lean();
          
          templateData.beneficiaries = beneficiaries;
          templateData.effectiveRelevantDate = moment(relevantDate).format("YYYY-MM-DD");
        } catch (beneficiaryError) {
          console.error("Error fetching beneficiaries:", beneficiaryError);
          templateData.beneficiaries = [];
        }
      }

      res.render(formTypeInfo.template, templateData);
    } catch (error) {
      console.error("Forms route error:", error);
      console.error("Error stack:", error.stack);
      req.flash("error", "Error loading form");
      const { companyCode, employeeId } = req.params;
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

// POST route for handling all payroll components
router.post(
  "/:companyCode/employeeProfile/:employeeId/regularInputs/:componentType",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Regular Inputs POST Handler Debug ===");
    console.log("Route Params:", req.params);
    console.log("Request Body:", req.body);

    // Detailed request debugging
    console.log("\n=== Detailed Request Debug ===");
    console.log("Headers:", {
      all: req.headers,
      accept: req.headers.accept,
      "content-type": req.headers["content-type"],
      "x-requested-with": req.headers["x-requested-with"],
    });
    console.log("Request Properties:", {
      xhr: req.xhr,
      isJson: req.is("json"),
      acceptsJson: req.accepts("json"),
      path: req.path,
      method: req.method,
    });

    const { employeeId, companyCode, componentType } = req.params;
    const { amount, periodEndDate, relevantDate, ...otherData } = req.body;
    const effectiveRelevantDate = periodEndDate
      ? new Date(periodEndDate)
      : relevantDate
      ? new Date(relevantDate)
      : getCurrentOrNextLastDayOfMonth();

    try {
      let result;
      console.log("\n=== Processing Component Type ===");
      console.log("Component Type:", componentType);
      console.log("Amount:", amount);
      console.log("Effective Date:", effectiveRelevantDate);

      switch (componentType) {
        case "union-membership-fee":
        case "union_membership_fee": {
          console.log("\n=== Processing Union Membership Fee ===");
          console.log("Input data:", {
            employeeId,
            amount,
            effectiveRelevantDate,
            otherData,
          });

          // Find or create payroll record for this period
          let unionFeePayroll = await Payroll.findOne({
            employee: employeeId,
            month: {
              $gte: moment(effectiveRelevantDate).startOf("month").toDate(),
              $lte: moment(effectiveRelevantDate).endOf("month").toDate(),
            },
          });

          console.log("Found existing payroll:", !!unionFeePayroll);

          if (!unionFeePayroll) {
            unionFeePayroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: effectiveRelevantDate, // Use the exact date from the form
            });
          }

          console.log("Updating union membership fee amount:", amount);
          unionFeePayroll.unionMembershipFee = parseFloat(amount);

          console.log("Saving payroll record...");
          await unionFeePayroll.save();
          console.log("Payroll record saved successfully");

          result = {
            success: true,
            message: "Union membership fee updated successfully",
            data: {
              amount: unionFeePayroll.unionMembershipFee,
              month: unionFeePayroll.month,
            },
          };
          break;
        }
        case "loss-of-income": {
          console.log("\n=== Processing Loss of Income ===");
          console.log("Input data:", {
            employeeId,
            amount: req.body.amount,
            relevantDate: req.body.relevantDate,
          });

          const amount = parseFloat(req.body.amount);
          const relevantDate = new Date(req.body.relevantDate);

          console.log("Parsed data:", {
            amount,
            relevantDate,
            isValidAmount: !isNaN(amount),
            isValidDate: !isNaN(relevantDate.getTime()),
          });

          // Validate input
          if (isNaN(amount) || amount <= 0) {
            throw new Error("Invalid amount for loss of income");
          }

          if (isNaN(relevantDate.getTime())) {
            throw new Error("Invalid date for loss of income");
          }

          // First find the payroll record
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(relevantDate).startOf("month").toDate(),
              $lte: moment(relevantDate).endOf("month").toDate(),
            },
          });

          if (!payroll) {
            // Create new payroll if it doesn't exist
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(relevantDate).endOf("month").toDate(),
              lossOfIncome: amount,
              lossOfIncomeEnabled: true,
            });
          } else {
            // Update existing payroll
            payroll.lossOfIncome = amount;
            payroll.lossOfIncomeEnabled = true;
            
            // Store the date in the data Map
            if (!payroll.data) {
              payroll.data = new Map();
            }
            payroll.data.set('lossOfIncomeDate', relevantDate);
          }

          // Save the payroll record
          await payroll.save();

          console.log("Updated payroll record:", {
            id: payroll._id,
            amount,
            date: relevantDate,
            lossOfIncome: payroll.lossOfIncome,
          });

          // Set success flash message
          req.flash("success", "Loss of income updated successfully");

          // Redirect to employee profile
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "travel-allowance": {
          console.log("\n=== Processing Travel Allowance ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          // Update travel allowance details
          payroll.travelAllowance = {
            fixedAllowance: req.body.fixedAllowance === "on",
            fixedAllowanceAmount: req.body.fixedAllowanceAmount
              ? parseFloat(req.body.fixedAllowanceAmount)
              : 0,
            reimbursedExpenses: req.body.reimbursedExpenses === "on",
            companyPetrolCard: req.body.companyPetrolCard === "on",
            reimbursedPerKmTravelled:
              req.body.reimbursedPerKmTravelled === "on",
            ratePerKm: req.body.ratePerKm ? parseFloat(req.body.ratePerKm) : 0,
            only20PercentTax: req.body.only20PercentTax === "on",
          };

          await payroll.save();
          console.log("Travel allowance updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            travelAllowance: payroll.travelAllowance,
          });

          // Set success message and redirect
          req.flash("success", "Travel allowance updated successfully");
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "accommodation-benefit": {
          console.log("\n=== Processing Accommodation Benefit ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          // Update accommodation benefit amount
          payroll.accommodationBenefit = parseFloat(req.body.amount) || 0;

          await payroll.save();
          console.log("Accommodation benefit updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            accommodationBenefit: payroll.accommodationBenefit,
          });

          // Set success message and redirect
          req.flash("success", "Accommodation benefit updated successfully");
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "commission": {
          console.log("\n=== Processing Commission ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const action = req.body.action;
          if (action === "remove") {
            // Disable commission
            payroll.commissionEnabled = false;
            payroll.commission = 0;
          } else {
            // Enable commission
            payroll.commissionEnabled = req.body.commissionEnabled === "true";
          }

          await payroll.save();
          console.log("Commission settings updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            commissionEnabled: payroll.commissionEnabled,
            commission: payroll.commission,
          });

          // Set success message and redirect
          const message =
            action === "remove"
              ? "Commission removed successfully"
              : "Commission enabled successfully";
          req.flash("success", message);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "bursaries-and-scholarships": {
          console.log("\n=== Processing Bursaries and Scholarships ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          // Update bursaries and scholarships details
          payroll.bursariesAndScholarships = {
            taxablePortion: parseFloat(req.body.taxablePortion) || 0,
            exemptPortion: parseFloat(req.body.exemptPortion) || 0,
            employeeHandlesPayment: req.body.employeeHandlesPayment === "on",
            toDisabledPerson: req.body.toDisabledPerson === "on",
            enabled: true,
            type: req.body.type,
          };

          await payroll.save();
          console.log("Bursaries and scholarships updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            bursariesAndScholarships: payroll.bursariesAndScholarships,
          });

          // Set success message and redirect
          req.flash(
            "success",
            "Bursaries and scholarships updated successfully"
          );
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "company-car": {
          console.log("\n=== Processing Company Car ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const action = req.body.action;
          if (action === "remove") {
            // Reset company car details
            payroll.companyCar = {
              deemedValue: 0,
              includesMaintenancePlan: false,
              taxablePercentage: "0%",
            };
          } else {
            // Update company car details
            payroll.companyCar = {
              deemedValue: parseFloat(req.body.deemedValue) || 0,
              includesMaintenancePlan:
                req.body.includesMaintenancePlan === "on",
              taxablePercentage: req.body.taxablePercentage || "0%",
            };
          }

          await payroll.save();
          console.log("Company car updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            companyCar: payroll.companyCar,
            action: action,
          });

          // Set success message and redirect
          const message =
            action === "remove"
              ? "Company car removed successfully"
              : "Company car updated successfully";
          req.flash("success", message);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "company-car-lease": {
          console.log("\n=== Processing Company Car Operating Lease ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const action = req.body.action;
          if (action === "remove") {
            // Reset company car under operating lease details
            payroll.companyCarUnderOperatingLease = {
              amount: 0,
              taxablePercentage: "0%",
            };
          } else {
            // Update company car under operating lease details
            payroll.companyCarUnderOperatingLease = {
              amount: parseFloat(req.body.amount) || 0,
              taxablePercentage: req.body.taxablePercentage || "0%",
            };
          }

          await payroll.save();
          console.log(
            "Company car under operating lease updated successfully:",
            {
              payrollId: payroll._id,
              month: moment(payroll.month).format("YYYY-MM-DD"),
              companyCarUnderOperatingLease:
                payroll.companyCarUnderOperatingLease,
              action: action,
            }
          );

          // Set success message and redirect
          const message =
            action === "remove"
              ? "Company car under operating lease removed successfully"
              : "Company car under operating lease updated successfully";
          req.flash("success", message);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "garnishee": {
          console.log("\n=== Processing Garnishee ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const action = req.body.action;
          if (action === "remove") {
            // Reset garnishee to 0
            payroll.garnishee = 0;
          } else {
            // Update garnishee amount
            payroll.garnishee = parseFloat(req.body.amount) || 0;
          }

          await payroll.save();
          console.log("Garnishee updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            garnishee: payroll.garnishee,
            action: action,
          });

          // Set success message and redirect
          const message =
            action === "remove"
              ? "Garnishee removed successfully"
              : "Garnishee updated successfully";
          req.flash("success", message);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
        case "maintenance-order": {
          console.log("\n=== Processing Maintenance Order ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod ? moment(currentPeriod.startDate).format("YYYY-MM-DD") : null,
            endDate: currentPeriod ? moment(currentPeriod.endDate).format("YYYY-MM-DD") : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const action = req.body.action;
          if (action === "remove") {
            // Reset maintenance order to 0
            payroll.maintenanceOrder = 0;
          } else {
            // Update maintenance order with just the amount
            payroll.maintenanceOrder = parseFloat(req.body.amount) || 0;
          }

          await payroll.save();
          console.log("Maintenance order updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            maintenanceOrder: payroll.maintenanceOrder,
            action: action,
          });

          // Set success message and redirect
          const message = action === "remove" ? "Maintenance order removed successfully" : "Maintenance order updated successfully";
          req.flash("success", message);
          return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
        }
        case "medical-aid": {
          console.log("\n=== Processing Medical Aid ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Parse and validate form data
          const {
            medicalAid,
            employerContribution,
            members,
            employeeHandlesPayment,
            dontApplyTaxCredits,
          } = req.body;

          // Validate inputs
          if (!medicalAid || isNaN(medicalAid) || parseFloat(medicalAid) < 0) {
            throw new Error("Invalid medical aid amount");
          }

          if (
            employerContribution &&
            (isNaN(employerContribution) ||
              parseFloat(employerContribution) < 0)
          ) {
            throw new Error("Invalid employer contribution amount");
          }

          if (!members || isNaN(members) || parseInt(members) < 1) {
            throw new Error("Invalid number of members");
          }

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod
              ? moment(currentPeriod.startDate).format("YYYY-MM-DD")
              : null,
            endDate: currentPeriod
              ? moment(currentPeriod.endDate).format("YYYY-MM-DD")
              : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          console.log("Existing payroll record found:", {
            found: !!payroll,
            payrollId: payroll?._id,
            month: payroll ? moment(payroll.month).format("YYYY-MM-DD") : null,
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          // Update medical aid details
          payroll.medical = {
            medicalAid: parseFloat(medicalAid),
            employerContribution: employerContribution
              ? parseFloat(employerContribution)
              : 0,
            members: parseInt(members),
            employeeHandlesPayment: employeeHandlesPayment === "on",
            dontApplyTaxCredits: dontApplyTaxCredits === "on",
          };

          // Calculate employee contribution
          const employeeContribution =
            parseFloat(medicalAid) -
            (payroll.medical.employerContribution || 0);

          // Update the employee contribution
          payroll.medical.employeeContribution = employeeContribution;

          // Save the changes
          result = await payroll.save();

          console.log("Medical aid details updated:", payroll.medical);
          break;
        }
        case "retirement-annuity-fund": {
          console.log("\n=== Processing Retirement Annuity Fund ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          let mongoSession;
          try {
            console.log("Starting mongoose session");
            mongoSession = await mongoose.startSession();
            mongoSession.startTransaction();
            console.log("Transaction started successfully");

            // Extract values from request body
            const {
              employeeContribution,
              employerContribution,
              relevantDate,
              employeeHandlesPayment,
              beneficiary,
            } = req.body;

            // Input validation
            console.log("Validating input parameters:", {
              employeeContribution,
              employerContribution,
              relevantDate,
              employeeHandlesPayment,
              beneficiary,
            });

            if (
              !employeeContribution ||
              isNaN(employeeContribution) ||
              parseFloat(employeeContribution) < 0
            ) {
              throw new Error("Invalid employee contribution amount");
            }

            if (
              employerContribution &&
              (isNaN(employerContribution) ||
                parseFloat(employerContribution) < 0)
            ) {
              throw new Error("Invalid employer contribution amount");
            }

            if (!relevantDate) {
              throw new Error("Relevant date is required");
            }

            // Find employee and company with session
            const [employee, company] = await Promise.all([
              Employee.findById(employeeId).session(mongoSession),
              Company.findById(req.user.currentCompany).session(mongoSession),
            ]);

            console.log("Database lookup results:", {
              employeeFound: !!employee,
              companyFound: !!company,
              employeeId: employee?._id,
              companyId: company?._id,
            });

            if (!employee) throw new Error("Employee not found");
            if (!company) throw new Error("Company not found");

            // Parse and validate date
            const parsedDate = moment(relevantDate).toDate();
            console.log("Parsed relevant date:", parsedDate);

            // Find or create payroll period
            console.log("Looking up payroll period for date:", parsedDate);
            let payrollPeriod = await PayrollPeriod.findOne({
              employee: employeeId,
              startDate: { $lte: parsedDate },
              endDate: { $gte: parsedDate },
            }).session(mongoSession);

            console.log("Payroll period lookup result:", {
              found: !!payrollPeriod,
              periodId: payrollPeriod?._id,
              startDate: payrollPeriod?.startDate,
              endDate: payrollPeriod?.endDate,
            });

            if (!payrollPeriod) {
              console.log("No payroll period found, generating next period");
              // Get the latest period for this employee
              const latestPeriod = await PayrollPeriod.findOne({
                employee: employeeId,
              })
                .sort({ endDate: -1 })
                .session(mongoSession);

              // Generate next period using PayrollService
              payrollPeriod = await PayrollService.generateNextPeriod(
                employee,
                latestPeriod || null,
                mongoSession
              );

              if (!payrollPeriod) {
                throw new Error("Failed to generate next payroll period");
              }

              console.log("Generated new payroll period:", {
                periodId: payrollPeriod._id,
                startDate: payrollPeriod.startDate,
                endDate: payrollPeriod.endDate,
              });
            }

            // Find or update payroll record
            console.log("Looking up payroll record");
            let payroll = await Payroll.findOneAndUpdate(
              {
                employee: employeeId,
                company: company._id,
                month: parsedDate
              },
              {
                $set: {
                  retirementAnnuityFund: {
                    employeeContribution: parseFloat(employeeContribution),
                    employerContribution: employerContribution
                      ? parseFloat(employerContribution)
                      : 0,
                    employeeHandlesPayment: employeeHandlesPayment === "on",
                    beneficiary: beneficiary || "",
                    lastUpdated: new Date()
                  },
                },
              },
              {
                new: true,
                upsert: true,
                session: mongoSession,
              }
            );

            console.log("Payroll record upsert result:", {
              payrollId: payroll?._id,
              retirementAnnuityFund: payroll?.retirementAnnuityFund,
            });

            // Commit the transaction
            console.log("Committing transaction");
            await mongoSession.commitTransaction();
            console.log("Transaction committed successfully");

            console.log(
              "Retirement annuity fund details updated:",
              payroll.retirementAnnuityFund
            );

            // Set success message and redirect
            req.flash(
              "success",
              "Retirement annuity fund updated successfully"
            );
            return res.redirect(
              `/clients/${companyCode}/employeeProfile/${employeeId}`
            );
          } catch (error) {
            console.error("Error in retirement annuity fund processing:", {
              name: error.name,
              message: error.message,
              stack: error.stack,
            });

            // Rollback transaction on error
            if (mongoSession) {
              console.log("Rolling back transaction");
              await mongoSession.abortTransaction();
              console.log("Transaction rolled back");
            }

            throw error; // Re-throw to be caught by outer catch block
          } finally {
            if (mongoSession) {
              console.log("Ending mongoose session");
              mongoSession.endSession();
              console.log("Mongoose session ended");
            }
          }
        }
        case "voluntaryTaxOverDeduction": {
          console.log("Processing voluntary tax over deduction:", {
            amount: parseFloat(amount),
            relevantDate
          });

          // Start MongoDB session
          const session = await mongoose.startSession();
          session.startTransaction();

          try {
            // Parse the date
            const parsedDate = new Date(relevantDate);
            if (isNaN(parsedDate.getTime())) {
              throw new Error("Invalid date format");
            }
            
            // Set to end of day (23:59:59.999)
            parsedDate.setHours(23, 59, 59, 999);

            // Get company first
            const company = await Company.findOne({ companyCode }).session(session);
            if (!company) {
              throw new Error("Company not found");
            }

            // Find or create payroll record
            const payroll = await Payroll.findOneAndUpdate(
              {
                employee: employeeId,
                company: company._id,
                month: parsedDate
              },
              {
                $set: {
                  voluntaryTaxOverDeduction: parseFloat(amount),
                  "data.voluntaryTaxOverDeductionDate": parsedDate
                }
              },
              {
                new: true,
                upsert: true,
                session,
                runValidators: true
              }
            );

            if (!payroll) {
              throw new Error("Failed to update payroll record");
            }

            await session.commitTransaction();
            
            req.flash("success", "Voluntary tax over deduction updated successfully");
            return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
          } catch (error) {
            await session.abortTransaction();
            throw error;
          } finally {
            session.endSession();
          }
        }
        case "employer-loan": {
          console.log("\n=== Processing Employer Loan ===");
          console.log("Input data:", {
            employeeId,
            interestRate: req.body.interestRate,
            regularRepayment: req.body.regularRepayment,
            calculateInterestBenefit: req.body.calculateInterestBenefit,
            relevantDate: req.body.relevantDate
          });

          const interestRate = parseFloat(req.body.interestRate);
          const regularRepayment = parseFloat(req.body.regularRepayment);
          const calculateInterestBenefit = req.body.calculateInterestBenefit === 'on';
          const relevantDate = new Date(req.body.relevantDate);

          // Input validation
          if (isNaN(interestRate) || interestRate < 0) {
            throw new Error("Invalid interest rate");
          }

          if (isNaN(regularRepayment) || regularRepayment < 0) {
            throw new Error("Invalid regular repayment amount");
          }

          if (isNaN(relevantDate.getTime())) {
            throw new Error("Invalid date format");
          }

          // Find employee and company
          const [employee, company] = await Promise.all([
            Employee.findById(employeeId),
            Company.findOne({ companyCode })
          ]);

          if (!employee) {
            throw new Error("Employee not found");
          }

          if (!company) {
            throw new Error("Company not found");
          }

          // Find or create payroll record
          let payroll = await Payroll.findOne({
            employee: employeeId,
            month: {
              $gte: moment(relevantDate).startOf("month").toDate(),
              $lte: moment(relevantDate).endOf("month").toDate(),
            },
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: company._id,
              month: relevantDate
            });
          }

          // Update employer loan data
          payroll.employerLoan = {
            interestRate,
            regularRepayment,
            calculateInterestBenefit,
            lastUpdated: new Date()
          };

          await payroll.save();

          result = {
            success: true,
            message: "Employer loan updated successfully",
            data: {
              employerLoan: payroll.employerLoan,
              month: payroll.month
            }
          };
          break;
        }
        case "foreign-service-income": {
          console.log("\n=== Processing Foreign Service Income ===");
          console.log("Input data:", {
            employeeId,
            foreignServiceTaxExemption: req.body.foreignServiceTaxExemption,
            relevantDate: req.body.relevantDate
          });

          const foreignServiceTaxExemption = req.body.foreignServiceTaxExemption === 'on';
          const relevantDate = new Date(req.body.relevantDate);

          if (isNaN(relevantDate.getTime())) {
            throw new Error("Invalid date format");
          }

          // Find employee and company
          const [employee, company] = await Promise.all([
            Employee.findById(employeeId),
            Company.findOne({ companyCode })
          ]);

          if (!employee) {
            throw new Error("Employee not found");
          }

          if (!company) {
            throw new Error("Company not found");
          }

          // Find or create payroll record
          let payroll = await Payroll.findOne({
            employee: employeeId,
            month: {
              $gte: moment(relevantDate).startOf("month").toDate(),
              $lte: moment(relevantDate).endOf("month").toDate(),
            },
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: company._id,
              month: relevantDate
            });
          }

          // Update foreign service income data
          payroll.foreignServiceIncome = {
            foreignServiceTaxExemption,
            lastUpdated: new Date()
          };

          await payroll.save();

          result = {
            success: true,
            message: "Foreign service income updated successfully",
            data: {
              foreignServiceIncome: payroll.foreignServiceIncome,
              month: payroll.month
            }
          };
          break;
        }
        case "savings": {
          console.log("\n=== Processing Savings ===");
          console.log("Input data:", {
            employeeId,
            regularDeduction: otherData.regularDeduction,
            relevantDate
          });

          // Find the employee and company
          const [employee, company] = await Promise.all([
            Employee.findById(employeeId),
            Company.findById(req.user.currentCompany)
          ]);

          if (!employee || !company) {
            throw new Error("Employee or company not found");
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            month: {
              $gte: moment(effectiveRelevantDate).startOf("month").toDate(),
              $lte: moment(effectiveRelevantDate).endOf("month").toDate()
            }
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: company._id,
              month: effectiveRelevantDate
            });
          }

          // Update savings data
          payroll.savings = {
            regularDeduction: parseFloat(otherData.regularDeduction) || 0,
            lastUpdated: new Date()
          };

          await payroll.save();

          result = {
            success: true,
            message: "Savings updated successfully",
            data: {
              savings: payroll.savings,
              month: payroll.month
            }
          };
          break;
        }
        case "tax-directive": {
          console.log("\n=== Processing Tax Directive ===");
          console.log("Input data:", {
            employeeId,
            directiveNumber: otherData.directiveNumber,
            directiveType: otherData.directiveType,
            percentage: otherData.percentage,
            directiveIssueDate: otherData.directiveIssueDate,
            directiveIncomeSourceCode: otherData.directiveIncomeSourceCode,
            directiveIncomeAmount: otherData.directiveIncomeAmount,
            amountToDeduct: otherData.amountToDeduct,
            relevantDate
          });

          // Find the employee and company
          const [employee, company] = await Promise.all([
            Employee.findById(employeeId),
            Company.findById(req.user.currentCompany)
          ]);

          if (!employee || !company) {
            throw new Error("Employee or company not found");
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            month: {
              $gte: moment(effectiveRelevantDate).startOf("month").toDate(),
              $lte: moment(effectiveRelevantDate).endOf("month").toDate()
            }
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: company._id,
              month: effectiveRelevantDate
            });
          }

          // Map form directive type values to schema enum values
          let directiveType;
          if (otherData.directiveType === 'fixed-percentage') {
            directiveType = 'Fixed Percentage - IRP3(b) and IRP3(pa)';
          } else if (otherData.directiveType === 'fixed-amount') {
            directiveType = 'Fixed Amount - IRP3(c)';
          } else {
            directiveType = otherData.directiveType;
          }

          // Update tax directive data
          payroll.taxDirective = {
            directiveNumber: otherData.directiveNumber,
            directiveType: directiveType,
            percentage: parseFloat(otherData.percentage) || 0,
            directiveIssueDate: otherData.directiveIssueDate ? new Date(otherData.directiveIssueDate) : null,
            directiveIncomeSourceCode: otherData.directiveIncomeSourceCode,
            directiveIncomeAmount: parseFloat(otherData.directiveIncomeAmount) || 0,
            amountToDeduct: parseFloat(otherData.amountToDeduct) || 0,
            lastUpdated: new Date()
          };

          await payroll.save();

          result = {
            success: true,
            message: "Tax directive updated successfully",
            data: {
              taxDirective: payroll.taxDirective,
              month: payroll.month
            }
          };
          break;
        }
        case "pension-fund": {
          console.log("\n=== Processing Pension Fund ===");
          console.log("Input data:", {
            employeeId,
            ...req.body,
          });

          // Get current period
          const currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            isFinalized: false,
          }).sort({ startDate: 1 });

          console.log("Current period found:", {
            periodId: currentPeriod?._id,
            startDate: currentPeriod ? moment(currentPeriod.startDate).format("YYYY-MM-DD") : null,
            endDate: currentPeriod ? moment(currentPeriod.endDate).format("YYYY-MM-DD") : null,
            isFinalized: currentPeriod?.isFinalized,
          });

          if (!currentPeriod) {
            console.error("No open period found for employee:", employeeId);
            req.flash("error", "No open period found");
            return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
          }

          // Find or create payroll record for this period
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(currentPeriod.startDate).startOf("month").toDate(),
              $lte: moment(currentPeriod.endDate).endOf("month").toDate(),
            },
          });

          if (!payroll) {
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(currentPeriod.endDate).endOf("month").toDate(),
            });
          }

          const {
            contributionCalculation,
            fixedContributionEmployee,
            fixedContributionEmployer,
            rfiEmployee,
            rfiEmployer,
            beneficiary,
            categoryFactor
          } = req.body;

          // Update pension fund details based on contribution calculation method
          payroll.pensionFund = {
            contributionCalculation,
            fixedContributionEmployee: contributionCalculation === 'fixedAmount' ? parseFloat(fixedContributionEmployee) || 0 : 0,
            fixedContributionEmployer: contributionCalculation === 'fixedAmount' ? parseFloat(fixedContributionEmployer) || 0 : 0,
            rfiEmployee: contributionCalculation === 'percentageRFI' ? parseFloat(rfiEmployee) || 0 : 0,
            rfiEmployer: contributionCalculation === 'percentageRFI' ? parseFloat(rfiEmployer) || 0 : 0,
            beneficiary: beneficiary || '',
            categoryFactor: parseFloat(categoryFactor) || 1,
            lastUpdated: new Date()
          };

          await payroll.save();
          console.log("Pension fund updated successfully:", {
            payrollId: payroll._id,
            month: moment(payroll.month).format("YYYY-MM-DD"),
            pensionFund: payroll.pensionFund,
          });

          // Set success message and redirect
          req.flash("success", "Pension fund updated successfully");
          return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
        }
        case "union-membership-fee":
        case "union_membership_fee": {
          console.log("\n=== Processing Union Membership Fee ===");
          console.log("Input data:", {
            employeeId,
            amount,
            effectiveRelevantDate,
            otherData,
          });

          try {
            // Find or create payroll record for this period
            let unionFeePayroll = await Payroll.findOne({
              employee: employeeId,
              month: {
                $gte: moment(effectiveRelevantDate).startOf("month").toDate(),
                $lte: moment(effectiveRelevantDate).endOf("month").toDate(),
              },
            }).session(session);

            console.log("Found existing payroll:", !!unionFeePayroll);

            if (!unionFeePayroll) {
              unionFeePayroll = new Payroll({
                employee: employeeId,
                company: req.user.currentCompany,
                month: effectiveRelevantDate, // Use the exact date from the form
              });
              console.log("Created new payroll record");
            }

            // Update the union membership fee
            unionFeePayroll.unionMembershipFee = parseFloat(amount);
            console.log(
              "Updated union membership fee amount:",
              unionFeePayroll.unionMembershipFee
            );

            // Save the payroll record
            await unionFeePayroll.save();
            console.log("Saved payroll record successfully");

            result = {
              success: true,
              message: "Union membership fee updated successfully",
            };
          } catch (error) {
            // Let the main error handler catch it
            throw error;
          }
          break;
        }
        case "loss-of-income": {
          console.log("\n=== Processing Loss of Income ===");
          console.log("Input data:", {
            employeeId,
            amount: req.body.amount,
            relevantDate: req.body.relevantDate,
          });

          const amount = parseFloat(req.body.amount);
          const relevantDate = new Date(req.body.relevantDate);

          console.log("Parsed data:", {
            amount,
            relevantDate,
            isValidAmount: !isNaN(amount),
            isValidDate: !isNaN(relevantDate.getTime()),
          });

          // Validate input
          if (isNaN(amount) || amount <= 0) {
            throw new Error("Invalid amount for loss of income");
          }

          if (isNaN(relevantDate.getTime())) {
            throw new Error("Invalid date for loss of income");
          }

          // First find the payroll record
          let payroll = await Payroll.findOne({
            employee: employeeId,
            company: req.user.currentCompany,
            month: {
              $gte: moment(relevantDate).startOf("month").toDate(),
              $lte: moment(relevantDate).endOf("month").toDate(),
            },
          });

          if (!payroll) {
            // Create new payroll if it doesn't exist
            payroll = new Payroll({
              employee: employeeId,
              company: req.user.currentCompany,
              month: moment(relevantDate).endOf("month").toDate(),
              lossOfIncome: amount,
              lossOfIncomeEnabled: true,
            });
          } else {
            // Update existing payroll
            payroll.lossOfIncome = amount;
            payroll.lossOfIncomeEnabled = true;
            
            // Store the date in the data Map
            if (!payroll.data) {
              payroll.data = new Map();
            }
            payroll.data.set('lossOfIncomeDate', relevantDate);
          }

          // Save the payroll record
          await payroll.save();

          console.log("Updated payroll record:", {
            id: payroll._id,
            amount,
            date: relevantDate,
            lossOfIncome: payroll.lossOfIncome,
          });

          // Set success flash message
          req.flash("success", "Loss of income updated successfully");

          // Redirect to employee profile
          return res.redirect(`/${companyCode}/employeeProfile/${employeeId}`);
        }
        default:
          console.log("Unhandled form type:", formType);
      }

      // Update total earnings
      payroll.totalEarnings = (payroll.totalEarnings || 0) + parseFloat(amount);

      console.log("Updating payroll:", {
        formType,
        amount: parseFloat(amount),
        payrollId: payroll._id,
        annualPayment: payroll.annualPayment?.amount,
        annualPaymentEnabled: payroll.annualPaymentEnabled,
        annualBonus: payroll.annualBonus,
      });

      // Save payroll record
      await payroll.save({ session });

      // Create once-off payment record
      const onceOffPayment = new OnceOffPayment({
        employee: employeeId,
        company: company._id,
        type: formType,
        amount: parseFloat(amount),
        date: parsedDate,
        period: payrollPeriod._id,
      });

      await onceOffPayment.save({ session });
      await session.commitTransaction();

      console.log("Successfully saved:", {
        payrollId: payroll._id,
        onceOffPaymentId: onceOffPayment._id,
        amount,
        type: formType,
      });

      res.json({
        success: true,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      if (session.inTransaction()) {
        await session.abortTransaction();
      }
      session.endSession();
      console.error("Error processing form:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// POST route for handling medical aid form submissions
router.post(
  "/:companyCode/employeeProfile/:employeeId/onceOff/medical-aid",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { amount, relevantDate, ...otherData } = req.body;

      console.log("\n=== Medical Aid Processing ===");
      console.log("Input data:", {
        employeeId,
        amount,
        relevantDate,
        otherData,
      });

      // First find the employee
      const employee = await Employee.findById(employeeId).session(session);
      console.log("Employee lookup result:", {
        found: !!employee,
        id: employee?._id,
        companyRef: employee?.company,
      });

      if (!employee) {
        throw new Error(`Employee not found with ID: ${employeeId}`);
      }

      // Then find the company using both methods
      let company = await Company.findOne({ code: companyCode }).session(
        session
      );
      if (!company && employee.company) {
        company = await Company.findById(employee.company).session(session);
      }
      console.log("Company lookup result:", {
        found: !!company,
        id: company?._id,
        code: company?.code,
        matchedByCode: !!(await Company.findOne({ code: companyCode })),
        matchedById: !!(await Company.findById(employee.company)),
      });

      if (!company) {
        throw new Error(
          `Company not found with code: ${companyCode} or id: ${employee.company}`
        );
      }

      // Parse the relevant date
      const parsedDate = moment(relevantDate).toDate();
      const periodStartDate = moment(parsedDate).startOf("month").toDate();
      const periodEndDate = moment(parsedDate).endOf("month").toDate();

      // Find or create payroll period
      let payrollPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        startDate: { $lte: parsedDate },
        endDate: { $gte: parsedDate },
      }).session(session);

      // Create period if it doesn't exist
      if (!payrollPeriod) {
        payrollPeriod = new PayrollPeriod({
          employee: employeeId,
          company: company._id,
          startDate: periodStartDate,
          endDate: periodEndDate,
          frequency: "monthly",
          status: "open",
        });
        await payrollPeriod.save({ session });
      }

      // Find or create payroll record
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: {
          $gte: periodStartDate,
          $lte: periodEndDate,
        },
      }).session(session);

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: periodEndDate,
        });
      }

      // Parse and validate form data
      const {
        medicalAid,
        employerContribution,
        members,
        employeeHandlesPayment,
        dontApplyTaxCredits,
      } = req.body;

      // Validate inputs
      if (!medicalAid || isNaN(medicalAid) || parseFloat(medicalAid) < 0) {
        throw new Error("Invalid medical aid amount");
      }

      if (
        employerContribution &&
        (isNaN(employerContribution) || parseFloat(employerContribution) < 0)
      ) {
        throw new Error("Invalid employer contribution amount");
      }

      if (!members || isNaN(members) || parseInt(members) < 1) {
        throw new Error("Invalid number of members");
      }

      // Update medical aid details
      payroll.medical = {
        medicalAid: parseFloat(medicalAid),
        employerContribution: employerContribution
          ? parseFloat(employerContribution)
          : 0,
        members: parseInt(members),
        employeeHandlesPayment: employeeHandlesPayment === "on",
        dontApplyTaxCredits: dontApplyTaxCredits === "on",
      };

      // Calculate employee contribution
      const employeeContribution =
        parseFloat(medicalAid) - (payroll.medical.employerContribution || 0);

      // Update the employee contribution
      payroll.medical.employeeContribution = employeeContribution;

      // Save payroll record with the session
      await payroll.save({ session });

      // Create once-off payment record
      const medicalAidPayment = new OnceOffPayment({
        employee: employeeId,
        company: company._id,
        type: "medical-aid",
        amount: parseFloat(medicalAid),
        date: parsedDate,
        period: payrollPeriod._id,
        payroll: payroll._id,
      });

      await medicalAidPayment.save({ session });
      await session.commitTransaction();

      console.log("Successfully saved medical aid:", {
        payrollId: payroll._id,
        onceOffPaymentId: medicalAidPayment._id,
        amount,
        type: "medical-aid",
      });

      res.json({
        success: true,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      if (session.inTransaction()) {
        await session.abortTransaction();
      }
      session.endSession();
      console.error("Error processing medical aid:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Add a handler for retirement-annuity-fund in the regularInputs route
router.post(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/regularInputs/retirement-annuity-fund",
    "/:companyCode/employeeProfile/:employeeId/regularInputs/retirement-annuity-fund",
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    let session;
    try {
      const { companyCode, employeeId } = req.params;
      const {
        employeeContribution,
        employerContribution,
        employeeHandlesPayment,
        beneficiary,
        relevantDate,
      } = req.body;

      console.log("\n=== Processing Retirement Annuity Fund ===", {
        params: { companyCode, employeeId },
        body: req.body,
      });

      // Input validation
      if (!employeeContribution || isNaN(employeeContribution) || parseFloat(employeeContribution) < 0) {
        throw new Error("Invalid employee contribution amount");
      }

      if (employerContribution && (isNaN(employerContribution) || parseFloat(employerContribution) < 0)) {
        throw new Error("Invalid employer contribution amount");
      }

      if (!relevantDate) {
        throw new Error("Relevant date is required");
      }

      // Parse date
      const parsedDate = new Date(relevantDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error("Invalid date format");
      }

      // Start MongoDB session
      session = await mongoose.startSession();
      session.startTransaction();

      // Find employee and company
      const [employee, company] = await Promise.all([
        Employee.findById(employeeId).session(session),
        Company.findOne({ companyCode }).session(session)
      ]);

      if (!employee) {
        throw new Error("Employee not found");
      }

      if (!company) {
        throw new Error("Company not found");
      }

      // Find or create payroll record
      let payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: parsedDate
        },
        {
          $set: {
            retirementAnnuityFund: {
              employeeContribution: parseFloat(employeeContribution),
              employerContribution: employerContribution ? parseFloat(employerContribution) : 0,
              employeeHandlesPayment: employeeHandlesPayment === "on",
              beneficiary: beneficiary || "",
              lastUpdated: new Date(),
              month: parsedDate,
              enabled: true
            }
          }
        },
        {
          new: true,
          upsert: true,
          session,
          runValidators: true
        }
      );

      // Commit transaction
      await session.commitTransaction();
      console.log("Transaction committed successfully");

      // Set success flash message
      req.flash("success", "Retirement annuity fund updated successfully");
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

    } catch (error) {
      console.error("\n=== Error in Retirement Annuity Fund Processing ===");
      console.error("Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      if (session) {
        try {
          await session.abortTransaction();
          console.log("Transaction rolled back");
        } catch (abortError) {
          console.error("Error aborting transaction:", abortError);
        }
      }

      req.flash("error", `Error updating retirement annuity fund: ${error.message}`);
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

    } finally {
      if (session) {
        session.endSession();
        console.log("Session ended");
      }
    }
  }
);



// Add voluntary tax over deduction route handler
router.post(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/regularInputs/voluntaryTaxOverDeduction",
    "/:companyCode/employeeProfile/:employeeId/regularInputs/voluntaryTaxOverDeduction"
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    let session;
    try {
      const { companyCode, employeeId } = req.params;
      const { amount, relevantDate } = req.body;

      console.log("Processing voluntary tax over deduction:", {
        amount,
        relevantDate
      });

      // Input validation
      if (!amount || isNaN(amount) || parseFloat(amount) < 0) {
        throw new Error("Invalid voluntary tax over deduction amount");
      }

      if (!relevantDate) {
        throw new Error("Relevant date is required");
      }

      // Parse date
      const parsedDate = new Date(relevantDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error("Invalid date format");
      }

      // Start MongoDB session
      session = await mongoose.startSession();
      session.startTransaction();

      // Find employee and company
      const [employee, company] = await Promise.all([
        Employee.findById(employeeId).session(session),
        Company.findOne({ companyCode }).session(session)
      ]);

      if (!employee) {
        throw new Error("Employee not found");
      }

      if (!company) {
        throw new Error("Company not found");
      }

      // Find or create payroll record
      const payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: parsedDate
        },
        {
          $set: {
            voluntaryTaxOverDeduction: parseFloat(amount),
            "data.voluntaryTaxOverDeductionDate": parsedDate
          }
        },
        {
          new: true,
          upsert: true,
          session,
          runValidators: true
        }
      );

      if (!payroll) {
        throw new Error("Failed to update payroll record");
      }

      // Log the update
      console.log("Updated voluntary tax over deduction:", {
        amount: payroll.voluntaryTaxOverDeduction,
        date: payroll.data?.get('voluntaryTaxOverDeductionDate')
      });

      // Commit transaction
      await session.commitTransaction();
      console.log("Transaction committed successfully");

      // Set success flash message
      req.flash("success", "Voluntary tax over deduction updated successfully");
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

    } catch (error) {
      console.error("Error in voluntary tax over deduction processing:", error);

      if (session) {
        try {
          await session.abortTransaction();
          console.log("Transaction rolled back");
        } catch (abortError) {
          console.error("Error aborting transaction:", abortError);
        }
      }

      req.flash("error", `Error updating voluntary tax over deduction: ${error.message}`);
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

    } finally {
      if (session) {
        session.endSession();
        console.log("Session ended");
      }
    }
  }
);

// Add employer loan route handler
router.post(
  [
    "/clients/:companyCode/employeeProfile/:employeeId/regularInputs/employer-loan",
    "/:companyCode/employeeProfile/:employeeId/regularInputs/employer-loan"
  ],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    let session;
    try {
      const { companyCode, employeeId } = req.params;
      const { interestRate, regularRepayment, calculateInterestBenefit, relevantDate } = req.body;

      console.log("Processing employer loan:", {
        params: { companyCode, employeeId },
        body: req.body
      });

      // Input validation
      if (!interestRate || isNaN(interestRate) || parseFloat(interestRate) < 0) {
        throw new Error("Invalid interest rate");
      }

      if (!regularRepayment || isNaN(regularRepayment) || parseFloat(regularRepayment) < 0) {
        throw new Error("Invalid regular repayment amount");
      }

      if (!relevantDate) {
        throw new Error("Relevant date is required");
      }

      // Parse date
      const parsedDate = new Date(relevantDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error("Invalid date format");
      }

      // Start MongoDB session
      session = await mongoose.startSession();
      session.startTransaction();

      // Find employee and company
      const [employee, company] = await Promise.all([
        Employee.findById(employeeId).session(session),
        Company.findOne({ companyCode }).session(session)
      ]);

      if (!employee) {
        throw new Error("Employee not found");
      }

      if (!company) {
        throw new Error("Company not found");
      }

      // Find or create payroll record
      const payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: parsedDate
        },
        {
          $set: {
            employerLoan: {
              interestRate: parseFloat(interestRate),
              regularRepayment: parseFloat(regularRepayment),
              calculateInterestBenefit: calculateInterestBenefit === 'on',
              lastUpdated: new Date()
            }
          }
        },
        {
          new: true,
          upsert: true,
          session,
          runValidators: true
        }
      );

      await session.commitTransaction();

      if (req.xhr || req.headers.accept?.includes('json')) {
        return res.json({ success: true, payroll });
      }

      req.flash('success', 'Employer loan updated successfully');
      return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);

    } catch (error) {
      if (session) {
        await session.abortTransaction();
      }

      console.error('Error processing employer loan:', error);

      if (req.xhr || req.headers.accept?.includes('json')) {
        return res.status(400).json({ success: false, error: error.message });
      }

      req.flash('error', error.message || 'Error processing employer loan');
      return res.redirect(`/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`);

    } finally {
      if (session) {
        session.endSession();
      }
    }
  }
);

module.exports = router;
