const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const User = require("../models/user");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const EmployerDetails = require("../models/employerDetails");
const Notification = require("../models/notification");
const crypto = require("crypto");
const moment = require("moment");
const SupportAccess = require('../models/SupportAccess');
const populateUserCompanies = require('../middleware/populateUserCompanies');

// Add the populateUserCompanies middleware to ensure companies are always populated
router.use(populateUserCompanies);

// Dashboard route that handles both normal access and support token access
router.get("/:companyCode/dashboard", ensureAuthenticated, async (req, res, next) => {

  try {
    // Verify user and company data integrity
    const verifyUserData = () => {
      if (!req.user) throw new Error('User not found');
      if (!req.user.companies) throw new Error('User companies not found');
      if (!Array.isArray(req.user.companies)) throw new Error('Invalid companies data structure');
      return true;
    };

    // Check if there's a support token in the query or session
    if (req.query.supportToken || req.session.supportAccess) {
      
      // Use token from query or session
      const supportToken = req.query.supportToken || req.session.supportAccess?.token;
      
      // Use company code from URL parameters
      const companyCode = req.params.companyCode;
      
      // Check if we have support access in session
      if (req.session.supportAccess) {
      }
      
      try {
        // Try to find the company by company code (case insensitive)
        // First try exact match
        let company = await Company.findOne({ companyCode: companyCode });
        
        if (company) {
        } else {
          company = await Company.findOne({ 
            companyCode: { $regex: new RegExp(`^${companyCode}$`, 'i') } 
          });
          
          if (company) {
          } else {
          }
        }
        
        if (!company) {
          req.flash('error', 'Company not found');
          return res.redirect('/login');
        }

        // Verify the support access token
        const supportAccess = await SupportAccess.findOne({
          token: supportToken,
          company: company._id,
          isRevoked: false,
          expiresAt: { $gt: new Date() }
        });

        if (supportAccess) {
        } else {
          console.log(`Support token validation failed. Token not found or expired.`);
          
          // Check if token exists at all
          const anyToken = await SupportAccess.findOne({ token: supportToken });
          if (anyToken) {
          } else {
          }
        }

        if (!supportAccess) {
          req.flash('error', 'Support access token is invalid or expired');
          return res.redirect('/login');
        }


        // Create a minimal user object for support access
        const user = {
          _id: 'support',
          email: '<EMAIL>',
          firstName: 'Support',
          lastName: 'Access',
          currentCompany: company,
          isSupportAccess: true,
          companies: [company]
        };


        // Get dashboard data for support access
        const currentDate = moment([filterYear, filterMonth - 1]).endOf('month');
        const firstDayOfMonth = moment([filterYear, filterMonth - 1]).startOf('month');


        try {
          const employees = await Employee.find({ company: company._id });
          const headcount = employees.length;

          // Check for valid dates before querying
          const validDoa = (doa) => moment(doa).isValid();

          const joiners = await Employee.countDocuments({
            company: company._id,
            doa: {
              $gte: firstDayOfMonth.toDate(),
              $lte: currentDate.toDate(),
            },
          }).where('doa').ne(null).where('doa').exists();

          const terminations = await Employee.countDocuments({
            company: company._id,
            endServiceDate: {
              $gte: firstDayOfMonth.toDate(),
              $lte: currentDate.toDate(),
            },
          }).where('endServiceDate').ne(null).where('endServiceDate').exists();

          const latestNotification = await Notification.findOne({
            company: company._id,
          })
            .sort({ createdAt: -1 })
            .limit(1);

          const headcountPerMonth = await calculateHeadcountPerMonth(
            company._id,
            filterYear,
            filterMonth
          );

          const costCenterDistribution = await calculateCostCenterDistribution(
            company._id
          );

          // Render the dashboard with support access
          return res.render("dashboard", {
            title: "Dashboard",
            user: user,
            company: company,
            companies: [company], // For support access, only include the current company
            currentCompany: company,
            headcount,
            joiners,
            terminations,
            latestNotification,
            headcountPerMonth,
            costCenterDistribution,
            currentCompanyCode: company.companyCode,
            moment,
            filterMonth,
            filterYear,
            req,
            supportAccess: true
          });
        } catch (dataError) {
          console.error("Error fetching dashboard data:", dataError);
          req.flash('error', 'Error fetching dashboard data');
          return res.redirect('/login');
        }
      } catch (companyError) {
        console.error("Error finding company:", companyError);
        req.flash('error', 'Error finding company');
        return res.redirect('/login');
      }
    } else {
      
      // Verify user data integrity
      verifyUserData();

      // No support token, use normal authentication
      if (!req.isAuthenticated()) {
        return res.redirect('/login');
      }

      // Continue with normal dashboard rendering for authenticated users
      const { companyCode } = req.params;
      
      // Get the current year and calculate valid year range
      const currentYear = moment().year();
      const minYear = currentYear - 5;
      // Only allow next year if we're in Q4 (October onwards)
      const maxYear = moment().quarter() >= 4 ? currentYear + 1 : currentYear;
      
      // Validate and set filter month and year
      const filterMonth = Math.max(1, Math.min(12, parseInt(req.query.month))) || new Date().getMonth() + 1;
      const requestedYear = parseInt(req.query.year) || currentYear;
      
      // If requested year is next year, only allow it if we're in Q4
      const filterYear = requestedYear > currentYear ? 
        (moment().quarter() >= 4 ? Math.min(requestedYear, maxYear) : currentYear) :
        Math.max(minYear, Math.min(maxYear, requestedYear));


      try {
        // Get company data from user's current company
        const company = await Company.findOne({ companyCode });
        
        if (!company) {
          return res.status(404).render("error", { message: "Company not found" });
        }
        

        // Populate user with companies
        const user = await User.findById(req.user._id).populate('companies');

        // Check if user has access to this company
        if (!user.companies || !Array.isArray(user.companies)) {
          return res.status(403).render("error", { message: "Access denied - No companies" });
        }
        
        const hasAccess = user.companies.some(c => {
          if (!c || !c._id) {
            return false;
          }
          try {
            return c._id.toString() === company._id.toString();
          } catch (err) {
            console.log("Error comparing company IDs:", err);
            return false;
          }
        });

        if (!hasAccess) {
          return res.status(403).render("error", { message: "Access denied" });
        }
        

        // Get dashboard data for authenticated users
        const currentDate = moment([filterYear, filterMonth - 1]).endOf('month');
        const firstDayOfMonth = moment([filterYear, filterMonth - 1]).startOf('month');


        const employees = await Employee.find({ company: company._id });
        const headcount = employees.length;

        // Check for valid dates before querying
        const validDoa = (doa) => moment(doa).isValid();

        const joiners = await Employee.countDocuments({
          company: company._id,
          doa: {
            $gte: firstDayOfMonth.toDate(),
            $lte: currentDate.toDate(),
          },
        }).where('doa').ne(null).where('doa').exists();

        const terminations = await Employee.countDocuments({
          company: company._id,
          endServiceDate: {
            $gte: firstDayOfMonth.toDate(),
            $lte: currentDate.toDate(),
          },
        }).where('endServiceDate').ne(null).where('endServiceDate').exists();

        const latestNotification = await Notification.findOne({
          company: company._id,
        })
          .sort({ createdAt: -1 })
          .limit(1);

        const headcountPerMonth = await calculateHeadcountPerMonth(
          company._id,
          filterYear,
          filterMonth
        );

        const costCenterDistribution = await calculateCostCenterDistribution(
          company._id
        );

        // Get employee counts for all companies
        const companiesWithCounts = await Promise.all(
          user.companies.map(async (company) => {
            const activeEmployeeCount = await getActiveEmployeeCount(company._id);
            return {
              ...company.toObject(),
              employeeCount: activeEmployeeCount,
            };
          })
        );

        // Update user's current company if needed
        if (
          !user.currentCompany ||
          user.currentCompany.toString() !== company._id.toString()
        ) {
          user.currentCompany = company._id;
          await user.save();
        }

        // Render the dashboard for authenticated users
        return res.render("dashboard", {
          title: "Dashboard",
          user,
          company,
          companies: companiesWithCounts,
          currentCompany: company,
          headcount,
          joiners,
          terminations,
          latestNotification,
          headcountPerMonth,
          costCenterDistribution,
          currentCompanyCode: company.companyCode,
          moment,
          filterMonth,
          filterYear,
          req,
          supportAccess: false
        });
      } catch (authError) {
        console.error("Error in authenticated user flow:", authError);
        req.flash('error', 'Error processing dashboard for authenticated user');
        return res.redirect('/login');
      }
    }
  } catch (error) {
    console.error("CRITICAL ERROR in dashboard route:", error);
    console.error("Error stack:", error.stack);
    req.flash('error', error.message || 'An error occurred while loading the dashboard');
    return res.redirect('/login');
  }
});

// Helper functions
async function calculateHeadcountPerMonth(companyId, filterYear, filterMonth) {
  try {
    const sixMonthsAgo = moment([filterYear, filterMonth - 1])
      .subtract(5, "months")
      .startOf("month");
    const endDate = moment([filterYear, filterMonth - 1]).endOf("month");

    const employeeData = await Employee.find({
      company: companyId,
      doa: { $lte: endDate.toDate() },
      $or: [
        { endServiceDate: null },
        { endServiceDate: { $gte: sixMonthsAgo.toDate() } },
      ],
    }).select("doa endServiceDate");

    const headcountData = [];

    for (
      let d = moment(sixMonthsAgo);
      d.isSameOrBefore(endDate);
      d.add(1, "month")
    ) {
      const year = d.year();
      const month = d.month() + 1;
      const monthEnd = moment(d).endOf("month");

      const count = employeeData.filter((employee) => {
        const startDate = moment(employee.doa);
        const endDate = employee.endServiceDate
          ? moment(employee.endServiceDate)
          : moment().add(1, "day");
        return startDate.isSameOrBefore(monthEnd) && endDate.isAfter(d);
      }).length;

      headcountData.push({
        _id: { year, month },
        count,
      });
    }

    return headcountData;
  } catch (error) {
    console.error("Error in calculateHeadcountPerMonth:", error);
    return [];
  }
}

function fillMissingMonths(data, startDate, endDate) {
  const filledData = [];

  for (
    let d = moment(startDate);
    d.isSameOrBefore(endDate);
    d.add(1, "month")
  ) {
    const year = d.year();
    const month = d.month() + 1;
    const existingData = data.find(
      (item) => item._id.year === year && item._id.month === month
    );

    if (existingData) {
      filledData.push(existingData);
    } else {
      filledData.push({
        _id: { year, month },
        count: 0,
      });
    }
  }

  return filledData;
}

async function calculateCostCenterDistribution(companyId) {
  try {
    const costCenterData = await Employee.aggregate([
      {
        $match: {
          company: companyId,
          costCentre: { $exists: true, $ne: null },
        },
      },
      {
        $group: {
          _id: "$costCentre",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ]);

    return costCenterData;
  } catch (error) {
    console.error("Error in calculateCostCenterDistribution:", error);
    return [];
  }
}

async function getActiveEmployeeCount(companyId) {
  try {
    return await Employee.countDocuments({
      company: companyId,
      status: "Active"
    });
  } catch (error) {
    console.error("Error in getActiveEmployeeCount:", error);
    return 0;
  }
}

router.post("/quick-add-company", ensureAuthenticated, async (req, res) => {
  try {
    // Generate a unique company code
    const companyCode = generateUniqueCompanyCode();

    // Create a new Company with minimal details
    const newCompany = new Company({
      name: "New Company", // Default name, to be updated later
      owner: req.user._id,
      companyCode: companyCode, // Add the generated company code
    });
    await newCompany.save();

    // Create a new EmployerDetails document
    const newEmployerDetails = new EmployerDetails({
      company: newCompany._id,
      tradingName: "New Company", // Default name, to be updated later
    });
    await newEmployerDetails.save();

    // Link EmployerDetails to Company
    newCompany.employerDetails = newEmployerDetails._id;
    await newCompany.save();

    // Add the new company to the user's companies array
    req.user.companies.push(newCompany._id);
    await req.user.save();

    // Redirect to the settings page for the new company
    res.redirect(`/settings?companyId=${newCompany._id}&isNewCompany=true`);
  } catch (error) {
    console.error("Error creating new company:", error);
    res.status(500).send("Error creating new company");
  }
});

// Function to generate a unique company code
function generateUniqueCompanyCode() {
  return crypto.randomBytes(3).toString("hex").toUpperCase();
}

module.exports = router;
