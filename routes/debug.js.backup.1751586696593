const express = require('express');
const router = express.Router();
const moment = require('moment-timezone');
const Employee = require('../models/Employee');
const PayrollPeriod = require('../models/PayrollPeriod');
const PayFrequency = require('../models/PayFrequency');
const payrollPeriodEngine = require('../utils/payrollPeriodEngine');

/**
 * PRODUCTION DEBUGGING ENDPOINT
 * Temporary endpoint to debug the timezone/period issue in production
 */

router.get('/production-debug/:employeeId', async (req, res) => {
  try {
    const { employeeId } = req.params;
    
    
    // Get employee data
    const employee = await Employee.findById(employeeId).populate('payFrequency');
    if (!employee) {
      return res.json({ error: 'Employee not found' });
    }
    
    // Get all payroll periods for this employee
    const payrollPeriods = await PayrollPeriod.find({
      employee: employeeId
    }).sort({ endDate: 1 });
    
    // Server environment info
    const serverInfo = {
      timezone: process.env.TZ || 'Not set',
      nodeTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      serverTime: new Date().toString(),
      serverUTC: new Date().toISOString(),
      momentGuess: moment.tz.guess(),
      timezoneOffset: new Date().getTimezoneOffset()
    };
    
    // Employee analysis
    const employeeAnalysis = {
      id: employee._id,
      name: `${employee.firstName} ${employee.lastName}`,
      doa: employee.doa,
      doaFormatted: moment.utc(employee.doa).format('YYYY-MM-DD'),
      payFrequency: employee.payFrequency ? {
        frequency: employee.payFrequency.frequency,
        lastDayOfPeriod: employee.payFrequency.lastDayOfPeriod,
        firstPayrollPeriodEndDate: employee.payFrequency.firstPayrollPeriodEndDate
      } : null
    };
    
    // Analyze existing periods
    const periodAnalysis = payrollPeriods.map(period => {
      const startDate = moment.utc(period.startDate);
      const endDate = moment.utc(period.endDate);
      const duration = endDate.diff(startDate, 'days') + 1;
      
      return {
        id: period._id,
        startDate: period.startDate,
        endDate: period.endDate,
        startFormatted: startDate.format('YYYY-MM-DD'),
        endFormatted: endDate.format('YYYY-MM-DD'),
        duration: duration,
        basicSalary: period.basicSalary,
        isFinalized: period.isFinalized,
        isProblematic: duration < 7 && employee.payFrequency?.frequency === 'monthly'
      };
    });
    
    // Generate correct periods using business rule engine
    let correctPeriods = [];
    if (employee.payFrequency) {
      try {
        const firstPeriodEnd = payrollPeriodEngine.calculateFirstPeriodEndDate(
          employee.doa,
          employee.payFrequency
        );
        
        correctPeriods = payrollPeriodEngine.generatePayrollPeriods(
          firstPeriodEnd,
          employee.payFrequency,
          new Date()
        ).map(period => ({
          startDate: moment.utc(period.startDate).format('YYYY-MM-DD'),
          endDate: moment.utc(period.endDate).format('YYYY-MM-DD'),
          duration: moment.utc(period.endDate).diff(moment.utc(period.startDate), 'days') + 1
        }));
      } catch (error) {
        correctPeriods = [{ error: error.message }];
      }
    }
    
    // Find current period
    const today = moment.utc();
    const currentPeriodFromDB = payrollPeriods.find(period => {
      const start = moment.utc(period.startDate);
      const end = moment.utc(period.endDate);
      return today.isBetween(start, end, null, '[]');
    });
    
    // Issues analysis
    const issues = [];
    
    if (employee.payFrequency?.firstPayrollPeriodEndDate) {
      const configuredFirstEnd = moment.utc(employee.payFrequency.firstPayrollPeriodEndDate);
      const employeeDOA = moment.utc(employee.doa);
      
      if (configuredFirstEnd.isBefore(employeeDOA)) {
        issues.push({
          type: 'CRITICAL',
          message: 'firstPayrollPeriodEndDate is BEFORE employee DOA',
          details: {
            configuredFirstEnd: configuredFirstEnd.format('YYYY-MM-DD'),
            employeeDOA: employeeDOA.format('YYYY-MM-DD'),
            impact: 'This causes incorrect period generation'
          }
        });
      }
    }
    
    const shortPeriods = periodAnalysis.filter(p => p.isProblematic);
    if (shortPeriods.length > 0) {
      issues.push({
        type: 'CRITICAL',
        message: 'Found periods shorter than 7 days for monthly frequency',
        details: shortPeriods
      });
    }
    
    if (!currentPeriodFromDB) {
      issues.push({
        type: 'WARNING',
        message: 'No current period found containing today\'s date',
        details: {
          today: today.format('YYYY-MM-DD'),
          suggestion: 'Period generation may be incorrect'
        }
      });
    }
    
    // Response
    const debugData = {
      timestamp: new Date().toISOString(),
      serverInfo,
      employeeAnalysis,
      periodAnalysis: {
        totalPeriods: payrollPeriods.length,
        periods: periodAnalysis,
        currentPeriod: currentPeriodFromDB ? {
          id: currentPeriodFromDB._id,
          start: moment.utc(currentPeriodFromDB.startDate).format('YYYY-MM-DD'),
          end: moment.utc(currentPeriodFromDB.endDate).format('YYYY-MM-DD'),
          basicSalary: currentPeriodFromDB.basicSalary
        } : null
      },
      correctPeriods: {
        total: correctPeriods.length,
        periods: correctPeriods.slice(0, 6) // Show first 6 periods
      },
      issues,
      recommendations: issues.length > 0 ? [
        'Fix firstPayrollPeriodEndDate to be after employee DOA',
        'Regenerate payroll periods using business rule engine',
        'Ensure monthly periods have proper duration (28-31 days)'
      ] : ['No critical issues found']
    };
    
    
    res.json(debugData);
    
  } catch (error) {
    console.error("❌ PRODUCTION DEBUG ERROR:", error);
    res.status(500).json({ 
      error: error.message,
      stack: error.stack 
    });
  }
});

/**
 * Fix periods endpoint - regenerates periods using business rule engine
 */
router.post('/fix-periods/:employeeId', async (req, res) => {
  try {
    const { employeeId } = req.params;
    
    
    const employee = await Employee.findById(employeeId).populate('payFrequency');
    if (!employee) {
      return res.json({ error: 'Employee not found' });
    }
    
    if (!employee.payFrequency) {
      return res.json({ error: 'No pay frequency configured for employee' });
    }
    
    // Generate correct periods
    const firstPeriodEnd = payrollPeriodEngine.calculateFirstPeriodEndDate(
      employee.doa,
      employee.payFrequency
    );
    
    const correctPeriods = payrollPeriodEngine.generatePayrollPeriods(
      firstPeriodEnd,
      employee.payFrequency,
      new Date()
    );
    
    // Delete existing periods (optional - be careful!)
    if (req.query.deleteExisting === 'true') {
      await PayrollPeriod.deleteMany({ employee: employeeId });
    }
    
    // Create new periods
    const createdPeriods = [];
    for (const period of correctPeriods) {
      const newPeriod = new PayrollPeriod({
        employee: employeeId,
        company: employee.company,
        startDate: period.startDate,
        endDate: period.endDate,
        basicSalary: 0, // Will be set later
        isFinalized: false,
        isPartial: false
      });
      
      const saved = await newPeriod.save();
      createdPeriods.push(saved);
    }
    
    
    res.json({
      success: true,
      message: `Created ${createdPeriods.length} new periods`,
      periods: createdPeriods.map(p => ({
        id: p._id,
        start: moment.utc(p.startDate).format('YYYY-MM-DD'),
        end: moment.utc(p.endDate).format('YYYY-MM-DD')
      }))
    });
    
  } catch (error) {
    console.error("❌ FIX PERIODS ERROR:", error);
    res.status(500).json({ 
      error: error.message,
      stack: error.stack 
    });
  }
});

module.exports = router;
