const express = require('express');
const router = express.Router();
const Company = require('../models/Company');
const LeaveType = require('../models/LeaveType');

// Internal API: Get all active leave types for a company (intended for server-to-server calls, bypasses main auth)
router.get('/leave-types/:companyCode', async (req, res) => {
  try {
    const { companyCode } = req.params;

    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({ success: false, message: 'Company not found' });
    }

    const leaveTypes = await LeaveType.find({ company: company._id, active: true })
      .select('name description category daysPerYear')
      .sort({ name: 1 });
    

    return res.json({ success: true, data: leaveTypes });
  } catch (error) {
    console.error('Error fetching leave types via internal API:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch leave types internally', error: error.message });
  }
});

module.exports = router; 