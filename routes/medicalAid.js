const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const {
  formatDateForMongoDB,
  formatDateForDisplay,
  getCurrentOrNextLastDayOfMonth,
} = require("../utils/dateUtils");

// GET route to display the medical aid form
router.get(
  "/clients/:companyCode/employeeProfile/:employeeId/medical-aid",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;

      // Fetch employee and company data
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee || !employee.company) {
        req.flash("error", "Employee or company not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile`);
      }

      // Get existing payroll data
      const existingPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(getCurrentOrNextLastDayOfMonth()),
      });

      console.log(
        "Rendering medical aid form with CSRF token:",
        req.csrfToken()
      );

      res.render("newMedicalAid", {
        employee,
        company: employee.company,
        companyCode,
        employeeId,
        relevantDate:
          req.query.relevantDate || getCurrentOrNextLastDayOfMonth(),
        formattedDate: formatDateForDisplay(
          req.query.relevantDate || getCurrentOrNextLastDayOfMonth()
        ),
        messages: req.flash(),
        existingData: existingPayroll?.medical || {},
      });
    } catch (error) {
      console.error("Error in GET medical aid route:", error);
      req.flash("error", "An error occurred while loading the form");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for medical aid
router.post(
  "/clients/:companyCode/employeeProfile/:employeeId/medical-aid",
  ensureAuthenticated,
  async (req, res) => {
    try {

      const { employeeId, companyCode } = req.params;
      const {
        amount,
        relevantDate,
        employerContribution,
        members,
        employeeHandlesPayment,
        dontApplyTaxCredits,
      } = req.body;

      // Fetch employee and verify company
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee || !employee.company) {
        req.flash("error", "Employee or company not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile`);
      }

      // Find or create payroll entry
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: formatDateForMongoDB(relevantDate),
        });
      }

      // Update medical aid information
      payroll.medical = {
        medicalAid: parseFloat(amount),
        employerContribution: parseFloat(employerContribution) || 0,
        members: parseInt(members) || 0,
        employeeHandlesPayment: employeeHandlesPayment === "on",
        dontApplyTaxCredits: dontApplyTaxCredits === "on",
      };

      await payroll.save();

      req.flash("success", "Medical aid information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Medical Aid Form Error:", error);
      req.flash(
        "error",
        "An error occurred while saving medical aid information"
      );
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add this new route before your existing routes
router.get(
  "/clients/:companyCode/employeeProfile/:employeeId/test-medical-aid",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;

      // Generate CSRF token
      const csrfToken = req.csrfToken();

      // Fetch employee and company data
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee || !employee.company) {
        req.flash("error", "Employee or company not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile`);
      }

      // Get existing payroll data if any
      const existingPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(getCurrentOrNextLastDayOfMonth()),
      });

      // Prepare view data
      const viewData = {
        employee,
        company: employee.company,
        companyCode,
        employeeId,
        relevantDate:
          req.query.relevantDate || getCurrentOrNextLastDayOfMonth(),
        formattedDate: formatDateForDisplay(
          req.query.relevantDate || getCurrentOrNextLastDayOfMonth()
        ),
        messages: req.flash(),
        csrfToken,
        existingData: existingPayroll?.medical || {},
      };

      console.log("Rendering test form with data:", {
        employeeId,
        companyCode,
        csrfToken,
      });

      res.render("testMedicalAidForm", viewData);
    } catch (error) {
      console.error("Error in GET test medical aid route:", error);
      req.flash("error", "An error occurred while loading the test form");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Simplified test routes
router.get("/test-csrf", (req, res) => {
  console.log("Test form GET - CSRF Token:", req.csrfToken());
  res.render("test-csrf", {
    messages: req.flash(),
    csrfToken: req.csrfToken(),
  });
});

router.post("/test-csrf", (req, res) => {
  console.log("Test form POST - Received data:", {
    csrf: req.body._csrf,
    testField: req.body.testField,
  });
  req.flash("success", "Form submitted successfully!");
  res.redirect("/test-csrf");
});

module.exports = router;
