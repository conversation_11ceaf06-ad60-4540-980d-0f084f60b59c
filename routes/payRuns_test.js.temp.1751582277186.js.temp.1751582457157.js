const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const PayRun = require("../models/payRun");
const Company = require("../models/Company");
const nodeFetch = require("node-fetch");
const csrf = require("csurf");
const axios = require("axios");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Simple logging middleware
router.use((req, res, next) => {
  next();
});

// Client-facing route for payrun details
router.get("/:companyCode/payruns/:id", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id,
    path: req.path,
    originalUrl: req.originalUrl
  });
  console.log('Session data:', {
    userId: req.user?._id,
    isAuthenticated: req.isAuthenticated(),
    companyCode: req.session?.companyCode
  });

  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
        
        // Redirect to the correct URL with the company code
        const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
        return res.redirect(correctUrl);
      }
      
      // If still no company code, try to get from user's current company
      if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
          
          // Redirect to the correct URL with the company code
          const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
          return res.redirect(correctUrl);
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    
    // Find company
    const company = await Company.findOne({ companyCode });
    console.log('Company lookup result:', company ? {
      id: company._id,
      code: company.companyCode,
      name: company.name
    } : 'Not found');

    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }


    // Find payrun and populate payslips and employee data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    console.log('Pay run lookup result:', payRun ? {
      id: payRun._id,
      startDate: payRun.startDate,
      endDate: payRun.endDate,
      payslipsCount: payRun.payslips ? payRun.payslips.length : 0
    } : 'Not found');

    // If pay run not found, return 404
    if (!payRun) {
      return res.status(404).render('error', {
        message: 'Pay run not found',
        error: { status: 404 }
      });
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      
      // Attach them to the pay run
      if (payrollPeriods.length > 0) {
        try {
          // Save the IDs to the database
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          
          // For rendering, use the fully populated payroll periods
          payRun.payrollPeriods = payrollPeriods;
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    } else {
      // If payrollPeriods exist but employee is not populated, populate it now
      if (payRun.payrollPeriods.length > 0 && (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' || !payRun.payrollPeriods[0].employee.firstName)) {
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    // Calculate totals
    const totals = payRun.payslips.reduce((acc, payslip) => {
      acc.totalGrossPay += payslip.grossPay || 0;
      acc.totalNetPay += payslip.netPay || 0;
      acc.totalPAYE += payslip.PAYE || 0;
      acc.totalSDL += payslip.SDL || 0;
      acc.totalUIF += payslip.UIF || 0;
      return acc;
    }, {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    });


    // Render the payrun details page
    res.render('payRunDetails', {
      title: 'Pay Run Details',
      company,
      payRun,
      user: req.user,
      csrfToken: req.csrfToken(),
      moment: require('moment'),
      ...totals
    });

  } catch (error) {
    console.error('\n=== Error in Pay Run Details Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error fetching pay run details',
      error: { 
        status: 500, 
        stack: process.env.NODE_ENV === 'development' ? error.stack : '' 
      }
    });
  }
});

// Process a pay run
router.post("/:companyCode/payruns/:id/process", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    });

    if (!payRun) {
      return res.status(404).render('error', { 
        message: 'Pay run not found',
        error: { status: 404, stack: '' }
      });
    }

    // Update the pay run status to processing
    payRun.status = 'processing';
    await payRun.save();


    // Redirect back to the pay run details page
    res.redirect(`/clients/${company.companyCode}/payruns/${payRun._id}`);

  } catch (error) {
    console.error('\n=== Error in Process Pay Run Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error processing pay run',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// Finalize a pay run
router.post("/:companyCode/payruns/:id/finalize", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Validate company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      
      // Attach them to the pay run
      payRun.payrollPeriods = payrollPeriods;
      
      // Save the updated pay run with the payroll periods
      if (payrollPeriods.length > 0) {
        try {
          // Save the IDs to the database
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          
          // Re-fetch the pay run with populated payroll periods
          await payRun.populate({
            path: 'payrollPeriods',
            populate: {
              path: 'employee',
              select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
            }
          });
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    } else {
      // If payrollPeriods exist but employee is not populated, populate it now
      if (payRun.payrollPeriods.length > 0 && (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' || !payRun.payrollPeriods[0].employee.firstName)) {
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    // Check if payslips need to be created
    if ((!payRun.payslips || payRun.payslips.length === 0) && payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      
      const Payslip = require('../models/Payslip');
      
      // Check for existing payslips to avoid duplicates
      const existingPayslips = await Payslip.find({
        payRun: payRun._id
      });
      
      if (existingPayslips.length > 0) {
        payRun.payslips = existingPayslips.map(p => p._id);
        payRun.status = 'finalized';
        payRun.finalizedAt = new Date();
        payRun.finalizedBy = req.user._id;
        await payRun.save();
      } else {
        // Create payslips from payroll periods
        const payslips = await Promise.all(
          payRun.payrollPeriods.map(async (period) => {
            
            // Extract earnings and deductions if available
            const earnings = [];
            if (period.basicSalary) {
              earnings.push({
                type: 'basic',
                description: 'Basic Salary',
                amount: period.basicSalary
              });
            }
            
            // Extract deductions if available
            const deductions = [];
            if (period.totalDeductions && period.totalDeductions > 0) {
              // If we don't have detailed deductions, create a generic tax entry
              deductions.push({
                type: 'tax',
                description: 'Tax Deductions',
                amount: period.totalDeductions
              });
            }
            
            // Create payslip with all available data
            const payslip = new Payslip({
              company: company._id,
              employee: period.employee._id,
              payrollPeriod: period._id,
              payRun: payRun._id,
              basicSalary: period.basicSalary || 0,
              grossPay: period.grossPay || 0,
              totalDeductions: period.totalDeductions || 0,
              netPay: period.netPay || 0,
              earnings: earnings,
              deductions: deductions,
              status: 'approved',
              paymentMethod: period.employee.paymentMethod || 'EFT',
              bankDetails: {
                accountHolder: period.employee.bankDetails?.accountHolder || `${period.employee.firstName} ${period.employee.lastName}`,
                accountNumber: period.employee.bankDetails?.accountNumber || '',
                bankName: period.employee.bankDetails?.bankName || '',
                branchCode: period.employee.bankDetails?.branchCode || ''
              }
            });
            
            try {
              const savedPayslip = await payslip.save();
              return savedPayslip;
            } catch (error) {
              console.error(`Error creating payslip for employee ${period.employee._id}:`, error);
              throw error;
            }
          })
        );
        
        
        // Update payRun with the newly created payslips
        payRun.payslips = [...(payRun.payslips || []), ...payslips.map(p => p._id)];
        payRun.status = 'finalized';
        payRun.finalizedAt = new Date();
        payRun.finalizedBy = req.user._id;
        
        await payRun.save();
      }
    }

    res.json({
      success: true,
      message: 'Pay run finalized successfully',
      payRunId: payRun._id
    });

  } catch (error) {
    console.error('\n=== Error in Finalize Pay Run Route ===');
    console.error(error);
    console.error('=== End of Error ===\n');
