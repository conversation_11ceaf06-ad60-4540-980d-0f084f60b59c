const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const PayRun = require("../models/payRun");
const Company = require("../models/Company");
const nodeFetch = require("node-fetch");
const csrf = require("csurf");
const axios = require("axios");
const PDFDocument = require("pdfkit-table");
const ExcelJS = require("exceljs");
const path = require("path");
const fs = require("fs");
const moment = require("moment");
const Payslip = require("../models/Payslip");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Simple logging middleware
router.use((req, res, next) => {
  next();
});

// Client-facing route for payrun details
router.get("/:companyCode/payruns/:id", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id,
    path: req.path,
    originalUrl: req.originalUrl
  });
  console.log('Session data:', {
    userId: req.user?._id,
    isAuthenticated: req.isAuthenticated(),
    companyCode: req.session?.companyCode
  });

  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
        
        // Redirect to the correct URL with the company code
        const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
        return res.redirect(correctUrl);
      }
      
      // If still no company code, try to get from user's current company
      if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
          
          // Redirect to the correct URL with the company code
          const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
          return res.redirect(correctUrl);
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    
    // Find company
    const company = await Company.findOne({ companyCode });
    console.log('Company lookup result:', company ? {
      id: company._id,
      code: company.companyCode,
      name: company.name
    } : 'Not found');

    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }


    // Find payrun and populate payslips and employee data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    console.log('Pay run lookup result:', payRun ? {
      id: payRun._id,
      startDate: payRun.startDate,
      endDate: payRun.endDate,
      payslipsCount: payRun.payslips ? payRun.payslips.length : 0
    } : 'Not found');

    // If pay run not found, return 404
    if (!payRun) {
      return res.status(404).render('error', {
        message: 'Pay run not found',
        error: { status: 404 }
      });
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      
      // Save the IDs to the pay run
      if (payrollPeriods.length > 0) {
        try {
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
      
      // Attach the populated payroll periods to the pay run object for rendering
      // This is important because we need the populated data for the template
      payRun.payrollPeriods = payrollPeriods;
    } else {
      // If payrollPeriods exist but aren't populated, populate them
      if (payRun.payrollPeriods.length > 0 && 
          (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' || 
           !payRun.payrollPeriods[0].employee.firstName)) {
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    // Calculate totals - use payslips if available, otherwise use payroll periods
    let totals;
    
    if (payRun.payslips && payRun.payslips.length > 0) {
      // Calculate from payslips
      totals = payRun.payslips.reduce((acc, payslip) => {
        acc.totalGrossPay += payslip.grossPay || 0;
        acc.totalNetPay += payslip.netPay || 0;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += payslip.SDL || 0;
        acc.totalUIF += payslip.UIF || 0;
        return acc;
      }, {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      });
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // Calculate from payroll periods if no payslips
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        acc.totalGrossPay += period.grossPay || 0;
        acc.totalNetPay += period.netPay || 0;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += period.SDL || 0;
        acc.totalUIF += period.UIF || 0;
        return acc;
      }, {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      });
    } else {
      // Default to zeros if neither payslips nor payroll periods
      totals = {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      };
    }


    // Render the payrun details page
    res.render('payRunDetails', {
      title: 'Pay Run Details',
      company,
      payRun,
      user: req.user,
      csrfToken: req.csrfToken(),
      moment: require('moment'),
      ...totals
    });

  } catch (error) {
    console.error('\n=== Error in Pay Run Details Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error fetching pay run details',
      error: { 
        status: 500, 
        stack: process.env.NODE_ENV === 'development' ? error.stack : '' 
      }
    });
  }
});

// Process a pay run
router.post("/:companyCode/payruns/:id/process", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    });

    if (!payRun) {
      return res.status(404).render('error', { 
        message: 'Pay run not found',
        error: { status: 404, stack: '' }
      });
    }

    // Update the pay run status to processing
    payRun.status = 'processing';
    await payRun.save();


    // Redirect back to the pay run details page
    res.redirect(`/clients/${company.companyCode}/payruns/${payRun._id}`);

  } catch (error) {
    console.error('\n=== Error in Process Pay Run Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error processing pay run',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// Finalize a pay run
router.post("/:companyCode/payruns/:id/finalize", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Validate company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      
      // Attach them to the pay run
      payRun.payrollPeriods = payrollPeriods;
      
      // Save the updated pay run with the payroll periods
      if (payrollPeriods.length > 0) {
        try {
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          
          // Re-fetch the pay run with populated payroll periods
          await payRun.populate({
            path: 'payrollPeriods',
            populate: {
              path: 'employee',
              select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
            }
          });
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    }

    // Check if payslips need to be created
    if ((!payRun.payslips || payRun.payslips.length === 0) && payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      
      const Payslip = require('../models/Payslip');
      
      // Create payslips from payroll periods
      const payslips = await Promise.all(
        payRun.payrollPeriods.map(async (period) => {
          
          const payslip = new Payslip({
            company: company._id,
            employee: period.employee._id,
            payrollPeriod: period._id,
            payRun: payRun._id,
            basicSalary: period.basicSalary || 0,
            grossPay: period.grossPay || 0,
            totalDeductions: period.totalDeductions || 0,
            netPay: period.netPay || 0,
            status: 'approved',
            paymentMethod: period.employee.paymentMethod || 'EFT',
            bankDetails: period.employee.bankDetails || {}
          });
          
          const savedPayslip = await payslip.save();
          return savedPayslip;
        })
      );
      
      
      // Update payRun with the newly created payslips
      payRun.payslips = [...(payRun.payslips || []), ...payslips.map(p => p._id)];
      payRun.status = 'finalized';
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;
      
      await payRun.save();
    }

    res.json({
      success: true,
      message: 'Pay run finalized successfully',
      payRunId: payRun._id
    });

  } catch (error) {
    console.error('\n=== Error in Finalize Pay Run Route ===');
    console.error(error);
    console.error('=== End of Error ===\n');

    // Send a JSON response with the error details
    res.status(500).json({
      success: false,
      message: 'Failed to finalize pay run',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// GET /api/pay-runs
router.get("/", ensureAuthenticated, async (req, res) => {
  try {
    const payRuns = await PayRun.find();
    res.json({ success: true, data: payRuns });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// GET /api/pay-runs/:id
router.get("/:id", ensureAuthenticated, async (req, res) => {
  try {
    const payRun = await PayRun.findById(req.params.id);
    if (!payRun) {
      return res
        .status(404)
        .json({ success: false, message: "Pay run not found" });
    }
    res.json({ success: true, data: payRun });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// POST /api/pay-runs/:id/xero
router.post(
  "/:id/xero",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);
      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Get the company for this pay run
      const company = await Company.findById(payRun.company);
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get CSRF token
      const csrfToken = req.csrfToken();

      // Get base URL from environment variables with fallback
      const protocol = process.env.PROTOCOL || "http";
      const host = process.env.HOST || "localhost:3002";
      const baseUrl = `${protocol}://${host}`;

      // Include company code in URL path
      const syncUrl = `${baseUrl}/xero/${company.companyCode}/sync-payroll`;

      // Use axios instead of fetch
      const xeroResponse = await axios({
        method: "post",
        url: syncUrl,
        headers: {
          "Content-Type": "application/json",
          "CSRF-Token": csrfToken,
          Cookie: req.headers.cookie,
        },
        data: {
          payrollId: payRun._id,
          payrollData: {
            basicSalary: payRun.totalAmount,
            employeeCount: payRun.payslips?.length || 0,
            period: {
              startDate: payRun.startDate,
              endDate: payRun.endDate,
            },
          },
          description: `Payroll sync for period ${payRun.startDate} to ${payRun.endDate}`,
        },
        withCredentials: true,
        timeout: 30000, // 30 second timeout
        validateStatus: function (status) {
          return status >= 200 && status < 500; // Don't reject if status is 4xx
        },
      });


      if (xeroResponse.status === 404) {
        throw new Error(
          "Xero sync endpoint not found. Please check the server configuration."
        );
      }

      if (xeroResponse.status !== 200) {
        console.error("Xero sync failed:", xeroResponse.data);
        throw new Error(
          xeroResponse.data.message || "Failed to sync with Xero"
        );
      }

      const result = xeroResponse.data;

      // Update pay run with Xero sync status
      await PayRun.findByIdAndUpdate(payRun._id, {
        $set: {
          xeroSynced: true,
          xeroSyncedAt: new Date(),
          xeroSyncStatus: result.status,
        },
      });

      res.json({
        success: true,
        message: "Successfully initiated Xero sync",
        payRunId: payRun._id,
        syncStatus: result,
      });
    } catch (error) {
      console.error("Xero sync error:", error);
      res.status(error.response?.status || 500).json({
        success: false,
        message: "Failed to process Xero integration",
        error: error.message || "Unknown error occurred",
        details: error.response?.data || {},
      });
    }
  }
);

// GET /api/pay-runs/:id/report/pdf
router.get("/:id/report/pdf", ensureAuthenticated, async (req, res) => {
  console.log('Request params:', {
    payRunId: req.params.id,
    companyCode: req.query.companyCode
  });

  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }

    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run with populated data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4' });
    
    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=payrun-${payRun._id}-${moment().format('YYYY-MM-DD')}.pdf`);
    
    // Pipe the PDF document to the response
    doc.pipe(res);

    // Add company logo if available (placeholder for now)
    // doc.image('path/to/logo.png', 50, 45, { width: 100 });

    // Add document title
    doc.fontSize(20).text(`${company.name} - Pay Run Report`, { align: 'center' });
    doc.moveDown();

    // Add pay run details
    doc.fontSize(12).text(`Pay Run Period: ${moment(payRun.startDate).format('DD MMM YYYY')} - ${moment(payRun.endDate).format('DD MMM YYYY')}`, { align: 'center' });
    doc.fontSize(12).text(`Pay Run Reference: ${payRun.reference || payRun._id}`, { align: 'center' });
    doc.fontSize(12).text(`Status: ${payRun.status}`, { align: 'center' });
    doc.moveDown(2);

    // Add summary section
    doc.fontSize(16).text('Summary', { underline: true });
    doc.moveDown();

    // Calculate totals
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    };

    if (payRun.payslips && payRun.payslips.length > 0) {
      // Calculate from payslips
      totals = payRun.payslips.reduce((acc, payslip) => {
        acc.totalGrossPay += payslip.grossPay || 0;
        acc.totalNetPay += payslip.netPay || 0;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += payslip.SDL || 0;
        acc.totalUIF += payslip.UIF || 0;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // Calculate from payroll periods
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        acc.totalGrossPay += period.grossPay || 0;
        acc.totalNetPay += period.netPay || 0;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += period.SDL || 0;
        acc.totalUIF += period.UIF || 0;
        return acc;
      }, totals);
    }

    // Add summary table
    const summaryTable = {
      title: "Financial Summary",
      headers: ["Description", "Amount (R)"],
      rows: [
        ["Total Gross Pay", totals.totalGrossPay.toFixed(2)],
        ["Total PAYE", totals.totalPAYE.toFixed(2)],
        ["Total UIF", totals.totalUIF.toFixed(2)],
        ["Total SDL", totals.totalSDL.toFixed(2)],
        ["Total Net Pay", totals.totalNetPay.toFixed(2)]
      ]
    };

    await doc.table(summaryTable, { 
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
      prepareRow: () => doc.font('Helvetica').fontSize(10)
    });
    
    doc.moveDown(2);

    // Add detailed section
    doc.fontSize(16).text('Employee Details', { underline: true });
    doc.moveDown();

    // Determine which data source to use
    const dataSource = payRun.payslips && payRun.payslips.length > 0 
      ? payRun.payslips 
      : payRun.payrollPeriods;

    if (dataSource && dataSource.length > 0) {
      // Prepare table data
      const tableData = {
        title: "Employee Payments",
        headers: ["Employee #", "Name", "Gross Pay (R)", "PAYE (R)", "UIF (R)", "SDL (R)", "Net Pay (R)"],
        rows: dataSource.map(item => {
          const employee = item.employee;
          // Handle both payslip and payroll period data structures
          const grossPay = item.grossPay || 0;
          const netPay = item.netPay || 0;
          const paye = item.PAYE || (item.payrollPeriod ? item.payrollPeriod.PAYE : 0) || 0;
          const uif = item.UIF || (item.payrollPeriod ? item.payrollPeriod.UIF : 0) || 0;
          const sdl = item.SDL || (item.payrollPeriod ? item.payrollPeriod.SDL : 0) || 0;
          
          return [
            employee.companyEmployeeNumber || 'N/A',
            `${employee.firstName} ${employee.lastName}`,
            grossPay.toFixed(2),
            paye.toFixed(2),
            uif.toFixed(2),
            sdl.toFixed(2),
            netPay.toFixed(2)
          ];
        })
      };

      await doc.table(tableData, { 
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10)
      });
    } else {
      doc.text('No employee data available for this pay run.');
    }

    // Add footer
    doc.moveDown(2);
    doc.fontSize(10).text(`Generated on ${moment().format('DD MMM YYYY HH:mm:ss')}`, { align: 'center' });
    doc.fontSize(10).text(`Panda Software Solutions Group`, { align: 'center' });

    // Finalize the PDF
    doc.end();

  } catch (error) {
    console.error('\n=== Error in Generate PDF Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: 'Failed to generate PDF report',
      error: error.message
    });
  }
});

// GET /api/pay-runs/:id/report/excel
router.get("/:id/report/excel", ensureAuthenticated, async (req, res) => {
  console.log('Request params:', {
    payRunId: req.params.id,
    companyCode: req.query.companyCode
  });

  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }

    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run with populated data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // Create a new Excel workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Panda Software Solutions Group';
    workbook.lastModifiedBy = 'Panda Software Solutions Group';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add a summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Add title and header information
    summarySheet.mergeCells('A1:G1');
    summarySheet.getCell('A1').value = `${company.name} - Pay Run Report`;
    summarySheet.getCell('A1').font = { size: 16, bold: true };
    summarySheet.getCell('A1').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A2:G2');
    summarySheet.getCell('A2').value = `Period: ${moment(payRun.startDate).format('DD MMM YYYY')} - ${moment(payRun.endDate).format('DD MMM YYYY')}`;
    summarySheet.getCell('A2').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A3:G3');
    summarySheet.getCell('A3').value = `Reference: ${payRun.reference || payRun._id}`;
    summarySheet.getCell('A3').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A4:G4');
    summarySheet.getCell('A4').value = `Status: ${payRun.status}`;
    summarySheet.getCell('A4').alignment = { horizontal: 'center' };

    // Calculate totals
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    };

    if (payRun.payslips && payRun.payslips.length > 0) {
      // Calculate from payslips
      totals = payRun.payslips.reduce((acc, payslip) => {
        acc.totalGrossPay += payslip.grossPay || 0;
        acc.totalNetPay += payslip.netPay || 0;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += payslip.SDL || 0;
        acc.totalUIF += payslip.UIF || 0;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // Calculate from payroll periods
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        acc.totalGrossPay += period.grossPay || 0;
        acc.totalNetPay += period.netPay || 0;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += period.SDL || 0;
        acc.totalUIF += period.UIF || 0;
        return acc;
      }, totals);
    }

    // Add summary data
    summarySheet.addRow([]);
    summarySheet.addRow(['Financial Summary']);
    summarySheet.lastRow.font = { bold: true, size: 14 };
    
    summarySheet.addRow(['Description', 'Amount (R)']);
    summarySheet.lastRow.font = { bold: true };
    
    summarySheet.addRow(['Total Gross Pay', totals.totalGrossPay.toFixed(2)]);
    summarySheet.addRow(['Total PAYE', totals.totalPAYE.toFixed(2)]);
    summarySheet.addRow(['Total UIF', totals.totalUIF.toFixed(2)]);
    summarySheet.addRow(['Total SDL', totals.totalSDL.toFixed(2)]);
    summarySheet.addRow(['Total Net Pay', totals.totalNetPay.toFixed(2)]);
    
    // Format the currency cells
    for (let i = 4; i <= 8; i++) {
      summarySheet.getCell(`B${i}`).numFmt = '#,##0.00';
    }

    // Add a details worksheet
    const detailsSheet = workbook.addWorksheet('Employee Details');
    
    // Add headers
    detailsSheet.addRow(['Employee #', 'First Name', 'Last Name', 'Gross Pay (R)', 'PAYE (R)', 'UIF (R)', 'SDL (R)', 'Net Pay (R)']);
    detailsSheet.getRow(1).font = { bold: true };
    
    // Determine which data source to use
    const dataSource = payRun.payslips && payRun.payslips.length > 0 
      ? payRun.payslips 
      : payRun.payrollPeriods;

    if (dataSource && dataSource.length > 0) {
      // Add employee data
      dataSource.forEach(item => {
        const employee = item.employee;
        // Handle both payslip and payroll period data structures
        const grossPay = item.grossPay || 0;
        const netPay = item.netPay || 0;
        const paye = item.PAYE || (item.payrollPeriod ? item.payrollPeriod.PAYE : 0) || 0;
        const uif = item.UIF || (item.payrollPeriod ? item.payrollPeriod.UIF : 0) || 0;
        const sdl = item.SDL || (item.payrollPeriod ? item.payrollPeriod.SDL : 0) || 0;
        
        detailsSheet.addRow([
          employee.companyEmployeeNumber || 'N/A',
          employee.firstName,
          employee.lastName,
          grossPay,
          paye,
          uif,
          sdl,
          netPay
        ]);
      });
      
      // Format the currency columns
      for (let i = 2; i <= dataSource.length + 1; i++) {
        for (let j = 4; j <= 8; j++) {
          const cell = detailsSheet.getCell(i, j);
          cell.numFmt = '#,##0.00';
        }
      }
    } else {
      detailsSheet.addRow(['No employee data available for this pay run.']);
    }

    // Auto-size columns for better readability
    detailsSheet.columns.forEach(column => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, cell => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = maxLength < 10 ? 10 : maxLength + 2;
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=payrun-${payRun._id}-${moment().format('YYYY-MM-DD')}.xlsx`);

    // Write to response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('\n=== Error in Generate Excel Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: 'Failed to generate Excel report',
      error: error.message
    });
  }
});

// GET /api/pay-runs/:id/report/pdf - API endpoint for PDF reports
router.get("/api/pay-runs/:id/report/pdf", ensureAuthenticated, async (req, res) => {
  console.log('Request params:', {
    payRunId: req.params.id,
    companyCode: req.query.companyCode
  });
  
  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }
    
    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }
    
    // Find pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });
    
    if (!payRun) {
      throw new Error('Pay run not found');
    }
    
    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=PayRun_${payRun._id}_Report.pdf`);
    
    // Pipe the PDF document to the response
    doc.pipe(res);
    
    // Add company header
    doc.fontSize(20).text(`${company.name} - Pay Run Report`, { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(`Period: ${moment(payRun.startDate).format('DD MMM YYYY')} to ${moment(payRun.endDate).format('DD MMM YYYY')}`, { align: 'center' });
    doc.moveDown();
    
    // Add summary section
    doc.fontSize(16).text('Summary', { underline: true });
    doc.moveDown();
    
    // Determine data source based on what's available
    let dataSource = [];
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalUIF: 0,
      totalSDL: 0
    };
    
    if (payRun.payslips && payRun.payslips.length > 0) {
      dataSource = payRun.payslips;
      // Calculate totals from payslips
      totals = payRun.payslips.reduce((acc, payslip) => {
        acc.totalGrossPay += payslip.grossPay || 0;
        acc.totalNetPay += payslip.netPay || 0;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += payslip.SDL || 0;
        acc.totalUIF += payslip.UIF || 0;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      dataSource = payRun.payrollPeriods;
      // Calculate totals from payroll periods
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        acc.totalGrossPay += period.grossPay || 0;
        acc.totalNetPay += period.netPay || 0;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += period.SDL || 0;
        acc.totalUIF += period.UIF || 0;
        return acc;
      }, totals);
    }
    
    // Add summary table
    const summaryTable = {
      headers: ['Description', 'Amount'],
      rows: [
        ['Total Employees', dataSource.length],
        ['Total Gross Pay', `R ${totals.totalGrossPay.toFixed(2)}`],
        ['Total Net Pay', `R ${totals.totalNetPay.toFixed(2)}`],
        ['Total PAYE', `R ${totals.totalPAYE.toFixed(2)}`],
        ['Total UIF', `R ${totals.totalUIF.toFixed(2)}`],
        ['Total SDL', `R ${totals.totalSDL.toFixed(2)}`]
      ]
    };
    
    doc.table(summaryTable, {
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
      prepareRow: () => doc.font('Helvetica').fontSize(10)
    });
    
    doc.moveDown(2);
    
    // Add detailed section
    doc.fontSize(16).text('Employee Details', { underline: true });
    doc.moveDown();
    
    if (dataSource && dataSource.length > 0) {
      // Add employee data
      const employeeTable = {
        headers: ['Employee #', 'Name', 'Gross Pay', 'Deductions', 'Net Pay'],
        rows: dataSource.map(item => {
          const employee = item.employee;
          // Handle both payslip and payroll period data structures
          const grossPay = item.grossPay || 0;
          const netPay = item.netPay || 0;
          const deductions = grossPay - netPay;
          
          return [
            employee.companyEmployeeNumber || 'N/A',
            `${employee.firstName} ${employee.lastName}`,
            `R ${grossPay.toFixed(2)}`,
            `R ${deductions.toFixed(2)}`,
            `R ${netPay.toFixed(2)}`
          ];
        })
      };
      
      doc.table(employeeTable, {
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10)
      });
    } else {
      doc.text('No employee data available for this pay run.');
    }
    
    // Finalize the PDF
    doc.end();
    
  } catch (error) {
    console.error('\n=== Error in Generate PDF Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: error.message || 'Error generating PDF report'
    });
  }
});

// GET /api/pay-runs/:id/report/excel - API endpoint for Excel reports
router.get("/api/pay-runs/:id/report/excel", ensureAuthenticated, async (req, res) => {
  console.log('Request params:', {
    payRunId: req.params.id,
    companyCode: req.query.companyCode
  });
  
  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }
    
    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }
    
    // Find pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });
    
    if (!payRun) {
      throw new Error('Pay run not found');
    }
    
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Panda Software Solutions Group';
    workbook.lastModifiedBy = 'Panda Software Solutions Group';
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // Add a summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Add title
    summarySheet.mergeCells('A1:E1');
    const titleCell = summarySheet.getCell('A1');
    titleCell.value = `${company.name} - Pay Run Report`;
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    
    // Add period
    summarySheet.mergeCells('A2:E2');
    const periodCell = summarySheet.getCell('A2');
    periodCell.value = `Period: ${moment(payRun.startDate).format('DD MMM YYYY')} to ${moment(payRun.endDate).format('DD MMM YYYY')}`;
    periodCell.font = { size: 12 };
    periodCell.alignment = { horizontal: 'center' };
    
    // Add empty row
    summarySheet.addRow([]);
    
    // Determine data source based on what's available
    let dataSource = [];
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalUIF: 0,
      totalSDL: 0
    };
    
    if (payRun.payslips && payRun.payslips.length > 0) {
      dataSource = payRun.payslips;
      // Calculate totals from payslips
      totals = payRun.payslips.reduce((acc, payslip) => {
        acc.totalGrossPay += payslip.grossPay || 0;
        acc.totalNetPay += payslip.netPay || 0;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += payslip.SDL || 0;
        acc.totalUIF += payslip.UIF || 0;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      dataSource = payRun.payrollPeriods;
      // Calculate totals from payroll periods
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        acc.totalGrossPay += period.grossPay || 0;
        acc.totalNetPay += period.netPay || 0;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += period.SDL || 0;
        acc.totalUIF += period.UIF || 0;
        return acc;
      }, totals);
    }
    
    // Add summary data
    summarySheet.addRow(['Summary Information']);
    summarySheet.addRow(['Description', 'Amount']);
    summarySheet.addRow(['Total Employees', dataSource.length]);
    summarySheet.addRow(['Total Gross Pay', `R ${totals.totalGrossPay.toFixed(2)}`]);
    summarySheet.addRow(['Total Net Pay', `R ${totals.totalNetPay.toFixed(2)}`]);
    summarySheet.addRow(['Total PAYE', `R ${totals.totalPAYE.toFixed(2)}`]);
    summarySheet.addRow(['Total UIF', `R ${totals.totalUIF.toFixed(2)}`]);
    summarySheet.addRow(['Total SDL', `R ${totals.totalSDL.toFixed(2)}`]);
    
    // Format summary table
    const summaryHeaderRow = summarySheet.getRow(5);
    summaryHeaderRow.font = { bold: true };
    summarySheet.getColumn(1).width = 20;
    summarySheet.getColumn(2).width = 15;
    
    // Add a details worksheet
    const detailsSheet = workbook.addWorksheet('Employee Details');
    
    // Add headers
    detailsSheet.addRow(['Employee #', 'Name', 'Gross Pay', 'Deductions', 'Net Pay', 'Payment Method']);
    const headerRow = detailsSheet.getRow(1);
    headerRow.font = { bold: true };
    
    // Add employee data
    if (dataSource && dataSource.length > 0) {
      // Add employee data
      dataSource.forEach(item => {
        const employee = item.employee;
        // Handle both payslip and payroll period data structures
        const grossPay = item.grossPay || 0;
        const netPay = item.netPay || 0;
        const deductions = grossPay - netPay;
        
        detailsSheet.addRow([
          employee.companyEmployeeNumber || 'N/A',
          `${employee.firstName} ${employee.lastName}`,
          `R ${grossPay.toFixed(2)}`,
          `R ${deductions.toFixed(2)}`,
          `R ${netPay.toFixed(2)}`,
          employee.paymentMethod || 'EFT'
        ]);
      });
    } else {
      detailsSheet.addRow(['No employee data available for this pay run.']);
    }
    
    // Format details table
    detailsSheet.getColumn(1).width = 15;
    detailsSheet.getColumn(2).width = 25;
    detailsSheet.getColumn(3).width = 15;
    detailsSheet.getColumn(4).width = 15;
    detailsSheet.getColumn(5).width = 15;
    detailsSheet.getColumn(6).width = 20;
    
    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=PayRun_${payRun._id}_Report.xlsx`);
    
    // Write to response
    await workbook.xlsx.write(res);
    
  } catch (error) {
    console.error('\n=== Error in Generate Excel Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: error.message || 'Error generating Excel report'
    });
  }
});

// GET /:companyCode/payruns/:id/download - Client-facing download route
router.get("/:companyCode/payruns/:id/download", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id,
    format: req.query.format || 'pdf'
  });
  
  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
      } 
      // If still no company code, try to get from user's current company
      else if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    // Determine format (default to PDF if not specified)
    const format = req.query.format || 'pdf';
    console.log(`Generating ${format.toUpperCase()} report for pay run ${req.params.id}`);
    
    // Generate CSRF token for the API request
    const csrfToken = req.csrfToken();
    
    // Redirect to the appropriate report endpoint
    const redirectUrl = `/api/pay-runs/${req.params.id}/report/${format}?_csrf=${csrfToken}&companyCode=${companyCode}`;
    
    return res.redirect(redirectUrl);
    
  } catch (error) {
    console.error('\n=== Error in Pay Run Download Route ===');
    console.error(error);
    
    return res.status(500).render('error', {
      message: 'Error generating report',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// POST /payrun/create - Create a new pay run
router.post("/create", ensureAuthenticated, async (req, res) => {
  try {
    const { payslipIds, payRunType = 'regular', description } = req.body;

    // Validate payslips
    const payslips = await Payslip.find({
      _id: { $in: payslipIds },
      company: req.user.currentCompany,
      isFinalized: true,
      payRun: { $exists: false },
    }).populate("employee");

    if (payslips.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No valid payslips found for pay run creation",
      });
    }

    // Group payslips by period to create separate pay runs
    const payslipsByPeriod = payslips.reduce((acc, payslip) => {
      if (!acc[payslip.period]) {
        acc[payslip.period] = [];
      }
      acc[payslip.period].push(payslip);
      return acc;
    }, {});

    const results = [];

    // Create pay runs for each period
    for (const [period, periodPayslips] of Object.entries(payslipsByPeriod)) {
      // Find existing pay runs for this period and type to determine sequence
      const existingPayRuns = await PayRun.find({
        company: req.user.currentCompany,
        period: period,
        payRunType: payRunType
      }).sort({ sequence: -1 });

      // Calculate next sequence number
      const nextSequence = existingPayRuns.length > 0 ? existingPayRuns[0].sequence + 1 : 1;

      const totalPayable = periodPayslips.reduce((sum, p) => sum + p.netPay, 0);
      const totalAmount = periodPayslips.reduce((sum, p) => sum + p.grossPay, 0);

      const newPayRun = new PayRun({
        company: req.user.currentCompany,
        period,
        payRunType,
        description,
        sequence: nextSequence,
        payslips: periodPayslips.map((p) => p._id),
        totalPayable,
        totalAmount,
        status: "created",
        createdBy: req.user._id,
        createdAt: new Date(),
      });

      await newPayRun.save();

      // Update payslips with pay run reference
      await Payslip.updateMany(
        { _id: { $in: periodPayslips.map((p) => p._id) } },
        { $set: { payRun: newPayRun._id } }
      );

      results.push({
        period,
        payRunId: newPayRun._id,
        totalPayable,
        totalAmount,
        payslipCount: periodPayslips.length,
        sequence: nextSequence,
        payRunType
      });
    }

    res.json({
      success: true,
      message: "Pay run(s) created successfully",
      results,
    });
  } catch (error) {
    console.error("Error creating pay run:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create pay run",
    });
  }
});

module.exports = router;
