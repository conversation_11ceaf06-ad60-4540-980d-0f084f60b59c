const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });

router.use(csrfProtection);

// Map form types to their templates
const formTemplates = {
  // Regular Forms
  "medical-aid": "newMedicalAid",
  garnishee: "newGarnishee",
  "maintenance-order": "newMaintenanceOrder",
  "pension-fund": "newPensionFund",
  "provident-fund": "newProvidentFund",
  "retirement-annuity": "newRetirementAnnuity",
  "income-protection": "newIncomeProtection",
  "union-fee": "newUnionMembershipFee",
  savings: "newSavings",
  "employer-loan": "newEmployerLoan",

  // Travel Forms
  "travel-allowance": "newTravelAllowanceInput",
  "travel-reimbursement": "newTravelReimbursement",
  "company-petrol-card": "newCompanyPetrolCard",

  // Company Car Forms
  "company-car": "newCompanyCar",
  "company-car-lease": "newCompanyCarUnderOperatingLease",

  // Additional Forms
  commissions: "newCommissions",
  "loss-of-income": "newLossOfIncome",
  "accommodation-benefit": "newAccommodationBenefitInput",
  "tax-directive": "newTaxDirective",
};

// GET route for displaying forms
router.get(
  "/clients/:companyCode/employeeProfile/:employeeId/new/:formType",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { formType } = req.params;
      const template = formTemplates[formType];

      if (!template) {
        console.error(`Unknown form type: ${formType}`);
        req.flash("error", `Unsupported form type: ${formType}`);
        return res.redirect("/");
      }

      // Render the appropriate template
      res.render(template, {
        csrfToken: req.csrfToken(),
        formType,
        // ... other necessary data
      });
    } catch (error) {
      console.error("Error in GET form route:", error);
      req.flash("error", "An error occurred while loading the form");
      res.redirect("/");
    }
  }
);

// POST route for form submissions
router.post(
  "/clients/:companyCode/employeeProfile/:employeeId/new/:formType",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { formType } = req.params;

      if (!formTemplates[formType]) {
        throw new Error(`Unsupported form type: ${formType}`);
      }

      // Process the form data based on type
      // ... handle form submission logic

      res.json({
        success: true,
        message: "Form submitted successfully",
      });
    } catch (error) {
      console.error("Error in POST form route:", error);
      res.status(500).json({
        error: "An error occurred while processing the form",
        details: error.message,
      });
    }
  }
);

module.exports = router;
