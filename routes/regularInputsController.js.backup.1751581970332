const Payroll = require("../models/payroll");

exports.getPayrollForm = async (req, res) => {
  try {
    const payroll = await Payroll.findOne({});

    const regularInputsName = req.query.regularInputs_name || "garnishee";

    res.render("regularInputs/new", {
      payroll,
      regularInputsName,
    });
  } catch (err) {
    console.error(err);
    res.status(500).send("Internal Server Error");
  }
};
