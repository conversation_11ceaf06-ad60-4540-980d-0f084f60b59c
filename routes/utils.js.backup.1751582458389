const express = require('express');
const router = express.Router();
const { generateToken, verifyToken, extractToken } = require('../utils/jwtUtils');

// Generate a test JWT token
router.get('/generate-jwt', (req, res) => {
  try {
    console.log('=== Generate JWT ===');
    // Create a test user object
    const testUser = {
      _id: '123456789',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    };
    
    // Generate JWT
    const token = generateToken(testUser);
    console.log('JWT generated successfully');
    
    // Set JWT in cookie
    res.cookie('jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', 
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    console.log('JWT cookie set');
    
    // Return token
    res.json({ 
      success: true, 
      message: 'JWT generated successfully',
      token
    });
  } catch (error) {
    console.error('JWT Generation Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Verify a JWT token
router.get('/verify-jwt', (req, res) => {
  try {
    console.log('=== Verify JWT ===');
    // Extract JWT from request
    const token = extractToken(req);
    
    if (!token) {
      console.log('No JWT token found');
      return res.json({ 
        success: false, 
        message: 'No token provided'
      });
    }
    
    // Verify JWT
    const decoded = verifyToken(token);
    console.log('JWT verification result:', decoded ? 'Valid' : 'Invalid');
    
    if (!decoded) {
      return res.json({ 
        success: false, 
        message: 'Invalid token'
      });
    }
    
    // Return decoded token
    res.json({
      success: true,
      message: 'Token verified successfully',
      user: decoded
    });
  } catch (error) {
    console.error('JWT Verification Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Check authentication status
router.get('/auth-status', (req, res) => {
  try {
    console.log('=== Auth Status ===');
    // Extract JWT from request
    const token = extractToken(req);
    
    // Verify JWT if present
    let jwtUser = null;
    if (token) {
      jwtUser = verifyToken(token);
    }
    
    // Return auth status
    res.json({
      success: true,
      auth: {
        session: {
          isAuthenticated: req.isAuthenticated?.() || false,
          sessionID: req.sessionID || null,
          user: req.user ? {
            id: req.user._id,
            username: req.user.username,
            email: req.user.email,
            role: req.user.role
          } : null
        },
        jwt: {
          hasToken: !!token,
          isValid: !!jwtUser,
          user: jwtUser
        }
      }
    });
  } catch (error) {
    console.error('Auth Status Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Protected resource - requires either JWT or session auth
router.get('/protected-resource', (req, res) => {
  try {
    console.log('=== Protected Resource ===');
    // Check JWT auth
    const token = extractToken(req);
    let jwtUser = null;
    if (token) {
      jwtUser = verifyToken(token);
    }
    
    // Check session auth
    const sessionAuth = req.isAuthenticated?.() || false;
    
    // If neither auth method is valid
    if (!jwtUser && !sessionAuth) {
      return res.json({
        success: false,
        message: 'Authentication required to access this resource',
        authMethods: {
          jwt: !!jwtUser,
          session: sessionAuth
        }
      });
    }
    
    // Return protected data
    res.json({
      success: true,
      message: 'Access granted to protected resource',
      data: {
        secret: 'This is protected data',
        timestamp: new Date().toISOString()
      },
      authenticatedVia: {
        jwt: !!jwtUser,
        session: sessionAuth
      },
      user: jwtUser || (req.user ? {
        id: req.user._id,
        username: req.user.username,
        email: req.user.email,
        role: req.user.role
      } : null)
    });
  } catch (error) {
    console.error('Protected Resource Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router; 