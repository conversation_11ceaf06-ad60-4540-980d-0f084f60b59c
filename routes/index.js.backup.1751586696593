const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");

router.get("/settings", ensureAuthenticated, async (req, res) => {
  const companyId = req.query.companyId;
  const activeTab = req.query.tab || "accountingIntegrations"; // Default to 'accountingIntegrations' if no tab is specified
  let company = null;

  if (companyId) {
    company = await Company.findById(companyId).populate("employerDetails");
    if (!company || String(company.owner) !== String(req.user._id)) {
      return res.status(404).send("Company not found or unauthorized");
    }
  }

  res.render("settings", {
    user: req.user,
    company: company,
    activeTab: activeTab,
  });
});

module.exports = router;
