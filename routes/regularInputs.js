const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const Company = require("../models/Company");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const formatDate = require("./helpers");
const PDFDocument = require("pdfkit");
const fs = require("fs");
const moment = require("moment-timezone");

//____________

router.get("/:employeeId/edit/commission/:month", async (req, res) => {
  try {
    const { employeeId, month } = req.params;

    // Find the employee
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Convert month parameter to a Date object
    const selectedMonth = new Date(month);

    // Find the specific payroll document for the selected month
    const currentPayroll = await Payroll.findOne({
      employee: employeeId,
      month: {
        $gte: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth(),
          1
        ),
        $lt: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth() + 1,
          1
        ),
      },
    });


    if (!currentPayroll) {
      return res.status(404).send("Payroll for the selected month not found");
    }

    // Special handling for commission
    const payComponent = {
      amount: currentPayroll.commission || 0,
      enabled: currentPayroll.commissionEnabled || false,
    };

    // Render the correct template with the relevant data
    res.render("newCommissionPayslip", {
      payComponent,
      employee,
      employeeId,
      payroll: currentPayroll,
      thisMonth: currentPayroll.month,
      company: employee.company,
      relevantDate: currentPayroll.month,
    });
  } catch (error) {
    console.error("Error in GET route:", error);
    res
      .status(500)
      .send("An error occurred while loading the data for editing.");
  }
});

// GET route to fetch payroll details for a specific employee and month
router.get("/:employeeId/edit/:componentName/:month", async (req, res) => {
  try {
    const { employeeId, componentName, month } = req.params;

    // Find the employee
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Convert month parameter to a Date object
    const selectedMonth = new Date(month);

    // Find the specific payroll document for the selected month
    const currentPayroll = await Payroll.findOne({
      employee: employeeId,
      month: {
        $gte: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth(),
          1
        ),
        $lt: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth() + 1,
          1
        ),
      },
    });


    if (!currentPayroll) {
      return res.status(404).send("Payroll for the selected month not found");
    }

    let payComponent;

    // Special handling for commission
    if (componentName === "commission") {
      payComponent = {
        amount: currentPayroll.commission || 0,
        enabled: currentPayroll.commissionEnabled || false,
      };
    } else if (componentName in currentPayroll) {
      payComponent = currentPayroll[componentName];
    } else {
      payComponent = currentPayroll.payComponents.find(
        (component) => component.componentType === componentName
      );
    }

    if (!payComponent) {
      return res
        .status(404)
        .send(`${componentName} component not found in payroll`);
    }

    // Define a variable to store the template name
    let templateName = `edit${
      componentName.charAt(0).toUpperCase() + componentName.slice(1)
    }`;

    // Render the correct template with the relevant data
    res.render(templateName, {
      payComponent,
      employee,
      componentName,
      employeeId,
      payroll: currentPayroll,
      thisMonth: currentPayroll.month,
      company: employee.company,
      relevantDate: currentPayroll.month,
    });
  } catch (error) {
    console.error("Error in GET route:", error);
    res
      .status(500)
      .send("An error occurred while loading the data for editing.");
  }
});

//__________UPDATE THE PAYCOMPONENT______________________

// POST route to update a specific pay component for an employee
// router.post("/:employeeId/edit/:componentName", async (req, res) => {
//   try {
//     const { employeeId, componentName } = req.params;
//     const { relevantDate, amount, ...otherComponentData } = req.body;

//     
//     

//     // Find the employee and populate company
//     const employee = await Employee.findById(employeeId).populate("company");
//     if (!employee) {
//       
//       return res.status(404).send("Employee not found");
//     }

//     // Find the specific payroll document for the provided month
//     const currentPayroll = await Payroll.findOne({
//       employee: employeeId,
//       month: new Date(relevantDate),
//     });

//     if (!currentPayroll) {
//       
//       return res.status(404).send("Payroll not found");
//     }

//     // Helper function to parse numeric values
//     const parseNumber = (value) => {
//       const parsed = parseFloat(value);
//       return isNaN(parsed) ? 0 : parsed;
//     };

//     // Update the specific pay component in the payroll document
//     if (componentName === "garnishee") {
//       currentPayroll.garnishee = parseNumber(amount);
//     } else if (componentName in currentPayroll) {
//       // For other direct components
//       currentPayroll[componentName] = {
//         ...currentPayroll[componentName],
//         ...Object.entries(otherComponentData).reduce((acc, [key, value]) => {
//           acc[key] = ["employeeHandlesPayment", "dontApplyTaxCredits"].includes(
//             key
//           )
//             ? value === "on" || value === "true"
//             : parseNumber(value);
//           return acc;
//         }, {}),
//         amount: parseNumber(amount),
//       };
//     } else {
//       // For components in payComponents array
//       const componentIndex = currentPayroll.payComponents.findIndex(
//         (component) => component.componentType === componentName
//       );

//       if (componentIndex !== -1) {
//         currentPayroll.payComponents[componentIndex] = {
//           ...currentPayroll.payComponents[componentIndex],
//           ...Object.entries(otherComponentData).reduce((acc, [key, value]) => {
//             acc[key] = [
//               "employeeHandlesPayment",
//               "dontApplyTaxCredits",
//             ].includes(key)
//               ? value === "on" || value === "true"
//               : parseNumber(value);
//             return acc;
//           }, {}),
//           amount: parseNumber(amount),
//         };
//       } else {
//         
//         return res.status(404).send("Pay component not found in payroll");
//       }
//     }

//     

//     // Save the updated payroll document
//     await currentPayroll.save();

//     

//     // Redirect to the employee profile page
//     res.redirect(
//       `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
//     );
//   } catch (error) {
//     console.error("Error updating pay component:", error);
//     res.status(500).send("An error occurred while updating the pay component.");
//   }
// });

//___________DELETE PAY______________________________

router.post("/:employeeId/remove/:componentName", async (req, res) => {
  const { employeeId, componentName } = req.params;

  try {

    // Check if employeeId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(employeeId)) {
      console.error("Invalid employee ID format");
      req.flash(
        "error",
        `Invalid employee ID format for employee ID: ${employeeId}`
      );
      return res.redirect(`/employeeProfile/${employeeId}`);
    }

    // Find employee and populate company
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      console.error("Employee Not Found");
      req.flash("error", `Employee Not Found for employee ID: ${employeeId}`);
      return res.redirect(`/employeeProfile/${employeeId}`);
    }

    const company = employee.company;
    if (!company) {
      console.error("Company Not Found for employee");
      req.flash("error", `Company Not Found for employee ID: ${employeeId}`);
      return res.redirect(`/employeeProfile/${employeeId}`);
    }

    // Find the most recent payroll data for the employee
    const payroll = await Payroll.findOne({ employee: employeeId }).sort({
      month: -1,
    });
    if (!payroll) {
      console.error("Payroll data not found for this employee");
      req.flash(
        "error",
        `Payroll data not found for employee ID: ${employeeId}`
      );
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }


    // Component name mapping: convert kebab-case to camelCase for database field names
    const componentNameMapping = {
      'accommodation-benefit': 'accommodationBenefit',
      'travel-allowance': 'travelAllowance',
      'medical-aid': 'medicalAid',
      'pension-fund': 'pensionFund',
      'provident-fund': 'providentFund',
      'retirement-annuity-fund': 'retirementAnnuityFund',
      'company-car': 'companyCar',
      'company-car-under-operating-lease': 'companyCarUnderOperatingLease',
      'loss-of-income': 'lossOfIncome',
      'maintenance-order': 'maintenanceOrder',
      'garnishee': 'garnishee',
      'union-membership-fee': 'unionMembershipFee',
      'income-protection': 'incomeProtection',
      'voluntary-tax-over-deduction': 'voluntaryTaxOverDeduction',
      'commission': 'commission',
      'bursaries-and-scholarships': 'bursariesAndScholarships'
    };

    // Get the actual field name in the database
    const actualFieldName = componentNameMapping[componentName] || componentName;


    // Check if the component exists in the payroll
    const componentExists = actualFieldName in payroll &&
                           payroll[actualFieldName] !== undefined &&
                           payroll[actualFieldName] !== null &&
                           payroll[actualFieldName] !== 0;

    if (!componentExists) {
      console.error(`${actualFieldName} component not found or is already empty in payroll`);
      req.flash(
        "error",
        `${componentName} component not found or is already removed for employee ID: ${employeeId}`
      );
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }


    // Remove the component by setting it to 0 (for Number fields) or undefined (for Object fields)
    if (typeof payroll[actualFieldName] === 'number') {
      payroll[actualFieldName] = 0;
    } else if (typeof payroll[actualFieldName] === 'object') {
      payroll[actualFieldName] = undefined;
    } else {
      payroll[actualFieldName] = undefined;
    }

    // Mark the field as modified to ensure Mongoose saves the change
    payroll.markModified(actualFieldName);

    await payroll.save();


    req.flash(
      "success",
      `${componentName} removed successfully for employee ID: ${employeeId}`
    );

    return res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    console.error("Error removing pay component:", error);
    req.flash(
      "error",
      `Error removing pay component "${componentName}" for employee ID: ${employeeId}`
    );
    return res.redirect(`/employeeProfile/${employeeId}`);
  }
});

//----------------------------------------

router.get("/:payComponentId/edit", async (req, res) => {
  try {
    const payComponentId = req.params.payComponentId;

    // Retrieve the pay component based on the provided payComponentId
    const payComponent = await LossOfIncome.findOne({ _id: payComponentId });

    if (!payComponent) {
      return res.status(404).send("Pay component not found 1");
    }

    // Access the employee data using the employeeId stored in the pay component
    const employee = await Employee.findById(payComponent.employeeId);

    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    res.render("editLossOfIncome", { payComponent, employee });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .send("An error occurred while loading the data for editing.");
  }
});

//________________________________________
router.get("/:employeeId/update/:componentName/:month", async (req, res) => {
  try {
    const { employeeId, componentName, month } = req.params;

    // Find the employee
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Convert month parameter to a Date object
    const selectedMonth = new Date(month);

    // Find the specific payroll document for the selected month
    const currentPayroll = await Payroll.findOne({
      employee: employeeId,
      month: {
        $gte: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth(),
          1
        ),
        $lt: new Date(
          selectedMonth.getFullYear(),
          selectedMonth.getMonth() + 1,
          1
        ),
      },
    });


    if (!currentPayroll) {
      return res.status(404).send("Payroll for the selected month not found");
    }

    let payComponent;

    // Special handling for commission
    if (componentName === "commission") {
      payComponent = {
        amount: currentPayroll.commission || 0,
        enabled: currentPayroll.commissionEnabled || false,
      };
    } else if (componentName in currentPayroll) {
      payComponent = currentPayroll[componentName];
    } else {
      payComponent = currentPayroll.payComponents.find(
        (component) => component.componentType === componentName
      );
    }

    if (!payComponent) {
      return res
        .status(404)
        .send(`${componentName} component not found in payroll`);
    }

    // Define a variable to store the template name
    let templateName = `edit${
      componentName.charAt(0).toUpperCase() + componentName.slice(1)
    }`;

    // Render the correct template with the relevant data
    res.render("editCommission", {
      payComponent,
      employee,
      componentName,
      employeeId,
      payroll: currentPayroll,
      thisMonth: currentPayroll.month,
      company: employee.company,
      relevantDate: currentPayroll.month,
    });
  } catch (error) {
    console.error("Error in GET route:", error);
    res
      .status(500)
      .send("An error occurred while loading the data for editing.");
  }
});
//_________________________________________

//______________________________________________________

module.exports = router;
