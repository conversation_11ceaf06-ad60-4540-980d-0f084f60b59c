const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../middleware/auth');
const crypto = require('crypto');
const Invoice = require('../models/invoice');
const paystackService = require('../services/paystackService');
const billingController = require('../controllers/billingController');

// GET billing preferences page
router.get('/preferences', ensureAuthenticated, billingController.getBillingPreferences);

// POST update billing preferences
router.post('/preferences', ensureAuthenticated, billingController.updateBillingPreferences);

// GET statement for download (PDF or Excel)
router.get('/statement/download/:format', ensureAuthenticated, billingController.getStatement);

// Initialize Paystack payment
router.post('/payment/initialize', ensureAuthenticated, async (req, res) => {
  try {
    const { invoiceId } = req.body;
    const userId = req.user._id;
    
    // The callback URL where user will be redirected after payment
    const callbackUrl = `${req.protocol}://${req.get('host')}/billing/payment/callback`;
    
    // Process the payment and get authorization URL
    const authorizationUrl = await paystackService.processInvoicePayment(
      invoiceId, 
      userId, 
      callbackUrl
    );
    
    // Redirect the user to Paystack payment page
    return res.json({ url: authorizationUrl });
  } catch (error) {
    console.error('Error initializing payment:', error);
    req.flash('error', 'Unable to initialize payment. Please try again.');
    return res.status(500).json({ error: 'Payment initialization failed' });
  }
});

// Handle Paystack callback
router.get('/payment/callback', ensureAuthenticated, async (req, res) => {
  try {
    const { reference } = req.query;
    
    if (!reference) {
      req.flash('error', 'Payment reference not found');
      return res.redirect('/billing/preferences');
    }
    
    // Verify the transaction
    const verification = await paystackService.verifyTransaction(reference);
    
    if (verification.status === 'success') {
      req.flash('success', 'Payment processed successfully!');
    } else {
      req.flash('error', 'Payment was not successful. Please try again.');
    }
    
    // Redirect to billing preferences page
    return res.redirect('/billing/preferences');
  } catch (error) {
    console.error('Error processing payment callback:', error);
    req.flash('error', 'Error processing payment. Please contact support.');
    return res.redirect('/billing/preferences');
  }
});

// Handle Paystack webhook
router.post('/payment/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    // Verify that the request is from Paystack
    const hash = crypto.createHmac('sha512', process.env.PAYSTACK_SECRET_KEY)
                      .update(JSON.stringify(req.body))
                      .digest('hex');
    
    if (hash !== req.headers['x-paystack-signature']) {
      return res.status(400).send('Invalid signature');
    }
    
    // Process the webhook event
    await paystackService.handleWebhook(req.body);
    
    // Acknowledge receipt of webhook
    return res.status(200).send('Webhook received');
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).send('Webhook processing failed');
  }
});

// Get latest pending invoice for current company
router.get('/latest-invoice', ensureAuthenticated, async (req, res) => {
  try {
    const companyId = req.user.currentCompany._id;
    
    // Find the latest pending invoice for the current company
    const invoice = await Invoice.findOne({
      company: companyId,
      status: 'pending'
    }).sort({ date: -1 });
    
    if (!invoice) {
      return res.status(404).json({ message: 'No pending invoice found' });
    }
    
    return res.json({ invoice });
  } catch (error) {
    console.error('Error fetching latest invoice:', error);
    return res.status(500).json({ error: 'Failed to fetch invoice' });
  }
}); 