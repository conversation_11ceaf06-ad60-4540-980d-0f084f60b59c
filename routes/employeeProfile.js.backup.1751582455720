const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const Company = require("../models/Company");
const PayFrequency = require("../models/PayFrequency");
const PayComponent = require("../models/payComponent");
const PayrollPeriod = require("../models/PayrollPeriod");
const payrollCalculations = require("../utils/payrollCalculations");
const Beneficiaries = require("../models/beneficiaries");
const CustomItem = require("../models/CustomItem");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const moment = require("moment-timezone");
const { formatDateForMongoDB, getCurrentOrNextLastDayOfMonth } = require("../utils/dateUtils");
const cookieParser = require("cookie-parser");
const csrf = require("csurf");

// Configure middleware
router.use(cookieParser());
router.use(express.urlencoded({ extended: true }));
router.use(express.json());

// CSRF Protection setup
const csrfProtection = csrf({
  cookie: {
    key: "_csrf",
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  },
});

// Apply CSRF protection to all routes
router.use(csrfProtection);

// Make CSRF token available to all views
router.use((req, res, next) => {
  res.locals.csrfToken = req.csrfToken();
  next();
});

// Middleware to ensure company code is available
router.use("/:companyCode/*", (req, res, next) => {
  req.companyCode = req.params.companyCode;
  next();
});

// Middleware to ensure employee can only access their own profile
const ensureOwnProfile = async (req, res, next) => {
  try {
    const { employeeId } = req.params;

    // Check if the logged-in user is the employee
    if (
      req.user.role === "employee" &&
      req.user._id.toString() !== employeeId
    ) {
      return res.status(403).send("Unauthorized access to employee profile");
    }

    next();
  } catch (error) {
    console.error("Profile access error:", error);
    res.status(500).send("Server error");
  }
};

// Apply authentication middleware
router.use(ensureAuthenticated);

// GET route for regular inputs page
router.get(
  "/:companyCode/employeeProfile/:employeeId/regularInputs",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
      }).sort({ endDate: -1 });

      res.render("regularInputs", {
        employee,
        company,
        currentPeriod,
        req,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading regular inputs page:", error);
      req.flash("error", "Error loading regular inputs page");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// API route to fetch regular inputs data including custom income items
router.get(
  "/:employeeId/regularInputs",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        return res.status(404).json({ error: "Employee not found" });
      }

      // Fetch existing regular inputs from payroll
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(getCurrentOrNextLastDayOfMonth()),
      });

      // Fetch all custom income items for the company (for Regular Inputs display)
      const customIncomeItems = await CustomItem.find({
        companyCode: employee.company.companyCode,
        type: 'income'
      }).lean();

      // Combine regular inputs and custom income items
      const regularInputsData = [];

      // Add existing payroll data (if any)
      if (payroll) {
        // Add logic here to extract existing regular inputs from payroll
        // This would depend on how regular inputs are stored in the payroll model
      }

      // Add all custom income items (they will appear in Regular Inputs card)
      customIncomeItems.forEach(item => {
        // Check if this item is already configured in payroll
        const configuredInPayroll = payroll && payroll.customIncomeItems &&
          payroll.customIncomeItems.some(payrollItem =>
            payrollItem.customIncomeId.toString() === item._id.toString()
          );

        regularInputsData.push({
          customIncomeId: item._id,
          name: item.name,
          inputType: item.inputType,
          type: 'custom-income',
          isConfigured: configuredInPayroll, // Flag to indicate if already configured
          ...item
        });
      });

      res.json(regularInputsData);
    } catch (error) {
      console.error("Error fetching regular inputs:", error);
      return res.status(500).json({ error: "Error fetching regular inputs" });
    }
  }
);

// Route to handle custom income item form
router.get(
  "/:companyCode/employeeProfile/:employeeId/custom-income/:customIncomeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, customIncomeId, companyCode } = req.params;

      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const customIncomeItem = await CustomItem.findById(customIncomeId);
      if (!customIncomeItem) {
        req.flash("error", "Custom income item not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}/regularInputs`);
      }

      // Get current payroll period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
      }).sort({ endDate: -1 });

      // Check if this is edit mode (accessed from employeeProfile placeholders)
      const isEditMode = req.query.mode === 'edit';

      res.render("customIncomeForm", {
        employee,
        company,
        customIncomeItem,
        currentPeriod,
        isEditMode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading custom income form:", error);
      req.flash("error", "Error loading custom income form");
      res.redirect(`/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/regularInputs`);
    }
  }
);

// POST route to handle custom income form submission
router.post(
  "/:companyCode/employeeProfile/:employeeId/custom-income/:customIncomeId",
  async (req, res) => {
    try {
      console.log("=== CUSTOM INCOME FORM SUBMISSION SERVER DEBUG ===");
      console.log("Timestamp:", new Date().toISOString());
      console.log("Request method:", req.method);
      console.log("Request URL:", req.url);
      console.log("Request originalUrl:", req.originalUrl);
      console.log("Request path:", req.path);
      console.log("Request query:", req.query);
      console.log("Request params:", req.params);
      console.log("Request headers:", JSON.stringify(req.headers, null, 2));
      console.log("Request content-type:", req.get('Content-Type'));
      console.log("Request content-length:", req.get('Content-Length'));
      console.log("Request body type:", typeof req.body);
      console.log("Request body:", JSON.stringify(req.body, null, 2));
      console.log("Request body keys:", Object.keys(req.body || {}));
      console.log("Request is JSON:", req.is('json'));
      console.log("Request accepts:", req.get('Accept'));

      const { employeeId, customIncomeId, companyCode } = req.params;
      const formData = req.body;

      console.log("=== EXTRACTED PARAMETERS ===");
      console.log("Employee ID:", employeeId);
      console.log("Custom Income ID:", customIncomeId);
      console.log("Company Code:", companyCode);
      console.log("Form Data:", JSON.stringify(formData, null, 2));

      // Validate all ObjectIds before proceeding
      console.log("=== VALIDATION STEP ===");
      const mongoose = require('mongoose');
      console.log("Validating Employee ID:", employeeId);
      const isValidEmployeeId = mongoose.Types.ObjectId.isValid(employeeId);
      console.log("Employee ID valid:", isValidEmployeeId);

      if (!isValidEmployeeId) {
        console.log("VALIDATION FAILED: Invalid employee ID format");
        const errorResponse = {
          success: false,
          message: "Invalid employee ID format"
        };
        console.log("Sending error response:", errorResponse);
        return res.status(400).json(errorResponse);
      }

      console.log("Validating Custom Income ID:", customIncomeId);
      const isValidCustomIncomeId = mongoose.Types.ObjectId.isValid(customIncomeId);
      console.log("Custom Income ID valid:", isValidCustomIncomeId);

      if (!isValidCustomIncomeId) {
        console.log("VALIDATION FAILED: Invalid custom income item ID format");
        const errorResponse = {
          success: false,
          message: "Invalid custom income item ID format"
        };
        console.log("Sending error response:", errorResponse);
        return res.status(400).json(errorResponse);
      }

      console.log("All ObjectId validations passed");

      console.log("=== DATABASE OPERATIONS ===");
      console.log("Fetching employee with ID:", employeeId);
      const employee = await Employee.findById(employeeId).populate("company");
      console.log("Employee found:", !!employee);
      if (employee) {
        console.log("Employee details:", {
          id: employee._id,
          name: employee.firstName + " " + employee.lastName,
          company: employee.company ? employee.company.companyCode : "No company"
        });
      }

      if (!employee) {
        console.log("DATABASE ERROR: Employee not found");
        const errorResponse = { success: false, message: "Employee not found" };
        console.log("Sending error response:", errorResponse);
        return res.status(404).json(errorResponse);
      }

      console.log("Fetching custom income item with ID:", customIncomeId);
      const customIncomeItem = await CustomItem.findById(customIncomeId);
      console.log("Custom income item found:", !!customIncomeItem);
      if (customIncomeItem) {
        console.log("Custom income item details:", {
          id: customIncomeItem._id,
          name: customIncomeItem.name,
          type: customIncomeItem.type,
          inputType: customIncomeItem.inputType
        });
      }

      if (!customIncomeItem) {
        console.log("DATABASE ERROR: Custom income item not found");
        const errorResponse = { success: false, message: "Custom income item not found" };
        console.log("Sending error response:", errorResponse);
        return res.status(404).json(errorResponse);
      }

      // Get or create current payroll
      console.log("=== PAYROLL OPERATIONS ===");
      const currentMonth = formatDateForMongoDB(getCurrentOrNextLastDayOfMonth());
      console.log("Current month for payroll:", currentMonth);

      console.log("Searching for existing payroll...");
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: currentMonth,
      });

      console.log("Existing payroll found:", !!payroll);
      if (payroll) {
        console.log("Existing payroll ID:", payroll._id);
        console.log("Existing custom income items count:", payroll.customIncomeItems ? payroll.customIncomeItems.length : 0);
      }

      if (!payroll) {
        console.log("Creating new payroll record...");
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: currentMonth,
          customIncomeItems: []
        });
        console.log("New payroll created (not saved yet)");
      }

      // Initialize customIncomeItems array if it doesn't exist
      if (!payroll.customIncomeItems) {
        console.log("Initializing customIncomeItems array");
        payroll.customIncomeItems = [];
      }

      console.log("Payroll customIncomeItems array length:", payroll.customIncomeItems.length);

      // Remove existing entry for this custom income item
      console.log("=== DATA PROCESSING ===");
      console.log("Removing existing entries for custom income ID:", customIncomeId);
      const originalLength = payroll.customIncomeItems.length;
      payroll.customIncomeItems = payroll.customIncomeItems.filter(
        item => item.customIncomeId.toString() !== customIncomeId
      );
      const newLength = payroll.customIncomeItems.length;
      console.log(`Removed ${originalLength - newLength} existing entries`);

      // Filter out CSRF token and other non-data fields from formData
      console.log("Filtering form data...");
      console.log("Raw form data keys:", Object.keys(formData));
      const { _csrf, customIncomeId: formCustomIncomeId, ...cleanFormData } = formData;

      console.log("Clean form data:", JSON.stringify(cleanFormData, null, 2));
      console.log("Custom income item details:", {
        name: customIncomeItem.name,
        inputType: customIncomeItem.inputType,
        _id: customIncomeItem._id
      });

      // Add new entry with only essential fields to avoid validation issues
      console.log("=== ENTRY CREATION ===");
      console.log("Creating custom income entry with base fields...");
      const customIncomeEntry = {
        customIncomeId: new mongoose.Types.ObjectId(customIncomeId),
        name: customIncomeItem.name,
        inputType: customIncomeItem.inputType
      };
      console.log("Base entry created:", customIncomeEntry);

      // Only add specific fields that we know are valid for custom income
      console.log("Processing optional fields...");
      if (cleanFormData.amount && !isNaN(parseFloat(cleanFormData.amount))) {
        console.log("Adding amount:", cleanFormData.amount);
        customIncomeEntry.amount = parseFloat(cleanFormData.amount);
      }
      if (cleanFormData.percentage && !isNaN(parseFloat(cleanFormData.percentage))) {
        console.log("Adding percentage:", cleanFormData.percentage);
        customIncomeEntry.percentage = parseFloat(cleanFormData.percentage);
      }
      if (cleanFormData.quantity && !isNaN(parseFloat(cleanFormData.quantity))) {
        console.log("Adding quantity:", cleanFormData.quantity);
        customIncomeEntry.quantity = parseFloat(cleanFormData.quantity);
      }
      if (cleanFormData.customRate && !isNaN(parseFloat(cleanFormData.customRate))) {
        console.log("Adding customRate:", cleanFormData.customRate);
        customIncomeEntry.customRate = parseFloat(cleanFormData.customRate);
      }
      if (cleanFormData.monthlyAmount && !isNaN(parseFloat(cleanFormData.monthlyAmount))) {
        console.log("Adding monthlyAmount:", cleanFormData.monthlyAmount);
        customIncomeEntry.monthlyAmount = parseFloat(cleanFormData.monthlyAmount);
      }
      if (cleanFormData.enableProRata === 'on' || cleanFormData.enableProRata === true) {
        console.log("Adding enableProRata: true");
        customIncomeEntry.enableProRata = true;
      }
      if (cleanFormData.differentRateForEveryEmployee === 'on' || cleanFormData.differentRateForEveryEmployee === true) {
        console.log("Adding differentRateForEveryEmployee: true");
        customIncomeEntry.differentRateForEveryEmployee = true;
      }
      if (cleanFormData.incomeItems && Array.isArray(cleanFormData.incomeItems)) {
        console.log("Adding incomeItems:", cleanFormData.incomeItems);
        customIncomeEntry.incomeItems = cleanFormData.incomeItems;
      }

      console.log("Final custom income entry:", JSON.stringify(customIncomeEntry, null, 2));

      console.log("Adding entry to payroll customIncomeItems array...");
      payroll.customIncomeItems.push(customIncomeEntry);
      console.log("Entry added. New array length:", payroll.customIncomeItems.length);

      console.log("=== SAVING TO DATABASE ===");
      console.log("Attempting to save payroll...");
      await payroll.save();
      console.log("Payroll saved successfully!");

      // Always return JSON for AJAX requests, regardless of input type
      console.log("=== RESPONSE GENERATION ===");
      console.log("Checking input type for response type...");
      console.log("Input type:", customIncomeItem.inputType);
      console.log("Request Content-Type:", req.get('Content-Type'));
      console.log("Request accepts JSON:", req.accepts('json'));
      console.log("Request is AJAX:", req.xhr);

      // Check if this is an AJAX request (JSON content-type or accepts JSON)
      const isAjaxRequest = req.get('Content-Type') === 'application/json' || req.xhr || req.accepts('json');
      console.log("Is AJAX request:", isAjaxRequest);

      if (customIncomeItem.inputType === 'customRateQuantity' && !isAjaxRequest) {
        console.log("CustomRateQuantity type detected with non-AJAX request - using redirect response");
        req.flash("success", "Custom income item configured successfully");
        console.log("Flash message set, redirecting to:", `/clients/${companyCode}/employeeProfile/${employeeId}`);
        return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
      }

      console.log("Using JSON response (AJAX request or non-customRateQuantity type)");
      const successResponse = {
        success: true,
        message: "Custom income item saved successfully",
        redirectUrl: customIncomeItem.inputType === 'customRateQuantity'
          ? `/clients/${companyCode}/employeeProfile/${employeeId}`
          : `/clients/${companyCode}/employeeProfile/${employeeId}/regularInputs`
      };
      console.log("Sending success response:", successResponse);
      res.json(successResponse);
    } catch (error) {
      console.error("=== ERROR OCCURRED ===");
      console.error("Error timestamp:", new Date().toISOString());
      console.error("Error saving custom income item:", error);
      console.error("Error type:", error.constructor.name);
      console.error("Error details:", {
        message: error.message,
        name: error.name,
        code: error.code,
        stack: error.stack
      });

      // Handle specific validation errors
      if (error.name === 'ValidationError') {
        console.error("VALIDATION ERROR DETECTED");
        const validationErrors = Object.values(error.errors).map(err => ({
          field: err.path,
          message: err.message,
          value: err.value
        }));
        console.error("Validation errors:", validationErrors);
        const errorResponse = {
          success: false,
          message: `Validation error: ${validationErrors.map(e => `${e.field}: ${e.message}`).join(', ')}`
        };
        console.error("Sending validation error response:", errorResponse);
        return res.status(400).json(errorResponse);
      }

      // Handle MongoDB cast errors (like invalid ObjectId)
      if (error.name === 'CastError') {
        console.error("CAST ERROR DETECTED");
        console.error("Cast error details:", {
          path: error.path,
          value: error.value,
          kind: error.kind
        });
        const errorResponse = {
          success: false,
          message: `Invalid ${error.path}: ${error.message}`
        };
        console.error("Sending cast error response:", errorResponse);
        return res.status(400).json(errorResponse);
      }

      // Always return JSON response for this route
      console.error("GENERIC ERROR - Sending 500 response");
      const errorResponse = {
        success: false,
        message: error.message || "Error saving custom income item",
        error: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };
      console.error("Final error response:", errorResponse);
      console.error("Response status: 500");
      console.error("Response headers will include Content-Type: application/json");
      res.status(500).json(errorResponse);
    }
  }
);

// GET employee profile page with CSRF token
router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureOwnProfile,
  async (req, res) => {
    try {
      console.log("=== Employee Profile Route Debug ===");
      const { employeeId, companyCode } = req.params;
      console.log("Route Parameters:", { employeeId, companyCode });

      // CRITICAL FIX: Clear any cached payroll data from session to ensure fresh data
      if (req.session && req.session.payrollCache) {
        delete req.session.payrollCache;
      }

      const employee = await Employee.findById(employeeId);
      console.log("Found Employee:", employee ? "Yes" : "No");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Calculate employee's age
      const employeeAge = employee.calculateAge();
      console.log("Employee Age:", employeeAge);

      // Get current month's end date
      const currentDate = new Date();
      const endOfMonth = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        0
      );
      const startOfMonth = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        1
      );
      const thisMonth = endOfMonth;

      // Get company data first (needed for period queries)
      const company = await Company.findOne({ companyCode });
      console.log("Found Company:", company ? "Yes" : "No");

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // FIXED: Determine current period based on user selection first, then default to unfinalized
      let currentPeriod;
      let selectedMonth;

      if (req.query.selectedMonth) {
        // PRIORITY FIX: If a specific month is selected, use that period (respects user choice)
        // Parse the date string using South African timezone to prevent timezone shift issues
        selectedMonth = moment.tz(req.query.selectedMonth, 'Africa/Johannesburg').endOf('day').toDate();
        console.log("🎯 User selected specific period:", selectedMonth.toISOString());

        // Find the exact period for the selected date
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: new Date(selectedMonth.getTime() - 24 * 60 * 60 * 1000), // 1 day before
            $lte: new Date(selectedMonth.getTime() + 24 * 60 * 60 * 1000)  // 1 day after
          }
        });

        if (!currentPeriod) {
          // Try exact match if range search fails
          currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            company: company._id,
            endDate: selectedMonth,
          });
        }

        console.log("✅ Found selected period:", currentPeriod ? {
          periodId: currentPeriod._id,
          endDate: currentPeriod.endDate.toISOString(),
          basicSalary: currentPeriod.basicSalary,
          isFinalized: currentPeriod.isFinalized
        } : "No period found for selected date");

      } else {
        // DEFAULT: Only use most recent unfinalized period when no specific selection
        console.log("🎯 No specific period selected, finding most recent unfinalized period as default...");
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          isFinalized: false,
        }).sort({ endDate: -1 });

        if (currentPeriod) {
          selectedMonth = currentPeriod.endDate;
          console.log("✅ Found unfinalized period:", {
            periodId: currentPeriod._id,
            endDate: currentPeriod.endDate.toISOString(),
            basicSalary: currentPeriod.basicSalary,
            isFinalized: currentPeriod.isFinalized
          });
        } else {
          // Fallback: Use current month if no unfinalized periods exist
          console.log("⚠️ No unfinalized periods found, falling back to current month");
          selectedMonth = endOfMonth;
          currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            company: company._id,
            endDate: selectedMonth,
          });
        }
      }

      console.log("Date Range:", {
        currentDate: currentDate.toISOString(),
        startOfMonth: startOfMonth.toISOString(),
        endOfMonth: endOfMonth.toISOString(),
        selectedMonth: selectedMonth.toISOString(),
        thisMonth: thisMonth.toISOString(),
        periodSelectionMethod: req.query.selectedMonth ? 'query_parameter' : 'most_recent_unfinalized'
      });

      if (!currentPeriod) {
        console.log("No period found for selected month");
        return res
          .status(404)
          .send("No payroll period found for the selected month");
      }

      // Calculate payroll totals
      const calculations = await PayrollService.calculatePayrollTotals(
        employeeId,
        selectedMonth,
        company._id
      );

      // Format calculations with explicit number conversion
      const formattedCalculations = {
        ...calculations,
        totalIncome: Number(
          calculations.totalIncome || calculations.basicSalary || 0
        ),
        paye: Number(calculations.totalPAYE || 0),
        uif: Number(calculations.totalUIF || 0),
        totalDeductions: Number(calculations.totalDeductions || 0),
        netPay: Number(calculations.nettPay || 0),
      };

      // Get payroll data for the selected month (FIXED: Use selectedMonth instead of current month)
      console.log("=== PAYROLL DATA FETCH ===");
      console.log("Selected month for payroll lookup:", selectedMonth);
      console.log("Selected month ISO:", selectedMonth.toISOString());

      // CRITICAL FIX: Use selectedMonth for historical payroll data instead of current month
      const payrollMonth = formatDateForMongoDB(selectedMonth);
      console.log("Formatted payroll month:", payrollMonth);
      console.log("Formatted payroll month ISO:", payrollMonth.toISOString());

      // Try to find payroll for the specific historical month
      let payroll = await Payroll.findOne({
        employee: employeeId,
        month: payrollMonth, // Use selectedMonth instead of getCurrentOrNextLastDayOfMonth()
      });

      console.log("Payroll found with formatted selected month:", !!payroll);

      // If not found with formatted month, try with selected month directly
      if (!payroll) {
        console.log("Trying with selected month directly...");
        payroll = await Payroll.findOne({
          employee: employeeId,
          month: selectedMonth,
        });
        console.log("Payroll found with selected month:", !!payroll);
      }

      if (payroll) {
        console.log("Found payroll ID:", payroll._id);
        console.log("Payroll month:", payroll.month);
        console.log("Custom income items count:", payroll.customIncomeItems ? payroll.customIncomeItems.length : 0);
        if (payroll.customIncomeItems && payroll.customIncomeItems.length > 0) {
          console.log("=== DETAILED CUSTOM INCOME ITEMS ANALYSIS ===");
          payroll.customIncomeItems.forEach((item, index) => {
            console.log(`Item ${index + 1}:`, {
              customIncomeId: item.customIncomeId,
              customIncomeIdType: typeof item.customIncomeId,
              name: item.name,
              nameType: typeof item.name,
              inputType: item.inputType,
              inputTypeType: typeof item.inputType,
              quantity: item.quantity,
              customRate: item.customRate,
              calculatedAmount: item.calculatedAmount,
              fullItem: JSON.stringify(item, null, 2)
            });

            // Test the filtering conditions used in employeeProfile.ejs
            const passesInputTypeCheck = item.inputType === 'customRateQuantity';
            const passesCustomIncomeIdCheck = !!item.customIncomeId;
            const passesNameCheck = !!item.name;
            const passesTestCheck = item.name ? !item.name.toLowerCase().includes('test') : false;

            console.log(`Item ${index + 1} filter results:`, {
              inputType: `${item.inputType} === 'customRateQuantity' = ${passesInputTypeCheck}`,
              customIncomeId: `exists = ${passesCustomIncomeIdCheck}`,
              name: `exists = ${passesNameCheck}`,
              notTest: `not test = ${passesTestCheck}`,
              overallPass: passesInputTypeCheck && passesCustomIncomeIdCheck && passesNameCheck && passesTestCheck
            });
          });

          // Apply the same filter as the template
          const filteredItems = payroll.customIncomeItems.filter(function(item) {
            return item.inputType === 'customRateQuantity' &&
                   item.customIncomeId &&
                   item.name &&
                   !item.name.toLowerCase().includes('test');
          });

          console.log("Filtered items count (should match template):", filteredItems.length);
          console.log("Filtered items:", filteredItems.map(item => ({
            id: item.customIncomeId,
            name: item.name,
            inputType: item.inputType
          })));
        }
      } else {
        console.log("No payroll found for either month format");
      }

      // Fetch all payroll periods for the employee to display in the modal
      console.log("=== FETCHING PAYROLL PERIODS ===");
      const payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ endDate: -1 }); // Sort by end date descending (newest first)

      console.log("Found payroll periods:", payPeriods.length);
      if (payPeriods.length > 0) {
        console.log("Sample period data:", {
          id: payPeriods[0]._id,
          startDate: payPeriods[0].startDate,
          endDate: payPeriods[0].endDate,
          basicSalary: payPeriods[0].basicSalary,
          isFinalized: payPeriods[0].isFinalized
        });

        // Debug: Log all periods' basicSalary values
        console.log("All periods basicSalary values:");
        payPeriods.forEach((period, index) => {
          console.log(`Period ${index + 1}: ID=${period._id}, basicSalary=${period.basicSalary}, isFinalized=${period.isFinalized}, endDate=${period.endDate}`);
        });
      }

      // Debug: Check if currentPeriod is in payPeriods and compare basicSalary values
      if (currentPeriod && payPeriods.length > 0) {
        const matchingPeriod = payPeriods.find(p => p._id.toString() === currentPeriod._id.toString());
        if (matchingPeriod) {
          console.log("Current period found in payPeriods:", {
            currentPeriodSalary: currentPeriod.basicSalary,
            payPeriodsMatchingSalary: matchingPeriod.basicSalary,
            areEqual: currentPeriod.basicSalary === matchingPeriod.basicSalary
          });
        }
      }

      // CRITICAL FIX: Ensure each period has its own independent basicSalary value
      // Convert to plain objects to prevent any reference issues
      const sanitizedPayPeriods = payPeriods.map(period => ({
        _id: period._id,
        startDate: period.startDate,
        endDate: period.endDate,
        basicSalary: Number(period.basicSalary), // Ensure it's a number
        isFinalized: period.isFinalized,
        status: period.status,
        frequency: period.frequency
      }));

      console.log("Sanitized periods basicSalary values:");
      sanitizedPayPeriods.forEach((period, index) => {
        console.log(`Sanitized Period ${index + 1}: ID=${period._id}, basicSalary=${period.basicSalary}, isFinalized=${period.isFinalized}`);
      });

      // CRITICAL DEBUG: Verify currentPeriod basicSalary preservation
      console.log("=== CURRENT PERIOD BASIC SALARY VERIFICATION ===");
      console.log("Original currentPeriod.basicSalary:", currentPeriod.basicSalary);
      console.log("Calculations basicSalary:", formattedCalculations.basicSalary);
      console.log("Payroll basicSalary:", payroll?.basicSalary);
      console.log("Final currentPeriod basicSalary will be:", Number(currentPeriod.basicSalary || 0));

      // CRITICAL FIX: Preserve original PayrollPeriod basicSalary value
      // Don't let calculations override the historical basicSalary from PayrollPeriod
      const currentPeriodData = currentPeriod.toObject();

      // 🚨🚨🚨 VERIFICATION TEST MARKER - ROUTE HANDLER EXECUTION 🚨🚨🚨
      console.log("🔥🔥🔥 VERIFICATION: EMPLOYEE PROFILE ROUTE HANDLER IS EXECUTING! 🔥🔥🔥");
      console.log("🎯 TEST MARKER: Period Selection Fix Route Handler - TIMESTAMP:", new Date().toISOString());
      console.log("🎯 TEST MARKER: Employee ID:", employeeId);
      console.log("🎯 TEST MARKER: Selected Month:", selectedMonth.toISOString());
      console.log("🎯 TEST MARKER: Period Selection Method:", req.query.selectedMonth ? 'query_parameter' : 'most_recent_unfinalized');
      console.log("🎯 TEST MARKER: Current Period Finalized:", currentPeriod.isFinalized);

      // DIRECT FIX: Use basicSalary directly from PayrollPeriod model
      // This bypasses all complex calculations and transformations
      // currentPeriod already contains the correct PayrollPeriod for the selected month
      const basicSalary = Number(currentPeriod.basicSalary || 0);

      console.log("=== DIRECT BASIC SALARY RETRIEVAL ===");
      console.log("PayrollPeriod ID:", currentPeriod._id);
      console.log("PayrollPeriod endDate:", currentPeriod.endDate);
      console.log("PayrollPeriod basicSalary (direct):", currentPeriod.basicSalary);
      console.log("Final basicSalary for template:", basicSalary);
      console.log("Selected month from query:", req.query.selectedMonth);
      console.log("Period selection method:", req.query.selectedMonth ? 'USER_SELECTED' : 'DEFAULT_UNFINALIZED');
      console.log("🚨 VERIFICATION: This route handler is definitely executing! 🚨");

      // Note: Removed complex salary resolution debug logging since we're using direct approach
      // Use calculations for other values, but basicSalary comes directly from PayrollPeriod
      const totalIncome = formattedCalculations.totalIncome || basicSalary;
      const isProrated = formattedCalculations.basicSalary?.prorated !== formattedCalculations.basicSalary?.full;

      // Create payrollDetails object for calculator compatibility
      const payrollDetails = {
        basicSalary: basicSalary, // Direct from PayrollPeriod
        totalIncome: totalIncome,
        totalDeductions: formattedCalculations.totalDeductions || 0,
        proratedAmount: formattedCalculations.basicSalary?.prorated || basicSalary,
      };

      // DIRECT FIX: Ensure payroll object has correct historical basicSalary from PayrollPeriod
      const correctedPayroll = payroll ? {
        ...payroll.toObject(),
        basicSalary: basicSalary // Use direct value from PayrollPeriod
      } : null;

      const templateData = {
        employee,
        company,
        companyCode,
        thisMonth,
        employeeAge,
        payroll: correctedPayroll, // Use corrected payroll with historical basicSalary
        payPeriods: sanitizedPayPeriods, // Use sanitized data to prevent reference issues
        dobFormatted: employee.dob
          ? moment(employee.dob).format("YYYY-MM-DD")
          : null,
        currentPeriod: {
          ...currentPeriodData,
          // DIRECT FIX: Use basicSalary directly from PayrollPeriod model
          // No transformations or calculations - just the raw database value
          basicSalary: basicSalary, // Direct value from PayrollPeriod.basicSalary
          calculations: formattedCalculations,
        },
        // CRITICAL FIX: Add missing calculator variables
        basicSalary: basicSalary,
        totalIncome: totalIncome,
        isProrated: isProrated,
        payrollDetails: payrollDetails,
        csrfToken: req.csrfToken(),
        // 🚨 VERIFICATION TEST MARKER - This will appear on the webpage 🚨
        testMarker: "🔥 PERIOD SELECTION FIX ACTIVE - DEFAULTS TO UNFINALIZED PERIOD 🔥",
        testTimestamp: new Date().toISOString(),
        debugInfo: {
          periodSelectionMethod: req.query.selectedMonth ? 'query_parameter' : 'most_recent_unfinalized',
          salarySource: 'direct_database_query_current',
          selectedMonth: req.query.selectedMonth || 'none',
          viewFinalized: currentPeriod.isFinalized,
          periodId: currentPeriod._id,
          periodBasicSalary: currentPeriod.basicSalary,
          directDatabaseValue: currentPeriod.basicSalary,
          usingDirectDatabase: true
        },
      };

      // DIRECT APPROACH VERIFICATION: Confirm all salary values are from PayrollPeriod
      console.log("=== DIRECT BASIC SALARY VERIFICATION ===");
      console.log("Template Data Summary:", {
        basicSalary: templateData.basicSalary,
        currentPeriodBasicSalary: templateData.currentPeriod?.basicSalary,
        payrollBasicSalary: templateData.payroll?.basicSalary,
        originalPayrollPeriodSalary: currentPeriod.basicSalary,
        selectedMonth: selectedMonth.toISOString(),
        timestamp: new Date().toISOString()
      });

      // VERIFICATION: All salary values should match the original PayrollPeriod value
      const originalValue = currentPeriod.basicSalary;
      if (templateData.basicSalary === originalValue &&
          templateData.currentPeriod?.basicSalary === originalValue &&
          templateData.payroll?.basicSalary === originalValue) {
        console.log("✅ SUCCESS: All salary values match PayrollPeriod.basicSalary");
      } else {
        console.warn("⚠️  MISMATCH: Some values don't match PayrollPeriod.basicSalary:", originalValue);
      }

      // CRITICAL FIX: Prevent browser caching to ensure updated data is displayed
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Last-Modified': new Date().toUTCString()
      });

      res.render("employeeProfile", templateData);
    } catch (error) {
      console.error("Error in employee profile route:", error);
      res.status(500).send("Server error");
    }
  }
);

// DELETE employee with CSRF protection
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res
          .status(404)
          .json({ success: false, message: "Company not found" });
      }

      // Validate employee exists and belongs to company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        return res
          .status(404)
          .json({ success: false, message: "Employee not found" });
      }

      // Delete employee
      await Employee.findByIdAndDelete(employeeId);

      res.json({
        success: true,
        message: "Employee deleted successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Delete employee error:", error);
      res.status(500).json({
        success: false,
        message: "Error deleting employee",
      });
    }
  }
);

// Update pro-rata percentage
router.post(
  "/:employeeId/updateProrate",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { periodId, proRataPercentage } = req.body;
      const { employeeId } = req.params;

      // Validate inputs
      if (!periodId || typeof proRataPercentage !== "number") {
        return res
          .status(400)
          .json({ error: "Missing or invalid required fields" });
      }

      // Find the period and employee
      const period = await PayrollPeriod.findById(periodId);
      const employee = await Employee.findById(employeeId);

      if (!period || !employee || period.employee.toString() !== employeeId) {
        return res.status(404).json({ error: "Period or employee not found" });
      }

      // Store original calculations
      const originalCalculations = { ...period.calculations };

      // Update pro-rata percentage and mark as manually adjusted
      period.calculations = {
        ...period.calculations,
        proRataPercentage: parseFloat(proRataPercentage),
        isManuallyAdjusted: true,
        manualAdjustmentDate: new Date(),
      };

      // Recalculate salary based on new percentage
      const fullSalary = period.calculations.basicSalary || 0;
      const proratedSalary = Math.round((fullSalary * proRataPercentage) / 100);

      // Update calculations
      period.calculations.grossIncome = proratedSalary;

      // Recalculate PAYE and other deductions
      const payrollService = require("../services/PayrollService");
      const updatedTotals = await payrollService.calculatePayrollTotals(
        employeeId,
        period.endDate,
        employee.company
      );

      // Update period with new calculations
      period.calculations = {
        ...period.calculations,
        ...updatedTotals,
        proRataPercentage: parseFloat(proRataPercentage),
        isManuallyAdjusted: true,
        manualAdjustmentDate: new Date(),
        previousCalculations: originalCalculations,
      };

      // Save the changes
      await period.save();

      // Update corresponding payroll record
      await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: period.endDate,
        },
        {
          $set: {
            basicSalary: proratedSalary,
            grossPay: updatedTotals.grossIncome,
            netPay: updatedTotals.netPay,
            totalDeductions: updatedTotals.deductions.total,
          },
        }
      );

      res.json({
        success: true,
        message: "Pro-rata percentage updated successfully",
        calculations: period.calculations,
      });
    } catch (error) {
      console.error("Error updating pro-rata percentage:", error);
      res.status(500).json({ error: "Failed to update pro-rata percentage" });
    }
  }
);

//_______________________

//________________________
router.get(
  "/:employeeId/edit/:componentName",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, componentName } = req.params;
      const { thisMonth, amount } = req.body;
      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: thisMonth,
      });

      if (!payroll) {
        return res.status(404).send("Payroll data not found");
      }

      payroll[componentName] = { amount: parseFloat(amount) };
      await payroll.save();

      res.redirect(`/clients/${req.companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      res.status(500).send("Server Error");
    }
  }
);

//_____________

// GET route to display the edit form
// router.get(
//   "/:companyCode/employeeProfile/:employeeId/edit",
//   ensureAuthenticated,
//   async (req, res) => {
//     try {
//       const { companyCode, employeeId } = req.params;
//       const company = await Company.findOne({ companyCode });
//       const employee = await Employee.findById(employeeId);

//       if (!company || !employee) {
//         req.flash("error", "Company or Employee not found");
//         return res.redirect("/");
//       }

//       res.render("editEmployeeBasicInfo", { company, employee });
//     } catch (error) {
//       console.error("Error rendering edit form:", error);
//       req.flash("error", "An error occurred while loading the edit form");
//       res.redirect(
//         `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
//       );
//     }
//   }
// );

// router.get(
//   "/:companyCode/employeeProfile/:employeeId/edit",
//   ensureAuthenticated,
//   async (req, res) => {
//     try {
//       const { companyCode, employeeId } = req.params;
//       const company = await Company.findOne({ companyCode });
//       const employee = await Employee.findById(employeeId).populate(
//         "payFrequency"
//       );

//       if (!company || !employee) {
//         req.flash("error", "Company or Employee not found");
//         return res.redirect("/");
//       }

//       // Fetch pay frequencies for the company
//       const payFrequencies = await PayFrequency.find({ company: company._id });

//       res.render("editEmployeeBasicInfo", {
//         company,
//         employee,
//         payFrequencies, // Pass payFrequencies to the template
//         moment,
//       });
//     } catch (error) {
//       console.error("Error rendering edit form:", error);
//       req.flash("error", "An error occurred while loading the edit form");
//       res.redirect(
//         `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
//       );
//     }
//   }
// );

// Route to edit payComponents for a specific month
router.post(
  "/:employeeId/edit/:componentName",
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId, componentName } = req.params;
    const { thisMonth, amount } = req.body;

    try {
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }
      if (!employee.company) {
        return res.status(404).send("Company not found for this employee");
      }

      const company = employee.company; // Get the company from the employee

      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: moment(thisMonth).format("YYYY-MM-DD"),
      });

      if (!payroll) {
        // If payroll doesn't exist, create a new one
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: moment(thisMonth).format("YYYY-MM-DD"),
        });
      }
      // Update the payroll data based on the component name
      payroll[componentName] = { amount: parseFloat(amount) };
      payroll.company = company._id; // Ensure the company is included

      await payroll.save();

      res.redirect(`/clients/${req.companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating payroll data:", error);
      res.status(500).send("Error updating payroll data");
    }
  }
);

//_____________________________
router.get(
  "/:employeeId/commission/:month",
  ensureOwnProfile,
  async (req, res) => {
    console.log("Commission route hit");
    console.log("employeeId:", req.params.employeeId);
    console.log("month:", req.params.month);
    console.log("companyCode:", req.query.companyCode);

    const { employeeId, month } = req.params;
    const { companyCode } = req.query;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const adjustedDate = new Date(month);
      adjustedDate.setUTCHours(21, 59, 59, 999);

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: adjustedDate,
      });

      if (!payroll || !payroll.commissionEnabled) {
        req.flash("error", "Commission input not enabled for this month");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }
      let relevantDate =
        latestPayroll && !latestPayroll.finalised
          ? latestPayroll.month
          : latestPayroll
          ? getNextMonthEndDate(latestPayroll.month)
          : getCurrentOrNextLastDayOfMonth();

      const formattedDate = formatDateForDisplay(relevantDate);

      res.render("editCommission", {
        employee,
        payroll,
        month: adjustedDate,
        relevantDate,
        formattedDate: formatDateForDisplay(adjustedDate),
        company,
      });
    } catch (error) {
      console.error("Error rendering commission input page:", error);
      req.flash("error", "Error rendering commission input page");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);
//______________________________
router.get(
  "/:employeeId/regularInputs/new",
  csrfProtection,
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId } = req.params;
    const { regularInputs_name } = req.query;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Fetch all beneficiaries for the company
      let beneficiaries = {
        garnishee: [],
        "maintenance-order": [],
        "medical-aid": [],
        "pension-fund": [],
        "provident-fund": [],
        "retirement-annuity-fund": [],
      };

      try {
        // Fetch all beneficiaries for the company and organize them by type
        const allBeneficiaries = await Beneficiaries.find({
          company: employee.company._id,
        });

        // Sort beneficiaries by type
        allBeneficiaries.forEach((beneficiary) => {
          if (beneficiaries[beneficiary.type]) {
            beneficiaries[beneficiary.type].push({
              _id: beneficiary._id,
              name: beneficiary.name,
              type: beneficiary.type,
              // Add other relevant fields based on your schema
              beneficiaryName: beneficiary.beneficiaryName,
              bankName: beneficiary.bankName,
              schemeName: beneficiary.schemeName,
              mainMemberName: beneficiary.mainMemberName,
              fundName: beneficiary.fundName,
              membershipNumber: beneficiary.membershipNumber,
              policyNumber: beneficiary.policyNumber,
            });
          }
        });
      } catch (error) {
        console.error("Error fetching beneficiaries:", error);
        beneficiaries = {
          garnishee: [],
          "maintenance-order": [],
          "medical-aid": [],
          "pension-fund": [],
          "provident-fund": [],
          "retirement-annuity-fund": [],
        };
      }

      // Rest of your existing code...
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
      }).sort({ month: -1 });

      let relevantDate =
        latestPayroll && !latestPayroll.finalised
          ? latestPayroll.month
          : latestPayroll
          ? getNextMonthEndDate(latestPayroll.month)
          : getCurrentOrNextLastDayOfMonth();

      const formattedDate = formatDateForDisplay(relevantDate);

      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: formatDateForMongoDB(relevantDate),
          ...(latestPayroll && {
            basicSalary: latestPayroll.basicSalary,
            medical: latestPayroll.medical,
          }),
        });
        await payroll.save();
      }

      const viewData = {
        employee,
        payroll,
        relevantDate,
        formattedDate,
        company: employee.company,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        beneficiaries, // This will now contain all beneficiaries organized by type
      };

      // Map regularInputs_name to the correct view name
      const viewNameMap = {
        garnishee: "newGarnishee",
        accommodation_benefit: "newAccommodationBenefitInput",
        income_protection: "newIncomeProtection",
        travel_allowance: "newTravelAllowanceInput",
        maintenance_order: "newMaintenanceOrder",
        pension_fund: "newPensionFund",
        provident_fund: "newProvidentFund",
        retirement_annuity_fund: "newRetirementAnnuityFund",
        union_membership_fee: "newUnionMembershipFee",
        voluntary_tax_over_deduction: "newVoluntaryTaxOverDeduction",
        savings: "newSavings",
        company_car: "newCompanyCar",
        bursaries_and_scholarships: "newBursariesAndScholarships",
        commissions: "newCommissionsInput",
        lossOfIncome: "newLossOfIncomeInput",
        company_car_under_operating_lease: "newCompanyCarUnderOperatingLease",
        "medical-aid": "newMedicalAid",
        tax_directive: "newTaxDirective",
        foreign_service_income: "newForeignServiceIncome",
        employer_loan: "newEmployerLoan",
      };

      const viewName = viewNameMap[regularInputs_name];

      if (viewName) {
        return res.render(viewName, viewData);
      } else {
        req.flash("error", `Unknown component name: ${regularInputs_name}`);
        return res.redirect(
          `/clients/${req.companyCode}/employeeProfile/${employeeId}`
        );
      }
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "An error occurred");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

//_______________________________

// router.post(
//   "/:employeeId/regularInputs/new",
//   csrfProtection,
//   async (req, res) => {
//     const { employeeId } = req.params;
//     const {
//       componentName,
//       amount,
//       employerContribution,
//       employeeHandlesPayment,
//       dontApplyTaxCredits,
//       deemedValue,
//       includesMaintenancePlan,
//       taxablePercentage,
//       members,
//       relevantDate, // Change thisMonth to relevantDate
//       fixedAllowance,
//       fixedAmount,
//       reimbursedExpenses,
//       companyPetrolCard,
//       reimbursedPerKmTravelled,
//       ratePerKm,
//       only20PercentTax,
//       contributionCalculation,
//       fixedContributionEmployee,
//       fixedContributionEmployer,
//       rfiEmployee,
//       rfiEmployer,
//       categoryFactor,
//       beneficiary,
//       employeeContribution,
//       fixedAllowancePaidRegularly,
//       interestRate,
//       regularRepayment,
//       calculateInterestBenefit,
//       directiveNumber,
//       directiveType,
//       foreignServiceTaxExemption,
//       regularDeduction,
//     } = req.body;

//     try {
//       const employee = await Employee.findById(employeeId).populate("company");
//       if (!employee || !employee.company) {
//         req.flash("error", "Employee or company information not found");
//         return res.redirect("/");
//       }

//       const company = employee.company;

//       if (!componentName) {
//         req.flash("error", "Component name is required");
//         return res.redirect(
//           `/clients/${company.companyCode}/employeeProfile/${employeeId}`
//         );
//       }

//       // Parse necessary values
//       const parsedAmount = parseFloat(amount) || 0;
//       const parsedFixedAmount = parseFloat(fixedAmount) || 0;
//       const parsedRatePerKm = parseFloat(ratePerKm) || 0;
//       const parsedFixedContributionEmployee =
//         parseFloat(fixedContributionEmployee) || 0;
//       const parsedFixedContributionEmployer =
//         parseFloat(fixedContributionEmployer) || 0;
//       const parsedRfiEmployee = parseFloat(rfiEmployee) || 0;
//       const parsedRfiEmployer = parseFloat(rfiEmployer) || 0;
//       const parsedMembers = parseInt(members, 10) || 0;

//       // Use the new date utility functions
//       const formattedDate = formatDateForMongoDB(new Date(relevantDate));

//       let payroll = await Payroll.findOne({
//         employee: employeeId,
//         month: formattedDate,
//         company: company._id,
//       });

//       if (!payroll) {
//         payroll = new Payroll({
//           employee: employeeId,
//           month: formattedDate,
//           company: company._id,
//         });
//       }

//       // Update payroll with component-specific data
//       switch (componentName) {
//         case "medical":
//           payroll.medical = {
//             medicalAid: parsedAmount,
//             employerContribution: parseFloat(employerContribution) || 0,
//             members: parsedMembers,
//             employeeHandlesPayment: employeeHandlesPayment === "on",
//             dontApplyTaxCredits: dontApplyTaxCredits === "on",
//           };
//           break;
//         case "companyCar":
//           payroll.companyCar = {
//             deemedValue: parseFloat(deemedValue) || 0,
//             includesMaintenancePlan: includesMaintenancePlan === "on",
//             taxablePercentage: taxablePercentage || "80%",
//           };
//           break;
//         case "companyCarUnderOperatingLease":
//           payroll.companyCarUnderOperatingLease = {
//             amount: parsedAmount,
//             taxablePercentage: taxablePercentage || "80%",
//           };
//           break;
//         case "providentFund":
//           payroll.providentFund = {
//             amount: parsedAmount,
//             employerContribution: employerContribution === "on",
//             employeeHandlesPayment: employeeHandlesPayment === "on",
//             contributionCalculation,
//             fixedContributionEmployee: parsedFixedContributionEmployee,
//             fixedContributionEmployer: parsedFixedContributionEmployer,
//             rfiEmployee: parsedRfiEmployee,
//             rfiEmployer: parsedRfiEmployer,
//             categoryFactor: categoryFactor || null,
//             beneficiary: beneficiary || null,
//           };
//           break;
//         case "pensionFund":
//           payroll.pensionFund = {
//             amount: parsedAmount,
//             employerContribution: employerContribution === "on",
//             contributionCalculation,
//             fixedContributionEmployee: parsedFixedContributionEmployee,
//             fixedContributionEmployer: parsedFixedContributionEmployer,
//             rfiEmployee: parsedRfiEmployee,
//             rfiEmployer: parsedRfiEmployer,
//             categoryFactor: categoryFactor || null,
//             beneficiary: beneficiary || null,
//           };
//           break;
//         case "retirementAnnuityFund":
//           payroll.retirementAnnuityFund = {
//             employeeContribution: parseFloat(employeeContribution) || 0,
//             employerContribution: parseFloat(employerContribution) || 0,
//             employeeHandlesPayment: employeeHandlesPayment === "on",
//             beneficiary: beneficiary || "",
//           };
//           break;
//         case "bursariesAndScholarships":
//           payroll.bursariesAndScholarships = {
//             taxablePortion: parseFloat(req.body.taxablePortion) || 0,
//             exemptPortion: parseFloat(req.body.exemptPortion) || 0,
//             type: req.body.type || "",
//             employeeHandlesPayment: req.body.employeeHandlesPayment === "on",
//             toDisabledPerson: req.body.toDisabledPerson === "on",
//           };
//           break;
//         case "commission":
//           payroll.commission = { amount: parseFloat(amount) };
//           payroll.commissionEnabled = true;
//           break;
//         case "garnishee":
//           payroll.garnishee = parsedAmount;
//           break;
//         case "travelAllowance":
//           payroll.travelAllowance = {
//             fixedAllowance: fixedAllowancePaidRegularly === "on",
//             fixedAllowanceAmount: parsedFixedAmount,
//             reimbursedExpenses: reimbursedExpenses === "on",
//             companyPetrolCard: companyPetrolCard === "on",
//             reimbursedPerKmTravelled: reimbursedPerKmTravelled === "on",
//             ratePerKm: parsedRatePerKm,
//             only20PercentTax: only20PercentTax === "on",
//             // Add these new fields and link them to the existing ones
//             expensesEnabled: reimbursedExpenses === "on",
//             expensesAmount: reimbursedExpenses === "on" ? parsedFixedAmount : 0,
//             petrolCardEnabled: companyPetrolCard === "on",
//             petrolCardSpend: companyPetrolCard === "on" ? 0 : 0, // You might want to add a field for this in your form
//             kmsReimbursementEnabled: reimbursedPerKmTravelled === "on",
//             kmsTravelled: reimbursedPerKmTravelled === "on" ? 0 : 0, // You might want to add a field for this in your form
//           };
//           break;
//         case "maintenanceOrder":
//           payroll.maintenanceOrder = parsedAmount || 0;
//           break;
//         case "commission":
//           payroll.commission = { amount: parseFloat(amount) };
//           payroll.commissionEnabled = true;
//           break;
//         case "lossOfIncome":
//           payroll.lossOfIncome = parsedAmount || 0;
//           break;
//         case "unionMembershipFee":
//           payroll.unionMembershipFee = parsedAmount || 0;
//           break;
//         case "voluntaryTaxOverDeduction":
//           payroll.voluntaryTaxOverDeduction = parsedAmount || 0;
//           break;
//         case "accommodationBenefit":
//           payroll.accommodationBenefit = parsedAmount || 0;
//           break;
//         case "employerLoan":
//           payroll.employerLoan = {
//             interestRate: parseFloat(interestRate) || 0,
//             regularRepayment: parseFloat(regularRepayment) || 0,
//             calculateInterestBenefit: calculateInterestBenefit === "on",
//           };
//           break;

//         case "taxDirective":
//           payroll.taxDirective = {
//             directiveNumber: directiveNumber || "",
//             directiveType: directiveType || "",
//           };
//           break;

//         case "foreignServiceIncome":
//           payroll.foreignServiceIncome = {
//             foreignServiceTaxExemption: foreignServiceTaxExemption === "on",
//           };
//           break;

//         case "savings":
//           payroll.savings = {
//             regularDeduction: parseFloat(regularDeduction) || 0,
//           };
//           break;
//         // ... (keep other cases as they are)
//         default:
//           req.flash("error", "Unknown component name");
//           return res.redirect(
//             `/clients/${company.companyCode}/employeeProfile/${employeeId}`
//           );
//       }

//       await payroll.save();
//       req.flash("success", "Employee payroll data updated successfully");
//       res.redirect(
//         `/clients/${
//           company.companyCode
//         }/employeeProfile/${employeeId}?nextMonth=${formatDateForDisplay(
//           formattedDate
//         )}`
//       );
//     } catch (error) {
//       console.error("Error updating payroll data:", error);
//       req.flash("error", "Error updating payroll data");
//       res.redirect(
//         `/clients/${req.body.companyCode}/employeeProfile/${employeeId}`
//       );
//     }
//   }
// );
//_______________________________

router.post("/:employeeId", ensureOwnProfile, async (req, res) => {
  const { employeeId } = req.params;
  const { finalise, ...formData } = req.body;
  console.log("User object:", req.user);
  console.log("Current company:", req.user?.currentCompany);

  try {
    if (!req.user || !req.user.currentCompany) {
      console.log("User or current company not found");
      req.flash("error", "Company information not found");
      return res.redirect("/");
    }

    const company = await Company.findById(req.user.currentCompany);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/");
    }

    const companyCode = company.companyCode;

    const employee = await Employee.findOne({
      _id: employeeId,
      company: req.user.currentCompany,
    });

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    let currentMonth = new Date(req.body.currentMonth || employee.doa);
    currentMonth = getEndOfMonthDate(currentMonth);

    // Find and remove any duplicate entries
    const duplicatePayrolls = await Payroll.find({
      employee: employeeId,
      company: req.user.currentCompany,
      month: {
        $gte: moment(currentMonth).startOf("day").toDate(),
        $lte: moment(currentMonth).endOf("day").toDate(),
      },
    });

    let payroll;
    if (duplicatePayrolls.length > 1) {
      // Keep the most recent entry and remove others
      const sortedPayrolls = duplicatePayrolls.sort(
        (a, b) => b.updatedAt - a.updatedAt
      );
      for (let i = 1; i < sortedPayrolls.length; i++) {
        await Payroll.findByIdAndRemove(sortedPayrolls[i]._id);
      }
      payroll = sortedPayrolls[0];
    } else if (duplicatePayrolls.length === 1) {
      payroll = duplicatePayrolls[0];
    } else {
      payroll = new Payroll({
        employee: employeeId,
        company: req.user.currentCompany,
        month: currentMonth,
        finalised: false,
      });
    }

    payroll.finalised = !!finalise;
    await payroll.save();

    // Calculate necessary values for PAYE calculation
    const sampleEmployeeAge = employee.calculateAge();
    const fullPeriodSalary = payroll.basicSalary;
    const isProrated = isProratedMonth(employee, currentMonth);
    const proratedPercentage =
      updateCumulativeProrata(employee, currentMonth) * 100;
    const medicalAidCredit = payroll.medical?.dontApplyTaxCredits
      ? 0
      : calculateMedicalAidCredit(payroll.medical?.members || 0);
    const payFrequency = employee.payFrequency || "monthly";

    console.log("PAYE Calculation Inputs:", {
      sampleEmployeeAge,
      fullPeriodSalary,
      isProrated,
      proratedPercentage,
      payFrequency,
      currentMonth,
    });

    // Calculate PAYE after payroll is defined
    const periodPAYE = Math.max(
      calculatePAYE(
        sampleEmployeeAge,
        fullPeriodSalary,
        null,
        { ...employee, payFrequency, currentMonth },
        isProrated,
        proratedPercentage
      ) - medicalAidCredit,
      0
    );

    console.log("Calculated PAYE:", periodPAYE);

    // Save the calculated PAYE to the payroll
    payroll.PAYE = periodPAYE;
    await payroll.save();

    if (finalise) {
      const nextMonth = getNextMonthEndDate(currentMonth);
      let nextPayroll = await Payroll.findOne({
        employee: employeeId,
        company: req.user.currentCompany,
        month: nextMonth,
      });

      if (!nextPayroll) {
        nextPayroll = new Payroll({
          employee: employeeId,
          company: req.user.currentCompany,
          month: nextMonth,
          basicSalary: payroll.basicSalary,
          medical: payroll.medical,
          finalised: false,
        });
      } else {
        if (nextPayroll.basicSalary === 0) {
          nextPayroll.basicSalary = payroll.basicSalary;
        }
        if (nextPayroll.medical === 0) {
          nextPayroll.medical = payroll.medical;
        }
      }

      await nextPayroll.save();

      // Update cumulative pro-rata for the employee
      updateCumulativeProrata(employee, nextMonth);
      await employee.save();

      req.flash(
        "success",
        "Employee payroll data updated and rolled over to next month successfully"
      );
    }

    const unfinalizedPayroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      finalised: false,
    }).sort({ month: -1 });

    const unfinalizedMonth = unfinalizedPayroll
      ? unfinalizedPayroll.month
      : null;

    const nextMonthString = formatDateForMongoDB(
      getNextMonthEndDate(currentMonth)
    )
      .toISOString()
      .split("T")[0];

    res.redirect(
      `/clients/${companyCode}/employeeProfile/${employeeId}?nextMonth=${nextMonthString}&unfinalizedMonth=${
        unfinalizedMonth
          ? formatDateForMongoDB(unfinalizedMonth).toISOString().split("T")[0]
          : ""
      }`
    );
  } catch (error) {
    console.error("Error updating payroll data:", error);
    req.flash("error", "Error updating payroll data");
    res.redirect("/");
  }
});
//______________________________________

//______________
router.post("/updatePayroll/:id", ensureOwnProfile, async (req, res) => {
  const employeeId = req.params.id;
  const { componentName, amount, month } = req.body;
  console.log("Received form data:", req.body);

  try {
    // Validate and parse inputs
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount)) {
      return res.status(400).send("Invalid amount provided");
    }

    // Fetch the payroll document for the employee
    let payroll = await Payroll.findOne({
      employee: employeeId,
      finalised: false,
    });

    // Create a new payroll entry if none exists
    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: new Date(month), // Use the provided month
        payComponents: [],
      });
    } else {
      // Update the month if payroll exists and is not finalised
      payroll.month = new Date(month);
    }

    // Add a new pay component
    payroll.payComponents.push({
      componentType: componentName,
      amount: parsedAmount, // Save the parsed amount
    });

    // Save the updated or newly created payroll document
    await payroll.save();

    res.status(200).send("Payroll updated successfully");
  } catch (error) {
    console.error("Error updating payroll:", error);
    res.status(500).send("Internal server error");
  }
});

// Route to fetch payroll data for a specific month
router.get(
  "/:employeeId/payroll/:month",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, month } = req.params;
      const employee = await Employee.findById(employeeId).populate("payroll");
      const payroll = await Payroll.findOne({ employee: employeeId, month });

      if (!employee || !payroll) {
        return res.status(404).send("Payroll data not found");
      }

      res.render("employeeProfile", {
        employee,
        payroll,
        selectedMonth: month,
      });
    } catch (error) {
      res.status(500).send("Server Error");
    }
  }
);

//______________________________________________

// GET route to render the defineRFI
router.get("/:employeeId/defineRFI", ensureOwnProfile, async (req, res) => {
  const { employeeId } = req.params;
  try {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Fetch the existing RFI configuration for the employee's company
    const rfiConfig =
      (await RFIConfig.findOne({ company: employee.company }).lean()) || {};

    // Fetch all pay components for the company
    const payComponents = await PayComponent.find({
      company: employee.company,
    }).lean();

    res.render("defineRFI", {
      employeeId,
      employee,
      rfiConfig,
      payComponents,
    });
  } catch (error) {
    console.error("Error fetching employee or RFI configuration:", error);
    res.status(500).send("Server Error");
  }
});

// POST route to save RFI determination
router.post("/:employeeId/defineRFI", ensureOwnProfile, async (req, res) => {
  const { employeeId } = req.params;
  const { rfiOption, selectedIncomes, percentageIncomes, percentageValues } =
    req.body;

  try {
    // Find employee and save the selected RFI determination logic
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Logic to update payroll based on RFI selection
    // Save the RFI determination in payroll for the employee or perform other required calculations
    // Add your logic here

    // Redirect to the employee profile or another page after saving
    res.redirect(`/clients/${req.companyCode}/employeeProfile/${employeeId}`);
  } catch (error) {
    res.status(500).send("Server Error");
  }
});

// DELETE route to remove an employee

//

//__________________________________________________

router.post(
  "/:employeeId/edit/:componentName/:month",
  ensureOwnProfile,
  async (req, res) => {
    try {
      console.log("Update route hit");
      console.log(req.params);
      console.log(req.body);

      // Destructure req.params
      const { employeeId, componentName, month } = req.params;

      // Destructure req.body safely
      const {
        amount,
        employerContribution,
        members,
        employeeHandlesPayment, // Declare after req.body
        dontApplyTaxCredits,
      } = req.body;

      // Convert month parameter to a Date object
      const selectedMonth = new Date(month);

      // Find the specific payroll document for the selected month
      const currentPayroll = await Payroll.findOne({
        employee: employeeId,
        month: selectedMonth,
      });

      if (!currentPayroll) {
        return res.status(404).send("Payroll for the selected month not found");
      }

      // Update the specific pay component in the payroll document
      switch (componentName) {
        case "medical":
          currentPayroll.medical = {
            medicalAid: parseFloat(amount) || 0,
            employerContribution: parseFloat(employerContribution) || 0,
            members: parseInt(members, 10) || 0,
            employeeHandlesPayment: employeeHandlesPayment === "on",
            dontApplyTaxCredits: dontApplyTaxCredits === "on",
          };
          break;

        case "bursariesAndScholarships":
          const parsedTaxablePortion = parseFloat(req.body.taxablePortion) || 0;
          const parsedExemptPortion = parseFloat(req.body.exemptPortion) || 0;
          const type = req.body.type || "";
          const bursariesEmployeeHandlesPayment =
            req.body.employeeHandlesPayment === "on";
          const toDisabledPerson = req.body.toDisabledPerson === "on";

          currentPayroll.bursariesAndScholarships = {
            taxablePortion: parsedTaxablePortion,
            exemptPortion: parsedExemptPortion,
            type: type,
            employeeHandlesPayment: bursariesEmployeeHandlesPayment,
            toDisabledPerson: toDisabledPerson,
          };
          break;

        case "retirementAnnuityFund":
          const parsedEmployeeContribution =
            parseFloat(req.body.employeeContribution) || 0;
          const parsedEmployerContribution =
            parseFloat(employerContribution) || 0;

          currentPayroll.retirementAnnuityFund = {
            employeeContribution: parsedEmployeeContribution,
            employerContribution: parsedEmployerContribution,
            employeeHandlesPayment: employeeHandlesPayment === "on",
            beneficiary: req.body.beneficiary || "",
          };
          break;

        case "providentFund":
          currentPayroll.providentFund = {
            amount: parseFloat(amount) || 0,
            employerContribution: parseFloat(employerContribution) || 0,
            employeeHandlesPayment: employeeHandlesPayment === "on",
          };
          break;

        case "commission":
          currentPayroll.commission = { amount: parseFloat(amount) };
          currentPayroll.commissionEnabled = true;
          break;

        case "maintenanceOrder":
          currentPayroll.maintenanceOrder = parseFloat(amount) || 0;
          break;

        case "lossOfIncome":
          currentPayroll.lossOfIncome = parseFloat(amount) || 0;
          break;

        case "voluntaryTaxOverDeduction":
          currentPayroll.voluntaryTaxOverDeduction = parseFloat(amount) || 0;
          break;

        case "accommodationBenefit":
          currentPayroll.accommodationBenefit = parseFloat(amount) || 0;
          break;

        case "travelAllowance":
          payroll.travelAllowance = {
            fixedAllowance: fixedAllowancePaidRegularly === "on",
            fixedAllowanceAmount: parsedFixedAmount,
            reimbursedExpenses: reimbursedExpenses === "on",
            companyPetrolCard: companyPetrolCard === "on",
            reimbursedPerKmTravelled: reimbursedPerKmTravelled === "on",
            ratePerKm: parsedRatePerKm,
            only20PercentTax: only20PercentTax === "on",
            // Add these new fields and link them to the existing ones
            expensesEnabled: reimbursedExpenses === "on",
            expensesAmount: reimbursedExpenses === "on" ? parsedFixedAmount : 0,
            petrolCardEnabled: companyPetrolCard === "on",
            petrolCardSpend: companyPetrolCard === "on" ? 0 : 0, // You might want to add a field for this in your form
            kmsReimbursementEnabled: reimbursedPerKmTravelled === "on",
            kmsTravelled: reimbursedPerKmTravelled === "on" ? 0 : 0, // You might want to add a field for this in your form
          };
          break;

        // Add cases for other component types if needed

        default:
          return res.status(404).send("Unknown component name");
      }

      // Save the updated payroll document
      await currentPayroll.save();

      // Redirect back to the same month after saving
      res.redirect(
        `/clients/${
          currentPayroll.company.companyCode
        }/employeeProfile/${employeeId}?nextMonth=${selectedMonth.toISOString()}`
      );
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .send("An error occurred while updating the pay component.");
    }
  }
);

//____________________________________________________________

router.post(
  "/:employeeId/remove/:componentName/:month",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, componentName, month } = req.params;

      // Convert month parameter to a Date object
      const selectedMonth = new Date(month);

      // Find the specific payroll document for the selected month
      const currentPayroll = await Payroll.findOne({
        employee: employeeId,
        month: selectedMonth,
      });

      if (!currentPayroll) {
        return res.status(404).send("Payroll for the selected month not found");
      }

      // Remove the specified component from the payroll document
      switch (componentName) {
        case "medical":
          currentPayroll.medical = undefined; // or set to {}
          break;

        case "companyCar":
          if (currentPayroll.companyCar) {
            currentPayroll.companyCar.deemedValue = undefined;
            currentPayroll.companyCar.includesMaintenancePlan = undefined;
            currentPayroll.companyCar.taxablePercentage = undefined;
          }
          break;
        case "providentFund":
          currentPayroll.providentFund = undefined; // or set to {}
          break;
        case "retirementAnnuityFund":
          currentPayroll.retirementAnnuityFund = undefined; // or set to {}
          break;

        case "lossOfIncome":
          currentPayroll.lossOfIncome = undefined; // or set to {}
          break;
        case "pensionFund":
          currentPayroll.pensionFund = undefined; // or set to {}
          break;
        case "travelAllowance":
          currentPayroll.travelAllowance = undefined; // or set to {}
          break;
        case "maintenanceOrder":
          currentPayroll.maintenanceOrder = undefined;
          break;
        // Remove the specified component from the payroll document
        case "bursariesAndScholarships":
          // Clear individual fields instead of setting entire object to undefined
          currentPayroll.bursariesAndScholarships.taxablePortion = undefined;
          currentPayroll.bursariesAndScholarships.exemptPortion = undefined;
          currentPayroll.bursariesAndScholarships.type = undefined;
          currentPayroll.bursariesAndScholarships.employeeHandlesPayment =
            undefined;
          currentPayroll.bursariesAndScholarships.toDisabledPerson = undefined;
          break;
        case "accommodationBenefit":
          currentPayroll.accommodationBenefit = undefined; // or set to {}
          break;
        case "unionMembershipFee":
          currentPayroll.unionMembershipFee = undefined; // or set to {}
          break;
        case "voluntaryTaxOverDeduction":
          currentPayroll.voluntaryTaxOverDeduction = undefined; // or set to {}
          break;
        case "incomeProtection":
          currentPayroll.incomeProtection = undefined; // or set to {}
          break;
        case "accommodationBenefit":
          currentPayroll.accommodationBenefit = undefined; // or set to {}
          break;

        case "commission":
          currentPayroll.commission = undefined; // or set to {}
          break;
        case "companyCarUnderOperatingLease":
          if (currentPayroll.companyCarUnderOperatingLease) {
            currentPayroll.companyCarUnderOperatingLease.amount = undefined;
            currentPayroll.companyCarUnderOperatingLease.taxablePercentage =
              undefined;
          }
          break;

        // Add cases for other component types if needed

        default:
          return res.status(404).send("Unknown component name");
      }

      // Save the updated payroll document
      await currentPayroll.save();

      // Flash message for success
      req.flash(
        "success",
        `Pay component '${componentName}' removed successfully!`
      );
      console.log(
        "success",
        `Pay component '${componentName}' removed successfully!`
      );

      // Redirect back to the same month after removing the pay component
      res.redirect(
        `/clients/${
          company.companyCode
        }/employeeProfile/${employeeId}?nextMonth=${selectedMonth.toISOString()}`
      );
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .send("An error occurred while removing the pay component.");
    }
  }
);

//----------------------

//_______________________________________

router.get(
  "/:employeeId/termination-certificate/:periodIndex",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, periodIndex } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      const servicePeriod = employee.servicePeriods[periodIndex];
      if (!servicePeriod) {
        return res.status(404).send("Service period not found");
      }

      // Create a new PDF document
      const doc = new PDFDocument();

      // Set the response headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=termination_certificate_${employeeId}_${periodIndex}.pdf`
      );

      // Pipe the PDF document to the response
      doc.pipe(res);

      // Add content to the PDF
      doc.fontSize(18).text("Termination Certificate", { align: "center" });
      doc.moveDown();
      doc
        .fontSize(12)
        .text(`Employee: ${employee.firstName} ${employee.lastName}`);
      doc.text(`Employee Number: ${employee.employeeNumber}`);
      doc.text(
        `Service Period: ${servicePeriod.startDate.toLocaleDateString()} to ${
          servicePeriod.endDate
            ? servicePeriod.endDate.toLocaleDateString()
            : "Present"
        }`
      );
      doc.text(
        `Reason for Termination: ${servicePeriod.terminationReason || "N/A"}`
      );
      // Add more details as needed

      // Finalize the PDF and end the stream
      doc.end();
    } catch (error) {
      console.error("Error generating termination certificate:", error);
      res.status(500).send("Error generating termination certificate");
    }
  }
);

router.get(
  "/:employeeId/edit-termination-certificate/:periodIndex",
  ensureOwnProfile,
  async (req, res) => {
    try {
      const { employeeId, periodIndex } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      const servicePeriod = employee.servicePeriods[periodIndex];
      if (!servicePeriod) {
        return res.status(404).send("Service period not found");
      }

      res.render("editTerminationCertificate", {
        employee,
        servicePeriod,
        periodIndex,
      });
    } catch (error) {
      console.error(
        "Error rendering edit termination certificate page:",
        error
      );
      res.status(500).send("Error rendering edit termination certificate page");
    }
  }
);

// //__________________________________________________________

// router.get("/:employeeId/regularInputs", async (req, res) => {
//   const { employeeId } = req.params;

//   try {
//     const employeeId = req.params.employeeId;
//     const employee = await Employee.findById(employeeId).populate("company");

//     if (!employee) {
//       return res.status(404).send("Employee not found");
//     }
//     const company = employee.company;

//     // Retrieve or set thisMonth to the last day of the month from employee's date of appointment
//     const thisMonth = new Date(
//       req.session.thisMonth || getLastDayOfMonth(new Date(employee.doa))
//     );

//     // Fetch payroll for the specific employee and month
//     let payroll = await Payroll.findOne({
//       employee: employeeId,
//       month: thisMonth,
//     });

//     // If no payroll exists, create an empty payroll object for form purposes
//     if (!payroll) {
//       payroll = new Payroll({
//         employee: employeeId,
//         month: thisMonth,
//       });
//     }

//     // Render the annual bonus form
//     res.render("regularInputs", {
//       employee,
//       payroll,
//       thisMonth,
//       messages: req.flash(),
//     });
//   } catch (error) {
//     console.error("Error fetching employee:", error);
//     req.flash("error", "Error fetching employee");
//     res.redirect("/");
//   }
// });

// Route to retrieve DOA and update payroll entry
router.get("/:employeeId/doa", ensureOwnProfile, async (req, res) => {
  const { employeeId } = req.params;

  try {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).json({ error: "Employee not found" });
    }

    const endOfDoaMonth = getEndOfMonthDate(employee.doa);

    // Find or create payroll data for the employee
    let payroll = await Payroll.findOne({ employee: employeeId });
    if (!payroll) {
      console.log(
        "Payroll data not found for this employee, creating new payroll entry"
      );
      payroll = new Payroll({
        month: endOfDoaMonth,
        employee: employeeId,
      });
    } else {
      // Update existing payroll entry
      payroll.month = endOfDoaMonth;
    }

    await payroll.save();

    res.status(200).json({ doa: employee.doa, payroll });
  } catch (error) {
    console.error(
      "Error fetching employee DOA or updating payroll:",
      error.message
    );
    res
      .status(500)
      .json({ error: "Failed to fetch employee DOA or update payroll" });
  }
});

//___________________________

// __________________________

router.get("/:employeeId/onceOff/new", ensureOwnProfile, async (req, res) => {
  try {
    const { employeeId } = req.params;
    const employee = await Employee.findById(employeeId);

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect(
        `/clients/${req.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Retrieve or set thisMonth to the last day of the month from employee's date of appointment
    const thisMonth = new Date(
      req.session.thisMonth || getLastDayOfMonth(new Date(employee.doa))
    );

    // Fetch payroll for the specific employee and month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      month: thisMonth,
    });

    // If no payroll exists, create an empty payroll object for form purposes
    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: thisMonth,
      });
    }

    // Render the annual bonus form
    res.render("onceOffAnnualBonus", {
      employee,
      payroll,
      thisMonth,
      messages: req.flash(),
    });
  } catch (error) {
    console.error("Error displaying annual bonus form:", error);
    req.flash("error", "An error occurred while loading the form");
    res.redirect(`/employeeProfile/${req.params.employeeId}`);
  }
});

// router.get("/:employeeId/payslipInput/new", async (req, res) => {
//   const { employeeId } = req.params;
//   const { payslipInput_name, thisMonth } = req.query;

//   try {
//     const employee = await Employee.findById(employeeId).populate("company");

//     if (!employee) {
//       req.flash("error", "Employee not found");
//       return res.redirect("/");
//     }

//     const company = employee.company;

//     if (!company) {
//       req.flash("error", "Company not found for this employee");
//       return res.redirect("/");
//     }

//     const selectedMonth = new Date(thisMonth);

//     // Fetch the payroll for the specific month
//     let payroll = await Payroll.findOne({
//       employee: employeeId,
//       company: company._id,
//       month: selectedMonth,
//     });

//     if (!payroll) {
//       // If no payroll exists for this month, create a new one
//       payroll = new Payroll({
//         employee: employeeId,
//         company: company._id,
//         month: selectedMonth,
//       });
//       await payroll.save();
//     }

//     // Determine which form to render based on the payslipInput_name
//     let formToRender;
//     switch (payslipInput_name) {
//       // Income
//       case "annualBonus":
//       case "annualPayment":
//       case "arbitrationAward":
//       case "dividendsSubject":
//       case "extraPay":
//       case "leavePaidOut":
//       case "commission":
//       case "restraintOfTrade":
//       // Allowance
//       case "broadBasedEmployeeSharePlan":
//       case "computerAllowance":
//       case "expenseClaim":
//       case "gainVesting":
//       case "phoneAllowance":
//       case "relocationAllowance":
//       case "subsistenceAllowanceInternational":
//       case "subsistenceAllowanceLocal":
//       case "toolAllowance":
//       case "uniformAllowance":
//       // Benefit
//       case "bursariesAndScholarships":
//       case "employeeDebtBenefit":
//       case "medicalCosts":
//       // Deduction
//       case "donations":
//       case "repaymentOfAdvance":
//       case "staffPurchases":
//       // Other
//       case "covid19DisasterRelief":
//       case "longServiceAward":
//       case "tersPayout":
//       case "terminationLumpSums":
//         formToRender = `onceOff${
//           payslipInput_name.charAt(0).toUpperCase() + payslipInput_name.slice(1)
//         }`;
//         break;
//       default:
//         formToRender = "genericPayslipInputForm";
//     }

//     res.render(formToRender, {
//       employee,
//       company,
//       payroll,
//       thisMonth: selectedMonth,
//       payslipInput_name,
//       messages: req.flash(),
//     });
//   } catch (error) {
//     console.error("Error preparing payslip input form:", error);
//     req.flash("error", "Error preparing payslip input form");
//     res.redirect(
//       `/clients/${req.params.companyCode}/employeeProfile/${employeeId}`
//     );
//   }
// });

// //____________________________________________

// router.post("/:employeeId/payslipInput/new", async (req, res) => {
//   const { employeeId } = req.params;
//   const { payslipInput_name, thisMonth } = req.query;
//   const { amount, ...otherFields } = req.body;

//   try {
//     const employee = await Employee.findById(employeeId).populate("company");

//     if (!employee) {
//       req.flash("error", "Employee not found");
//       return res.redirect("/");
//     }

//     const company = employee.company;

//     if (!company) {
//       req.flash("error", "Company not found for this employee");
//       return res.redirect("/");
//     }

//     const selectedMonth = new Date(thisMonth);

//     // Fetch or create the payroll for the specific month
//     let payroll = await Payroll.findOne({
//       employee: employeeId,
//       company: company._id,
//       month: selectedMonth,
//     });

//     if (!payroll) {
//       payroll = new Payroll({
//         employee: employeeId,
//         company: company._id,
//         month: selectedMonth,
//       });
//     }

//     // Update payroll based on payslipInput_name
//     switch (payslipInput_name) {
//       case "annualBonus":
//         payroll.annualBonus = {
//           amount: parseFloat(amount),
//         };
//         break;
//       case "commission":
//         payroll.commission = parseFloat(amount);
//         break;
//       case "leavePaidOut":
//         payroll.leavePaidOut = parseFloat(amount);
//         break;
//       case "bursariesAndScholarships":
//         payroll.bursariesAndScholarships = {
//           taxablePortion: parseFloat(otherFields.taxablePortion) || 0,
//           exemptPortion: parseFloat(otherFields.exemptPortion) || 0,
//         };
//         break;
//       // Add more cases for other payslip input types
//       default:
//         // For generic inputs, you might want to store them in a separate field
//         if (!payroll.otherPayslipInputs) {
//           payroll.otherPayslipInputs = {};
//         }
//         payroll.otherPayslipInputs[payslipInput_name] = parseFloat(amount);
//     }

//     await payroll.save();

//     req.flash("success", `${payslipInput_name} added successfully`);
//     res.redirect(
//       `/clients/${company.companyCode}/employeeProfile/${employeeId}`
//     );
//   } catch (error) {
//     console.error("Error saving payslip input:", error);
//     req.flash("error", "Error saving payslip input");
//     res.redirect(
//       `/clients/${req.params.companyCode}/employeeProfile/${employeeId}`
//     );
//   }
// });
//____________________________

//________________________________________________________

router.post("/:employeeId/unfinalise", ensureOwnProfile, async (req, res) => {
  const { employeeId } = req.params;
  const { month } = req.body;

  try {
    const employee = await Employee.findOne({
      _id: employeeId,
      company: req.user.currentCompany,
    });

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    const payrollMonth = new Date(month);

    // Find the payroll for the specified month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: payrollMonth,
    });

    if (!payroll) {
      req.flash("error", "Payroll not found for the specified month");
      return res.redirect(
        `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Unfinalise the payroll
    payroll.finalised = false;
    await payroll.save();

    // Delete the next month's payroll if it exists and is not finalised
    const nextMonth = new Date(
      payrollMonth.getFullYear(),
      payrollMonth.getMonth() + 1,
      1
    );
    await Payroll.deleteOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: nextMonth,
      finalised: false,
    });

    req.flash("success", "Payroll has been unfinalised successfully");
    res.redirect(
      `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}?nextMonth=${month}`
    );
  } catch (error) {
    console.error("Error unfinalising payroll:", error);
    req.flash("error", "Error unfinalising payroll");
    res.redirect(
      `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
    );
  }
});

//_________________________________________________________

router.get(
  "/:employeeId/add-commission",
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId } = req.params;
    const { relevantDate } = req.query;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Parse the relevantDate and set the time to 21:59:59.999
      const adjustedDate = new Date(relevantDate);
      adjustedDate.setUTCHours(21, 59, 59, 999);

      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: adjustedDate,
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: adjustedDate,
          commission: 0, // Set initial commission to 0
        });
      }

      // Enable commission for this payroll
      payroll.commissionEnabled = true;

      await payroll.save();

      req.flash("success", "Commission input added successfully");
      res.redirect(
        `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error adding commission input:", error);
      req.flash("error", "Error adding commission input");
      res.redirect(
        `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

//__________________________________________________________

//__________________________________________________________
router.get(
  "/:employeeId/edit/commission/:month",
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId, month } = req.params;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const adjustedDate = new Date(month);
      adjustedDate.setUTCHours(21, 59, 59, 999);

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: adjustedDate,
      });

      if (!payroll || !payroll.commissionEnabled) {
        req.flash("error", "Commission not enabled for this month");
        return res.redirect(
          `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
        );
      }
      let relevantDate =
        latestPayroll && !latestPayroll.finalised
          ? latestPayroll.month
          : latestPayroll
          ? getNextMonthEndDate(latestPayroll.month)
          : getCurrentOrNextLastDayOfMonth();

      const formattedDate = formatDateForDisplay(relevantDate);

      res.render("editCommission", {
        employee,
        payroll,
        relevantMonth,
        month: adjustedDate,
        formattedDate: formatDateForDisplay(adjustedDate),
        company: employee.company, // Add this line to pass the company object
      });
    } catch (error) {
      console.error("Error rendering edit commission page:", error);
      req.flash("error", "Error rendering edit commission page");
      res.redirect(
        `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);
//_______________________________________________________

//____________________________________

router.post(
  "/:employeeId/commission/:month",
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId, month } = req.params;
    const { commissionAmount } = req.body;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Parse the month and set the time to 21:59:59.999
      const adjustedDate = new Date(month);
      adjustedDate.setUTCHours(21, 59, 59, 999);

      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: adjustedDate,
      });

      if (!payroll) {
        // If payroll doesn't exist, create a new one
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: adjustedDate,
        });
      }

      // Update the commission amount
      payroll.commission = parseFloat(commissionAmount) || 0;
      payroll.commissionEnabled = true;

      await payroll.save();

      req.flash("success", "Commission updated successfully");
      res.redirect(
        `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error processing commission action:", error);
      req.flash("error", "Error processing commission action");
      res.redirect(
        `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

//__________________________
router.post(
  "/:employeeId/update/commission/:month",
  ensureOwnProfile,
  async (req, res) => {
    const { employeeId, month } = req.params;
    const { commissionAmount, action } = req.body;

    try {
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      // Parse the month and set the time to 21:59:59.999
      const adjustedDate = new Date(month);
      adjustedDate.setUTCHours(21, 59, 59, 999);

      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: adjustedDate,
      });

      if (!payroll) {
        // If payroll doesn't exist, create a new one
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: adjustedDate,
        });
      }

      switch (action) {
        case "save":
          // Update the commission amount
          payroll.commission = parseFloat(commissionAmount) || 0;
          payroll.commissionEnabled = true;
          req.flash("success", "Commission updated successfully");
          break;
        case "remove":
          // Remove the commission
          payroll.commission = 0;
          payroll.commissionEnabled = false;
          req.flash("success", "Commission removed successfully");
          break;
        default:
          req.flash("error", "Invalid action");
          return res.redirect(
            `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
          );
      }

      await payroll.save();

      res.redirect(
        `/clients/${employee.company.companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error processing commission action:", error);
      req.flash("error", "Error processing commission action");
      res.redirect(
        `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
      );
    }
  }
);

// POST route to handle form submission

//_________________________

// Remove these old routes:
// router.get("/:employeeId/test-csrf"...)
// router.post("/:employeeId/test-csrf"...)

// Keep only these routes with the correct path structure:

router.use((req, res, next) => {
  console.log("\n=== Employee Profile Route Debug ===");
  console.log("Checking route:", req.path);
  console.log(
    "Route stack:",
    router.stack.map((layer) => ({
      regexp: layer.regexp?.toString(),
      path: layer.route?.path,
    }))
  );
  next();
});

// Update the route path to be more specific

// Add this route if it's not already present

// Unified UI19 form submission and download route
router.post(
  "/:companyCode/employeeProfile/:employeeId/ui19/submit",
  ensureAuthenticated,
  csrfProtection,
  async function (req, res) {
    // Changed to explicit async function declaration
    console.log("\n=== Unified UI19 Form Submission Route ===");
    try {
      const { companyCode, employeeId } = req.params;
      const { signatureData, signatureType, _csrf, ...formData } = req.body;

      // Find company and employee
      console.log("\n=== Looking up Company and Employee ===");
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        console.error("Company or Employee not found:", {
          companyCode,
          employeeId,
        });
        return res.status(404).json({ error: "Company or Employee not found" });
      }

      // Process form data
      const processedFormData = Object.entries(formData).reduce(
        (acc, [key, value]) => {
          acc[key] = Array.isArray(value) ? value[0] : value;
          return acc;
        },
        {}
      );

      // Save UI19 document
      const ui19Doc = await UI19.findOneAndUpdate(
        { company: company._id, employee: employee._id },
        {
          company: company._id,
          employee: employee._id,
          signature: signatureData
            ? {
                data: signatureData,
                type: signatureType,
                date: new Date(),
              }
            : undefined,
          formData: processedFormData,
        },
        { upsert: true, new: true }
      );

      // Generate PDF
      const pdfBuffer = await generateUI19PDF(ui19Doc);

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=UI19_${companyCode}_${employeeId}.pdf`
      );
      res.send(pdfBuffer);
    } catch (error) {
      console.error("Error in UI19 form submission:", error);
      res.status(500).json({ error: "Failed to process UI19 form" });
    }
  }
);

// Route to handle custom income quantity form (Form 2)
router.get(
  "/:companyCode/employeeProfile/:employeeId/custom-income-quantity/:customIncomeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, customIncomeId, companyCode } = req.params;

      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const customIncomeItem = await CustomItem.findById(customIncomeId);
      if (!customIncomeItem) {
        req.flash("error", "Custom income item not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
      }

      // Check if this is a customRateQuantity type
      if (customIncomeItem.inputType !== 'customRateQuantity') {
        req.flash("error", "This custom income item does not support quantity input");
        return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
      }

      // Get current payroll to check for existing quantity
      const currentMonth = formatDateForMongoDB(getCurrentOrNextLastDayOfMonth());
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: currentMonth,
      });

      let existingQuantity = null;
      if (payroll && payroll.customIncomeItems) {
        const existingItem = payroll.customIncomeItems.find(
          item => item.customIncomeId.toString() === customIncomeId
        );
        existingQuantity = existingItem ? existingItem.quantity : null;
      }

      res.render("customIncomeQuantityForm", {
        employee,
        company,
        customIncomeItem,
        existingQuantity,
        req,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading custom income quantity form:", error);
      req.flash("error", "Error loading form");
      res.redirect(`/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`);
    }
  }
);

// POST route to handle custom income quantity form submission
router.post(
  "/:companyCode/employeeProfile/:employeeId/custom-income-quantity/:customIncomeId",
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId, customIncomeId, companyCode } = req.params;
      const { quantity } = req.body;

      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect("/");
      }

      const customIncomeItem = await CustomItem.findById(customIncomeId);
      if (!customIncomeItem) {
        req.flash("error", "Custom income item not found");
        return res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
      }

      // Get or create current payroll
      const currentMonth = formatDateForMongoDB(getCurrentOrNextLastDayOfMonth());
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: currentMonth,
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: employee.company._id,
          month: currentMonth,
          customIncomeItems: []
        });
      }

      // Initialize customIncomeItems array if it doesn't exist
      if (!payroll.customIncomeItems) {
        payroll.customIncomeItems = [];
      }

      // Find existing entry or create new one
      let existingItemIndex = payroll.customIncomeItems.findIndex(
        item => item.customIncomeId.toString() === customIncomeId
      );

      if (existingItemIndex >= 0) {
        // Update existing entry
        payroll.customIncomeItems[existingItemIndex].quantity = parseFloat(quantity);
        payroll.customIncomeItems[existingItemIndex].calculatedAmount =
          parseFloat(quantity) * (customIncomeItem.customRate || 0);
      } else {
        // Create new entry
        const customIncomeEntry = {
          customIncomeId: customIncomeId,
          name: customIncomeItem.name,
          inputType: customIncomeItem.inputType,
          quantity: parseFloat(quantity),
          customRate: customIncomeItem.customRate,
          hoursWorkedFactor: customIncomeItem.hoursWorkedFactor || 1,
          differentRateForEveryEmployee: customIncomeItem.differentRateForEveryEmployee || false,
          calculatedAmount: parseFloat(quantity) * (customIncomeItem.customRate || 0)
        };
        payroll.customIncomeItems.push(customIncomeEntry);
      }

      await payroll.save();

      req.flash("success", "Quantity saved successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error saving custom income quantity:", error);
      req.flash("error", "Error saving quantity");
      res.redirect(`/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`);
    }
  }
);

// DELETE route to handle custom income item removal
router.delete(
  "/:companyCode/employeeProfile/:employeeId/custom-income/:customIncomeId",
  async (req, res) => {
    try {
      console.log("=== CUSTOM INCOME ITEM REMOVAL ===");
      const { employeeId, customIncomeId, companyCode } = req.params;

      console.log("Removal request for:", {
        employeeId,
        customIncomeId,
        companyCode
      });

      // Validate ObjectIds
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(employeeId)) {
        console.log("Invalid employee ID format");
        return res.status(400).json({
          success: false,
          message: "Invalid employee ID format"
        });
      }
      if (!mongoose.Types.ObjectId.isValid(customIncomeId)) {
        console.log("Invalid custom income item ID format");
        return res.status(400).json({
          success: false,
          message: "Invalid custom income item ID format"
        });
      }

      // Find the employee
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        console.log("Employee not found");
        return res.status(404).json({ success: false, message: "Employee not found" });
      }

      // Get current payroll
      const currentMonth = formatDateForMongoDB(getCurrentOrNextLastDayOfMonth());
      console.log("Looking for payroll with month:", currentMonth);

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company._id,
        month: currentMonth,
      });

      if (!payroll) {
        console.log("No payroll found for current month");
        return res.status(404).json({
          success: false,
          message: "No payroll found for current period"
        });
      }

      console.log("Found payroll:", payroll._id);
      console.log("Custom income items before removal:", payroll.customIncomeItems.length);

      // Find the item to remove
      const itemIndex = payroll.customIncomeItems.findIndex(
        item => item.customIncomeId.toString() === customIncomeId
      );

      if (itemIndex === -1) {
        console.log("Custom income item not found in payroll");
        return res.status(404).json({
          success: false,
          message: "Custom income item not found in current payroll"
        });
      }

      const itemToRemove = payroll.customIncomeItems[itemIndex];
      console.log("Found item to remove:", {
        name: itemToRemove.name,
        customIncomeId: itemToRemove.customIncomeId,
        inputType: itemToRemove.inputType
      });

      // Remove the item from the array
      payroll.customIncomeItems.splice(itemIndex, 1);
      console.log("Custom income items after removal:", payroll.customIncomeItems.length);

      // Save the updated payroll
      await payroll.save();
      console.log("Payroll saved successfully");

      res.json({
        success: true,
        message: `${itemToRemove.name} has been removed from this payroll period`
      });

    } catch (error) {
      console.error("Error removing custom income item:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Error removing custom income item"
      });
    }
  }
);

module.exports = router;
