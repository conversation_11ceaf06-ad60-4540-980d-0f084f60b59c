const express = require("express");
const router = express.Router();
const Payroll = require("../models/Payroll");
const Employee = require("../models/Employee");

// GET route to display the travel allowance edit form
router.get("/:employeeId/edit/travelAllowance/:date", async (req, res) => {
  try {
    const { employeeId, date } = req.params;
    const employee = await Employee.findById(employeeId);
    const payroll = await Payroll.findOne({
      employee: employeeId,
      month: new Date(date),
    });

    if (!employee || !payroll) {
      return res.status(404).send("Employee or Payroll not found");
    }

    res.render("editTravelAllowancePayslipInput", {
      employee,
      payroll,
      date,
    });
  } catch (error) {
    console.error("Error fetching travel allowance data:", error);
    res.status(500).send("Server error");
  }
});

// POST route to handle the travel allowance form submission
router.post("/:employeeId/update/travelAllowance/:date", async (req, res) => {
  try {
    const { employeeId, date } = req.params;
    const {
      expensesEnabled,
      expensesAmount,
      petrolCardEnabled,
      petrolCardSpend,
      kmsReimbursementEnabled,
      kmsTravelled,
    } = req.body;

    const payroll = await Payroll.findOne({
      employee: employeeId,
      month: new Date(date),
    });

    if (!payroll) {
      return res.status(404).send("Payroll not found");
    }

    // Update the payroll document
    payroll.travelAllowance.expensesEnabled = expensesEnabled === "on";
    payroll.travelAllowance.expensesAmount =
      expensesEnabled === "on" ? parseFloat(expensesAmount) : 0;
    payroll.travelAllowance.petrolCardEnabled = petrolCardEnabled === "on";
    payroll.travelAllowance.petrolCardSpend =
      petrolCardEnabled === "on" ? parseFloat(petrolCardSpend) : 0;
    payroll.travelAllowance.kmsReimbursementEnabled =
      kmsReimbursementEnabled === "on";
    payroll.travelAllowance.kmsTravelled =
      kmsReimbursementEnabled === "on" ? parseInt(kmsTravelled) : 0;

    await payroll.save();

    res.redirect(`/employeeProfile/${employeeId}`);
  } catch (error) {
    console.error("Error updating travel allowance:", error);
    res.status(500).send("Server error");
  }
});

module.exports = router;
