const express = require("express");
const router = express.Router({ mergeParams: true });
const ReportController = require("../controllers/reportController");
const Company = require("../models/Company");
const Report = require("../models/Report");
const ReportSettings = require("../models/ReportSettings");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const Employee = require("../models/Employee");
const ReportEmailService = require("../services/reportEmailService");
const moment = require("moment");

// GET /clients/:companyCode/reporting
router.get("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    console.log("Rendering reporting page");
    const { companyCode } = req.params;
    const company = await Company.findOne({ companyCode });

    if (!company) {
      console.log("Company not found:", companyCode);
      req.flash("error", "Company not found");
      return res.redirect("/");
    }

    // Get recent reports - limit to 6
    const recentReports = await Report.find({ company: company._id })
      .sort({ createdAt: -1 })
      .limit(6)
      .populate("generatedBy", "firstName lastName");

    // Get report settings
    const settings = await ReportSettings.findOne({ company: company._id });

    // Get active employees for the company
    const employees = await Employee.find({
      company: company._id,
      status: "Active", // Only get active employees
    })
      .select("_id firstName lastName companyEmployeeNumber") // Only get needed fields
      .sort("lastName firstName"); // Sort by name

    console.log("Rendering with data:", {
      company: company._id,
      companyCode,
      recentReports: recentReports.length,
      employeeCount: employees.length,
      settings: !!settings,
      csrfToken: req.csrfToken(),
    });

    res.render("reporting", {
      company,
      companyCode,
      recentReports,
      employees, // Pass employees to the template
      settings,
      csrfToken: req.csrfToken(),
      user: req.user,
      moment: require("moment"),
    });
  } catch (error) {
    console.error("Error loading reporting page:", error);
    req.flash("error", "Failed to load reporting page");
    res.redirect("/");
  }
});

// Generate Employee List Report
router.post(
  "/employee-list",
  ensureAuthenticated,
  csrfProtection,
  ReportController.generateEmployeeList
);

// Get Recent Reports
router.get(
  "/recent",
  ensureAuthenticated,
  csrfProtection,
  ReportController.getRecentReports
);

// Download Report
router.get(
  "/download/:reportId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      await ReportController.downloadReport(req, res);
    } catch (error) {
      console.error("Error in download route:", error);
      res.status(500).json({
        success: false,
        message: "Failed to download report",
        error: error.message,
      });
    }
  }
);

// Delete Report
router.delete(
  "/:reportId",
  ensureAuthenticated,
  csrfProtection,
  ReportController.deleteReport
);

// Add this route for report generation
router.post(
  "/:reportType",
  ensureAuthenticated,
  csrfProtection,
  async (req, res, next) => {
    console.log("\n=== Generate Report Route ===");
    console.log("Report Type:", req.params.reportType);
    console.log("Request Body:", req.body);

    try {
      // Skip settings requests - these should be handled by the settings route
      if (req.params.reportType === "settings") {
        console.log("Forwarding to settings route");
        return next();
      }

      const { companyCode, reportType } = req.params;
      const { format, dateRange, selectedFields, selectedEmployees } = req.body;

      console.log("Processing report generation:", {
        companyCode,
        reportType,
        format,
        dateRange,
        selectedFields,
        selectedEmployees
      });

      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Get report settings
      let settings = await ReportSettings.findOne({ company: company._id });
      console.log("Found settings:", !!settings);

      // Merge selectedFields from request into settings for this report generation
      if (selectedFields && selectedFields.length > 0) {
        console.log("Merging selectedFields from request:", selectedFields);
        // Create a temporary settings object with selectedFields
        settings = settings ? { ...settings.toObject(), selectedFields } : { selectedFields };
      }

      console.log("Final settings for report generation:", settings);

      // Generate report based on type
      let reportData;
      switch (reportType) {
        case "employeeList":
          reportData = await ReportController.generateEmployeeList(
            company._id,
            format,
            settings
          );
          break;
        case "employeeBasicInfo":
          reportData = await ReportController.generateEmployeeBasicInfo(
            company._id,
            format,
            settings,
            selectedEmployees
          );
          break;
        case "payrollVariance":
          // Calculate date range based on selection
          const dates = calculateDateRange(dateRange);
          reportData = await ReportController.generatePayrollVarianceReport(
            company._id,
            format,
            settings,
            dates
          );
          break;
        case "transactionHistory":
          reportData = await ReportController.generateTransactionHistory(
            company._id,
            format,
            settings,
            selectedEmployees,
            calculateDateRange(dateRange)
          );
          break;
        case "employmentTaxIncentive":
          reportData = await ReportController.generateEmploymentTaxIncentive(
            company._id,
            format,
            settings,
            dateRange ? calculateDateRange(dateRange) : null
          );
          break;
        case "leaveDaysReport":
          reportData = await ReportController.generateLeaveDaysReport(
            company._id,
            format,
            settings,
            selectedEmployees,
            req.body.selectedLeaveTypes || null
          );
          break;
        case "leaveExpiryReport":
          reportData = await ReportController.generateLeaveExpiryReport(
            company._id,
            format,
            settings,
            selectedEmployees,
            dateRange ? calculateDateRange(dateRange) : null
          );
          break;
        case "leaveReport":
          reportData = await ReportController.generateLeaveReport(
            company._id,
            format,
            settings,
            selectedEmployees,
            dateRange ? calculateDateRange(dateRange) : null
          );
          break;
        case "payslips":
          // Calculate date range based on selection
          const payslipDates = calculateDateRange(dateRange);
          reportData = await ReportController.generatePayslips(
            company._id,
            format,
            settings,
            payslipDates,
            selectedEmployees
          );
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      // Create report record
      const report = new Report({
        company: company._id,
        type: reportType,
        format,
        settings: settings?.toObject ? settings.toObject() : settings,
        dateRange: {
          startDate: new Date(), // Calculate based on dateRange
          endDate: new Date(),
        },
        generatedBy: req.user._id,
        data: reportData,
        status: "completed",
      });

      await report.save();
      console.log("Report saved:", report._id);

      // Send email if automation is enabled
      try {
        const emailResult = await ReportEmailService.sendReportEmail(
          reportData,
          reportType,
          company._id,
          req.user,
          format
        );

        if (emailResult.sent) {
          console.log(`Report emailed to ${emailResult.successfulSends}/${emailResult.totalRecipients} recipients`);
        }
      } catch (emailError) {
        // Log email error but don't fail the report generation
        console.error("Failed to send report email:", emailError);
      }

      // Get file details for response
      const { contentType, extension } =
        ReportController.getFileDetails(format);

      // Send response
      res.setHeader("Content-Type", contentType);
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${reportType}_${
          new Date().toISOString().split("T")[0]
        }.${extension}`
      );
      res.send(reportData);
    } catch (error) {
      console.error("Error generating report:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate report",
        error: error.message,
      });
    }
  }
);

// Add this helper function
function calculateDateRange(dateRange) {
  const now = moment();
  switch (dateRange) {
    case "currentMonth":
      return {
        startDate: now.startOf("month").toDate(),
        endDate: now.endOf("month").toDate(),
      };
    case "previousMonth":
      return {
        startDate: now.subtract(1, "month").startOf("month").toDate(),
        endDate: now.endOf("month").toDate(),
      };
    case "currentYear":
      return {
        startDate: now.startOf("year").toDate(),
        endDate: now.endOf("year").toDate(),
      };
    // Add other cases as needed
    default:
      throw new Error(`Invalid date range: ${dateRange}`);
  }
}

module.exports = router;
