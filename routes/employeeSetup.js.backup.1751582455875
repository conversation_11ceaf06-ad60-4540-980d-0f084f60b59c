const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const User = require("../models/user");
const Role = require("../models/role"); 
const bcrypt = require("bcrypt");
const csrf = require("csurf");
const csrfProtection = csrf({
  cookie: {
    key: '_csrf',
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  }
});

const SALT_ROUNDS = 12; // Add consistent salt rounds

// Debug middleware
router.use((req, res, next) => {
  console.log("\n=== Employee Setup Route ===");
  console.log("Path:", req.path);
  console.log("Method:", req.method);
  console.log("Token:", req.params.token);
  console.log("CSRF Cookie:", req.cookies._csrf);
  console.log("CSRF Token:", req.csrfToken ? req.csrfToken() : 'Not generated yet');
  next();
});

// GET route for setup page
router.get("/setup/:token", csrfProtection, async (req, res) => {
  console.log("Setup GET route hit with token:", req.params.token);
  try {
    const { token } = req.params;

    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      console.log("Invalid or expired token");
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/login");
    }

    res.render("employee-setup", {
      employee,
      token,
      csrfToken: req.csrfToken(),
    });
  } catch (error) {
    console.error("Error in setup GET route:", error);
    req.flash("error", "An error occurred while processing your request");
    res.redirect("/login");
  }
});

// POST route for handling password setup
router.post("/setup/:token", csrfProtection, async (req, res) => {
  console.log("Setup POST route hit");
  try {
    const { token } = req.params;
    const { password, confirmPassword } = req.body;
    console.log("Processing setup for token:", token);

    // Validate passwords match
    if (password !== confirmPassword) {
      console.log("Passwords do not match");
      return res.render("employee-setup", {
        error: "Passwords do not match",
        token,
        csrfToken: req.csrfToken(),
      });
    }

    // Find employee with valid token
    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    }).populate('user');

    if (!employee) {
      console.log("Invalid or expired token on submission");
      req.flash("error", "Invalid or expired setup link");
      return res.redirect("/login");
    }

    // Find or create user account
    let user = await User.findOne({ email: employee.email.toLowerCase() });
    console.log("Existing user found:", user ? "yes" : "no");

    if (!user) {
      console.log("Creating new user for employee");
      user = new User({
        email: employee.email.toLowerCase(),
        firstName: employee.firstName,
        lastName: employee.lastName,
        username: employee.email.toLowerCase(),
      });
    }

    // Update user password and verify
    user.password = password; // Let the User model's pre-save hook handle the hashing
    user.isVerified = true;

    // Set default role if not set
    if (!user.role) {
      const defaultRole = await Role.findOne({ name: 'employee' });
      if (defaultRole) {
        user.role = defaultRole._id;
        user.roleName = 'employee';
      }
    }

    try {
      await user.save();
      console.log("User saved with ID:", user._id);
    } catch (error) {
      if (error.code === 11000) {
        // Handle duplicate key error
        console.log("Duplicate key error - attempting to find existing user");
        user = await User.findOne({ email: employee.email.toLowerCase() });
        if (!user) {
          throw new Error("Failed to create or find user");
        }
      } else {
        throw error;
      }
    }

    // Update employee record
    employee.selfServiceToken = undefined;
    employee.selfServiceTokenExpiration = undefined;
    employee.user = user._id;
    await employee.save();
    console.log("Employee updated with user ID:", user._id);

    console.log("Setup completed successfully");
    req.flash("success_msg", "Account setup complete. Please log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error in setup POST route:", error);
    res.render("error", {
      message: "An error occurred while processing your setup request.",
      error: { status: 500 },
    });
  }
});

module.exports = router;
