const express = require("express");
const router = express.Router();
const User = require("../models/user");
const Company = require("../models/Company");
const Role = require("../models/role");
const { ensureAuthenticated } = require("../config/auth");
const crypto = require("crypto");
const nodemailer = require("nodemailer");
const ejs = require("ejs");
const path = require("path");
const adminController = require("../controllers/adminController");
const bcrypt = require("bcrypt");
const { checkFeatureAccess } = require('../middleware/featureAccess');

// Add the feature check middleware to all role management routes
router.use('/assign-roles*', checkFeatureAccess('ROLE_MANAGEMENT'));
router.use('/delete-user', checkFeatureAccess('ROLE_MANAGEMENT'));
router.use('/assign-companies*', checkFeatureAccess('ROLE_MANAGEMENT'));

// Render a page to view users
router.get("/manage-users", ensureAuthenticated, async (req, res) => {
  try {
    const companyId = req.user.currentCompany;
    let company = null;
    if (companyId) {
      company = await Company.findById(companyId);
    }

    const users = await User.find(companyId ? { companies: companyId } : {})
      .populate("role")
      .lean();

    const roles = await Role.find().lean();

    // Create the view data object
    const viewData = {
      users,
      company,
      roles,
      currentUser: req.user,
      successMessage: req.flash("success")[0] || "",
      errorMessage: req.flash("error")[0] || "",
    };

    // Add csrfToken if available
    if (req.csrfToken) {
      viewData.csrfToken = req.csrfToken();
    }

    res.render("assign-roles", viewData);
  } catch (error) {
    console.error("Error fetching users:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Add a new user
router.post("/add-user", ensureAuthenticated, async (req, res) => {
  try {
    const { firstName, lastName, email, password } = req.body;
    const newUser = new User({ firstName, lastName, email, password });
    await newUser.save();
    req.flash("success", "User added successfully");
    res.redirect("/admin/manage-users");
  } catch (error) {
    console.error("Error adding user:", error);
    req.flash("error", "Error adding user");
    res.redirect("/admin/manage-users");
  }
});

// Handle role assignment
router.post("/assign-role", ensureAuthenticated, async (req, res) => {
  try {
    const { userId, roleId } = req.body;

    const user = await User.findById(userId);
    const role = await Role.findById(roleId);

    if (!user || !role) {
      req.flash("error", "User or Role not found");
      return res.redirect("/admin/manage-users");
    }

    user.role = role._id;
    await user.save();

    req.flash(
      "success",
      `Role ${role.name} assigned to ${user.firstName} ${user.lastName}`
    );
    res.redirect("/admin/manage-users");
  } catch (error) {
    console.error("Error assigning role:", error);
    req.flash("error", "Error assigning role");
    res.redirect("/admin/manage-users");
  }
});

// Route for assign-roles form
router.get("/assign-roles-form", ensureAuthenticated, async (req, res) => {
  try {
    const roles = await Role.find().lean();

    // Get the current user's company
    const company = req.user.currentCompany
      ? await Company.findById(req.user.currentCompany).lean()
      : null;

    // Fetch only the companies associated with the current user
    const userCompanies = await Company.find({
      _id: { $in: req.user.companies },
    }).lean();

    res.render("assign-roles-form", {
      roles,
      companies: userCompanies,
      company,
      currentUser: req.user,
      successMessage: req.flash("success")[0] || "",
      errorMessage: req.flash("error")[0] || "",
    });
  } catch (error) {
    console.error("Error fetching data for assign-roles form:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Handle the form submission for assigning roles
router.post("/assign-roles", ensureAuthenticated, async (req, res) => {
  try {
    const { email, firstName, lastName, role, companies, permissions } =
      req.body;

    // Validate input
    if (!email || !firstName || !lastName || !role) {
      return res.status(400).json({
        success: false,
        message: "Please fill in all required fields",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "A user with this email already exists",
      });
    }

    // Generate a temporary password
    const tempPassword = crypto.randomBytes(10).toString("hex");

    // Create user object with the temporary password
    const newUser = new User({
      email,
      firstName,
      lastName,
      role,
      password: tempPassword, // Add temporary password
      verificationToken: crypto.randomBytes(20).toString("hex"),
      verificationTokenExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000),
      isVerified: false,
      createdBy: req.user._id,
      createdAt: new Date(),
    });

    // Handle companies and permissions
    if (Array.isArray(companies)) {
      newUser.companies = companies;
      newUser.companyPermissions = companies.map((companyId) => ({
        company: companyId,
        accessLevel: permissions[companyId] || "read",
        grantedBy: req.user._id,
        grantedAt: new Date(),
      }));
    }

    await newUser.save();

    // Send verification email
    const verificationUrl = `${req.protocol}://${req.get(
      "host"
    )}/set-password/${newUser.verificationToken}`;

    try {
      await sendVerificationEmail(newUser, verificationUrl);
    } catch (emailError) {
      console.error("Error sending verification email:", emailError);
      return res.status(200).json({
        success: true,
        message:
          "User created successfully, but there was an error sending the verification email. Please try resending it.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "User created successfully. A verification email has been sent.",
    });
  } catch (error) {
    console.error("Error creating user:", error);
    let errorMessage = "Error creating user";

    if (error.code === 11000) {
      errorMessage = "Email address is already in use";
    } else if (error.name === "ValidationError") {
      errorMessage = Object.values(error.errors)
        .map((err) => err.message)
        .join(", ");
    }

    return res.status(400).json({
      success: false,
      message: errorMessage,
    });
  }
});

// Helper function to send verification email
async function sendVerificationEmail(user, verificationUrl) {
  const emailHtml = await ejs.renderFile(
    path.join(__dirname, "../views/emails/verification-email.ejs"),
    {
      firstName: user.firstName,
      verificationUrl,
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
    }
  );

  const transporter = nodemailer.createTransport({
    service: process.env.EMAIL_SERVICE || "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  await transporter.sendMail({
    from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM}>`,
    to: user.email,
    subject: "Complete Your Account Setup",
    html: emailHtml,
    attachments: [
      {
        filename: "logo.png",
        path: path.join(__dirname, "../public/images/logo.png"),
        cid: "company-logo",
      },
    ],
  });
}

// Add a route to handle password setup
router.get("/set-password/:token", async (req, res) => {
  try {
    const user = await User.findOne({
      verificationToken: req.params.token,
      verificationTokenExpiry: { $gt: new Date() },
    });

    if (!user) {
      req.flash("error", "Invalid or expired verification token");
      return res.redirect("/auth/login");
    }

    res.render("set-password", {
      token: req.params.token,
      email: user.email,
    });
  } catch (error) {
    console.error("Error in set-password route:", error);
    req.flash("error", "An error occurred");
    res.redirect("/auth/login");
  }
});

// Handle password setup
router.post("/set-password", async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    // Validate password
    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect(`/admin/set-password/${token}`);
    }

    if (password.length < 8) {
      req.flash("error", "Password must be at least 8 characters long");
      return res.redirect(`/admin/set-password/${token}`);
    }

    const user = await User.findOne({
      verificationToken: token,
      verificationTokenExpiry: { $gt: new Date() },
    });

    if (!user) {
      req.flash("error", "Invalid or expired verification token");
      return res.redirect("/auth/login");
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user
    user.password = hashedPassword;
    user.isVerified = true;
    user.verificationToken = undefined;
    user.verificationTokenExpiry = undefined;
    await user.save();

    req.flash("success", "Password set successfully. You can now log in.");
    res.redirect("/auth/login");
  } catch (error) {
    console.error("Error setting password:", error);
    req.flash("error", "An error occurred while setting your password");
    res.redirect("/auth/login");
  }
});

// Add a route to resend verification email
router.post("/resend-verification", ensureAuthenticated, async (req, res) => {
  try {
    const { userId } = req.body;
    const user = await User.findById(userId);

    if (!user) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    // Generate new verification token
    user.verificationToken = crypto.randomBytes(20).toString("hex");
    user.verificationTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
    await user.save();

    // Send new verification email
    // ... (reuse email sending logic from above)

    res.json({ success: true, message: "Verification email resent" });
  } catch (error) {
    console.error("Error resending verification:", error);
    res
      .status(500)
      .json({ success: false, message: "Error resending verification email" });
  }
});

// GET route to display the form for assigning additional companies
router.get(
  "/assign-companies/:userId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.userId).populate("companies");

      // Get the current user's company
      const company = req.user.currentCompany
        ? await Company.findById(req.user.currentCompany).lean()
        : null;

      // Fetch only the companies associated with the current user (admin)
      const adminCompanies = await Company.find({
        _id: { $in: req.user.companies },
      });

      // Filter out companies that the user already has
      const availableCompanies = adminCompanies.filter(
        (company) =>
          !user.companies.some(
            (userCompany) =>
              userCompany._id.toString() === company._id.toString()
          )
      );

      res.render("assign-companies", {
        user,
        company,
        allCompanies: availableCompanies,
        currentUser: req.user,
        successMessage: req.flash("success")[0] || "",
        errorMessage: req.flash("error")[0] || "",
      });
    } catch (error) {
      console.error("Error fetching user and companies:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

// POST route to handle assigning additional companies
router.post(
  "/assign-companies/:userId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.userId);
      const { companies } = req.body;

      // Convert companies to an array if it's not already
      const companyIds = Array.isArray(companies) ? companies : [companies];

      // Add new companies to the user's companies array
      user.companies = [...new Set([...user.companies, ...companyIds])];

      await user.save();

      req.flash("success", "Companies assigned successfully");
      res.redirect("/admin/manage-users");
    } catch (error) {
      console.error("Error assigning companies:", error);
      req.flash("error", "Error assigning companies");
      res.redirect(`/admin/assign-companies/${req.params.userId}`);
    }
  }
);

router.get("/defineRFI", adminController.getRFIConfig);
router.post("/defineRFI", adminController.updateRFIConfig);

router.get("/rfi-wizard", adminController.getRFIWizard);
router.post("/rfi-wizard", adminController.postRFIWizard);

router.post("/settings/employee/employee-numbers", async (req, res) => {
  try {
    const {
      employeeNumberMode,
      firstCompanyEmployeeNumber,
      sortEmployeesByNumber,
    } = req.body;
    const company = req.company;

    let settings = await EmployeeNumberSettings.findOne({
      company: company._id,
    });

    if (!settings) {
      settings = new EmployeeNumberSettings({ company: company._id });
    }

    settings.mode = employeeNumberMode;
    settings.firstCompanyEmployeeNumber = firstCompanyEmployeeNumber;
    settings.sortEmployeesByNumber = sortEmployeesByNumber === "on";

    if (employeeNumberMode === "automatic") {
      settings.currentNumber = parseInt(firstCompanyEmployeeNumber, 10) - 1;
    }

    await settings.save();

    res.redirect(
      `/clients/${company.companyCode}/settings/employee/employee-numbers`
    );
  } catch (error) {
    console.error("Error updating employee number settings:", error);
    res.status(500).send("An error occurred while updating settings");
  }
});

// Delete user route
router.post("/delete-user", ensureAuthenticated, async (req, res) => {
  try {
    const { userId } = req.body;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/admin/manage-users");
    }

    // Check if user is trying to delete themselves
    if (userId === req.user._id.toString()) {
      req.flash("error", "You cannot delete your own account");
      return res.redirect("/admin/manage-users");
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    req.flash("success", "User deleted successfully");
    res.redirect("/admin/manage-users");
  } catch (error) {
    console.error("Error deleting user:", error);
    req.flash("error", "Error deleting user");
    res.redirect("/admin/manage-users");
  }
});

module.exports = router;
