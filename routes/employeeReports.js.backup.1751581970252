const express = require('express');
const router = express.Router();
const EmployeeReportAutomation = require('../models/EmployeeReportAutomation');
const { requireAuth } = require('../middleware/auth');

// Get current Employee Report Automation Settings
router.get('/api/employee-report-automation', requireAuth, async (req, res) => {
  try {
    const automationSettings = await EmployeeReportAutomation.findOne({ 
      userId: req.user._id 
    });

    res.status(200).json({ 
      settings: automationSettings || {} 
    });
  } catch (error) {
    console.error('Error fetching employee report automation settings:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve automation settings' 
    });
  }
});

// Save Employee Report Automation Settings
router.post('/api/employee-report-automation', requireAuth, async (req, res) => {
  try {
    const { 
      enabled, 
      frequency, 
      reportTypes, 
      preferredEmail 
    } = req.body;

    // Validate input
    if (enabled && (!frequency || reportTypes.length === 0)) {
      return res.status(400).json({ 
        error: 'Invalid settings. Frequency and report types are required.' 
      });
    }

    // Find or create automation settings for the user
    let automationSettings = await EmployeeReportAutomation.findOne({ 
      userId: req.user._id 
    });

    if (!automationSettings) {
      automationSettings = new EmployeeReportAutomation({
        userId: req.user._id,
        companyId: req.user.companyId
      });
    }

    // Update settings
    automationSettings.enabled = enabled;
    automationSettings.frequency = frequency;
    automationSettings.reportTypes = reportTypes;
    automationSettings.preferredEmail = preferredEmail || req.user.email;
    
    // Calculate next scheduled generation
    if (enabled) {
      automationSettings.nextScheduledGeneration = calculateNextGenerationDate(frequency);
    } else {
      automationSettings.nextScheduledGeneration = null;
    }

    await automationSettings.save();

    res.status(200).json({ 
      message: 'Employee Report Automation Settings Saved',
      settings: automationSettings 
    });
  } catch (error) {
    console.error('Error saving employee report automation settings:', error);
    res.status(500).json({ 
      error: 'Failed to save automation settings' 
    });
  }
});

// Helper function to calculate next generation date
function calculateNextGenerationDate(frequency) {
  const now = new Date();
  switch(frequency) {
    case 'weekly':
      return new Date(now.setDate(now.getDate() + 7));
    case 'biWeekly':
      return new Date(now.setDate(now.getDate() + 14));
    case 'monthly':
      return new Date(now.setMonth(now.getMonth() + 1));
    default:
      return null;
  }
}

module.exports = router;
