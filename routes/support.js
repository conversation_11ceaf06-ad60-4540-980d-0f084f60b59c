const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../middleware/auth");
const nodemailer = require("nodemailer");
const csrf = require("csurf");
const cookieParser = require("cookie-parser");
const Company = require("../models/Company");
const crypto = require('crypto');
const SupportAccess = require('../models/SupportAccess');
const transporter = require('../config/emailConfig');
const { getAppUrl } = require('../utils/url');

// Configure middleware
router.use(cookieParser());
router.use(express.urlencoded({ extended: true }));
router.use(express.json());

// CSRF Protection setup
const csrfProtection = csrf({
  cookie: {
    key: "_csrf",
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  },
});

// Debug middleware for support routes
router.use((req, res, next) => {
  next();
});

// Make CSRF token available to all views
router.use((req, res, next) => {
  res.locals.csrfToken = req.csrfToken();
  next();
});

// Enhanced Navigation Debug Middleware - Add at the top of middleware chain
router.use((req, res, next) => {

  // Enhance redirect to track navigation chain
  const originalRedirect = res.redirect;
  res.redirect = function (url) {

    return originalRedirect.call(this, url);
  };

  next();
});

// Add this before other middleware
router.use((req, res, next) => {

  // Enhanced navigation tracking
  const originalRedirect = res.redirect;
  res.redirect = function (url) {

    // Log the complete navigation state

    return originalRedirect.call(this, url);
  };

  next();
});

// @route   GET /support
// @desc    Display support page
// @access  Private
router.get("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Get the user's current company with full details
    const company = await Company.findById(req.user.currentCompany);

    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/clients");
    }

    // Get active support access tokens
    const activeTokens = await SupportAccess.find({
      company: company._id,
      isRevoked: false,
      expiresAt: { $gt: new Date() }
    }).sort({ createdAt: -1 });


    res.render("support", {
      title: "Support",
      user: req.user,
      company: company,
      currentPath: req.path,
      csrfToken: req.csrfToken(),
      messages: req.flash(),
      activeTokens: activeTokens
    });
  } catch (error) {
    console.error("Error in support route:", error);
    req.flash("error", "Something went wrong");
    return res.redirect("/dashboard");
  }
});

// @route   POST /support/send
// @desc    Handle support message submission
// @access  Private
router.post("/send", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { subject, message } = req.body;
    const user = req.user;
    const company = user.currentCompany?.name || "Not specified";

    // Generate request ID first
    const requestId = `REQ-${Date.now()}`;

    // Log request details

    // Create email content with requestId
    const emailContent = `
Support Request from Panda User

From: ${user.email}
Name: ${user.firstName} ${user.lastName}
Company: ${company}
Subject: ${subject}
Request ID: ${requestId}

Message:
${message}

---
Sent from Panda Support System
Reference: ${requestId}
    `.trim();


    // Check environment variables
    const envDebug = {
      timestamp: new Date().toISOString(),
      hasSmtpPass: !!process.env.SMTP_PASS,
      smtpPassLength: process.env.SMTP_PASS ? process.env.SMTP_PASS.length : 0,
      envKeys: Object.keys(process.env).filter((key) =>
        key.startsWith("SMTP_")
      ),
      nodeEnv: process.env.NODE_ENV,
      envVarsLoaded: !!process.env,
      dotenvPath: process.env.DOTENV_PATH || "Not specified",
      processEnvKeys: Object.keys(process.env).length,
      dotEnvLocation: require("path").resolve(process.cwd(), ".env"),
      envFileContents: require("fs").existsSync(".env")
        ? require("fs")
            .readFileSync(".env", "utf8")
            .split("\n")
            .filter((line) => line.startsWith("SMTP_"))
            .map((line) => line.split("=")[0])
        : [],
    };



    // SMTP Configuration for Zoho
    const smtpConfig = {
      host: process.env.EMAIL_HOST || "smtppro.zoho.com",
      port: parseInt(process.env.EMAIL_PORT || "587"),
      secure: process.env.EMAIL_SECURE === "true",
      auth: {
        user: process.env.EMAIL_USER || "<EMAIL>",
        pass: process.env.EMAIL_PASS,
      },
      debug: true,
      logger: true,
    };

    // Enhanced environment logging

    // Log SMTP configuration (safely)
    const smtpDebug = {
      timestamp: new Date().toISOString(),
      host: smtpConfig.host,
      port: smtpConfig.port,
      user: smtpConfig.auth.user,
      secure: smtpConfig.secure,
      hasPassword: !!smtpConfig.auth.pass,
      passwordLength: smtpConfig.auth.pass ? smtpConfig.auth.pass.length : 0,
      tls: smtpConfig.tls,
      authMethod: "PLAIN",
      requiresAuth: true,
      usingTLS: true,
      zohoSpecific: {
        isZohoHost: smtpConfig.host.includes("zoho"),
        recommendedPort: 587,
        portMatch: smtpConfig.port === 587,
        tlsRecommended: true,
        tlsConfigured: !smtpConfig.secure && !!smtpConfig.tls,
      },
    };

    console.log("3a. SMTP Connection Details:", {
      timestamp: new Date().toISOString(),
      dnsResolved: await new Promise((resolve) => {
        require("dns").resolve(smtpConfig.host, (err, addresses) => {
          resolve({
            success: !err,
            addresses: err ? null : addresses,
            error: err ? err.message : null,
          });
        });
      }),
      portOpen: await new Promise((resolve) => {
        const net = require("net");
        const socket = new net.Socket();
        socket.setTimeout(5000);
        socket.on("connect", () => {
          socket.destroy();
          resolve(true);
        });
        socket.on("timeout", () => {
          socket.destroy();
          resolve(false);
        });
        socket.on("error", () => {
          socket.destroy();
          resolve(false);
        });
        socket.connect(smtpConfig.port, smtpConfig.host);
      }),
    });

    // Create transporter with detailed logging
    const transporter = nodemailer.createTransport(smtpConfig);

    // Add transport event listeners
    transporter.on("token", (token) => {
    });

    transporter.on("log", (log) => {
    });

    // Verify SMTP connection with timeout
    try {
      const verification = await Promise.race([
        transporter.verify(),
        new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("SMTP verification timeout")),
            10000
          )
        ),
      ]);
    } catch (verifyError) {
      console.error("SMTP Verification Failed:", {
        error: verifyError.message,
        code: verifyError.code,
        command: verifyError.command,
        stack: verifyError.stack,
        authData: {
          hasUser: !!smtpConfig.auth.user,
          hasPass: !!smtpConfig.auth.pass,
          userLength: smtpConfig.auth.user?.length,
          passLength: smtpConfig.auth.pass?.length,
        },
      });
      throw verifyError;
    }

    // Prepare email options
    const mailOptions = {
      from: {
        name: "Panda Support",
        address: "<EMAIL>",
      },
      replyTo: {
        name: `${user.firstName} ${user.lastName}`.trim(),
        address: user.email,
      },
      to: "<EMAIL>",
      subject: `[Panda Support] ${subject} (Ref: ${requestId})`,
      text: emailContent,
      headers: {
        "X-Priority": "High",
        "X-Company": company,
        "X-User-Email": user.email,
        "X-User-Name": `${user.firstName} ${user.lastName}`.trim(),
        "X-Support-Request-ID": requestId,
        "X-Session-ID": req.sessionID,
      },
    };

    // Log complete email configuration


    // Send email with timeout
    const info = await Promise.race([
      transporter.sendMail(mailOptions),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Email send timeout")), 30000)
      ),
    ]);


    // Function to send acknowledgment email
    async function sendAcknowledgmentEmail(user, subject, requestId) {

      const acknowledgmentContent = `
Dear ${user.firstName} ${user.lastName},

Thank you for contacting Panda Support. We have received your support request:

Reference Number: ${requestId}
Subject: ${subject}

Our support team has been notified and will review your request shortly. We aim to respond within 24 hours during business days.

Please keep this reference number for future correspondence.

Best regards,
Panda Support Team
`.trim();

      const ackMailOptions = {
        from: {
          name: "Panda Support",
          address: "<EMAIL>",
        },
        to: {
          name: `${user.firstName} ${user.lastName}`.trim(),
          address: user.email,
        },
        subject: `[Panda Support] Request Received - ${requestId}`,
        text: acknowledgmentContent,
        headers: {
          "X-Priority": "High",
          "X-Support-Request-ID": requestId,
          "X-Auto-Response": "true",
          "X-Session-ID": req.sessionID,
        },
      };


      return await transporter.sendMail(ackMailOptions);
    }

    // Send acknowledgment email
    try {
      const ackResult = await sendAcknowledgmentEmail(user, subject, requestId);
    } catch (ackError) {
      console.error("Acknowledgment Email Error:", {
        timestamp: new Date().toISOString(),
        error: {
          name: ackError.name,
          message: ackError.message,
          code: ackError.code,
          command: ackError.command,
        },
        recipient: user.email,
        requestId,
      });
      // Continue execution even if acknowledgment fails
    }

    req.flash(
      "success_msg",
      "Your message has been sent to our support team. You will receive a confirmation email shortly."
    );
    return res.redirect("/dashboard");
  } catch (error) {
    console.error("\n=== Support Email Error ===", {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        code: error.code,
        command: error.command,
        responseCode: error.responseCode,
        response: error.response,
        stack: error.stack,
        authError: error.code === "EAUTH",
        connectionError: error.code === "ECONNECTION",
        timeoutError: error.message.includes("timeout"),
      },
    });

    // Log environment state on error
    console.error("Error Environment State:", {
      timestamp: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
      hasSmtpPass: !!process.env.SMTP_PASS,
      smtpPassLength: process.env.SMTP_PASS ? process.env.SMTP_PASS.length : 0,
    });

    req.flash(
      "error",
      "Failed to send message. <NAME_EMAIL> directly."
    );
    return res.redirect("/dashboard");
  }
});

// @route   POST /support/grant-access
// @desc    Grant temporary support access
// @access  Private
router.post('/grant-access', ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Get company details
    const company = await Company.findById(req.user.currentCompany);
    if (!company) {
      req.flash('error', 'Company not found');
      return res.redirect('/support');
    }

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Calculate expiration (48 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 48);

    // Create new support access record
    const supportAccess = new SupportAccess({
      company: req.user.currentCompany,
      grantedBy: req.user._id,
      token,
      expiresAt
    });

    await supportAccess.save();

    // Generate access URL using environment-aware URL utility
    const accessUrl = getAppUrl(req, `/clients/${company.companyCode}?supportToken=${token}`);

    // Prepare email content
    const emailHtml = `
      <h2>New Support Access Request</h2>
      <p>A client has granted temporary support access to their portal.</p>
      
      <h3>Details:</h3>
      <ul>
        <li><strong>Company:</strong> ${company.name}</li>
        <li><strong>Company Code:</strong> ${company.companyCode}</li>
        <li><strong>Granted By:</strong> ${req.user.firstName} ${req.user.lastName} (${req.user.email})</li>
        <li><strong>Expires:</strong> ${expiresAt.toLocaleString()}</li>
      </ul>

      <h3>Access Instructions:</h3>
      <p>Click the link below or copy it to your browser to access the client's portal:</p>
      <p><a href="${accessUrl}">${accessUrl}</a></p>

      <p style="color: #666; margin-top: 20px;">
        <strong>Note:</strong> This access is read-only and will expire in 48 hours.
      </p>
    `;

    // Send email notification
    await transporter.sendMailWithRetry({
      to: '<EMAIL>',
      subject: `Support Access Granted - ${company.name}`,
      html: emailHtml,
      headers: {
        'X-Priority': 'High',
        'X-Company': company.name,
        'X-Company-Code': company.companyCode,
        'X-Granted-By': req.user.email,
        'X-Access-Type': 'Temporary Support Access',
        'X-Expires': expiresAt.toISOString()
      }
    });

    req.flash('success', 'Support access has been granted for 48 hours');
    res.redirect('/support');
  } catch (error) {
    console.error('Error granting support access:', error);
    req.flash('error', 'Failed to grant support access');
    res.redirect('/support');
  }
});

// @route   POST /support/revoke-access
// @desc    Revoke support access
// @access  Private
router.post('/revoke-access', ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    await SupportAccess.updateMany(
      { company: req.user.currentCompany, isRevoked: false },
      { isRevoked: true }
    );

    req.flash('success', 'Support access has been revoked');
    res.redirect('/support');
  } catch (error) {
    console.error('Error revoking support access:', error);
    req.flash('error', 'Failed to revoke support access');
    res.redirect('/support');
  }
});

// Add this before the 404 handler
router.use((req, res, next) => {

  // Enhanced navigation state tracking
  const originalRedirect = res.redirect;
  res.redirect = function (url) {

    // Log the complete redirect chain

    return originalRedirect.call(this, url);
  };

  next();
});

// Update the 404 handler with more detailed logging
router.use((req, res) => {

  // Instead of redirecting to dashboard, let's redirect to the referrer
  const referrer = req.headers.referer;
  if (referrer && !referrer.includes("/support")) {
    return res.redirect(referrer);
  }

  // If no valid referrer, redirect to dashboard
  return res.redirect("/dashboard");
});

module.exports = router;
