const express = require('express');
const router = express.Router();
const { getAppUrl } = require('../utils/url');

// Test route for URL generation
router.get('/url', (req, res) => {
  const urls = {
    dashboard: getAppUrl(req, 'dashboard'),
    support: getAppUrl(req, 'support'),
    videos: getAppUrl(req, 'videos'),
    current: getAppUrl(req, req.path),
    root: getAppUrl(req, '/'),
    verifyEmail: getAppUrl(req, 'verify-email/some-token'),
    passwordReset: getAppUrl(req, 'reset-password/sample-token-123'),
    productionExample: process.env.NODE_ENV === 'production'
      ? getAppUrl(null, 'dashboard')
      : 'Not in production mode'
  };

  res.json({
    currentEnvironment: process.env.NODE_ENV || 'development',
    baseUrl: process.env.BASE_URL || 'Not set',
    requestInfo: {
      protocol: req.protocol,
      host: req.get('host'),
      originalUrl: req.originalUrl,
      path: req.path
    },
    generatedUrls: urls
  });
});

module.exports = router; 