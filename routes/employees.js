const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const EmployerDetails = require("../models/employerDetails");
const nodemailer = require("nodemailer");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const ensureAuthenticated = require("../config/ensureAuthenticated");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/Company");
const moment = require("moment-timezone");
const { v4: uuidv4 } = require("uuid");
const mongoose = require("mongoose");
const PayFrequency = require("../models/PayFrequency");

// Create a Nodemailer transporter
// const transporter = nodemailer.createTransport({
//   service: "gmail",
//   auth: {
//     user: "<EMAIL>",
//     pass: "gkjk cyrp rfxt fuap",
//   },
// });

// Define a route to render the edit employee form

// Employee Dashboard
router.get("/dashboard", (req, res) =>
  res.render("employeeDashboard", {
    user: req.user,
  })
);

router.post("/self-service", async (req, res) => {
  try {
    const { email, enabled } = req.body;
    const employee = await Employee.findOne({ email });

    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    employee.selfServiceEnabled = enabled === "on";
    await employee.save();

    if (employee.selfServiceEnabled) {
      // Generate token
      const token = jwt.sign(
        { email: employee.email },
        process.env.JWT_SECRET,
        { expiresIn: "1h" }
      );

      // Create setup link
      const setupLink = `${req.protocol}://${req.get(
        "host"
      )}/employee/setup/${token}`;

      // Send email with the setup link
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Profile",
        text: `Please follow the link to set up your profile: ${setupLink}`,
      };

      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.log(error);
        } else {
        }
      });
    }

    res.redirect("/employeeManagement");
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});

// Setup Profile Route
router.get("/setup/:token", async (req, res) => {
  try {
    const { token } = req.params;
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const employee = await Employee.findOne({ email: decoded.email });

    if (!employee) {
      return res.status(404).send("Invalid token or user does not exist");
    }

    res.render("setupProfile", { email: employee.email });
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});

// Complete Profile Setup
router.post("/complete-setup", async (req, res) => {
  try {
    const { email, password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      return res.status(400).send("Passwords do not match");
    }

    const employee = await Employee.findOne({ email });

    if (!employee) {
      return res.status(404).send("User not found");
    }

    // Hash the password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update the employee's password
    employee.password = hashedPassword;
    await employee.save();

    res.redirect("/auth/login");
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});

// router.post("/create", async (req, res) => {
//   try {
//     const { companyId } = req.body;
//     const settings = await EmployeeNumberSettings.findOne({
//       company: companyId,
//     });

//     let companyEmployeeNumber;
//     if (settings && settings.mode === "automatic") {
//       settings.currentNumber += 1;
//       companyEmployeeNumber = settings.currentNumber
//         .toString()
//         .padStart(4, "0");
//       await settings.save();
//     } else {
//       companyEmployeeNumber = req.body.companyEmployeeNumber; // For manual mode
//     }

//     const newEmployee = new Employee({
//       ...req.body,
//       companyEmployeeNumber,
//     });

//     await newEmployee.save();
//     res.status(201).json(newEmployee);
//   } catch (error) {
//     res.status(400).json({ message: error.message });
//   }
// });

// // Create new employee
// router.post("/", async (req, res) => {
//   try {
//     const { companyId, ...employeeData } = req.body;

//     // Find the Company
//     const company = await Company.findById(companyId);
//     if (!company) {
//       return res.status(404).json({ message: "Company not found" });
//     }

//     // Find the EmployerDetails for the given company
//     const employerDetails = await EmployerDetails.findOne({
//       company: companyId,
//     });
//     if (!employerDetails) {
//       return res
//         .status(404)
//         .json({ message: "EmployerDetails not found for the given company" });
//     }

//     const newEmployee = new Employee({
//       ...employeeData,
//       company: companyId,
//       employer: employerDetails._id, // Set the employer field to the EmployerDetails _id
//     });

//     const savedEmployee = await newEmployee.save();
//     res.status(201).json(savedEmployee);
//   } catch (error) {
//     console.error("Error creating employee:", error);
//     res.status(400).json({ message: error.message });
//   }
// });

// // Update employee
// router.put("/:id", async (req, res) => {
//   try {
//     const { companyId, ...updateData } = req.body;

//     if (companyId) {
//       // Find the Company
//       const company = await Company.findById(companyId);
//       if (!company) {
//         return res.status(404).json({ message: "Company not found" });
//       }

//       // Find the EmployerDetails for the given company
//       const employerDetails = await EmployerDetails.findOne({
//         company: companyId,
//       });
//       if (!employerDetails) {
//         return res
//           .status(404)
//           .json({ message: "EmployerDetails not found for the given company" });
//       }

//       updateData.company = companyId;
//       updateData.employer = employerDetails._id; // Update the employer field
//     }

//     const updatedEmployee = await Employee.findByIdAndUpdate(
//       req.params.id,
//       updateData,
//       { new: true }
//     );
//     if (!updatedEmployee) {
//       return res.status(404).json({ message: "Employee not found" });
//     }
//     res.json(updatedEmployee);
//   } catch (error) {
//     console.error("Error updating employee:", error);
//     res
//       .status(500)
//       .json({ message: "Error updating employee", error: error.message });
//   }
// });

// // Get employee with employer details
// router.get("/:id", async (req, res) => {
//   try {
//     const employee = await Employee.findById(req.params.id).populate(
//       "employer"
//     );
//     if (!employee) {
//       return res.status(404).json({ message: "Employee not found" });
//     }
//     res.json(employee);
//   } catch (error) {
//     res.status(500).json({ message: error.message });
//   }
// });

// router.get("/download/:employeeId/:month", async (req, res) => {
//   const { employeeId, month } = req.params;

//   try {
//     const employee = await Employee.findById(employeeId);
//     if (!employee) {
//       return res.status(404).send("Employee not found");
//     }

//     let employerDetails;
//     if (employee.employer) {
//       employerDetails = await EmployerDetails.findById(employee.employer);
//     } else {
//       employerDetails = await EmployerDetails.findOne({
//         company: employee.company,
//       });
//       if (employerDetails) {
//         employee.employer = employerDetails._id;
//         await employee.save();
//       }
//     }

//     if (!employerDetails) {
//       
//       employerDetails = { tradingName: "Company Name" }; // Default fallback
//     }

//     // ... (rest of the code remains the same)
//   } catch (err) {
//     console.error("Error in payslip generation:", err);
//     res.status(500).send("An error occurred while generating the payslip.");
//   }
// });

// router.post("/employer-details", async (req, res) => {
//   try {
//     const { isNewCompany, companyId, tradingName, ...otherDetails } = req.body;

//     if (isNewCompany === "true") {
//       // Create a new company
//       const newCompany = new Company({
//         name: tradingName,
//         ...otherDetails,
//       });
//       await newCompany.save();
//       req.flash("success", "New company created successfully");
//     } else {
//       // Update existing company
//       await Company.findByIdAndUpdate(companyId, {
//         name: tradingName,
//         ...otherDetails,
//       });
//       req.flash("success", "Company details updated successfully");
//     }

//     res.redirect("/settings?tab=employerDetails");
//   } catch (error) {
//     console.error("Error processing employer details:", error);
//     req.flash("error", "An error occurred while processing your request");
//     res.redirect("/settings?tab=employerDetails");
//   }
// });

// router.get(
//   "/:companyCode/settings/:mainTab?/:subTab?",
//   ensureAuthenticated,
//   async (req, res) => {
//     try {
//       const { companyCode, mainTab, subTab } = req.params;
//       const { newCompany } = req.query; // Get newCompany from query parameters
//       const company = await Company.findOne({ companyCode });

//       if (!company) {
//         return res
//           .status(404)
//           .render("error", { message: "Company not found" });
//       }

//       // If no mainTab is provided, render the main settings page
//       if (!mainTab) {
//         return res.render("settings/index", {
//           company,
//           activeTab: "",
//           newCompany,
//         });
//       }

//       let viewName;
//       let additionalData = { newCompany }; // Include newCompany in additionalData

//       switch (mainTab) {
//         case "accounting":
//         case "employee":
//         case "payroll":
//         case "other":
//           if (!subTab) {
//             viewName = `settings/${mainTab}`;
//           } else {
//             viewName = `settings/${subTab}`;
//             // Add logic here to fetch any necessary data for the sub-tab
//             switch (subTab) {
//               case "employer-details":
//                 let employerDetails = await EmployerDetails.findOne({
//                   company: company._id,
//                 });
//                 // If no employer details exist, create an empty object with default values
//                 if (!employerDetails) {
//                   employerDetails = {
//                     tradingName: "",
//                     physicalAddress: {
//                       unitNumber: "",
//                       complex: "",
//                       streetNumber: "",
//                       street: "",
//                       suburbDistrict: "",
//                       cityTown: "",
//                       code: "",
//                     },
//                     postalAddress: {
//                       line1: "",
//                       line2: "",
//                     },
//                     logo: "",
//                   };
//                 }
//                 const isNewCompany =
//                   newCompany === "true" || !employerDetails._id;
//                 additionalData = {
//                   ...additionalData,
//                   employerDetails,
//                   isNewCompany,
//                 };
//                 break;
//               // ... (other cases remain unchanged)
//             }
//           }
//           break;
//         default:
//           return res.status(404).render("error", { message: "Tab not found" });
//       }

//       res.render(viewName, {
//         company,
//         activeTab: mainTab,
//         activeSubTab: subTab || "",
//         ...additionalData,
//       });
//     } catch (error) {
//       console.error(`Error rendering settings:`, error);
//       res.status(500).render("error", { message: "Error loading settings" });
//     }
//   }
// );

router.post(
  "/clients/:companyCode/employeeProfile/:employeeId/edit",
  async (req, res) => {
    try {
      const company = await Company.findOne({
        companyCode: req.params.companyCode,
      });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findById(req.params.employeeId);
      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Handle personalDetails fields
      if (!employee.personalDetails) {
        employee.personalDetails = {};
      }
      
      // Update personalDetails.mobileNumber if it exists in the request
      if (req.body['personalDetails.mobileNumber'] !== undefined) {
        employee.personalDetails.mobileNumber = req.body['personalDetails.mobileNumber'];
      }

      // Update other employee fields
      const { 'personalDetails.mobileNumber': mobileNumber, ...otherFields } = req.body;
      Object.assign(employee, otherFields);

      // Don't allow changing the companyEmployeeNumber in edit mode
      delete employee.companyEmployeeNumber;

      await employee.save();
      req.flash('success', 'Employee updated successfully');
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${employee._id}`
      );
    } catch (error) {
      console.error("Error updating employee:", error);
      req.flash('error', 'Error updating employee');
      res.redirect('back');
    }
  }
);

// router.post(
//   "/clients/:companyCode/employeeManagement/addNew/submit",
//   async (req, res) => {
//     const session = await mongoose.startSession();
//     session.startTransaction();

//     try {
//       const companyCode = req.params.companyCode;
//       const company = await Company.findOne({ companyCode }).session(session);

//       if (!company) {
//         throw new Error("Company not found");
//       }

//       const employeeData = req.body;

//       const settings = await EmployeeNumberSettings.findOne({
//         company: company._id,
//       }).session(session);

//       if (!settings) {
//         throw new Error("Employee number settings not found");
//       }

//       if (settings.mode === "manual") {
//         if (!employeeData.companyEmployeeNumber) {
//           throw new Error("Employee number is required in manual mode");
//         }
//         // Check for uniqueness in manual mode
//         const existingEmployee = await Employee.findOne({
//           company: company._id,
//           companyEmployeeNumber: employeeData.companyEmployeeNumber,
//         }).session(session);

//         if (existingEmployee) {
//           throw new Error("Employee number already exists");
//         }
//       } else {
//         // Automatic mode
//         employeeData.companyEmployeeNumber = await generateEmployeeNumber(
//           company,
//           session
//         );
//       }

//       const newEmployee = new Employee({
//         ...employeeData,
//         globalEmployeeId: uuidv4(),
//         company: company._id,
//       });

//       await newEmployee.save({ session });
//       await session.commitTransaction();

//       res.status(200).json({
//         message: "Employee added successfully",
//         employeeId: newEmployee._id,
//       });
//     } catch (error) {
//       await session.abortTransaction();
//       console.error("Error adding new employee:", error);
//       res.status(500).json({
//         error: error.message || "An error occurred while adding the employee",
//       });
//     } finally {
//       session.endSession();
//     }
//   }
// );

// async function generateEmployeeNumber(company, session) {
//   const settings = await EmployeeNumberSettings.findOne({
//     company: company._id,
//   }).session(session);

//   if (!settings) {
//     throw new Error("Employee number settings not found");
//   }

//   settings.lastGeneratedNumber += 1;
//   await settings.save({ session });

//   const paddedNumber = settings.lastGeneratedNumber.toString().padStart(4, "0");
//   return settings.numberPattern.replace("{0000}", paddedNumber);
// }

router.get("/new", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.company);
    const payFrequencies = await PayFrequency.find({ company: company._id });
    res.render("addNewEmployee", { company, payFrequencies });
  } catch (error) {
    console.error("Error fetching pay frequencies:", error);
    req.flash("error", "Error loading pay frequencies");
    res.redirect("/dashboard");
  }
});

router.post("/new", ensureAuthenticated, async (req, res) => {
  try {
    const { payFrequency, ...otherFields } = req.body;
    const selectedPayFrequency = await PayFrequency.findById(payFrequency);

    if (!selectedPayFrequency) {
      throw new Error("Invalid pay frequency selected");
    }

    const newEmployee = new Employee({
      ...otherFields,
      payFrequency: selectedPayFrequency._id,
      company: req.user.company,
    });

    await newEmployee.save();

    req.flash("success", "Employee added successfully");
    res.redirect("/employees");
  } catch (error) {
    console.error("Error adding new employee:", error);
    req.flash("error", "Error adding new employee");
    res.redirect("/employees/new");
  }
});

// Update OID eligibility
router.patch('/:id/oid-eligibility', ensureAuthenticated, async (req, res) => {
  try {
    const { oidEligible, oidExemptReason } = req.body;
    
    // Validate the request
    if (typeof oidEligible !== 'boolean') {
      return res.status(400).json({ message: 'OID eligibility must be a boolean value' });
    }
    
    if (!oidEligible && (!oidExemptReason || !oidExemptReason.trim())) {
      return res.status(400).json({ message: 'Exempt reason is required when marking employee as not eligible' });
    }

    const employee = await Employee.findOneAndUpdate(
      { _id: req.params.id, company: req.user.currentCompany },
      { 
        oidEligible,
        oidExemptReason: oidEligible ? null : oidExemptReason 
      },
      { new: true, runValidators: true }
    );

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    res.json({ 
      message: 'OID eligibility updated successfully',
      oidEligible: employee.oidEligible,
      oidExemptReason: employee.oidExemptReason
    });
  } catch (error) {
    console.error('Error updating OID eligibility:', error);
    res.status(500).json({ message: 'Error updating OID eligibility' });
  }
});

// Update employee basic information
router.post('/:id/update', ensureAuthenticated, async (req, res) => {
    const employeeController = require('./employeeController');
    await employeeController.updateEmployeeBasicInfo(req, res);
});

module.exports = router;
