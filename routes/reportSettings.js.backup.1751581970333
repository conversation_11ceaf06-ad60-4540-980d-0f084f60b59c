const express = require("express");
const router = express.Router({ mergeParams: true });
const ReportSettings = require("../models/ReportSettings");
const Company = require("../models/Company");
const ReportEmailService = require("../services/reportEmailService");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });

// GET /clients/:companyCode/reporting/settings
router.get("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log("\n=== Report Settings GET Route ===");
  console.log("Company Code:", req.params.companyCode);
  console.log("User:", req.user?._id);
  console.log("CSRF Token:", req.csrfToken?.());

  try {
    const { companyCode } = req.params;
    const company = await Company.findOne({ companyCode });

    if (!company) {
      console.log("Company not found:", companyCode);
      return res.status(404).json({
        success: false,
        message: "Company not found",
      });
    }

    console.log("Found company:", company._id);

    let settings = await ReportSettings.findOne({ company: company._id });
    console.log("Existing settings:", settings ? "Found" : "Not found");

    if (!settings) {
      settings = new ReportSettings({
        company: company._id,
        defaultFormat: "pdf",
        dateRangePresets: {
          payrollReports: { default: "currentMonth" },
          employeeReports: { default: "allTime" },
        },
      });
      await settings.save();
      console.log("Created new settings");
    }

    res.json({
      success: true,
      settings,
    });
  } catch (error) {
    console.error("Error in report settings GET:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch settings",
      error: error.message,
    });
  }
});

// POST /clients/:companyCode/reporting/settings
router.post("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  console.log("\n=== Report Settings POST Route ===");
  console.log("Company Code:", req.params.companyCode);
  console.log("User:", req.user?._id);
  console.log("Request Body:", req.body);
  console.log("CSRF Token:", req.csrfToken?.());

  try {
    const { companyCode } = req.params;
    const company = await Company.findOne({ companyCode });

    if (!company) {
      console.log("Company not found:", companyCode);
      return res.status(404).json({
        success: false,
        message: "Company not found",
      });
    }

    console.log("Found company:", company._id);

    // Update or create settings
    const settings = await ReportSettings.findOneAndUpdate(
      { company: company._id },
      {
        ...req.body,
        company: company._id,
        updatedAt: new Date(),
      },
      { new: true, upsert: true }
    );

    console.log("Settings saved:", settings);

    res.json({
      success: true,
      message: "Settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Error in report settings POST:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update settings",
      error: error.message,
    });
  }
});

// Test email automation endpoint
router.post("/:companyCode/test-email", ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;
    const { testEmail } = req.body;

    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: "Test email address is required",
      });
    }

    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found",
      });
    }

    const result = await ReportEmailService.sendTestEmail(company._id, testEmail);

    res.json({
      success: true,
      message: "Test email sent successfully",
      result,
    });
  } catch (error) {
    console.error("Error sending test email:", error);
    res.status(500).json({
      success: false,
      message: "Failed to send test email",
      error: error.message,
    });
  }
});

// Get email automation status endpoint
router.get("/:companyCode/email-status", ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;

    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found",
      });
    }

    const status = await ReportEmailService.getEmailAutomationStatus(company._id);

    res.json({
      success: true,
      status,
    });
  } catch (error) {
    console.error("Error getting email automation status:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get email automation status",
      error: error.message,
    });
  }
});

module.exports = router;
