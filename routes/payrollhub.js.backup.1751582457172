const express = require("express");
const router = express.Router({ mergeParams: true });
const mongoose = require("mongoose");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrfProtection = require("../middleware/csrfProtection");
const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const PayRun = require("../models/payRun");
const Payroll = require("../models/Payroll");
const PayrollService = require("../services/PayrollService");
const AuditLog = require("../models/auditLog");
const payrollCalculations = require("../utils/payrollCalculations");
const Integration = require("../models/Integration");
const XeroAccountMapping = require("../models/xeroAccountMapping");
const { generateFNBBankFile } = require("../utils/bankFileGenerator");

// Helper function to format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
  }).format(amount || 0);
};

// Helper function to format pay period
const formatPayPeriod = (startDate, endDate) => {
  if (!startDate || !endDate) return "N/A";
  return `${moment(startDate).format("DD MMM")} - ${moment(endDate).format(
    "DD MMM YYYY"
  )}`;
};

// Helper function to calculate deductions
const calculateDeductions = (payslip) => {
  let totalDeductions = 0;
  const uif = Math.min((payslip.basicSalary || 0) * 0.01, 177.12);
  totalDeductions += uif;

  const annualSalary = (payslip.basicSalary || 0) * 12;
  let paye = 0;

  const TAX_BRACKETS = [
    { threshold: 0, rate: 0.18, base: 0 },
    { threshold: 237100, rate: 0.26, base: 42678 },
    { threshold: 370500, rate: 0.31, base: 77362 },
    { threshold: 512800, rate: 0.36, base: 121475 },
    { threshold: 673000, rate: 0.39, base: 179147 },
    { threshold: 857900, rate: 0.41, base: 251258 },
    { threshold: 1817000, rate: 0.45, base: 644489 },
  ];

  let bracket = TAX_BRACKETS[0];
  for (let i = 1; i < TAX_BRACKETS.length; i++) {
    if (annualSalary >= TAX_BRACKETS[i].threshold) {
      bracket = TAX_BRACKETS[i];
    } else {
      break;
    }
  }

  const annualTax =
    bracket.base + bracket.rate * (annualSalary - bracket.threshold);
  const primaryRebate = 17235;
  const annualTaxAfterRebate = Math.max(0, annualTax - primaryRebate);
  paye = annualTaxAfterRebate / 12;
  totalDeductions += paye;

  if (payslip.pensionFund) totalDeductions += payslip.pensionFund;
  if (payslip.providentFund) totalDeductions += payslip.providentFund;
  if (payslip.retirementAnnuity) totalDeductions += payslip.retirementAnnuity;
  if (payslip.garnishee) totalDeductions += payslip.garnishee;
  if (payslip.maintenanceOrder) totalDeductions += payslip.maintenanceOrder;
  if (payslip.medical && payslip.medical.medicalAid)
    totalDeductions += payslip.medical.medicalAid;

  payslip.calculations = {
    deductions: {
      statutory: { paye, uif },
    },
  };
  return totalDeductions;
};

// Helper function to calculate gross income
const calculateGrossIncome = (payslip) => {
  let totalIncome = payslip.basicSalary || 0;
  if (payslip.commission) totalIncome += payslip.commission;
  if (payslip.lossOfIncome) totalIncome += payslip.lossOfIncome;
  if (payslip.travelAllowance && payslip.travelAllowance.fixedAllowanceAmount) {
    totalIncome += payslip.travelAllowance.fixedAllowanceAmount;
  }
  return totalIncome;
};

// Helper function to get progress status icon
const getProgressStatusIcon = (status) => {
  switch (status.toLowerCase()) {
    case "draft":
    case "pending":
      return "ph-clock";
    case "processing":
      return "ph-spinner";
    case "finalized":
      return "ph-check-circle";
    case "released":
      return "ph-upload";
    default:
      return "ph-circle";
  }
};

// Helper function to calculate period statistics
const calculatePeriodStats = (periods = []) => {
  if (!Array.isArray(periods))
    return {
      totalCount: 0,
      finalizedCount: 0,
      unfinalizedCount: 0,
      totalBasicSalary: 0,
      employeeCount: 0,
    };

  return {
    totalCount: periods.length,
    finalizedCount: periods.filter((p) => p && p.isFinalized).length,
    unfinalizedCount: periods.filter((p) => p && !p.isFinalized).length,
    totalBasicSalary: periods.reduce(
      (sum, p) => sum + (p && p.basicSalary ? p.basicSalary : 0),
      0
    ),
    employeeCount: new Set(
      periods
        .filter((p) => p && p.employee && p.employee._id)
        .map((p) => p.employee._id.toString())
    ).size,
  };
};

// Helper function to group periods by month and frequency
const groupPeriodsByMonthAndFrequency = (periods = []) => {
  console.log("=== Debug: Grouping Periods ===");
  console.log("Input periods:", periods.length);

  if (!Array.isArray(periods)) {
    console.log("Error: Periods is not an array");
    return {};
  }

  const grouped = {};

  periods.forEach((period) => {
    if (!period || !period.endDate || !period.frequency) {
      console.log("Skipping invalid period:", period);
      return;
    }

    const frequency = period.frequency.toLowerCase();
    const monthYear = moment(period.endDate).format("YYYY-MM");

    console.log(
      `Processing period - Frequency: ${frequency}, Month: ${monthYear}`
    );

    if (!grouped[frequency]) {
      grouped[frequency] = {};
    }
    if (!grouped[frequency][monthYear]) {
      grouped[frequency][monthYear] = {
        periods: [],
        stats: null,
      };
    }

    grouped[frequency][monthYear].periods.push(period);
  });

  // Calculate stats for each group
  Object.keys(grouped).forEach((frequency) => {
    console.log(`Processing frequency: ${frequency}`);
    Object.keys(grouped[frequency]).forEach((monthYear) => {
      console.log(`Calculating stats for ${frequency} - ${monthYear}`);
      grouped[frequency][monthYear].stats = calculatePeriodStats(
        grouped[frequency][monthYear].periods
      );
      console.log("Stats:", grouped[frequency][monthYear].stats);
    });
  });

  console.log("Final grouped structure:", JSON.stringify(grouped, null, 2));
  return grouped;
};

// Helper function to calculate total taxable income
function calculateTotalTaxableIncome(payrollData) {
  console.log("\n=== Calculating Total Taxable Income ===");
  console.log("Input payroll data:", payrollData);

  const basicSalary = Number(payrollData.basicSalary) || 0;
  const commission = Number(payrollData.commission) || 0;
  const travelAllowance = payrollData.travelAllowance?.amount
    ? Number(payrollData.travelAllowance.amount)
    : 0;

  console.log("Processed values:", {
    basicSalary,
    commission,
    travelAllowance,
  });

  const totalTaxableIncome = basicSalary + commission + travelAllowance;

  console.log("Total taxable income calculated:", totalTaxableIncome);
  return totalTaxableIncome;
}

// Helper function to calculate total deductions
function calculateTotalDeductions(
  payrollData,
  taxableIncome,
  frequency,
  startDate,
  endDate
) {
  console.log("\n=== Calculating Total Deductions ===");
  console.log("Input parameters:", {
    payrollData,
    taxableIncome,
    frequency,
    startDate,
    endDate,
  });

  try {
    // Initialize deductions object
    const deductions = {
      statutory: {
        paye: 0,
        uif: 0,
      },
      nonStatutory: {
        medicalAid: 0,
        pensionFund: 0,
        retirementAnnuity: 0,
      },
    };

    // Calculate PAYE if applicable
    if (taxableIncome > 0) {
      console.log("Calculating PAYE for taxable income:", taxableIncome);
      // Assuming monthly frequency for now
      const annualTaxableIncome =
        frequency === "monthly" ? taxableIncome * 12 : taxableIncome;
      deductions.statutory.paye = calculatePAYE(annualTaxableIncome, frequency);
      console.log("Calculated PAYE:", deductions.statutory.paye);
    }

    // Calculate UIF
    if (!payrollData.employee?.isUifExempt) {
      console.log("Calculating UIF");
      const uifableIncome = payrollData.basicSalary || 0;
      deductions.statutory.uif = Math.min(uifableIncome * 0.01, 148.72); // 1% of income, capped at R148.72
      console.log("Calculated UIF:", deductions.statutory.uif);
    }

    // Calculate non-statutory deductions
    if (payrollData.medicalAid?.amount) {
      deductions.nonStatutory.medicalAid = Number(
        payrollData.medicalAid.amount
      );
    }
    if (payrollData.pensionFund?.amount) {
      deductions.nonStatutory.pensionFund = Number(
        payrollData.pensionFund.amount
      );
    }
    if (payrollData.retirementAnnuityFund?.amount) {
      deductions.nonStatutory.retirementAnnuity = Number(
        payrollData.retirementAnnuityFund.amount
      );
    }

    console.log("Non-statutory deductions:", deductions.nonStatutory);

    // Calculate total deductions
    const totalDeductions =
      Object.values(deductions.statutory).reduce((a, b) => a + b, 0) +
      Object.values(deductions.nonStatutory).reduce((a, b) => a + b, 0);

    console.log("Total deductions calculated:", totalDeductions);
    return totalDeductions;
  } catch (error) {
    console.error("Error calculating deductions:", error);
    throw error;
  }
}

// Helper function to calculate PAYE
function calculatePAYE(annualTaxableIncome, frequency) {
  console.log("\n=== Calculating PAYE ===");
  console.log("Annual taxable income:", annualTaxableIncome);
  console.log("Frequency:", frequency);

  // 2024/2025 tax brackets
  const taxBrackets = [
    { threshold: 0, rate: 0.18, base: 0 },
    { threshold: 237100, rate: 0.26, base: 42678 },
    { threshold: 370500, rate: 0.31, base: 77362 },
    { threshold: 512800, rate: 0.36, base: 121475 },
    { threshold: 673000, rate: 0.39, base: 179147 },
    { threshold: 857900, rate: 0.41, base: 251258 },
    { threshold: 1817000, rate: 0.45, base: 644489 },
  ];

  // Find applicable tax bracket
  let bracket = taxBrackets[0];
  for (let i = 1; i < taxBrackets.length; i++) {
    if (annualTaxableIncome >= taxBrackets[i].threshold) {
      bracket = taxBrackets[i];
    } else {
      break;
    }
  }

  console.log("Applicable tax bracket:", bracket);

  // Calculate annual tax
  const annualTax =
    bracket.base + bracket.rate * (annualTaxableIncome - bracket.threshold);
  console.log("Annual tax before rebates:", annualTax);

  // Apply primary rebate
  const primaryRebate = 17235;
  const annualTaxAfterRebate = Math.max(0, annualTax - primaryRebate);
  console.log("Annual tax after primary rebate:", annualTaxAfterRebate);

  // Convert to period amount based on frequency
  const periodTax =
    frequency === "monthly" ? annualTaxAfterRebate / 12 : annualTaxAfterRebate;
  console.log("Period tax amount:", periodTax);

  return periodTax;
}

// UIF calculation function
const calculateUIF = ({ salary, employee, startDate, endDate }) => {
  console.log("\n=== UIF Calculation Start ===");
  console.log("Input:", {
    salary,
    employeeId: employee?.id,
    startDate,
    endDate,
  });

  try {
    if (!salary || !employee) {
      console.log("Missing required parameters for UIF calculation");
      return 0;
    }

    // Check if employee is UIF exempt
    if (employee.isUifExempt) {
      console.log("Employee is UIF exempt");
      return 0;
    }

    // UIF is 1% of salary, capped at R148.72 per month
    const UIF_RATE = 0.01;
    const UIF_CAP = 148.72;

    const uifAmount = Math.min(salary * UIF_RATE, UIF_CAP);
    console.log("Calculated UIF amount:", uifAmount);

    return uifAmount;
  } catch (error) {
    console.error("Error calculating UIF:", error);
    return 0;
  }
};

// PAYE calculation function with improved logging
const calculatePAYEImproved = ({
  annualSalary,
  frequency = "monthly",
  proRataPercentage = 100,
}) => {
  console.log("\n=== PAYE Calculation Start ===");
  console.log("Input parameters:", {
    annualSalary,
    frequency,
    proRataPercentage,
  });

  try {
    // Validate inputs
    if (typeof annualSalary !== "number" || annualSalary < 0) {
      console.log("Invalid annual salary:", annualSalary);
      return 0;
    }

    if (
      typeof proRataPercentage !== "number" ||
      proRataPercentage < 0 ||
      proRataPercentage > 100
    ) {
      console.log("Invalid pro-rata percentage:", proRataPercentage);
      proRataPercentage = 100;
    }

    // Tax brackets for 2024/2025
    const TAX_BRACKETS = [
      { threshold: 0, rate: 0.18, base: 0, nextBracketThreshold: 237100 },
      {
        threshold: 237101,
        rate: 0.26,
        base: 42678,
        nextBracketThreshold: 370500,
      },
      {
        threshold: 370501,
        rate: 0.31,
        base: 77362,
        nextBracketThreshold: 512800,
      },
      {
        threshold: 512801,
        rate: 0.36,
        base: 121475,
        nextBracketThreshold: 673000,
      },
      {
        threshold: 673001,
        rate: 0.39,
        base: 179147,
        nextBracketThreshold: 857900,
      },
      {
        threshold: 857901,
        rate: 0.41,
        base: 251258,
        nextBracketThreshold: 1817000,
      },
      {
        threshold: 1817001,
        rate: 0.45,
        base: 644489,
        nextBracketThreshold: Infinity,
      },
    ];

    // Find applicable tax bracket
    const bracket =
      TAX_BRACKETS.find(
        (b) =>
          annualSalary >= b.threshold && annualSalary < b.nextBracketThreshold
      ) || TAX_BRACKETS[0];

    console.log("Applicable tax bracket:", bracket);

    // Calculate annual tax
    const annualTaxBeforeRebate =
      bracket.base + bracket.rate * (annualSalary - bracket.threshold);
    console.log("Annual tax before rebate:", annualTaxBeforeRebate);

    // Apply primary rebate
    const PRIMARY_REBATE = 17235;
    const annualTaxAfterRebate = Math.max(
      0,
      annualTaxBeforeRebate - PRIMARY_REBATE
    );
    console.log("Annual tax after primary rebate:", annualTaxAfterRebate);

    // Convert to period amount based on frequency
    let periodDivisor = 12; // monthly
    if (frequency === "weekly") periodDivisor = 52;
    if (frequency === "biweekly") periodDivisor = 26;

    const periodTax =
      (annualTaxAfterRebate / periodDivisor) * (proRataPercentage / 100);
    console.log("Period tax amount:", periodTax);

    return Math.round(periodTax * 100) / 100;
  } catch (error) {
    console.error("Error calculating PAYE:", error);
    return 0;
  }
};

// Add debug logging for payroll data processing
const processPayrollData = async (payslip) => {
  console.log("=== Processing Payroll Data ===");
  console.log("Input payslip:", {
    id: payslip?._id,
    employeeId: payslip?.employee?._id,
    startDate: payslip?.startDate,
    endDate: payslip?.endDate,
    frequency: payslip?.frequency,
  });

  if (!payslip) {
    console.log("No payslip data to process");
    return null;
  }

  try {
    // Get employee details
    const employee = payslip.employee;
    if (!employee) {
      console.log("No employee data found in payslip");
      return null;
    }

    console.log("Processing employee:", {
      id: employee._id,
      name:
        `${employee.firstName || ""} ${employee.lastName || ""}`.trim() ||
        "Unknown Employee",
    });

    // Calculate payroll
    const calculations = await calculatePayroll({
      employee: employee._id,
      startDate: payslip.startDate,
      endDate: payslip.endDate,
      frequency: payslip.frequency,
    });

    console.log("Payroll calculations:", {
      basicSalary: calculations?.basicSalary,
      totalDeductions: calculations?.totalDeductions,
      netPay: calculations?.netPay,
    });

    return {
      ...payslip,
      calculations,
    };
  } catch (error) {
    console.error("Error processing payroll data:", error);
    return null;
  }
};

// Process pending payslips
const processPendingPayslips = async (payslips) => {
  console.log("=== Processing Pending Payslips ===");
  console.log("Number of payslips:", payslips?.length || 0);

  if (!payslips || !Array.isArray(payslips)) {
    console.log("No payslips to process");
    return [];
  }

  const processedPayslips = [];
  for (const payslip of payslips) {
    try {
      const processedPayslip = await processPayrollData(payslip);
      if (processedPayslip) {
        const employee = processedPayslip.employee;
        const employeeName = employee
          ? `${employee.firstName || ""} ${employee.lastName || ""}`.trim() ||
            "Unknown Employee"
          : "Unknown Employee";

        const calculations = processedPayslip.calculations || {};
        processedPayslips.push({
          employee: employeeName,
          netPay: calculations.netPay || 0,
          totalDeductions: calculations.totalDeductions || 0,
        });
      }
    } catch (error) {
      console.error("Error processing payslip:", error);
    }
  }

  console.log("Processed payslips:", processedPayslips);
  return processedPayslips;
};

// Debug middleware for all payroll hub routes
router.use((req, res, next) => {
  console.log("\n=== Payroll Hub Debug Middleware ===");
  console.log({
    originalUrl: req.originalUrl,
    baseUrl: req.baseUrl,
    path: req.path,
    params: req.params,
    query: req.query,
    user: req.user?._id,
    isAuthenticated: req.isAuthenticated(),
  });
  next();
});

// GET route for payroll hub - Make more specific to avoid catching other routes
router.get(
  "/:companyCode/payrollhub",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Payroll Hub Route Handler Start ===");
    console.log("Request details:", {
      path: req.path,
      method: req.method,
      companyCode: req.params.companyCode,
      userId: req.user._id,
    });

    try {
      // Get company code from params
      const { companyCode } = req.params;
      console.log("=== Company Code Debug ===", {
        companyCode,
        baseUrl: req.baseUrl,
        originalUrl: req.originalUrl,
      });

      // Find company
      console.log("Looking up company:", companyCode);
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Check for Xero integration
      const xeroIntegration = await Integration.findOne({
        company: company._id,
        provider: "xero",
        type: "accounting",
        status: "active",
      });

      console.log("Found company:", {
        id: company._id,
        code: company.companyCode,
        name: company.name,
      });

      // UPDATED: Fetch active payroll periods for the 2-step workflow
      // This includes periods that are "unfinished" or "active" in the payroll process
      console.log("Fetching active payroll periods...");

      // Step 1: Get periods that are still being reviewed (open or processing, not finalized)
      const reviewingPeriods = await PayrollPeriod.find({
        company: company._id,
        $or: [
          { status: "open", isFinalized: false },
          { status: "processing", isFinalized: false }
        ]
      })
        .populate({
          path: "employee",
          select: "firstName lastName payFrequency isUifExempt uifExemptReason",
        })
        .lean();

      // Step 2: Get periods that are finalized but don't have completed pay runs yet
      console.log("Fetching finalized periods without completed pay runs...");
      const finalizedPayslips = await PayrollPeriod.find({
        company: company._id,
        $or: [
          { status: "open", isFinalized: true },
          { status: "processing", isFinalized: true },
          { status: "finalized", isFinalized: true }
        ]
      })
        .populate({
          path: "employee",
          select: "firstName lastName payFrequency isUifExempt uifExemptReason",
        })
        .lean();

      // Combine both sets to create the complete active periods dataset
      // This represents all periods that are active in the 2-step workflow
      const pendingPayslips = [...reviewingPeriods, ...finalizedPayslips];

      console.log("Active periods summary:", {
        reviewingPeriods: reviewingPeriods.length,
        finalizedPayslips: finalizedPayslips.length,
        totalActivePeriods: pendingPayslips.length,
        statusBreakdown: {
          open: pendingPayslips.filter(p => p.status === 'open').length,
          processing: pendingPayslips.filter(p => p.status === 'processing').length,
          finalized: pendingPayslips.filter(p => p.status === 'finalized').length,
          finalized_count: pendingPayslips.filter(p => p.isFinalized === true).length,
          unfinalized_count: pendingPayslips.filter(p => p.isFinalized === false).length
        }
      });

      // Remove duplicates from the combined dataset (in case a period appears in both queries)
      const uniquePendingPayslips = pendingPayslips.filter((period, index, self) =>
        index === self.findIndex(p => p._id.toString() === period._id.toString())
      );

      console.log(`Found ${uniquePendingPayslips.length} unique active periods for progress tracker`);

      // Get all pay runs for the company
      const page = parseInt(req.query.page) || 1;
      const limit = 10;
      const skip = (page - 1) * limit;

      // Count total pay runs for pagination
      const totalPayRuns = await PayRun.countDocuments({ company: company._id });
      const totalPages = Math.ceil(totalPayRuns / limit);

      // Get pay runs with pagination
      const allPayRuns = await PayRun.find({ company: company._id })
        .select({
          startDate: 1,
          endDate: 1,
          status: 1,
          payslips: 1,
          totals: 1,
          totalAmount: 1,
          xeroSynced: 1,
          bankFile: 1,
          frequency: 1,
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      // Create pagination object
      const pagination = {
        page,
        limit,
        totalPages,
        totalItems: totalPayRuns,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      };

      // Calculate overall stats for each frequency
      const overallStats = {
        weekly: { unfinalizedCount: 0 },
        biweekly: { unfinalizedCount: 0 },
        monthly: { unfinalizedCount: 0 },
      };

      // Group payslips by frequency using the unique active periods
      const payslipsByFrequency = {};
      uniquePendingPayslips.forEach((payslip) => {
        const freq = payslip.frequency;
        if (!payslipsByFrequency[freq]) {
          payslipsByFrequency[freq] = [];
        }
        payslipsByFrequency[freq].push(payslip);
        // Only count unfinalized periods in the stats
        if (!payslip.isFinalized) {
          overallStats[freq].unfinalizedCount++;
        }
      });

      // Get pay runs grouped by frequency
      const payRunsByFrequency = {};
      allPayRuns.forEach((payRun) => {
        if (!payRunsByFrequency[payRun.frequency]) {
          payRunsByFrequency[payRun.frequency] = payRun;
        }
      });

      // IMPORTANT: Filter out periods that already have completed pay runs
      // This ensures we only show truly "active" periods in the 2-step workflow
      const activePeriodsOnly = uniquePendingPayslips.filter(period => {
        // Check if there's a completed pay run for this period
        const hasCompletedPayRun = allPayRuns.some(payRun => {
          const periodStart = new Date(period.startDate).toISOString().split('T')[0];
          const periodEnd = new Date(period.endDate).toISOString().split('T')[0];
          const payRunStart = new Date(payRun.startDate).toISOString().split('T')[0];
          const payRunEnd = new Date(payRun.endDate).toISOString().split('T')[0];

          return periodStart === payRunStart &&
                 periodEnd === payRunEnd &&
                 period.frequency === payRun.frequency &&
                 (payRun.status === 'finalized' || payRun.status === 'released');
        });

        // Only include periods that don't have completed pay runs
        return !hasCompletedPayRun;
      });

      console.log(`Filtered to ${activePeriodsOnly.length} truly active periods (removed periods with completed pay runs)`);

      // Recalculate payslips by frequency using only active periods
      const activePayslipsByFrequency = {};
      const activeOverallStats = {
        weekly: { unfinalizedCount: 0 },
        biweekly: { unfinalizedCount: 0 },
        monthly: { unfinalizedCount: 0 },
      };

      activePeriodsOnly.forEach((payslip) => {
        const freq = payslip.frequency;
        if (!activePayslipsByFrequency[freq]) {
          activePayslipsByFrequency[freq] = [];
        }
        activePayslipsByFrequency[freq].push(payslip);
        // Only count unfinalized periods in the stats
        if (!payslip.isFinalized) {
          activeOverallStats[freq].unfinalizedCount++;
        }
      });

      // Calculate currentStep based on active payslips' status
      const currentStep = activePeriodsOnly.every(
        (payslip) => payslip.isFinalized
      )
        ? 2
        : 1;

      // Render the template with all required variables
      res.render("payrollhub", {
        company,
        user: req.user,
        pendingPayslips: activePeriodsOnly, // Use only truly active periods for progress tracker
        originalPendingPayslips: activePeriodsOnly, // Keep same for consistency
        finalizedPayslips: activePeriodsOnly.filter(p => p.isFinalized), // Extract finalized from active periods
        payslipsByFrequency: activePayslipsByFrequency, // Use recalculated frequency groups
        allPayRuns,
        overallStats: activeOverallStats, // Use recalculated stats
        payRunsByFrequency,
        currentStep,
        moment: require("moment"),
        formatCurrency,
        formatPayPeriod,
        calculateGrossIncome,
        calculateDeductions,
        getProgressStatusIcon,
        csrfToken: req.csrfToken(),
        xeroIntegrationActive: !!xeroIntegration,
        pagination,
      });
    } catch (error) {
      console.error("Error in payroll hub route:", error);
      req.flash("error", "Error loading payroll hub");
      return res.redirect("/dashboard");
    }
  }
);

// POST: Bulk finalize payroll periods
router.post(
  "/:companyCode/payroll/bulk-finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== DEBUG: Bulk Finalize Attempt ===");
    console.log("Request URL:", req.originalUrl);
    console.log("Request path:", req.path);
    console.log("Request method:", req.method);
    console.log("Request body:", req.body);

    const { companyCode } = req.params;
    const { payslipIds, frequency, generateNextPeriod, startDate, endDate } =
      req.body;

    console.log("Company Code:", companyCode);
    console.log("Payslip IDs:", payslipIds);
    console.log("Frequency:", frequency);
    console.log("Generate Next Period:", generateNextPeriod);
    console.log("Period Dates:", { startDate, endDate });

    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      console.log("Company found:", !!company);

      if (!company) {
        console.log("Company not found");
        return res.status(404).json({ error: "Company not found" });
      }

      // Validate payslip IDs
      if (
        !payslipIds ||
        !Array.isArray(payslipIds) ||
        payslipIds.length === 0
      ) {
        console.log("Invalid payslip IDs");
        return res
          .status(400)
          .json({ error: "Please select payslips to finalize" });
      }

      // Remove duplicate payslip IDs
      const uniquePayslipIds = [...new Set(payslipIds)];

      // Query periods with populated employee data for next period generation
      const selectedPeriods = await PayrollPeriod.find({
        _id: { $in: uniquePayslipIds },
        company: company._id,
        frequency: frequency,
        startDate: {
          $gte: moment
            .tz(startDate, "Africa/Johannesburg")
            .startOf("day")
            .subtract(1, "day")
            .toDate(),
        },
        endDate: {
          $lte: moment.tz(endDate, "Africa/Johannesburg").endOf("day").toDate(),
        },
      }).populate({
        path: "employee",
        populate: {
          path: "payFrequency",
        },
      });

      if (selectedPeriods.length === 0) {
        console.log("No matching periods found");
        console.log("Debug - Query Parameters:", {
          payslipIds: uniquePayslipIds,
          companyId: company._id,
          frequency,
          startDate: moment
            .tz(startDate, "Africa/Johannesburg")
            .startOf("day")
            .subtract(1, "day")
            .toDate(),
          endDate: moment
            .tz(endDate, "Africa/Johannesburg")
            .endOf("day")
            .toDate(),
        });
        return res.status(404).json({ error: "No matching periods found" });
      }

      console.log(`Found ${selectedPeriods.length} selected periods`);

      // Filter for unfinalized periods
      const unfinalizedPeriods = selectedPeriods.filter(
        (p) => !p.isFinalized && p.status === "open"
      );
      console.log("Unfinalized periods count:", unfinalizedPeriods.length);

      // Track successful next period generations
      const generatedNextPeriods = [];

      // Check if any periods were previously finalized
      let message = "";
      let currentStep = 1;

      if (unfinalizedPeriods.length === 0) {
        // All selected periods are already finalized
        console.log("All selected periods are already finalized");
        currentStep = 2; // Move to Create Pay Run step
        message = "Selected payslips are already finalized";
      } else {
        // Process each unfinalized period
        for (const period of unfinalizedPeriods) {
          // Finalize the period
          period.isFinalized = true;
          period.finalizedAt = new Date();
          period.finalizedBy = req.user._id;
          period.status = "finalized";

          // Save the updated period
          await period.save();

          console.log(
            `Finalized period ${period._id} for employee ${period.employee?._id}`
          );

          // Generate next period if requested
          if (generateNextPeriod && period.employee) {
            try {
              console.log(
                `Generating next period for employee ${period.employee._id}`
              );

              // Make sure employee object is properly populated
              if (!period.employee.payFrequency) {
                console.log("Employee missing pay frequency, populating...");
                await period.populate({
                  path: "employee",
                  populate: {
                    path: "payFrequency",
                  },
                });
              }

              // Generate the next period
              const nextPeriod = await PayrollPeriod.generateNextPeriod(
                period.employee,
                period
              );

              if (nextPeriod) {
                console.log(
                  `Generated next period ${nextPeriod._id} for employee ${period.employee._id}`
                );
                generatedNextPeriods.push(nextPeriod);
              } else {
                console.log(
                  `Failed to generate next period for employee ${period.employee._id}`
                );
              }
            } catch (error) {
              console.error(
                `Error generating next period for employee ${period.employee._id}:`,
                error
              );
            }
          }
        }

        // Set success message
        currentStep = 2; // Move to Create Pay Run step
        message = `Successfully finalized ${unfinalizedPeriods.length} payslips and generated ${generatedNextPeriods.length} new periods`;
      }

      // Check if all payslips for this frequency and period are now finalized
      const remainingUnfinalizedCount = await PayrollPeriod.countDocuments({
        company: company._id,
        frequency: frequency,
        startDate: { $gte: new Date(startDate) },
        endDate: { $lte: new Date(endDate) },
        isFinalized: false,
        status: "open",
      });

      console.log("Remaining unfinalized count:", remainingUnfinalizedCount);
      console.log("Generated next periods:", generatedNextPeriods.length);

      res.json({
        success: true,
        message: message,
        currentStep: currentStep,
        allFinalized: remainingUnfinalizedCount === 0,
        count: unfinalizedPeriods.length,
        skippedCount: selectedPeriods.length - unfinalizedPeriods.length,
        periodIds: unfinalizedPeriods.map((p) => p._id),
        nextPeriods: generatedNextPeriods.map((p) => ({
          id: p._id,
          employeeId: p.employee,
          startDate: p.startDate,
          endDate: p.endDate,
          frequency: p.frequency,
        })),
      });
    } catch (error) {
      console.error("Error in bulk finalize:", error);
      res.status(500).json({
        error: "Failed to finalize payslips",
        details: error.message,
      });
    }
  }
);

// POST: Create a new pay run
router.post(
  "/:companyCode/payruns",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== DEBUG: Pay Run Creation Attempt ===");
    const { companyCode } = req.params;
    const { frequency, startDate, endDate, forceCreate } = req.body;

    console.log("Company Code:", companyCode);
    console.log("Frequency:", frequency);
    console.log("Start Date:", startDate);
    console.log("End Date:", endDate);
    console.log("Force Create:", forceCreate);

    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        return res.status(404).json({ error: "Company not found" });
      }

      // Check for existing pay run in the same period
      const existingPayRun = await PayRun.findOne({
        company: company._id,
        frequency: frequency,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });

      if (existingPayRun) {
        return res.status(400).json({
          error: "A pay run already exists for this period",
          existingPayRun: {
            _id: existingPayRun._id,
            status: existingPayRun.status,
          },
        });
      }

      // Get all payslips for this period and frequency
      const allPayslips = await PayrollPeriod.find({
        company: company._id,
        frequency: frequency,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      }).populate("employee");

      // Get finalized payslips
      const finalizedPayslips = allPayslips.filter((p) => p.isFinalized);

      // Check for unfinalized payslips
      const unfinalizedCount = allPayslips.length - finalizedPayslips.length;

      if (unfinalizedCount > 0 && !forceCreate) {
        return res.status(400).json({
          warning: true,
          message: "There are unfinalized payslips",
          unfinalizedCount: unfinalizedCount,
          totalCount: allPayslips.length,
          finalizedCount: finalizedPayslips.length,
        });
      }

      // Proceed with only finalized payslips
      if (finalizedPayslips.length === 0) {
        console.log("No finalized payslips found");
        return res
          .status(400)
          .json({ error: "No finalized payslips found for this period" });
      }

      // Calculate period information
      const periodDate = new Date(startDate);
      const period =
        periodDate.toLocaleString("default", { month: "long" }) +
        " " +
        periodDate.getFullYear();
      const monthYear = startDate.slice(0, 7);

      // Calculate payment date (7 days after end date)
      const paymentDate = new Date(endDate);
      paymentDate.setDate(paymentDate.getDate() + 7);

      // Create the pay run
      const payRun = new PayRun({
        company: company._id,
        frequency: frequency,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        period: period,
        monthYear: monthYear,
        taxPeriod: monthYear,
        paymentDate: paymentDate,
        status: "finalized",
        payslips: finalizedPayslips.map((p) => p._id),
        totals: {
          grossPay: finalizedPayslips.reduce(
            (sum, p) => sum + (p.grossPay || 0),
            0
          ),
          totalDeductions: finalizedPayslips.reduce(
            (sum, p) => sum + (p.totalDeductions || 0),
            0
          ),
          netPay: finalizedPayslips.reduce(
            (sum, p) => sum + (p.netPay || 0),
            0
          ),
          employerContributions: 0,
        },
        createdBy: req.user._id,
        createdAt: new Date(),
      });

      await payRun.save();

      // Update payslips with pay run reference
      await PayrollPeriod.updateMany(
        { _id: { $in: finalizedPayslips.map((p) => p._id) } },
        { $set: { payRun: payRun._id } }
      );

      console.log("Pay run created successfully:", payRun._id);

      res.json({
        success: true,
        message: "Pay run created successfully",
        payRun: {
          _id: payRun._id,
          frequency: payRun.frequency,
          startDate: payRun.startDate,
          endDate: payRun.endDate,
          period: payRun.period,
          paymentDate: payRun.paymentDate,
          status: payRun.status,
          totals: payRun.totals,
        },
      });
    } catch (error) {
      console.error("Error creating pay run:", error);
      res.status(500).json({
        error: "Failed to create pay run",
        details: error.message,
      });
    }
  }
);

// GET: Get latest pay run for frequency
router.get(
  "/:companyCode/payruns/latest/:frequency",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Get Latest Pay Run ===");
    const { companyCode, frequency } = req.params;

    console.log("Company Code:", companyCode);
    console.log("Frequency:", frequency);

    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        return res.status(404).json({ error: "Company not found" });
      }

      // Get the latest pay run for this frequency
      const payRun = await PayRun.findOne({
        company: company._id,
        frequency: frequency,
        status: "draft",
      }).sort({ createdAt: -1 });

      console.log("Found pay run:", payRun);

      if (!payRun) {
        return res
          .status(404)
          .json({ error: "No pay run found for this frequency" });
      }

      res.json({
        success: true,
        payRun: payRun,
      });
    } catch (error) {
      console.error("Error getting latest pay run:", error);
      res.status(500).json({
        error: "Failed to get latest pay run",
        details: error.message,
      });
    }
  }
);

// POST: Finalize a pay run
router.post(
  "/:companyCode/payruns/:payRunId/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Pay Run Finalization Route ===");
    console.log("Request URL:", req.originalUrl);
    console.log("Request path:", req.path);
    console.log("Request method:", req.method);
    console.log("Pay Run ID:", req.params.payRunId);
    console.log("Company Code:", req.params.companyCode);

    try {
      const { companyCode, payRunId } = req.params;

      console.log("\n=== Fetching Pay Run Details ===");
      const payRun = await PayRun.findById(payRunId).populate("company");
      if (!payRun) {
        console.error("Pay run not found:", payRunId);
        req.flash("error", "Pay run not found");
        return res.redirect("/dashboard");
      }
      console.log("Found pay run:", {
        id: payRun._id,
        startDate: payRun.startDate,
        endDate: payRun.endDate,
        status: payRun.status,
        company: payRun.company?._id,
      });

      // Fetch all payslips for this pay run
      console.log("\n=== Fetching Payslips ===");
      const payslips = await PayrollPeriod.find({
        company: payRun.company,
        startDate: payRun.startDate,
        endDate: payRun.endDate,
      }).populate({
        path: "employee",
        populate: {
          path: "payFrequency",
        },
      });

      console.log("Found payslips count:", payslips.length);

      // Process each payslip
      console.log("\n=== Processing Payslips ===");
      for (const payslip of payslips) {
        try {
          console.log("\nProcessing payslip for employee:", {
            id: payslip.employee?._id,
            name: payslip.employee
              ? `${payslip.employee.firstName} ${payslip.employee.lastName}`
              : "Unknown",
            frequency: payslip.employee?.payFrequency?.frequency || "monthly",
          });

          // Validate employee data
          if (!payslip.employee) {
            console.error("Payslip has no associated employee:", payslip._id);
            continue;
          }

          // Calculate final amounts
          console.log("Calculating final amounts");
          const payrollData = await Payroll.findOne({
            employee: payslip.employee._id,
            month: payRun.endDate,
          });
          console.log(
            "Found payroll data:",
            payrollData
              ? {
                  id: payrollData._id,
                  basicSalary: payrollData.basicSalary,
                  month: payrollData.month,
                }
              : "No payroll data found"
          );

          let taxableIncome = 0;
          let totalDeductions = 0;

          if (payrollData) {
            taxableIncome = calculateTotalTaxableIncomeWithLogging(payrollData);
            console.log("Calculated taxable income from payroll data:", taxableIncome);

            totalDeductions = calculateTotalDeductionsWithLogging({
              payrollData,
              taxableIncome,
              frequency: payslip.employee.payFrequency?.frequency || "monthly",
              startDate: payRun.startDate,
              endDate: payRun.endDate,
              employee: payslip.employee,
            });
            console.log("Calculated total deductions from payroll data:", totalDeductions);
          } else if (payslip.payrollPeriod) {
            // Try to get data from the payroll period
            console.log("Using data from payroll period instead");
            const payrollPeriod = await PayrollPeriod.findById(payslip.payrollPeriod);
            
            if (payrollPeriod) {
              taxableIncome = payrollPeriod.grossPay || 0;
              totalDeductions = payrollPeriod.totalDeductions || 0;
              console.log("Using payroll period data:", {
                grossPay: taxableIncome,
                totalDeductions: totalDeductions
              });
            } else {
              console.log("No payroll period found either, using values from payslip");
              taxableIncome = payslip.grossPay || 0;
              totalDeductions = payslip.totalDeductions || 0;
            }
          } else {
            console.log("No payroll data or period found, using values from payslip");
            taxableIncome = payslip.grossPay || 0;
            totalDeductions = payslip.totalDeductions || 0;
          }

          // Update payslip with final calculations
          payslip.calculations = {
            basicSalary: {
              full: payrollData?.basicSalary || payslip.basicSalary || 0,
              prorated: taxableIncome,
            },
            totalIncome: taxableIncome,
            deductions: {
              statutory: {
                paye: calculatePAYEImproved({
                  annualSalary: taxableIncome * 12,
                  frequency:
                    payslip.employee.payFrequency?.frequency || "monthly",
                  proRataPercentage: 100,
                }),
                uif: calculateUIF({
                  salary: taxableIncome,
                  employee: payslip.employee,
                  startDate: payRun.startDate,
                  endDate: payRun.endDate,
                }),
              },
            },
            totalDeductions,
            netPay: taxableIncome - totalDeductions,
          };

          // Store tax values in the PayrollPeriod model
          const paye = payslip.calculations.deductions.statutory.paye || 0;
          const uif = payslip.calculations.deductions.statutory.uif || 0;
          const sdl = taxableIncome * 0.01; // SDL is typically 1% of gross pay
          
          payslip.PAYE = paye;
          payslip.UIF = uif;
          payslip.SDL = sdl;

          // Update payslip status
          payslip.status = "finalized";
          payslip.isFinalized = true;
          payslip.finalizedAt = new Date();
          payslip.finalizedBy = req.user._id;
          
          // Ensure core payslip fields are updated
          payslip.grossPay = taxableIncome;
          payslip.totalDeductions = totalDeductions;
          payslip.netPay = taxableIncome - totalDeductions;

          await payslip.save();
          console.log("Successfully finalized payslip");
        } catch (error) {
          console.error("Error processing payslip:", error);
          throw error;
        }
      }

      // Update pay run status
      console.log("\n=== Updating Pay Run Status ===");
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      await payRun.save();
      console.log("Successfully finalized pay run");

      req.flash("success", "Pay run has been finalized successfully");
      console.log("Redirecting to payroll hub");
      return res.redirect(`/clients/${companyCode}/payrollhub`);
    } catch (error) {
      console.error("Error in pay run finalization:", error);
      req.flash("error", "An error occurred while finalizing the pay run");
      return res.redirect("/dashboard");
    }
  }
);

// Bank file generation route
router.post('/:companyCode/payruns/:payRunId/bank-file/standard', async (req, res) => {
    console.log('\n=== Bank File Generation Debug ===');
    const { companyCode, payRunId } = req.params;
    const { actionDate } = req.body;
    const userId = req.user.id;

    console.log('Request parameters:', { companyCode, payRunId, actionDate, userId });

    try {
        // Find company
        const company = await Company.findOne({ companyCode });
        if (!company) {
            throw new Error('Company not found');
        }
        console.log('\n=== Company Details ===');
        console.log('Company found:', {
            id: company._id,
            name: company.name,
            code: company.companyCode
        });

        // Find pay run
        const rawPayRun = await PayRun.findOne({
            _id: payRunId,
            company: company._id
        });
        
        console.log('\n=== Raw Pay Run Data Check ===');
        console.log('Raw pay run data:', {
            id: rawPayRun?._id,
            payslipsIds: rawPayRun?.payslips,
            payslipsCount: rawPayRun?.payslips?.length || 0,
            totalPayslipCount: rawPayRun?.totalPayslipCount,
            status: rawPayRun?.status,
            finalized: rawPayRun?.finalized,
            totals: rawPayRun?.totals,
            startDate: rawPayRun?.startDate,
            endDate: rawPayRun?.endDate,
            period: rawPayRun?.period
        });

        if (!rawPayRun) {
            throw new Error('Pay run not found');
        }

        // Find payroll periods
        console.log('\n=== Finding Payroll Periods ===');
        const payslips = await PayrollPeriod.find({
            _id: { $in: rawPayRun.payslips }
        });
        console.log('Found payslips:', payslips.length);
        console.log('Payslip details:', payslips.map(p => ({
            id: p._id,
            employeeId: p.employee,
            grossPay: p.grossPay,
            netPay: p.netPay,
            status: p.status,
            isFinalized: p.isFinalized
        })));

        // Get employee details for each payslip
        const employeeIds = payslips.map(p => p.employee);
        console.log('\n=== Finding Employee Details ===');
        console.log('Looking up employees:', employeeIds);
        
        const employees = await Employee.find(
            { _id: { $in: employeeIds } },
            {
                firstName: 1,
                lastName: 1,
                bank: 1,
                accountType: 1,
                accountNumber: 1,
                branchCode: 1,
                holderRelationship: 1,
                accountHolder: 1
            }
        );

        console.log('Found employees:', employees.length);

        // Create a map of employee details
        const employeeMap = employees.reduce((map, emp) => {
            map[emp._id.toString()] = emp;
            return map;
        }, {});

        console.log('\n=== Detailed Payslips Check ===');
        const detailedPayslips = payslips.map((payslip, index) => {
            const employee = employeeMap[payslip.employee.toString()];
            console.log(`Payslip ${index + 1}:`, {
                id: payslip._id,
                hasEmployee: !!employee,
                employeeDetails: employee ? {
                    id: employee._id,
                    name: `${employee.firstName} ${employee.lastName}`,
                    bank: employee.bank,
                    accountType: employee.accountType,
                    accountNumber: employee.accountNumber,
                    branchCode: employee.branchCode
                } : null,
                grossPay: payslip.grossPay,
                netPay: payslip.netPay
            });
            return {
                payslip,
                employee
            };
        });

        // Validate bank details and create transactions
        console.log('\n=== Bank Details Validation ===');
        const transactions = [];
        const invalidPayslips = [];

        for (const { payslip, employee } of detailedPayslips) {
            const validation = {
                hasEmployee: !!employee,
                hasBankDetails: !!(employee?.bank && employee?.accountNumber && employee?.branchCode),
                hasAccountNumber: !!employee?.accountNumber,
                hasBankName: !!employee?.bank,
                hasAccountType: !!employee?.accountType,
                isValid: employee?.branchCode
            };
            
            console.log(`Validation for payslip ${payslip._id}:`, validation);

            if (validation.hasEmployee && validation.hasBankDetails) {
                const transaction = {
                    branchCode: employee.branchCode,
                    accountNumber: employee.accountNumber,
                    amount: payslip.netPay,
                    employeeName: `${employee.firstName} ${employee.lastName}`
                };
                console.log('Created transaction:', transaction);
                transactions.push(transaction);
            } else {
                invalidPayslips.push({
                    employeeId: payslip.employee,
                    employeeName: employee ? `${employee.firstName} ${employee.lastName}` : 'Unknown',
                    missingDetails: {
                        bankName: !validation.hasBankName,
                        accountNumber: !validation.hasAccountNumber,
                        branchCode: !validation.isValid,
                        accountType: !validation.hasAccountType
                    }
                });
            }
        }

        console.log('\n=== Transaction Summary ===');
        console.log('Valid transactions:', transactions.length);
        console.log('Invalid payslips:', invalidPayslips.length);
        console.log('Total amount:', transactions.reduce((sum, t) => sum + t.amount, 0));

        if (transactions.length === 0) {
            throw new Error('No valid payslips found with complete bank details');
        }

        if (invalidPayslips.length > 0) {
            throw new Error(JSON.stringify({
                type: 'MISSING_BANK_DETAILS',
                message: 'Some employees are missing bank details',
                details: invalidPayslips
            }));
        }

        // Generate bank file
        console.log('\n=== Bank File Generation Start ===');
        console.log('Generating bank file with:', {
            companyId: company._id,
            companyName: company.name,
            transactionCount: transactions.length,
            actionDate: actionDate || new Date()
        });

        // Import the correct version from bankFileUtils.js
        const { generateFNBBankFile } = require('../utils/bankFileUtils');
        const bankFile = await generateFNBBankFile(company, transactions, actionDate || new Date());

        // Set the filename
        const filename = `bank_file_${companyCode}_${moment().format('YYYYMMDD_HHmmss')}.txt`;
        console.log('Generated filename:', filename);

        // Send the response
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(bankFile);

    } catch (error) {
        console.error('Error generating bank file:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({
            error: 'Error generating bank file',
            details: error.message
        });
    }
});

// Get EFT format for a specific pay run
router.get(
  "/payrun/:payRunId/eft-format",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { payRunId } = req.params;
      const { currentCompany } = req.user;

      // Find the pay run
      const payRun = await PayRun.findById(payRunId);
      if (!payRun) {
        return res.status(404).json({ error: "Pay run not found" });
      }

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(currentCompany);

      // Update the pay run with the EFT settings
      payRun.eftSettings = eftSettings._id;
      await payRun.save();

      // Return the EFT settings
      res.json({
        bankFileFormat: eftSettings.bankFileFormat,
        bankName: eftSettings.bankName,
        branchCode: eftSettings.branchCode,
        accountNumber: eftSettings.accountNumber,
      });
    } catch (error) {
      console.error("Error retrieving EFT format:", error);
      res.status(500).json({ error: "Failed to retrieve EFT format" });
    }
  }
);

// Route to finalize a single payslip
router.post(
  "/:companyCode/payroll/finalize-payslip/:payslipId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Finalize Single Payslip Route Debug ===");
    console.log("Request URL:", req.originalUrl);
    console.log("Base URL:", req.baseUrl);
    console.log("Path:", req.path);
    console.log("Method:", req.method);
    console.log("Params:", req.params);
    console.log("Query:", req.query);
    console.log("Body:", req.body);
    console.log("Headers:", {
      "csrf-token": req.headers["csrf-token"],
      "content-type": req.headers["content-type"],
    });
    console.log("User:", {
      id: req.user?._id,
      email: req.user?.email,
    });

    try {
      const { companyCode, payslipId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      console.log("Company found:", !!company);

      if (!company) {
        console.log("Company not found for code:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find the payslip period
      const payrollPeriod = await PayrollPeriod.findById(payslipId).populate(
        "employee"
      );
      console.log("Payroll period found:", !!payrollPeriod);

      if (!payrollPeriod) {
        console.log("Payroll period not found for ID:", payslipId);
        return res.status(404).json({
          success: false,
          message: "Payroll period not found",
        });
      }

      // Calculate PAYE
      const annualSalary = payrollPeriod.basicSalary * 12;
      const payeResult = await calculatePAYEImproved({
        annualSalary,
        frequency: payrollPeriod.frequency || "monthly",
        proRataPercentage: 100,
      });

      // Calculate UIF (1% of basic salary, capped at R177.12)
      const uif = Math.min(payrollPeriod.basicSalary * 0.01, 177.12);

      // Set the amounts and finalization flags
      payrollPeriod.grossPay = payrollPeriod.basicSalary;
      payrollPeriod.totalDeductions = payeResult + uif;
      payrollPeriod.netPay =
        payrollPeriod.grossPay - payrollPeriod.totalDeductions;

      // Store tax values in the PayrollPeriod model
      payrollPeriod.PAYE = payeResult;
      payrollPeriod.UIF = uif;
      payrollPeriod.SDL = payrollPeriod.grossPay * 0.01; // SDL is typically 1% of gross pay

      // Set finalization flags
      payrollPeriod.isFinalized = true;
      payrollPeriod.finalizedAt = new Date();
      payrollPeriod.finalizedBy = req.user._id;
      payrollPeriod.status = "finalized";

      console.log("Saving payroll period with finalization:", {
        grossPay: payrollPeriod.grossPay,
        totalDeductions: payrollPeriod.totalDeductions,
        netPay: payrollPeriod.netPay,
        isFinalized: payrollPeriod.isFinalized,
        status: payrollPeriod.status,
      });

      await payrollPeriod.save();

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(
        payrollPeriod.employee._id
      )
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
      }

      if (!populatedEmployee.payFrequency) {
        req.flash("error", "Employee pay frequency not configured");
        return res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
      }

      // Generate next period
      await PayrollPeriod.generateNextPeriod(populatedEmployee, payrollPeriod);

      req.flash("success", "Payslip finalized successfully");
      res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
    } catch (error) {
      console.error("Error finalizing payslip:", error);
      req.flash("error", error.message || "Failed to finalize payslip");
      res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
    }
  }
);

// Route to finalize payslips by frequency
router.post(
  "/payroll/finalize-payslips/:frequency",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Finalize Payslips by Frequency Route ===");
    console.log("Request body:", req.body);

    try {
      const { payslipIds } = req.body;
      const { frequency } = req.params;

      if (
        !payslipIds ||
        !Array.isArray(payslipIds) ||
        payslipIds.length === 0
      ) {
        return res.status(400).json({
          success: false,
          message: "No payslip IDs provided",
        });
      }

      // Find all payslips
      const payslips = await PayrollPeriod.find({
        _id: { $in: payslipIds },
        frequency: frequency,
        isFinalized: false,
      }).populate("employee");

      if (!payslips || payslips.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No valid payslips found to finalize",
        });
      }

      // Start a transaction
      const session = await mongoose.startSession();
      session.startTransaction();

      try {
        // Update all payslips
        for (const payslip of payslips) {
          // Calculate PAYE
          const annualSalary = payslip.basicSalary * 12;
          const payeResult = await calculatePAYEImproved({
            annualSalary,
            frequency: payslip.frequency || "monthly",
            proRataPercentage: 100,
          });

          // Calculate UIF (1% of basic salary, capped at R177.12)
          const uif = Math.min(payslip.basicSalary * 0.01, 177.12);

          // Set the amounts and finalization flags
          payslip.grossPay = payslip.basicSalary;
          payslip.totalDeductions = payeResult + uif;
          payslip.netPay = payslip.grossPay - payslip.totalDeductions;

          // Store tax values in the PayrollPeriod model
          payslip.PAYE = payeResult;
          payslip.UIF = uif;
          payslip.SDL = payslip.grossPay * 0.01; // SDL is typically 1% of gross pay

          // Set finalization flags
          payslip.isFinalized = true;
          payslip.finalizedAt = new Date();
          payslip.finalizedBy = req.user._id;
          payslip.status = "finalized";

          await payslip.save({ session });

          // Generate next period if needed
          const employee = await Employee.findById(payslip.employee._id)
            .populate("payFrequency")
            .populate("company")
            .session(session);

          if (employee && employee.status === "Active") {
            try {
              await PayrollService.generateNextPeriod(employee, payslip);
            } catch (error) {
              console.error("Error generating next period:", error);
            }
          }
        }

        // Commit the transaction
        await session.commitTransaction();

        res.json({
          success: true,
          message: `Successfully finalized ${payslips.length} payslips`,
          count: payslips.length,
        });
      } catch (error) {
        // Rollback the transaction on error
        await session.abortTransaction();
        throw error;
      } finally {
        session.endSession();
      }
    } catch (error) {
      console.error("Error finalizing payslips:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to finalize payslips",
      });
    }
  }
);

// Route to manually finalize a pay run (for progress tracker)
router.post(
  "/:companyCode/payruns/:payRunId/manual-finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Manual Pay Run Finalization Route ===");

    try {
      const { companyCode, payRunId } = req.params;
      const payRun = await PayRun.findById(payRunId).populate("company");

      if (!payRun) {
        return res.status(404).json({ error: "Pay run not found" });
      }

      if (payRun.company.companyCode !== companyCode) {
        return res.status(403).json({ error: "Company code mismatch" });
      }

      // Update the pay run to mark it as manually finalized
      payRun.manuallyFinalized = true;
      await payRun.save();

      // Create audit log
      const auditLog = new AuditLog({
        user: req.user._id,
        company: payRun.company._id, // Add the company ID
        action: "manual_finalize_pay_run",
        details: {
          payRunId: payRun._id,
          frequency: payRun.frequency,
          companyCode,
        },
      });
      await auditLog.save();

      res.status(200).json({
        success: true,
        message: "Pay run manually finalized successfully",
        frequency: payRun.frequency,
      });
    } catch (error) {
      console.error("Error manually finalizing pay run:", error);
      res.status(500).json({
        error: "Failed to manually finalize pay run",
        details: error.message,
      });
    }
  }
);

// GET route for payslips by frequency
router.get(
  "/:companyCode/payroll/payslips/:frequency",
  ensureAuthenticated,
  async (req, res) => {
    try {
      console.log("\n=== Payslips Route Debug ===");
      console.log("Company Code:", req.params.companyCode);
      console.log("Frequency:", req.params.frequency);
      console.log("Query:", req.query);

      const { companyCode, frequency } = req.params;
      const { startDate, endDate } = req.query;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        return res.status(404).json({ error: "Company not found" });
      }

      console.log("Found company:", company._id);

      // Find existing payroll periods first
      let payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        frequency: frequency,
      }).populate("employee");

      console.log(`Found ${payrollPeriods.length} total payroll periods`);

      // Filter periods by date range
      payrollPeriods = payrollPeriods.filter((period) => {
        const periodStart = moment(period.startDate);
        const periodEnd = moment(period.endDate);
        const queryStart = moment(startDate);
        const queryEnd = moment(endDate);

        return (
          periodStart.isSameOrAfter(queryStart, "day") &&
          periodEnd.isSameOrBefore(queryEnd, "day")
        );
      });

      console.log(`Found ${payrollPeriods.length} periods within date range`);

      // If no periods found, find active employees to create periods for
      if (payrollPeriods.length === 0) {
        console.log("No periods found, checking for active employees");

        const employees = await Employee.find({
          company: company._id,
          isActive: true,
        });

        console.log(`Found ${employees.length} active employees`);

        if (employees.length > 0) {
          const newPeriods = [];
          for (const employee of employees) {
            console.log(`Creating period for employee ${employee._id}`);

            // Calculate tax deductions for the period
            const basicSalary = employee.basicSalary || 0;
            let paye = 0;
            let uif = 0;
            let totalDeductions = 0;
            let netPay = basicSalary;

            if (basicSalary > 0) {
              try {
                // Calculate PAYE using the existing function
                const annualSalary = basicSalary * 12;
                const TAX_BRACKETS = [
                  { threshold: 0, rate: 0.18, base: 0 },
                  { threshold: 237100, rate: 0.26, base: 42678 },
                  { threshold: 370500, rate: 0.31, base: 77362 },
                  { threshold: 512800, rate: 0.36, base: 121475 },
                  { threshold: 673000, rate: 0.39, base: 179147 },
                  { threshold: 857900, rate: 0.41, base: 251258 },
                  { threshold: 1817000, rate: 0.45, base: 644489 },
                ];

                let bracket = TAX_BRACKETS[0];
                for (let i = 1; i < TAX_BRACKETS.length; i++) {
                  if (annualSalary >= TAX_BRACKETS[i].threshold) {
                    bracket = TAX_BRACKETS[i];
                  } else {
                    break;
                  }
                }

                const annualTax = bracket.base + bracket.rate * (annualSalary - bracket.threshold);
                const primaryRebate = 17235;
                const annualTaxAfterRebate = Math.max(0, annualTax - primaryRebate);
                paye = annualTaxAfterRebate / 12;

                // Calculate UIF
                uif = Math.min(basicSalary * 0.01, 177.12);

                // Calculate totals
                totalDeductions = paye + uif;
                netPay = basicSalary - totalDeductions;

                console.log(`Tax calculations for employee ${employee._id}:`, {
                  basicSalary,
                  paye: paye.toFixed(2),
                  uif: uif.toFixed(2),
                  totalDeductions: totalDeductions.toFixed(2),
                  netPay: netPay.toFixed(2)
                });
              } catch (error) {
                console.error(`Error calculating taxes for employee ${employee._id}:`, error);
                // Continue with zero values if calculation fails
              }
            }

            const period = new PayrollPeriod({
              employee: employee._id,
              company: company._id,
              frequency: frequency,
              startDate: moment(startDate).startOf("day").toDate(),
              endDate: moment(endDate).endOf("day").toDate(),
              basicSalary: basicSalary,
              grossPay: basicSalary,
              PAYE: paye,
              UIF: uif,
              totalDeductions: totalDeductions,
              netPay: netPay,
              status: "open",
              isFinalized: false,
            });
            newPeriods.push(period);
          }

          // Save all new periods
          payrollPeriods = await PayrollPeriod.insertMany(newPeriods);

          // Populate employee data
          payrollPeriods = await PayrollPeriod.find({
            _id: { $in: payrollPeriods.map((p) => p._id) },
          }).populate("employee");

          console.log(`Created ${payrollPeriods.length} new payroll periods`);
        }
      }

      // Calculate totals from already calculated period values
      let totalGross = 0;
      let totalDeductions = 0;
      let totalNet = 0;

      // Sum up the already calculated values from each period
      for (const period of payrollPeriods) {
        const grossPay = period.grossPay || 0;
        const deductions = period.totalDeductions || 0;
        const netPay = period.netPay || 0;

        totalGross += grossPay;
        totalDeductions += deductions;
        totalNet += netPay;
      }

      const totals = {
        totalGross,
        totalDeductions,
        totalNet
      };

      console.log("Calculated totals:", totals);

      // Format response
      const formattedPayslips = payrollPeriods.map((period) => ({
        _id: period._id,
        employeeName: period.employee
          ? `${period.employee.firstName} ${period.employee.lastName}`
          : "Unknown",
        employeeNumber: period.employee?.employeeNumber || "N/A",
        grossPay: period.grossPay || 0,
        totalDeductions: period.totalDeductions || 0,
        netPay: period.netPay || 0,
        PAYE: period.PAYE || 0,
        UIF: period.UIF || 0,
      }));

      console.log(`Returning ${formattedPayslips.length} formatted payslips`);

      res.json({
        success: true,
        payslips: formattedPayslips,
        totals,
      });
    } catch (error) {
      console.error("Error fetching payslips:", error);
      res.status(500).json({
        success: false,
        message: "Error fetching payslips",
        error: error.message,
      });
    }
  }
);

// GET: Get finalized payslips for a specific frequency and period
router.get(
  "/:companyCode/payroll/finalized-payslips/:frequency",
  ensureAuthenticated,
  async (req, res) => {
    console.log("\n=== GET Finalized Payslips by Frequency and Period ===");
    const { companyCode, frequency } = req.params;
    const { startDate, endDate } = req.query;

    console.log("Company Code:", companyCode);
    console.log("Frequency:", frequency);
    console.log("Start Date:", startDate);
    console.log("End Date:", endDate);

    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        return res.status(404).json({ error: "Company not found" });
      }

      // Build query for finalized payslips
      const query = {
        company: company._id,
        frequency: frequency,
        isFinalized: true,
      };

      // Add date filters if provided
      if (startDate && endDate) {
        query.startDate = { $gte: new Date(startDate) };
        query.endDate = { $lte: new Date(endDate) };
      }

      console.log("Query:", query);

      // Find payslips
      const payslips = await PayrollPeriod.find(query);
      console.log(`Found ${payslips.length} finalized payslips`);

      // Return the payslip IDs with success flag
      console.log(
        "Returning payslip IDs:",
        payslips.map((p) => p._id)
      );

      res.json({
        success: true,
        payslipIds: payslips.map((p) => p._id),
        count: payslips.length,
      });
    } catch (error) {
      console.error("Error getting finalized payslips:", error);
      res.status(500).json({
        error: "Failed to get finalized payslips",
        details: error.message,
      });
    }
  }
);

// POST route to send payroll data to Xero
router.post(
  "/:companyCode/pay-runs/:payRunId/xero",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("\n=== Send to Xero Route ===");
    try {
      const { companyCode, payRunId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Find pay run
      const payRun = await PayRun.findById(payRunId)
        .populate("payslips")
        .populate("company");

      if (!payRun) {
        throw new Error("Pay run not found");
      }

      // Check if already synced
      if (payRun.xeroSynced) {
        return res.status(400).json({
          success: false,
          message: "This pay run has already been synced to Xero",
        });
      }

      // Check for active Xero integration
      const integration = await Integration.findOne({
        company: company._id,
        provider: "xero",
        type: "accounting",
        status: "active",
      });

      if (!integration) {
        throw new Error("No active Xero integration found");
      }

      // Get account mappings
      const mappings = await XeroAccountMapping.find({
        company: company._id,
      });

      if (!mappings.length) {
        throw new Error("No Xero account mappings configured");
      }

      // Calculate totals
      const totals = {
        basicSalary: payRun.payslips.reduce(
          (sum, p) => sum + (p.basicSalary || 0),
          0
        ),
        paye: payRun.payslips.reduce((sum, p) => sum + (p.PAYE || 0), 0),
        uif: payRun.payslips.reduce((sum, p) => sum + (p.UIF || 0), 0),
        sdl: payRun.payslips.reduce((sum, p) => sum + (p.SDL || 0), 0),
      };

      // Prepare data for Xero sync
      const syncData = {
        payrollId: payRun._id,
        payrollData: {
          basicSalary: totals.basicSalary,
          paye: totals.paye,
          uif: totals.uif,
          sdl: totals.sdl,
          employeeCount: payRun.payslips.length,
          period: `${moment(payRun.startDate).format("MMMM YYYY")} Payroll`,
        },
        description: `Payroll for ${moment(payRun.startDate).format(
          "MMMM YYYY"
        )}`,
      };

      // Send to Xero integration endpoint
      const xeroResponse = await fetch(
        `/clients/${companyCode}/settings/accounting/xero/sync-payroll`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "CSRF-Token": req.csrfToken(),
          },
          body: JSON.stringify(syncData),
        }
      );

      if (!xeroResponse.ok) {
        const error = await xeroResponse.json();
        throw new Error(error.message || "Failed to sync with Xero");
      }

      // Update pay run with Xero sync status
      payRun.xeroSynced = true;
      payRun.xeroSyncedAt = new Date();
      await payRun.save();

      // Create audit log
      const auditLog = new AuditLog({
        user: req.user._id,
        company: company._id,
        action: "sync_payroll_to_xero",
        details: {
          payRunId: payRun._id,
          syncedAt: new Date(),
          totals,
        },
      });
      await auditLog.save();

      res.json({
        success: true,
        message: "Successfully synced payroll data to Xero",
        syncedAt: new Date(),
      });
    } catch (error) {
      console.error("Error syncing to Xero:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to sync with Xero",
      });
    }
  }
);

// NEW ROUTE: Delete pay run (integrated with PayRunValidationService)
router.delete(
  "/:companyCode/payrun/:payRunId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, payRunId } = req.params;
      const { force, reason } = req.body;

      console.log("=== Delete Pay Run Request ===");
      console.log("Company Code:", companyCode);
      console.log("Pay Run ID:", payRunId);
      console.log("Force:", force);
      console.log("User:", req.user._id);

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Use PayRunValidationService for deletion
      const PayRunValidationService = require("../services/PayRunValidationService");

      const result = await PayRunValidationService.deletePayRun(
        payRunId,
        req.user._id,
        reason || "Deleted via payroll hub",
        force
      );

      if (!result.success) {
        return res.status(400).json(result);
      }

      res.json({
        success: true,
        message: "Pay run deleted successfully",
        payRun: result.payRun,
        warnings: result.warnings,
      });
    } catch (error) {
      console.error("Error deleting pay run:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete pay run",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Get pay run deletion analysis
router.get(
  "/:companyCode/payrun/:payRunId/deletion-analysis",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { payRunId } = req.params;

      const PayRunValidationService = require("../services/PayRunValidationService");

      const analysis = await PayRunValidationService.getAffectedEntities(payRunId);

      if (analysis.error) {
        return res.status(404).json({
          success: false,
          message: analysis.error,
        });
      }

      res.json({
        success: true,
        analysis: analysis,
      });
    } catch (error) {
      console.error("Error analyzing pay run deletion:", error);
      res.status(500).json({
        success: false,
        message: "Failed to analyze deletion impact",
        error: error.message,
      });
    }
  }
);

module.exports = router;
