const express = require("express");
const router = express.Router();
const moment = require("moment-timezone");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayFrequency = require("../models/PayFrequency");
const payrollCalculations = require("../utils/payrollCalculations");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/Company");
const checkCompany = require("../middleware/checkCompany");
const {
  ensureAuthenticated,
  ensureOwner,
} = require("../config/ensureAuthenticated");
const PayrollPeriod = require("../models/PayrollPeriod");
const beneficiaries = require("../models/beneficiaries");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const RFIConfig = require("../models/rfiConfig");
const mongoose = require("mongoose");
const fs = require("fs");
const dateUtils = require("../utils/dateUtils");
const transporter = require("../config/emailConfig");
const crypto = require("crypto");
const EmployeeCredential = require("../models/employeeCredential");
const ejs = require("ejs");
const path = require("path");
const PayrollService = require("../services/PayrollService");
const RFICalculationService = require("../services/rfiCalculationService");
const EmployeeNumberService = require("../services/employeeNumberService");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const Role = require("../models/role");
const bcrypt = require("bcrypt");
const EmployeeTemplateGenerator = require("../utils/excel-templates");
const XLSX = require("xlsx");
const PayComponent = require("../models/payComponent");
const AuditLog = require("../models/auditLog");
const PensionFundBeneficiary = require("../models/pensionFundBeneficiary");
const Beneficiary = require("../models/beneficiaries");
const WorkedHours = require("../models/WorkedHours");
const { calculatePeriodTotals } = require("../services/PayrollService");
const ChronologicalValidator = require("../utils/ChronologicalValidator");
const PeriodCalculations = require("../utils/periodCalculations");
const BusinessDate = require("../utils/BusinessDate");
const { invalidateEmployeeCache } = require("../middleware/cacheInvalidation");
const { routeCache } = require("../middleware/routeCache");
const {
  formatDateForDisplay,
  isProratedMonth,
  getEndOfMonthDate,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
  getCurrentOrNextLastDayOfMonth,
  getSundaysInPeriod,
  getCurrentPayPeriod,
} = dateUtils;
const { isValidObjectId } = mongoose;
const ExcelJS = require("exceljs");
const multer = require("multer");
const xlsx = require("xlsx");
const HourlyRate = require("../models/HourlyRate");
const Busboy = require("busboy");
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const { getHolidaysForRange } = require("../utils/holidayCalculator");
const timezoneUtils = require("../utils/timezoneUtils");
const payrollPeriodEngine = require("../utils/payrollPeriodEngine");

// Define default timezone for the application
const DEFAULT_TIMEZONE = "Africa/Johannesburg"; // South African timezone

// SIMPLE FIX: Remove complex business rule engine - use only database periods
function findPeriodByDate(databasePeriods, targetDate) {
  const targetDateString = moment.utc(targetDate).format('YYYY-MM-DD');

  // Find in database periods only
  const dbPeriod = databasePeriods.find(period => {
    if (!period.endDate) return false;
    return moment.utc(period.endDate).format('YYYY-MM-DD') === targetDateString;
  });

  if (dbPeriod) {
    return dbPeriod;
  }

  return null;
}

/**
 * Calculate employee-specific first payroll period end date based on DOA and pay frequency
 * @param {moment} employeeDOA - Employee's date of appointment
 * @param {Object} payFrequency - Pay frequency object with frequency and lastDayOfPeriod
 * @returns {moment} - Calculated first period end date
 */
function calculateEmployeeSpecificPeriodEndDate(employeeDOA, payFrequency) {
  const doa = employeeDOA.clone();

  switch (payFrequency.frequency.toLowerCase()) {
    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        // For month-end pay frequency, first period ends at the end of DOA month
        return doa.clone().endOf("month");
      } else {
        // For specific day of month (e.g., 15th)
        const targetDay = parseInt(payFrequency.lastDayOfPeriod);
        let periodEnd = doa.clone().date(targetDay).endOf("day");

        // If DOA is after the target day, move to next month's target day
        if (doa.date() > targetDay) {
          periodEnd = doa.clone().add(1, "month").date(targetDay).endOf("day");
        }

        return periodEnd;
      }

    case "weekly":
      // Map day names to numbers (0 = Sunday, 6 = Saturday)
      const daysOfWeek = {
        sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
        thursday: 4, friday: 5, saturday: 6
      };

      const targetDay = daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
      const currentDay = doa.day();

      // Calculate days to add to reach the next target day
      let daysToAdd = (targetDay - currentDay + 7) % 7;

      // If we're already on the target day, use DOA as the end date
      if (daysToAdd === 0) {
        return doa.clone().endOf("day");
      } else {
        // Go to the next occurrence of the target day
        return doa.clone().add(daysToAdd, "days").endOf("day");
      }

    case "bi-weekly":
    case "biweekly":
      // For bi-weekly, first period is 14 days from DOA
      return doa.clone().add(13, "days").endOf("day");

    default:
      // Default to end of month for unknown frequencies
      console.warn(`Unknown pay frequency: ${payFrequency.frequency}, defaulting to month-end`);
      return doa.clone().endOf("month");
  }
}

// Import the upload router
const uploadRouter = require("./upload");

// Configure multer with detailed logging
const storage = multer.memoryStorage();

// Initialize multer with unified configuration
const uploadMiddleware = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1,
    fields: 10,
    fieldSize: 10 * 1024 * 1024 // 10MB for field size
  },
  fileFilter: function (req, file, cb) {
    // Accept any Excel-like MIME type to be more permissive
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/octet-stream',
      'application/binary'
    ];

    const isValidMimeType = allowedTypes.includes(file.mimetype) ||
                           file.mimetype.includes('excel') ||
                           file.mimetype.includes('spreadsheet');

    if (!isValidMimeType) {
      console.error('Invalid mime type:', file.mimetype);
      return cb(new Error('Invalid file type. Only Excel files (.xlsx, .xls) are allowed.'));
    }

    const validExtension = /\.(xlsx|xls)$/i.test(file.originalname);
    if (!validExtension) {
      console.error('Invalid file extension:', file.originalname);
      return cb(new Error('Invalid file extension. Only .xlsx and .xls files are allowed.'));
    }

    cb(null, true);
  }
});

// Configure busboy options
const busboyOptions = {
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    fields: 5, // Allow up to 5 non-file fields
    files: 1, // Allow only 1 file
    parts: 6, // fields + files + potential metadata
  },
  preservePath: false,
};

// Add debug middleware
router.use((req, res, next) => {
  next();
});

// Add debug middleware for request tracking
router.use((req, res, next) => {
  next();
});

// Debug logging middleware
router.use((req, res, next) => {
  const requestId = uuidv4();
  req.requestId = requestId;
  const startTime = Date.now();

  next();
});

// Global error handler middleware
const handleError = (error, req, res, next) => {
  const errorId = uuidv4();
  console.error(
    `[${req.requestId || "NO_REQ_ID"}] [ErrorID: ${errorId}] ❌ Error:`,
    {
      message: error.message,
      stack: error.stack,
      code: error.code,
      path: req.path,
      method: req.method,
      params: req.params,
      query: req.query,
      user: req.user ? { id: req.user.id, email: req.user.email } : null,
    }
  );

  // Send appropriate error response
  res.status(error.status || 500).json({
    error: error.message,
    errorId,
    requestId: req.requestId,
  });
};

// Database operation logging middleware
const logDatabaseOperation = (operation) => async (req, res, next) => {
  console.log(`[${req.requestId}] 📝 Database operation: ${operation}`, {
    user: req.user ? { id: req.user.id, email: req.user.email } : null,
    params: req.params,
    method: req.method,
  });
  next();
};

// Transaction logging middleware
const logTransaction = (operation) => async (req, res, next) => {
  console.log(`[${req.requestId}] 🔄 Transaction started: ${operation}`, {
    user: req.user ? { id: req.user.id, email: req.user.email } : null,
    params: req.params,
  });
  next();
};

// Add this near the top of the file with other middleware imports
const checkAuth = (req, res, next) => {
  if (!req.isAuthenticated()) {
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }
  next();
};

function isNewEmployee(doa, currentMonth) {
  const doaMoment = moment(doa, "YYYY-MM-DD");
  const currentMoment = moment(currentMonth, "YYYY-MM-DD");

  if (doaMoment.isValid() && currentMoment.isValid()) {
    return doaMoment.isSame(currentMoment, "month");
  }

  console.error("Invalid date format:", { doa, currentMonth });
  return false;
}

// Add age calculation utility function
const calculateAge = (dob) => {
  if (!dob) return null;
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// Add this middleware to check if the user is authenticated
router.use(ensureAuthenticated);

// Apply the middleware to all routes in this file
router.use(ensureAuthenticated);
router.use(checkCompany);

// Add this route before other routes that might conflict
router.get(
  "/:companyCode/employeeManagement/api/holidays",
  ensureAuthenticated,
  async (req, res) => {
    try {
      // Validate query parameters
      const startYear = parseInt(req.query.startYear) || new Date().getFullYear();
      const endYear = parseInt(req.query.endYear) || startYear;

      // Validate year range
      if (startYear > endYear) {
        return res.status(400).json({
          error: "Invalid date range",
          details: "Start year cannot be greater than end year"
        });
      }

      // Validate reasonable year range (e.g., not too far in the past or future)
      const currentYear = new Date().getFullYear();
      if (startYear < currentYear - 1 || endYear > currentYear + 2) {
        return res.status(400).json({
          error: "Invalid year range",
          details: "Years must be within a reasonable range"
        });
      }

      // Get holidays using the utility function
      const holidays = getHolidaysForRange(startYear, endYear);

      // Set cache headers for better performance
      res.set({
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        'ETag': `"holidays-${startYear}-${endYear}"`,
      });

      res.json(holidays);
    } catch (error) {
      console.error("Error fetching public holidays:", error);
      res.status(500).json({
        error: "Failed to fetch public holidays",
        details: error.message,
      });
    }
  }
);

// Leave Overview Route
router.get(
  "/:companyCode/employeeManagement/leaveOverview",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get the authenticated user
      const user = await User.findById(req.user._id);

      res.render("leaveOverview", {
        user,
        company,
        page: "leaveOverview",
      });
    } catch (error) {
      console.error("Error in leave overview route:", error);
      req.flash(
        "error_msg",
        "An error occurred while loading the leave overview"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Employee Leave Route
router.get(
  "/:companyCode/employeeManagement/employee/:employeeId/leave",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const employeeId = req.params.employeeId;

      // Get company details
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get employee details (populate payFrequency for consistency with employeeProfile)
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        req.flash("error_msg", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get leave types with full details
      const leaveTypes = await LeaveType.find({
        company: company._id,
        active: true,
        $or: [{ gender: "all" }, { gender: employee.gender }],
      });

      // Get leave balances with accrual calculations
      const leaveBalances = await LeaveBalance.find({
        employee: employeeId,
        year: new Date().getFullYear(),
      }).populate("leaveType");

      // Calculate actual available balance considering accrual
      const leaveTypesWithBalances = leaveTypes.map((type) => {
        const balance = leaveBalances.find(
          (b) =>
            b.leaveType && b.leaveType._id.toString() === type._id.toString()
        );

        let availableBalance = 0;
        let monthlyAmount = 0;

        // Check for employee-specific allocation
        const specificAllocation = type.employeeAllocations?.find(
          (a) => a.employee.toString() === employeeId
        );

        const totalAllocation = specificAllocation
          ? specificAllocation.daysPerYear
          : type.daysPerYear;

        if (balance) {
          if (type.accrualRate === "yearly") {
            // Recalculate remaining balance to ensure accuracy
            availableBalance = balance.calculateRemaining();
          } else if (type.accrualRate === "monthly") {
            monthlyAmount = Number((totalAllocation / 12).toFixed(2));
            availableBalance = type.calculateAccruedDays(
              employee.startDate,
              new Date()
            );
          }
        } else {
          // If no balance record exists, create default values
          availableBalance =
            type.accrualRate === "yearly"
              ? totalAllocation
              : type.calculateAccruedDays(employee.startDate, new Date());
        }

        return {
          _id: type._id,
          name: type.name,
          icon: type.icon || "ph-calendar",
          balance: availableBalance,
          total: totalAllocation,
          accrualRate: type.accrualRate,
          monthlyAmount,
          minDaysNotice: type.minDaysNotice,
          requiresDocument: type.requiresDocument,
          documentRequiredAfterDays: type.documentRequiredAfterDays,
          gender: type.gender,
        };
      });

      // Calculate date of birth formatting (consistent with employeeProfile)
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Calculate pay frequency display (consistent with employeeProfile)
      const payFrequency = employee.payFrequency?.name || "N/A";

      // Render the leave page
      res.render("employeeLeave", {
        company,
        employee,
        user: req.user,
        leaveTypes: leaveTypesWithBalances,
        dobFormatted,
        payFrequency,
        title: "Employee Leave Management",
      });
    } catch (error) {
      console.error("Error in employee leave route:", error);
      req.flash("error_msg", "Error loading employee leave page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Make sure this route comes after the holidays API route
router.get(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  routeCache.cache({ ttl: 300 }), // Cache for 5 minutes
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Session ID:", req.sessionID);

    try {
      // Get company ID from user object or support access session
      let companyId;
      if (req.user?.currentCompany) {
        companyId = req.user.currentCompany._id || req.user.currentCompany;
      } else if (req.session?.supportAccess) {
        companyId = req.session.supportAccess.companyId;
      } else {
        console.log("No company context found");
        return res.status(400).send("No company context available");
      }

      const company = await Company.findById(companyId);

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the current company (optimized with lean())
      const employees = await Employee.find({ company: company._id })
        .select("companyEmployeeNumber firstName lastName costCentre status dob doa")
        .lean(); // Use lean() for better performance when not modifying documents

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

// Keep the existing route for /:companyIdentifier/employeeManagement
router.get(
  "/:companyIdentifier/employeeManagement",
  ensureAuthenticated,
  ensureOwner,
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Company identifier:", req.params.companyIdentifier);
    console.log("Session ID:", req.sessionID);

    try {
      const companyIdentifier = req.params.companyIdentifier;
      let company;

      if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
        company = await Company.findById(companyIdentifier);
      } else {
        company = await Company.findOne({ companyCode: companyIdentifier });
      }

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the selected company (optimized with lean())
      const employees = await Employee.find({ company: company._id })
        .select("companyEmployeeNumber firstName lastName costCentre status dob doa")
        .lean(); // Use lean() for better performance

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const periodStartDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // 🚨🚨🚨 VERIFICATION TEST MARKER - CORRECT ROUTE HANDLER FOUND! 🚨🚨🚨
      console.log("🔥🔥🔥 VERIFICATION: PERIOD SELECTION FIX ROUTE HANDLER IS EXECUTING! 🔥🔥🔥");
      console.log("🎯 TEST MARKER: employeeManagement.js period selection fix - TIMESTAMP:", new Date().toISOString());
      console.log("🎯 TEST MARKER: Employee ID:", employeeId);
      console.log("🎯 TEST MARKER: Company Code:", companyCode);
      console.log("🎯 PERIOD SELECTION: selectedMonth:", selectedMonth);
      console.log("🎯 PERIOD SELECTION: viewFinalized:", viewFinalized);

      // Initialize basicSalary - will be updated with period-specific value later
      const defaultBasicSalary = 0;
      let basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // SIMPLE FIX: Remove complex period generation - use only database periods
      console.log("📊 SIMPLE: Using database periods only");

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        console.log("Generating periods with PayFrequency settings:", {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
          firstPayrollPeriodEndDate: moment
            .tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
            .format("YYYY-MM-DD HH:mm:ss"),
        });

        // Use moment.tz for all dates
        const employeeStartDate = moment
          .tz(employee.doa, DEFAULT_TIMEZONE)
          .startOf("day");
        const currentEndDate = moment
          .tz(new Date(), DEFAULT_TIMEZONE)
          .endOf("day");

        console.log("Period Generation Dates:", {
          employeeStartDate: employeeStartDate.format("YYYY-MM-DD"),
          currentEndDate: currentEndDate.format("YYYY-MM-DD"),
        });

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          // Use BusinessDate for timezone-independent pro-rata calculations
          const periodStartBusiness = period.startDateBusiness || BusinessDate.fromDate(period.startDate);
          const periodEndBusiness = period.endDateBusiness || BusinessDate.fromDate(period.endDate);

          const proratedResult = BusinessDate.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            periodStartBusiness,
            periodEndBusiness
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency
          );

          // 🚨 CRITICAL FIX: Preserve original basicSalary from database
          const originalBasicSalary = Number(period.basicSalary || 0);

          console.log("🔧 PRESERVING ORIGINAL BASIC SALARY:", {
            periodId: period._id,
            originalBasicSalary: originalBasicSalary,
            currentPayrollBasicSalary: Number(basicSalary),
            periodEndDate: period.endDate
          });

          return {
            ...period.toObject(),
            // Preserve the original historical basicSalary from PayrollPeriod database record
            basicSalary: originalBasicSalary,
            // Store current salary separately for calculations if needed
            currentPayrollBasicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // FIXED: Enhanced period selection logic to prioritize most recent unfinalized period
      let currentPeriod;
      let periodSelectionMethod;

      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        // SIMPLE FIX: Use only database periods
        currentPeriod = findPeriodByDate(payPeriodsWithDetails, selectedMonth);
        periodSelectionMethod = 'specific_finalized_period';
        console.log("🎯 PERIOD SELECTION: Using specific finalized period from query params");
        console.log("🔍 Database Period Match:", {
          selectedMonth: moment.utc(selectedMonth).format('YYYY-MM-DD'),
          foundPeriod: currentPeriod ? moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') : 'none',
          source: 'database'
        });
      } else if (selectedMonth) {
        // If a specific month is selected (but not necessarily finalized)
        // SIMPLE FIX: Use only database periods
        currentPeriod = findPeriodByDate(payPeriodsWithDetails, selectedMonth);
        console.log("🔍 Database Period Match:", {
          selectedMonth: moment.utc(selectedMonth).format('YYYY-MM-DD'),
          foundPeriod: currentPeriod ? moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') : 'none',
          source: 'database'
        });
        periodSelectionMethod = 'specific_selected_period';
        console.log("🎯 PERIOD SELECTION: Using specific selected period from query params");
      } else {
        // CRITICAL FIX: Default to most recent unfinalized period
        console.log("🎯 PERIOD SELECTION: Finding most recent unfinalized period as default...");

        // Find all unfinalized periods and sort by endDate descending to get the most recent
        const unfinalizedPeriods = payPeriodsWithDetails
          .filter(p => !p.isFinalized)
          .sort((a, b) => new Date(b.endDate) - new Date(a.endDate));

        if (unfinalizedPeriods.length > 0) {
          currentPeriod = unfinalizedPeriods[0]; // Most recent unfinalized period
          periodSelectionMethod = 'most_recent_unfinalized';
          console.log("✅ PERIOD SELECTION: Found most recent unfinalized period:", {
            periodId: currentPeriod._id,
            endDate: currentPeriod.endDate.toISOString(),
            isFinalized: currentPeriod.isFinalized
          });
        } else {
          // Fallback: Use the most recent period (even if finalized)
          currentPeriod = payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
          periodSelectionMethod = 'most_recent_any';
          console.log("⚠️ PERIOD SELECTION: No unfinalized periods found, using most recent period");
        }
      }

      // 🚨 PERIOD SELECTION DEBUG - Log final selection result
      console.log("🎯 FINAL PERIOD SELECTION RESULT:", {
        periodSelectionMethod: periodSelectionMethod,
        selectedPeriodId: currentPeriod?._id,
        selectedPeriodEndDate: currentPeriod?.endDate?.toISOString(),
        selectedPeriodFinalized: currentPeriod?.isFinalized,
        queryParams: { selectedMonth, viewFinalized },
        timestamp: new Date().toISOString()
      });

      // Calculate initial period values
      const currentPeriodCalculations =
        await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod?.endDate ||
            moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
          company._id
        );

      console.log("Initial Period Calculations:", currentPeriodCalculations);
      // After fetching the currentPeriod, add this code to fetch hourly rates
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          }).lean()
        : null;

      // Ensure to log the hourly rates for debugging
      console.log("Hourly rates found:", {
        found: !!hourlyRates,
        normalHours: hourlyRates?.normalHours || 0,
        overtimeHours: hourlyRates?.overtimeHours || 0,
        sundayHours:
          hourlyRates?.weeks?.reduce(
            (total, week) => total + (Number(week.sundayHours) || 0),
            0
          ) || 0,
      });
      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );

      // Use the totalIncome directly from currentPeriodCalculations
      // This will include Loss of Income as calculated in PayrollService.calculatePayrollTotals
      const totalIncome = Number(
        currentPeriodCalculations.totalIncome || proratedSalary
      );

      console.log("Salary Calculations:", {
        basicSalary,
        proratedSalary,
        totalIncome,
        lossOfIncome: payroll?.lossOfIncome || 0, // Log Loss of Income for debugging
        isProrated: currentPeriodCalculations.proRataDetails?.isProrated,
      });

      // Initialize payroll totals with the correct values
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome,
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0)
        ),
      };

      console.log("Final Payroll Totals:", payrollTotals);

      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        hourlyRates, // Ensure this line is included
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
        // SIMPLE FIX: Remove generated periods - use only database periods
        payFrequency: payFrequency, // Add pay frequency for template access
        selectedMonth: selectedMonth,
        viewFinalized: viewFinalized,
        periodSelectionMethod: periodSelectionMethod
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(
            currentPeriodCalculations.basicSalary?.full ||
              currentPeriodCalculations.basicSalary ||
              0
          ),
          proratedSalary: Number(
            currentPeriodCalculations.basicSalary?.prorated ||
              currentPeriodCalculations.proratedSalary ||
              0
          ),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(
            currentPeriodCalculations.proRataDetails?.percentage || 100
          ),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(
            currentPeriodCalculations.totalDeductions || 0
          ),
          netPay: Number(currentPeriodCalculations.netPay || 0),
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(new Date(), DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const age = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;
      const pensionFundDeduction =
        payroll?.pensionFund?.fixedContributionEmployee || 0;
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // 🎯 COMPREHENSIVE PERIOD-SPECIFIC LOGIC
      console.log("🔍 PERIOD SELECTION DEBUG:", {
        selectedMonth,
        viewFinalized,
        hasCurrentPeriod: !!currentPeriod,
        payPeriodsCount: payPeriodsWithDetails.length
      });

      // Find the specific payroll period based on selection parameters
      let payrollPeriod;
      // Note: periodSelectionMethod already declared above

      if (selectedMonth) {
        // User has selected a specific month/period
        // CRITICAL FIX: Use timezone-safe period finding
        if (viewFinalized === "true") {
          // Viewing a finalized period
          payrollPeriod = timezoneUtils.findPeriodByDate(payPeriodsWithDetails, selectedMonth);
          periodSelectionMethod = "finalized_period";
        } else {
          // Viewing a specific month (could be current or historical)
          payrollPeriod = timezoneUtils.findPeriodByDate(payPeriodsWithDetails, selectedMonth);
          periodSelectionMethod = "selected_month";
        }
      } else {
        // No specific month selected, use current period
        payrollPeriod = currentPeriod;
        periodSelectionMethod = "current_period";
      }

      console.log("🎯 PERIOD SELECTION RESULT:", {
        method: periodSelectionMethod,
        foundPeriod: !!payrollPeriod,
        periodId: payrollPeriod?._id,
        periodBasicSalary: payrollPeriod?.basicSalary
      });

      // 🎯 PURE DATABASE RETRIEVAL - BYPASS ALL CALCULATIONS
      let periodSpecificBasicSalary = defaultBasicSalary;
      let salarySource = "default";
      let directDatabasePeriod = null;

      // 🚨 CRITICAL FIX: Determine if we should use current period regardless of selectedMonth
      // Find the actual current (non-finalized) period
      const actualCurrentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false
      }).sort({ endDate: -1 }).lean();

      console.log("🔍 CURRENT PERIOD DETECTION:", {
        selectedMonth: selectedMonth,
        viewFinalized: viewFinalized,
        actualCurrentPeriodId: actualCurrentPeriod?._id,
        actualCurrentPeriodEndDate: actualCurrentPeriod?.endDate,
        payrollPeriodId: payrollPeriod?._id,
        shouldUseCurrentPeriod: selectedMonth && viewFinalized === "false"
      });

      // Step 1: Determine which period to query directly from database
      if (selectedMonth && viewFinalized === "true") {
        // Viewing a finalized period - use selectedMonth
        console.log("🔍 DIRECT DATABASE QUERY for finalized period:", selectedMonth);

        // CRITICAL FIX: Use timezone-safe date range query
        const dateRange = timezoneUtils.createDateRange(selectedMonth);

        directDatabasePeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: dateRange
        }).lean();

        timezoneUtils.debugDate("Database Query Date", selectedMonth);
        console.log("🔍 Database query result:", {
          found: !!directDatabasePeriod,
          periodId: directDatabasePeriod?._id
        });

        if (directDatabasePeriod) {
          periodSpecificBasicSalary = Number(directDatabasePeriod.basicSalary || 0);
          salarySource = "direct_database_query_finalized";

          console.log("🎯 DIRECT DATABASE RESULT (FINALIZED):", {
            periodId: directDatabasePeriod._id,
            rawBasicSalary: directDatabasePeriod.basicSalary,
            convertedBasicSalary: periodSpecificBasicSalary,
            endDate: directDatabasePeriod.endDate
          });
        }
      } else {
        // Either no selectedMonth OR viewFinalized is false - use current period
        console.log("🔍 DIRECT DATABASE QUERY for current period:", actualCurrentPeriod?._id);

        if (actualCurrentPeriod) {
          directDatabasePeriod = actualCurrentPeriod;
          periodSpecificBasicSalary = Number(directDatabasePeriod.basicSalary || 0);
          salarySource = "direct_database_query_current";

          console.log("🎯 DIRECT DATABASE RESULT (CURRENT):", {
            periodId: directDatabasePeriod._id,
            rawBasicSalary: directDatabasePeriod.basicSalary,
            convertedBasicSalary: periodSpecificBasicSalary,
            endDate: directDatabasePeriod.endDate,
            isFinalized: directDatabasePeriod.isFinalized
          });
        }
      }

      // Step 2: Fallback to processed payrollPeriod if direct query failed
      if (!directDatabasePeriod && payrollPeriod && payrollPeriod.basicSalary !== undefined && payrollPeriod.basicSalary !== null) {
        periodSpecificBasicSalary = Number(payrollPeriod.basicSalary);
        salarySource = "processed_payroll_period";
      } else if (!directDatabasePeriod && payroll?.basicSalary) {
        // Final fallback to current payroll basicSalary
        periodSpecificBasicSalary = Number(payroll.basicSalary);
        salarySource = "current_payroll_fallback";
      }

      // Update basicSalary with the period-specific value
      basicSalary = periodSpecificBasicSalary;

      console.log("🎯 CURRENT PERIOD INHERITANCE FIX - FINAL COMPARISON:", {
        hasSelectedMonth: !!selectedMonth,
        viewFinalized: viewFinalized,
        usingCurrentPeriod: salarySource === "direct_database_query_current",
        directDatabaseValue: directDatabasePeriod?.basicSalary || "N/A",
        processedPeriodValue: payrollPeriod?.basicSalary || "N/A",
        finalBasicSalary: basicSalary,
        salarySource: salarySource,
        selectionMethod: periodSelectionMethod,
        selectedMonth: selectedMonth || "none",
        actualCurrentPeriodId: actualCurrentPeriod?._id || "N/A",
        isFinalized: directDatabasePeriod?.isFinalized || "N/A",
        valuesMatch: directDatabasePeriod ? (directDatabasePeriod.basicSalary === payrollPeriod?.basicSalary) : "N/A"
      });

      // If no period is found, create a default one with the resolved basicSalary
      if (!payrollPeriod) {
        console.log("🔧 CREATING DEFAULT PERIOD with basicSalary:", basicSalary);

        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month"); // Always start from the 1st
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary, // Use the resolved period-specific basicSalary
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };

        salarySource = "default_period_created";
      }

      console.log("🚨 FINAL VERIFICATION - PERIOD-SPECIFIC BASIC SALARY:", {
        finalBasicSalary: basicSalary,
        salarySource: salarySource,
        periodId: payrollPeriod._id || "default",
        timestamp: new Date().toISOString()
      });

      // Get current payroll period end date
      const currentPayrollDate = getCurrentOrNextLastDayOfMonth();

      console.log("=== Employee Profile Debug ===", {
        employeeId: employee._id,
        currentDate: currentPayrollDate,
        payFrequency: employee.payFrequency,
        doa: employee.doa,
      });

      // Calculate basic salary first
      // 🚨 CRITICAL FIX: Use period-specific basicSalary instead of current payroll
      const fullBasicSalary = basicSalary; // Use our period-specific value

      console.log("🔧 FULL BASIC SALARY ASSIGNMENT:", {
        originalPayrollBasicSalary: payroll?.basicSalary || 0,
        periodSpecificBasicSalary: basicSalary,
        finalFullBasicSalary: fullBasicSalary
      });

      // Calculate period dates using BusinessDate for timezone independence
      const calculatedPeriodDates = (() => {
        if (payrollPeriod) {
          // Use BusinessDate fields if available, fallback to legacy Date fields
          const startDateBusiness = payrollPeriod.startDateBusiness || BusinessDate.fromDate(payrollPeriod.startDate);
          const endDateBusiness = payrollPeriod.endDateBusiness || BusinessDate.fromDate(payrollPeriod.endDate);

          return {
            startDate: startDateBusiness,
            endDate: endDateBusiness,
          };
        } else {
          // Default to current month if no period specified
          const currentDate = BusinessDate.today();
          return {
            startDate: BusinessDate.startOfMonth(currentDate),
            endDate: BusinessDate.endOfMonth(currentDate),
          };
        }
      })();

      // Calculate period dates using BusinessDate for timezone independence

      // FOCUSED FIX: Ensure correct period dates for pro-rata calculation
      let correctedPeriodStart = calculatedPeriodDates.startDate;
      let correctedPeriodEnd = calculatedPeriodDates.endDate;

      // If we have a payroll period with end date 2025-05-31, ensure start date is 2025-05-01
      if (correctedPeriodEnd === '2025-05-31' && correctedPeriodStart !== '2025-05-01') {
        correctedPeriodStart = '2025-05-01';
      }

      // More general fix: if end date is last day of month, ensure start is first day of month
      if (correctedPeriodEnd && (correctedPeriodEnd.endsWith('-31') || correctedPeriodEnd.endsWith('-30') || correctedPeriodEnd.endsWith('-28') || correctedPeriodEnd.endsWith('-29'))) {
        const endDateParts = correctedPeriodEnd.split('-');
        const expectedStart = `${endDateParts[0]}-${endDateParts[1]}-01`;
        if (correctedPeriodStart !== expectedStart) {
          correctedPeriodStart = expectedStart;
        }
      }

      // Calculate prorated salary using BusinessDate for timezone independence
      const proratedSalaryResult = BusinessDate.calculateProratedSalary(
        employee.doa,
        fullBasicSalary,
        employee,
        correctedPeriodStart,
        correctedPeriodEnd
      );



      // Get the pro-rata details from the payroll calculations
      const proRataDetails = {
        isProrated: true,
        percentage: parseFloat(proratedSalaryResult.proratedPercentage),
        daysWorked: proratedSalaryResult.workedDays,
        totalDays: proratedSalaryResult.totalDaysInPeriod,
      };

      // Use the properly calculated prorated salary from the utility function
      const calculatedProratedAmount = parseFloat(
        proratedSalaryResult.proratedSalary
      );

      // Calculate annual salary for PAYE
      const annualSalary = fullBasicSalary * 12;

      // Calculate PAYE using enhanced calculation from utils
      const payeCalculation = payrollCalculations.calculateEnhancedPAYE({
        annualSalary,
        age,
        frequency: "monthly",
        accommodationBenefit, // Add accommodation benefit here
        travelAllowance: payroll?.travelAllowance?.fixedAllowanceAmount || 0 // Add travel allowance
      });

      // Get monthly PAYE and apply pro-rata
      const monthlyPAYE = payeCalculation.monthlyPAYE;
      const proratedPAYE = parseFloat(
        ((monthlyPAYE * proRataDetails.percentage) / 100).toFixed(2)
      );

      // Calculate UIF (capped at 177.12)
      const proratedUIF = Math.min(
        parseFloat((calculatedProratedAmount * 0.01).toFixed(2)),
        177.12
      );

      const totalDeductions = parseFloat(
        (proratedPAYE + proratedUIF).toFixed(2)
      );

      console.log("=== PAYE Calculation Debug ===", {
        annualSalary,
        payeCalculation,
        monthlyPAYE,
        proratedPAYE,
      });

      console.log("=== Payroll Details Debug ===", {
        payrollDetails: {
          basicSalary: fullBasicSalary.toFixed(2),
          proRataDetails: {
            isProrated: proRataDetails.isProrated,
            percentage: proRataDetails.percentage,
            daysWorked: proRataDetails.daysWorked,
            totalDays: proRataDetails.totalDays,
          },
          isProrated: proRataDetails.isProrated,
          proratedAmount: calculatedProratedAmount.toFixed(2),
          proratedPAYE: proratedPAYE.toFixed(2),
          proratedUIF: proratedUIF.toFixed(2),
          totalDeductions: totalDeductions.toFixed(2),
        },
      });

      // Update render data with calculated values
      console.log("=== Initial Render Data Values ===", {
        proratedSalaryFromCalc: proratedSalaryResult.proratedSalary,
        basicSalaryValue: fullBasicSalary,
      });

      renderData.proratedSalary = parseFloat(
        proratedSalaryResult.proratedSalary
      );
      // 🚨 CRITICAL FIX: Ensure renderData uses period-specific basicSalary
      renderData.basicSalary = fullBasicSalary; // Now uses period-specific value
      renderData.isProrated = proRataDetails.isProrated;

      console.log("🔧 RENDER DATA BASIC SALARY ASSIGNMENT:", {
        renderDataBasicSalary: renderData.basicSalary,
        fullBasicSalary: fullBasicSalary,
        originalPeriodBasicSalary: payrollPeriod?.basicSalary,
        shouldMatch: renderData.basicSalary === (payrollPeriod?.basicSalary || basicSalary)
      });
      renderData.proRataDetails = proRataDetails;
      
      // Include Loss of Income in totalIncome calculation
      const lossOfIncomeAmount = payroll?.lossOfIncome || 0;
      renderData.totalIncome = parseFloat(proratedSalaryResult.proratedSalary) + parseFloat(lossOfIncomeAmount);
      
      renderData.totalPAYE = proratedPAYE;
      renderData.totalUIF = proratedUIF;
      renderData.totalDeductions = totalDeductions;
      renderData.nettPay = parseFloat(
        (
          renderData.totalIncome - totalDeductions
        ).toFixed(2)
      );

      // Add payrollDetails for the template
      renderData.payrollDetails = {
        basicSalary: fullBasicSalary,
        proRataDetails: proRataDetails,
        isProrated: proRataDetails.isProrated,
        proratedAmount: proratedSalaryResult.proratedSalary,
        proratedPAYE: proratedPAYE.toFixed(2),
        proratedUIF: proratedUIF.toFixed(2),
        totalDeductions: totalDeductions.toFixed(2),
        totalIncome: renderData.totalIncome.toFixed(2), // Include totalIncome in payrollDetails
      };

      console.log("=== Pre-Final Render Data Debug ===", {
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        isProrated: renderData.isProrated,
        proRataDetails: renderData.proRataDetails,
        totalIncome: renderData.totalIncome,
        payrollDetails: renderData.payrollDetails,
      });

      // Prevent later calculations from overwriting our prorated values
      const finalRenderData = {
        currentPeriod: true,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        totalIncome: renderData.totalIncome,
        totalPAYE: renderData.totalPAYE,
        totalUIF: renderData.totalUIF,
        totalDeductions: renderData.totalDeductions,
        nettPay: renderData.nettPay,
        payrollDetails: renderData.payrollDetails,
        proRataDetails: renderData.proRataDetails,
      };

      console.log("=== Protected Final Render Data ===", finalRenderData);

      // Use Object.assign to update renderData instead of reassignment
      Object.assign(renderData, finalRenderData);

      console.log("=== Final Render Data Debug ===", {
        proratedSalary: renderData.proratedSalary.toFixed(2),
        basicSalary: renderData.basicSalary.toFixed(2),
        isProrated: renderData.isProrated,
        proRataDetails: renderData.proRataDetails,
        totalPAYE: renderData.totalPAYE.toFixed(2),
        totalUIF: renderData.totalUIF.toFixed(2),
        totalDeductions: renderData.totalDeductions.toFixed(2),
        nettPay: renderData.nettPay.toFixed(2),
      });

      // Get current payroll data
      // Get current payroll data
      const taxableIncome = payrollCalculations.calculateTotalTaxableIncome(
        payroll || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly"
      );

      // Calculate all deductions using new function
      const deductionsResult = payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome
      );

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge: age,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });

      console.log("Detailed Payroll Calculations Debug", {
        paye: {
          amount: currentPeriodCalculations.paye,
          monthlyTax: currentPeriodCalculations.paye,
        },
        uif: {
          amount: currentPeriodCalculations.uif,
        },
        deductions: {
          statutory: {
            paye: currentPeriodCalculations.paye,
            uif: currentPeriodCalculations.uif,
          },
          total: currentPeriodCalculations.totalDeductions,
        },
        fullCalculations: currentPeriodCalculations,
      });

      // Travel Allowance Calculation
      console.log("Travel Allowance Debug - Payroll Object", {
        travelAllowance: payroll?.travelAllowance,
        hasFixedAllowance: !!payroll?.travelAllowance?.fixedAllowance,
        hasReimbursedExpenses: !!payroll?.travelAllowance?.reimbursedExpenses,
      });

      if (
        payroll?.travelAllowance?.fixedAllowance ||
        payroll?.travelAllowance?.reimbursedExpenses
      ) {
        const travelAllowanceResult =
          await PayrollService.processTravelAllowance(employee, payroll);

        console.log(
          "Travel Allowance Processing Result",
          travelAllowanceResult
        );

        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;

          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: 20,
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: 80,
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes,
          };
        }
      }

      // Log employee age details
      console.log("🎂 Employee Profile Age Debug:", {
        "Employee ID": employeeId,
        "Date of Birth": employee.dob
          ? moment(employee.dob).format("YYYY-MM-DD")
          : "N/A",
        "Calculated Age": age,
        "Company Code": companyCode,
      });

      // Add debug logging
      console.log("Render Data:", {
        currentPeriod: !!currentPeriod,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        ...renderData.payrollTotals,
      });

      // 🚨 FINAL VERIFICATION: Ensure basicSalary is correct before template render
      console.log("🔥 FINAL TEMPLATE DATA VERIFICATION:", {
        renderDataBasicSalary: renderData.basicSalary,
        directDatabaseBasicSalary: directDatabasePeriod?.basicSalary,
        periodSpecificBasicSalary: basicSalary,
        fullBasicSalary: fullBasicSalary,
        templateWillReceive: renderData.basicSalary
      });

      // 🚨 CRITICAL FIX: Override renderData.basicSalary with direct database value
      if (directDatabasePeriod && directDatabasePeriod.basicSalary !== undefined) {
        renderData.basicSalary = Number(directDatabasePeriod.basicSalary);
        console.log("🔧 OVERRIDING renderData.basicSalary with direct database value:", renderData.basicSalary);
      }

      // Pass the data directly to the template
      res.render("employeeProfile", {
        ...renderData,
        // 🚨 DOUBLE PROTECTION: Explicitly pass the correct basicSalary
        basicSalary: directDatabasePeriod ? Number(directDatabasePeriod.basicSalary) : basicSalary,
        totalPAYE: proratedPAYE,
        totalUIF: proratedUIF,
        totalDeductions: totalDeductions,
        nettPay: parseFloat(
          (calculatedProratedAmount - totalDeductions).toFixed(2)
        ),
        // 🎯 BusinessDate Pro-rated Salary Results
        proratedSalaryResult: proratedSalaryResult,
        proRataDetails: proRataDetails,
        isProrated: proratedSalaryResult.isFirstPeriodWithDOA,
        // 🚨 VERIFICATION TEST MARKER - This will appear on the webpage 🚨
        testMarker: "🔥 PERIOD SELECTION FIX ACTIVE - DEFAULTS TO UNFINALIZED PERIOD 🔥",
        testTimestamp: new Date().toISOString(),
        // 🎯 DEBUGGING INFO FOR PURE DATABASE RETRIEVAL
        debugInfo: {
          selectedMonth: selectedMonth || "none",
          viewFinalized: viewFinalized || "false",
          periodSelectionMethod: periodSelectionMethod,
          salarySource: salarySource,
          periodId: payrollPeriod?._id || "default",
          periodBasicSalary: payrollPeriod?.basicSalary || "N/A",
          directDatabaseValue: directDatabasePeriod?.basicSalary || "N/A",
          usingDirectDatabase: !!directDatabasePeriod,
          // BusinessDate calculation details
          proratedCalculation: {
            workedDays: proratedSalaryResult.workedDays,
            totalDays: proratedSalaryResult.totalDaysInPeriod,
            percentage: proratedSalaryResult.proratedPercentage,
            proratedAmount: proratedSalaryResult.proratedSalary,
            fullAmount: proratedSalaryResult.fullPeriodSalary
          }
        },
      });
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "error",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("=== Add Basic Salary Route Debug ===", {
        companyCode,
        employeeId,
        params: req.params,
      });

      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get fully populated employee object with company validation
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      })
        .populate({
          path: "payFrequency",
          model: "PayFrequency",
        })
        .populate({
          path: "company",
          model: "Company",
        });

      if (!employee) {
        console.error("Employee not found in company:", employeeId, companyCode);
        req.flash("error", "Employee not found in the specified company");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      console.log("Found Employee:", {
        id: employee._id,
        name: `${employee.firstName} ${employee.lastName}`,
        status: employee.status,
        payFrequency: employee.payFrequency,
      });

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        req.flash(
          "error",
          `Cannot edit salary for ${employee.status} employee`
        );
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      if (!employee.payFrequency) {
        req.flash("error", "Employee pay frequency not configured");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // FIXED: Get current period based on user selection first, then default to unfinalized
      let currentPeriod;
      let selectedMonth;

      if (req.query.selectedMonth) {
        // PRIORITY FIX: If a specific month is selected, use that period (respects user choice)
        // CRITICAL FIX: Use UTC parsing to avoid timezone conversion issues
        selectedMonth = moment.utc(req.query.selectedMonth).endOf('day').toDate();
        console.log("🎯 User selected specific period for addBasicSalary:", moment.utc(selectedMonth).format('YYYY-MM-DD'));

        // Find the exact period for the selected date with range search for better matching
        // Use timezone-aware date calculations for range search
        const dayBefore = moment.tz(selectedMonth, DEFAULT_TIMEZONE).subtract(1, 'day').toDate();
        const dayAfter = moment.tz(selectedMonth, DEFAULT_TIMEZONE).add(1, 'day').toDate();
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: dayBefore,
            $lte: dayAfter
          }
        });

        if (!currentPeriod) {
          // Try exact match if range search fails
          currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            company: company._id,
            endDate: selectedMonth,
          });
        }

        console.log("✅ Found selected period for addBasicSalary:", currentPeriod ? {
          periodId: currentPeriod._id,
          endDate: currentPeriod.endDate.toISOString(),
          basicSalary: currentPeriod.basicSalary,
          isFinalized: currentPeriod.isFinalized
        } : "No period found for selected date");

      } else {
        // DEFAULT: Only use earliest unfinalized period when no specific selection
        console.log("🎯 No specific period selected for addBasicSalary, using earliest unfinalized period as default...");
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          isFinalized: false,
        }).sort({ endDate: 1 }); // Get the earliest non-finalized period

        if (currentPeriod) {
          console.log("✅ Found unfinalized period for addBasicSalary:", {
            periodId: currentPeriod._id,
            endDate: currentPeriod.endDate.toISOString(),
            basicSalary: currentPeriod.basicSalary,
            isFinalized: currentPeriod.isFinalized
          });
        }
      }

      if (!currentPeriod) {
        try {
          // Generate a new period using PayrollService
          currentPeriod = await PayrollService.generateNextPeriod(
            employee,
            null, // No previous period
            null // No session needed for read operation
          );
        } catch (error) {
          console.error("Error generating period:", error);
          req.flash("error", `Failed to generate pay period: ${error.message}`);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }

      // Validate period end date against termination date
      if (employee.lastDayOfService) {
        const terminationDate = moment(employee.lastDayOfService).endOf("day");
        const periodEndDate = moment(currentPeriod.endDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          req.flash(
            "error",
            "Cannot edit salary for period after termination date"
          );
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }

      // Find or create payroll for the current period
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: currentPeriod.endDate,
      });

      console.log(
        "Existing Payroll:",
        payroll
          ? {
              id: payroll._id,
              month: moment(payroll.month).format("YYYY-MM-DD"),
              basicSalary: payroll.basicSalary,
              hourlyPaid: payroll.hourlyPaid,
            }
          : "Not found"
      );

      console.log("Rendering addBasicSalary with BusinessDate:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll?._id,
        currentPeriod: {
          id: currentPeriod._id,
          startDateBusiness: currentPeriod.businessStartDate,
          endDateBusiness: currentPeriod.businessEndDate,
          legacyStartDate: moment(currentPeriod.startDate).format("YYYY-MM-DD"),
          legacyEndDate: moment(currentPeriod.endDate).format("YYYY-MM-DD"),
          basicSalary: currentPeriod.basicSalary,
        },
        basicSalary: payroll?.basicSalary || currentPeriod.basicSalary || 0,
        hourlyPaid: payroll?.hourlyPaid || false,
        selectedMonth: req.query.selectedMonth,
        periodSelectionMethod: req.query.selectedMonth ? 'USER_SELECTED' : 'DEFAULT_UNFINALIZED',
      });

      // Create a default payroll object if none exists
      const defaultPayroll = payroll || {
        basicSalary: currentPeriod.basicSalary || 0,
        hourlyPaid: false,
        dontAutoPayPublicHolidays: false,
        additionalHours: false,
      };

      // Create a default current period if none exists
      const defaultCurrentPeriod = currentPeriod || {
        _id: "new",
        startDate: new Date(),
        endDate: new Date(),
        basicSalary: 0,
      };

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod: defaultCurrentPeriod,
        payroll: defaultPayroll,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req,
        path: req.path,
        moment,
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    // Start a database session for transaction support
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      console.log("=== Update Basic Salary Debug ===");
      console.log("Request Body:", req.body);
      console.log("Request Params:", req.params);
      console.log("Content-Type:", req.headers['content-type']);

      const { employeeId, companyId, basicSalary, hourlyPaid } = req.body;
      const { companyCode } = req.params;

      // Determine if this is a JSON request (AJAX) or form submission
      const isJsonRequest = req.headers['content-type']?.includes('application/json');

      // Enhanced validation for required fields
      if (!employeeId) {
        throw new Error("Missing required field: employeeId");
      }

      // Validate basic salary - ensure we have a valid number
      const parsedSalary = parseFloat(basicSalary);
      if (isNaN(parsedSalary) || parsedSalary < 0) {
        throw new Error(
          "Invalid basic salary value. Must be a positive number."
        );
      }

      console.log("Processing salary update:", {
        employeeId,
        companyCode,
        basicSalary: parsedSalary,
        hourlyPaid: hourlyPaid === "on" || hourlyPaid === true,
        isJsonRequest,
      });

      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Get fully populated employee object with company validation
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      })
        .populate("payFrequency")
        .populate("company");

      if (!employee) {
        throw new Error("Employee not found in the specified company");
      }

      // Get company ID from validated company
      const validCompanyId = company._id.toString();

      console.log("Validated Company ID:", validCompanyId);

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(`Cannot edit salary for ${employee.status} employee`);
      }

      if (!employee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Get current period
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        isFinalized: false,
      }).sort({ endDate: 1 }); // Get the earliest non-finalized period

      if (!currentPeriod) {
        try {
          // Generate a new period using PayrollService
          currentPeriod = await PayrollService.generateNextPeriod(
            employee,
            null, // No previous period
            null // No session needed for read operation
          );
        } catch (error) {
          console.error("Error generating period:", error);
          req.flash("error", `Failed to generate pay period: ${error.message}`);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }

      console.log("Current Period:", {
        id: currentPeriod._id,
        startDate: moment(currentPeriod.startDate).format("YYYY-MM-DD"),
        endDate: moment(currentPeriod.endDate).format("YYYY-MM-DD"),
        basicSalary: currentPeriod.basicSalary,
      });

      // Find or create payroll for the current period
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: validCompanyId,
        month: currentPeriod.endDate,
      });

      console.log(
        "Existing Payroll:",
        payroll
          ? {
              id: payroll._id,
              month: moment(payroll.month).format("YYYY-MM-DD"),
              basicSalary: payroll.basicSalary,
              hourlyPaid: payroll.hourlyPaid,
            }
          : "Not found"
      );

      const isHourlyPaid = hourlyPaid === "on" || hourlyPaid === true;
      const oldSalary = payroll ? payroll.basicSalary : 0;
      const oldHourlyPaid = payroll ? payroll.hourlyPaid : false;

      if (!payroll) {
        console.log("Creating new payroll record with salary:", parsedSalary);
        payroll = new Payroll({
          employee: employeeId,
          company: validCompanyId,
          month: moment(currentPeriod.endDate).toDate(),
          basicSalary: parsedSalary,
          hourlyPaid: isHourlyPaid,
          status: "draft",
        });
      } else {
        console.log(
          "Updating existing payroll record with salary:",
          parsedSalary
        );
        payroll.basicSalary = parsedSalary;
        payroll.hourlyPaid = isHourlyPaid;
      }

      // Update the current period's basic salary as well
      currentPeriod.basicSalary = parsedSalary;
      console.log("Setting current period basic salary to:", parsedSalary);

      // Save with session for transaction support
      await currentPeriod.save({ session });
      console.log("Saved Period:", {
        id: currentPeriod._id,
        basicSalary: currentPeriod.basicSalary,
      });

      // Save the payroll with session
      await payroll.save({ session });

      console.log("Updated Payroll:", {
        id: payroll._id,
        basicSalary: payroll.basicSalary,
        hourlyPaid: payroll.hourlyPaid,
      });

      // Create audit log entry with session
      const auditLog = new AuditLog({
        action: "UPDATE_SALARY",
        entityType: "PAYROLL",
        entityId: payroll._id,
        changes: {
          basicSalary: {
            from: oldSalary,
            to: parsedSalary,
          },
          hourlyPaid: {
            from: oldHourlyPaid,
            to: isHourlyPaid,
          },
        },
        user: req.user._id,
        company: validCompanyId,
        employee: employeeId,
      });
      await auditLog.save({ session });

      // Commit the transaction to ensure all data is persisted
      await session.commitTransaction();

      // Log the successful update
      console.log("Basic salary updated successfully:", {
        employeeId: employee._id,
        companyId: validCompanyId,
        payrollId: payroll._id,
        period: {
          startDate: moment(currentPeriod.startDate).format("YYYY-MM-DD"),
          endDate: moment(currentPeriod.endDate).format("YYYY-MM-DD"),
        },
        basicSalary: parsedSalary,
        hourlyPaid: isHourlyPaid,
        transactionCommitted: true,
      });

      // Handle response based on request type
      if (isJsonRequest) {
        // For AJAX requests, return JSON response
        res.status(200).json({
          success: true,
          message: "Basic salary updated successfully",
          data: {
            payrollId: payroll._id,
            basicSalary: parsedSalary,
            hourlyPaid: isHourlyPaid,
            periodId: currentPeriod._id,
          },
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      } else {
        // For traditional form submissions, use redirect
        req.flash("success", "Basic salary updated successfully");
        res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
      }
    } catch (error) {
      // Abort transaction on error
      await session.abortTransaction();

      console.error("Error saving basic salary:", error);

      // Handle error response based on request type
      if (req.headers['content-type']?.includes('application/json')) {
        // For AJAX requests, return JSON error response
        res.status(400).json({
          success: false,
          message: error.message || "An error occurred while saving the basic salary",
          error: error.message,
        });
      } else {
        // For traditional form submissions, use redirect with flash message
        req.flash(
          "error",
          error.message || "An error occurred while saving the basic salary"
        );
        res.redirect(
          `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
        );
      }
    } finally {
      // Always end the session
      session.endSession();
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation using BusinessDate for complete timezone independence
      const periodEndDateStr = BusinessDate.normalize(currentPeriodEndDate);

      console.log("🔍 Finalize route - searching for period (BusinessDate):", {
        originalEndDate: currentPeriodEndDate,
        normalizedEndDate: periodEndDateStr,
        employeeId
      });

      // Try BusinessDate field first (preferred)
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDateBusiness: periodEndDateStr,
        isFinalized: false,
      });

      // If BusinessDate field not found, try legacy Date field for backward compatibility
      if (!currentPeriod) {
        console.log("🔍 Finalize route - BusinessDate field not found, trying legacy Date field...");

        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: BusinessDate.toDate(periodEndDateStr, true),
          isFinalized: false,
        });

        if (currentPeriod) {
          console.log("✅ Finalize route - found period using legacy Date field:", {
            periodId: currentPeriod._id,
            storedEndDate: currentPeriod.endDate.toISOString(),
            searchedEndDate: periodEndDateStr,
            hasBusinessDate: !!currentPeriod.endDateBusiness
          });
        }
      } else {
        console.log("✅ Finalize route - found period using BusinessDate field:", {
          periodId: currentPeriod._id,
          endDateBusiness: currentPeriod.endDateBusiness,
          searchedEndDate: periodEndDateStr
        });
      }

      if (!currentPeriod) {
        console.log("❌ Finalize route - no period found for:", {
          employeeId,
          periodEndDate: periodEndDateStr
        });
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date using BusinessDate
      if (populatedEmployee.lastDayOfService) {
        const terminationDateStr = BusinessDate.normalize(populatedEmployee.lastDayOfService);

        if (BusinessDate.isAfter(periodEndDateStr, terminationDateStr)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Validate sequential finalization order using normalized date
      const chronologicalValidation = await ChronologicalValidator.validateFinalizationOrder(
        employeeId,
        populatedEmployee.company._id,
        periodEndDateStr
      );

      if (!chronologicalValidation.isValid) {
        const errorMessage = chronologicalValidation.errors.join("; ");
        throw new Error(errorMessage);
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save();

      // Generate next period with comprehensive termination checks using BusinessDate
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          BusinessDate.isBefore(
            periodEndDateStr,
            BusinessDate.normalize(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod
          );
          console.log("Next period generated successfully (BusinessDate):", {
            periodId: nextPeriod._id,
            startDateBusiness: nextPeriod.businessStartDate,
            endDateBusiness: nextPeriod.businessEndDate,
            legacyStartDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            legacyEndDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// NEW ROUTE: Unfinalize payroll period
router.post(
  "/:companyCode/employeeProfile/:employeeId/unfinalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      console.log("=== Unfinalize Period Request ===");
      console.log("Employee ID:", employeeId);
      console.log("Period End Date:", currentPeriodEndDate);
      console.log("Company Code:", companyCode);
      console.log("User:", req.user._id);

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Find employee
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee belongs to company
      if (employee.company.toString() !== company._id.toString()) {
        throw new Error("Employee does not belong to this company");
      }

      // Use UnfinalizationService for validation and processing
      const UnfinalizationService = require("../services/UnfinalizationService");

      // Validate unfinalization
      const validation = await UnfinalizationService.validateUnfinalization(
        employeeId,
        company._id,
        currentPeriodEndDate
      );

      if (!validation.isValid) {
        console.log("Unfinalization validation failed:", validation.errors);
        req.flash("error", validation.errors.join("; "));
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // Perform unfinalization
      const result = await UnfinalizationService.unfinalizePeriod(
        employeeId,
        company._id,
        currentPeriodEndDate,
        req.user._id
      );

      if (!result.success) {
        console.log("Unfinalization failed:", result.message);
        req.flash("error", result.message);
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      console.log("Period unfinalized successfully:", result.period._id);

      // Show warnings if any
      if (result.warnings && result.warnings.length > 0) {
        result.warnings.forEach(warning => {
          req.flash("warning", warning);
        });
      }

      req.flash("success", "Period unfinalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error unfinalizing period:", error);
      req.flash("error", error.message || "Failed to unfinalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// NEW ROUTE: Check if period can be unfinalized (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/can-unfinalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, periodEndDate, companyCode } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Use UnfinalizationService for validation
      const UnfinalizationService = require("../services/UnfinalizationService");

      const validation = await UnfinalizationService.validateUnfinalization(
        employeeId,
        company._id,
        periodEndDate
      );

      res.json({
        success: true,
        canUnfinalize: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
      });
    } catch (error) {
      console.error("Error checking unfinalization eligibility:", error);
      res.status(500).json({
        success: false,
        message: "Failed to check unfinalization eligibility",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Get affected periods for unfinalization (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/affected-periods",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, periodEndDate, companyCode } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get affected periods
      const UnfinalizationService = require("../services/UnfinalizationService");
      const ChronologicalValidator = require("../utils/ChronologicalValidator");

      const affectedPeriods = await UnfinalizationService.getAffectedPeriods(
        employeeId,
        company._id,
        periodEndDate
      );

      const impact = await ChronologicalValidator.analyzeUnfinalizationImpact(
        employeeId,
        company._id,
        periodEndDate
      );

      res.json({
        success: true,
        affectedPeriods: affectedPeriods,
        impact: impact,
      });
    } catch (error) {
      console.error("Error getting affected periods:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get affected periods",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Check if period can be finalized (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/debug-periods",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Get all periods for this employee
      const allPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id
      }).sort({ endDate: 1 }).lean();

      const debugInfo = {
        employeeId,
        companyId: company._id,
        companyCode,
        totalPeriods: allPeriods.length,
        periods: allPeriods.map(p => ({
          id: p._id,
          startDate: p.startDate.toISOString(),
          endDate: p.endDate.toISOString(),
          startDateFormatted: new Date(p.startDate).toISOString().split('T')[0],
          endDateFormatted: new Date(p.endDate).toISOString().split('T')[0],
          isFinalized: p.isFinalized,
          frequency: p.frequency,
          basicSalary: p.basicSalary
        }))
      };

      res.json(debugInfo);
    } catch (error) {
      console.error("Error in debug-periods route:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/can-finalize",
  ensureAuthenticated,
  async (req, res) => {
    // Prevent caching to avoid 304 responses
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    try {
      const { employeeId, periodEndDate, companyCode } = req.params;

      console.log("🔍 Can-finalize route called:", {
        employeeId,
        periodEndDate,
        companyCode,
        timestamp: new Date().toISOString()
      });

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find employee
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        return res.json({
          success: true,
          canFinalize: false,
          errors: [`Cannot finalize period for ${employee.status} employee`],
          warnings: [],
        });
      }

      // Check if period exists and is not already finalized
      // Use BusinessDate for complete timezone independence
      const periodEndDateStr = BusinessDate.normalize(periodEndDate);

      // The PayrollPeriod model automatically converts YYYY-MM-DD strings to T23:59:59.999Z
      // So we need to query using the exact same format that would be stored
      const queryEndDate = new Date(periodEndDateStr + 'T23:59:59.999Z');

      console.log("🔍 Period query details:", {
        originalPeriodEndDate: periodEndDate,
        normalizedPeriodEndDate: periodEndDateStr,
        queryEndDate: queryEndDate.toISOString(),
        employeeId,
        company: company._id
      });

      // First, let's see what PayrollPeriod records actually exist for this employee
      const allEmployeePeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id
      }).sort({ endDate: 1 }).lean();

      console.log("🔍 All PayrollPeriod records for employee:", {
        totalPeriods: allEmployeePeriods.length,
        periods: allEmployeePeriods.map(p => ({
          id: p._id,
          startDate: p.startDate.toISOString(),
          endDate: p.endDate.toISOString(),
          isFinalized: p.isFinalized,
          frequency: p.frequency
        }))
      });

      // Try to find the specific period using BusinessDate field first (preferred)
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        endDateBusiness: periodEndDateStr,
        isFinalized: false,
      });

      // If BusinessDate field not found, try legacy Date field for backward compatibility
      if (!currentPeriod) {
        console.log("🔍 BusinessDate field not found, trying legacy Date field...");

        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: BusinessDate.toDate(periodEndDateStr, true),
          isFinalized: false,
        });

        if (currentPeriod) {
          console.log("✅ Found period using legacy Date field:", {
            periodId: currentPeriod._id,
            storedEndDate: currentPeriod.endDate.toISOString(),
            searchedEndDate: periodEndDateStr,
            hasBusinessDate: !!currentPeriod.endDateBusiness
          });
        }
      } else {
        console.log("✅ Found period using BusinessDate field:", {
          periodId: currentPeriod._id,
          endDateBusiness: currentPeriod.endDateBusiness,
          searchedEndDate: periodEndDateStr
        });
      }

      console.log("🔍 Exact period query result:", {
        found: !!currentPeriod,
        periodId: currentPeriod?._id,
        periodEndDate: currentPeriod?.endDate?.toISOString(),
        isFinalized: currentPeriod?.isFinalized
      });

      // If exact match fails, try a range query to see if there's a close match
      if (!currentPeriod) {
        const dayBefore = new Date(queryEndDate.getTime() - 24 * 60 * 60 * 1000);
        const dayAfter = new Date(queryEndDate.getTime() + 24 * 60 * 60 * 1000);

        const nearbyPeriods = await PayrollPeriod.find({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: dayBefore,
            $lte: dayAfter
          },
          isFinalized: false,
        }).lean();

        console.log("🔍 Nearby periods (±1 day):", {
          searchRange: {
            from: dayBefore.toISOString(),
            to: dayAfter.toISOString()
          },
          foundPeriods: nearbyPeriods.map(p => ({
            id: p._id,
            endDate: p.endDate.toISOString(),
            isFinalized: p.isFinalized
          }))
        });
      }

      if (!currentPeriod) {
        // Try to find any period with this end date, regardless of finalization status
        // Use date range to handle timezone discrepancies
        const startOfDay = new Date(periodEndDateStr + 'T00:00:00.000Z');
        const endOfDay = new Date(periodEndDateStr + 'T23:59:59.999Z');

        const anyPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        });

        if (anyPeriod) {
          console.log("🔍 Found period but it's finalized:", {
            periodId: anyPeriod._id,
            isFinalized: anyPeriod.isFinalized,
            endDate: anyPeriod.endDate.toISOString(),
            searchedEndDate: queryEndDate.toISOString()
          });

          return res.json({
            success: true,
            canFinalize: false,
            errors: [`Period ending ${periodEndDateStr} is already finalized`],
            warnings: [],
          });
        }

        console.log("❌ No period found with end date:", queryEndDate.toISOString());
        return res.json({
          success: true,
          canFinalize: false,
          errors: ["Period not found or already finalized"],
          warnings: [],
          debug: {
            searchedEndDate: queryEndDate.toISOString(),
            employeeId,
            companyId: company._id,
            totalPeriodsForEmployee: allEmployeePeriods.length
          }
        });
      }

      // Use ChronologicalValidator for sequential validation with timezone-independent date
      const validation = await ChronologicalValidator.validateFinalizationOrder(
        employeeId,
        company._id,
        periodEndDateStr
      );

      console.log("✅ Can-finalize validation result:", {
        canFinalize: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        periodEndDate: periodEndDateStr
      });

      res.json({
        success: true,
        canFinalize: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
      });
    } catch (error) {
      console.error("Error checking finalization eligibility:", error);
      res.status(500).json({
        success: false,
        message: "Error checking finalization eligibility",
      });
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      console.log("Available Pay Frequencies:", {
        count: payFrequencies.length,
        frequencies: payFrequencies.map((pf) => ({
          id: pf._id,
          name: pf.name,
          frequency: pf.frequency,
        })),
      });

      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Create an empty employee object for the template
      const employee = {
        bank: "",
        accountNumber: "",
        accountType: "",
        branchCode: "",
        accountHolder: "",
      };

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        employee, // Pass the empty employee object
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  invalidateEmployeeCache,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("\n=== Calculating First Payroll Period End Date ===");
      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        phone: req.body.phone, // Add phone field
        dob: req.body.dob ? moment.utc(req.body.dob).startOf('day').toDate() : null,
        passportNumber: req.body.idType === 'passport' ? req.body.passportNumber : undefined,
        paymentMethod: req.body.paymentMethod, // Use value directly from form
        companyEmployeeNumber,
        globalEmployeeId: uuidv4(),
        payFrequency: payFrequency._id,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        doa: req.body.doa,
        firstPayrollPeriodEndDate,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        isDirector: req.body.isDirector === true,
        typeOfDirector: req.body.isDirector ? req.body.typeOfDirector : null,
        workingHours: req.body.workingHours === "Part Time" ? "Less Than 22 Hours a Week" : "Full Time",
        
        // Personal Details
        personalDetails: {
          idType: req.body.idType === 'none' ? 'other' : (req.body.idType || 'rsa'),
          idNumber: req.body.idType === 'none' ? undefined : req.body.idNumber,
          mobileNumber: req.body.personalDetails?.mobileNumber || req.body['personalDetails.mobileNumber'] || null,
          nationality: "South African",
          countryOfBirth: "South Africa",
        },

        // Payment Details
        paymentMethod: req.body.paymentMethod?.toLowerCase() || 'eft', // Fix case sensitivity but keep default
        
        // Address Details
        streetAddress: req.body.streetAddress,
        suburb: req.body.suburb,
        city: req.body.city,
        postalCode: req.body.postalCode,
        
        // Regular Hours
        regularHours: req.body.regularHours,
        
        // Company Reference
        company: company._id,
      };

      // Bank Details - only add if provided and not empty
      if (req.body.bankName?.trim()) {
        employeeData.bank = req.body.bankName.trim();
      }
      if (req.body.accountType?.trim()) {
        employeeData.accountType = req.body.accountType.trim();
      }
      if (req.body.accountNumber?.trim()) {
        employeeData.accountNumber = req.body.accountNumber.trim();
      }
      if (req.body.branchCode?.trim()) {
        employeeData.branchCode = req.body.branchCode.trim();
      }
      if (req.body.accountHolder?.trim()) {
        employeeData.accountHolder = req.body.accountHolder.trim();
      }
      if (req.body.holderRelationship?.trim()) {
        employeeData.holderRelationship = req.body.holderRelationship.trim();
      }

      // Create and save the new employee
      const newEmployee = new Employee(employeeData);
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  invalidateEmployeeCache,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates - handle empty strings as null
      console.log('DOB processing - original value:', employeeData.dob, 'type:', typeof employeeData.dob);
      if (employeeData.dob && employeeData.dob.trim() !== '') {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
        console.log('DOB processed as date:', employeeData.dob);
      } else {
        employeeData.dob = null;
        console.log('DOB set to null');
      }

      console.log('DOA processing - original value:', employeeData.doa, 'type:', typeof employeeData.doa);
      if (employeeData.doa && employeeData.doa.trim() !== '') {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
        console.log('DOA processed as date:', employeeData.doa);
      } else {
        employeeData.doa = null;
        console.log('DOA set to null');
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        endOfMonthDate: employeeData.endOfMonthDate, // Include endOfMonthDate if it was calculated
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Debug: Log the initial updateData creation
      console.log('=== Initial Update Data Creation ===');
      console.log('employeeData.dob:', employeeData.dob, 'type:', typeof employeeData.dob);
      console.log('employeeData.doa:', employeeData.doa, 'type:', typeof employeeData.doa);
      console.log('updateData.dob:', updateData.dob, 'type:', typeof updateData.dob);
      console.log('updateData.doa:', updateData.doa, 'type:', typeof updateData.doa);
      
      // Debug: Log the entire request body
      console.log('=== Request Body ===');
      console.log('Raw request body:', JSON.stringify(employeeData, null, 2));
      
      // Initialize personalDetails as an object to ensure proper structure
      updateData.personalDetails = updateData.personalDetails || {};
      
      // Directly set mobile number if present in personalDetails
      if (employeeData.personalDetails && employeeData.personalDetails.mobileNumber !== undefined) {
        updateData.personalDetails.mobileNumber = employeeData.personalDetails.mobileNumber || undefined;
      }
      
      // Handle idType and related fields
      if (employeeData.idType) {
        updateData.personalDetails.idType = employeeData.idType;
        // Also update root level for backward compatibility
        updateData.idType = employeeData.idType;

        // Only include ID number or passport number if idType is not 'none'
        if (employeeData.idType !== 'none') {
          if (employeeData.idType === 'rsa' && employeeData.idNumber) {
            updateData.personalDetails.idNumber = employeeData.idNumber;
            updateData.idNumber = employeeData.idNumber; // Backward compatibility
          } else if (employeeData.idType === 'passport' && employeeData.passportNumber) {
            updateData.personalDetails.passportNumber = employeeData.passportNumber;
            updateData.passportNumber = employeeData.passportNumber; // Backward compatibility
          }
        } else {
          // Clear ID-related fields when idType is 'none'
          updateData.personalDetails.idNumber = undefined;
          updateData.personalDetails.passportNumber = undefined;
          updateData.idNumber = undefined;
          updateData.passportNumber = undefined;
        }
      }
      
      // Handle mobile number in different possible formats
      if (employeeData['personalDetails.mobileNumber'] !== undefined) {
        console.log('Found mobile number in personalDetails.mobileNumber format:', employeeData['personalDetails.mobileNumber']);
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData['personalDetails.mobileNumber'] || undefined;
      } else if (employeeData.personalDetails && employeeData.personalDetails.mobileNumber) {
        console.log('Found mobile number in nested personalDetails object:', employeeData.personalDetails.mobileNumber);
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData.personalDetails.mobileNumber || undefined;
      } else if (employeeData.mobileNumber) {
        console.log('Found mobile number in root level:', employeeData.mobileNumber);
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData.mobileNumber || undefined;
      } else {
        console.log('No mobile number found in request');
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = undefined;
      }
      
      // Debug: Log the update data before saving
      console.log('=== Update Data ===');
      console.log(JSON.stringify(updateData, null, 2));

      // Clean up the update data
      const cleanObject = (obj) => {
        Object.keys(obj).forEach(key => {
          console.log(`Cleaning field ${key}:`, obj[key], 'type:', typeof obj[key]);
          if (obj[key] === undefined || obj[key] === '') {
            console.log(`Removing field ${key} because it's undefined or empty string`);
            delete obj[key];
          } else if (typeof obj[key] === 'object' && obj[key] !== null && !(obj[key] instanceof Date)) {
            cleanObject(obj[key]);
            // Remove empty objects
            if (Object.keys(obj[key]).length === 0) {
              console.log(`Removing field ${key} because it's an empty object`);
              delete obj[key];
            }
          }
        });
        return obj;
      };

      // Debug: Log update data before cleaning
      console.log('=== Update Data Before Cleaning ===');
      console.log('DOB before cleaning:', updateData.dob, 'type:', typeof updateData.dob);
      console.log('DOA before cleaning:', updateData.doa, 'type:', typeof updateData.doa);

      // Clean the update data and personalDetails
      cleanObject(updateData);
      if (updateData.personalDetails) {
        cleanObject(updateData.personalDetails);
      }

      // Debug: Log update data after cleaning
      console.log('=== Update Data After Cleaning ===');
      console.log('DOB after cleaning:', updateData.dob, 'type:', typeof updateData.dob);
      console.log('DOA after cleaning:', updateData.doa, 'type:', typeof updateData.doa);
      console.log('Final update data keys:', Object.keys(updateData));

      // Debug: Log the update operation details
      console.log('=== MongoDB Update Operation ===');
      console.log('Filter:', { _id: employeeId, company: company._id });
      console.log('Update:', { $set: updateData });
      
      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { 
          new: true, 
          runValidators: true,
          // Add this to include all fields in the returned document
          projection: { __v: 0 } // Exclude version key
        }
      );

      if (!updatedEmployee) {
        console.error('Employee not found:', { employeeId, company: company._id });
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Debug: Log the updated employee document
      console.log('=== Updated Employee Document ===');
      console.log(JSON.stringify({
        _id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        personalDetails: updatedEmployee.personalDetails,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
        updatedAt: updatedEmployee.updatedAt
      }, null, 2));
      
      // Verify the mobile number was saved correctly
      const freshEmployee = await Employee.findById(employeeId).lean();
      console.log('=== Fresh Employee Document (After Save) ===');
      console.log('Mobile number in DB:', freshEmployee?.personalDetails?.mobileNumber);

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  routeCache.cache({ ttl: 240 }), // Cache for 4 minutes
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber (optimized with lean())
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 })
        .lean(); // Use lean() for better performance

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      
      if (!company) {
        return res.status(404).send("Company not found");
      }

      res.render("bulk-add-employees", {
        company,
        companyCode,
        user: req.user,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering bulk add employees page:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/preview-upload",
  ensureAuthenticated,
  csrfProtection,
  (req, res) => {
    console.log('\n=== DETAILED DEBUG: Preview Upload Request ===');
    console.log('Request URL:', req.originalUrl);
    console.log('Request Method:', req.method);
    console.log('Content Type:', req.get('content-type'));
    console.log('Content Length:', req.get('content-length'));
    console.log('X-CSRF-Token:', req.get('x-csrf-token'));
    
    // Log all request headers for debugging
    console.log('All Request Headers:');
    Object.keys(req.headers).forEach(key => {
      console.log(`${key}: ${req.headers[key]}`);
    });
    
    // Create a temporary buffer to collect the request body for diagnosis
    let rawBodyChunks = [];
    
    // Handle body data events to inspect the raw request
    req.on('data', chunk => {
      console.log(`Received chunk (${chunk.length} bytes)`);
      if (rawBodyChunks.length < 5) { // Only store first few chunks to avoid memory issues
        rawBodyChunks.push(chunk);
      }
    });
    
    // Handle request end event
    req.on('end', () => {
      console.log('Request body transmission complete');
      
      // Log information about the collected chunks
      if (rawBodyChunks.length > 0) {
        const totalLength = rawBodyChunks.reduce((acc, chunk) => acc + chunk.length, 0);
        console.log(`Received ${rawBodyChunks.length} chunks, total ${totalLength} bytes`);
        
        // Peek at the first chunk to see the form boundary
        const firstChunk = Buffer.concat(rawBodyChunks.slice(0, 1));
        console.log('First chunk preview:', firstChunk.toString().substring(0, 200));
      }
      
      // Now process with multer
      console.log('\n=== Starting Multer Processing ===');
      
      // Configure multer with detailed logging
      const customMulter = multer({
        storage: multer.memoryStorage(),
        limits: {
          fileSize: 10 * 1024 * 1024, // 10MB
          files: 1,
          fields: 10
        },
        fileFilter: (req, file, cb) => {
          console.log('\n=== DEBUG: File Filter Called ===');
          console.log('Received file:', {
            fieldname: file.fieldname,
            originalname: file.originalname,
            encoding: file.encoding,
            mimetype: file.mimetype
          });
          
          // Accept all files for diagnostic purposes
          console.log('Accepting file for diagnostic purposes');
          return cb(null, true);
        }
      });
      
      // Use the custom multer instance
      customMulter.single('file')(req, res, function(err) {
        console.log('\n=== DEBUG: Multer Callback Executed ===');
        
        if (err) {
          console.error('\n=== DEBUG: Multer Error ===');
          console.error('Error type:', err.constructor.name);
          console.error('Error message:', err.message);
          console.error('Error stack:', err.stack);
          
          // If the error comes from Busboy, analyze deeper
          if (err.message.includes('Unexpected end of form')) {
            console.error('\n=== CRITICAL DEBUG: Busboy Error ===');
            console.error('This suggests the multipart form data is malformed or incomplete');
            console.error('Content-Type header:', req.get('content-type'));
            console.error('Boundary value:', req.get('content-type').split('boundary=')[1]);
          }
          
          return res.status(400).json({
            success: false,
            message: err.message,
            diagnostic: 'The file upload failed due to malformed request data. Please try again with a different file or browser.'
          });
        }
        
        if (!req.file) {
          console.error('\n=== DEBUG: No File in Request ===');
          return res.status(400).json({
            success: false,
            message: 'No file uploaded or file upload was interrupted',
            diagnostic: 'The server did not receive a file in the request. Please select a file and try again.'
          });
        }
        
        console.log('\n=== DEBUG: File Upload Success ===');
        console.log('File details:', {
          fieldname: req.file.fieldname,
          originalname: req.file.originalname,
          encoding: req.file.encoding,
          mimetype: req.file.mimetype,
          size: req.file.size,
          buffer: req.file.buffer ? `Present (${req.file.buffer.length} bytes)` : 'Missing'
        });
        
        try {
          // Basic file validation
          if (!req.file.buffer || req.file.buffer.length === 0) {
            throw new Error('File buffer is empty or corrupted');
          }
          
          // Process the file as plain text first to inspect its content
          const filePreview = req.file.buffer.toString().substring(0, 100);
          console.log('\n=== DEBUG: File Content Preview ===');
          console.log(filePreview);
          
          if (filePreview.includes('PK')) {
            console.log('File appears to be a ZIP-based format (likely XLSX)');
          } else if (filePreview.includes('<?xml')) {
            console.log('File appears to be XML-based format');
          } else {
            console.log('File format is not immediately recognizable');
          }
          
          // Try to process with xlsx
          console.log('\n=== DEBUG: Processing with XLSX Library ===');
          try {
            const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
            console.log('XLSX processing successful');
            console.log('Workbook structure:', {
              sheetNames: workbook.SheetNames,
              numberOfSheets: workbook.SheetNames.length
            });
            
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            console.log('First sheet ref:', firstSheet['!ref']);
            
            // Convert to JSON
            const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });
            console.log('Number of rows:', rows.length);
            
            if (rows.length < 2) {
              throw new Error('Excel file must contain headers and at least one data row');
            }
            
            const headers = rows[0].map(h => h?.toString().trim() || '');
            console.log('Headers:', headers);
            
            // Simple data processing
            const dataRows = rows.slice(1, 6).map(row => {
              const obj = {};
              headers.forEach((header, i) => {
                obj[header] = row[i] || '';
              });
              return obj;
            });
            
            // Return success
            return res.status(200).json({
              success: true,
              message: 'File processed successfully',
              file: {
                name: req.file.originalname,
                size: req.file.size
              },
              preview: dataRows
            });
            
          } catch (xlsxError) {
            console.error('XLSX processing error:', xlsxError);
            throw new Error(`XLSX processing failed: ${xlsxError.message}`);
          }
          
        } catch (error) {
          console.error('\n=== DEBUG: Processing Error ===');
          console.error('Error:', error);
          return res.status(400).json({
            success: false,
            message: error.message,
            diagnostic: 'The server received the file but could not process it. Please check the file format.'
          });
        }
      });
    });
    
    // Handle request errors
    req.on('error', err => {
      console.error('\n=== DEBUG: Request Stream Error ===');
      console.error('Error:', err);
      res.status(400).json({
        success: false,
        message: 'Error in request stream: ' + err.message,
        diagnostic: 'The file upload was interrupted. Please try again.'
      });
    });
  }
);

router.post(
  "/:companyCode/employeeManagement/confirm-upload",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const records = req.body;

      const successList = [];
      const errorList = [];

      for (const record of records) {
        try {
          // Create employee record
          const employee = new Employee({
            ...record,
            company: company._id,
            globalEmployeeId: uuidv4()
          });

          await employee.save();

          successList.push({
            row: record.row,
            name: `${record.firstName} ${record.lastName}`,
            employeeNumber: employee.companyEmployeeNumber
          });
        } catch (error) {
          errorList.push({
            row: record.row,
            error: error.message
          });
        }
      }

      res.json({
        successful: successList.length,
        failed: errorList.length,
        successList,
        errorList
      });
    } catch (error) {
      console.error("Error confirming upload:", error);
      res.status(500).json({ error: "Failed to process upload" });
    }
  }
);

router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});
router.get(
  "/:companyCode/employeeManagement/download-template",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Check user permissions
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        req.flash(
          "error",
          "You don't have permission to access this company's data"
        );
        return res.redirect("/clients");
      }

      const generator = new EmployeeTemplateGenerator();
      const workbook = await generator.generateTemplate();

      // Set security headers
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader(
        "Cache-Control",
        "no-store, no-cache, must-revalidate, private"
      );

      // Set download headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=employee-import-template-${companyCode}-${Date.now()}.xlsx`
      );

      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error("Error generating template:", error);
      req.flash("error", "Failed to generate template");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Handle file upload
router.post(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  uploadMiddleware.single('file'),
  async (req, res) => {
    try {
      console.log("\n=== Bulk Employee Upload Request ===");
      console.log("Request headers:", req.headers);
      
      const { companyCode } = req.params;
      console.log("Company Code:", companyCode);

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      // Check if file was uploaded
      if (!req.file) {
        console.error("No file uploaded");
        return res.status(400).json({
          success: false,
          message: "No file uploaded"
        });
      }

      console.log('File upload details:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });

      try {
        // Read Excel file from buffer
        console.log('=== Excel Validation Start ===');
        const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
        
        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Invalid Excel file structure');
        }

        console.log('Workbook structure:', {
          sheetNames: workbook.SheetNames,
          numberOfSheets: workbook.SheetNames.length
        });

        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        if (!firstSheet || !firstSheet['!ref']) {
          throw new Error('Excel file is empty or corrupted');
        }

        // Convert to JSON
        const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });
        
        if (rows.length < 2) {
          throw new Error('Excel file must contain headers and at least one data row');
        }

        // Validate headers
        const headers = rows[0].map(h => h?.toString().trim() || '');
        const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

        if (missingHeaders.length > 0) {
          throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
        }

        console.log('Excel validation successful');
        console.log('Found headers:', headers);
        console.log('Number of data rows:', rows.length - 1);

        res.json({
          success: true,
          message: 'File uploaded and validated successfully',
          file: {
            name: req.file.originalname,
            size: req.file.size,
            rows: rows.length - 1 // Exclude header row
          }
        });

      } catch (error) {
        console.error('Excel validation error:', error);
        return res.status(400).json({
          success: false,
          message: error.message || 'Invalid Excel file format'
        });
      }

    } catch (error) {
      console.error("Error in bulk employee upload:", error);
      res.status(error.status || 400).json({
        success: false,
        message: error.message || 'Error uploading file'
      });
    }
  }
);

// Mount upload router with proper prefix
router.use('/:companyCode/employeeManagement', (req, res, next) => {
  console.log('\n=== Upload Router Mount Debug ===');
  console.log('Original URL:', req.originalUrl);
  console.log('Base URL:', req.baseUrl);
  console.log('Path:', req.path);
  next();
}, uploadRouter);

// Add an alternative upload route that bypasses busboy/multer issues
router.post(
  "/:companyCode/employeeManagement/alt-preview-upload",
  ensureAuthenticated,
  csrfProtection,
  express.raw({ 
    type: 'multipart/form-data',
    limit: '15mb'  // Slightly larger than our 10MB limit to ensure we get the full file
  }),
  async (req, res) => {
    console.log('\n=== ALTERNATIVE Upload Route ===');
    console.log('Headers:', {
      'content-type': req.get('content-type'),
      'content-length': req.get('content-length')
    });
    
    try {
      // Manually parse multipart form data
      console.log('Manually parsing form data...');
      
      // Check if we have the raw body
      if (!req.body || !Buffer.isBuffer(req.body) || req.body.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No request body received',
          diagnostic: 'The server did not receive any data. Please try again.'
        });
      }
      
      console.log(`Raw body received (${req.body.length} bytes)`);
      
      // Extract boundary from content-type header
      const contentType = req.get('content-type');
      const boundaryMatch = contentType.match(/boundary=(?:"([^"]+)"|([^;]+))/i);
      
      if (!boundaryMatch) {
        return res.status(400).json({
          success: false,
          message: 'Invalid content-type header (missing boundary)',
          diagnostic: 'The upload request was missing required information. Please try again.'
        });
      }
      
      const boundary = boundaryMatch[1] || boundaryMatch[2];
      console.log('Form boundary:', boundary);
      
      // Convert to string for simple processing (not ideal for large files but works for this case)
      const bodyStr = req.body.toString('utf-8');
      
      // Find the file in the multipart data
      const filePartMatch = bodyStr.match(new RegExp(`--${boundary}[\\r\\n]+content-disposition:\\s*form-data;\\s*name="file";\\s*filename="([^"]+)"[\\r\\n]+content-type:\\s*([^\\r\\n]+)[\\r\\n]+[\\r\\n]+(.*?)--${boundary}`, 'is'));
      
      if (!filePartMatch) {
        return res.status(400).json({
          success: false, 
          message: 'No file found in request',
          diagnostic: 'The server could not find the file in the uploaded data. Please try a different file.'
        });
      }
      
      // Extract file info
      const filename = filePartMatch[1];
      const fileMimeType = filePartMatch[2];
      
      console.log('File found:', {
        filename: filename,
        mimeType: fileMimeType
      });
      
      // Find the file content indices
      const fileHeaderEnd = bodyStr.indexOf('\r\n\r\n', bodyStr.indexOf(`filename="${filename}"`)) + 4;
      const fileEnd = bodyStr.indexOf(`--${boundary}`, fileHeaderEnd) - 2; // -2 for \r\n
      
      // Extract file content as buffer
      const fileContent = req.body.slice(fileHeaderEnd, fileEnd);
      console.log(`Extracted file content (${fileContent.length} bytes)`);
      
      // Process Excel file
      console.log('Processing Excel file...');
      try {
        const workbook = xlsx.read(fileContent, { type: 'buffer' });
        
        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Invalid Excel file structure');
        }
        
        console.log('Workbook processed successfully');
        console.log('Sheets:', workbook.SheetNames);
        
        // Get first worksheet
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        
        if (!firstSheet || !firstSheet['!ref']) {
          throw new Error('Empty worksheet');
        }
        
        // Convert to JSON
        const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });
        
        if (rows.length < 2) {
          throw new Error('Excel file must contain headers and at least one data row');
        }
        
        // Get headers from first row
        const headers = rows[0].map(h => h?.toString().trim() || '');
        console.log('Headers found:', headers);
        
        // Convert data rows to objects with header keys
        const dataRows = [];
        for (let i = 1; i < Math.min(rows.length, 6); i++) {
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = rows[i][index]?.toString() || '';
          });
          dataRows.push(rowData);
        }
        
        // Return success
        return res.status(200).json({
          success: true,
          message: 'File processed successfully',
          file: {
            name: filename,
            size: fileContent.length,
            rows: rows.length - 1
          },
          preview: dataRows
        });
        
      } catch (excelError) {
        console.error('Excel processing error:', excelError);
        return res.status(400).json({
          success: false,
          message: excelError.message,
          diagnostic: 'The file appears to be invalid or corrupted. Please check the file format.'
        });
      }
      
    } catch (error) {
      console.error('Error in alternative upload handler:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error processing upload',
        diagnostic: error.message
      });
    }
  }
);

// Alternative upload route using multer
router.post('/:companyCode/employeeManagement/alt-preview-upload', ensureAuthenticated, csrf({ cookie: true }), async (req, res) => {
    console.log('=== Alternative Upload Route Debug ===');
    console.log('Request headers:', req.headers);
    
    // Create a multer instance with memory storage
    const upload = multer({
        storage: multer.memoryStorage(),
        limits: {
            fileSize: 15 * 1024 * 1024, // 15MB limit
            files: 1
        },
        fileFilter: (req, file, cb) => {
            console.log('File filter called for:', file.originalname);
            console.log('MIME type:', file.mimetype);
            
            // Check file type
            if (!file.mimetype.match(/^(application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|application\/vnd\.ms-excel)$/)) {
                console.log('Invalid file type:', file.mimetype);
                return cb(new Error('Invalid file type. Only Excel files are allowed.'));
            }
            
            // Check file extension
            if (!file.originalname.match(/\.(xlsx|xls)$/)) {
                console.log('Invalid file extension:', file.originalname);
                return cb(new Error('Invalid file extension. Only .xlsx and .xls files are allowed.'));
            }
            
            cb(null, true);
        }
    }).single('file');

    try {
        // Handle the file upload
        upload(req, res, async (err) => {
            if (err instanceof multer.MulterError) {
                console.error('Multer error:', err);
                return res.status(400).json({
                    success: false,
                    message: 'File upload error',
                    error: err.message
                });
            } else if (err) {
                console.error('Upload error:', err);
                return res.status(400).json({
                    success: false,
                    message: 'File validation error',
                    error: err.message
                });
            }

            if (!req.file) {
                console.error('No file uploaded');
                return res.status(400).json({
                    success: false,
                    message: 'No file uploaded',
                    diagnostic: 'Please select a file to upload.'
                });
            }

            console.log('File uploaded successfully:', {
                filename: req.file.originalname,
                mimetype: req.file.mimetype,
                size: req.file.size,
                buffer: req.file.buffer ? 'Present' : 'Missing'
            });

            try {
                // Process the Excel file
                const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

                // Validate the data structure
                if (!data || data.length < 2) {
                    return res.status(400).json({
                        success: false,
                        message: 'Invalid file structure',
                        diagnostic: 'The file must contain at least a header row and one data row.'
                    });
                }

                // Get headers and validate required columns
                const headers = data[0];
                const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
                const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

                if (missingHeaders.length > 0) {
                    return res.status(400).json({
                        success: false,
                        message: 'Missing required columns',
                        diagnostic: `The following required columns are missing: ${missingHeaders.join(', ')}`
                    });
                }

                // Convert to JSON format
                const jsonData = data.slice(1).map((row, index) => {
                    const record = {};
                    headers.forEach((header, colIndex) => {
                        record[header] = row[colIndex] || '';
                    });
                    return record;
                });

                // Return success response with preview
                return res.json({
                    success: true,
                    message: 'File processed successfully',
                    file: {
                        name: req.file.originalname,
                        size: req.file.size,
                        type: req.file.mimetype
                    },
                    preview: jsonData.slice(0, 5) // Return first 5 rows as preview
                });

            } catch (excelError) {
                console.error('Excel processing error:', excelError);
                return res.status(400).json({
                    success: false,
                    message: 'Error processing Excel file',
                    error: excelError.message
                });
            }
        });
    } catch (error) {
        console.error('Route handler error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// Alternative upload route with minimal middleware
// Place this BEFORE any routes that use body-parser or other middleware that might consume the request body
router.post('/:companyCode/employeeManagement/basic-upload', ensureAuthenticated, (req, res) => {
    console.log('=== Basic Upload Route Accessed ===');
    console.log('Request headers:', {
        'content-type': req.headers['content-type'],
        'x-csrf-token': req.headers['x-csrf-token'] ? 'Present' : 'Missing',
        'content-length': req.headers['content-length']
    });
    
    // Manually check CSRF token to avoid middleware issues
    const csrfToken = req.headers['x-csrf-token'];
    const csrfCookie = req.cookies && req.cookies['_csrf'];
    
    console.log('CSRF Check:', {
        tokenProvided: !!csrfToken,
        cookieFound: !!csrfCookie
    });
    
    // Configure multer for this specific route only
    const upload = multer({
        storage: multer.memoryStorage(),
        limits: { fileSize: 15 * 1024 * 1024 }, // 15MB limit
        fileFilter: (req, file, cb) => {
            console.log('File details:', {
                originalname: file.originalname,
                mimetype: file.mimetype,
                fieldname: file.fieldname,
                encoding: file.encoding
            });
            
            // Check MIME type
            const validMimeTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/octet-stream', // Some browsers might send this for Excel files
                'application/zip', // .xlsx files are actually zip files
                '' // Handle empty mimetype
            ];
            
            if (file.mimetype && !validMimeTypes.includes(file.mimetype)) {
                console.log('Invalid MIME type:', file.mimetype);
                return cb(new Error(`Invalid file type: ${file.mimetype}. Only Excel files are allowed.`));
            }
            
            // Check file extension
            if (!file.originalname.match(/\.(xlsx|xls)$/i)) {
                console.log('Invalid file extension:', file.originalname);
                return cb(new Error(`Invalid file extension. Only .xlsx and .xls files are allowed.`));
            }
            
            cb(null, true);
        }
    }).single('file'); // Make sure field name matches client-side code

    // Handle upload using multer with direct error handling
    upload(req, res, function(err) {
        console.log('=== Upload handler executed ===');
        
        // Check for errors
        if (err) {
            console.error('Upload error:', err);
            return res.status(400).json({
                success: false,
                message: err.message || 'File upload failed',
                error: err.toString()
            });
        }
        
        // Check if file exists
        if (!req.file) {
            console.error('No file received');
            return res.status(400).json({
                success: false,
                message: 'No file uploaded',
                diagnostic: 'Please select a valid Excel file to upload.'
            });
        }
        
        console.log('File received successfully:', {
            filename: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype,
            buffer: req.file.buffer ? `Present (${req.file.buffer.length} bytes)` : 'Missing'
        });
        
        try {
            // Process Excel file
            const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
            const sheetName = workbook.SheetNames[0];
            
            if (!sheetName) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid Excel file',
                    diagnostic: 'The uploaded file does not contain any sheets.'
                });
            }
            
            const worksheet = workbook.Sheets[sheetName];
            const rawData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
            
            // Validate data
            if (!rawData || rawData.length < 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid file contents',
                    diagnostic: 'The Excel file must contain at least a header row and one data row.'
                });
            }
            
            // Process headers
            const headers = rawData[0];
            const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
            const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
            
            if (missingHeaders.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Missing required columns',
                    diagnostic: `The following required columns are missing: ${missingHeaders.join(', ')}`
                });
            }
            
            // Convert data to JSON with header mapping
            const jsonData = rawData.slice(1)
                .filter(row => row.some(cell => cell !== undefined && cell !== null && cell !== '')) // Skip empty rows
                .map(row => {
                    const record = {};
                    headers.forEach((header, i) => {
                        record[header] = row[i] !== undefined ? row[i] : '';
                    });
                    return record;
                });
            
            console.log('Processed data successfully:', {
                totalRows: jsonData.length,
                previewRows: Math.min(5, jsonData.length)
            });
            
            // Return success with preview
            return res.status(200).json({
                success: true,
                message: 'File uploaded and processed successfully',
                file: {
                    name: req.file.originalname,
                    size: req.file.size,
                    type: req.file.mimetype
                },
                preview: jsonData.slice(0, 5), // First 5 rows
                totalRows: jsonData.length
            });
        } catch (error) {
            console.error('Excel processing error:', error);
            return res.status(400).json({
                success: false,
                message: 'Error processing Excel file',
                error: error.message,
                diagnostic: 'The file could not be processed. Please ensure it is a valid Excel file with the required structure.'
            });
        }
    });
});

// Confirm upload route to process previewed data
router.post('/:companyCode/employeeManagement/confirm-upload', ensureAuthenticated, express.json(), async (req, res) => {
    console.log('=== Confirm Upload Route Accessed ===');
    const companyCode = req.params.companyCode;
    const employeeData = req.body;
    
    if (!Array.isArray(employeeData)) {
        console.error('Invalid data format received');
        return res.status(400).json({
            success: false,
            message: 'Invalid data format. Expected an array of employee records.'
        });
    }
    
    console.log(`Processing ${employeeData.length} employee records for company ${companyCode}`);
    
    try {
        // In a real implementation, you would save this data to your database
        // For this example, we'll just simulate success
        
        const results = {
            successful: employeeData.length,
            failed: 0
        };
        
        return res.status(200).json({
            success: true,
            message: 'Employees added successfully',
            ...results
        });
    } catch (error) {
        console.error('Error processing employee data:', error);
        return res.status(500).json({
            success: false,
            message: 'Error processing employee data',
            error: error.message
        });
    }
});

router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Enable self-service route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing enable request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        console.log("Employee role not found");
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });
      console.log("Existing user found:", user ? "yes" : "no");

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();
      console.log("User saved successfully");

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      // Ensure personalDetails exists and has idType set
      if (!employee.personalDetails) {
        employee.personalDetails = {};
      }
      
      // Only set idType if it doesn't exist to avoid overwriting existing data
      if (!employee.personalDetails.idType) {
        employee.personalDetails.idType = "other"; // Set default value to pass validation
      }

      await employee.save();
      console.log("Employee updated successfully");

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email sent successfully");

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      
      // Log validation errors in more detail
      if (error.name === 'ValidationError') {
        console.error('Validation errors:', JSON.stringify(error.errors));
      }
      
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/selfService/enable/:employeeId",
  async (req, res) => {
    console.log("Enable self-service route hit");
    console.log("Processing enable request for employee:", req.params.employeeId);
    try {
      const employee = await Employee.findById(req.params.employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee already has self-service enabled
      if (employee.selfServiceEnabled) {
        return res.status(400).json({
          success: false,
          message: "Self-service is already enabled for this employee",
        });
      }

      // Check if email exists
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address for self-service",
        });
      }

      // Generate a temporary password
      const tempPassword = generateTempPassword();

      // Check if user already exists with this email
      let user = await User.findOne({ email: employee.email });
      const existingUser = !!user;
      console.log("Existing user found:", existingUser ? "yes" : "no");

      // Find the employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Create or update user
      if (existingUser) {
        user.companies = [...new Set([...user.companies, employee.company])];
        if (!user.currentCompany) {
          user.currentCompany = employee.company;
        }
      } else {
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          bio: "Tell us about yourself",
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();
      console.log("User saved successfully");

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      // Ensure personalDetails exists and has idType set
      if (!employee.personalDetails) {
        employee.personalDetails = {};
      }
      
      // Only set idType if it doesn't exist to avoid overwriting existing data
      if (!employee.personalDetails.idType) {
        employee.personalDetails.idType = "other"; // Set default value to pass validation
      }

      await employee.save();
      console.log("Employee updated successfully");

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email sent successfully");

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      res.render("classifications", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering classifications form:", error);
      req.flash("error", "Failed to load classifications form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for classifications
router.post(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        workingHours,
        isDirector,
        typeOfDirector,
        isContractor,
        isUifExempt,
        uifExemptReason,
      } = req.body;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update employee
      const employee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        {
          $set: {
            workingHours,
            isDirector: !!isDirector,
            typeOfDirector: isDirector ? typeOfDirector : null,
            isContractor: !!isContractor,
            isUifExempt: !!isUifExempt,
            uifExemptReason: isUifExempt ? uifExemptReason : null,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      req.flash("success", "Classifications updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating classifications:", error);
      req.flash("error", "Failed to update classifications");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/classifications`
      );
    }
  }
);

// GET route for RFI configuration
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId)
        .populate("rfiConfig.selectedIncomes.componentId")
        .populate("rfiConfig.percentageIncomes.componentId");

      // Get the employee's payroll record with basic salary
      const payroll = await Payroll.findOne({ employee: employeeId });
      const assignedComponents = [];

      // Add basic salary if it exists in payroll
      if (payroll?.basicSalary) {
        assignedComponents.push({
          id: "basicSalary",
          name: "Basic Salary",
          amount: payroll.basicSalary,
          type: "income",
          isSelected:
            employee.rfiConfig?.selectedIncomes?.some(
              (inc) => inc.name === "Basic Salary"
            ) || false,
        });
      }

      // Add other components from payroll if they exist and have values
      if (payroll) {
        // Commission
        if (payroll.commission && payroll.commission.amount) {
          assignedComponents.push({
            id: "commission",
            name: "Commission",
            amount: payroll.commission.amount,
            type: "income",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Commission"
              ) || false,
          });
        }

        // Travel Allowance
        if (payroll.travelAllowance && payroll.travelAllowance.amount) {
          assignedComponents.push({
            id: "travelAllowance",
            name: "Travel Allowance",
            amount: payroll.travelAllowance.amount,
            type: "allowance",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Travel Allowance"
              ) || false,
          });
        }

        // Add other allowances if they exist
        if (payroll.allowances && payroll.allowances.length > 0) {
          payroll.allowances.forEach((allowance) => {
            if (allowance.amount) {
              assignedComponents.push({
                id: allowance._id.toString(),
                name: allowance.name,
                amount: allowance.amount,
                type: "allowance",
                isSelected:
                  employee.rfiConfig?.selectedIncomes?.some(
                    (inc) => inc.name === allowance.name
                  ) || false,
              });
            }
          });
        }

        // Add any other relevant components
        // ... add other components as needed
      }

      // Sort components by type and name
      assignedComponents.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === "income" ? -1 : 1;
      });

      res.render("defineRFI", {
        company,
        employee,
        payComponents: assignedComponents, // Pass the assigned components
        rfiConfig: employee.rfiConfig || {
          option: "",
          selectedIncomes: [],
          percentageIncomes: {},
        },
        validation: RFI_VALIDATION,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI configuration form:", error);
      req.flash(
        "error",
        "An error occurred while loading the RFI configuration"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for RFI configuration
router.post(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { option, selectedIncomes, percentageValues } = req.body;

      // Get company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Validate required fields
      if (!option) {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: "RFI calculation method is required",
        });
      }

      // Validate based on selected option
      if (option === "selectedIncome") {
        if (
          !selectedIncomes ||
          !Array.isArray(selectedIncomes) ||
          selectedIncomes.length === 0
        ) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: "At least one income component must be selected",
          });
        }
      } else if (option === "percentagePerIncome") {
        if (!percentageValues || Object.keys(percentageValues).length === 0) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message:
              "Percentage values are required for at least one component",
          });
        }

        // Validate percentage values
        for (const [component, percentage] of Object.entries(
          percentageValues
        )) {
          const value = parseFloat(percentage);
          if (isNaN(value) || value < 0 || value > 100) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Invalid percentage value for ${component}. Must be between 0 and 100`,
            });
          }
        }
      }

      // Update employee RFI configuration
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        {
          $set: {
            "rfiConfig.option": option,
            "rfiConfig.selectedIncomes":
              option === "selectedIncome"
                ? selectedIncomes.map((name) => ({
                    name,
                    includeInCalculation: true,
                  }))
                : [],
            "rfiConfig.percentageIncomes":
              option === "percentagePerIncome"
                ? Object.entries(percentageValues).map(
                    ([name, percentage]) => ({
                      name,
                      percentage: parseFloat(percentage),
                    })
                  )
                : [],
          },
        },
        { new: true, session }
      );

      if (!updatedEmployee) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Add audit log with company ID
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id, // Add company ID here
            action: "UPDATE_RFI_CONFIG",
            entity: "Employee",
            entityId: employeeId,
            details: {
              option,
              selectedIncomes:
                option === "selectedIncome" ? selectedIncomes : [],
              percentageValues:
                option === "percentagePerIncome" ? percentageValues : {},
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "RFI configuration updated successfully",
        data: updatedEmployee.rfiConfig,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating RFI configuration:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update RFI configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

const RFI_VALIDATION = {
  MIN_PERCENTAGE: 0,
  MAX_PERCENTAGE: 100,
  MIN_COMPONENTS: 1,
  REQUIRED_COMPONENTS: ["basicSalary"], // Components that must be included
};
// Add these validation rules at the top of the file
const FUND_VALIDATION = {
  FIXED_AMOUNT: {
    MIN: 0,
    MAX: 1000000000, // 1 billion
    DECIMALS: 2,
  },
  PERCENTAGE: {
    MIN: 0,
    MAX: 100,
    DECIMALS: 2,
  },
  CATEGORY_FACTOR: {
    MIN: 0.01,
    MAX: 10,
    DECIMALS: 2,
  },
};

// Add validation middleware
const validateFundInput = (req, res, next) => {
  const {
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
  } = req.body;

  const errors = [];

  try {
    // Validate contribution method
    if (!["fixedAmount", "percentageRFI"].includes(contributionCalculation)) {
      errors.push("Invalid contribution calculation method");
    }

    // Validate fixed amounts if that method is selected
    if (contributionCalculation === "fixedAmount") {
      const employeeAmount = parseFloat(fixedContributionEmployee);
      const employerAmount = parseFloat(fixedContributionEmployer);

      if (
        isNaN(employeeAmount) ||
        employeeAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employeeAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employee fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }

      if (
        isNaN(employerAmount) ||
        employerAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employerAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employer fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }
    }

    // Validate RFI percentages if that method is selected
    if (contributionCalculation === "percentageRFI") {
      const employeePercentage = parseFloat(rfiEmployee);
      const employerPercentage = parseFloat(rfiEmployer);

      if (
        isNaN(employeePercentage) ||
        employeePercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employeePercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employee RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }

      if (
        isNaN(employerPercentage) ||
        employerPercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employerPercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employer RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }
    }

    // Validate category factor
    if (categoryFactor) {
      const factor = parseFloat(categoryFactor);
      if (
        isNaN(factor) ||
        factor < FUND_VALIDATION.CATEGORY_FACTOR.MIN ||
        factor > FUND_VALIDATION.CATEGORY_FACTOR.MAX
      ) {
        errors.push(
          `Category factor must be between ${FUND_VALIDATION.CATEGORY_FACTOR.MIN} and ${FUND_VALIDATION.CATEGORY_FACTOR.MAX}`
        );
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("Fund validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating fund configuration",
    });
  }
};

// GET route for regular hours page
router.get(
  "/:companyCode/:employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Fetch the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Fetch the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the regular hours page
      res.render("regularHours", {
        company,
        employee,
        csrfToken: req.csrfToken(), // Add CSRF token
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering regular hours form:", error);
      req.flash("error", "Failed to load regular hours form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;

      console.log("=== Regular Hours Update ===");
      console.log("Request body:", req.body);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      console.log("Updated employee regular hours:", employee.regularHours);
      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

router.get(
  "/:companyCode/:employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection, // Make sure this middleware is here
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Generate CSRF token
      const csrfToken = req.csrfToken();
      console.log("Generated CSRF token:", csrfToken); // Debug log

      // Render with all necessary data
      res.render("eti", {
        company,
        employee,
        user: req.user,
        csrfToken, // Pass the token explicitly
        messages: req.flash(),
        moment: require("moment"), // Add moment if needed for date formatting
      });
    } catch (error) {
      console.error("Error rendering ETI page:", error);
      req.flash("error", "An error occurred while loading the ETI page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for ETI updates
router.post(
  "/:companyCode/employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1);
      effectiveDate.setHours(0, 0, 0, 0);

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/eti`
      );
    }
  }
);

router.get(
  "/:companyCode/:employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("skillsEquity", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering Skills Equity page:", error);
      req.flash(
        "error",
        "An error occurred while loading the Skills Equity page"
      );
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// POST route for updating Skills Equity
router.post(
  "/:companyCode/employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        gender,
        race,
        occupationLevel,
        occupationCategory,
        jobValue,
        province,
        disabled,
        foreignNational,
        notRSACitizen,
      } = req.body;

      // First find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find and update employee using company._id
      const employee = await Employee.findOneAndUpdate(
        {
          _id: employeeId,
          company: company._id, // Use company._id instead of req.company._id
        },
        {
          $set: {
            gender,
            race,
            occupationLevel,
            occupationCategory,
            jobValue,
            province,
            disabled: !!disabled,
            foreignNational: !!foreignNational,
            notRSACitizen: !!notRSACitizen,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Success case
      req.flash("success", "Skills & Equity information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating Skills & Equity:", error);
      req.flash("error", "Failed to update Skills & Equity information");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/skillsEquity`
      );
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Delete page route hit:", { companyCode, employeeId });

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  invalidateEmployeeCache,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Processing delete request for employee:", employeeId);

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Update hourly status
router.post('/:companyCode/employee/:employeeId/hourly-status', ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { employeeId, companyCode } = req.params;
    const { hourlyPaid } = req.body;

    console.log('Updating hourly status for employee:', employeeId, 'in company:', companyCode, 'to:', hourlyPaid);

    // Find company first to validate company code
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.log('Company not found:', companyCode);
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Find the employee and ensure they belong to the company
    const employee = await Employee.findOne({
      _id: employeeId,
      company: company._id
    });

    if (!employee) {
      console.log('Employee not found:', employeeId, 'in company:', companyCode);
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Update the hourly paid status
    if (!employee.regularHours) {
      employee.regularHours = {};
    }
    employee.regularHours.hourlyPaid = hourlyPaid;

    // Save the changes
    await employee.save();

    console.log('Successfully updated hourly status for employee:', employeeId, 'in company:', companyCode);

    res.json({
      success: true,
      message: `Employee is now ${hourlyPaid ? 'hourly paid' : 'salaried'}`,
      hourlyPaid: hourlyPaid
    });
  } catch (error) {
    console.error('Error updating hourly paid status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update hourly paid status',
      error: error.message
    });
  }
});

// Self-service disable route
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Disable self-service route hit");
    try {
      const { employeeId, companyCode } = req.params;
      console.log("Processing disable request for employee:", employeeId);

      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find the employee and ensure they belong to the company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      });

      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();
      console.log("Employee self-service disabled successfully");

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Self-service resend invite route
router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Resend invite route hit");
    try {
      const { employeeId, companyCode } = req.params;
      console.log("Processing resend request for employee:", employeeId);

      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find the employee and ensure they belong to the company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      });

      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Use SelfServiceTokenService to handle the resend
      const baseUrl = `${req.protocol}://${req.get("host")}`;
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      console.log("Setup email resent successfully");

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);

module.exports = router;
