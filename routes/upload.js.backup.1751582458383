const express = require('express');
const router = express.Router();
const multer = require('multer');
const xlsx = require('xlsx');
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });
const { ensureAuthenticated } = require('../middleware/auth');

// Configure multer with enhanced error handling
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    console.log('\n=== File Upload Debug ===');
    console.log('Original name:', file.originalname);
    console.log('Mime type:', file.mimetype);
    console.log('Headers:', req.headers);
    
    // Check file type
    if (!file.originalname.match(/\.(xlsx|xls)$/)) {
      console.error('Invalid file type:', file.mimetype);
      return cb(new Error('Only Excel files are allowed'));
    }
    cb(null, true);
  }
}).single('file');

// Debug middleware for all routes
router.use((req, res, next) => {
  console.log('\n=== Upload Router Debug ===');
  console.log('URL:', req.url);
  console.log('Method:', req.method);
  console.log('Content Type:', req.get('content-type'));
  console.log('Content Length:', req.get('content-length'));
  console.log('CSRF Token:', req.get('csrf-token'));
  next();
});

// Wrap multer middleware to handle errors properly
const handleUpload = (req, res, next) => {
  console.log('\n=== Upload Request Debug ===');
  console.log('Headers:', {
    'content-type': req.get('content-type'),
    'content-length': req.get('content-length'),
    'csrf-token': req.get('csrf-token')
  });

  upload(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      console.error('\n=== Multer Error ===');
      console.error('Error Type:', err.name);
      console.error('Error Message:', err.message);
      console.error('Field name:', err.field);
      return res.status(400).json({ 
        success: false,
        error: 'File upload error',
        details: err.message 
      });
    } else if (err) {
      console.error('\n=== Upload Error ===');
      console.error('Error Type:', err.constructor.name);
      console.error('Error Message:', err.message);
      console.error('Stack:', err.stack);
      
      if (err.message === 'Unexpected end of form') {
        return res.status(400).json({
          success: false,
          error: 'Upload interrupted',
          details: 'The file upload was interrupted or incomplete. Please try again.'
        });
      }
      return res.status(400).json({ 
        success: false,
        error: 'File upload failed',
        details: err.message 
      });
    }

    console.log('\n=== Upload Success ===');
    console.log('File details:', req.file ? {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    } : 'No file');

    next();
  });
};

// Preview upload endpoint with enhanced error handling
router.post(
  '/:companyCode/employeeManagement/preview-upload',
  ensureAuthenticated,
  csrfProtection,
  handleUpload,
  async (req, res) => {
    console.log('\n=== Preview Upload Processing ===');
    console.log('Company Code:', req.params.companyCode);
    
    try {
      // Check if file exists
      if (!req.file || !req.file.buffer) {
        console.error('No file received in request');
        return res.status(400).json({
          success: false,
          error: 'No file uploaded',
          details: 'Please select a file to upload'
        });
      }

      console.log('Processing file:', {
        filename: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      });

      // Validate file type
      if (!req.file.originalname.match(/\.(xlsx|xls)$/)) {
        console.error('Invalid file type:', req.file.mimetype);
        return res.status(400).json({
          success: false,
          error: 'Invalid file type',
          details: 'Please upload an Excel file (.xlsx or .xls)'
        });
      }

      try {
        // Process the Excel file
        console.log('Reading Excel buffer...');
        const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
        console.log('Workbook loaded successfully');
        console.log('Available sheets:', workbook.SheetNames);

        if (workbook.SheetNames.length === 0) {
          throw new Error('Excel file contains no sheets');
        }

        // Get the first sheet
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        console.log('Processing first sheet:', workbook.SheetNames[0]);
        
        const rows = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
        console.log('Rows extracted:', rows.length);

        if (rows.length < 2) {
          throw new Error('Excel file must contain at least a header row and one data row');
        }

        // Process the data and return preview
        const headers = rows[0];
        console.log('Headers found:', headers);
        
        const previewData = rows.slice(1, 6).map(row => {
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = row[index];
          });
          return rowData;
        });

        console.log('Preview data prepared');
        return res.json({
          success: true,
          headers: headers,
          preview: previewData,
          totalRows: rows.length - 1 // Exclude header row
        });

      } catch (excelError) {
        console.error('Excel processing error:', excelError);
        console.error('Stack:', excelError.stack);
        return res.status(400).json({
          success: false,
          error: 'Excel processing failed',
          details: excelError.message
        });
      }

    } catch (error) {
      console.error('Preview processing error:', error);
      console.error('Stack:', error.stack);
      // Ensure we always return a JSON response, even for unexpected errors
      return res.status(500).json({
        success: false,
        error: 'Server error',
        details: 'An unexpected error occurred while processing the file'
      });
    }
  }
);

module.exports = router;
