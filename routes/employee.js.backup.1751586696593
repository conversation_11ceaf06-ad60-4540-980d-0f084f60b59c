const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const nodemailer = require("nodemailer");
const jwt = require("jsonwebtoken");
const ensureAuthenticated = require("../config/ensureAuthenticated");

// Add this before any JWT middleware
router.get("/setup/:token", async (req, res, next) => {
  try {
    const { token } = req.params;

    // Find employee with valid token
    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      return res.render("error", {
        message:
          "Invalid or expired setup link. Please request a new invitation.",
        error: { status: 400 },
      });
    }

    // Render setup form
    res.render("employee-setup", {
      employee,
      token,
      csrfToken: req.csrfToken(),
    });
  } catch (error) {
    console.error("Error in setup route:", error);
    next(error);
  }
});

// JWT verification middleware for other routes
router.use((req, res, next) => {
  // Skip JWT verification for setup route
  if (req.path.startsWith("/setup/")) {
    return next();
  }

  // Your existing JWT verification logic
  const token = req.headers.authorization?.split(" ")[1];
  if (!token) {
    return res.status(401).json({ message: "No token provided" });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: "Invalid token" });
  }
});

// Create a Nodemailer transporter
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Employee Dashboard
router.get("/dashboard", (req, res) => {
  res.render("employeeDashboard", {
    user: req.session.user,
  });
});

router.post("/self-service", async (req, res) => {
  try {
    const employeeUpdates = Object.keys(req.body)
      .filter((key) => key.startsWith("email_"))
      .map((key) => {
        const id = key.split("_")[1];
        return {
          id,
          email: req.body[key],
          enabled: req.body[`enabled_${id}`] === "on",
        };
      });

    for (let { id, email, enabled } of employeeUpdates) {
      const employee = await Employee.findById(id);

      if (!employee) {
        console.error(`Employee with ID ${id} not found.`);
        continue;
      }

      const wasEnabled = employee.selfServiceEnabled;
      employee.selfServiceEnabled = enabled;

      // Save the employee status change
      await employee.save();

      // Send email if the user is newly enabled and has not received the email before
      if (enabled && (!wasEnabled || !employee.essEmailSent)) {
        const token = jwt.sign(
          { email: employee.email },
          process.env.JWT_SECRET,
          { expiresIn: "1h" }
        );

        const setupLink = `${req.protocol}://${req.get(
          "host"
        )}/employee/setup/${token}`;

        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: employee.email,
          subject: "Set Up Your Employee Self-Service Profile",
          text: `Please follow the link to set up your profile: ${setupLink}`,
        };

        transporter.sendMail(mailOptions, async (error, info) => {
          if (error) {
          } else {
            employee.essEmailSent = true;
            employee.lastActivity = new Date(); // Update last activity on email sent

            // Add flash message for successful email sending
            req.flash(
              "success",
              `Self-service setup email successfully sent to ${employee.email}.`
            );

            // Save the email sent flag without using await
            employee
              .save()
              .catch((err) => console.error("Error saving employee:", err));
          }
        });
      } else {
        console.log(
          `Self-service already enabled and email sent for ${employee.email}.`
        );
      }
    }

    res.redirect("/employeeManagement");
  } catch (err) {
    console.error(err);
    req.flash("error", "An error occurred while processing the request.");
    res.status(500).redirect("/employeeManagement");
  }
});

// Setup Profile Route
router.get("/setup/:token", async (req, res) => {
  try {
    const { token } = req.params;
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const employee = await Employee.findOne({ email: decoded.email });

    if (!employee) {
      return res.status(404).send("Invalid token or user does not exist");
    }

    res.render("setupProfile", { email: employee.email });
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});

// Complete Profile Setup
router.post("/complete-setup", async (req, res) => {
  try {
    const { email, password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      return res.status(400).send("Passwords do not match");
    }

    const employee = await Employee.findOne({ email });

    if (!employee) {
      return res.status(404).send("User not found");
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    // Update the employee's password
    employee.password = hashedPassword;
    await employee.save();

    res.redirect("/auth/login");
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});

// Example: routes/employees.js
router.get("/", async (req, res) => {
  const employees = await Employee.find({ company: req.company._id });
  res.render("employees/index", { employees });
});

router.get(
  "/:companyCode/employees/:employeeId/pro-rata-override/:payslipId",
  async (req, res) => {
    try {
      const { companyCode, employeeId, payslipId } = req.params;
      const employee = await Employee.findById(employeeId);
      const company = await Company.findOne({ companyCode });
      const payslip = await Payslip.findById(payslipId);

      if (!employee || !company || !payslip) {
        return res.status(404).render("error", { message: "Not found" });
      }

      res.render("pro-rata-override", {
        employee,
        company,
        payslipId,
        payslipDate: payslip.date,
        calculatedProRataPercentage: payslip.proratedPercentage,
        currentOverride: payslip.proRataOverride,
      });
    } catch (error) {
      console.error("Error rendering pro-rata override page:", error);
      res.status(500).render("error", { message: "Server error" });
    }
  }
);

// Route to handle the pro-rata override form submission
router.post("/:employeeId/pro-rata-override", async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { payslipId, proRataOverride } = req.body;

    const payslip = await Payslip.findById(payslipId);
    payslip.proRataOverride = proRataOverride
      ? parseFloat(proRataOverride)
      : null;
    await payslip.save();

    res.redirect(`/clients/${req.company.companyCode}/employees/${employeeId}`);
  } catch (error) {
    console.error(error);
    res.status(500).send("Server error");
  }
});

module.exports = router;
