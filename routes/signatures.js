const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const { ensureAuthenticated } = require("../config/auth");

// Signature Schema
const signatureSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  data: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ["draw", "type", "upload"],
    default: "draw",
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const Signature = mongoose.model("Signature", signatureSchema);

// Save a new signature
router.post("/save", ensureAuthenticated, async (req, res) => {
  try {
    const { name, data, type } = req.body;


    if (!name || !data) {
      console.log("Error: Missing required fields");
      return res
        .status(400)
        .json({ error: "Name and signature data are required" });
    }

    const signature = new Signature({
      user: req.user._id,
      name,
      data,
      type: type || "draw",
    });

    await signature.save();

    res.json({
      success: true,
      signatureId: signature._id,
      message: "Signature saved successfully",
    });
  } catch (error) {
    console.error("Error saving signature:", error);
    res.status(500).json({ error: "Failed to save signature" });
  }
});

// Get a specific signature
router.get("/:id", ensureAuthenticated, async (req, res) => {
  try {
    const signature = await Signature.findOne({
      _id: req.params.id,
      user: req.user._id,
    });

    if (!signature) {
      return res.status(404).json({ error: "Signature not found" });
    }


    res.json({
      id: signature._id,
      name: signature.name,
      data: signature.data,
      type: signature.type,
      createdAt: signature.createdAt,
    });
  } catch (error) {
    console.error("Error fetching signature:", error);
    res.status(500).json({ error: "Failed to fetch signature" });
  }
});

// Get all signatures for the current user
router.get("/", ensureAuthenticated, async (req, res) => {
  try {
    const signatures = await Signature.find({ user: req.user._id }).sort({
      createdAt: -1,
    });


    res.json(
      signatures.map((sig) => ({
        id: sig._id,
        name: sig.name,
        type: sig.type,
        createdAt: sig.createdAt,
      }))
    );
  } catch (error) {
    console.error("Error fetching signatures:", error);
    res.status(500).json({ error: "Failed to fetch signatures" });
  }
});

// Delete a signature
router.delete("/:id", ensureAuthenticated, async (req, res) => {
  try {
    const result = await Signature.deleteOne({
      _id: req.params.id,
      user: req.user._id,
    });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: "Signature not found" });
    }

    res.json({ success: true, message: "Signature deleted successfully" });
  } catch (error) {
    console.error("Error deleting signature:", error);
    res.status(500).json({ error: "Failed to delete signature" });
  }
});

module.exports = router;
