const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const PayRun = require("../models/payRun");
const Company = require("../models/Company");
const nodeFetch = require("node-fetch");
const csrf = require("csurf");
const axios = require("axios");

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Simple logging middleware
router.use((req, res, next) => {
  console.log(`PayRuns API: ${req.method} ${req.path}`);
  next();
});

// Client-facing route for payrun details
router.get("/:companyCode/payruns/:id", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('\n=== Pay Run Details Route Handler Start ===');
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id,
    path: req.path,
    originalUrl: req.originalUrl
  });
  console.log('Session data:', {
    userId: req.user?._id,
    isAuthenticated: req.isAuthenticated(),
    companyCode: req.session?.companyCode
  });

  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
        console.log('Using company code from session:', companyCode);
        
        // Redirect to the correct URL with the company code
        const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
        console.log('Redirecting to correct URL:', correctUrl);
        return res.redirect(correctUrl);
      }
      
      // If still no company code, try to get from user's current company
      if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
          console.log('Using company code from user current company:', companyCode);
          
          // Redirect to the correct URL with the company code
          const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
          console.log('Redirecting to correct URL:', correctUrl);
          return res.redirect(correctUrl);
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      console.log('No company code found in URL, session, or user data');
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    console.log('\n=== Company Lookup ===');
    console.log('Looking for company with code:', companyCode);
    
    // Find company
    const company = await Company.findOne({ companyCode });
    console.log('Company lookup result:', company ? {
      id: company._id,
      code: company.companyCode,
      name: company.name
    } : 'Not found');

    if (!company) {
      console.log('Company not found, returning 404');
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }

    console.log('\n=== Pay Run Lookup ===');
    console.log('Looking for pay run with ID:', req.params.id);
    console.log('Company ID:', company._id);

    // Find payrun and populate payslips and employee data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    console.log('Pay run lookup result:', payRun ? {
      id: payRun._id,
      startDate: payRun.startDate,
      endDate: payRun.endDate,
      payslipsCount: payRun.payslips ? payRun.payslips.length : 0
    } : 'Not found');

    // If pay run not found, return 404
    if (!payRun) {
      return res.status(404).render('error', {
        message: 'Pay run not found',
        error: { status: 404 }
      });
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      console.log('No payroll periods found in pay run, searching separately');
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      console.log(`Found ${payrollPeriods.length} payroll periods separately`);
      
      // Attach them to the pay run
      if (payrollPeriods.length > 0) {
        try {
          // Save the IDs to the database
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          console.log('Updated pay run with payroll periods');
          
          // For rendering, use the fully populated payroll periods
          payRun.payrollPeriods = payrollPeriods;
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    } else {
      // If payrollPeriods exist but employee is not populated, populate it now
      if (payRun.payrollPeriods.length > 0 && (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' || !payRun.payrollPeriods[0].employee.firstName)) {
        console.log('PayrollPeriods exist but employee is not populated, populating now');
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    console.log('\n=== Calculating Totals ===');
    // Calculate totals
    const totals = payRun.payslips.reduce((acc, payslip) => {
      acc.totalGrossPay += payslip.grossPay || 0;
      acc.totalNetPay += payslip.netPay || 0;
      acc.totalPAYE += payslip.PAYE || 0;
      acc.totalSDL += payslip.SDL || 0;
      acc.totalUIF += payslip.UIF || 0;
      return acc;
    }, {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    });

    console.log('Calculated totals:', totals);

    console.log('\n=== Rendering Template ===');
    // Render the payrun details page
    res.render('payRunDetails', {
      title: 'Pay Run Details',
      company,
      payRun,
      user: req.user,
      csrfToken: req.csrfToken(),
      moment: require('moment'),
      ...totals
    });
    console.log('Template rendered successfully');

  } catch (error) {
    console.error('\n=== Error in Pay Run Details Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error fetching pay run details',
      error: { 
        status: 500, 
        stack: process.env.NODE_ENV === 'development' ? error.stack : '' 
      }
    });
  }
});

// Process a pay run
router.post("/:companyCode/payruns/:id/process", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('\n=== Process Pay Run Route Handler Start ===');
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      console.log('Company not found');
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    });

    if (!payRun) {
      console.log('Pay run not found');
      return res.status(404).render('error', { 
        message: 'Pay run not found',
        error: { status: 404, stack: '' }
      });
    }

    // Update the pay run status to processing
    payRun.status = 'processing';
    await payRun.save();

    console.log('Pay run status updated to processing');

    // Redirect back to the pay run details page
    res.redirect(`/clients/${company.companyCode}/payruns/${payRun._id}`);

  } catch (error) {
    console.error('\n=== Error in Process Pay Run Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error processing pay run',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// Finalize a pay run
router.post("/:companyCode/payruns/:id/finalize", [ensureAuthenticated, csrfProtection], async (req, res) => {
  console.log('\n=== Finalize Pay Run Route Handler Start ===');
  console.log('Request params:', {
    companyCode: req.params.companyCode,
    payRunId: req.params.id
  });

  try {
    // Validate company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }
    console.log('Company found:', company.name);

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }
    console.log('Pay run found:', payRun._id);

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      console.log('No payroll periods found in pay run, searching separately');
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      console.log(`Found ${payrollPeriods.length} payroll periods separately`);
      
      // Attach them to the pay run
      payRun.payrollPeriods = payrollPeriods;
      
      // Save the updated pay run with the payroll periods
      if (payrollPeriods.length > 0) {
        try {
          // Save the IDs to the database
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          console.log('Updated pay run with payroll periods');
          
          // Re-fetch the pay run with populated payroll periods
          await payRun.populate({
            path: 'payrollPeriods',
            populate: {
              path: 'employee',
              select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
            }
          });
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    } else {
      // If payrollPeriods exist but employee is not populated, populate it now
      if (payRun.payrollPeriods.length > 0 && (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' || !payRun.payrollPeriods[0].employee.firstName)) {
        console.log('PayrollPeriods exist but employee is not populated, populating now');
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    // Check if payslips need to be created
    if ((!payRun.payslips || payRun.payslips.length === 0) && payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      console.log('No payslips found, creating payslips from payroll periods');
      
      const Payslip = require('../models/Payslip');
      
      // Check for existing payslips to avoid duplicates
      const existingPayslips = await Payslip.find({
        payRun: payRun._id
      });
      
      if (existingPayslips.length > 0) {
        console.log(`Found ${existingPayslips.length} existing payslips for this pay run, using them instead of creating new ones`);
        payRun.payslips = existingPayslips.map(p => p._id);
        payRun.status = 'finalized';
        payRun.finalizedAt = new Date();
        payRun.finalizedBy = req.user._id;
        await payRun.save();
      } else {
        // Create payslips from payroll periods
        const payslips = await Promise.all(
          payRun.payrollPeriods.map(async (period) => {
            console.log(`Creating payslip for employee ${period.employee.firstName} ${period.employee.lastName}`);
            console.log(`Payroll period data: Basic Salary: ${period.basicSalary}, Gross Pay: ${period.grossPay}, Net Pay: ${period.netPay}`);
            
            // Extract earnings and deductions if available
            const earnings = [];
            if (period.basicSalary) {
              earnings.push({
                type: 'basic',
                description: 'Basic Salary',
                amount: period.basicSalary
              });
            }
            
            // Extract deductions if available
            const deductions = [];
            if (period.totalDeductions && period.totalDeductions > 0) {
              // If we don't have detailed deductions, create a generic tax entry
              deductions.push({
                type: 'tax',
                description: 'Tax Deductions',
                amount: period.totalDeductions
              });
            }
            
            // Create payslip with all available data
            const payslip = new Payslip({
              company: company._id,
              employee: period.employee._id,
              payrollPeriod: period._id,
              payRun: payRun._id,
              basicSalary: period.basicSalary || 0,
              grossPay: period.grossPay || 0,
              totalDeductions: period.totalDeductions || 0,
              netPay: period.netPay || 0,
              earnings: earnings,
              deductions: deductions,
              status: 'approved',
              paymentMethod: period.employee.paymentMethod || 'EFT',
              bankDetails: {
                accountHolder: period.employee.bankDetails?.accountHolder || `${period.employee.firstName} ${period.employee.lastName}`,
                accountNumber: period.employee.bankDetails?.accountNumber || '',
                bankName: period.employee.bankDetails?.bankName || '',
                branchCode: period.employee.bankDetails?.branchCode || ''
              }
            });
            
            try {
              const savedPayslip = await payslip.save();
              console.log(`Payslip created with ID: ${savedPayslip._id}`);
              return savedPayslip;
            } catch (error) {
              console.error(`Error creating payslip for employee ${period.employee._id}:`, error);
              throw error;
            }
          })
        );
        
        console.log(`Created ${payslips.length} payslips`);
        
        // Update payRun with the newly created payslips
        payRun.payslips = [...(payRun.payslips || []), ...payslips.map(p => p._id)];
        payRun.status = 'finalized';
        payRun.finalizedAt = new Date();
        payRun.finalizedBy = req.user._id;
        
        await payRun.save();
        console.log(`Pay run ${payRun._id} updated with ${payslips.length} new payslips and status set to finalized`);
      }
    }

    res.json({
      success: true,
      message: 'Pay run finalized successfully',
      payRunId: payRun._id
    });

  } catch (error) {
    console.error('\n=== Error in Finalize Pay Run Route ===');
    console.error(error);
    console.error('=== End of Error ===\n');
