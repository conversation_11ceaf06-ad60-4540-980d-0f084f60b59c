const express = require("express");
const router = express.Router();
const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const fs = require("fs").promises;
const path = require("path");
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Payslip = require("../models/Payslip");
const payrollCalculations = require("../utils/payrollCalculations");
const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");
const PayrollService = require("../services/PayrollService");
const Payroll = require("../models/Payroll");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const Company = require("../models/Company");
const PayRun = require("../models/payRun");
const archiver = require('archiver');

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Helper function to format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// POST: Finalize a payslip
router.post("/:payslipId/finalize", ensureAuthenticated, async (req, res) => {

  try {
    const { payslipId } = req.params;
    const payrollPeriod = await PayrollPeriod.findById(payslipId).populate("employee", "firstName lastName");

    if (!payrollPeriod) {
      return res.status(404).json({
        success: false,
        message: "Payroll period not found",
      });
    }

    // Check if user has access to this company
    const hasAccess = req.user.companies.some((companyId) =>
      companyId.equals(payrollPeriod.company)
    );

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to finalize this payslip",
      });
    }

    // Check if already finalized
    if (payrollPeriod.isFinalized) {
      return res.status(400).json({
        success: false,
        message: "Payroll period is already finalized",
      });
    }

    // Update current payroll period
    payrollPeriod.isFinalized = true;
    payrollPeriod.finalizedAt = new Date();
    payrollPeriod.finalizedBy = req.user._id;
    await payrollPeriod.save();

    // Get employee details for generating next period
    const employee = await Employee.findById(payrollPeriod.employee).populate(
      "payFrequency"
    );
    if (!employee) {
      throw new Error("Employee not found");
    }

    // Generate next period using the PayrollPeriod model's logic
    const newPayrollPeriod = await PayrollPeriod.generateNextPeriod(
      employee,
      payrollPeriod
    );
    if (!newPayrollPeriod) {
    }

    console.log(
      "Successfully finalized payroll period and generated next period:",
      {
        finalized: payslipId,
        newPeriod: newPayrollPeriod?._id,
      }
    );

    res.json({
      success: true,
      message: "Payroll period finalized successfully",
      newPeriodId: newPayrollPeriod?._id,
    });
  } catch (error) {
    console.error("Error finalizing payroll period:", error);
    res.status(500).json({
      success: false,
      message: "Error finalizing payroll period",
    });
  }
});

// Update the GET route
router.get("/download/:employeeId/:month", async (req, res) => {
  const { employeeId, month } = req.params;

  console.log("Download request:", {
    employeeId,
    month,
  });

  try {
    // Get employee with populated company and employerDetails
    const employee = await Employee.findById(employeeId).populate([
      {
        path: "company",
        populate: {
          path: "employerDetails",
        },
      },
      "payFrequency",
    ]);

    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Find the payroll period
    const requestedDate = moment.tz(month, DEFAULT_TIMEZONE);
    const payrollPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: { $lte: requestedDate.toDate() },
      endDate: { $gte: requestedDate.toDate() },
    });

    if (!payrollPeriod) {
      return res.status(404).send("No payroll period found for this date");
    }

    // Get calculations from PayrollService
    const calculations = await PayrollService.calculatePayrollTotals(
      employeeId,
      payrollPeriod.endDate
    );

    // Destructure all needed values - Fixed data structure mapping
    const {
      totalIncome,  // Fixed: Changed from 'grossIncome: totalIncome' to 'totalIncome'
      basicSalary,  // Added: Extract basic salary from calculations
      deductions: {
        statutory: { paye, uif },
        courtOrders: { maintenanceOrder },
        total: totalDeductions,
      },
      netPay,
    } = calculations;

    // Extract basic salary value properly (handle object structure)
    const basicSalaryValue = typeof basicSalary === 'object'
      ? (basicSalary.full || basicSalary.prorated || 0)
      : (basicSalary || 0);

    // Ensure totalIncome is never undefined - add fallback
    const safeTotalIncome = totalIncome || basicSalaryValue || 0;

    // Calculate SDL (1% of total income) with safe value
    const sdl = safeTotalIncome * 0.01;


    // Calculate medical aid credit using safe total income
    const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
      payrollPeriod.medical || {},
      employee.payFrequency.frequency,
      safeTotalIncome
    );

    const medicalAidCredit = medicalAidImpact.taxCredit || 0;

    // Format dates consistently
    const formattedStartDate = moment(payrollPeriod.startDate).format(
      "DD/MM/YYYY"
    );
    const formattedEndDate = moment(payrollPeriod.endDate).format("DD/MM/YYYY");
    const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;

    // Add debug logging with corrected values
    console.log("Payslip Values (CORRECTED):", {
      basicSalary: basicSalaryValue,
      totalIncome: safeTotalIncome,
      paye,
      uif,
      sdl,
      maintenanceOrder,
      totalDeductions,
      netPay,
      medicalAidCredit,
    });

    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
    const { width, height } = page.getSize();

    // Load fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(
      StandardFonts.HelveticaBold
    );

    // Define helper functions first
    const drawText = (text, x, y, options = {}) => {
      const {
        font = helveticaFont,
        size = 8,
        color = rgb(0, 0, 0),
        align = "left",
      } = options;

      // Handle numbers and format them safely
      if (typeof text === "number") {
        text = text.toLocaleString("en-ZA", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      }

      const textWidth = font.widthOfTextAtSize(text.toString(), size);
      const textX =
        align === "center"
          ? x - textWidth / 2
          : align === "right"
          ? x - textWidth
          : x;
      page.drawText(text.toString(), { x: textX, y, font, size, color });
    };

    // Helper function to draw text with more space between label and amount
    const drawTextWithSpace = (label, amount, x, y, options = {}) => {
      drawText(label, x, y, options);
      drawText(formatCurrency(amount), x + 180, y, {
        ...options,
        align: "right",
      });
    };

    // Helper function to format currency safely
    const formatCurrency = (amount) => {
      const safeAmount = Number(amount) || 0;
      return new Intl.NumberFormat("en-ZA", {
        style: "currency",
        currency: "ZAR",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(safeAmount);
    };

    // Add DRAFT watermark if period is not finalized
    if (!payrollPeriod.isFinalized) {
      // Draw diagonal DRAFT text
      const draftText = "DRAFT";
      const fontSize = 60;
      const draftWidth = helveticaBoldFont.widthOfTextAtSize(
        draftText,
        fontSize
      );

      // Calculate rotation in radians (45 degrees)
      const rotationAngle = Math.PI / 4; // 45 degrees in radians

      page.drawText(draftText, {
        x: (width - draftWidth) / 2,
        y: height / 2,
        font: helveticaBoldFont,
        size: fontSize,
        color: rgb(0.9, 0.3, 0.3), // Light red color
        opacity: 0.3, // Semi-transparent
        rotate: {
          type: "degrees",
          angle: 45,
          origin: {
            x: width / 2,
            y: height / 2,
          },
        },
      });

      // Add draft notice at the top
      const noticeText = "This is a draft payslip - Not finalized";
      page.drawText(noticeText, {
        x: 50,
        y: height - 30,
        font: helveticaBoldFont,
        size: 10,
        color: rgb(0.9, 0.3, 0.3),
      });
    }

    // Add logo if it exists
    if (employee.company.employerDetails && employee.company.employerDetails.logo) {
      try {
        const logoPath = path.join(
          __dirname,
          "..",
          employee.company.employerDetails.logo
        );
        const logoFile = await fs.readFile(logoPath);
        const logoImage = await pdfDoc.embedPng(logoFile);
        const logoWidth = 100;
        const logoHeight = (logoWidth / logoImage.width) * logoImage.height;
        page.drawImage(logoImage, {
          x: 50,
          y: height - 20 - logoHeight, // Raised higher on the page
          width: logoWidth,
          height: logoHeight,
        });
      } catch (error) {
        console.error("Error loading logo:", error);
      }
    }

    // Company name (centered, 8pt)
    const companyName = employee.company.employerDetails?.tradingName || 
                       employee.company.name || 
                       "Company Name";
    drawText(
      companyName,
      width / 2,
      height - 50,
      { font: helveticaBoldFont, size: 8, align: "center" }
    );

    // Company address (right-aligned)
    const addressY = height - 100;
    const cityTown = employee.company.employerDetails?.physicalAddress?.cityTown || "City";
    const postalCode = employee.company.employerDetails?.physicalAddress?.code || "Postal Code";
    
    drawText(
      cityTown,
      width - 50,
      addressY,
      { align: "right" }
    );
    drawText(
      postalCode,
      width - 50,
      addressY - 15,
      { align: "right" }
    );

    // Update the period display based on frequency
    drawText(`Period: ${periodDisplay}`, 50, height - 145, {
      font: helveticaBoldFont,
      size: 8,
    });

    // Update employee details section with period-specific information
    const startY = height - 100;
    drawText(
      `Employee Name: ${employee.firstName} ${employee.lastName}`,
      50,
      startY
    );
    drawText(
      `Pay Frequency: ${
        employee.payFrequency.frequency.charAt(0).toUpperCase() +
        employee.payFrequency.frequency.slice(1)
      }`,
      50,
      startY - 15
    );
    
    // Safely format employment date
    const employmentDate = employee.doa ? 
        new Date(employee.doa).toLocaleDateString("en-ZA") : 
        "Not specified";
        
    drawText(
      `Employment Date: ${employmentDate}`,
      50,
      startY - 30
    );

    // Horizontal line under employee details
    const lineY = startY - 50;
    page.drawLine({
      start: { x: 50, y: lineY },
      end: { x: width - 50, y: lineY },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Create a table for income, deductions, and employer contributions
    const tableStartY = lineY - 20;
    const lineHeight = 20;
    const tableCol1 = 50;
    const tableCol2 = 200;
    const tableCol3 = 350;
    const tableCol4 = 450;

    // Headers (all bold headers now 8pt)
    drawText("Income", tableCol1, tableStartY, {
      font: helveticaBoldFont,
      size: 8,
    });
    drawText("Employer Contribution", tableCol3, tableStartY, {
      font: helveticaBoldFont,
      size: 8,
    });

    // Income - Using corrected values
    drawText("Basic Salary", tableCol1, tableStartY - lineHeight);
    drawTextWithSpace(
      "Basic Salary",
      safeTotalIncome,
      tableCol1,
      tableStartY - lineHeight
    );

    // Total Income

    drawText(
      formatCurrency(safeTotalIncome),
      tableCol2 - 8,
      tableStartY - 2 * lineHeight,
      { font: helveticaBoldFont, size: 8, align: "right" }
    );

    // Deductions
    drawText("Deduction", tableCol1, tableStartY - 3 * lineHeight, {
      font: helveticaBoldFont,
      size: 8,
    });
    drawText("Medical Aid", tableCol1, tableStartY - 4 * lineHeight);
    drawText(
      formatCurrency(payrollPeriod.medical?.medicalAid || 0),
      tableCol2 - 8,
      tableStartY - 4 * lineHeight,
      { align: "right" }
    );
    drawText("UIF", tableCol1, tableStartY - 5 * lineHeight);
    drawTextWithSpace("UIF", uif, tableCol1, tableStartY - 5 * lineHeight);
    drawText("PAYE", tableCol1, tableStartY - 6 * lineHeight);
    drawTextWithSpace("PAYE", paye, tableCol1, tableStartY - 6 * lineHeight);

    // Total Deductions
    drawText("Total Deductions:", tableCol1, tableStartY - 7 * lineHeight, {
      font: helveticaBoldFont,
      size: 8,
    });
    drawTextWithSpace(
      "Total Deductions:",
      totalDeductions,
      tableCol1,
      tableStartY - 7 * lineHeight,
      { font: helveticaBoldFont, size: 8 }
    );

    // Employer Contributions
    drawText("Employer Contribution", tableCol3, tableStartY, {
      font: helveticaBoldFont,
      size: 8,
    });
    drawTextWithSpace("SDL", sdl, tableCol3, tableStartY - lineHeight);
    drawTextWithSpace(
      "UIF - Employer",
      uif,
      tableCol3,
      tableStartY - 2 * lineHeight
    );

    // Tax Credit
    drawText("Tax Credit", tableCol3, tableStartY - 4 * lineHeight, {
      font: helveticaBoldFont,
      size: 8,
    });
    drawTextWithSpace(
      "Medical Aid Tax Credit",
      medicalAidCredit,
      tableCol3,
      tableStartY - 5 * lineHeight
    );

    // NETT PAY (moved to bottom of the page)
    const nettPayY = 50; // Adjust this value to position it at the bottom

    page.drawLine({
      start: { x: 50, y: nettPayY + 25 },
      end: { x: 545, y: nettPayY + 25 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    drawText("NETT PAY", 50, nettPayY, { font: helveticaBoldFont, size: 8 });
    drawTextWithSpace("NETT PAY", netPay, 50, nettPayY, {
      font: helveticaBoldFont,
      size: 8,
    });

    page.drawLine({
      start: { x: 50, y: nettPayY - 5 },
      end: { x: 545, y: nettPayY - 5 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Add finalization status to the footer
    const statusText = payrollPeriod.isFinalized
      ? "FINALIZED PAYSLIP"
      : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";

    drawText(statusText, width / 2, 30, {
      font: helveticaBoldFont,
      size: 8,
      color: payrollPeriod.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
      align: "center",
    });

    // Finalize and send the PDF
    const pdfBytes = await pdfDoc.save();
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=payslip_${employee._id}_${
        payrollPeriod.endDate.toISOString().split("T")[0]
      }.pdf`
    );
    res.send(Buffer.from(pdfBytes));
  } catch (err) {
    console.error("Error in payslip generation:", err);
    res.status(500).send("An error occurred while generating the payslip.");
  }
});

// Get pending payslips count
router.get(
  "/:companyCode/payroll/pending-count",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Employee.findOne({ code: companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Get all pending payslips
      const pendingPayslips = await PayrollPeriod.find({
        company: company._id,
        isFinalized: false,
      }).populate("employee", "firstName lastName");

      // Format payslips for response
      const formattedPayslips = pendingPayslips.map((p) => ({
        employeeName: `${p.employee.firstName} ${p.employee.lastName}`,
        period:
          p.startDate.toLocaleDateString("en-ZA") +
          " - " +
          p.endDate.toLocaleDateString("en-ZA"),
      }));

      res.json({
        pendingCount: pendingPayslips.length,
        pendingPayslips: formattedPayslips,
      });
    } catch (error) {
      console.error("Error getting pending payslips count:", error);
      res.status(500).json({ error: "Failed to get pending payslips count" });
    }
  }
);

// POST: Create new pay run from finalized periods
router.post(
  "/:companyCode/payruns",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { periodIds, releaseToSelfService } = req.body;


      // 1. Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      console.log("Found company:", {
        id: company._id,
        code: company.companyCode,
        name: company.name,
      });

      // 2. Validate periods
      const periods = await PayrollPeriod.find({
        _id: { $in: periodIds },
        company: company._id,
        isFinalized: true,
        status: { $ne: "locked" },
      }).populate({
        path: "employee",
        select: "firstName lastName",
        populate: {
          path: "company",
          populate: {
            path: "employerDetails",
          },
        },
      });

      console.log(
        JSON.stringify(
          periods.map((p) => ({
            periodId: p._id,
            isFinalized: p.isFinalized,
            status: p.status,
            startDate: p.startDate,
            endDate: p.endDate,
          })),
          null,
          2
        )
      );

      if (periods.length === 0) {
        return res.status(400).json({
          error: "No valid payroll periods found for pay run creation",
        });
      }

      if (periods.length !== periodIds.length) {
        console.log("Period count mismatch:", {
          requested: periodIds.length,
          found: periods.length,
        });
        return res.status(400).json({
          error:
            "Some payroll periods were not found or are not eligible for pay run creation",
        });
      }

      // 3. Sort and validate period dates
      periods.sort((a, b) => a.startDate - b.startDate);
      const firstPeriod = periods[0];
      const lastPeriod = periods[periods.length - 1];

      // 4. Generate monthYear based on the last period's end date
      const monthYear = moment(lastPeriod.endDate).format("YYYY-MM");

      // 5. Check for existing pay run with same company and monthYear
      const existingPayRun = await PayRun.findOne({
        company: company._id,
        monthYear: monthYear,
        status: { $ne: "draft" }, // Only check for non-draft pay runs
      });

      if (existingPayRun) {
        console.log("Existing non-draft pay run found:", {
          id: existingPayRun._id,
          monthYear: existingPayRun.monthYear,
          status: existingPayRun.status,
          company: existingPayRun.company,
        });
        return res.status(400).json({
          error: `A finalized pay run already exists for ${moment(
            lastPeriod.endDate
          ).format("MMMM YYYY")}`,
          details: {
            existingPayRun: {
              id: existingPayRun._id,
              period: existingPayRun.period,
              status: existingPayRun.status,
            },
          },
        });
      }

      // 6. Create new pay run with unique identifier
      const payRunIdentifier = `${company._id}_${monthYear}_${Date.now()}`;
      const payRun = new PayRun({
        company: company._id,
        createdBy: req.user._id,
        status: releaseToSelfService ? "released" : "draft",
        payrollPeriods: periodIds,
        startDate: firstPeriod.startDate,
        endDate: lastPeriod.endDate,
        frequency: firstPeriod.frequency,
        monthYear: monthYear,
        period: moment(lastPeriod.endDate).format("MMMM YYYY"),
        taxPeriod: moment(lastPeriod.endDate).format("YYYYMM"),
        paymentDate: moment().toDate(),
        payRunIdentifier: payRunIdentifier,
      });

      console.log("Creating pay run with data:", {
        company: company._id.toString(),
        monthYear: monthYear,
        period: moment(lastPeriod.endDate).format("MMMM YYYY"),
        frequency: firstPeriod.frequency,
        payRunIdentifier: payRunIdentifier,
      });

      // 7. Save pay run in a transaction
      const session = await mongoose.startSession();
      let savedPayRun;

      try {
        await session.withTransaction(async () => {
          // Save pay run
          savedPayRun = await payRun.save({ session });

          // Update payroll periods
          const updateResult = await PayrollPeriod.updateMany(
            {
              _id: { $in: periodIds },
              company: company._id,
              isFinalized: true,
              status: { $ne: "locked" },
            },
            {
              $set: {
                status: "locked",
                payRun: savedPayRun._id,
                updatedAt: new Date(),
              },
            },
            { session }
          );


          if (updateResult.modifiedCount !== periods.length) {
            throw new Error("Failed to update all payroll periods");
          }
        });

        // Send success response
        res.json({
          success: true,
          message: "Pay run created successfully",
          payRun: {
            id: savedPayRun._id,
            status: savedPayRun.status,
            periodCount: periodIds.length,
            period: savedPayRun.period,
            paymentDate: savedPayRun.paymentDate,
          },
        });
      } catch (error) {
        throw error;
      } finally {
        session.endSession();
      }
    } catch (error) {
      console.error("Error creating pay run:", error);

      if (
        error.code === 11000 &&
        error.keyPattern &&
        error.keyPattern.monthYear
      ) {
        return res.status(400).json({
          error: `A pay run already exists for this month`,
          details: {
            monthYear: error.keyValue.monthYear,
            message: error.message,
          },
        });
      }

      res.status(500).json({
        error: "Failed to create pay run",
        details: error.message,
      });
    }
  }
);

// GET: Download all payslips for a pay run
router.get("/bulk-download/:payRunId", ensureAuthenticated, async (req, res) => {
  
  try {
    const { payRunId } = req.params;
    
    if (!payRunId) {
      return res.status(400).send("Invalid pay run ID");
    }
    
    // First, get the raw pay run to check if it has payslips
    const rawPayRun = await PayRun.findById(payRunId);
    
    if (!rawPayRun) {
      return res.status(404).send("Pay run not found");
    }
    
    console.log(`Pay run found with ${rawPayRun.payslips?.length || 0} payslip IDs:`, 
                rawPayRun.payslips ? JSON.stringify(rawPayRun.payslips) : 'none');
    
    // Find the pay run with populated payslips and employees
    const payRun = await PayRun.findById(payRunId)
      .populate({
        path: "payslips",
        populate: {
          path: "employee",
          select: "firstName lastName employeeId company payFrequency doa bankDetails",
          populate: [
            {
              path: "company",
              populate: {
                path: "employerDetails",
              },
            },
            "payFrequency",
          ],
        },
      });
      
    if (payRun.payslips && payRun.payslips.length > 0) {
      console.log("First payslip populated:", payRun.payslips[0]._id, 
                  "Has employee:", !!payRun.payslips[0].employee);
    }

    // Check if user has access to this company
    const hasAccess = req.user.companies.some((companyId) =>
      companyId.equals(payRun.company)
    );

    if (!hasAccess) {
      return res.status(403).send("Not authorized to access these payslips");
    }
    
    // Check if we have populated payslips
    if (!payRun.payslips || payRun.payslips.length === 0 || 
        (payRun.payslips.length > 0 && !payRun.payslips[0].employee)) {
      
      // If we have payslip IDs but they didn't populate, there might be an issue with the references
      if (rawPayRun.payslips && rawPayRun.payslips.length > 0) {
        
        // Try to find the payslips directly
        const directPayslips = await Payslip.find({
          _id: { $in: rawPayRun.payslips }
        }).populate({
          path: "employee",
          select: "firstName lastName employeeId company payFrequency doa bankDetails",
          populate: [
            {
              path: "company",
              populate: {
                path: "employerDetails",
              },
            },
            "payFrequency",
          ],
        });
        
        
        // If no results, try with Payroll model as fallback
        let finalPayslips = directPayslips;
        if (!directPayslips || directPayslips.length === 0) {
          try {
            // Check if Payroll model exists
            const Payroll = mongoose.models.Payroll || mongoose.model("Payroll");
            
            const payrolls = await Payroll.find({
              company: payRun.company,
              payPeriods: { 
                $elemMatch: {
                  startDate: { $lte: payRun.endDate },
                  endDate: { $gte: payRun.startDate }
                }
              }
            }).populate({
              path: "employee",
              select: "firstName lastName employeeId company payFrequency doa bankDetails",
              populate: [
                {
                  path: "company",
                  populate: {
                    path: "employerDetails",
                  },
                },
                "payFrequency",
              ],
            });
            
            if (payrolls && payrolls.length > 0) {
              // Filter out payrolls without employee data
              const validPayrolls = payrolls.filter(payroll => payroll.employee);
              
              if (validPayrolls.length > 0) {
                // Convert payrolls to a format compatible with payslips
                const payrollsAsPayslips = validPayrolls.map(payroll => {
                  // Find the relevant pay period
                  const relevantPeriod = payroll.payPeriods.find(period => 
                    period.startDate <= payRun.endDate && period.endDate >= payRun.startDate
                  );
                  
                  if (!relevantPeriod) return null;
                  
                  return {
                    _id: payroll._id,
                    employee: payroll.employee,
                    startDate: relevantPeriod.startDate,
                    endDate: relevantPeriod.endDate,
                    basicSalary: relevantPeriod.basicSalary || payroll.basicSalary || 0,
                    grossPay: relevantPeriod.total || payroll.grossPay || 0,
                    netPay: (relevantPeriod.total - (relevantPeriod.paye || 0) - (relevantPeriod.uif || 0)) || payroll.netPay || 0,
                    totalDeductions: ((relevantPeriod.paye || 0) + (relevantPeriod.uif || 0)) || payroll.totalDeductions || 0,
                    allowances: payroll.allowances || [],
                    deductions: payroll.deductions || [],
                    overtime: payroll.overtime || [],
                    medical: payroll.medical || {},
                    isFinalized: relevantPeriod.isFinalized || false
                  };
                }).filter(p => p !== null);
                
                
                if (payrollsAsPayslips.length > 0) {
                  finalPayslips = payrollsAsPayslips;
                } else {
                }
              } else {
              }
            } else {
            }
          } catch (fallbackError) {
            console.error("Error in Payroll model fallback:", fallbackError);
          }
          
          // If still no results, try with PayrollPeriod as final fallback
          if (!finalPayslips || finalPayslips.length === 0) {
            try {
              // Try to find PayrollPeriod directly using the IDs from the payRun
              const PayrollPeriod = mongoose.models.PayrollPeriod || mongoose.model("PayrollPeriod");
              
              // First try with the payslip IDs directly (they might actually be PayrollPeriod IDs)
              let payrollPeriods = await PayrollPeriod.find({
                _id: { $in: payRun.payrollPeriods || [] }
              }).populate({
                path: "employee",
                select: "firstName lastName employeeId company payFrequency doa bankDetails",
                populate: [
                  {
                    path: "company",
                    populate: {
                      path: "employerDetails",
                    },
                  },
                  "payFrequency",
                ],
              });
              
              // If that doesn't work, try by date range and company
              if (!payrollPeriods || payrollPeriods.length === 0) {
                payrollPeriods = await PayrollPeriod.find({
                  company: payRun.company,
                  startDate: { $lte: payRun.endDate },
                  endDate: { $gte: payRun.startDate }
                }).populate({
                  path: "employee",
                  select: "firstName lastName employeeId company payFrequency doa bankDetails",
                  populate: [
                    {
                      path: "company",
                      populate: {
                        path: "employerDetails",
                      },
                    },
                    "payFrequency",
                  ],
                });
              }
              
              
              if (payrollPeriods && payrollPeriods.length > 0) {
                // Check if any period has an employee that is just an ID reference and not populated
                for (let period of payrollPeriods) {
                  if (period.employee && typeof period.employee === 'string' || 
                      (period.employee && !period.employee.firstName)) {
                    try {
                      // Try to populate the employee data
                      const employee = await Employee.findById(period.employee).populate({
                        path: "company",
                        populate: {
                          path: "employerDetails",
                        },
                      });
                      
                      if (employee) {
                        period.employee = employee;
                      } else {
                      }
                    } catch (err) {
                      console.error(`Error fetching employee data: ${err.message}`);
                    }
                  }
                }
                
                // First, ensure all employee references are fully populated with company data
                for (const period of payrollPeriods) {
                  if (period.employee && period.employee._id) {
                    try {
                      // Make sure employee has company data populated
                      if (!period.employee.company || typeof period.employee.company === 'string') {
                        
                        // Get company ID from different sources
                        const companyId = period.company || 
                                         (typeof period.employee.company === 'string' ? period.employee.company : null);
                                         
                        if (companyId) {
                          const Company = mongoose.models.Company || mongoose.model("Company");
                          try {
                            const company = await Company.findById(companyId);
                            if (company) {
                              period.employee.company = company;
                            }
                          } catch (err) {
                            console.error(`Error finding company: ${err.message}`);
                          }
                        }
                      }
                    } catch (err) {
                      console.error(`Error populating company for period ${period._id}:`, err);
                    }
                  }
                }
                
                // Filter out periods without employee data
                const validPeriods = payrollPeriods.filter(period => {
                  if (!period.employee) {
                    return false;
                  }
                  return true;
                });
                
                if (validPeriods.length > 0) {
                  // Convert periods to a format compatible with payslips
                  const periodsAsPayslips = validPeriods.map(async period => {
                    try {
                      console.log(`PayrollPeriod employee data: ${JSON.stringify({
                        id: period.employee._id || 'Missing',
                        firstName: period.employee.firstName || 'Unknown',
                        lastName: period.employee.lastName || 'Unknown'
                      })}`);
                      
                      // Create deep copy of employee object to prevent reference issues
                      let employeeData;
                      try {
                        employeeData = JSON.parse(JSON.stringify(period.employee));
                        console.log(`Deep copied employee data: ${JSON.stringify(employeeData)}`);
                      } catch (copyError) {
                        console.error(`Error during deep copy of employee data: ${copyError.message}`);
                        // Use the original reference if deep copy failed
                        employeeData = period.employee;
                      }
                      
                      // Create a properly structured payslip with embedded employee data
                      const payslipFromPeriod = {
                        _id: period._id,
                        employee: employeeData,
                        startDate: period.startDate,
                        endDate: period.endDate,
                        basicSalary: period.basicSalary || 0,
                        grossPay: period.grossPay || 0,
                        netPay: period.netPay || 0,
                        totalDeductions: period.totalDeductions || 0,
                        allowances: period.allowances || [],
                        deductions: period.deductions || [],
                        overtime: period.overtime || [],
                        medical: period.medical || {},
                        isFinalized: period.isFinalized || false,
                        // Add tax-related fields if they exist
                        paye: period.PAYE || 0,
                        uif: period.UIF || 0,
                        sdl: period.SDL || 0
                      };
                      
                      // Extra verification to make sure employee data is present
                      if (!payslipFromPeriod.employee || !payslipFromPeriod.employee._id) {
                        // As a last resort, use the original employee reference if the deep copy failed
                        payslipFromPeriod.employee = period.employee;
                      }
                      
                      // Ensure company data is properly structured
                      if (payslipFromPeriod.employee && 
                          payslipFromPeriod.employee.company && 
                          typeof payslipFromPeriod.employee.company === 'string') {
                        try {
                          const Company = mongoose.models.Company || mongoose.model("Company");
                          const company = await Company.findById(payslipFromPeriod.employee.company);
                          if (company) {
                            payslipFromPeriod.employee.company = company;
                          }
                        } catch (err) {
                          console.error(`Error fetching company for payslip:`, err);
                        }
                      }
                      
                      return payslipFromPeriod;
                    } catch (mapError) {
                      console.error(`Error in payslip map function: ${mapError.message}`);
                      // Return a minimal valid payslip to prevent array holes
                      return {
                        _id: period._id || "error",
                        employee: period.employee || null,
                        startDate: period.startDate,
                        endDate: period.endDate
                      };
                    }
                  });
                  
                  // Wait for all payslips to be processed with their async operations
                  
                  try {
                    const resolvedPayslips = await Promise.allSettled(periodsAsPayslips);
                    
                    if (resolvedPayslips.length > 0) {
                      finalPayslips = resolvedPayslips.filter(p => p.status === 'fulfilled').map(p => p.value);
                    } else {
                    }
                  } catch (promiseErr) {
                    console.error("Error resolving payslip promises:", promiseErr);
                    // Fallback to directly using the array without waiting for promises
                    const directPayslips = periodsAsPayslips.map(promise => {
                      try {
                        if (promise.then) {
                          return {
                            _id: "unknown",
                            employee: null
                          };
                        }
                        return promise;
                      } catch (err) {
                        console.error("Error extracting from promise:", err);
                        return null;
                      }
                    }).filter(Boolean);
                    
                    if (directPayslips.length > 0) {
                      finalPayslips = directPayslips;
                    }
                  }
                } else {
                }
              } else {
              }
            } catch (periodError) {
              console.error("Error in PayrollPeriod fallback:", periodError);
            }
          }
        }
        
        if (finalPayslips && finalPayslips.length > 0) {
          
          // Extra verification of finalPayslips before assignment
          finalPayslips = finalPayslips.map(payslip => {
            // Make sure each payslip has employee data before proceeding
            if (!payslip.employee) {
              // Try to find the corresponding period to get employee data
              const matchingPeriod = payrollPeriods?.find(p => p._id.toString() === payslip._id.toString());
              if (matchingPeriod && matchingPeriod.employee) {
                payslip.employee = JSON.parse(JSON.stringify(matchingPeriod.employee));
              }
            }
            return payslip;
          });
          
          // Count payslips with valid employee data
          const validPayslips = finalPayslips.filter(p => p.employee && p.employee._id);
          
          // Instead of just assigning, create a deep copy to ensure references are properly preserved
          payRun.payslips = JSON.parse(JSON.stringify(finalPayslips));
          
          // Extra verification after assignment to ensure data integrity
          console.log(`Verifying payRun.payslips after assignment: ${
            payRun.payslips.filter(p => p.employee && p.employee._id).length
          }/${payRun.payslips.length} have valid employee data`);
        } else {
          return res.status(404).send("No valid payslips or payroll data found for this pay run after all fallback attempts");
        }
      } else {
        return res.status(404).send("No valid payslips or payroll data found for this pay run");
      }
    } // Add closing bracket for the if statement starting at line 839

    // Create a ZIP file containing all payslips
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Handle archive warnings
    archive.on('warning', function(err) {
      if (err.code === 'ENOENT') {
      } else {
        console.error('Archive error:', err);
        // Don't throw as this would crash the stream
      }
    });

    archive.on('error', function(err) {
      console.error('Archive error:', err);
      if (!res.headersSent) {
        res.status(500).send('Error creating archive: ' + err.message);
      }
    });

    // Set the headers with a safe filename
    const startDate = payRun.payPeriodStart ? payRun.payPeriodStart.toISOString().split('T')[0] : 
                      payRun.startDate ? payRun.startDate.toISOString().split('T')[0] : 
                      new Date().toISOString().split('T')[0];
    
    const endDate = payRun.payPeriodEnd ? payRun.payPeriodEnd.toISOString().split('T')[0] : 
                    payRun.endDate ? payRun.endDate.toISOString().split('T')[0] : 
                    new Date().toISOString().split('T')[0];
    
    res.attachment(`Payslips_${startDate}_to_${endDate}.zip`);
    archive.pipe(res);

    // Track successful and failed payslips
    let successCount = 0;
    let failureCount = 0;

    // Extra safety check - verify that payRun.payslips all have employee data
    console.log(`Before processing: ${
      payRun.payslips.filter(p => p && p.employee && p.employee._id).length
    }/${payRun.payslips.length} payslips have valid employee data`);
    
    // Generate each payslip and add to the archive
    for (const payslip of payRun.payslips) {
      try {
        // Last chance data recovery: if employee is missing for any reason, try to rebuild it
        if (!payslip.employee && payslip._id) {
          try {
            // Try to find the corresponding period to get employee data
            const matchingPeriod = payrollPeriods?.find(p => p._id.toString() === payslip._id.toString());
            if (matchingPeriod && matchingPeriod.employee) {
              payslip.employee = JSON.parse(JSON.stringify(matchingPeriod.employee));
            } else {
              // As a last resort, try with the Employee model directly
              const Employee = mongoose.models.Employee || mongoose.model("Employee");
              
              // Try to get employee ID from multiple sources
              const employeeId = payslip.employeeId || 
                               (payslip.employee && typeof payslip.employee === 'object' ? payslip.employee._id : null) ||
                               (payslip.employee && typeof payslip.employee === 'string' ? payslip.employee : null);
              
              if (employeeId) {
                // Extract ID string if it's an ObjectId
                const idString = typeof employeeId === 'object' && employeeId.toString ? 
                               employeeId.toString() : employeeId;
                
                const employee = await Employee.findById(idString).populate('company');
                if (employee) {
                  payslip.employee = JSON.parse(JSON.stringify(employee));
                } else {
                }
              } else {
              }
            }
          } catch (err) {
            console.error(`Error during emergency recovery: ${err.message}`);
          }
        }
        
        // Additional debug logging to trace the issue
        
        // Enhanced employee data validation
        if (!payslip.employee) {
          failureCount++;
          continue;
        }
        
        // Make sure employee has required fields
        if (!payslip.employee._id || !payslip.employee.firstName || !payslip.employee.lastName) {
          console.warn(`Skipping payslip with incomplete employee data: ${JSON.stringify({
            id: payslip.employee._id || 'Missing',
            firstName: payslip.employee.firstName || 'Unknown',
            lastName: payslip.employee.lastName || 'Unknown'
          })}`);
          failureCount++;
          continue;
        }

        // Enhanced logging for employee data debugging
        console.log(`Processing payslip ${payslip._id} for employee:`, {
          id: payslip.employee._id || 'Missing ID',
          firstName: payslip.employee.firstName || 'Unknown',
          lastName: payslip.employee.lastName || 'Unknown',
          hasCompany: payslip.employee.company ? 'Yes' : 'No'
        });
        
        // Check if employee has company data
        if (!payslip.employee.company) {
          
          try {
            // Try to find the company from the company ID if it exists
            if (payslip.company) {
              const Company = mongoose.models.Company || mongoose.model("Company");
              const company = await Company.findById(payslip.company);
              if (company) {
                payslip.employee.company = company;
              }
            }
            
            // If we still don't have company data, check if we have a companyId in the employee
            if (!payslip.employee.company && payslip.employee.company) {
              const Company = mongoose.models.Company || mongoose.model("Company");
              const company = await Company.findById(payslip.employee.company);
              if (company) {
                payslip.employee.company = company;
              }
            }
          } catch (companyError) {
            console.error(`Error fetching company for payslip ${payslip._id}:`, companyError);
          }
        }
        
        // Get calculations from PayrollService
        const calculations = await PayrollService.calculatePayrollTotals(
          payslip.employee._id,
          payslip.endDate || payRun.payPeriodEnd || payRun.endDate
        );
        
        console.log(`Got calculations for employee ${payslip.employee._id}:`, 
                    calculations ? 'Success' : 'Failed');

        // Destructure all needed values with fallbacks - Fixed data structure mapping
        const {
          totalIncome = calculations?.totalIncome || payslip.basicSalary || payslip.grossPay || 0,
          basicSalary = calculations?.basicSalary || payslip.basicSalary || 0,
          deductions: {
            statutory: { paye = 0, uif = 0 } = {},
            courtOrders: { maintenanceOrder = 0 } = {},
            total: totalDeductions = payslip.totalDeductions || 0
          } = { statutory: {}, courtOrders: {} },
          netPay = payslip.netPay || (totalIncome - totalDeductions) || 0,
        } = calculations || {};

        // Extract basic salary value properly (handle object structure)
        const basicSalaryValue = typeof basicSalary === 'object'
          ? (basicSalary.full || basicSalary.prorated || 0)
          : (basicSalary || 0);

        // Ensure totalIncome is never undefined - add fallback
        const safeTotalIncome = totalIncome || basicSalaryValue || 0;

        console.log(`Financial data (CORRECTED): BasicSalary=${basicSalaryValue}, Income=${safeTotalIncome}, Deductions=${totalDeductions}, Net=${netPay}`);

        // Calculate SDL (1% of total income) with safe value
        const sdl = safeTotalIncome * 0.01;

        // Calculate medical aid credit using safe total income
        const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
          payslip.medical || {},
          payslip.employee.payFrequency?.frequency || 'monthly',
          safeTotalIncome
        );

        const medicalAidCredit = medicalAidImpact?.taxCredit || 0;

        // Format dates consistently with fallbacks
        const formattedStartDate = moment(payslip.startDate || payRun.startDate || payRun.payPeriodStart || new Date()).format(
          "DD/MM/YYYY"
        );
        const formattedEndDate = moment(payslip.endDate || payRun.endDate || payRun.payPeriodEnd || new Date()).format("DD/MM/YYYY");
        const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;
        
        // Create PDF document
        const pdfDoc = await PDFDocument.create();
        const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
        const { width, height } = page.getSize();

        // Load fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaBoldFont = await pdfDoc.embedFont(
          StandardFonts.HelveticaBold
        );

        // Define helper functions for this payslip
        const drawText = (text, x, y, options = {}) => {
          const {
            font = helveticaFont,
            size = 10,
            color = rgb(0, 0, 0),
            align = "left",
          } = options;

          const textWidth = font.widthOfTextAtSize(text, size);
          let xPos = x;

          if (align === "right") {
            xPos = x - textWidth;
          } else if (align === "center") {
            xPos = x - textWidth / 2;
          }

          page.drawText(text || "", {
            x: xPos,
            y,
            size,
            font,
            color,
          });
        };

        // Helper function to draw text with more space between label and amount
        const drawTextWithSpace = (label, amount, x, y, options = {}) => {
          drawText(label, x, y, options);
          drawText(amount, x + 250, y, { ...options, align: "right" });
        };

        // Helper function to format currency safely
        const formatCurrency = (amount) => {
          if (amount === undefined || amount === null) {
            return "R0.00";
          }
          return new Intl.NumberFormat("en-ZA", {
            style: "currency",
            currency: "ZAR",
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(amount);
        };

        // Company details
        const companyName = payslip.employee.company?.name || "Company Name";
        const companyAddress = payslip.employee.company?.address || "Company Address";
        const companyRegistration = payslip.employee.company?.registrationNumber || "";
        const companyLogo = payslip.employee.company?.logo;
        

        // Employee details
        const employeeName = `${payslip.employee.firstName || ""} ${payslip.employee.lastName || ""}`.trim() || "Employee Name";
        const employeeId = payslip.employee.employeeId || "Employee ID";
        const employeeDoa = payslip.employee.doa
          ? moment(payslip.employee.doa).format("DD/MM/YYYY")
          : "N/A";
          

        // Set up the document structure
        const margin = 40;
        const tableCol1 = margin;
        const tableCol2 = width - margin;
        const headerHeight = height - margin;

        // Add company logo if it exists
        if (companyLogo) {
          try {
            const logoPath = path.join(__dirname, "..", companyLogo);
            const logoFile = await fs.readFile(logoPath);
            const logoImage = await pdfDoc.embedPng(logoFile);
            const logoWidth = 100;
            const logoHeight = (logoWidth / logoImage.width) * logoImage.height;
            page.drawImage(logoImage, {
              x: margin,
              y: height - 20 - logoHeight, // Raised higher on the page
              width: logoWidth,
              height: logoHeight,
            });
          } catch (error) {
            console.error("Error loading logo:", error);
          }
        }

        // Company name (centered, 8pt)
        drawText(
          companyName,
          width / 2,
          height - 50,
          { font: helveticaBoldFont, size: 8, align: "center" }
        );

        // Company address (right-aligned)
        const addressY = height - 100;
        const cityTown = payslip.employee.company?.physicalAddress?.cityTown || "City";
        const postalCode = payslip.employee.company?.physicalAddress?.code || "Postal Code";
        
        drawText(
          cityTown,
          width - 50,
          addressY,
          { align: "right" }
        );
        drawText(
          postalCode,
          width - 50,
          addressY - 15,
          { align: "right" }
        );

        // Update the period display based on frequency
        drawText(`Period: ${periodDisplay}`, 50, height - 145, {
          font: helveticaBoldFont,
          size: 8,
        });

        // Update employee details section with period-specific information
        const startY = height - 100;
        drawText(
          `Employee Name: ${employeeName}`,
          50,
          startY
        );
        drawText(
          `Employee ID: ${employeeId}`,
          50,
          startY - 15
        );
        drawText(
          `Date of Appointment: ${employeeDoa}`,
          50,
          startY - 30
        );

        // Horizontal line under employee details
        const lineY = startY - 50;
        page.drawLine({
          start: { x: 50, y: lineY },
          end: { x: width - 50, y: lineY },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        // Create a table for income, deductions, and employer contributions
        const tableStartY = lineY - 20;
        const lineHeight = 20;
        const tableCol3 = 350;
        const tableCol4 = 450;

        // Headers (all bold headers now 8pt)
        drawText("Income", tableCol1, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawText("Employer Contribution", tableCol3, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });

        // Income - Using corrected values
        drawText("Basic Salary", tableCol1, tableStartY - lineHeight);
        drawTextWithSpace(
          "Basic Salary",
          safeTotalIncome,
          tableCol1,
          tableStartY - lineHeight
        );

        // Total Income

        drawText(
          formatCurrency(safeTotalIncome),
          tableCol2 - 8,
          tableStartY - 2 * lineHeight,
          { font: helveticaBoldFont, size: 8, align: "right" }
        );

        // Deductions
        drawText("Deduction", tableCol1, tableStartY - 3 * lineHeight, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawText("Medical Aid", tableCol1, tableStartY - 4 * lineHeight);
        drawText(
          formatCurrency(payslip.medical?.medicalAid || 0),
          tableCol2 - 8,
          tableStartY - 4 * lineHeight,
          { align: "right" }
        );
        drawText("UIF", tableCol1, tableStartY - 5 * lineHeight);
        drawTextWithSpace("UIF", uif, tableCol1, tableStartY - 5 * lineHeight);
        drawText("PAYE", tableCol1, tableStartY - 6 * lineHeight);
        drawTextWithSpace("PAYE", paye, tableCol1, tableStartY - 6 * lineHeight);

        // Total Deductions
        drawText("Total Deductions:", tableCol1, tableStartY - 7 * lineHeight, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawTextWithSpace(
          "Total Deductions:",
          totalDeductions,
          tableCol1,
          tableStartY - 7 * lineHeight,
          { font: helveticaBoldFont, size: 8 }
        );

        // Employer Contributions
        drawText("Employer Contribution", tableCol3, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawTextWithSpace("SDL", sdl, tableCol3, tableStartY - lineHeight);
        drawTextWithSpace(
          "UIF - Employer",
          uif,
          tableCol3,
          tableStartY - 2 * lineHeight
        );

        // Tax Credit
        drawText("Tax Credit", tableCol3, tableStartY - 4 * lineHeight, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawTextWithSpace(
          "Medical Aid Tax Credit",
          medicalAidCredit,
          tableCol3,
          tableStartY - 5 * lineHeight
        );

        // NETT PAY (moved to bottom of the page)
        const nettPayY = 50; // Adjust this value to position it at the bottom

        page.drawLine({
          start: { x: 50, y: nettPayY + 25 },
          end: { x: 545, y: nettPayY + 25 },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        drawText("NETT PAY", 50, nettPayY, { font: helveticaBoldFont, size: 8 });
        drawTextWithSpace("NETT PAY", netPay, 50, nettPayY, {
          font: helveticaBoldFont,
          size: 8,
        });

        page.drawLine({
          start: { x: 50, y: nettPayY - 5 },
          end: { x: 545, y: nettPayY - 5 },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        // Add finalization status to the footer
        const statusText = payslip.isFinalized
          ? "FINALIZED PAYSLIP"
          : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";

        drawText(statusText, width / 2, 30, {
          font: helveticaBoldFont,
          size: 8,
          color: payslip.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
          align: "center",
        });

        // Draw the payslip period box
        const periodY = height - 150;
        page.drawRectangle({
          x: 50,
          y: periodY - 25,
          width: width - 100,
          height: 40,
          borderColor: rgb(0.7, 0.7, 0.7),
          borderWidth: 1,
        });

        drawText(
          "PAYSLIP",
          width / 2,
          periodY,
          { font: helveticaBoldFont, size: 14, align: "center" }
        );

        drawText(
          `Period: ${periodDisplay}`,
          width / 2,
          periodY - 15,
          { size: 10, align: "center" }
        );

        // Draw earnings section
        const earningsY = periodY - 70;
        drawText(
          "EARNINGS",
          50,
          earningsY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under earnings title
        page.drawLine({
          start: { x: 50, y: earningsY - 5 },
          end: { x: width - 50, y: earningsY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // Draw earnings items
        let currentY = earningsY - 25;

        // Basic salary - Using corrected values
        const basicSalaryForDisplay = basicSalaryValue || payslip.basicSalary || payslip.grossPay || safeTotalIncome || 0;
        drawTextWithSpace(
          "Basic Salary",
          formatCurrency(basicSalaryForDisplay),
          50,
          currentY
        );
        currentY -= 15;

        // Add allowances if they exist
        if (payslip.allowances && payslip.allowances.length > 0) {
          for (const allowance of payslip.allowances) {
            if (allowance.amount > 0) {
              drawTextWithSpace(
                allowance.name || "Allowance",
                formatCurrency(allowance.amount),
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add overtime if it exists
        if (payslip.overtime && payslip.overtime.length > 0) {
          for (const ot of payslip.overtime) {
            if (ot.amount > 0) {
              drawTextWithSpace(
                `Overtime (${ot.hours || 0} hours)`,
                formatCurrency(ot.amount),
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add total earnings
        currentY -= 10;
        page.drawLine({
          start: { x: 50, y: currentY + 5 },
          end: { x: width - 50, y: currentY + 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        drawTextWithSpace(
          "Total Earnings",
          formatCurrency(safeTotalIncome),
          50,
          currentY,
          { font: helveticaBoldFont }
        );

        // Draw deductions section
        const deductionsY = currentY - 40;
        drawText(
          "DEDUCTIONS",
          50,
          deductionsY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under deductions title
        page.drawLine({
          start: { x: 50, y: deductionsY - 5 },
          end: { x: width - 50, y: deductionsY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // Draw deductions items
        currentY = deductionsY - 25;

        // PAYE
        drawTextWithSpace("PAYE", formatCurrency(paye), 50, currentY);
        currentY -= 15;

        // UIF
        drawTextWithSpace("UIF", formatCurrency(uif), 50, currentY);
        currentY -= 15;

        // SDL
        drawTextWithSpace("SDL", formatCurrency(sdl), 50, currentY);
        currentY -= 15;

        // Add medical aid if it exists
        if (payslip.medical && payslip.medical.amount > 0) {
          drawTextWithSpace(
            "Medical Aid",
            formatCurrency(payslip.medical.amount || 0),
            50,
            currentY
          );
          currentY -= 15;

          // Medical aid tax credit
          drawTextWithSpace(
            "Medical Aid Tax Credit",
            formatCurrency(-medicalAidCredit),
            50,
            currentY
          );
          currentY -= 15;
        }

        // Add deductions if they exist
        if (payslip.deductions && payslip.deductions.length > 0) {
          for (const deduction of payslip.deductions) {
            if (deduction.amount > 0) {
              drawTextWithSpace(
                deduction.name || "Deduction",
                formatCurrency(deduction.amount),
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add maintenance order if it exists
        if (maintenanceOrder > 0) {
          drawTextWithSpace(
            "Maintenance Order",
            formatCurrency(maintenanceOrder),
            50,
            currentY
          );
          currentY -= 15;
        }

        // Add total deductions
        currentY -= 10;
        page.drawLine({
          start: { x: 50, y: currentY + 5 },
          end: { x: width - 50, y: currentY + 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        drawTextWithSpace(
          "Total Deductions",
          formatCurrency(totalDeductions),
          50,
          currentY,
          { font: helveticaBoldFont }
        );

        // Draw net pay section
        const netPayY = currentY - 40;
        drawText(
          "NET PAY",
          50,
          netPayY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under net pay title
        page.drawLine({
          start: { x: 50, y: netPayY - 5 },
          end: { x: width - 50, y: netPayY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // Draw net pay amount
        drawTextWithSpace(
          "Net Pay",
          formatCurrency(netPay),
          50,
          netPayY - 25,
          { font: helveticaBoldFont, size: 12 }
        );

        // Add bank details if they exist
        const bankDetailsY = netPayY - 70;
        if (payslip.employee.bankDetails) {
          drawText(
            "BANK DETAILS",
            50,
            bankDetailsY,
            { font: helveticaBoldFont, size: 12 }
          );

          // Draw line under bank details title
          page.drawLine({
            start: { x: 50, y: bankDetailsY - 5 },
            end: { x: width - 50, y: bankDetailsY - 5 },
            thickness: 1,
            color: rgb(0.7, 0.7, 0.7),
          });

          currentY = bankDetailsY - 25;
          const bankDetails = payslip.employee.bankDetails;

          drawText(`Bank: ${bankDetails.bankName || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Account Number: ${bankDetails.accountNumber || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Account Type: ${bankDetails.accountType || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Branch Code: ${bankDetails.branchCode || "Not specified"}`, 50, currentY);
        }

        // Add footer with company registration
        const footerY = 50;
        if (companyRegistration) {
          drawText(
            `Company Registration: ${companyRegistration}`,
            width / 2,
            footerY,
            { size: 8, align: "center" }
          );
        }

        // Add DRAFT watermark if period is not finalized
        if (!payslip.isFinalized) {
          // Draw diagonal DRAFT text
          const draftText = "DRAFT";
          const fontSize = 60;
          const draftWidth = helveticaBoldFont.widthOfTextAtSize(
            draftText,
            fontSize
          );

          page.drawText(draftText, {
            x: (width - draftWidth) / 2,
            y: height / 2,
            font: helveticaBoldFont,
            size: fontSize,
            color: rgb(0.9, 0.3, 0.3), // Light red color
            opacity: 0.3, // Semi-transparent
            rotate: {
              type: "degrees",
              angle: 45,
              origin: {
                x: width / 2,
                y: height / 2,
              },
            },
          });

          // Add draft notice at the top
          const noticeText = "This is a draft payslip - Not finalized";
          page.drawText(noticeText, {
            x: 50,
            y: height - 30,
            font: helveticaBoldFont,
            size: 10,
            color: rgb(0.9, 0.3, 0.3),
          });
        }

        // Serialize the PDF
        const pdfBytes = await pdfDoc.save();
        

        // Add the PDF to the archive
        const filename = `Payslip_${employeeName.replace(/\s+/g, "_")}_${formattedEndDate.replace(/\//g, "-")}.pdf`;
        archive.append(Buffer.from(pdfBytes), { name: filename });
        successCount++;
      } catch (error) {
        console.error(`Error generating payslip PDF: ${error.message}`, error);
        console.error(`Failed payslip details:`, {
          employeeId: payslip.employee?._id,
          employeeName: payslip.employee ? `${payslip.employee.firstName || ''} ${payslip.employee.lastName || ''}` : 'Unknown',
          payslipId: payslip._id
        });
        failureCount++;
      }
    }
    // Finalize the archive
    await archive.finalize();
    
  } catch (error) {
    console.error("Error generating bulk payslips:", error);
    
    // Only send error response if headers haven't been sent yet
    if (!res.headersSent) {
      res.status(500).send(`Error generating payslips: ${error.message}`);
    } else {
      // If headers were already sent, we can't send a proper error response
      // Just end the response to prevent hanging connections
      res.end();
    }
  }
});

// Helper function to try the Payroll model as a fallback
async function tryPayrollFallback(payRun, res) {
  try {
    const Payroll = mongoose.models.Payroll || mongoose.model("Payroll");
    const payrolls = await Payroll.find({
      company: payRun.company,
      payPeriods: { 
        $elemMatch: {
          startDate: { $lte: payRun.endDate },
          endDate: { $gte: payRun.startDate }
        }
      }
    }).populate({
      path: "employee",
      select: "firstName lastName employeeId company payFrequency doa bankDetails",
      populate: [
        {
          path: "company",
          populate: {
            path: "employerDetails",
          },
        },
        "payFrequency",
      ],
    });
    
    
    if (payrolls && payrolls.length > 0) {
      // Filter out payrolls without employee data
      const validPayrolls = payrolls.filter(payroll => payroll.employee);
      
      if (validPayrolls.length > 0) {
        // Convert payrolls to a format compatible with payslips
        const payrollsAsPayslips = validPayrolls.map(payroll => {
          // Find the relevant pay period
          const relevantPeriod = payroll.payPeriods.find(period => 
            period.startDate <= payRun.endDate && period.endDate >= payRun.startDate
          );
          
          if (!relevantPeriod) return null;
          
          return {
            _id: payroll._id,
            employee: payroll.employee,
            startDate: relevantPeriod.startDate,
            endDate: relevantPeriod.endDate,
            basicSalary: relevantPeriod.basicSalary || payroll.basicSalary || 0,
            grossPay: relevantPeriod.total || payroll.grossPay || 0,
            netPay: (relevantPeriod.total - (relevantPeriod.paye || 0) - (relevantPeriod.uif || 0)) || payroll.netPay || 0,
            totalDeductions: ((relevantPeriod.paye || 0) + (relevantPeriod.uif || 0)) || payroll.totalDeductions || 0,
            allowances: payroll.allowances || [],
            deductions: payroll.deductions || [],
            overtime: payroll.overtime || [],
            medical: payroll.medical || {},
            isFinalized: relevantPeriod.isFinalized || false
          };
        }).filter(p => p !== null);
        
        
        if (payrollsAsPayslips.length > 0) {
          payRun.payslips = payrollsAsPayslips;
          return { success: true, payRun };
        }
      }
    }
    
    return { success: false };
  } catch (error) {
    console.error("Error in tryPayrollFallback:", error);
    return { success: false, error };
  }
}

module.exports = router;
