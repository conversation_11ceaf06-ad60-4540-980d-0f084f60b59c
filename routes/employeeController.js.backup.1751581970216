const Employee = require("../models/Employee");

// Example route handler to fetch and display latest paid month
exports.displayLatestPaidMonth = async (req, res) => {
  try {
    // Assuming you are fetching the latest paid month for a specific employee
    const employeeId = req.params.employeeId;

    // Find the employee by ID
    const employee = await Employee.findById(employeeId);

    // Assuming you have an array of paid months and you want to get the latest one
    const latestPaidMonth =
      employee.paidMonths.length > 0
        ? employee.paidMonths[employee.paidMonths.length - 1]
        : null;

    // Render the view with the latest paid month
    res.render("employeeProfile", {
      employee,
      latestPaidMonth: latestPaidMonth ? latestPaidMonth.toISOString() : null,
    });
  } catch (err) {
    console.error(err);
    res.status(500).send("Internal Server Error");
  }
};

// Update employee basic information
exports.updateEmployeeBasicInfo = async (req, res) => {
    try {
        const employeeId = req.params.id;
        const updates = req.body;
        
        // Validate OID eligibility data
        if (typeof updates.oidEligible === 'boolean' && !updates.oidEligible && !updates.oidExemptReason) {
            return res.status(400).json({
                success: false,
                message: 'OID exempt reason is required when employee is not eligible'
            });
        }

        // If employee is OID eligible, clear the exempt reason
        if (updates.oidEligible === true) {
            updates.oidExemptReason = null;
        }

        const employee = await Employee.findOneAndUpdate(
            { _id: employeeId, company: req.user.currentCompany },
            { 
                $set: {
                    ...updates,
                    lastModified: new Date(),
                    modifiedBy: req.user._id
                }
            },
            { new: true, runValidators: true }
        );

        if (!employee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found'
            });
        }

        res.json({
            success: true,
            message: 'Employee information updated successfully',
            employee: employee
        });
    } catch (error) {
        console.error('Error updating employee:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating employee information'
        });
    }
};
