const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { isVercelEnv, getMongoConnectionState } = require('../../utils/mongoDbUtils');

/**
 * Route to provide connection diagnostics
 */
router.get('/', (req, res) => {
    const connectionState = getMongoConnectionState();
    const mongoConnected = mongoose.connection.readyState === 1;
    
    res.json({
        success: true,
        timestamp: new Date().toISOString(),
        mongoConnected,
        connectionState,
        mongoHost: process.env.MONGODB_URI 
            ? process.env.MONGODB_URI.split('@')[1]?.split('/')[0] || 'Hidden for security'
            : 'Not configured',
        environment: {
            NODE_ENV: process.env.NODE_ENV || 'development',
            VERCEL: isVercelEnv()
        }
    });
});

/**
 * Test MongoDB connection and return detailed results
 */
router.get('/test-mongo-connection', async (req, res) => {
    // Capture logs
    const logs = [];
    let startTime = Date.now();
    let success = false;
    let error = null;
    let details = null;

    try {
        logs.push(`[${new Date().toISOString()}] MongoDB connection state: ${getMongoConnectionState()}`);
        
        if (mongoose.connection.readyState !== 1) {
            logs.push(`[${new Date().toISOString()}] Attempting to establish connection...`);
            
            // If not connected, try to connect
            if (!process.env.MONGODB_URI) {
                throw new Error('MONGODB_URI environment variable is not set');
            }
            
            logs.push(`[${new Date().toISOString()}] Connection URI available: ${process.env.MONGODB_URI.split('@')[0]}...`);
            
            // Try to connect with a short timeout
            await Promise.race([
                mongoose.connect(process.env.MONGODB_URI, {
                    useNewUrlParser: true,
                    useUnifiedTopology: true
                }),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Connection timeout after 5 seconds')), 5000)
                )
            ]);
        }
        
        // Test the connection
        logs.push(`[${new Date().toISOString()}] Testing connection with ping...`);
        const adminDb = mongoose.connection.db.admin();
        const pingResult = await adminDb.ping();
        
        logs.push(`[${new Date().toISOString()}] Ping result: ${JSON.stringify(pingResult)}`);
        
        // Get server info
        const serverInfo = await adminDb.serverInfo();
        logs.push(`[${new Date().toISOString()}] Server info: MongoDB v${serverInfo.version}`);
        
        success = true;
    } catch (err) {
        error = err.message;
        details = err.stack;
        logs.push(`[${new Date().toISOString()}] Error: ${err.message}`);
        
        // Check if we can get more details from the error
        if (err.name === 'MongoNetworkError') {
            logs.push(`[${new Date().toISOString()}] Network error detected. This usually indicates connectivity issues.`);
        } else if (err.name === 'MongoServerSelectionError') {
            logs.push(`[${new Date().toISOString()}] Server selection error. This often means the server is unavailable.`);
        }
    }
    
    const connectionTime = Date.now() - startTime;
    logs.push(`[${new Date().toISOString()}] Test completed in ${connectionTime}ms`);
    
    // Send the results
    res.json({
        success,
        connectionTime,
        error,
        details,
        host: process.env.MONGODB_URI 
            ? process.env.MONGODB_URI.split('@')[1]?.split('/')[0] || 'Hidden for security'
            : 'Not configured',
        logs: logs.join('\n')
    });
});

module.exports = router; 