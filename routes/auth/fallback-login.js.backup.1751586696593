const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { isVercelEnv } = require('../../utils/mongoDbUtils');
const Logger = require('../../utils/Logger');

// Initialize logger
const logger = new Logger('fallback-login');

// In-memory user store for Vercel environment
// WARNING: This is only for development and testing!
// Reset on each deployment/restart
const FALLBACK_USERS = [
    {
        id: 'admin-fallback-id',
        email: '<EMAIL>',
        // Password: admin123 (hashed)
        password: '$2a$10$mLK.rrdlvx9DCFb6Eck1t.TlltnGulepXnov3bBp5T2TloO1MYj52',
        name: 'Admin User',
        role: 'admin',
        tenantId: 'default-tenant'
    },
    {
        id: 'user-fallback-id',
        email: '<EMAIL>',
        // Password: user123 (hashed)
        password: '$2a$10$rrTtJH.7QXoQdZm5vUkG5O41RPTxXMJJhs3RuqBq9xK110PTJLnsi',
        name: 'Regular User',
        role: 'user',
        tenantId: 'default-tenant'
    }
];

/**
 * Helper function to get JWT cookie options
 */
function getCookieOptions() {
    return {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        path: '/'
    };
}

/**
 * Fallback login route for Vercel environment
 * This is used when MongoDB is not available
 */
router.post('/', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ 
                success: false, 
                message: 'Email and password are required' 
            });
        }

        // Log login attempt (redact password)
        logger.info(`Fallback login attempt for email: ${email}`);
        logger.debug(`Request headers: ${JSON.stringify(req.headers)}`);
        
        // Find user from in-memory store
        const user = FALLBACK_USERS.find(u => u.email.toLowerCase() === email.toLowerCase());
        
        if (!user) {
            logger.warn(`Fallback login failed: User not found: ${email}`);
            return res.status(401).json({ 
                success: false, 
                message: 'Invalid credentials' 
            });
        }
        
        // Verify password
        const passwordMatch = await bcrypt.compare(password, user.password);
        if (!passwordMatch) {
            logger.warn(`Fallback login failed: Invalid password for: ${email}`);
            return res.status(401).json({ 
                success: false, 
                message: 'Invalid credentials' 
            });
        }
        
        // Create user object without password
        const userForToken = {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            tenantId: user.tenantId,
            isFallbackUser: true // Flag to indicate this is a fallback user
        };
        
        // Generate JWT
        const token = jwt.sign(userForToken, process.env.JWT_SECRET, {
            expiresIn: '7d'
        });
        
        // Set HTTP-only cookie
        res.cookie('auth_token', token, getCookieOptions());
        
        logger.info(`Fallback login successful for: ${email}`);
        logger.debug(`JWT issued for user: ${user.id}`);
        
        // Return success with user info
        return res.status(200).json({
            success: true,
            message: 'Login successful',
            token, // Include token in response body for non-cookie clients
            user: userForToken
        });
    } catch (error) {
        logger.error(`Fallback login error: ${error.message}`, error);
        res.status(500).json({ 
            success: false, 
            message: 'An error occurred during login',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

module.exports = router; 