const express = require("express");
const mongoose = require("mongoose");
const router = express.Router();
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Employee = require("../models/Employee");
const EmployerDetails = require("../models/employerDetails");
const EFTDetails = require("../models/eftDetails");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const Company = require("../models/Company");
const checkCompanyMiddleware = require("../middleware/checkCompany");
const User = require("../models/user");
const Beneficiary = require("../models/beneficiaries");
const EmployerFilingDetails = require("../models/employerFilingDetails");
const PayFrequency = require("../models/PayFrequency"); // Add this line
const PayPoint = require("../models/PayPoint");
const Template = require("../models/Template");
const AdvancedSettings = require("../models/AdvancedSettings");
const PayslipSettings = require("../models/PayslipSettings");
const CustomItem = require("../models/CustomItem");
const Integration = require("../models/Integration");
const xeroRouter = require("./xero");
const csrfProtection = require("../middleware/csrfProtection");
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const JobGrade = require("../models/JobGrade");
const { checkFeatureAccess } = require('../middleware/featureAccess');
const WhatsAppRequestLog = require('../models/WhatsAppRequestLog');
const whatsappService = require('../services/whatsappService');

// Add CSRF error handler middleware
router.use((err, req, res, next) => {
  if (err && err.code === 'EBADCSRFTOKEN') {
    console.error('\n=== CSRF Error ===');
    console.error('Invalid CSRF token:', err);
    console.error('Path:', req.path);
    console.error('Method:', req.method);
    console.error('Headers:', req.headers);
    
    // Flash an error message and redirect
    req.flash('error', 'Security validation failed. Please try again.');
    
    // Try to extract company code from the URL
    const companyCodeMatch = req.path.match(/\/([^\/]+)\/settings/);
    const companyCode = companyCodeMatch ? companyCodeMatch[1] : null;
    
    if (companyCode) {
      return res.redirect(`/clients/${companyCode}/settings/employee`);
    } else {
      return res.redirect('/clients');
    }
  }
  next(err);
});

// Mount Xero integration routes
router.use("/:companyCode/settings/accounting/xero", xeroRouter);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "..", "uploads");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname)
    );
  },
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error("Only .png, .jpg and .jpeg format allowed!"));
    }
  },
});

// Apply middleware to all routes
router.use(ensureAuthenticated);
router.use(checkCompanyMiddleware);

// Add feature flag protection to other settings routes
router.use('/other*', checkFeatureAccess('OTHER_SETTINGS'));

// Protect specific payroll settings routes
router.use('/payroll/pay-points*', checkFeatureAccess('PAY_POINTS_SETTINGS'));
router.use('/payroll/payroll-calculations*', checkFeatureAccess('PAYROLL_CALCULATIONS_SETTINGS'));
router.use('/payroll/payslip*', checkFeatureAccess('PAYSLIP_SETTINGS'));

// Protect specific accounting settings routes
router.use('/accounting/beneficiaries*', checkFeatureAccess('BENEFICIARIES_SETTINGS'));
router.use('/accounting/accounting-splits*', checkFeatureAccess('ACCOUNTING_SPLITS'));

// Protect specific employee settings routes
router.use('/employee/job-grades*', checkFeatureAccess('JOB_GRADES_SETTINGS'));

// Protect OID Return routes
router.use('/oid-return*', checkFeatureAccess('OID_RETURN'));

// Protect bulk action routes
router.use('/employeeManagement/end-service*', checkFeatureAccess('BULK_EMPLOYEE_ACTIONS'));
router.use('/employeeManagement/reinstate*', checkFeatureAccess('BULK_EMPLOYEE_ACTIONS'));
router.use('/employeeManagement/undo-end-of-service*', checkFeatureAccess('BULK_EMPLOYEE_ACTIONS'));
router.use('/employeeManagement/essentials*', checkFeatureAccess('BULK_INFORMATION_INPUTS'));
router.use('/employeeManagement/payment-banking-info*', checkFeatureAccess('BULK_INFORMATION_INPUTS'));
router.use('/employeeManagement/payslip-inputs*', checkFeatureAccess('BULK_PAYROLL_INPUTS'));

// Add this POST route handler - make sure it's not commented out
// Update the route handler to match the exact URL path
// Update the route handler to match the exact URL path
router.post(
  "/:companyCode/settings/employee/employer-details",
  upload.single("logo"),
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const {
        tradingName,
        unitNumber,
        complex,
        streetNumber,
        street,
        suburbDistrict,
        cityTown,
        code,
        postalLine1,
        postalLine2,
      } = req.body;

      const employerDetailsData = {
        company: company._id,
        tradingName,
        physicalAddress: {
          unitNumber,
          complex,
          streetNumber,
          street,
          suburbDistrict,
          cityTown,
          code,
        },
        postalAddress: {
          line1: postalLine1,
          line2: postalLine2,
        },
      };

      if (req.file) {
        employerDetailsData.logo = req.file.path;
      }

      // Use findOneAndUpdate with upsert
      const employerDetails = await EmployerDetails.findOneAndUpdate(
        { company: company._id },
        employerDetailsData,
        {
          new: true,
          upsert: true,
          runValidators: true,
        }
      );

      // Return JSON response
      return res.json({
        success: true,
        message: "Employer details saved successfully",
        data: employerDetails,
      });
    } catch (error) {
      console.error("Error saving employer details:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to save employer details",
        error: error.message,
      });
    }
  }
);
// Add this route at the beginning of your routes
router.get(
  "/:companyCode/settings/employee/employer-details",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { newCompany } = req.query;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      let employerDetails = await EmployerDetails.findOne({
        company: company._id,
      });

      // If it's a new company or no employer details exist, create an empty object
      if (newCompany === "true" || !employerDetails) {
        employerDetails = {
          tradingName: "",
          physicalAddress: {
            unitNumber: "",
            complex: "",
            streetNumber: "",
            street: "",
            suburbDistrict: "",
            cityTown: "",
            code: "",
          },
          postalAddress: {
            line1: "",
            line2: "",
          },
          logo: "",
        };
      }

      const isNewCompany = newCompany === "true";

      res.render("settings/employer-details", {
        company,
        employerDetails,
        isNewCompany,
        newCompany: isNewCompany,
        activeTab: "employee",
        activeSubTab: "employer-details",
        messages: req.flash(), // Add flash messages
        csrfToken: req.csrfToken(), // Add CSRF token
        req: req,
      });
    } catch (error) {
      console.error("Error rendering employer details:", error);
      res
        .status(500)
        .render("error", { message: "Error loading employer details" });
    }
  }
);

// Add this new route
router.get("/:companyCode/settings", ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;
    const company = await Company.findOne({ companyCode });

    if (!company) {
      return res.status(404).render("error", { message: "Company not found" });
    }

    // Check if the user has access to this company by comparing ObjectIds as strings
    const hasAccess = req.user.companies.some(c => 
      (typeof c === 'object' ? c._id.toString() : c.toString()) === company._id.toString()
    );

    if (!hasAccess) {
      return res
        .status(403)
        .render("error", { message: "Unauthorized access to this company" });
    }

    // Set the current company for the user
    req.user.currentCompany = company;

    // Fetch any necessary data for the settings overview
    const employerDetails = await EmployerDetails.findOne({
      company: company._id,
    });
    const employeeNumberSettings = await EmployeeNumberSettings.findOne({
      company: company._id,
    });
    const eftDetails = await EFTDetails.findOne({ company: company._id });
    const payslipSettings = await PayslipSettings.findOne({
      company: company._id,
    });

    res.render("settings/index", {
      company,
      employerDetails,
      employeeNumberSettings,
      eftDetails,
      payslipSettings,
      csrfToken: req.csrfToken(),
      messages: req.flash()
    });
  } catch (error) {
    console.error("Error loading settings page:", error);
    res.status(500).render("error", { message: "Error loading settings page" });
  }
});

// router.get("/", ensureAuthenticated, async (req, res) => {
//   try {
//     const activeTab = req.query.tab || "employerDetails";
//     const isNewCompany = req.query.isNewCompany === "true";
//     const companyId = req.query.companyId || req.user.currentCompany;

//     // Fetch the user with populated companies
//     const user = await User.findById(req.user._id).populate("companies");

//     let company = await Company.findById(companyId);
//     if (!company) {
//       return res.status(404).render("error", { message: "Company not found" });
//     }

//     let employerDetails = await EmployerDetails.findOne({ company: companyId });
//     if (!employerDetails) {
//       employerDetails = new EmployerDetails({ company: companyId });
//     }

//     const employeeNumberSettings = await EmployeeNumberSettings.findOne({
//       company: companyId,
//     });

//     const eftDetails = await EFTDetails.findOne({
//       company: companyId,
//     });

//     const payslipSettings = await PayslipSettings.findOne({
//       company: companyId,
//     });

//     res.render("settings", {
//       activeTab: activeTab,
//       user: user, // Use the populated user object
//       employerDetails: employerDetails || {},
//       companyContext: req.companyContext,
//       isNewCompany: isNewCompany,
//       company: company,
//       employeeNumberSettings: employeeNumberSettings || {},
//       eftDetails: eftDetails || {},
//       payslipSettings: payslipSettings || {},
//     });
//   } catch (error) {
//     console.error("Error in settings route:", error);
//     res.status(500).render("error", { message: "Error loading settings page" });
//   }
// });

// // Existing settings routes (if any)

// // New route for the new-template page
// router.get("/new-template", (req, res) => {
//   res.render("new-template");
// });

// // Handle form submission for new template
// router.post("/new-template", (req, res) => {
//   const { templateName } = req.body;
//   // TODO: Save the new template to your database
//   console.log("New template created:", templateName);
//   res.redirect("/settings#templates"); // Redirect back to the templates tab in settings
// });

// Handle employee number settings
router.get(
  "/:companyCode/settings/employee/employee-numbers",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
      const { companyCode } = req.params;

    try {
      // Set timeout for company query
      const company = await Company.findOne({ companyCode }).maxTimeMS(5000).lean();

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      // Set timeout for employee number settings query
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      }).maxTimeMS(5000).lean();


      res.render("settings/employee-numbers", {
        layout: "layouts/settings",
        company,
        employeeNumberSettings: employeeNumberSettings || {},
        csrfToken: req.csrfToken(),
        req,
        activeTab: 'employee', // Added activeTab
        title: 'Employee Number Settings', // Added title for consistency
        currentPage: 'employee-numbers' // Added currentPage for navigation
      });
    } catch (error) {
      console.error("Error loading employee numbers settings:", error);
      req.flash("error_msg", "Error loading employee numbers settings. Please try again.");
      res.redirect(`/${companyCode}/settings`);
    }
  }
);

router.post(
  "/:companyCode/settings/employee/employee-numbers",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      // Set timeout for company query
      const company = await Company.findOne({ companyCode }).maxTimeMS(5000).lean();
      
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const {
        employeeNumberMode,
        firstCompanyEmployeeNumber,
        updateExistingEmployees,
      } = req.body;

      console.log("Request body:", {
        employeeNumberMode,
        firstCompanyEmployeeNumber,
        updateExistingEmployees,
      });

      // Set timeout for employee number settings query
      let settings = await EmployeeNumberSettings.findOne({ company: company._id }).maxTimeMS(5000);

      if (!settings) {
        settings = new EmployeeNumberSettings({
        company: company._id,
          employeeNumberMode: employeeNumberMode || "manual",
          firstCompanyEmployeeNumber: firstCompanyEmployeeNumber || "1000",
        });
      } else {
        settings.employeeNumberMode = employeeNumberMode || "manual";
        settings.firstCompanyEmployeeNumber = firstCompanyEmployeeNumber || "1000";
      }

      await settings.save({ maxTimeMS: 5000 });

      if (updateExistingEmployees === "true" && employeeNumberMode === "automatic") {
        // Set timeout for employee update query
        const employees = await Employee.find({ company: company._id }).maxTimeMS(5000);
        let nextNumber = parseInt(firstCompanyEmployeeNumber);

        for (const employee of employees) {
          employee.employeeNumber = nextNumber.toString();
          await employee.save({ maxTimeMS: 5000 });
          nextNumber++;
        }

      }

      req.flash("success_msg", "Employee number settings updated successfully");
      res.redirect(`/${companyCode}/settings/employee/employee-numbers`);
    } catch (error) {
      console.error("Error updating employee numbers settings:", error);
      req.flash("error_msg", "Error updating employee numbers settings. Please try again.");
      res.redirect(`/${companyCode}/settings/employee/employee-numbers`);
    }
  }
);

// New route to fetch employee number settings
router.get(
  "/clients/:companyCode/settings/employee-number-settings",
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Find the company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }

      // Find settings for this company
      let settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        // If settings don't exist, create default settings
        settings = new EmployeeNumberSettings({
          company: company._id,
          mode: "manual",
          numberPattern: `${company.companyCode
            .slice(0, 3)
            .toUpperCase()}{0000}`,
          lastGeneratedNumber: 0,
          sortEmployeesByNumber: true,
        });
        await settings.save();
      }

      res.json(settings);
    } catch (error) {
      console.error("Error fetching employee number settings:", error);
      res
        .status(500)
        .json({ message: "Error fetching employee number settings" });
    }
  }
);

// // New routes for beneficiaries
// router.get("/beneficiaries-add-garnishee", (req, res) => {
//   res.render("beneficiaries-add-garnishee");
// });

// router.get("/beneficiaries-add-maintenance-order", (req, res) => {
//   res.render("beneficiaries-add-maintenance-order");
// });

// router.get("/beneficiaries-add-medical-aid", (req, res) => {
//   res.render("beneficiaries-add-medical-aid");
// });

// router.get("/beneficiaries-add-pension-fund", (req, res) => {
//   res.render("beneficiaries-add-pension-fund");
// });

// router.get("/beneficiaries-add-provident-fund", (req, res) => {
//   res.render("beneficiaries-add-provident-fund");
// });

// router.get("/beneficiaries-add-retirement-annuity-fund", (req, res) => {
//   res.render("beneficiaries-add-retirement-annuity-fund");
// });

// // New route for adding a custom item
router.get(
  ["/clients/:companyCode/settings/add-new-custom-item", "/:companyCode/settings/add-new-custom-item"],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      return res.render("settings/add-new-custom-item", {
        layout: "layouts/settings",
        company,
        activeTab: 'accounting',
        currentPage: 'add-new-custom-item',
        title: 'Add New Custom Item',
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error in add-new-custom-item route:", error);
      req.flash("error_msg", "Error loading add new custom item page");
      res.redirect(`/clients/${req.params.companyCode}/settings`);
    }
  }
);

// // Handle custom item form submission
// router.post("/add-new-custom-item", (req, res) => {
//   // TODO: Implement logic to save the new custom item to your database
//   console.log("New custom item:", req.body);
//   res.redirect("/settings#customItems");
// });

// // New routes for custom items
// router.get("/new-custom-income", (req, res) => {
//   res.render("new-custom-income");
// });

// router.get("/new-custom-deduction", (req, res) => {
//   res.render("new-custom-deduction");
// });

// router.get("/new-custom-allowance", (req, res) => {
//   res.render("new-custom-allowance");
// });

// router.get("/new-custom-benefit", (req, res) => {
//   res.render("new-custom-benefit");
// });

// router.get("/new-custom-erContribution", (req, res) => {
//   res.render("new-custom-erContribution");
// });

// router.get("/new-custom-reimbursement", (req, res) => {
//   res.render("new-custom-reimbursement");
// });

// router.get("/new-custom-existing-item", (req, res) => {
//   res.render("new-custom-existing-item");
// });

// ... (rest of the code remains unchanged)

// Implement similar routes for other sections

// Add these new routes to your existing routes/settings.js file

router.get("/advanced-settings", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    const advancedSettings = await AdvancedSettings.findOne({
      company: company._id,
    });
    res.render("settings/advanced-settings", { company, advancedSettings });
  } catch (error) {
    console.error("Error rendering advanced settings:", error);
    res
      .status(500)
      .render("error", { message: "Error loading advanced settings" });
  }
});

router.get("/company-information", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    res.render("settings/company-information", { company });
  } catch (error) {
    console.error("Error rendering company information:", error);
    res
      .status(500)
      .render("error", { message: "Error loading company information" });
  }
});

router.get("/user-management", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    const users = await User.find({ company: company._id });
    res.render("settings/user-management", { company, users });
  } catch (error) {
    console.error("Error rendering user management:", error);
    res
      .status(500)
      .render("error", { message: "Error loading user management" });
  }
});

router.get("/integrations", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    const integrations = await Integration.find({ company: company._id });
    res.render("settings/integrations", { company, integrations });
  } catch (error) {
    console.error("Error rendering integrations:", error);
    res.status(500).render("error", { message: "Error loading integrations" });
  }
});

router.get("/audit-log", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    const auditLogs = await AuditLog.find({ company: company._id })
      .sort({ timestamp: -1 })
      .limit(100);
    res.render("settings/audit-log", { company, auditLogs });
  } catch (error) {
    console.error("Error rendering audit log:", error);
    res.status(500).render("error", { message: "Error loading audit log" });
  }
});

// Implement POST routes for saving changes in these new sections
// For example:

router.post("/advanced-settings", ensureAuthenticated, async (req, res) => {
  try {
    const company = await Company.findById(req.user.currentCompany);
    const updatedSettings = await AdvancedSettings.findOneAndUpdate(
      { company: company._id },
      req.body,
      { new: true, upsert: true }
    );
    req.flash("success", "Advanced settings updated successfully");
    res.redirect("/settings/advanced-settings");
  } catch (error) {
    console.error("Error saving advanced settings:", error);
    req.flash("error", "Failed to save advanced settings");
    res.redirect("/settings/advanced-settings");
  }
});

// Implement similar POST routes for other new sections

// Templates
router.get(
  "/:companyCode/settings/templates",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      const templates = await Template.find({ company: company._id });
      res.render("settings/templates", {
        company,
        templates,
        activeTab: "other",
        activeSubTab: "templates",
      });
    } catch (error) {
      console.error("Error rendering templates:", error);
      res.status(500).render("error", { message: "Error loading templates" });
    }
  }
);

// Advanced Settings
router.get(
  "/:companyCode/settings/advanced",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      const advancedSettings = await AdvancedSettings.findOne({
        company: company._id,
      });
      res.render("settings/advanced-settings", {
        company,
        advancedSettings,
        activeTab: "other",
        activeSubTab: "advanced",
      });
    } catch (error) {
      console.error("Error rendering advanced settings:", error);
      res
        .status(500)
        .render("error", { message: "Error loading advanced settings" });
    }
  }
);

router.get(
  "/:companyCode/settings/employee/employee-numbers/ajax",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      let settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        // Create default settings if they don't exist
        settings = new EmployeeNumberSettings({
          company: company._id,
          mode: "manual",
          numberPattern: `${company.companyCode
            .slice(0, 3)
            .toUpperCase()}{0000}`,
          lastGeneratedNumber: 0,
          sortEmployeesByNumber: true,
        });
        await settings.save();
      }

      res.json({
        mode: settings.mode,
        numberPattern: settings.numberPattern,
        lastGeneratedNumber: settings.lastGeneratedNumber,
        sortEmployeesByNumber: settings.sortEmployeesByNumber,
      });
    } catch (error) {
      console.error("Error fetching employee number settings:", error);
      res
        .status(500)
        .json({ error: "Failed to fetch employee number settings" });
    }
  }
);

// Leave settings page
router.get(
  "/:companyCode/settings/leave",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const company = await Company.findOne({
        companyCode: req.params.companyCode,
      });
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/dashboard");
      }

      // Fetch leave types with employee allocations
      const leaveTypes = await LeaveType.find({ company: company._id })
        .populate(
          "employeeAllocations.employee",
          "firstName lastName employeeNumber"
        )
        .sort({ name: 1 });

      // Fetch active employees for the allocation modal
      const employees = await Employee.find({
        company: company._id,
        status: "Active",
      })
        .select("firstName lastName employeeNumber")
        .sort("firstName lastName");

      res.render("settings/leave", {
        company,
        leaveTypes,
        employees,
        user: req.user,
        title: "Leave Settings",
        mainTab: "settings",
        subTab: "leave",
      });
    } catch (error) {
      console.error("Error loading leave settings:", error);
      req.flash("error_msg", "Error loading leave settings");
      res.redirect(`/clients/${req.params.companyCode}/settings`);
    }
  }
);

// WhatsApp requests page - SPECIFIC route BEFORE generic route (handle both patterns)
router.get(['/clients/:companyCode/settings/other/whatsapp', '/:companyCode/settings/other/whatsapp'], ensureAuthenticated, checkCompanyMiddleware, csrfProtection, async (req, res) => {
  try {
    const { companyCode } = req.params;

    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).send('Company not found');
    }

    // Initialize variables with empty arrays
    let payslipRequests = [];
    let irp5Requests = [];
    let leaveRequests = [];
    let employeesWithWhatsApp = [];

    try {

      // Log the query being executed
      const query = {
        company: company._id,
        requestType: { $in: ['payslip', 'irp5', 'leave'] }
      };
      console.log('MongoDB Query:', JSON.stringify(query, null, 2));

      // Test: Query all logs for this company without requestType filter
      const allCompanyLogs = await WhatsAppRequestLog.find({ company: company._id });

      if (allCompanyLogs.length > 0) {
        console.log('Sample company logs (first 3):');
        allCompanyLogs.slice(0, 3).forEach((log, idx) => {
          console.log(`Log ${idx + 1}:`, {
            _id: log._id,
            requestType: log.requestType,
            mobileNumber: log.mobileNumber,
            status: log.status,
            employee: log.employee,
            company: log.company
          });
        });
      }

  // Query WhatsApp logs with better population and error handling
  const logs = await WhatsAppRequestLog.find(query)
    .populate({
      path: 'employee',
      select: 'firstName lastName personalDetails.mobileNumber mobileNumber phone',
      options: { lean: true }
    })
    .populate('leaveRequest')
    .sort({ timestamp: -1 })
    .lean()
    .exec();

  if (logs.length > 0) {
    console.log('Sample log:', JSON.stringify(logs[0], null, 2));
  }

      if (logs.length > 0) {
        console.log('[WhatsApp Settings] Sample log:', JSON.stringify(logs[0], null, 2));
      } else {
      }

      // Split logs by type with null checks
      payslipRequests = logs.filter(l => l && l.requestType === 'payslip');
      irp5Requests = logs.filter(l => l && l.requestType === 'irp5');
      leaveRequests = logs.filter(l => l && l.requestType === 'leave');


      // Log company id and type

      try {
        // Get all employees with WhatsApp numbers - enhanced with more detailed logging
        const employeeQuery = {
          company: company._id,
          status: 'Active',
          $or: [
            { 'personalDetails.mobileNumber': { $exists: true, $nin: [null, '', undefined] } },
            { mobileNumber: { $exists: true, $nin: [null, '', undefined] } },
            { phone: { $exists: true, $nin: [null, '', undefined] } }
          ]
        };

        console.log('Employee Query:', JSON.stringify(employeeQuery, null, 2));

        const employees = await Employee.find(employeeQuery)
          .select('firstName lastName personalDetails.mobileNumber mobileNumber phone')
          .lean()
          .exec();

        if (employees.length > 0) {
          console.log('[WhatsApp Settings] Sample employees:', JSON.stringify(employees.slice(0, 3), null, 2));

          // Log the first 3 employees' phone numbers for verification
          employees.slice(0, 3).forEach((emp, idx) => {
            console.log(`Employee ${idx + 1} phone numbers:`, {
              personalMobile: emp.personalDetails?.mobileNumber,
              mobile: emp.mobileNumber,
              phone: emp.phone
            });
          });

          // Format employees for the view
          employeesWithWhatsApp = employees.map(emp => {
            const mobileNumber = emp.personalDetails?.mobileNumber || emp.mobileNumber || emp.phone;
            return {
              _id: emp._id,
              firstName: emp.firstName || 'N/A',
              lastName: emp.lastName || 'N/A',
              mobileDisplay: mobileNumber || 'N/A',
              company: emp.company,
              status: emp.status
            };
          }).filter(emp => emp.mobileDisplay !== 'N/A');

        } else {
          // Try a more permissive query to see if any employees exist at all
          const anyEmployee = await Employee.findOne({ company: company._id })
            .select('_id firstName lastName personalDetails.mobileNumber mobileNumber phone')
            .lean()
            .exec();

          if (anyEmployee) {
            console.log('Sample employee found (not filtered by phone):',
              JSON.stringify(anyEmployee, null, 2));
          }
          employeesWithWhatsApp = []; // Set empty array when no employees found
        }


      } catch (error) {
        console.error('Error fetching employees:', error);
        employeesWithWhatsApp = []; // Set empty array on error
      }

    } catch (error) {
      console.error('[WhatsApp Settings] Error querying data:', error);
      // Continue with empty arrays if queries fail
    }

    // Pass all variables to template
    console.log('[WhatsApp Settings] Rendering template with:', {
      payslipRequests: payslipRequests.length,
      irp5Requests: irp5Requests.length,
      leaveRequests: leaveRequests.length,
      employeesWithWhatsApp: employeesWithWhatsApp.length
    });
    res.render('settings/whatsapp', {
      layout: "layouts/settings",
      company,
      user: req.user,
      activeTab: 'other',
      activeSubTab: 'whatsapp',
      title: 'WhatsApp Settings',
      payslipRequests: payslipRequests || [],
      irp5Requests: irp5Requests || [],
      leaveRequests: leaveRequests || [],
      employeesWithWhatsApp: employeesWithWhatsApp || [],
      csrfToken: req.csrfToken(),
      messages: req.flash()
    });
  } catch (err) {
    console.error('[WhatsApp Settings] Error:', err);
    res.status(500).send('Error loading WhatsApp requests');
  }
});

// Add these new routes for main tabs
router.get(
  ["/clients/:companyCode/settings/:mainTab/:subTab?", "/:companyCode/settings/:mainTab/:subTab?"],
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, mainTab, subTab } = req.params;
      const { newCompany } = req.query;

      console.log("Route parameters:", {
        companyCode,
        mainTab,
        subTab,
        newCompany,
      });

      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      // Handle specific sub-tabs
      if (mainTab === 'employee') {
      switch (subTab) {
          case 'job-grades':
            const jobGrades = await JobGrade.find({ companyCode })
              .lean()
              .maxTimeMS(3000);
            
            return res.render("settings/job-grades", {
              layout: "layouts/settings",
              company,
              jobGrades,
              activeTab: 'employee',
              currentPage: 'job-grades',
              title: 'Job Grades',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'employer-filing-details':
            const employerFilingDetails = await EmployerFilingDetails.findOne({ companyCode })
              .lean()
              .maxTimeMS(3000) || {};
            
            return res.render("settings/employer-filing-details", {
              layout: "layouts/settings",
              company,
              employerFilingDetails,
              activeTab: 'employee',
              currentPage: 'employer-filing-details',
              title: 'Employer Filing Details',
            csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'employee-numbers':
            const employeeNumberSettings = await EmployeeNumberSettings.findOne({ companyCode })
              .lean()
              .maxTimeMS(3000);
            
            return res.render("settings/employee-numbers", {
              layout: "layouts/settings",
              company,
              employeeNumberSettings: employeeNumberSettings || {},
              activeTab: 'employee',
              currentPage: 'employee-numbers',
              title: 'Employee Numbers',
            csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          default:
            return res.render("settings/employee", {
              layout: "layouts/settings",
              company,
              activeTab: mainTab,
              title: 'Employee Settings',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });
        }
      } else if (mainTab === 'accounting') {
        switch (subTab) {
          case 'custom-items':
            let customItems = [];
            try {
              customItems = await CustomItem.find({ companyCode })
                .lean()
                .maxTimeMS(3000);
            } catch (error) {
              console.error("Error fetching custom items:", error);
              req.flash("error", "Failed to fetch custom items. Please try again.");
              // Continue with empty array if there's an error
            }
            
            return res.render("settings/custom-items", {
              layout: "layouts/settings",
              company,
              customItems: customItems || [], // Ensure customItems is always defined
              activeTab: 'accounting',
              currentPage: 'custom-items',
              title: 'Custom Items',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'beneficiaries':
            const beneficiaryTypes = ['garnishee', 'maintenance-order', 'medical-aid', 'pension-fund', 'provident-fund', 'retirement-annuity-fund'];
          const beneficiariesByType = {};
            
          for (const type of beneficiaryTypes) {
              beneficiariesByType[type] = await Beneficiary.find({ company: company._id, type })
                .lean()
                .maxTimeMS(3000);
            }
            
            return res.render("settings/beneficiaries", {
              layout: "layouts/settings",
              company,
            beneficiariesByType,
              activeTab: 'accounting',
              currentPage: 'beneficiaries',
              title: 'Beneficiaries',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'eft':
            const eftDetails = await EFTDetails.findOne({ company: company._id })
              .lean()
              .maxTimeMS(3000) || {};
            
            return res.render("settings/eft", {
              layout: "layouts/settings",
              company,
            eftDetails,
              activeTab: 'accounting',
              currentPage: 'eft',
              title: 'EFT Settings',
            csrfToken: req.csrfToken(),
              messages: req.flash(),
              req: req
            });

          case 'accounting-integrations':
            const integration = await Integration.findOne({
              company: company._id,
              provider: 'xero',
              type: 'accounting'
            }).lean().maxTimeMS(3000);
            
            return res.render("settings/accounting-integrations", {
              layout: "layouts/settings",
              company,
              xeroIntegration: integration || { status: 'inactive' },
              activeTab: 'accounting',
              currentPage: 'accounting-integrations',
              title: 'Accounting Integrations',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'accounting-splits':
            return res.render("settings/accounting-splits", {
              layout: "layouts/settings",
              company,
              activeTab: 'accounting',
              currentPage: 'accounting-splits',
              title: 'Accounting Splits',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          default:
            return res.render("settings/accounting", {
              layout: "layouts/settings",
              company,
              activeTab: mainTab,
              title: 'Accounting Settings',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });
        }
      } else if (mainTab === 'payroll') {
        switch (subTab) {
          case 'pay-frequencies':
            const payFrequencies = await PayFrequency.find({ company: company._id })
              .lean()
              .maxTimeMS(3000);
            
            return res.render("settings/pay-frequencies", {
              layout: "layouts/settings",
              company,
              payFrequencies,
              activeTab: 'payroll',
              currentPage: 'pay-frequencies',
              title: 'Pay Frequencies',
            csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'leave':
            const leaveTypes = await LeaveType.find({ company: company._id })
              .populate('employeeAllocations.employee', 'firstName lastName employeeNumber')
              .lean()
              .maxTimeMS(3000);
            const employees = await Employee.find({ company: company._id })
              .select('firstName lastName')
              .lean()
              .maxTimeMS(3000);
            
            return res.render("settings/leave", {
              layout: "layouts/settings",
              company,
              leaveTypes,
              employees,
              activeTab: 'payroll',
              currentPage: 'leave',
              title: 'Leave Settings',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'pay-points':
            const payPoints = await PayPoint.find({ company: company._id })
              .lean()
              .maxTimeMS(3000);
            
            return res.render("settings/pay-points", {
              layout: "layouts/settings",
              company,
              payPoints,
              activeTab: 'payroll',
              currentPage: 'pay-points',
              title: 'Pay Points',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'payroll-calculations':
            return res.render("settings/payroll-calculations", {
              layout: "layouts/settings",
              company,
              activeTab: 'payroll',
              currentPage: 'payroll-calculations',
              title: 'Payroll Calculations',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

          case 'payslip':
            const payslipSettings = await PayslipSettings.findOne({ company: company._id })
              .lean()
              .maxTimeMS(3000) || {};
            
            return res.render("settings/payslip", {
              layout: "layouts/settings",
              company,
              payslipSettings,
              activeTab: 'payroll',
              currentPage: 'payslip',
              title: 'Payslip Settings',
              csrfToken: req.csrfToken(),
              messages: req.flash()
            });

        default:
            return res.render("settings/payroll", {
              layout: "layouts/settings",
              company,
            activeTab: mainTab,
              title: 'Payroll Settings',
            csrfToken: req.csrfToken(),
              messages: req.flash()
            });
        }
      }

      // Handle custom-items specifically
      if (mainTab === 'custom-items') {
        let customItems = [];
        try {
          const CustomItem = require('../models/CustomItem');
          customItems = await CustomItem.find({ companyCode })
            .lean()
            .maxTimeMS(3000);
        } catch (error) {
          console.error("Error fetching custom items:", error);
          req.flash("error", "Failed to fetch custom items. Please try again.");
          // Continue with empty array if there's an error
        }

        return res.render("settings/custom-items", {
          layout: "layouts/settings",
          company,
          customItems: customItems || [], // Ensure customItems is always defined
          activeTab: mainTab,
          title: 'Custom Items',
          csrfToken: req.csrfToken(),
          messages: req.flash()
        });
      }

      // Handle other main tabs
      res.render(`settings/${mainTab}`, {
        layout: "layouts/settings",
        company,
        activeTab: mainTab,
        title: `${mainTab.charAt(0).toUpperCase() + mainTab.slice(1)} Settings`,
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error in settings route:", error);
      req.flash("error_msg", "Error loading settings");
      res.redirect(`/clients/${companyCode}/settings`);
    }
  }
);

// Accounting Splits
router.get(
  "/:companyCode/settings/accounting/accounting-splits",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      res.render("settings/accounting-splits", {
        company,
        activeTab: "accounting",
        activeSubTab: "accounting-splits",
      });
    } catch (error) {
      console.error("Error rendering accounting splits:", error);
      res
        .status(500)
        .render("error", { message: "Error loading accounting splits" });
    }
  }
);

// ... (add similar routes for beneficiaries, custom-items, and eft)

// Employee Numbers
router.get(
  "/:companyCode/settings/employee/employee-numbers",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    try {
      
      const { companyCode } = req.params;
      
      // Find company with timeout
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find employee number settings with timeout
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      }).lean();

      console.log(`Found employee number settings: ${JSON.stringify(employeeNumberSettings || {})}`);

      res.render("settings/employee-numbers", {
        company,
        employeeNumberSettings: employeeNumberSettings || {},
        activeTab: "employee",
        activeSubTab: "employee-numbers",
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req: req
      });
    } catch (error) {
      console.error("Error in employee-numbers route:", error);
      req.flash("error", "Error loading employee number settings. Please try again.");
      
      // If we have a company code, redirect to settings page
      if (req.params.companyCode) {
        return res.redirect(`/clients/${req.params.companyCode}/settings/employee`);
      }
      
      // Fallback redirect
      res.redirect("/clients");
    }
  }
);

router.post(
  "/:companyCode/settings/beneficiaries-add-:type",
  ensureAuthenticated,
  express.json(),
  async (req, res) => {
    try {
      const { companyCode, type } = req.params;

      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Create beneficiary data object based on type
      const beneficiaryData = {
        company: company._id,
        type: type,
        ...req.body,
      };

      // Remove _csrf token from beneficiaryData if present
      delete beneficiaryData._csrf;

      // Validate required fields based on type
      const requiredFields = {
        garnishee: [
          "beneficiaryName",
          "accountNumber",
          "bankName",
          "branchCode",
        ],
        "medical-aid": [
          "schemeName",
          "membershipNumber",
          "mainMemberName",
          "dependents",
        ],
        "pension-fund": [
          "fundName",
          "membershipNumber",
          "contributionPercentage",
        ],
        "provident-fund": [
          "fundName",
          "membershipNumber",
          "contributionPercentage",
        ],
        "maintenance-order": ["beneficiaryName", "orderNumber"],
        "retirement-annuity-fund": [
          "fundName",
          "policyNumber",
          "contributionAmount",
        ],
      };

      const missingFields = requiredFields[type]?.filter(
        (field) => !req.body[field]
      );

      if (missingFields?.length) {
        return res.status(400).json({
          success: false,
          message: `Missing required fields: ${missingFields.join(", ")}`,
        });
      }

      // Create and save new beneficiary
      const beneficiary = new Beneficiary(beneficiaryData);
      await beneficiary.save();


      // Return success response
      return res.json({
        success: true,
        message: `${type.replace("-", " ")} beneficiary added successfully`,
        data: beneficiary,
      });
    } catch (error) {
      console.error(`Error adding ${type} beneficiary:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to add ${type.replace("-", " ")} beneficiary`,
        error: error.message,
      });
    }
  }
);

router.get(
  "/api/beneficiaries/:companyCode/:type",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, type } = req.params;
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }
      console.log(
        "Fetching beneficiaries for company:",
        company._id,
        "and type:",
        type
      );
      const beneficiaries = await Beneficiary.find({
        company: company._id,
        type,
      });
      res.json(beneficiaries);
    } catch (error) {
      console.error("Error fetching beneficiaries:", error);
      res.status(500).json({ error: "Failed to fetch beneficiaries" });
    }
  }
);

// Update the EFT routes to handle form submission
router.post(
  "/:companyCode/settings/accounting/eft",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Extract form data
      const {
        eftFormat,
        bank,
        accountNumber,
        branchCode,
        accountType,
        additionalBankAccounts,
      } = req.body;

      // Parse additional bank accounts if present
      let parsedAdditionalAccounts = [];
      if (additionalBankAccounts) {
        try {
          parsedAdditionalAccounts = JSON.parse(additionalBankAccounts);
        } catch (e) {
          console.error("Error parsing additional bank accounts:", e);
        }
      }

      // Create EFT details object
      const eftDetails = {
        company: company._id,
        eftFormat,
        bank,
        accountNumber,
        branchCode,
        accountType,
        additionalBankAccounts: parsedAdditionalAccounts,
      };

      // Find existing EFT details or create new ones
      let existingEFTDetails = await EFTDetails.findOne({
        company: company._id,
      });

      if (existingEFTDetails) {
        // Update existing record
        existingEFTDetails.set(eftDetails);
        await existingEFTDetails.save();
      } else {
        // Create new record
        existingEFTDetails = new EFTDetails(eftDetails);
        await existingEFTDetails.save();
      }

      req.flash("success", "EFT details saved successfully");
      res.redirect(`/clients/${companyCode}/settings/accounting/eft`);
    } catch (error) {
      console.error("Error saving EFT details:", error);
      req.flash("error", "Failed to save EFT details: " + error.message);
      res.redirect(`/clients/${companyCode}/settings/accounting/eft`);
    }
  }
);

router.get(
  "/:companyCode/settings/accounting/eft",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/settings");
      }

      const eftDetails = (await EFTDetails.findOne({
        company: company._id,
      })) || {
        eftFormat: "",
        bank: "",
        accountNumber: "",
        branchCode: "",
        accountType: "",
        additionalBankAccounts: [],
      };

      res.render("settings/eft", {
        company,
        eftDetails,
        activeTab: "accounting",
        activeSubTab: "eft",
        req: req,
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error fetching EFT details:", error);
      req.flash("error", "Failed to load EFT details");
      res.redirect("/settings");
    }
  }
);

// Add new pay point
router.post(
  "/:companyCode/settings/add-pay-point",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }

      const { payPointName, payPointCode, payPointDescription } = req.body;

      const newPayPoint = new PayPoint({
        company: company._id,
        name: payPointName,
        code: payPointCode,
        description: payPointDescription,
      });

      await newPayPoint.save();

      req.flash("success", "Pay point added successfully");
      res.redirect(`/clients/${companyCode}/settings/payroll/pay-points`);
    } catch (error) {
      console.error("Error adding pay point:", error);
      req.flash("error", "Failed to add pay point");
      res.redirect(
        `/clients/${req.params.companyCode}/settings/payroll/pay-points`
      );
    }
  }
);

// Delete pay point
router.get(
  "/:companyCode/settings/delete-pay-point/:payPointId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, payPointId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      await PayPoint.findByIdAndDelete(payPointId);

      req.flash("success", "Pay point deleted successfully");
      res.redirect(`/clients/${companyCode}/settings/payroll/pay-points`);
    } catch (error) {
      console.error("Error deleting pay point:", error);
      req.flash("error", "Failed to delete pay point");
      res.redirect(
        `/clients/${req.params.companyCode}/settings/payroll/pay-points`
      );
    }
  }
);

// Edit pay point page
router.get(
  "/:companyCode/settings/edit-pay-point/:payPointId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, payPointId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      const payPoint = await PayPoint.findById(payPointId);

      if (!payPoint) {
        req.flash("error", "Pay point not found");
        return res.redirect(
          `/clients/${companyCode}/settings/payroll/pay-points`
        );
      }

      res.render("settings/edit-pay-point", {
        company,
        payPoint,
        activeTab: "payroll",
        activeSubTab: "pay-points",
      });
    } catch (error) {
      console.error("Error loading pay point edit page:", error);
      req.flash("error", "Failed to load pay point edit page");
      res.redirect(
        `/clients/${req.params.companyCode}/settings/payroll/pay-points`
      );
    }
  }
);

// Add new template
router.post(
  "/:companyCode/settings/new-template",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }

      const { name, content, type } = req.body;

      const newTemplate = new Template({
        company: company._id,
        name,
        content,
        type,
      });

      await newTemplate.save();

      req.flash("success", "Template added successfully");
      res.redirect(`/clients/${companyCode}/settings/other/templates`);
    } catch (error) {
      console.error("Error adding template:", error);
      req.flash("error", "Failed to add template");
      res.redirect(
        `/clients/${req.params.companyCode}/settings/other/templates`
      );
    }
  }
);

// Delete template
router.get(
  "/:companyCode/settings/delete-template/:templateId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, templateId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      await Template.findByIdAndDelete(templateId);

      req.flash("success", "Template deleted successfully");
      res.redirect(`/clients/${companyCode}/settings/other/templates`);
    } catch (error) {
      console.error("Error deleting template:", error);
      req.flash("error", "Failed to delete template");
      res.redirect(
        `/clients/${req.params.companyCode}/settings/other/templates`
      );
    }
  }
);

// Edit template page
router.get(
  "/:companyCode/settings/edit-template/:templateId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, templateId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      const template = await Template.findById(templateId);

      if (!template) {
        req.flash("error", "Template not found");
        return res.redirect(`/clients/${companyCode}/settings/other/templates`);
      }

      res.render("settings/edit-template", {
        company,
        template,
        activeTab: "other",
        activeSubTab: "templates",
      });
    } catch (error) {
      console.error("Error loading template edit page:", error);
      res.status(500).render("error", { message: "Error loading edit page" });
    }
  }
);

// Save advanced settings
router.post(
  "/:companyCode/settings/advanced",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findById(req.user.currentCompany);

      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }

      // Find existing settings or create new ones
      let advancedSettings = await AdvancedSettings.findOne({
        company: company._id,
      });

      if (advancedSettings) {
        // Update existing settings
        advancedSettings.set(req.body);
      } else {
        // Create new settings
        advancedSettings = new AdvancedSettings({
          company: company._id,
          ...req.body,
        });
      }

      await advancedSettings.save();

      req.flash("success", "Advanced settings saved successfully");
      res.redirect(`/clients/${companyCode}/settings/other/advanced`);
    } catch (error) {
      console.error("Error saving advanced settings:", error);
      req.flash("error", "Failed to save advanced settings");
      res.redirect(`/clients/${companyCode}/settings/other/advanced`);
    }
  }
);

// And add this route for saving payslip settings
router.post(
  "/:companyCode/settings/payroll/payslip",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }

      // Convert checkbox values to booleans
      const formData = {};
      for (const [key, value] of Object.entries(req.body)) {
        formData[key] = value === "on" ? true : value;
      }

      // Find existing settings or create new ones
      let payslipSettings = await PayslipSettings.findOne({
        company: company._id,
      });

      if (payslipSettings) {
        // Update existing settings
        payslipSettings.set(formData);
      } else {
        // Create new settings
        payslipSettings = new PayslipSettings({
          company: company._id,
          ...formData,
        });
      }

      await payslipSettings.save();

      req.flash("success", "Payslip settings saved successfully");
      res.redirect(`/clients/${companyCode}/settings/payroll/payslip`);
    } catch (error) {
      console.error("Error saving payslip settings:", error);
      req.flash("error", "Failed to save payslip settings");
      res.redirect(`/clients/${companyCode}/settings/payroll/payslip`);
    }
  }
);

// Add this route handler for employer filing details
router.post(
  "/:companyCode/settings/employee/employer-filing-details",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Extract form data
      const {
        payeNumber,
        diplomaticIndemnity,
        sicMainGroup,
        sicLevel2,
        sicLevel3,
        sicLevel4,
        sicCode,
        telephoneNumber,
        sarsContactName,
        sarsContactSurname,
        sarsContactPosition,
        sarsContactBusTelNo,
        sarsContactCellNo,
        sarsContactEmail,
        reportEmployeeNumbers,
        uifNumber,
        companyRegistrationNo,
        postalAddress,
      } = req.body;

      // Create employer filing details object
      const employerFilingDetailsData = {
        company: company._id,
        payeNumber,
        diplomaticIndemnity,
        sicMainGroup,
        sicLevel2,
        sicLevel3,
        sicLevel4,
        sicCode,
        telephoneNumber,
        sarsContactName,
        sarsContactSurname,
        sarsContactPosition,
        sarsContactBusTelNo,
        sarsContactCellNo,
        sarsContactEmail,
        reportEmployeeNumbers,
        uifNumber,
        companyRegistrationNo,
        postalAddress,
      };

      // Find existing employer filing details or create new ones
      const employerFilingDetails =
        await EmployerFilingDetails.findOneAndUpdate(
          { company: company._id },
          employerFilingDetailsData,
          { new: true, upsert: true }
        );

      // Always return JSON response
      return res.json({
        success: true,
        message: "Employer filing details saved successfully",
        data: employerFilingDetails,
      });
    } catch (error) {
      console.error("Error saving employer filing details:", error);
      // Always return JSON response even for errors
      return res.status(500).json({
        success: false,
        message: "Failed to save employer filing details",
        error: error.message,
      });
    }
  }
);

// Add this route before the error handling middleware
router.get(
  "/:companyCode/settings/api/beneficiaries/:companyCode/:type",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, type } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      const beneficiaries = await Beneficiary.find({
        company: company._id,
        type: type,
      });

      res.json(beneficiaries);
    } catch (error) {
      console.error("Error fetching beneficiaries:", error);
      res.status(500).json({ error: "Failed to fetch beneficiaries" });
    }
  }
);

// Add these new routes to your existing routes/settings.js file

// Add a new route specifically for the /clients/ prefix
router.get(
  "/clients/:companyCode/settings/accounting/accounting-integrations",
  ensureAuthenticated,
  async (req, res) => {
    try {
      console.log("Request User:", {
        id: req.user._id,
        email: req.user.email,
        currentCompany: req.user.currentCompany,
      });

      const { companyCode } = req.params;

      // Log company query details

      const company = await Company.findOne({ companyCode });

      if (!company) {
        console.error("Company not found for code:", companyCode);
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      console.log("Company Found:", {
        _id: company._id,
        name: company.name,
        companyCode: company.companyCode,
      });

      // Detailed integration query logging
      const integrationQuery = {
        company: company._id,
        provider: "xero",
        type: "accounting",
      };

      // Use findOne with lean() for performance and to ensure plain object
      const xeroIntegration = await Integration.findOne(
        integrationQuery
      ).lean();

      console.log("Xero Integration Query Result:", {
        found: !!xeroIntegration,
        details: xeroIntegration
          ? {
              _id: xeroIntegration._id,
              status: xeroIntegration.status,
              lastSync: xeroIntegration.lastSync,
              provider: xeroIntegration.provider,
              type: xeroIntegration.type,
            }
          : null,
      });

      // Ensure xeroIntegration is always an object
      const renderData = {
        company,
        activeTab: "accounting",
        activeSubTab: "accounting-integrations",
        xeroIntegration: xeroIntegration || {
          status: "inactive",
          provider: "xero",
          type: "accounting",
          company: company._id,
        },
        isXeroConnected: xeroIntegration && xeroIntegration.status === "active",
      };

      console.log("Render Data:", JSON.stringify(renderData, null, 2));

      res.render("settings/accounting-integrations", renderData);
    } catch (error) {
      console.error("===== ACCOUNTING INTEGRATIONS ROUTE ERROR =====");
      console.error("Full Error:", error);
      res.status(500).render("error", {
        message: "Error loading accounting integrations",
        error: process.env.NODE_ENV === "development" ? error.toString() : null,
      });
    }
  }
);

// Add this error handling middleware at the end of your routes
router.use((err, req, res, next) => {
  console.error("Settings route error:", err);
  if (err instanceof multer.MulterError) {
    return res.status(400).json({
      success: false,
      message: "File upload error",
      error: err.message,
    });
  }
  res.status(500).json({
    success: false,
    message: "Internal server error",
    error: err.message,
  });
});

// Add this GET route for editing beneficiaries
router.get(
  "/:companyCode/settings/beneficiaries-edit-:type/:id",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, type, id } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      const beneficiary = await Beneficiary.findOne({
        _id: id,
        company: company._id,
        type: type,
      });

      if (!beneficiary) {
        return res
          .status(404)
          .render("error", { message: "Beneficiary not found" });
      }

      res.render(`beneficiaries-edit-${type}`, {
        company,
        beneficiary,
        activeTab: "accounting",
        activeSubTab: "beneficiaries",
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error(`Error rendering beneficiary edit page:`, error);
      res
        .status(500)
        .render("error", { message: "Error loading beneficiary edit page" });
    }
  }
);

// Add this PUT route for updating beneficiaries
router.put(
  "/:companyCode/settings/beneficiaries-edit-:type/:id",
  ensureAuthenticated,
  express.json(),
  async (req, res) => {
    try {
      const { companyCode, type, id } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Remove _csrf token from update data
      const updateData = { ...req.body };
      delete updateData._csrf;

      const beneficiary = await Beneficiary.findOneAndUpdate(
        { _id: id, company: company._id, type: type },
        updateData,
        { new: true }
      );

      if (!beneficiary) {
        return res.status(404).json({
          success: false,
          message: "Beneficiary not found",
        });
      }

      return res.json({
        success: true,
        message: `${type.replace("-", " ")} beneficiary updated successfully`,
        data: beneficiary,
      });
    } catch (error) {
      console.error(`Error updating beneficiary:`, error);
      return res.status(500).json({
        success: false,
        message: "Failed to update beneficiary",
        error: error.message,
      });
    }
  }
);

// Add this to your existing route that renders the form
router.get(
  "/:companyCode/settings/employee/add",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      // Fetch all beneficiaries for this company
      const beneficiaries = {
        garnishee: await Beneficiary.find({
          company: company._id,
          type: "garnishee",
        }),
        "maintenance-order": await Beneficiary.find({
          company: company._id,
          type: "maintenance-order",
        }),
        "medical-aid": await Beneficiary.find({
          company: company._id,
          type: "medical-aid",
        }),
        "pension-fund": await Beneficiary.find({
          company: company._id,
          type: "pension-fund",
        }),
        "provident-fund": await Beneficiary.find({
          company: company._id,
          type: "provident-fund",
        }),
        "retirement-annuity-fund": await Beneficiary.find({
          company: company._id,
          type: "retirement-annuity-fund",
        }),
      };

      res.render("employee/add", {
        company,
        beneficiaries,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading add employee page:", error);
      res
        .status(500)
        .render("error", { message: "Error loading add employee page" });
    }
  }
);

// Add this DELETE route for beneficiaries
router.delete(
  "/:companyCode/settings/beneficiaries-delete/:id",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, id } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const beneficiary = await Beneficiary.findOneAndDelete({
        _id: id,
        company: company._id,
      });

      if (!beneficiary) {
        return res.status(404).json({
          success: false,
          message: "Beneficiary not found",
        });
      }

      return res.json({
        success: true,
        message: "Beneficiary deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting beneficiary:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to delete beneficiary",
        error: error.message,
      });
    }
  }
);

// Add these routes for custom items
router.post(
  "/:companyCode/settings/custom-items/new",
  ensureAuthenticated,
  express.json(),
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Process the request body for new custom income structure
      const processedData = {
        ...req.body,
        // Convert checkbox values to boolean
        taxedAnnually: req.body.taxedAnnually === true || req.body.taxedAnnually === "true",
        includeInFluctuatingLeaveRate: req.body.includeInFluctuatingLeaveRate === true || req.body.includeInFluctuatingLeaveRate === "true",
        overtime: req.body.overtime === true || req.body.overtime === "true",
        affectsWageForETI: req.body.affectsWageForETI === true || req.body.affectsWageForETI === "true",
        includeInCostToCompany: req.body.includeInCostToCompany === true || req.body.includeInCostToCompany === "true",
        // Handle new conditional fields
        enableProRata: req.body.enableProRata === true || req.body.enableProRata === "true",
        differentRateForEveryEmployee: req.body.differentRateForEveryEmployee === true || req.body.differentRateForEveryEmployee === "true",
        // Handle numeric fields
        amount: req.body.amount ? parseFloat(req.body.amount) : undefined,
        hoursWorkedFactor: req.body.hoursWorkedFactor ? parseFloat(req.body.hoursWorkedFactor) : undefined,
        customRate: req.body.customRate ? parseFloat(req.body.customRate) : undefined,
        monthlyAmount: req.body.monthlyAmount ? parseFloat(req.body.monthlyAmount) : undefined,
        // Handle array fields
        incomeItems: Array.isArray(req.body.incomeItems) ? req.body.incomeItems : (req.body.incomeItems ? [req.body.incomeItems] : undefined),
      };


      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Create custom item data
      const customItemData = {
        ...processedData,
        company: company._id,
        companyCode, // Add companyCode field
        createdBy: req.user._id,
      };

      // Remove _csrf token if present
      delete customItemData._csrf;

      // Basic validation for required fields
      if (!customItemData.name || !customItemData.name.trim()) {
        return res.status(400).json({
          success: false,
          message: "Name is required",
        });
      }

      if (!customItemData.inputType) {
        return res.status(400).json({
          success: false,
          message: "Input type is required",
        });
      }


      // Create and save new custom item
      const customItem = new CustomItem(customItemData);
      await customItem.save();

      console.log("Custom item saved successfully:", customItem._id.toString());

      return res.json({
        success: true,
        message: "Custom income item created successfully",
        data: customItem,
      });
    } catch (error) {
      console.error("Error creating custom item:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to create custom item",
        error: error.message,
      });
    }
  }
);

// Add route for fetching custom items
router.get(
  "/:companyCode/settings/custom-items",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res
          .status(403)
          .render("error", { message: "Unauthorized access to this company" });
      }
      
      // Get regular custom items
      const CustomItem = require('../models/CustomItem');
      const regularCustomItems = await CustomItem.find({ companyCode })
        .sort({
          type: 1,
          name: 1,
        })
        .lean()
        .maxTimeMS(3000);
      
      // Get custom benefits
      const CustomBenefit = require('../models/CustomBenefit');
      const customBenefits = await CustomBenefit.find({ companyId: company._id })
        .sort({ name: 1 })
        .lean()
        .maxTimeMS(3000);
      
      // Convert custom benefits to the format expected by the template
      const formattedBenefits = customBenefits.map(benefit => ({
        _id: benefit._id,
        name: benefit.name,
        type: 'benefit',
        inputType: benefit.inputType,
        amount: benefit.amount,
        enableProRata: benefit.enableProRata,
        excludeFromAccounting: benefit.excludeFromAccounting,
        bargainingCouncilItem: benefit.bargainingCouncilItem,
        createdAt: benefit.createdAt
      }));
      
      // Combine both types of custom items
      const customItems = [...regularCustomItems, ...formattedBenefits];

      res.render("settings/custom-items", {
        company,
        user: req.user,
        companyCode,
        customItems: customItems || [], // Ensure customItems is always defined
        csrfToken: req.csrfToken(),
        activeTab: "accounting",
        activeSubTab: "custom-items",
      });
    } catch (error) {
      console.error("Error in custom-items route:", error);
      res
        .status(500)
        .render("error", { message: "Error loading custom items page" });
    }
  }
);

// Add API route for fetching custom items
router.get(
  "/:companyCode/api/settings/custom-items",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get regular custom items
      const CustomItem = require('../models/CustomItem');
      const regularCustomItems = await CustomItem.find({ companyCode })
        .sort({
        type: 1,
        name: 1,
        })
        .lean()
        .maxTimeMS(3000);
      
      // Get custom benefits
      const CustomBenefit = require('../models/CustomBenefit');
      const customBenefits = await CustomBenefit.find({ companyId: company._id })
        .sort({ name: 1 })
        .lean()
        .maxTimeMS(3000);
      
      // Convert custom benefits to the format expected by the client
      const formattedBenefits = customBenefits.map(benefit => ({
        _id: benefit._id,
        name: benefit.name,
        type: 'benefit',
        inputType: benefit.inputType,
        amount: benefit.amount,
        enableProRata: benefit.enableProRata,
        excludeFromAccounting: benefit.excludeFromAccounting,
        bargainingCouncilItem: benefit.bargainingCouncilItem,
        createdAt: benefit.createdAt
      }));
      
      // Combine both types of custom items
      const customItems = [...regularCustomItems, ...formattedBenefits];

      return res.json({
        success: true,
        data: customItems,
      });
    } catch (error) {
      console.error("Error fetching custom items:", error);
      
      // Check for timeout error
      const isTimeoutError = error.message && (
        error.message.includes('timeout') || 
        error.message.includes('ETIMEDOUT') || 
        error.name === 'MongoTimeoutError'
      );
      
      return res.status(500).json({
        success: false,
        message: isTimeoutError 
          ? "Database connection timed out. Please try again later." 
          : "Failed to fetch custom items",
        error: error.message,
      });
    }
  }
);

// Add routes for custom item types
router.get(
  "/:companyCode/settings/new-custom-income",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-income", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom income page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-deduction",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-deduction", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom deduction page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-allowance",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-allowance", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom allowance page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-benefit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-benefit", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom benefit page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-erContribution",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-erContribution", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom employer contribution page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-reimbursement",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-reimbursement", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom reimbursement page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

router.get(
  "/:companyCode/settings/new-custom-existing-item",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-existing-item", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom existing item page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);

// Create a simple in-memory cache for custom benefits
const benefitsCache = {};

// Add POST route for custom benefit
router.post(
  "/:companyCode/settings/accounting/custom-items/benefit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      // Get form data
      const { 
        name, 
        inputType, 
        value, 
        enableProRata, 
        excludeFromAccounting, 
        bargainingCouncilItem,
        type
      } = req.body;

      console.log("Form data received:", {
        name,
        inputType,
        value,
        type,
        enableProRata: enableProRata === 'on',
        excludeFromAccounting: excludeFromAccounting === 'on',
        bargainingCouncilItem: bargainingCouncilItem === 'on'
      });

      // Create new custom item
      const CustomItem = require('../models/CustomItem');
      
      const newItem = new CustomItem({
        companyCode,
        company: company._id,
        name,
        type: type || 'benefit',
        inputType,
        value: inputType === 'fixedAmount' ? parseFloat(value) : undefined,
        enableProRata: enableProRata === 'on',
        excludeFromAccounting: excludeFromAccounting === 'on',
        bargainingCouncilItem: bargainingCouncilItem === 'on'
      });

      const savedItem = await newItem.save();
      console.log("Custom benefit created successfully:", savedItem._id.toString());

      // Also create a CustomBenefit record for use in regularInputs.ejs
      const CustomBenefit = require('../models/CustomBenefit');
      const newBenefit = new CustomBenefit({
        companyId: company._id,
        name,
        inputType,
        amount: inputType === 'fixedAmount' ? parseFloat(value) : undefined,
        enableProRata: enableProRata === 'on',
        excludeFromAccounting: excludeFromAccounting === 'on',
        bargainingCouncilItem: bargainingCouncilItem === 'on'
      });

      const savedBenefit = await newBenefit.save();
      console.log("CustomBenefit record created successfully:", savedBenefit._id.toString());

      // Clear the cache for this company
      const companyIdStr = company._id.toString();
      if (benefitsCache[companyIdStr]) {
        delete benefitsCache[companyIdStr];
      }

      // Redirect to the custom-items page with the new item ID and name as query parameters
      return res.redirect(`/clients/${companyCode}/settings/accounting/custom-items?newItemId=${savedItem._id}&newItemName=${encodeURIComponent(savedItem.name)}`);
    } catch (error) {
      console.error("Error creating custom benefit:", error);
      
      // Check for timeout error
      const isTimeoutError = error.message && (
        error.message.includes('timeout') || 
        error.message.includes('ETIMEDOUT') || 
        error.name === 'MongoTimeoutError'
      );
      
      // Handle validation errors
      if (error.name === 'ValidationError') {
        const errorMessages = Object.values(error.errors).map(err => err.message);
        return res.render("settings/new-custom-benefit", {
          company: await Company.findOne({ companyCode: req.params.companyCode })
            .lean()
            .maxTimeMS(3000)
            .catch(() => ({ companyCode: req.params.companyCode })),
          user: req.user,
          csrfToken: req.csrfToken(),
          errorMessage: errorMessages.join(', '),
          formData: req.body
        });
      }
      
      return res.status(500).render("settings/new-custom-benefit", {
        company: await Company.findOne({ companyCode: req.params.companyCode })
          .lean()
          .maxTimeMS(3000)
          .catch(() => ({ companyCode: req.params.companyCode })),
        user: req.user,
        csrfToken: req.csrfToken(),
        errorMessage: isTimeoutError 
          ? "Database connection timed out. Please try again later." 
          : "Failed to create custom benefit: " + error.message,
        formData: req.body
      });
    }
  }
);

// Add route for custom benefits page
router.get(
  "/:companyCode/settings/custom-benefits",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res
          .status(403)
          .render("error", { message: "Unauthorized access to this company" });
      }
      
      // Get custom items of type 'benefit'
      let customItems = [];
      const companyIdStr = company._id.toString();
      
      // First check the cache
      if (benefitsCache[companyIdStr] && benefitsCache[companyIdStr].length > 0) {
        customItems = benefitsCache[companyIdStr];
      } else {
        // Try to get from database with timeout protection
        try {
          
          // Set a timeout promise
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Database query timeout')), 3000)
          );
          
          // Database query promise
          const queryPromise = CustomItem.find({ 
            company: company._id,
            type: 'benefit'
          }).sort({ name: 1 }).maxTimeMS(3000).lean();
          
          // Race the promises
          customItems = await Promise.race([queryPromise, timeoutPromise]);
          
          // Update cache
          if (customItems.length > 0) {
            benefitsCache[companyIdStr] = customItems;
          }
        } catch (dbError) {
          console.error("Error or timeout fetching custom benefits:", dbError);
          // Continue with empty benefits array or cached data
        }
      }
      
      // Get any flash messages
      const successMessage = req.flash('success')[0];
      const errorMessage = req.flash('error')[0];

      res.render("settings/custom-benefits", {
        company,
        user: req.user,
        companyCode,
        customItems: customItems || [],
        csrfToken: req.csrfToken(),
        activeTab: "accounting",
        activeSubTab: "custom-benefits",
        successMessage,
        errorMessage,
        usingCache: benefitsCache[companyIdStr] && benefitsCache[companyIdStr].length > 0
      });
    } catch (error) {
      console.error("Error in custom-benefits route:", error);
      
      // Try to use cached data if available
      let cachedBenefits = [];
      if (req.params.companyCode) {
        const company = await Company.findOne({ companyCode: req.params.companyCode }).catch(() => null);
        if (company && benefitsCache[company._id.toString()]) {
          cachedBenefits = benefitsCache[company._id.toString()];
        }
      }
      
      // Render the page with cached data or an error message
      res.render("settings/custom-benefits", {
        company: req.params.companyCode ? { companyCode: req.params.companyCode, name: "Your Company" } : { name: "Unknown Company" },
        user: req.user || {},
        companyCode: req.params.companyCode || "",
        customItems: cachedBenefits,
        csrfToken: req.csrfToken ? req.csrfToken() : "",
        activeTab: "accounting",
        activeSubTab: "custom-benefits",
        error: cachedBenefits.length > 0 ? 
          "Using cached data due to database connection issues." : 
          "There was an error loading custom benefits. Please try again later.",
        usingCache: cachedBenefits.length > 0
      });
    }
  }
);

// Add DELETE route for custom benefits
router.delete(
  "/:companyCode/settings/custom-benefits/:benefitId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, benefitId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to this company",
        });
      }

      // Delete the custom benefit (now a CustomItem of type 'benefit')
      const result = await CustomItem.findOneAndDelete({
        _id: benefitId,
        company: company._id,
        type: 'benefit'
      });

      if (!result) {
        return res.status(404).json({
          success: false,
          message: "Custom benefit not found",
        });
      }

      return res.json({
        success: true,
        message: "Custom benefit deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting custom benefit:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to delete custom benefit",
        error: error.message,
      });
    }
  }
);

// Add a simple test route to verify routing
router.get(
  "/:companyCode/settings/employee/test",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    try {
      
      const { companyCode } = req.params;
      
      // Find company
      const company = await Company.findOne({ companyCode });
      
      if (company) {
        res.send(`Test route working for company: ${company.name} (${companyCode})<br>CSRF Token: ${req.csrfToken()}`);
      } else {
        res.send(`Test route working but company not found for code: ${companyCode}<br>CSRF Token: ${req.csrfToken()}`);
      }
    } catch (error) {
      console.error("Error in test route:", error);
      res.status(500).send("Test route error: " + error.message);
    }
  }
);

router.get(
  "/:companyCode/settings/employee/employer-filing-details",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    console.log("1. Route accessed at:", new Date().toISOString());

    try {
      const company = await Company.findOne({ companyCode: req.params.companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);
      

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const employerFilingDetails = await EmployerFilingDetails.findOne({ 
        company: company._id
      })
        .lean()
        .maxTimeMS(3000);


      const templateData = {
        layout: "layouts/settings",
        company,
        employerFilingDetails: employerFilingDetails || {},
        csrfToken: req.csrfToken(),
        activeTab: 'employee',
        title: 'Employer Filing Details',
        messages: req.flash()
      };

      console.log("10. Template data prepared with keys:", Object.keys(templateData));

      res.render("settings/employer-filing-details", templateData);
      
    } catch (error) {
      console.error("\n=== Error in Employer Filing Details Route ===");
      console.error("Error occurred at:", new Date().toISOString());
      console.error("Error name:", error.name);
      console.error("Error message:", error.message);
      console.error("Error code:", error.code);
      console.error("Error stack:", error.stack);
      console.error("Request path when error occurred:", req.path);
      console.error("Company code when error occurred:", req.params.companyCode);

      req.flash("error_msg", "Error loading employer filing details. Please try again.");
      res.redirect(`/clients/${req.params.companyCode}/settings`);
    }
  }
);

router.post(
  "/:companyCode/settings/employee/employer-filing-details",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      // Set timeout for company query
      const company = await Company.findOne({ companyCode }).maxTimeMS(5000).lean();
      
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const {
        payeNumber,
        diplomaticIndemnity,
        sicMainGroup,
        sicLevel2,
        sicLevel3,
        sicLevel4,
        sicCode,
        telephoneNumber,
        sarsContactName,
        sarsContactSurname,
        sarsContactPosition
      } = req.body;


      // Set timeout for employer filing details query
      let filingDetails = await EmployerFilingDetails.findOne({ company: company._id }).maxTimeMS(5000);

      if (!filingDetails) {
        filingDetails = new EmployerFilingDetails({
          company: company._id,
          payeNumber,
          diplomaticIndemnity,
          sicMainGroup,
          sicLevel2,
          sicLevel3,
          sicLevel4,
          sicCode,
          telephoneNumber,
          sarsContactName,
          sarsContactSurname,
          sarsContactPosition
        });
      } else {
        filingDetails.payeNumber = payeNumber;
        filingDetails.diplomaticIndemnity = diplomaticIndemnity === 'true';
        filingDetails.sicMainGroup = sicMainGroup;
        filingDetails.sicLevel2 = sicLevel2;
        filingDetails.sicLevel3 = sicLevel3;
        filingDetails.sicLevel4 = sicLevel4;
        filingDetails.sicCode = sicCode;
        filingDetails.telephoneNumber = telephoneNumber;
        filingDetails.sarsContactName = sarsContactName;
        filingDetails.sarsContactSurname = sarsContactSurname;
        filingDetails.sarsContactPosition = sarsContactPosition;
      }

      await filingDetails.save({ maxTimeMS: 5000 });

      req.flash("success_msg", "Employer filing details updated successfully");
      res.redirect(`/${companyCode}/settings/employee/employer-filing-details`);
    } catch (error) {
      console.error("Error updating employer filing details:", error);
      req.flash("error_msg", "Error updating employer filing details. Please try again.");
      res.redirect(`/${companyCode}/settings/employee/employer-filing-details`);
    }
  }
);

router.get(
  "/clients/:companyCode/settings/employee/employer-filing-details",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const employerFilingDetails = await EmployerFilingDetails.findOne({ company: company._id })
        .lean()
        .maxTimeMS(3000);

      console.log("Rendering template with data:", {
        company: company.name,
        hasEmployerFilingDetails: !!employerFilingDetails
      });

      res.render("settings/employer-filing-details", {
        layout: "layouts/settings",
        company,
        employerFilingDetails: employerFilingDetails || {},
        csrfToken: req.csrfToken(),
        activeTab: 'employee',
        title: 'Employer Filing Details',
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error in employer-filing-details route:", error);
      req.flash("error_msg", "Error loading employer filing details. Please try again.");
      res.redirect(`/clients/${companyCode}/settings`);
    }
  }
);

// POST route for employer filing details
router.post(
  "/clients/:companyCode/settings/employee/employer-filing-details",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        return res.status(404).json({ success: false, message: "Company not found" });
      }

      const updateData = {
        company: company._id,
        payeNumber: req.body.payeNumber,
        diplomaticIndemnity: req.body.diplomaticIndemnity === 'true' || req.body.diplomaticIndemnity === true,
        sicMainGroup: req.body.sicMainGroup,
        sicLevel2: req.body.sicLevel2,
        sicLevel3: req.body.sicLevel3,
        sicLevel4: req.body.sicLevel4,
        sicCode: req.body.sicCode,
        telephoneNumber: req.body.telephoneNumber,
        sarsContactName: req.body.sarsContactName,
        sarsContactSurname: req.body.sarsContactSurname,
        sarsContactPosition: req.body.sarsContactPosition
      };

      await EmployerFilingDetails.findOneAndUpdate(
        { company: company._id },
        updateData,
        { upsert: true, new: true }
      ).maxTimeMS(3000);

      if (req.xhr) {
        return res.json({ success: true, message: "Employer filing details updated successfully" });
      }

      req.flash("success_msg", "Employer filing details updated successfully");
      res.redirect(`/clients/${companyCode}/settings/employee/employer-filing-details`);
    } catch (error) {
      console.error("Error updating employer filing details:", error);
      if (req.xhr) {
        return res.status(500).json({ success: false, message: "Error updating employer filing details" });
      }
      req.flash("error_msg", "Error updating employer filing details. Please try again.");
      res.redirect(`/clients/${companyCode}/settings/employee/employer-filing-details`);
    }
  }
);

// Job Grades Route
router.get(
  "/clients/:companyCode/settings/employee/job-grades",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const jobGrades = await JobGrade.find({ company: company._id })
        .lean()
        .maxTimeMS(3000);


      return res.render("settings/job-grades", {
        layout: "layouts/settings",
        company,
        jobGrades,
        activeTab: 'employee',
        currentPage: 'job-grades',
        title: 'Job Grades',
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });

    } catch (error) {
      console.error("Error in job-grades route:", error);
      req.flash("error_msg", "Error loading job grades. Please try again.");
      return res.redirect(`/clients/${companyCode}/settings/employee`);
    }
  }
);

// Employee Numbers Route
router.get(
  "/clients/:companyCode/settings/employee/employee-numbers",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const settings = await EmployeeNumberSettings.findOne({ company: company._id })
        .lean()
        .maxTimeMS(3000);

      res.render("settings/employee-numbers", {
        layout: "layouts/settings",
        company,
        employeeNumberSettings: settings || {},
        activeTab: 'employee',
        currentPage: 'employee-numbers',
        title: 'Employee Numbers',
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error loading employee numbers settings:", error);
      req.flash("error_msg", "Error loading settings. Please try again.");
      return res.redirect(`/clients/${companyCode}/settings/employee`);
    }
  }
);

// Update Employee Numbers Settings
router.post(
  "/clients/:companyCode/settings/employee/employee-numbers",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      // Set timeout for company query
      const company = await Company.findOne({ companyCode }).maxTimeMS(5000).lean();
      
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      const {
        employeeNumberMode,
        firstCompanyEmployeeNumber,
        updateExistingEmployees,
      } = req.body;

      console.log("Request body:", {
        employeeNumberMode,
        firstCompanyEmployeeNumber,
        updateExistingEmployees,
      });

      // Set timeout for employee number settings query
      let settings = await EmployeeNumberSettings.findOne({ company: company._id }).maxTimeMS(5000);

      if (!settings) {
        settings = new EmployeeNumberSettings({
          company: company._id,
          employeeNumberMode: employeeNumberMode || "manual",
          firstCompanyEmployeeNumber: firstCompanyEmployeeNumber || "1000",
        });
      } else {
        settings.employeeNumberMode = employeeNumberMode || "manual";
        settings.firstCompanyEmployeeNumber = firstCompanyEmployeeNumber || "1000";
      }

      await settings.save({ maxTimeMS: 5000 });

      if (updateExistingEmployees === "true" && employeeNumberMode === "automatic") {
        // Set timeout for employee update query
        const employees = await Employee.find({ company: company._id }).maxTimeMS(5000);
        let nextNumber = parseInt(firstCompanyEmployeeNumber);

        for (const employee of employees) {
          employee.employeeNumber = nextNumber.toString();
          await employee.save({ maxTimeMS: 5000 });
          nextNumber++;
        }

      }

      req.flash("success_msg", "Employee number settings updated successfully");
      res.redirect(`/${companyCode}/settings/employee/employee-numbers`);
    } catch (error) {
      console.error("Error updating employee numbers settings:", error);
      req.flash("error_msg", "Error updating employee numbers settings. Please try again.");
      res.redirect(`/${companyCode}/settings/employee/employee-numbers`);
    }
  }
);

// Employee Settings Routes
router.get(
  "/clients/:companyCode/settings/employee",
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;

    try {
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/employee", {
        layout: "layouts/settings",
        company,
        activeTab: 'employee',
        title: 'Employee Settings',
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error loading employee settings:", error);
      req.flash("error_msg", "Error loading settings. Please try again.");
      res.redirect("/clients");
    }
  }
);

// Add DELETE route for custom items
router.delete(
  "/:companyCode/settings/custom-items/:itemId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, itemId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to this company",
        });
      }

      // Delete the custom item
      const result = await CustomItem.findOneAndDelete({
        _id: itemId,
        company: company._id
      });

      if (!result) {
        return res.status(404).json({
          success: false,
          message: "Custom item not found",
        });
      }

      return res.json({
        success: true,
        message: `Custom item "${result.name}" deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting custom item:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to delete custom item",
        error: error.message,
      });
    }
  }
);

// Add route for editing custom items
router.get(
  "/:companyCode/settings/custom-items/edit/:itemId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, itemId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      // Check if the user has access to this company
      // Convert ObjectIds to strings for proper comparison
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      const companyIdString = company._id.toString();

      console.log("Authorization check:", {
        userCompanyIds,
        companyIdString,
        hasAccess: userCompanyIds.includes(companyIdString)
      });

      if (!userCompanyIds.includes(companyIdString)) {
        return res
          .status(403)
          .render("error", { message: "Unauthorized access to this company" });
      }

      // Find the custom item
      const customItem = await CustomItem.findOne({
        _id: itemId,
        company: company._id
      }).maxTimeMS(3000).lean();

      if (!customItem) {
        req.flash("error", "Custom item not found");
        if (customItem && customItem.type === 'benefit') {
          return res.redirect(`/clients/${companyCode}/settings/custom-benefits`);
        }
        return res.redirect(`/clients/${companyCode}/settings/accounting/custom-items`);
      }

      // Determine which edit template to render based on the item type
      let template;
      let activeSubTab;
      
      if (customItem.type === 'benefit') {
        template = "settings/edit-custom-benefit";
        activeSubTab = "custom-benefits";
      } else if (customItem.type === 'deduction') {
        template = "settings/edit-custom-deduction";
        activeSubTab = "custom-deductions";
      } else {
        template = "settings/edit-custom-item";
        activeSubTab = "custom-items";
      }

      res.render(template, {
        company,
        user: req.user,
        companyCode,
        customItem,
        csrfToken: req.csrfToken(),
        activeTab: "accounting",
        activeSubTab,
        successMessage: req.flash("success")[0],
        errorMessage: req.flash("error")[0]
      });
    } catch (error) {
      console.error("Error in edit custom item route:", error);
      req.flash("error", "Error loading custom item");
      return res.redirect(`/clients/${req.params.companyCode}/settings/accounting/custom-items`);
    }
  }
);

// Add route for updating custom items
router.post(
  "/:companyCode/settings/custom-items/edit/:itemId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, itemId } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to this company"
        });
      }

      // Find the custom item
      const customItem = await CustomItem.findOne({
        _id: itemId,
        company: company._id
      });

      if (!customItem) {
        return res.status(404).json({
          success: false,
          message: "Custom item not found"
        });
      }

      // Update the custom item with the form data
      const {
        name,
        description,
        value,
        inputType,
        calculationType,
        enableProRata,
        excludeFromAccounting,
        bargainingCouncilItem
      } = req.body;

      // Update fields
      customItem.name = name;
      customItem.description = description || '';
      
      // Update type-specific fields
      if (customItem.type === 'benefit') {
        customItem.value = value !== undefined && value !== '' ? parseFloat(value) : undefined;
        customItem.inputType = inputType;
        customItem.enableProRata = enableProRata === 'on';
        customItem.excludeFromAccounting = excludeFromAccounting === 'on';
        customItem.bargainingCouncilItem = bargainingCouncilItem === 'on';
      } else {
        customItem.value = value !== undefined && value !== '' ? parseFloat(value) : undefined;
        customItem.calculationType = calculationType;
      }

      await customItem.save();

      // Determine redirect URL based on item type
      let redirectUrl;
      if (customItem.type === 'benefit') {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-items`;
      } else if (customItem.type === 'deduction') {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-deductions`;
      } else {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-items`;
      }

      req.flash("success", `${customItem.name} updated successfully`);
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error("Error updating custom item:", error);
      req.flash("error", "Failed to update custom item");
      return res.redirect(`/clients/${req.params.companyCode}/settings/accounting/custom-items`);
    }
  }
);

router.post(
  ["/clients/:companyCode/settings/custom-items", "/:companyCode/settings/custom-items"],
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        return res
          .status(404)
          .render("error", { message: "Company not found" });
      }

      // Check if the user has access to this company
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        return res
          .status(403)
          .render("error", { message: "Unauthorized access to this company" });
      }

      // Get form data
      const {
        name,
        description,
        type,
        value,
        inputType,
        calculationType,
        enableProRata,
        excludeFromAccounting,
        bargainingCouncilItem
      } = req.body;


      // Create new custom item
      const customItem = new CustomItem({
        name,
        description: description || '',
        type: type || 'earning', // Default to earning if not specified
        company: company._id,
        companyCode, // Add companyCode field
        value: value !== undefined && value !== '' ? parseFloat(value) : undefined,
        calculationType,
        inputType,
        enableProRata: enableProRata === 'on',
        excludeFromAccounting: excludeFromAccounting === 'on',
        bargainingCouncilItem: bargainingCouncilItem === 'on'
      });

      await customItem.save();

      // Determine redirect URL based on item type
      let redirectUrl;
      let successMessage;
      
      if (type === 'benefit') {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-items`;
        successMessage = `Custom benefit "${name}" created successfully`;
      } else if (type === 'deduction') {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-deductions`;
        successMessage = `Custom deduction "${name}" created successfully`;
      } else {
        redirectUrl = `/clients/${companyCode}/settings/accounting/custom-items`;
        successMessage = `Custom item "${name}" created successfully`;
      }

      // Add query parameters for notification
      redirectUrl += `?newItemId=${customItem._id}&newItemName=${encodeURIComponent(name)}`;
      
      req.flash("success", successMessage);
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error("Error creating custom item:", error);
      req.flash("error", "Failed to create custom item: " + error.message);
      return res.redirect(`/clients/${req.params.companyCode}/settings/accounting/custom-items`);
    }
  }
);

// Add a route with the accounting segment
router.get(
  "/:companyCode/settings/accounting/new-custom-benefit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode })
        .select('_id companyCode name')
        .lean()
        .maxTimeMS(3000);

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/clients");
      }

      res.render("settings/new-custom-benefit", {
        company,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error loading new custom benefit page:", error);
      res.status(500).render("error", { message: "Error loading page" });
    }
  }
);



// WhatsApp Invite All Employees Route (duplicate for both possible mount points)
const whatsappInviteHandler = [
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
    try {
      console.log('[WhatsApp Invite Route] CSRF Token (body):', req.body?._csrf);
      console.log('[WhatsApp Invite Route] CSRF Token (header):', req.headers['csrf-token']);
      console.log('[WhatsApp Invite Route] CSRF Token (cookie):', req.cookies?._csrf);
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error('[WhatsApp Invite Route] Company not found for code:', companyCode);
        return res.status(404).json({ success: false, error: 'Company not found' });
      }
      console.log('[WhatsApp Invite Route] Company found:', company._id.toString(), company.name);
      // Find all active employees with a mobile number in any field
      console.log('[WhatsApp Invite Route] Querying employees (any mobile/phone field, status: "Active")...');
      const employees = await Employee.find({
        company: company._id,
        status: 'Active',
        $or: [
          { 'personalDetails.mobileNumber': { $exists: true, $ne: '' } },
          { mobileNumber: { $exists: true, $ne: '' } },
          { phone: { $exists: true, $ne: '' } }
        ]
      });
      if (!employees.length) {
        return res.status(400).json({ success: false, error: 'No employees with mobile numbers found.' });
      }
      let sent = 0, failed = 0, errors = [];
      for (const emp of employees) {
        // Pick the first available number
        const mobile = emp.personalDetails?.mobileNumber || emp.mobileNumber || emp.phone;
        if (!mobile) {
          continue;
        }
        try {
          console.log(`[WhatsApp Invite Route] Sending WhatsApp invite to employee: ${emp._id} (${mobile})`);
          await whatsappService.sendTemplateMessage(mobile, 'employee_invite');
          sent++;
        } catch (err) {
          failed++;
          errors.push({ employee: emp._id, mobile, error: err.message });
          console.error(`[WhatsApp Invite Route] Error sending WhatsApp invite to ${mobile}:`, err.message, err.stack);
        }
      }
      return res.json({
        success: true,
        message: `Invitations sent: ${sent}, failed: ${failed}`,
        sent,
        failed,
        errors
      });
    } catch (error) {
      console.error('[WhatsApp Invite Route] FATAL ERROR:', error.message, error.stack);
      console.log('[WhatsApp Invite Route] END (with error)');
      return res.status(500).json({ success: false, error: error.message, stack: error.stack });
    }
  }
];

router.post('/clients/:companyCode/settings/whatsapp/invite', ...whatsappInviteHandler);
router.post('/:companyCode/settings/whatsapp/invite', ...whatsappInviteHandler);

// WhatsApp Invite Selected Employees Route (duplicate for both possible mount points)
const whatsappInviteSelectedHandler = [
  ensureAuthenticated,
  checkCompanyMiddleware,
  csrfProtection,
  async (req, res) => {
  try {
    const { companyCode } = req.params;
    const { employeeIds } = req.body;
    if (!Array.isArray(employeeIds) || !employeeIds.length) {
      return res.status(400).json({ success: false, error: 'No employees selected.' });
    }
    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({ success: false, error: 'Company not found' });
    }
    // Find selected employees in this company
    const employees = await Employee.find({
      _id: { $in: employeeIds },
      company: company._id,
      status: 'Active'
    });
    if (!employees.length) {
      return res.status(400).json({ success: false, error: 'No valid employees found.' });
    }
    let sent = 0, failed = 0, errors = [];
    for (const emp of employees) {
      const mobile = emp.personalDetails?.mobileNumber || emp.mobileNumber || emp.phone;
      if (!mobile) {
        failed++;
        errors.push({ employee: emp._id, error: 'No valid mobile/phone number' });
        continue;
      }
      try {
        await whatsappService.sendTemplateMessage(mobile, 'employee_invite');
        sent++;
      } catch (err) {
        failed++;
        errors.push({ employee: emp._id, mobile, error: err.message });
      }
    }
    return res.json({
      success: true,
      message: `Invitations sent: ${sent}, failed: ${failed}`,
      sent,
      failed,
      errors
    });
  } catch (error) {
    console.error('[WhatsApp Invite Selected Route] FATAL ERROR:', error.message, error.stack);
    return res.status(500).json({ success: false, error: error.message, stack: error.stack });
  }
  }
];

router.post('/clients/:companyCode/settings/whatsapp/invite-selected', ...whatsappInviteSelectedHandler);
router.post('/:companyCode/settings/whatsapp/invite-selected', ...whatsappInviteSelectedHandler);

module.exports = router;
