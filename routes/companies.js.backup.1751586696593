const express = require("express");
const router = express.Router();
const Company = require("../models/Company");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayRun = require("../models/payRun");
const User = require("../models/user");
const EmployerDetails = require("../models/employerDetails");
const TransferRequest = require("../models/transferRequest");
const DeactivationRequest = require("../models/deactivationRequest");
const passport = require("passport");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const nodemailer = require("nodemailer");
const crypto = require("crypto");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const mongoose = require("mongoose");

// Setup nodemailer with <PERSON>oho SMTP
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtppro.zoho.com",
  port: parseInt(process.env.EMAIL_PORT) || 587,
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  debug: true,
  logger: true,
});

// Add more detailed error handling for transporter verification
transporter.verify(function (error, success) {
  if (error) {
    console.error("Email server error:", error);
    console.error("Email configuration:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      user: process.env.EMAIL_USER ? "Set" : "Not set",
      pass: process.env.EMAIL_PASS ? "Set" : "Not set",
    });
  } else {
    console.log("Using email configuration:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      user: process.env.EMAIL_USER,
    });
  }
});

// Generate OTP
function generateOTP() {
  return crypto.randomBytes(3).toString("hex").toUpperCase();
}

// List companies (protected route)
router.get("/", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Fetch user with populated company data
    const user = await User.findById(req.user._id)
      .populate({
        path: 'companies',
        select: '_id name companyCode createdAt isActive'
      })
      .populate({
        path: 'currentCompany',
        select: '_id name companyCode'
      })
      .populate({
        path: 'defaultCompany',
        select: '_id name companyCode'
      })
      .exec();

    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/auth/login");
    }

    // Get employee counts for each company using aggregation
    const employeeCounts = await Employee.aggregate([
      {
        $match: {
          company: { $in: user.companies.map(c => c._id) }
        }
      },
      {
        $group: {
          _id: '$company',
          count: { $sum: 1 }
        }
      }
    ]);

    // Create a map of company ID to employee count
    const employeeCountMap = employeeCounts.reduce((map, item) => {
      map[item._id.toString()] = item.count;
      return map;
    }, {});

    // Add employee count to each company
    const companiesWithEmployees = user.companies.map(company => ({
      ...company.toObject(),
      employeeCount: employeeCountMap[company._id.toString()] || 0
    }));
    
    // Make company data available to the view
    res.locals.user = user;
    res.locals.companies = companiesWithEmployees;
    res.locals.currentCompany = user.currentCompany;
    res.locals.defaultCompany = user.defaultCompany;
    res.locals.moment = require('moment'); // Add moment to locals

    // Prepare template data
    const templateData = {
      user: user,
      companies: companiesWithEmployees,
      company: user.currentCompany,
      csrfToken: req.csrfToken(),
      moment: require('moment'), // Add moment to template data
      errors: req.flash('error'),
      success: req.flash('success')
    };

    res.render("companies", templateData);
  } catch (error) {
    console.error("Error fetching companies:", error);
    req.flash("error", "Failed to load companies");
    res.redirect("/");
  }
});

// Keep the existing select route for compatibility
router.get("/select", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate({
        path: 'companies',
        select: '_id name companyCode'
      })
      .populate({
        path: 'currentCompany',
        select: '_id name companyCode'
      })
      .populate({
        path: 'defaultCompany',
        select: '_id name companyCode'
      })
      .exec();

    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/auth/login");
    }

    if (!user.companies || user.companies.length === 0) {
      req.flash("error", "No companies found");
      return res.redirect("/companies/create");
    }

    // Make company data available to the view
    res.locals.user = user;
    res.locals.companies = user.companies;
    res.locals.currentCompany = user.currentCompany;
    res.locals.defaultCompany = user.defaultCompany;

    res.render("select-company", {
      user: user,
      companies: user.companies,
      currentCompany: user.currentCompany,
      csrfToken: req.csrfToken(),
      messages: req.flash()
    });
  } catch (error) {
    console.error("Error in company selection:", error);
    req.flash("error", "Error loading companies");
    res.redirect("/");
  }
});

router.post(
  "/create",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      // Generate a unique company code
      const companyCode = generateUniqueCompanyCode();

      const newCompany = new Company({
        name: req.body.name,
        owner: req.user._id,
        companyCode: companyCode,
      });

      const savedCompany = await newCompany.save();

      // Update the user's companies array
      await User.findByIdAndUpdate(req.user._id, {
        $push: { companies: savedCompany._id },
      });

      res.json({ success: true, message: "Company created successfully" });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error creating company",
        error: error.message,
      });
    }
  }
);

// Add this function to generate a unique company code
function generateUniqueCompanyCode() {
  // This is a simple example. You might want to make this more sophisticated
  // and ensure it's truly unique in your database.
  return "COM" + crypto.randomBytes(3).toString("hex").toUpperCase();
}

// Update the switch company route
router.post(
  "/switch/:companyId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyId } = req.params;
      const user = await User.findById(req.user._id);

      if (!user.companies.includes(companyId)) {
        req.flash("error", "Unauthorized access to company");
        return res.redirect("/companies/select");
      }

      // Update user's current company
      user.currentCompany = companyId;
      await user.save();

      // Get company code for redirect
      const company = await Company.findById(companyId);
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Set success message
      req.flash("success", `Switched to ${company.name}`);

      // Redirect to company dashboard
      res.redirect(`/clients/${company.companyCode}/dashboard`);
    } catch (error) {
      console.error("Error switching company:", error);
      req.flash("error", "Failed to switch company");
      res.redirect("/companies/select");
    }
  }
);

// Create a new company (protected route)
router.get("/createCompany", ensureAuthenticated, (req, res) => {
  res.render("createCompany");
});

router.post("/createCompany", ensureAuthenticated, async (req, res) => {
  const { name } = req.body;

  try {
    const company = new Company({
      name,
      owner: req.user._id,
    });

    const employerDetails = new EmployerDetails({
      company: company._id,
      tradingName: name,
    });

    company.employerDetails = employerDetails._id;

    await company.save();
    await employerDetails.save();

    req.user.companies.push(company._id);
    await req.user.save();

    res.redirect("/companies");
  } catch (error) {
    console.error("Error creating company:", error);
    res.status(500).send("Internal Server Error");
  }
});

router.get(
  "/:companyId/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    const companyId = req.params.companyId;
    try {
      const company = await Company.findById(companyId).populate(
        "employerDetails"
      );
      if (!company) {
        return res.status(404).send("Company not found");
      }
      const employees = await Employee.find({ company: companyId });
      res.render("employeeManagement", { company, employees });
    } catch (error) {
      console.error("Error fetching employee management data:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

// Edit company details (protected route)
router.get("/edit/:companyId", ensureAuthenticated, async (req, res) => {
  const companyId = req.params.companyId;

  try {
    const company = await Company.findById(companyId).populate(
      "employerDetails"
    );

    if (!company) {
      return res.status(404).send("Company not found");
    }

    if (String(company.owner) !== String(req.user._id)) {
      return res.status(403).send("Unauthorized");
    }

    res.render("edit-company", { company, user: req.user });
  } catch (error) {
    console.error(`Failed to edit the company: ${error}`);
    res.status(500).send("Internal Server Error");
  }
});

// Update company details
router.post("/edit/:companyId", ensureAuthenticated, async (req, res) => {
  const companyId = req.params.companyId;
  const { name, tradingName } = req.body;

  try {
    const company = await Company.findById(companyId);
    if (!company) {
      return res.status(404).send("Company not found");
    }

    if (String(company.owner) !== String(req.user._id)) {
      return res.status(403).send("Unauthorized");
    }

    company.name = name;
    await company.save();

    let employerDetails = await EmployerDetails.findOne({ company: companyId });
    if (!employerDetails) {
      employerDetails = new EmployerDetails({ company: companyId });
    }
    employerDetails.tradingName = tradingName;
    await employerDetails.save();

    res.redirect("/companies");
  } catch (error) {
    console.error(`Failed to update the company: ${error}`);
    res.status(500).send("Internal Server Error");
  }
});

// Fetch active companies for dashboard
router.get("/active", ensureAuthenticated, async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const companies = await Company.aggregate([
      { $match: { _id: { $in: req.user.companies } } },
      {
        $lookup: {
          from: "employees",
          localField: "_id",
          foreignField: "company",
          as: "employees",
        },
      },
      {
        $project: {
          name: 1,
          employeeCount: { $size: "$employees" },
        },
      },
    ]);

    res.json(companies);
  } catch (error) {
    console.error("Error fetching active companies:", error);
    res.status(500).json({ message: "Error fetching companies" });
  }
});

// Add new company route
router.get("/new", ensureAuthenticated, (req, res) => {
  res.render("employerDetailsSetup");
});

// Ensure this new transfer route is in place and correctly implemented
router.post(
  "/transfer/:companyId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const company = await Company.findById(req.params.companyId);
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!company || !userCompanyIds.includes(company._id.toString())) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      const { recipientEmail } = req.body;
      const otp = generateOTP();

      // Store the transfer request
      const transferRequest = new TransferRequest({
        company: company._id,
        currentOwner: req.user._id,
        recipientEmail,
        otp,
      });
      await transferRequest.save();

      // Prepare email content
      const mailOptions = {
        from: {
          name: process.env.EMAIL_FROM_NAME || "PSS Payroll",
          address: process.env.EMAIL_USER,
        },
        to: recipientEmail,
        subject: `Company Transfer Request: ${company.name}`,
        html: `
        <p>You have received a request to transfer ownership of the company "${company.name}".</p>
        <p>Your One-Time Password (OTP) is: <strong>${otp}</strong></p>
        <p>Please use this OTP to accept the transfer at the following link:</p>
        <a href="${process.env.BASE_URL}/companies/accept-transfer/${transferRequest._id}">Accept Transfer</a>
        <p>If you did not request this transfer, please ignore this email.</p>
      `,
      };

      // Send email with detailed error handling
      try {
        console.log("Attempting to send email with config:", {
          host: process.env.EMAIL_HOST,
          port: process.env.EMAIL_PORT,
          auth: {
            user: process.env.EMAIL_USER ? "Set" : "Not set",
            pass: process.env.EMAIL_PASS ? "Set" : "Not set",
          },
        });

        await transporter.sendMail(mailOptions);
        req.flash("success", "Transfer request sent successfully");
      } catch (emailError) {
        console.error("Email sending failed:", emailError);
        console.error("Email configuration:", {
          host: process.env.EMAIL_HOST,
          port: process.env.EMAIL_PORT,
          auth: {
            user: process.env.EMAIL_USER ? "Set" : "Not set",
            pass: process.env.EMAIL_PASS ? "Set" : "Not set",
          },
        });
        req.flash(
          "warning",
          "Transfer initiated but email notification failed"
        );
      }

      res.render("transfer-initiated", {
        company,
        recipientEmail,
        user: req.user,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error initiating transfer:", error);
      req.flash("error", "Failed to initiate transfer");
      res.redirect(`/companies/transfer/${req.params.companyId}`);
    }
  }
);

// Update the deactivate company route
router.post(
  "/deactivate/:companyId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const company = await Company.findById(req.params.companyId);
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!company || !userCompanyIds.includes(company._id.toString())) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      const { reason, deleteImmediately } = req.body;

      if (deleteImmediately) {
        // Delete the company and all associated data
        await Company.findByIdAndDelete(company._id);
        // Remove company from user's companies list
        req.user.companies = req.user.companies.filter(
          (c) => !c.equals(company._id)
        );
        await req.user.save();
        return res.json({
          success: true,
          message: "Company deleted successfully",
        });
      }

      // Create a deactivation request
      const deactivationRequest = new DeactivationRequest({
        company: company._id,
        requestedBy: req.user._id,
        reason: reason,
      });

      await deactivationRequest.save();

      // Update company status
      company.isActive = false;
      await company.save();

      res.json({ success: true, message: "Company deactivated successfully" });
    } catch (error) {
      console.error("Error deactivating company:", error);
      res
        .status(500)
        .json({ success: false, message: "Internal Server Error" });
    }
  }
);

// Company settings route
router.get("/:companyId/settings", ensureAuthenticated, async (req, res) => {
  const companyId = req.params.companyId;

  try {
    const company = await Company.findById(companyId).populate(
      "employerDetails"
    );

    if (!company) {
      return res.status(404).send("Company not found");
    }

    if (String(company.owner) !== String(req.user._id)) {
      return res.status(403).send("Unauthorized");
    }

    // If employerDetails doesn't exist, create a default object
    const employerDetails = company.employerDetails || { tradingName: "" };

    res.render("company-settings", {
      company,
      employerDetails,
      user: req.user,
    });
  } catch (error) {
    console.error(`Failed to load company settings: ${error}`);
    res.status(500).send("Internal Server Error");
  }
});

// Update company settings
router.post("/:companyId/settings", ensureAuthenticated, async (req, res) => {
  const companyId = req.params.companyId;
  const { name, tradingName } = req.body;

  try {
    const company = await Company.findById(companyId);
    if (!company) {
      return res.status(404).send("Company not found");
    }

    if (String(company.owner) !== String(req.user._id)) {
      return res.status(403).send("Unauthorized");
    }

    company.name = name;
    await company.save();

    let employerDetails = await EmployerDetails.findOne({ company: companyId });
    if (!employerDetails) {
      employerDetails = new EmployerDetails({ company: companyId });
    }
    employerDetails.tradingName = tradingName;
    await employerDetails.save();

    res.redirect("/companies");
  } catch (error) {
    console.error(`Failed to update company settings: ${error}`);
    res.status(500).send("Internal Server Error");
  }
});

// Remove these routes
// router.get("/:companyId/settings", ensureAuthenticated, async (req, res) => { ... });
// router.post("/:companyId/settings", ensureAuthenticated, async (req, res) => { ... });

// Instead, add this route to handle company updates
router.post("/update/:companyId", ensureAuthenticated, async (req, res) => {
  const companyId = req.params.companyId;
  const { name, tradingName } = req.body;

  try {
    const company = await Company.findById(companyId);
    if (!company) {
      return res.status(404).send("Company not found");
    }

    if (String(company.owner) !== String(req.user._id)) {
      return res.status(403).send("Unauthorized");
    }

    company.name = name;
    await company.save();

    let employerDetails = await EmployerDetails.findOne({ company: companyId });
    if (!employerDetails) {
      employerDetails = new EmployerDetails({ company: companyId });
    }
    employerDetails.tradingName = tradingName;
    await employerDetails.save();

    res.redirect("/settings?companyId=" + companyId);
  } catch (error) {
    console.error(`Failed to update company: ${error}`);
    res.status(500).send("Internal Server Error");
  }
});

// Transfer company page
router.get(
  "/transfer/:companyId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const company = await Company.findById(req.params.companyId);
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!company || !userCompanyIds.includes(company._id.toString())) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      res.render("transfer-company", {
        company,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading transfer page:", error);
      req.flash("error", "Error loading transfer page");
      res.redirect("/companies/select");
    }
  }
);

// Accept transfer
router.get("/accept-transfer/:transferId", async (req, res) => {
  try {
    const transferRequest = await TransferRequest.findById(
      req.params.transferId
    ).populate("company");
    if (!transferRequest || transferRequest.status !== "pending") {
      return res.status(404).send("Invalid or expired transfer request");
    }

    res.render("accept-transfer", { transferRequest });
  } catch (error) {
    console.error("Error loading accept transfer page:", error);
    res.status(500).send("Internal Server Error");
  }
});

router.post("/accept-transfer/:transferId", async (req, res) => {
  try {
    const { otp } = req.body;
    const transferRequest = await TransferRequest.findById(
      req.params.transferId
    ).populate("company currentOwner");

    if (!transferRequest || transferRequest.status !== "pending") {
      return res.status(404).send("Invalid or expired transfer request");
    }

    if (transferRequest.otp !== otp) {
      return res.status(400).send("Invalid OTP");
    }

    // Update company ownership
    const company = transferRequest.company;
    const newOwner = await User.findOne({
      email: transferRequest.recipientEmail,
    });

    if (!newOwner) {
      return res.status(404).send("Recipient user not found");
    }

    company.owner = newOwner._id;
    await company.save();

    // Update user's company list
    const currentOwner = transferRequest.currentOwner;
    currentOwner.companies = currentOwner.companies.filter(
      (c) => !c.equals(company._id)
    );
    await currentOwner.save();

    newOwner.companies.push(company._id);
    await newOwner.save();

    // Update transfer request status
    transferRequest.status = "accepted";
    await transferRequest.save();

    // Notify original owner
    const mailOptions = {
      from: process.env.GMAIL_USER,
      to: currentOwner.email,
      subject: `Company Transfer Completed: ${company.name}`,
      html: `<p>The ownership of "${company.name}" has been successfully transferred to ${newOwner.email}.</p>`,
    };

    await transporter.sendMail(mailOptions);

    res.send("Transfer completed successfully");
  } catch (error) {
    console.error("Error accepting transfer:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Reject transfer
router.post("/reject-transfer/:transferId", async (req, res) => {
  try {
    const transferRequest = await TransferRequest.findById(
      req.params.transferId
    ).populate("company currentOwner");

    if (!transferRequest || transferRequest.status !== "pending") {
      return res.status(404).send("Invalid or expired transfer request");
    }

    // Update transfer request status
    transferRequest.status = "rejected";
    await transferRequest.save();

    // Notify original owner
    const mailOptions = {
      from: process.env.GMAIL_USER,
      to: transferRequest.currentOwner.email,
      subject: `Company Transfer Rejected: ${transferRequest.company.name}`,
      html: `<p>The transfer of "${transferRequest.company.name}" to ${transferRequest.recipientEmail} has been rejected.</p>`,
    };

    await transporter.sendMail(mailOptions);

    res.send("Transfer request rejected");
  } catch (error) {
    console.error("Error rejecting transfer:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Update the reactivate company route
router.post(
  "/reactivate/:companyId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const company = await Company.findById(req.params.companyId);
      if (!company || !req.user.companies.includes(company._id)) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Check if there's a pending deactivation request and remove it
      await DeactivationRequest.deleteOne({
        company: company._id,
        status: "pending",
      });

      company.isActive = true;
      await company.save();

      res.json({
        success: true,
        message: "Company reactivated successfully",
      });
    } catch (error) {
      console.error("Error reactivating company:", error);
      res.status(500).json({
        success: false,
        message: "Internal Server Error",
      });
    }
  }
);

router.get("/select-company", ensureAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate("companies");
    res.render("select-company", { user: user });
  } catch (error) {
    console.error("Error loading select-company page:", error);
    res.status(500).send("Error loading select-company page");
  }
});

// Company selection page
router.get(
  "/select",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      // Fetch user with populated company data
      const user = await User.findById(req.user._id)
        .populate({
          path: 'companies',
          select: '_id name companyCode'
        })
        .populate({
          path: 'currentCompany',
          select: '_id name companyCode'
        })
        .populate({
          path: 'defaultCompany',
          select: '_id name companyCode'
        })
        .exec();

      if (!user) {
        req.flash("error", "User not found");
        return res.redirect("/auth/login");
      }

      // If no companies, redirect to create
      if (!user.companies || user.companies.length === 0) {
        req.flash("error", "No companies found");
        return res.redirect("/companies/create");
      }

      // Make company data available to the view
      res.locals.user = user;
      res.locals.companies = user.companies;
      res.locals.currentCompany = user.currentCompany;
      res.locals.defaultCompany = user.defaultCompany;

      // Render the selection page
      res.render("select-company", {
        user: user,
        companies: user.companies,
        currentCompany: user.currentCompany,
        csrfToken: req.csrfToken(),
        messages: req.flash()
      });
    } catch (error) {
      console.error("Error in company selection:", error);
      req.flash("error", "Error loading companies");
      res.redirect("/");
    }
  }
);

// Handle company selection
router.post("/select/:companyId", ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { companyId } = req.params;
    
    // Find user and validate company access
    const user = await User.findById(req.user._id)
      .populate({
        path: 'companies',
        select: '_id name companyCode'
      })
      .populate({
        path: 'currentCompany',
        select: '_id name companyCode'
      })
      .populate({
        path: 'defaultCompany',
        select: '_id name companyCode'
      })
      .exec();
    
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/auth/login");
    }

    const company = user.companies.find(c => c._id.toString() === companyId);
    if (!company) {
      req.flash("error", "Unauthorized access to company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = company;
    if (!user.defaultCompany) {
      user.defaultCompany = company;
    }

    // Save to database
    await User.findByIdAndUpdate(user._id, {
      currentCompany: company._id,
      defaultCompany: user.defaultCompany._id
    }).exec();

    // Update session
    req.user = user;
    req.session.user = user;
    req.session.currentCompany = company;
    
    // Save session explicitly
    await new Promise((resolve, reject) => {
      req.session.save(err => {
        if (err) {
          console.error("Error saving session:", err);
          reject(err);
        } else {
          resolve();
        }
      });
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Update the deactivate route to properly handle deletion
router.post(
  "/:companyId/deactivate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyId } = req.params;
      const { reason, deleteImmediately } = req.body;
      console.log("Deactivate request:", {
        companyId,
        deleteImmediately,
        reason,
      });

      // Validate company exists
      const company = await Company.findById(companyId);
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Check if user has permission
      const user = await User.findById(req.user._id);
      if (!user.companies.includes(companyId)) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized to modify this company",
        });
      }

      if (deleteImmediately) {
        try {
          // Start a session for transaction
          const session = await mongoose.startSession();
          await session.withTransaction(async () => {
            // Delete all associated data
            await Promise.all([
              // Delete employees
              Employee.deleteMany({ company: companyId }, { session }),

              // Delete employer details
              EmployerDetails.deleteMany({ company: companyId }, { session }),

              // Delete payroll data
              PayRun.deleteMany({ company: companyId }, { session }),

              // Delete any other associated data
              // Add other model deletions here

              // Delete the company itself
              await Company.findByIdAndDelete(companyId, { session }),

              // Update user references
              await User.updateMany(
                { companies: companyId },
                {
                  $pull: { companies: companyId },
                  $set: {
                    currentCompany: null,
                    defaultCompany: null,
                  },
                },
                { session }
              ),
            ]);
          });

          session.endSession();
        } catch (deleteError) {
          console.error("Error during deletion:", deleteError);
          throw new Error("Failed to delete company and associated data");
        }
      } else {
        // Just deactivate the company
        company.isActive = false;
        company.deactivatedAt = new Date();
        company.deactivatedBy = req.user._id;
        company.deactivationReason = reason;
        await company.save();
      }

      res.json({
        success: true,
        message: deleteImmediately
          ? "Company deleted successfully"
          : "Company deactivated successfully",
        redirectUrl: "/companies/select",
      });
    } catch (error) {
      console.error("Error in deactivate route:", error);
      res.status(500).json({
        success: false,
        message: "Failed to modify company",
        error: error.message,
      });
    }
  }
);

// Add reactivation route
router.post(
  "/:companyId/reactivate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyId } = req.params;

      // Validate company exists
      const company = await Company.findById(companyId);
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Check if user has permission
      const user = await User.findById(req.user._id);
      if (!user.companies.includes(companyId)) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized to reactivate this company",
        });
      }

      // Reactivate company
      company.isActive = true;
      company.deactivatedAt = null;
      company.deactivatedBy = null;
      company.deactivationReason = null;
      await company.save();

      res.json({
        success: true,
        message: "Company reactivated successfully",
      });
    } catch (error) {
      console.error("Error reactivating company:", error);
      res.status(500).json({
        success: false,
        message: "Failed to reactivate company",
        error: error.message,
      });
    }
  }
);

// Add this new route to handle setting the default company
router.post(
  "/set-default/:companyId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const company = await Company.findById(req.params.companyId);
      if (!company || !req.user.companies.includes(company._id)) {
        return res.status(404).send("Company not found");
      }

      req.user.defaultCompany = company._id;
      await req.user.save();

      req.flash(
        "success",
        `${company.name} has been set as your default company.`
      );
      res.redirect("/companies/select");
    } catch (error) {
      console.error("Error setting default company:", error);
      req.flash(
        "error",
        "An error occurred while setting the default company."
      );
      res.redirect("/companies/select");
    }
  }
);

module.exports = router;
