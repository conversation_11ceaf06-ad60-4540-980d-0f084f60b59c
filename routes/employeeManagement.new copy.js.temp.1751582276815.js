const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayFrequency = require("../models/PayFrequency");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/Company");
const checkCompany = require("../middleware/checkCompany");
const {
  ensureAuthenticated,
  ensureOwner,
} = require("../config/ensureAuthenticated");
const PayrollPeriod = require("../models/PayrollPeriod");
const beneficiaries = require("../models/beneficiaries");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const RFIConfig = require("../models/rfiConfig");
const mongoose = require("mongoose");
const fs = require("fs");
const moment = require("moment-timezone");
const dateUtils = require("../utils/dateUtils");
const transporter = require("../config/emailConfig");
const crypto = require("crypto");
const EmployeeCredential = require("../models/employeeCredential");
const ejs = require("ejs");
const path = require("path");
const PayrollService = require("../services/PayrollService");
const RFICalculationService = require("../services/rfiCalculationService");
const payrollCalculations = require("../utils/payrollCalculations");
const EmployeeNumberService = require("../services/employeeNumberService");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const Role = require("../models/role");
const bcrypt = require("bcrypt");
const EmployeeTemplateGenerator = require("../utils/excel-templates");
const XLSX = require("xlsx");
const PayComponent = require("../models/payComponent");
const AuditLog = require("../models/auditLog");
const PensionFundBeneficiary = require("../models/pensionFundBeneficiary");
const Beneficiary = require("../models/beneficiaries");
const WorkedHours = require("../models/WorkedHours");
const { calculatePeriodTotals } = require("../services/PayrollService");
const {
  formatDateForDisplay,
  isProratedMonth,
  getEndOfMonthDate,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
  getCurrentOrNextLastDayOfMonth,
  getSundaysInPeriod,
} = dateUtils;
const { isValidObjectId } = mongoose;
const ExcelJS = require("exceljs");
const multer = require("multer");
const HourlyRate = require("../models/HourlyRate");

// Configure multer for file upload
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type. Only Excel files are allowed."));
    }
  },
});

// Set the default timezone
const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Add this near the top of the file with other middleware imports
const checkAuth = (req, res, next) => {
  if (!req.isAuthenticated()) {
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }
  next();
};

function isNewEmployee(doa, currentMonth) {
  const doaMoment = moment(doa, "YYYY-MM-DD");
  const currentMoment = moment(currentMonth, "YYYY-MM-DD");

  if (doaMoment.isValid() && currentMoment.isValid()) {
    return doaMoment.isSame(currentMoment, "month");
  }

  console.error("Invalid date format:", { doa, currentMonth });
  return false;
}

// Add age calculation utility function
const calculateAge = (dob) => {
  if (!dob) return null;
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// Add this middleware to check if the user is authenticated
router.use(ensureAuthenticated);

// Apply the middleware to all routes in this file
router.use(ensureAuthenticated);
router.use(checkCompany);

// Add this new route for /employeeManagement
router.get(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Assuming the user has a currentCompany field
      const company = await Company.findById(req.user.currentCompany);

      if (!company) {
        return res.status(404).send("Company not found");
      }


      // Fetch only the employees for the current company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );


      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

// Keep the existing route for /:companyIdentifier/employeeManagement
router.get(
  "/:companyIdentifier/employeeManagement",
  ensureAuthenticated,
  ensureOwner,
  async (req, res) => {

    try {
      const companyIdentifier = req.params.companyIdentifier;
      let company;

      if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
        company = await Company.findById(companyIdentifier);
      } else {
        company = await Company.findOne({ companyCode: companyIdentifier });
      }

      if (!company) {
        return res.status(404).send("Company not found");
      }


      // Fetch only the employees for the selected company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );


      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

router.post("/setActiveTab", ensureAuthenticated, (req, res) => {
  req.session.activeTab = req.body.tab;
  res.sendStatus(200);
});

// Update the API route to fetch employees for the specific company
router.get(
  "/:companyCode/employeeManagement/api/employees",
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status"
      );
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      res
        .status(500)
        .json({ error: "An error occurred while fetching employees" });
    }
  }
);

// Add this new route to handle the update request for take-on tax totals
router.post("/update-take-on-tax-totals", async (req, res) => {
  try {
    const { employees } = req.body;
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {
        const updatedEmployee = await Employee.findByIdAndUpdate(
          employeeData._id,
          { $set: employeeData },
          { new: true }
        );

        if (updatedEmployee) {
          results.updated++;
        } else {
          results.errors.push({
            id: employeeData._id,
            error: "Employee not found",
          });
        }
      } catch (error) {
        results.errors.push({
          id: employeeData._id,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Add this new route to handle the update request for employees
router.post("/update-essentials", async (req, res) => {
  try {
    const { employees } = req.body;
    const results = { updated: 0, created: 0, errors: [] };
    const settings = await EmployeeNumberSettings.findOne();

    for (const employeeData of employees) {
      try {

        // Handle date fields
        ["dob", "doa"].forEach((dateField) => {
          if (employeeData[dateField]) {
            employeeData[dateField] = moment
              .utc(employeeData[dateField])
              .startOf("day")
              .toDate();
          } else {
            employeeData[dateField] = null;
          }
        });


        // Set email to null if it's blank
        if (employeeData.email === "") {
          employeeData.email = null;
        }

        if (settings && settings.mode === "automatic") {
          try {
            employeeData.employeeNumber = await settings.generateNextNumber();
          } catch (error) {
            console.error("Error generating employee number:", error);
            results.errors.push(
              `Error generating employee number for ${employeeData.email}: ${error.message}`
            );
            continue;
          }
        }

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          // If there's no _id, create a new employee
          const newEmployee = new Employee(employeeData);
          await newEmployee.save();
          results.created++;
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Add this new route to handle the update request for payment & banking info
router.post("/update-payment-banking-info", async (req, res) => {
  try {
    const { employees } = req.body;
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          results.errors.push("Employee ID not provided");
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Update residential address route
router.post(
  "/:companyCode/employeeManagement/update-residential-address",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employees } = req.body;
      const results = { updated: 0, errors: [] };

      for (const employeeData of employees) {
        try {
          if (employeeData._id) {
            const updatedEmployee = await Employee.findByIdAndUpdate(
              employeeData._id,
              { residentialAddress: employeeData.residentialAddress },
              { new: true, runValidators: true }
            );
            if (updatedEmployee) {
              results.updated++;
            } else {
              results.errors.push(
                `Employee with id ${employeeData._id} not found`
              );
            }
          } else {
            results.errors.push("Employee ID not provided");
          }
        } catch (error) {
          console.error("Error processing employee:", employeeData, error);
          results.errors.push(
            `Error processing employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      res.json({
        success: true,
        message: "Employees processed",
        results: results,
      });
    } catch (error) {
      console.error("Error updating employees:", error);
      res.status(500).json({
        success: false,
        message: "Error updating employees",
        error: error.message,
      });
    }
  }
);

// Keep existing routes...
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Update the existing skills-equity route
router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});

router.get("/api/employee-status/:employeeId", async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.employeeId);

    if (!employee) {
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    res.json({
      success: true,
      isTerminated: employee.employmentStatus === "Terminated",
      lastDayOfService: employee.endServiceDate,
      employmentStatus: employee.employmentStatus,
      terminationReason: employee.terminationReason,
    });
  } catch (error) {
    console.error("Error fetching employee status:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
});

router.get("/employee-list", ensureAuthenticated, (req, res) => {
  // ... fetch necessary data ...
  res.render("employeeListTab", {
    /* ... pass necessary data ... */
  });
});

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      console.log("Available Pay Frequencies:", {
        count: payFrequencies.length,
        frequencies: payFrequencies.map((pf) => ({
          id: pf._id,
          name: pf.name,
          frequency: pf.frequency,
        })),
      });

      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Create an empty employee object for the template
      const employee = {
        bank: "",
        accountNumber: "",
        accountType: "",
        branchCode: "",
        accountHolder: "",
      };

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        employee, // Pass the empty employee object
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get("/bulk-actions", ensureAuthenticated, (req, res) => {
  res.render("bulkActionsTab");
});

// Add a new route for company selection
router.get("/select-company", async (req, res) => {
  try {

    // Fetch user with populated companies
    const user = await User.findById(req.user._id).populate("companies");
    if (!user) {
      console.error("User not found");
      req.flash("error", "User not found");
      return res.redirect("/login");
    }


    // Generate CSRF token
    const csrfToken = req.csrfToken();

    // Render with all necessary data
    res.render("select-company", {
      user,
      companies: user.companies || [], // Pass companies array
      messages: req.flash(),
      csrfToken,
      partialsDir: "partials",
    });
  } catch (error) {
    console.error("Error loading select-company page:", error);
    req.flash("error", "Error loading companies");
    res.redirect("/");
  }
});

router.post(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        throw new Error("Company not found");
      }

      const employeeData = req.body;

      // Parse dates
      employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();

      // Format working days properly
      if (employeeData.workingDays) {
        // Convert single value to array if needed
        const workingDaysArray = Array.isArray(employeeData.workingDays)
          ? employeeData.workingDays
          : [employeeData.workingDays];

        // Format each working day as an object
        employeeData.regularHours = {
          ...employeeData.regularHours,
          workingDays: workingDaysArray.map((day) => ({
            day: day,
            type: "Normal Day",
          })),
        };
      }

      // Fetch PayFrequency details
      const payFrequency = await PayFrequency.findById(
        employeeData.payFrequency
      );
      if (!payFrequency) {
        throw new Error("Invalid pay frequency selected");
      }

      // Set PayFrequency related fields
      employeeData.payFrequency = payFrequency._id;
      employeeData.lastDayOfPeriod = payFrequency.lastDayOfPeriod;
      employeeData.firstPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;
      employeeData.initialPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;

      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        throw new Error("Employee number settings not found");
      }

      if (settings.mode === "manual") {
        if (!employeeData.companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }
        // Check for uniqueness in manual mode
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: employeeData.companyEmployeeNumber,
        });

        if (existingEmployee) {
          throw new Error("Employee number already exists");
        }
      } else {
        // Automatic mode
        try {
          employeeData.companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      }

      const newEmployee = new Employee({
        ...employeeData,
        globalEmployeeId: uuidv4(),
        company: company._id,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });


      await newEmployee.validate(); // This will throw a ValidationError if there are issues
      await newEmployee.save();

      res.status(200).json({
        message: "Employee added successfully",
        employeeId: newEmployee._id,
      });
    } catch (error) {
      console.error("Error adding new employee:", error);
      res.status(400).json({ error: error.message });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;


      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

async function generateEmployeeNumber(companyId) {
  try {
    return await EmployeeNumberService.generateEmployeeNumber(companyId);
  } catch (error) {
    throw new Error(`Failed to generate employee number: ${error.message}`);
  }
}

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode: companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const currentDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Set default values if no payroll exists
      const defaultBasicSalary = 0;
      const basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // Get firstPayrollPeriodEndDate from the payFrequency model
      const firstPayrollPeriodEndDate = moment.tz(
        employee.payFrequency.firstPayrollPeriodEndDate, // Use the value from payFrequency model
        DEFAULT_TIMEZONE
      );

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        console.log("Generating periods with PayFrequency settings:", {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
          firstPayrollPeriodEndDate: moment
            .tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
            .format("YYYY-MM-DD HH:mm:ss"),
        });

        // Use moment.tz for all dates
        const employeeStartDate = moment
          .tz(employee.doa, DEFAULT_TIMEZONE)
          .startOf("day");
        const currentEndDate = moment
          .tz(currentDate, DEFAULT_TIMEZONE)
          .endOf("day");

        console.log("Period Generation Dates:", {
          employeeStartDate: employeeStartDate.format("YYYY-MM-DD"),
          currentEndDate: currentEndDate.format("YYYY-MM-DD"),
        });

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          const proratedResult = payrollCalculations.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            period.startDate,
            period.endDate
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency
          );

          return {
            ...period.toObject(),
            basicSalary: Number(proratedResult.proratedSalary),
            basicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // When finding the current period
      let currentPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        currentPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the normal logic for current/next period
        currentPeriod =
          payPeriodsWithDetails.find((p) => !p.isFinalized) ||
          payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
      }

      // Calculate initial period values
      const currentPeriodCalculations =
        await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod?.endDate ||
            moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
          company._id
        );

      // After fetching the currentPeriod, add this code to fetch hourly rates
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          }).lean()
        : null;

      // Ensure to log the hourly rates for debugging
      console.log("Hourly rates found:", {
        found: !!hourlyRates,
        normalHours: hourlyRates?.normalHours || 0,
        overtimeHours: hourlyRates?.overtimeHours || 0,
        sundayHours:
          hourlyRates?.weeks?.reduce(
            (total, week) => total + (Number(week.sundayHours) || 0),
            0
          ) || 0,
      });
      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );

      const totalIncome = Number(
        currentPeriodCalculations.totalIncome || proratedSalary
      );

      console.log("Salary Calculations:", {
        basicSalary,
        proratedSalary,
        totalIncome,
        isProrated: currentPeriodCalculations.proRataDetails?.isProrated,
      });

      // Initialize payroll totals with the correct values
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome,
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0)
        ),
      };

      // Calculate prorated salary details
      const proRataDetails = {
        isProrated: payrollPeriod?.isProrated || false,
        percentage: payrollPeriod?.proRataDetails?.percentage || 100,
        daysWorked: payrollPeriod?.proRataDetails?.daysWorked || 0,
        totalDays: payrollPeriod?.proRataDetails?.totalDays || 0,
      };


      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        hourlyRates, // Ensure this line is included
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
        proRataDetails, // Add this line to pass prorated details to the template
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(
            currentPeriodCalculations.basicSalary?.full ||
              currentPeriodCalculations.basicSalary ||
              0
          ),
          proratedSalary: Number(
            currentPeriodCalculations.basicSalary?.prorated ||
              currentPeriodCalculations.proratedSalary ||
              0
          ),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(
            currentPeriodCalculations.proRataDetails?.percentage || 100
          ),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(
            currentPeriodCalculations.totalDeductions || 0
          ),
          netPay: Number(currentPeriodCalculations.netPay || 0),
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const employeeAge = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;
      const pensionFundDeduction =
        payroll?.pensionFund?.fixedContributionEmployee || 0;
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // Find the current payroll period
      let payrollPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        payrollPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the current period
        payrollPeriod = currentPeriod;
      }

      // If no period is found, create a default one
      if (!payrollPeriod) {
        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month");
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary,
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };
      }

      // Get current payroll data
      // Get current payroll data
      const taxableIncome = payrollCalculations.calculateTotalTaxableIncome(
        payroll || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly"
      );

      // Calculate all deductions using new function
      const deductionsResult = payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome
      );

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });

      console.log("Detailed Payroll Calculations Debug", {
        paye: {
          amount: currentPeriodCalculations.paye,
          monthlyTax: currentPeriodCalculations.paye,
        },
        uif: {
          amount: currentPeriodCalculations.uif,
        },
        deductions: {
          statutory: {
            paye: currentPeriodCalculations.paye,
            uif: currentPeriodCalculations.uif,
          },
          total: currentPeriodCalculations.totalDeductions,
        },
        fullCalculations: currentPeriodCalculations,
      });

      // Travel Allowance Calculation
      console.log("Travel Allowance Debug - Payroll Object", {
        travelAllowance: payroll?.travelAllowance,
        hasFixedAllowance: !!payroll?.travelAllowance?.fixedAllowance,
        hasReimbursedExpenses: !!payroll?.travelAllowance?.reimbursedExpenses,
      });

      if (
        payroll?.travelAllowance?.fixedAllowance ||
        payroll?.travelAllowance?.reimbursedExpenses
      ) {
        const travelAllowanceResult =
          await PayrollService.processTravelAllowance(employee, payroll);

        console.log(
          "Travel Allowance Processing Result",
          travelAllowanceResult
        );

        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;

          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: 20,
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: 80,
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes,
          };
        }
      }

      // Log employee age details
      console.log("🎂 Employee Profile Age Debug:", {
        "Employee ID": employeeId,
        "Date of Birth": employee.dob
          ? moment(employee.dob).format("YYYY-MM-DD")
          : "N/A",
        "Calculated Age": calculateAge(employee.dob),
        "Company Code": companyCode,
      });

      // Add debug logging
      console.log("Render Data:", {
        currentPeriod: !!currentPeriod,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        ...renderData.payrollTotals,
      });

      // Enhanced Payroll Details Calculation
      const payrollDetails = await PayrollService.calculatePayrollDetails(
        employee,
        payroll,
        currentPeriod
      );

      renderData.payrollDetails = payrollDetails;

      res.render("employeeProfile", renderData);
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "Error processing employee profile",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      // Fetch employee number settings - create default if not exists
      let employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!employeeNumberSettings) {
        employeeNumberSettings = new EmployeeNumberSettings({
          company: company._id,
          mode: "automatic",
          firstCompanyEmployeeNumber: "0001",
        });
        employeeNumberSettings.parsePattern(); // Initialize pattern and sequence
        await employeeNumberSettings.save();
      }

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        employeeNumberSettings, // Pass employee number settings to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      // Fetch employee number settings - create default if not exists
      let employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!employeeNumberSettings) {
        employeeNumberSettings = new EmployeeNumberSettings({
          company: company._id,
          mode: "automatic",
          firstCompanyEmployeeNumber: "0001",
        });
        employeeNumberSettings.parsePattern(); // Initialize pattern and sequence
        await employeeNumberSettings.save();
      }

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        employeeNumberSettings, // Pass employee number settings to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      // Fetch employee number settings - create default if not exists
      let employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!employeeNumberSettings) {
        employeeNumberSettings = new EmployeeNumberSettings({
          company: company._id,
          mode: "automatic",
          firstCompanyEmployeeNumber: "0001",
        });
        employeeNumberSettings.parsePattern(); // Initialize pattern and sequence
        await employeeNumberSettings.save();
      }

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        employeeNumberSettings, // Pass employee number settings to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      // Fetch employee number settings - create default if not exists
      let employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!employeeNumberSettings) {
        employeeNumberSettings = new EmployeeNumberSettings({
          company: company._id,
          mode: "automatic",
          firstCompanyEmployeeNumber: "0001",
        });
        employeeNumberSettings.parsePattern(); // Initialize pattern and sequence
        await employeeNumberSettings.save();
      }

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        employeeNumberSettings, // Pass employee number settings to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// ... previous imports and code ...

router.get(
  "/:companyCode/employeeProfile/:employeeId/basicSalaryHourly",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated payFrequency
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      console.log("Employee lookup:", {
        found: !!employee,
        id: employee?._id,
        doa: employee?.doa,
        payFrequency: employee?.payFrequency?.frequency,
      });

      if (!employee) {
        console.log("Employee not found:", {
          employeeId,
          companyId: company._id,
        });
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Calculate current period dates
      const currentDate = moment().tz("Africa/Johannesburg");
      const employeeDOA = moment(employee.doa).startOf("day");
      const currentWeekSunday = moment(currentDate).endOf("week");

      console.log("Period date calculations:", {
        currentDate: currentDate.format("YYYY-MM-DD"),
        employeeDOA: employeeDOA.format("YYYY-MM-DD"),
        currentWeekSunday: currentWeekSunday.format("YYYY-MM-DD"),
      });

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        startDate: {
          $gte: employeeDOA.toDate(),
          $lte: currentWeekSunday.toDate(),
        },
        endDate: { $lte: currentWeekSunday.toDate() },
        status: { $ne: "locked" },
        isFinalized: false,
      }).sort({ startDate: -1 });

      console.log("Current period found:", {
        found: !!currentPeriod,
        startDate: currentPeriod?.startDate,
        endDate: currentPeriod?.endDate,
      });

      // Get payroll record
      let payroll = null;
      if (currentPeriod) {
        payroll = await Payroll.findOne({
          employee: employeeId,
          company: company._id,
          month: currentPeriod.endDate,
        });
      }

      // Calculate rates
      const hourlyRate = payroll?.basicSalary || 0;
      const overtimeRate = hourlyRate * 1.5;
      const sundayRate = hourlyRate * 2;

      // Get existing hourly rates for this period
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          })
        : null;

      // Calculate total hours from hourlyRates if they exist
      let totalNormalHours = 0;
      let totalOvertimeHours = 0;
      if (hourlyRates && hourlyRates.weeks) {
        hourlyRates.weeks.forEach((week) => {
          totalNormalHours += Number(week.normalHours || 0);
          totalOvertimeHours += Number(week.overtimeHours || 0);
        });
      }

      // Check if we should show the form
      const shouldShowForm =
        currentDate.isSameOrAfter(employeeDOA) &&
        currentDate.isSameOrBefore(currentWeekSunday);

      res.render("basicSalaryHourly", {
        employee,
        company,
        companyCode,
        currentPeriod,
        hourlyRates: hourlyRates || {},
        payroll: payroll || {},
        hourlyRate,
        overtimeRate,
        sundayRate,
        totalNormalHours,
        totalOvertimeHours,
        shouldShowForm,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        user: req.user,
        moment,
      });
    } catch (error) {
      console.error("=== Error in Basic Salary Hourly Route ===", {
        message: error.message,
        stack: error.stack,
        params: req.params,
      });
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route to handle form submission
router.post(
  "/:companyCode/employeeProfile/:employeeId/basicSalaryHourly",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      console.log("Request body:", {
        body: req.body,
        normalHours: req.body.normalHours,
        overtimeHours: req.body.overtimeHours,
        weeks: req.body.weeks,
      });

      // Validate input
      const normalHours = Number(req.body.normalHours) || 0;
      const overtimeHours = Number(req.body.overtimeHours) || 0;

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        status: { $ne: "locked" },
        isFinalized: false,
      }).sort({ startDate: -1 });

      if (!currentPeriod) {
        return res.status(400).json({
          success: false,
          message: "No active payroll period found",
        });
      }

      // Process the weeks data
      const weeks = [];
      if (Array.isArray(req.body.weeks)) {
        req.body.weeks.forEach((week) => {
          if (week.weekEndingDate) {
            weeks.push({
              weekEndingDate: week.weekEndingDate,
              sundayHours: Number(week.sundayHours) || 0,
            });
          }
        });
      }

      console.log("Processed data:", {
        normalHours,
        overtimeHours,
        weeksCount: weeks.length,
      });

      // Create or update hourly rates
      const hourlyRates = await HourlyRate.findOneAndUpdate(
        {
          employee: employeeId,
          payrollPeriod: currentPeriod._id,
        },
        {
          $set: {
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
            normalHours,
            overtimeHours,
            weeks,
          },
        },
        { upsert: true, new: true }
      );

      console.log("Saved hourly rates:", {
        id: hourlyRates._id,
        normalHours: hourlyRates.normalHours,
        overtimeHours: hourlyRates.overtimeHours,
        weeksCount: hourlyRates.weeks.length,
      });

      res.json({
        success: true,
        message: "Hourly rates saved successfully",
        data: hourlyRates,
      });
    } catch (error) {
      console.error("Error saving hourly rates:", error);
      res.status(500).json({
        success: false,
        message: "An error occurred while saving hourly rates",
      });
    }
  }
);

// Update the addNew route handler
// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    throw error; // Re-throw the error to be handled by the calling function
  }
}

router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      const currentPeriod = {
        startDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        ),
        endDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        ),
      };

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(currentDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(currentDate),
          basicSalary: 0,
        });
        await payroll.save();
      }

      // Format the date directly
      const formattedDate = currentPeriod.endDate.toLocaleDateString("en-GB");

      console.log("Rendering addBasicSalary with:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll._id,
        currentPeriod,
      });

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod,
        formattedDate,
        payroll,
        relevantDate: currentDate,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post("/saveBasicSalary", async (req, res) => {
  try {
    const { employeeId, relevantDate, companyId, basicSalary } = req.body;

    if (!employeeId || !relevantDate || !companyId || !basicSalary) {
      console.error("Missing required fields:", req.body);
      req.flash("error", "Missing required fields");
      return res.redirect(
        `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
      );
    }

    const formattedDate = formatDateForMongoDB(new Date(relevantDate));

    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: companyId,
      month: formattedDate,
    });

    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        company: companyId,
        month: formattedDate,
      });
    }

    payroll.basicSalary = parseFloat(basicSalary);
    await payroll.save();

    req.flash("success", "Basic salary updated successfully");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    console.error("Error saving basic salary:", error);
    req.flash("error", "An error occurred while saving the basic salary");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
    );
  }
});

router.get(
  "/:companyCode/addBasicSalary/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Find the most recent unfinalized payroll
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Determine relevant date
      let relevantDate;
      if (latestPayroll && !latestPayroll.finalised) {
        relevantDate = latestPayroll.month;
      } else {
        relevantDate = getCurrentOrNextLastDayOfMonth();
      }

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(relevantDate),
          basicSalary: latestPayroll ? latestPayroll.basicSalary : 0,
        });
        await payroll.save();
      }

      // Render the template with all necessary data
      res.render("addBasicSalary", {
        employee,
        payroll,
        relevantDate,
        company,
        companyCode,
        formatDateForDisplay, // Pass the function to the template
        formattedDate: formatDateForDisplay(relevantDate),
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass CSRF token
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect("/");
    }
  }
);

// GET route for classifications
router.get(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      res.render("classifications", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering classifications form:", error);
      req.flash("error", "Failed to load classifications form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for classifications
router.post(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        workingHours,
        isDirector,
        typeOfDirector,
        isContractor,
        isUifExempt,
        uifExemptReason,
      } = req.body;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update employee
      const employee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        {
          $set: {
            workingHours,
            isDirector: !!isDirector,
            typeOfDirector: isDirector ? typeOfDirector : null,
            isContractor: !!isContractor,
            isUifExempt: !!isUifExempt,
            uifExemptReason: isUifExempt ? uifExemptReason : null,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      req.flash("success", "Classifications updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating classifications:", error);
      req.flash("error", "Failed to update classifications");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/classifications`
      );
    }
  }
);

// RFI Configuration Constants
const RFI_VALIDATION = {
  MIN_PERCENTAGE: 0,
  MAX_PERCENTAGE: 100,
  MIN_COMPONENTS: 1,
  REQUIRED_COMPONENTS: ["basicSalary"], // Components that must be included
};

// RFI Validation Middleware
const validateRFIConfig = (req, res, next) => {
  const { rfiOption, selectedIncomes, percentageValues } = req.body;
  const errors = [];

  try {
    // Validate RFI Option
    if (!["selectedIncome", "percentagePerIncome"].includes(rfiOption)) {
      errors.push("Invalid RFI calculation method");
    }

    // Validate Selected Incomes
    if (rfiOption === "selectedIncome") {
      if (
        !selectedIncomes ||
        !Array.isArray(selectedIncomes) ||
        selectedIncomes.length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be selected`
        );
      }

      // Check for required components
      const hasRequiredComponents = RFI_VALIDATION.REQUIRED_COMPONENTS.every(
        (comp) => selectedIncomes.includes(comp)
      );
      if (!hasRequiredComponents) {
        errors.push("Basic salary must be included in RFI calculation");
      }
    }

    // Validate Percentage Values
    if (rfiOption === "percentagePerIncome") {
      if (
        !percentageValues ||
        Object.keys(percentageValues).length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be configured`
        );
      }

      // Validate percentage ranges
      Object.entries(percentageValues).forEach(([component, percentage]) => {
        const value = parseFloat(percentage);
        if (
          isNaN(value) ||
          value < RFI_VALIDATION.MIN_PERCENTAGE ||
          value > RFI_VALIDATION.MAX_PERCENTAGE
        ) {
          errors.push(
            `Invalid percentage value for ${component}. Must be between ${RFI_VALIDATION.MIN_PERCENTAGE} and ${RFI_VALIDATION.MAX_PERCENTAGE}`
          );
        }
      });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("RFI Validation Error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating RFI configuration",
    });
  }
};

// GET route for RFI configuration
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId)
        .populate("rfiConfig.selectedIncomes.componentId")
        .populate("rfiConfig.percentageIncomes.componentId");

      // Get the employee's payroll record with basic salary
      const payroll = await Payroll.findOne({ employee: employeeId });
      const assignedComponents = [];

      // Add basic salary if it exists in payroll
      if (payroll?.basicSalary) {
        assignedComponents.push({
          id: "basicSalary",
          name: "Basic Salary",
          amount: payroll.basicSalary,
          type: "income",
          isSelected:
            employee.rfiConfig?.selectedIncomes?.some(
              (inc) => inc.name === "Basic Salary"
            ) || false,
        });
      }

      // Add other components from payroll if they exist and have values
      if (payroll) {
        // Commission
        if (payroll.commission && payroll.commission.amount) {
          assignedComponents.push({
            id: "commission",
            name: "Commission",
            amount: payroll.commission.amount,
            type: "income",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Commission"
              ) || false,
          });
        }

        // Travel Allowance
        if (payroll.travelAllowance && payroll.travelAllowance.amount) {
          assignedComponents.push({
            id: "travelAllowance",
            name: "Travel Allowance",
            amount: payroll.travelAllowance.amount,
            type: "allowance",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Travel Allowance"
              ) || false,
          });
        }

        // Add other allowances if they exist
        if (payroll.allowances && payroll.allowances.length > 0) {
          payroll.allowances.forEach((allowance) => {
            if (allowance.amount) {
              assignedComponents.push({
                id: allowance._id.toString(),
                name: allowance.name,
                amount: allowance.amount,
                type: "allowance",
                isSelected:
                  employee.rfiConfig?.selectedIncomes?.some(
                    (inc) => inc.name === allowance.name
                  ) || false,
              });
            }
          });
        }

        // Add any other relevant components
        // ... add other components as needed
      }

      // Sort components by type and name
      assignedComponents.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === "income" ? -1 : 1;
      });

      res.render("defineRFI", {
        company,
        employee,
        payComponents: assignedComponents, // Pass the assigned components
        rfiConfig: employee.rfiConfig || {
          option: "",
          selectedIncomes: [],
          percentageIncomes: {},
        },
        validation: RFI_VALIDATION,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI configuration form:", error);
      req.flash(
        "error",
        "An error occurred while loading the RFI configuration"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for RFI configuration
router.post(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { option, selectedIncomes, percentageValues } = req.body;

      // Get company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Validate required fields
      if (!option) {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: "RFI calculation method is required",
        });
      }

      // Validate based on selected option
      if (option === "selectedIncome") {
        if (
          !selectedIncomes ||
          !Array.isArray(selectedIncomes) ||
          selectedIncomes.length === 0
        ) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: "At least one income component must be selected",
          });
        }
      } else if (option === "percentagePerIncome") {
        if (!percentageValues || Object.keys(percentageValues).length === 0) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message:
              "Percentage values are required for at least one component",
          });
        }

        // Validate percentage values
        for (const [component, percentage] of Object.entries(
          percentageValues
        )) {
          const value = parseFloat(percentage);
          if (isNaN(value) || value < 0 || value > 100) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Invalid percentage value for ${component}. Must be between 0 and 100`,
            });
          }
        }
      }

      // Update employee RFI configuration
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        {
          $set: {
            "rfiConfig.option": option,
            "rfiConfig.selectedIncomes":
              option === "selectedIncome"
                ? selectedIncomes.map((name) => ({
                    name,
                    includeInCalculation: true,
                  }))
                : [],
            "rfiConfig.percentageIncomes":
              option === "percentagePerIncome"
                ? Object.entries(percentageValues).map(
                    ([name, percentage]) => ({
                      name,
                      percentage: parseFloat(percentage),
                    })
                  )
                : [],
          },
        },
        { new: true, session }
      );

      if (!updatedEmployee) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Add audit log with company ID
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id, // Add company ID here
            action: "UPDATE_RFI_CONFIG",
            entity: "Employee",
            entityId: employeeId,
            details: {
              option,
              selectedIncomes:
                option === "selectedIncome" ? selectedIncomes : [],
              percentageValues:
                option === "percentagePerIncome" ? percentageValues : {},
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "RFI configuration updated successfully",
        data: updatedEmployee.rfiConfig,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating RFI configuration:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update RFI configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

// Add these validation rules at the top of the file
const FUND_VALIDATION = {
  FIXED_AMOUNT: {
    MIN: 0,
    MAX: 1000000000, // 1 billion
    DECIMALS: 2,
  },
  PERCENTAGE: {
    MIN: 0,
    MAX: 100,
    DECIMALS: 2,
  },
  CATEGORY_FACTOR: {
    MIN: 0.01,
    MAX: 10,
    DECIMALS: 2,
  },
};

// Add validation middleware
const validateFundInput = (req, res, next) => {
  const {
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
  } = req.body;

  const errors = [];

  try {
    // Validate contribution method
    if (!["fixedAmount", "percentageRFI"].includes(contributionCalculation)) {
      errors.push("Invalid contribution calculation method");
    }

    // Validate fixed amounts if that method is selected
    if (contributionCalculation === "fixedAmount") {
      const employeeAmount = parseFloat(fixedContributionEmployee);
      const employerAmount = parseFloat(fixedContributionEmployer);

      if (
        isNaN(employeeAmount) ||
        employeeAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employeeAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employee fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }

      if (
        isNaN(employerAmount) ||
        employerAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employerAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employer fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }
    }

    // Validate RFI percentages if that method is selected
    if (contributionCalculation === "percentageRFI") {
      const employeePercentage = parseFloat(rfiEmployee);
      const employerPercentage = parseFloat(rfiEmployer);

      if (
        isNaN(employeePercentage) ||
        employeePercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employeePercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employee RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }

      if (
        isNaN(employerPercentage) ||
        employerPercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employerPercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employer RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }
    }

    // Validate category factor
    if (categoryFactor) {
      const factor = parseFloat(categoryFactor);
      if (
        isNaN(factor) ||
        factor < FUND_VALIDATION.CATEGORY_FACTOR.MIN ||
        factor > FUND_VALIDATION.CATEGORY_FACTOR.MAX
      ) {
        errors.push(
          `Category factor must be between ${FUND_VALIDATION.CATEGORY_FACTOR.MIN} and ${FUND_VALIDATION.CATEGORY_FACTOR.MAX}`
        );
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("Fund validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating fund configuration",
    });
  }
};

// GET route for regular hours page
router.get(
  "/:companyCode/:employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Fetch the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Fetch the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the regular hours page
      res.render("regularHours", {
        company,
        employee,
        csrfToken: req.csrfToken(), // Add CSRF token
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering regular hours form:", error);
      req.flash("error", "Failed to load regular hours form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

//_____________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection, // Make sure this middleware is here
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Generate CSRF token
      const csrfToken = req.csrfToken();
      console.log("Generated CSRF token:", csrfToken); // Debug log

      // Render with all necessary data
      res.render("eti", {
        company,
        employee,
        user: req.user,
        csrfToken, // Pass the token explicitly
        messages: req.flash(),
        moment: require("moment"), // Add moment if needed for date formatting
      });
    } catch (error) {
      console.error("Error rendering ETI page:", error);
      req.flash("error", "An error occurred while loading the ETI page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for ETI updates
router.post(
  "/:companyCode/employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1);
      effectiveDate.setHours(0, 0, 0, 0);

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/eti`
      );
    }
  }
);

//__________________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("skillsEquity", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering Skills Equity page:", error);
      req.flash(
        "error",
        "An error occurred while loading the Skills Equity page"
      );
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// POST route for updating Skills Equity
router.post(
  "/:companyCode/employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        gender,
        race,
        occupationLevel,
        occupationCategory,
        jobValue,
        province,
        disabled,
        foreignNational,
        notRSACitizen,
      } = req.body;

      // First find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find and update employee using company._id
      const employee = await Employee.findOneAndUpdate(
        {
          _id: employeeId,
          company: company._id, // Use company._id instead of req.company._id
        },
        {
          $set: {
            gender,
            race,
            occupationLevel,
            occupationCategory,
            jobValue,
            province,
            disabled: !!disabled,
            foreignNational: !!foreignNational,
            notRSACitizen: !!notRSACitizen,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Success case
      req.flash("success", "Skills & Equity information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating Skills & Equity:", error);
      req.flash("error", "Failed to update Skills & Equity information");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/skillsEquity`
      );
    }
  }
);

// Update basic salary route
router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, basicSalary, hourlyPaid } = req.body;
      const { companyCode } = req.params;

      console.log("Updating basic salary:", {
        employeeId,
        basicSalary,
        hourlyPaid,
        companyCode,
      });

      // Find employee and company
      const employee = await Employee.findById(employeeId);
      const company = await Company.findOne({ companyCode });

      if (!employee || !company) {
        throw new Error("Employee or company not found");
      }

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      if (!currentPeriod) {
        throw new Error("No active period found");
      }

      // Update current period
      currentPeriod.basicSalary = parseFloat(basicSalary);
      await currentPeriod.save({ session });

      // Update current payroll
      const currentPayroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: currentPeriod.endDate,
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        {
          new: true,
          upsert: true,
          session,
        }
      );

      // Update all future periods
      await PayrollPeriod.updateMany(
        {
          employee: employeeId,
          company: company._id,
          startDate: { $gt: currentPeriod.endDate },
          isFinalized: false,
        },
        {
          $set: { basicSalary: parseFloat(basicSalary) },
        },
        { session }
      );

      // Update all future payrolls
      await Payroll.updateMany(
        {
          employee: employeeId,
          company: company._id,
          month: { $gt: currentPeriod.endDate },
          status: "draft",
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        { session }
      );

      // Update employee's hourly status
      if (!employee.regularHours) {
        employee.regularHours = {};
      }
      employee.regularHours.hourlyPaid = hourlyPaid === "on";
      await employee.save({ session });

      await session.commitTransaction();

      console.log("Basic salary update completed:", {
        currentPeriod: currentPeriod._id,
        basicSalary: parseFloat(basicSalary),
        futurePeriodsUpdated: true,
      });

      req.flash("success", "Basic salary updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error saving basic salary:", error);
      req.flash("error", "Failed to update basic salary");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/addBasicSalary`
      );
    } finally {
      session.endSession();
    }
  }
);

router.get(
  "/:companyCode/addBasicSalary/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Find the most recent unfinalized payroll
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Determine relevant date
      let relevantDate;
      if (latestPayroll && !latestPayroll.finalised) {
        relevantDate = latestPayroll.month;
      } else {
        relevantDate = getCurrentOrNextLastDayOfMonth();
      }

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(relevantDate),
          basicSalary: latestPayroll ? latestPayroll.basicSalary : 0,
        });
        await payroll.save();
      }

      // Render the template with all necessary data
      res.render("addBasicSalary", {
        employee,
        payroll,
        relevantDate,
        company,
        companyCode,
        formatDateForDisplay, // Pass the function to the template
        formattedDate: formatDateForDisplay(relevantDate),
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass CSRF token
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect("/");
    }
  }
);

// Add these routes after your existing routes
router.post(
  "/api/employees/:employeeId/enableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Get base URL for email
      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user if needed
        if (
          !user.role ||
          user.role.toString() !== employeeRole._id.toString()
        ) {
          user.role = employeeRole._id;
          await user.save();
        }
      } else {
        // Create new user account
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          password: crypto.randomBytes(20).toString("hex"), // Temporary password
        });

        await user.save();
      }

      // Link user and role to employee
      employee.user = user._id;
      employee.role = employeeRole._id;
      employee.selfServiceEnabled = true;

      // Generate and send setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      await employee.save();

      res.json({
        success: true,
        message: "Self-service enabled successfully",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/disableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/resendInvite",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Resend setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      res.json({
        success: true,
        message: "Invitation resent successfully",
      });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend invite",
        error: error.message,
      });
    }
  }
);

// Self Service Routes
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Update the enable self-service route
router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      await employee.save();

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Generate new token
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );

      await employee.save();

      // Send new setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account setup link has been renewed. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);

module.exports = router;




// Add this new route to handle the update request for payment & banking info
router.post("/update-payment-banking-info", async (req, res) => {
  try {
    const { employees } = req.body;
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          results.errors.push("Employee ID not provided");
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Update residential address route
router.post(
  "/:companyCode/employeeManagement/update-residential-address",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employees } = req.body;
      const results = { updated: 0, errors: [] };

      for (const employeeData of employees) {
        try {
          if (employeeData._id) {
            const updatedEmployee = await Employee.findByIdAndUpdate(
              employeeData._id,
              { residentialAddress: employeeData.residentialAddress },
              { new: true, runValidators: true }
            );
            if (updatedEmployee) {
              results.updated++;
            } else {
              results.errors.push(
                `Employee with id ${employeeData._id} not found`
              );
            }
          } else {
            results.errors.push("Employee ID not provided");
          }
        } catch (error) {
          console.error("Error processing employee:", employeeData, error);
          results.errors.push(
            `Error processing employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      res.json({
        success: true,
        message: "Employees processed",
        results: results,
      });
    } catch (error) {
      console.error("Error updating employees:", error);
      res.status(500).json({
        success: false,
        message: "Error updating employees",
        error: error.message,
      });
    }
  }
);

// Keep existing routes...
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Update the existing skills-equity route
router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});

router.get("/api/employee-status/:employeeId", async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.employeeId);

    if (!employee) {
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    res.json({
      success: true,
      isTerminated: employee.employmentStatus === "Terminated",
      lastDayOfService: employee.endServiceDate,
      employmentStatus: employee.employmentStatus,
      terminationReason: employee.terminationReason,
    });
  } catch (error) {
    console.error("Error fetching employee status:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
});

router.get("/employee-list", ensureAuthenticated, (req, res) => {
  // ... fetch necessary data ...
  res.render("employeeListTab", {
    /* ... pass necessary data ... */
  });
});

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      console.log("Available Pay Frequencies:", {
        count: payFrequencies.length,
        frequencies: payFrequencies.map((pf) => ({
          id: pf._id,
          name: pf.name,
          frequency: pf.frequency,
        })),
      });

      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Create an empty employee object for the template
      const employee = {
        bank: "",
        accountNumber: "",
        accountType: "",
        branchCode: "",
        accountHolder: "",
      };

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        employee, // Pass the empty employee object
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get("/bulk-actions", ensureAuthenticated, (req, res) => {
  res.render("bulkActionsTab");
});

// Add a new route for company selection
router.get("/select-company", async (req, res) => {
  try {

    // Fetch user with populated companies
    const user = await User.findById(req.user._id).populate("companies");
    if (!user) {
      console.error("User not found");
      req.flash("error", "User not found");
      return res.redirect("/login");
    }


    // Generate CSRF token
    const csrfToken = req.csrfToken();

    // Render with all necessary data
    res.render("select-company", {
      user,
      companies: user.companies || [], // Pass companies array
      messages: req.flash(),
      csrfToken,
      partialsDir: "partials",
    });
  } catch (error) {
    console.error("Error loading select-company page:", error);
    req.flash("error", "Error loading companies");
    res.redirect("/");
  }
});

router.post(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        throw new Error("Company not found");
      }

      const employeeData = req.body;

      // Parse dates
      employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();

      // Format working days properly
      if (employeeData.workingDays) {
        // Convert single value to array if needed
        const workingDaysArray = Array.isArray(employeeData.workingDays)
          ? employeeData.workingDays
          : [employeeData.workingDays];

        // Format each working day as an object
        employeeData.regularHours = {
          ...employeeData.regularHours,
          workingDays: workingDaysArray.map((day) => ({
            day: day,
            type: "Normal Day",
          })),
        };
      }

      // Fetch PayFrequency details
      const payFrequency = await PayFrequency.findById(
        employeeData.payFrequency
      );
      if (!payFrequency) {
        throw new Error("Invalid pay frequency selected");
      }

      // Set PayFrequency related fields
      employeeData.payFrequency = payFrequency._id;
      employeeData.lastDayOfPeriod = payFrequency.lastDayOfPeriod;
      employeeData.firstPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;
      employeeData.initialPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;

      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        throw new Error("Employee number settings not found");
      }

      if (settings.mode === "manual") {
        if (!employeeData.companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }
        // Check for uniqueness in manual mode
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: employeeData.companyEmployeeNumber,
        });

        if (existingEmployee) {
          throw new Error("Employee number already exists");
        }
      } else {
        // Automatic mode
        try {
          employeeData.companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      }

      const newEmployee = new Employee({
        ...employeeData,
        globalEmployeeId: uuidv4(),
        company: company._id,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });


      await newEmployee.validate(); // This will throw a ValidationError if there are issues
      await newEmployee.save();

      res.status(200).json({
        message: "Employee added successfully",
        employeeId: newEmployee._id,
      });
    } catch (error) {
      console.error("Error adding new employee:", error);
      res.status(400).json({ error: error.message });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;


      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

async function generateEmployeeNumber(companyId) {
  try {
    return await EmployeeNumberService.generateEmployeeNumber(companyId);
  } catch (error) {
    throw new Error(`Failed to generate employee number: ${error.message}`);
  }
}

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode: companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const currentDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Set default values if no payroll exists
      const defaultBasicSalary = 0;
      const basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // Get firstPayrollPeriodEndDate from the payFrequency model
      const firstPayrollPeriodEndDate = moment.tz(
        employee.payFrequency.firstPayrollPeriodEndDate, // Use the value from payFrequency model
        DEFAULT_TIMEZONE
      );

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        console.log("Generating periods with PayFrequency settings:", {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
          firstPayrollPeriodEndDate: moment
            .tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
            .format("YYYY-MM-DD HH:mm:ss"),
        });

        // Use moment.tz for all dates
        const employeeStartDate = moment
          .tz(employee.doa, DEFAULT_TIMEZONE)
          .startOf("day");
        const currentEndDate = moment
          .tz(currentDate, DEFAULT_TIMEZONE)
          .endOf("day");

        console.log("Period Generation Dates:", {
          employeeStartDate: employeeStartDate.format("YYYY-MM-DD"),
          currentEndDate: currentEndDate.format("YYYY-MM-DD"),
        });

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          const proratedResult = payrollCalculations.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            period.startDate,
            period.endDate
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency
          );

          return {
            ...period.toObject(),
            basicSalary: Number(proratedResult.proratedSalary),
            basicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // When finding the current period
      let currentPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        currentPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the normal logic for current/next period
        currentPeriod =
          payPeriodsWithDetails.find((p) => !p.isFinalized) ||
          payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
      }

      // Calculate initial period values
      const currentPeriodCalculations =
        await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod?.endDate ||
            moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
          company._id
        );

      // After fetching the currentPeriod, add this code to fetch hourly rates
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          }).lean()
        : null;

      // Ensure to log the hourly rates for debugging
      console.log("Hourly rates found:", {
        found: !!hourlyRates,
        normalHours: hourlyRates?.normalHours || 0,
        overtimeHours: hourlyRates?.overtimeHours || 0,
        sundayHours:
          hourlyRates?.weeks?.reduce(
            (total, week) => total + (Number(week.sundayHours) || 0),
            0
          ) || 0,
      });
      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );

      const totalIncome = Number(
        currentPeriodCalculations.totalIncome || proratedSalary
      );

      console.log("Salary Calculations:", {
        basicSalary,
        proratedSalary,
        totalIncome,
        isProrated: currentPeriodCalculations.proRataDetails?.isProrated,
      });

      // Initialize payroll totals with the correct values
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome,
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0)
        ),
      };


      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        hourlyRates, // Ensure this line is included
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(
            currentPeriodCalculations.basicSalary?.full ||
              currentPeriodCalculations.basicSalary ||
              0
          ),
          proratedSalary: Number(
            currentPeriodCalculations.basicSalary?.prorated ||
              currentPeriodCalculations.proratedSalary ||
              0
          ),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(
            currentPeriodCalculations.proRataDetails?.percentage || 100
          ),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(
            currentPeriodCalculations.totalDeductions || 0
          ),
          netPay: Number(currentPeriodCalculations.netPay || 0),
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const employeeAge = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;
      const pensionFundDeduction =
        payroll?.pensionFund?.fixedContributionEmployee || 0;
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // Find the current payroll period
      let payrollPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        payrollPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the current period
        payrollPeriod = currentPeriod;
      }

      // If no period is found, create a default one
      if (!payrollPeriod) {
        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month");
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary,
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };
      }

      // Get current payroll data
      // Get current payroll data
      const taxableIncome = payrollCalculations.calculateTotalTaxableIncome(
        payroll || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly"
      );

      // Calculate all deductions using new function
      const deductionsResult = payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome
      );

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });

      console.log("Detailed Payroll Calculations Debug", {
        paye: {
          amount: currentPeriodCalculations.paye,
          monthlyTax: currentPeriodCalculations.paye,
        },
        uif: {
          amount: currentPeriodCalculations.uif,
        },
        deductions: {
          statutory: {
            paye: currentPeriodCalculations.paye,
            uif: currentPeriodCalculations.uif,
          },
          total: currentPeriodCalculations.totalDeductions,
        },
        fullCalculations: currentPeriodCalculations,
      });

      // Travel Allowance Calculation
      console.log("Travel Allowance Debug - Payroll Object", {
        travelAllowance: payroll?.travelAllowance,
        hasFixedAllowance: !!payroll?.travelAllowance?.fixedAllowance,
        hasReimbursedExpenses: !!payroll?.travelAllowance?.reimbursedExpenses,
      });

      if (
        payroll?.travelAllowance?.fixedAllowance ||
        payroll?.travelAllowance?.reimbursedExpenses
      ) {
        const travelAllowanceResult =
          await PayrollService.processTravelAllowance(employee, payroll);

        console.log(
          "Travel Allowance Processing Result",
          travelAllowanceResult
        );

        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;

          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: 20,
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: 80,
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes,
          };
        }
      }

      // Log employee age details
      console.log("🎂 Employee Profile Age Debug:", {
        "Employee ID": employeeId,
        "Date of Birth": employee.dob
          ? moment(employee.dob).format("YYYY-MM-DD")
          : "N/A",
        "Calculated Age": calculateAge(employee.dob),
        "Company Code": companyCode,
      });

      // Add debug logging
      console.log("Render Data:", {
        currentPeriod: !!currentPeriod,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        ...renderData.payrollTotals,
      });

      // Enhanced Payroll Details Calculation
      const payrollDetails = await PayrollService.calculatePayrollDetails(
        employee,
        payroll,
        currentPeriod
      );

      renderData.payrollDetails = payrollDetails;

      res.render("employeeProfile", renderData);
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "Error processing employee profile",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber =
            await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;

      console.log("Initial Data:", {
        doa: doa.format("YYYY-MM-DD"),
        payFrequency: {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        },
      });

      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

        console.log("Weekly Period Calculation:", {
          startDate: doa.format("YYYY-MM-DD"),
          endDate: moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD"),
          targetDay: payFrequency.lastDayOfPeriod,
          daysToAdd,
          resultingDayOfWeek: moment(firstPayrollPeriodEndDate).format("dddd"),
        });
      } else if (payFrequency.frequency === "monthly") {
        if (payFrequency.lastDayOfPeriod === "monthend") {
          firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
        } else {
          firstPayrollPeriodEndDate = doa
            .clone()
            .date(parseInt(payFrequency.lastDayOfPeriod))
            .endOf("day")
            .toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }

      console.log(
        "Final first payroll period end date:",
        moment(firstPayrollPeriodEndDate).format("YYYY-MM-DD")
      );

      // Create employee data object with proper structure
      const employeeData = {
        // Auto-generated fields
        companyEmployeeNumber: companyEmployeeNumber,
        globalEmployeeId: companyEmployeeNumber,
        workingHours:
          req.body.workingHours === "Part Time"
            ? "Less Than 22 Hours a Week"
            : "Full Time",
        workSchedule: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: false,
          sunday: false,
        },
        payFrequency: payFrequency._id,
        firstPayrollPeriodEndDate: firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,

        // Required fields from form
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        dob: req.body.dateOfBirth,
        doa: req.body.doa,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        personalDetails: {
          idType: req.body.idType === "none" ? "other" : "rsa",
          idNumber: req.body.idType === "none" ? undefined : req.body.idNumber,
          nationality: "South African", // Default value
          countryOfBirth: "South Africa", // Default value
        },
        paymentMethod: "eft", // Default to EFT
        address: {
          streetAddress: req.body.streetAddress,
          suburb: req.body.suburb,
          city: req.body.city,
          postalCode: req.body.postalCode,
        },
        regularHours: req.body.regularHours,
        bank: req.body.bank,
        accountType: req.body.accountType,
        accountNumber: req.body.accountNumber,
        branchCode: req.body.branchCode,
        holderRelationship:
          req.body.holderRelationship === "Own Account" ? "self" : "other",

        // Additional fields
        company: company._id,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      };

      // Create and save the new employee
      const newEmployee = new Employee({
        ...employeeData,
        // Transform bank name to match the model's format
        bank: employeeData.bank,
      });
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: { $in: ["Inactive", "Serving Notice"] },
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// ... previous imports and code ...

router.get(
  "/:companyCode/employeeProfile/:employeeId/basicSalaryHourly",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated payFrequency
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      console.log("Employee lookup:", {
        found: !!employee,
        id: employee?._id,
        doa: employee?.doa,
        payFrequency: employee?.payFrequency?.frequency,
      });

      if (!employee) {
        console.log("Employee not found:", {
          employeeId,
          companyId: company._id,
        });
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}`);
      }

      // Calculate current period dates
      const currentDate = moment().tz("Africa/Johannesburg");
      const employeeDOA = moment(employee.doa).startOf("day");
      const currentWeekSunday = moment(currentDate).endOf("week");

      console.log("Period date calculations:", {
        currentDate: currentDate.format("YYYY-MM-DD"),
        employeeDOA: employeeDOA.format("YYYY-MM-DD"),
        currentWeekSunday: currentWeekSunday.format("YYYY-MM-DD"),
      });

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        startDate: {
          $gte: employeeDOA.toDate(),
          $lte: currentWeekSunday.toDate(),
        },
        endDate: { $lte: currentWeekSunday.toDate() },
        status: { $ne: "locked" },
        isFinalized: false,
      }).sort({ startDate: -1 });

      console.log("Current period found:", {
        found: !!currentPeriod,
        startDate: currentPeriod?.startDate,
        endDate: currentPeriod?.endDate,
      });

      // Get payroll record
      let payroll = null;
      if (currentPeriod) {
        payroll = await Payroll.findOne({
          employee: employeeId,
          company: company._id,
          month: currentPeriod.endDate,
        });
      }

      // Calculate rates
      const hourlyRate = payroll?.basicSalary || 0;
      const overtimeRate = hourlyRate * 1.5;
      const sundayRate = hourlyRate * 2;

      // Get existing hourly rates for this period
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          })
        : null;

      // Calculate total hours from hourlyRates if they exist
      let totalNormalHours = 0;
      let totalOvertimeHours = 0;
      if (hourlyRates && hourlyRates.weeks) {
        hourlyRates.weeks.forEach((week) => {
          totalNormalHours += Number(week.normalHours || 0);
          totalOvertimeHours += Number(week.overtimeHours || 0);
        });
      }

      // Check if we should show the form
      const shouldShowForm =
        currentDate.isSameOrAfter(employeeDOA) &&
        currentDate.isSameOrBefore(currentWeekSunday);

      res.render("basicSalaryHourly", {
        employee,
        company,
        companyCode,
        currentPeriod,
        hourlyRates: hourlyRates || {},
        payroll: payroll || {},
        hourlyRate,
        overtimeRate,
        sundayRate,
        totalNormalHours,
        totalOvertimeHours,
        shouldShowForm,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        user: req.user,
        moment,
      });
    } catch (error) {
      console.error("=== Error in Basic Salary Hourly Route ===", {
        message: error.message,
        stack: error.stack,
        params: req.params,
      });
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route to handle form submission
router.post(
  "/:companyCode/employeeProfile/:employeeId/basicSalaryHourly",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      console.log("Request body:", {
        body: req.body,
        normalHours: req.body.normalHours,
        overtimeHours: req.body.overtimeHours,
        weeks: req.body.weeks,
      });

      // Validate input
      const normalHours = Number(req.body.normalHours) || 0;
      const overtimeHours = Number(req.body.overtimeHours) || 0;

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        status: { $ne: "locked" },
        isFinalized: false,
      }).sort({ startDate: -1 });

      if (!currentPeriod) {
        return res.status(400).json({
          success: false,
          message: "No active payroll period found",
        });
      }

      // Process the weeks data
      const weeks = [];
      if (Array.isArray(req.body.weeks)) {
        req.body.weeks.forEach((week) => {
          if (week.weekEndingDate) {
            weeks.push({
              weekEndingDate: week.weekEndingDate,
              sundayHours: Number(week.sundayHours) || 0,
            });
          }
        });
      }

      console.log("Processed data:", {
        normalHours,
        overtimeHours,
        weeksCount: weeks.length,
      });

      // Create or update hourly rates
      const hourlyRates = await HourlyRate.findOneAndUpdate(
        {
          employee: employeeId,
          payrollPeriod: currentPeriod._id,
        },
        {
          $set: {
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
            normalHours,
            overtimeHours,
            weeks,
          },
        },
        { upsert: true, new: true }
      );

      console.log("Saved hourly rates:", {
        id: hourlyRates._id,
        normalHours: hourlyRates.normalHours,
        overtimeHours: hourlyRates.overtimeHours,
        weeksCount: hourlyRates.weeks.length,
      });

      res.json({
        success: true,
        message: "Hourly rates saved successfully",
        data: hourlyRates,
      });
    } catch (error) {
      console.error("Error saving hourly rates:", error);
      res.status(500).json({
        success: false,
        message: "An error occurred while saving hourly rates",
      });
    }
  }
);

// Update the addNew route handler
// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    throw error; // Re-throw the error to be handled by the calling function
  }
}

router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      const currentPeriod = {
        startDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        ),
        endDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        ),
      };

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(currentDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(currentDate),
          basicSalary: 0,
        });
        await payroll.save();
      }

      // Format the date directly
      const formattedDate = currentPeriod.endDate.toLocaleDateString("en-GB");

      console.log("Rendering addBasicSalary with:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll._id,
        currentPeriod,
      });

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod,
        formattedDate,
        payroll,
        relevantDate: currentDate,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post("/saveBasicSalary", async (req, res) => {
  try {
    const { employeeId, relevantDate, companyId, basicSalary } = req.body;

    if (!employeeId || !relevantDate || !companyId || !basicSalary) {
      console.error("Missing required fields:", req.body);
      req.flash("error", "Missing required fields");
      return res.redirect(
        `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
      );
    }

    const formattedDate = formatDateForMongoDB(new Date(relevantDate));

    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: companyId,
      month: formattedDate,
    });

    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        company: companyId,
        month: formattedDate,
      });
    }

    payroll.basicSalary = parseFloat(basicSalary);
    await payroll.save();

    req.flash("success", "Basic salary updated successfully");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    console.error("Error saving basic salary:", error);
    req.flash("error", "An error occurred while saving the basic salary");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
    );
  }
});

router.get(
  "/:companyCode/addBasicSalary/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Find the most recent unfinalized payroll
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Determine relevant date
      let relevantDate;
      if (latestPayroll && !latestPayroll.finalised) {
        relevantDate = latestPayroll.month;
      } else {
        relevantDate = getCurrentOrNextLastDayOfMonth();
      }

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(relevantDate),
          basicSalary: latestPayroll ? latestPayroll.basicSalary : 0,
        });
        await payroll.save();
      }

      // Render the template with all necessary data
      res.render("addBasicSalary", {
        employee,
        payroll,
        relevantDate,
        company,
        companyCode,
        formatDateForDisplay, // Pass the function to the template
        formattedDate: formatDateForDisplay(relevantDate),
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass CSRF token
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect("/");
    }
  }
);

// GET route for classifications
router.get(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      res.render("classifications", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering classifications form:", error);
      req.flash("error", "Failed to load classifications form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for classifications
router.post(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        workingHours,
        isDirector,
        typeOfDirector,
        isContractor,
        isUifExempt,
        uifExemptReason,
      } = req.body;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update employee
      const employee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        {
          $set: {
            workingHours,
            isDirector: !!isDirector,
            typeOfDirector: isDirector ? typeOfDirector : null,
            isContractor: !!isContractor,
            isUifExempt: !!isUifExempt,
            uifExemptReason: isUifExempt ? uifExemptReason : null,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      req.flash("success", "Classifications updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating classifications:", error);
      req.flash("error", "Failed to update classifications");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/classifications`
      );
    }
  }
);

// RFI Configuration Constants
const RFI_VALIDATION = {
  MIN_PERCENTAGE: 0,
  MAX_PERCENTAGE: 100,
  MIN_COMPONENTS: 1,
  REQUIRED_COMPONENTS: ["basicSalary"], // Components that must be included
};

// RFI Validation Middleware
const validateRFIConfig = (req, res, next) => {
  const { rfiOption, selectedIncomes, percentageValues } = req.body;
  const errors = [];

  try {
    // Validate RFI Option
    if (!["selectedIncome", "percentagePerIncome"].includes(rfiOption)) {
      errors.push("Invalid RFI calculation method");
    }

    // Validate Selected Incomes
    if (rfiOption === "selectedIncome") {
      if (
        !selectedIncomes ||
        !Array.isArray(selectedIncomes) ||
        selectedIncomes.length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be selected`
        );
      }

      // Check for required components
      const hasRequiredComponents = RFI_VALIDATION.REQUIRED_COMPONENTS.every(
        (comp) => selectedIncomes.includes(comp)
      );
      if (!hasRequiredComponents) {
        errors.push("Basic salary must be included in RFI calculation");
      }
    }

    // Validate Percentage Values
    if (rfiOption === "percentagePerIncome") {
      if (
        !percentageValues ||
        Object.keys(percentageValues).length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be configured`
        );
      }

      // Validate percentage ranges
      Object.entries(percentageValues).forEach(([component, percentage]) => {
        const value = parseFloat(percentage);
        if (
          isNaN(value) ||
          value < RFI_VALIDATION.MIN_PERCENTAGE ||
          value > RFI_VALIDATION.MAX_PERCENTAGE
        ) {
          errors.push(
            `Invalid percentage value for ${component}. Must be between ${RFI_VALIDATION.MIN_PERCENTAGE} and ${RFI_VALIDATION.MAX_PERCENTAGE}`
          );
        }
      });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("RFI Validation Error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating RFI configuration",
    });
  }
};

// GET route for RFI configuration
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId)
        .populate("rfiConfig.selectedIncomes.componentId")
        .populate("rfiConfig.percentageIncomes.componentId");

      // Get the employee's payroll record with basic salary
      const payroll = await Payroll.findOne({ employee: employeeId });
      const assignedComponents = [];

      // Add basic salary if it exists in payroll
      if (payroll?.basicSalary) {
        assignedComponents.push({
          id: "basicSalary",
          name: "Basic Salary",
          amount: payroll.basicSalary,
          type: "income",
          isSelected:
            employee.rfiConfig?.selectedIncomes?.some(
              (inc) => inc.name === "Basic Salary"
            ) || false,
        });
      }

      // Add other components from payroll if they exist and have values
      if (payroll) {
        // Commission
        if (payroll.commission && payroll.commission.amount) {
          assignedComponents.push({
            id: "commission",
            name: "Commission",
            amount: payroll.commission.amount,
            type: "income",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Commission"
              ) || false,
          });
        }

        // Travel Allowance
        if (payroll.travelAllowance && payroll.travelAllowance.amount) {
          assignedComponents.push({
            id: "travelAllowance",
            name: "Travel Allowance",
            amount: payroll.travelAllowance.amount,
            type: "allowance",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Travel Allowance"
              ) || false,
          });
        }

        // Add other allowances if they exist
        if (payroll.allowances && payroll.allowances.length > 0) {
          payroll.allowances.forEach((allowance) => {
            if (allowance.amount) {
              assignedComponents.push({
                id: allowance._id.toString(),
                name: allowance.name,
                amount: allowance.amount,
                type: "allowance",
                isSelected:
                  employee.rfiConfig?.selectedIncomes?.some(
                    (inc) => inc.name === allowance.name
                  ) || false,
              });
            }
          });
        }

        // Add any other relevant components
        // ... add other components as needed
      }

      // Sort components by type and name
      assignedComponents.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === "income" ? -1 : 1;
      });

      res.render("defineRFI", {
        company,
        employee,
        payComponents: assignedComponents, // Pass the assigned components
        rfiConfig: employee.rfiConfig || {
          option: "",
          selectedIncomes: [],
          percentageIncomes: {},
        },
        validation: RFI_VALIDATION,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI configuration form:", error);
      req.flash(
        "error",
        "An error occurred while loading the RFI configuration"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for RFI configuration
router.post(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { option, selectedIncomes, percentageValues } = req.body;

      // Get company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Validate required fields
      if (!option) {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: "RFI calculation method is required",
        });
      }

      // Validate based on selected option
      if (option === "selectedIncome") {
        if (
          !selectedIncomes ||
          !Array.isArray(selectedIncomes) ||
          selectedIncomes.length === 0
        ) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: "At least one income component must be selected",
          });
        }
      } else if (option === "percentagePerIncome") {
        if (!percentageValues || Object.keys(percentageValues).length === 0) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message:
              "Percentage values are required for at least one component",
          });
        }

        // Validate percentage values
        for (const [component, percentage] of Object.entries(
          percentageValues
        )) {
          const value = parseFloat(percentage);
          if (isNaN(value) || value < 0 || value > 100) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Invalid percentage value for ${component}. Must be between 0 and 100`,
            });
          }
        }
      }

      // Update employee RFI configuration
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        {
          $set: {
            "rfiConfig.option": option,
            "rfiConfig.selectedIncomes":
              option === "selectedIncome"
                ? selectedIncomes.map((name) => ({
                    name,
                    includeInCalculation: true,
                  }))
                : [],
            "rfiConfig.percentageIncomes":
              option === "percentagePerIncome"
                ? Object.entries(percentageValues).map(
                    ([name, percentage]) => ({
                      name,
                      percentage: parseFloat(percentage),
                    })
                  )
                : [],
          },
        },
        { new: true, session }
      );

      if (!updatedEmployee) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Add audit log with company ID
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id, // Add company ID here
            action: "UPDATE_RFI_CONFIG",
            entity: "Employee",
            entityId: employeeId,
            details: {
              option,
              selectedIncomes:
                option === "selectedIncome" ? selectedIncomes : [],
              percentageValues:
                option === "percentagePerIncome" ? percentageValues : {},
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "RFI configuration updated successfully",
        data: updatedEmployee.rfiConfig,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating RFI configuration:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update RFI configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

// Add these validation rules at the top of the file
const FUND_VALIDATION = {
  FIXED_AMOUNT: {
    MIN: 0,
    MAX: 1000000000, // 1 billion
    DECIMALS: 2,
  },
  PERCENTAGE: {
    MIN: 0,
    MAX: 100,
    DECIMALS: 2,
  },
  CATEGORY_FACTOR: {
    MIN: 0.01,
    MAX: 10,
    DECIMALS: 2,
  },
};

// Add validation middleware
const validateFundInput = (req, res, next) => {
  const {
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
  } = req.body;

  const errors = [];

  try {
    // Validate contribution method
    if (!["fixedAmount", "percentageRFI"].includes(contributionCalculation)) {
      errors.push("Invalid contribution calculation method");
    }

    // Validate fixed amounts if that method is selected
    if (contributionCalculation === "fixedAmount") {
      const employeeAmount = parseFloat(fixedContributionEmployee);
      const employerAmount = parseFloat(fixedContributionEmployer);

      if (
        isNaN(employeeAmount) ||
        employeeAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employeeAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employee fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }

      if (
        isNaN(employerAmount) ||
        employerAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employerAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employer fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }
    }

    // Validate RFI percentages if that method is selected
    if (contributionCalculation === "percentageRFI") {
      const employeePercentage = parseFloat(rfiEmployee);
      const employerPercentage = parseFloat(rfiEmployer);

      if (
        isNaN(employeePercentage) ||
        employeePercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employeePercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employee RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }

      if (
        isNaN(employerPercentage) ||
        employerPercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employerPercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employer RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }
    }

    // Validate category factor
    if (categoryFactor) {
      const factor = parseFloat(categoryFactor);
      if (
        isNaN(factor) ||
        factor < FUND_VALIDATION.CATEGORY_FACTOR.MIN ||
        factor > FUND_VALIDATION.CATEGORY_FACTOR.MAX
      ) {
        errors.push(
          `Category factor must be between ${FUND_VALIDATION.CATEGORY_FACTOR.MIN} and ${FUND_VALIDATION.CATEGORY_FACTOR.MAX}`
        );
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("Fund validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating fund configuration",
    });
  }
};

// GET route for regular hours page
router.get(
  "/:companyCode/:employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Fetch the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Fetch the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the regular hours page
      res.render("regularHours", {
        company,
        employee,
        csrfToken: req.csrfToken(), // Add CSRF token
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering regular hours form:", error);
      req.flash("error", "Failed to load regular hours form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

//_____________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection, // Make sure this middleware is here
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Generate CSRF token
      const csrfToken = req.csrfToken();
      console.log("Generated CSRF token:", csrfToken); // Debug log

      // Render with all necessary data
      res.render("eti", {
        company,
        employee,
        user: req.user,
        csrfToken, // Pass the token explicitly
        messages: req.flash(),
        moment: require("moment"), // Add moment if needed for date formatting
      });
    } catch (error) {
      console.error("Error rendering ETI page:", error);
      req.flash("error", "An error occurred while loading the ETI page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for ETI updates
router.post(
  "/:companyCode/employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1);
      effectiveDate.setHours(0, 0, 0, 0);

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/eti`
      );
    }
  }
);

//__________________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("skillsEquity", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering Skills Equity page:", error);
      req.flash(
        "error",
        "An error occurred while loading the Skills Equity page"
      );
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// POST route for updating Skills Equity
router.post(
  "/:companyCode/employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        gender,
        race,
        occupationLevel,
        occupationCategory,
        jobValue,
        province,
        disabled,
        foreignNational,
        notRSACitizen,
      } = req.body;

      // First find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find and update employee using company._id
      const employee = await Employee.findOneAndUpdate(
        {
          _id: employeeId,
          company: company._id, // Use company._id instead of req.company._id
        },
        {
          $set: {
            gender,
            race,
            occupationLevel,
            occupationCategory,
            jobValue,
            province,
            disabled: !!disabled,
            foreignNational: !!foreignNational,
            notRSACitizen: !!notRSACitizen,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Success case
      req.flash("success", "Skills & Equity information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating Skills & Equity:", error);
      req.flash("error", "Failed to update Skills & Equity information");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/skillsEquity`
      );
    }
  }
);

// Update basic salary route
router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, basicSalary, hourlyPaid } = req.body;
      const { companyCode } = req.params;

      console.log("Updating basic salary:", {
        employeeId,
        basicSalary,
        hourlyPaid,
        companyCode,
      });

      // Find employee and company
      const employee = await Employee.findById(employeeId);
      const company = await Company.findOne({ companyCode });

      if (!employee || !company) {
        throw new Error("Employee or company not found");
      }

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      if (!currentPeriod) {
        throw new Error("No active period found");
      }

      // Update current period
      currentPeriod.basicSalary = parseFloat(basicSalary);
      await currentPeriod.save({ session });

      // Update current payroll
      const currentPayroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: currentPeriod.endDate,
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        {
          new: true,
          upsert: true,
          session,
        }
      );

      // Update all future periods
      await PayrollPeriod.updateMany(
        {
          employee: employeeId,
          company: company._id,
          startDate: { $gt: currentPeriod.endDate },
          isFinalized: false,
        },
        {
          $set: { basicSalary: parseFloat(basicSalary) },
        },
        { session }
      );

      // Update all future payrolls
      await Payroll.updateMany(
        {
          employee: employeeId,
          company: company._id,
          month: { $gt: currentPeriod.endDate },
          status: "draft",
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        { session }
      );

      // Update employee's hourly status
      if (!employee.regularHours) {
        employee.regularHours = {};
      }
      employee.regularHours.hourlyPaid = hourlyPaid === "on";
      await employee.save({ session });

      await session.commitTransaction();

      console.log("Basic salary update completed:", {
        currentPeriod: currentPeriod._id,
        basicSalary: parseFloat(basicSalary),
        futurePeriodsUpdated: true,
      });

      req.flash("success", "Basic salary updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error saving basic salary:", error);
      req.flash("error", "Failed to update basic salary");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/addBasicSalary`
      );
    } finally {
      session.endSession();
    }
  }
);

router.get(
  "/:companyCode/addBasicSalary/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Find the most recent unfinalized payroll
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Determine relevant date
      let relevantDate;
      if (latestPayroll && !latestPayroll.finalised) {
        relevantDate = latestPayroll.month;
      } else {
        relevantDate = getCurrentOrNextLastDayOfMonth();
      }

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(relevantDate),
          basicSalary: latestPayroll ? latestPayroll.basicSalary : 0,
        });
        await payroll.save();
      }

      // Render the template with all necessary data
      res.render("addBasicSalary", {
        employee,
        payroll,
        relevantDate,
        company,
        companyCode,
        formatDateForDisplay, // Pass the function to the template
        formattedDate: formatDateForDisplay(relevantDate),
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass CSRF token
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect("/");
    }
  }
);

// Add these routes after your existing routes
router.post(
  "/api/employees/:employeeId/enableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Get base URL for email
      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user if needed
        if (
          !user.role ||
          user.role.toString() !== employeeRole._id.toString()
        ) {
          user.role = employeeRole._id;
          await user.save();
        }
      } else {
        // Create new user account
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          password: crypto.randomBytes(20).toString("hex"), // Temporary password
        });

        await user.save();
      }

      // Link user and role to employee
      employee.user = user._id;
      employee.role = employeeRole._id;
      employee.selfServiceEnabled = true;

      // Generate and send setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      await employee.save();

      res.json({
        success: true,
        message: "Self-service enabled successfully",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/disableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/resendInvite",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Resend setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      res.json({
        success: true,
        message: "Invitation resent successfully",
      });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend invite",
        error: error.message,
      });
    }
  }
);

// Self Service Routes
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Update the enable self-service route
router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      await employee.save();

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Generate new token
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );

      await employee.save();

      // Send new setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account setup link has been renewed. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);