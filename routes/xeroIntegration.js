const express = require('express');
const { XeroClient } = require('xero-node');
const Integration = require('../models/Integration');
const Company = require('../models/Company');
const crypto = require('crypto');
const dotenv = require('dotenv');
const path = require('path');
const XeroAccountService = require('../services/xeroAccountService');
const XeroAccountMapping = require('../models/xeroAccountMapping');
const XeroTokenManager = require('../services/xeroTokenManager');
const debug = require('debug')('x-pay:xero');

// Create router
const router = express.Router({ mergeParams: true });

// Initialize token manager
const tokenManager = new XeroTokenManager();

// Note: Callback handling moved to app.js

// Load Xero configuration
dotenv.config({ path: path.resolve(__dirname, '../config/xero.env') });

// Define required scopes
const XERO_SCOPES = [
  'offline_access',
  'openid',
  'profile',
  'email',
  'accounting.transactions',
  'accounting.settings',
  'accounting.contacts'
].join(' ');

// Debug function for consistent logging
function debugLog(title, data = {}) {
  console.log('Timestamp:', new Date().toISOString());
  Object.entries(data).forEach(([key, value]) => {
    console.log(key + ':', typeof value === 'object' ? JSON.stringify(value, null, 2) : value);
  });
}

// Log environment variables
debugLog('Environment Variables', {
  XERO_CLIENT_ID: process.env.XERO_CLIENT_ID ? '***' + process.env.XERO_CLIENT_ID.slice(-4) : undefined,
  XERO_SCOPES: process.env.XERO_SCOPES,
  NODE_ENV: process.env.NODE_ENV,
  HOST: process.env.HOST,
  PROTOCOL: process.env.PROTOCOL
});

// Check for required environment variables
const requiredEnvVars = ['XERO_CLIENT_ID', 'XERO_CLIENT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

// Session monitoring middleware
router.use((req, res, next) => {
  const originalEnd = res.end;
  res.end = function() {
    debugLog('Session State at Response End', {
      path: req.path,
      method: req.method,
      sessionId: req.sessionID,
      hasSession: !!req.session,
      sessionKeys: req.session ? Object.keys(req.session) : [],
      xeroState: req.session?.xeroState,
      companyCode: req.session?.companyCode
    });
    originalEnd.apply(this, arguments);
  };

  next();
});

// Debug middleware
router.use((req, res, next) => {
  console.log('Router Stack:', router.stack
    .filter(r => r.route)
    .map(r => ({ 
      path: r.route.path,
      methods: Object.keys(r.route.methods)
    })));


  debugLog('Request Details', {
    path: req.path,
    method: req.method,
    companyCode: req.params.companyCode,
    fullUrl: req.originalUrl,
    baseUrl: req.baseUrl,
    query: req.query,
    headers: {
      ...req.headers,
      cookie: '***masked***'  // Mask sensitive data
    },
    session: {
      ...req.session,
      id: req.sessionID,
      passport: req.session.passport ? { ...req.session.passport, user: '***masked***' } : undefined
    }
  });
  next();
});

// Apply company middleware to all routes
router.use(async (req, res, next) => {
  try {
    debugLog('Company Middleware Start', {
      companyCode: req.params.companyCode,
      userId: req.user?._id,
      path: req.path,
      method: req.method
    });

    const companyCode = req.params.companyCode;
    if (!companyCode) {
      debugLog('Company Middleware Error', { error: 'Company code is required' });
      return res.status(400).json({ error: 'Company code is required' });
    }

    const company = await Company.findOne({ companyCode: companyCode });
    if (!company) {
      debugLog('Company Middleware Error', { 
        error: 'Company not found',
        searchedCode: companyCode
      });
      return res.status(404).json({ error: 'Company not found' });
    }

    // Check if user has access to this company
    const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
    if (!userCompanyIds.includes(company._id.toString())) {
      debugLog('Company Middleware Error', {
        error: 'Access denied',
        userCompanies: userCompanyIds,
        requestedCompany: company._id.toString()
      });
      return res.status(403).json({ error: 'Access denied to this company' });
    }

    debugLog('Company Middleware Success', {
      companyId: company._id,
      companyCode: company.companyCode,
      path: req.path
    });

    // Add company to request object
    req.company = company;
    next();
  } catch (error) {
    debugLog('Company Middleware Error', {
      error: error.message,
      stack: error.stack,
      path: req.path
    });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Configure Xero Client
function getXeroClient(companyCode) {
  const protocol = process.env.PROTOCOL || 'http';
  const host = process.env.HOST || 'localhost:3002';
  const redirectUri = `${protocol}://${host}/xero/callback`;
  
  debugLog('Xero Client Configuration', {
    clientId: '***' + process.env.XERO_CLIENT_ID.slice(-4),
    redirectUri,
    scopes: XERO_SCOPES.split(' '),
    environment: process.env.NODE_ENV,
    host,
    protocol,
    companyCode
  });

  const client = new XeroClient({
    clientId: process.env.XERO_CLIENT_ID,
    clientSecret: process.env.XERO_CLIENT_SECRET,
    redirectUris: [redirectUri],
    scopes: XERO_SCOPES.split(' '),
    state: crypto.randomBytes(32).toString('hex'),
    httpTimeout: 10000 // 10 seconds
  });

  return client;
}

// Middleware to ensure company code is in session
router.use((req, res, next) => {
  if (req.params.companyCode && !req.session.companyCode) {
    req.session.companyCode = req.params.companyCode;
    debugLog('Setting Company Code in Session', {
      companyCode: req.params.companyCode,
      sessionId: req.sessionID
    });
  }
  next();
});

// Path normalization middleware
router.use((req, res, next) => {
  // Remove /xero prefix if present
  if (req.path.startsWith('/xero/')) {
    req.url = req.url.replace('/xero/', '/');
    debugLog('Path Normalized', {
      originalPath: req.path,
      newPath: req.url,
      baseUrl: req.baseUrl
    });
  }
  next();
});

// Connect to Xero
router.get(['/connect', '/xero/connect'], async (req, res) => {
  const companyCode = req.params.companyCode;
  debugLog('Connect Request', {
    originalUrl: req.originalUrl,
    normalizedUrl: req.url,
    params: req.params,
    path: req.path,
    baseUrl: req.baseUrl,
    originalUrl: req.originalUrl,
    companyCode
  });
  if (!companyCode) {
    debugLog('Connect Error - Missing Company Code', {
      params: req.params,
      path: req.path,
      url: req.url,
      originalUrl: req.originalUrl
    });
    return res.status(400).json({ error: 'Company code is required' });
  }

  const company = await Company.findOne({ companyCode });
  if (!company) {
    debugLog('Connect Error - Company Not Found', {
      companyCode,
      params: req.params
    });
    return res.status(404).json({ error: 'Company not found' });
  }

  debugLog('Connect Request Parameters', {
    companyCode,
    params: req.params,
    path: req.path,
    baseUrl: req.baseUrl,
    originalUrl: req.originalUrl
  });
  debugLog('Connect Request Received', {
    url: req.url,
    method: req.method,
    headers: req.headers,
    query: req.query,
    params: req.params,
    session: {
      id: req.sessionID,
      cookie: req.session?.cookie ? {
        maxAge: req.session.cookie.maxAge,
        expires: req.session.cookie.expires,
        secure: req.session.cookie.secure,
        httpOnly: req.session.cookie.httpOnly
      } : 'No cookie'
    }
  });
  try {
    debugLog('Starting Xero Connect', {
      path: req.path,
      companyCode: req.params.companyCode,
      userId: req.user?._id,
      baseUrl: req.baseUrl,
      originalUrl: req.originalUrl,
      sessionId: req.sessionID,
      hasSession: !!req.session,
      sessionKeys: Object.keys(req.session || {}),
      headers: {
        ...req.headers,
        cookie: '***masked***'
      },
      protocol: req.protocol,
      hostname: req.hostname,
      host: req.get('host'),
      nodeEnv: process.env.NODE_ENV
    });

    try {
      // Initialize Xero client with company code
      const xeroClient = getXeroClient(companyCode);
      
      // Generate state and store in session
      const state = crypto.randomBytes(32).toString('hex');
      req.session.xeroState = state;
      req.session.xeroConnectStarted = Date.now();
      req.session.companyCode = companyCode;
      
      // Save session before redirect
      await new Promise((resolve, reject) => {
        req.session.save(err => {
          if (err) reject(err);
          else resolve();
        });
      });

      debugLog('Session Before Save', {
        sessionId: req.sessionID,
        xeroState: state,
        companyCode,
        sessionKeys: Object.keys(req.session),
        cookie: {
          maxAge: req.session.cookie.maxAge,
          expires: req.session.cookie.expires,
          httpOnly: req.session.cookie.httpOnly,
          secure: req.session.cookie.secure
        }
      });

      // Build the authorization URL with all required parameters
      const consentUrl = await xeroClient.buildConsentUrl({
        state,
        redirectUri: xeroClient.config.redirectUris[0],
        scope: XERO_SCOPES
      });
      
      debugLog('Redirecting to Xero', {
        consentUrl,
        state,
        companyCode,
        sessionId: req.sessionID,
        redirectUri: xeroClient.config.redirectUris[0],
        sessionState: req.session.xeroState,
        sessionCompanyCode: req.session.companyCode,
        xeroConnectStarted: req.session.xeroConnectStarted,
        scopes: XERO_SCOPES.split(' ')
      });

      // Final session check before redirect
      debugLog('Final Session Check', {
        sessionId: req.sessionID,
        hasSession: !!req.session,
        xeroState: req.session.xeroState,
        companyCode: req.session.companyCode,
        sessionKeys: Object.keys(req.session),
        cookie: {
          maxAge: req.session.cookie.maxAge,
          expires: req.session.cookie.expires
        }
      });

      res.redirect(consentUrl);
    } catch (error) {
      debugLog('Connect Error', {
        error: error.message,
        stack: error.stack,
        companyCode,
        sessionId: req.sessionID
      });
      res.status(500).json({ error: 'Failed to connect to Xero' });
    }
  } catch (error) {
    debugLog('Connect Error', {
      error: error.message,
      stack: error.stack,
      companyCode: req.params.companyCode,
      path: req.path,
      sessionId: req.sessionID,
      sessionExists: !!req.session,
      sessionKeys: req.session ? Object.keys(req.session) : [],
      headers: {
        ...req.headers,
        cookie: '***masked***'
      }
    });
    res.redirect(`/clients/${req.params.companyCode}/settings/accounting/accounting-integrations?error=${encodeURIComponent(error.message)}`);
  }
});

// Render accounting integrations page
router.get('/accounting-integrations', async (req, res) => {
  try {
    debugLog('Rendering Integrations Page', {
      companyId: req.company._id,
      userId: req.user._id,
      path: req.path
    });

    // Check if Xero is connected
    const integration = await Integration.findOne({
      company: req.company._id,
      provider: 'xero',
      type: 'accounting'
    });

    debugLog('Found Integration', {
      exists: !!integration,
      status: integration?.status,
      provider: integration?.provider,
      type: integration?.type,
      credentials: integration?.credentials ? 'present' : 'missing',
      tenantId: integration?.credentials?.tenantId,
      tokenExpiry: integration?.credentials?.expiresAt
    });

    const isConnected = integration?.status === 'active' && 
                       integration?.credentials?.accessToken && 
                       integration?.credentials?.tenantId;

    res.render('settings/accounting-integrations', {
      company: req.company,
      xeroConnected: isConnected,
      isXeroConnected: isConnected,
      activeTab: 'accounting',
      error: req.query.error,
      success: req.query.success,
      csrfToken: req.csrfToken(),
      xeroIntegration: integration
    });
  } catch (error) {
    console.error('Error rendering integrations page:', error);
    res.status(500).send('Error loading integrations page');
  }
});

// Test Xero Connection
router.get('/test-connection', async (req, res) => {
  try {
    
    // 1. Check if integration exists
    const integration = await Integration.findOne({
      company: req.company._id,
      provider: 'xero',
      type: 'accounting'
    });

    console.log('Integration record:', {
      exists: !!integration,
      status: integration?.status,
      hasCredentials: !!integration?.credentials,
      hasAccessToken: !!integration?.credentials?.accessToken,
      hasTenantId: !!integration?.credentials?.tenantId,
      tokenExpiry: integration?.credentials?.expiresAt
    });

    if (!integration) {
      throw new Error('No Xero integration found');
    }

    // 2. Check if credentials are present
    if (!integration.credentials) {
      throw new Error('No Xero credentials found');
    }

    // 3. Check token expiry using standardized token manager
    const tokenData = tokenManager.getTokenData(integration);
    const timeUntilExpiry = tokenManager.getTimeUntilExpiry(integration);
    const isExpired = timeUntilExpiry <= 0;

    console.log('Token expiry check:', {
      now: new Date().toISOString(),
      expiry: tokenData.expiresAt ? tokenData.expiresAt.toISOString() : null,
      timeUntilExpirySeconds: timeUntilExpiry,
      isExpired: isExpired
    });

    if (isExpired) {
      throw new Error('Xero access token has expired');
    }

    // 4. Test API connection using standardized token manager
    const xeroClient = tokenManager.createXeroClient();
    await xeroClient.initialize();

    // Set token using standardized format
    const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);
    xeroClient.setTokenSet(xeroTokenSet);

    const response = await xeroClient.accountingApi.getAccounts(tokenData.tenantId);

    console.log('Xero API test result:', {
      success: true,
      accountsCount: response.body.accounts?.length || 0
    });

    res.json({
      status: 'success',
      message: 'Xero connection is working',
      details: {
        integration: {
          status: integration.status,
          lastSync: integration.lastSync
        },
        token: {
          expiresAt: tokenData.expiresAt,
          timeUntilExpirySeconds: timeUntilExpiry,
          isExpired: isExpired
        },
        apiTest: {
          success: true,
          accountsCount: response.body.accounts?.length || 0
        }
      }
    });
  } catch (error) {
    console.error('Xero connection test failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Xero connection test failed',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Sync with Xero
router.post('/sync', async (req, res) => {
  try {
    const companyCode = req.params.companyCode;
    debugLog('Starting Xero Sync', {
      companyCode,
      userId: req.user._id,
      path: req.path
    });

    const xeroClient = getXeroClient(companyCode);
    
    const integration = await Integration.findOne({
      company: req.user.currentCompany,
      provider: 'xero'
    });

    if (!integration || integration.status !== 'active') {
      debugLog('Sync Error', {
        error: 'Xero integration not found or inactive'
      });
      throw new Error('Xero integration not found or inactive');
    }

    // Refresh token if needed using standardized token manager
    if (tokenManager.needsRefresh(integration)) {
      const companyId = req.company._id.toString();
      const shouldRefresh = await tokenManager.acquireRefreshMutex(companyId);

      if (shouldRefresh) {
        try {
          debugLog('Refreshing Token', {
            companyId: companyId,
            timeUntilExpiry: tokenManager.getTimeUntilExpiry(integration)
          });

          const tokenData = tokenManager.getTokenData(integration);
          const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);
          xeroClient.setTokenSet(xeroTokenSet);

          const newTokenSet = await xeroClient.refreshToken();

          const newTokenData = tokenManager.fromXeroTokenSet(newTokenSet);
          tokenManager.setTokenData(integration, newTokenData);
          await integration.save();

          debugLog('Refreshed Token', {
            newExpiresAt: newTokenData.expiresAt,
            timeUntilExpiry: tokenManager.getTimeUntilExpiry(integration)
          });
        } finally {
          tokenManager.releaseRefreshMutex(companyId);
        }
      } else {
        debugLog('Token refresh handled by another process');
      }
    }

    // TODO: Implement sync logic here
    
    integration.lastSync = new Date();
    await integration.save();
    debugLog('Sync Complete', {
      lastSync: integration.lastSync
    });

    res.json({ success: true });
  } catch (error) {
    debugLog('Sync Error', {
      error: error.message,
      stack: error.stack,
      companyCode: req.params.companyCode,
      path: req.path
    });
    res.status(500).json({ error: error.message });
  }
});

// Disconnect from Xero
router.post('/disconnect', async (req, res) => {
  try {
    debugLog('Starting Xero Disconnect', {
      path: req.path,
      companyCode: req.params.companyCode,
      userId: req.user._id
    });

    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    debugLog('Found Company', {
      companyId: company._id,
      companyCode: company.companyCode
    });

    // Delete the integration
    await Integration.deleteOne({
      company: company._id,
      provider: 'xero'
    });

    debugLog('Integration Deleted', {
      companyId: company._id,
      provider: 'xero'
    });

    res.json({
      status: 'success',
      message: 'Successfully disconnected from Xero'
    });
  } catch (error) {
    debugLog('Disconnect Error', {
      error: error.message,
      stack: error.stack,
      companyCode: req.params.companyCode
    });

    res.status(500).json({
      status: 'error',
      message: 'Failed to disconnect from Xero: ' + error.message
    });
  }
});

// Callback handling moved to app.js

router.get('/accounts', async (req, res) => {
  try {
    debugLog('Fetching Xero Accounts', {
      companyCode: req.params.companyCode,
      path: req.path,
      companyId: req.company?._id,
      hasUser: !!req.user
    });

    const integration = await Integration.findOne({
      company: req.company._id,
      provider: 'xero',
      type: 'accounting'
    });

    if (!integration || integration.status !== 'active') {
      throw new Error('Xero integration not found or not active');
    }

    // Get token data using standardized token manager
    const tokenData = tokenManager.getTokenData(integration);

    debugLog('Found Integration', {
      exists: !!integration,
      status: integration?.status,
      hasAccessToken: !!tokenData.accessToken,
      hasRefreshToken: !!tokenData.refreshToken,
      tokenExpiry: tokenData.expiresAt,
      tenantId: tokenData.tenantId,
      timeUntilExpiry: tokenManager.getTimeUntilExpiry(integration)
    });

    if (!tokenData.accessToken) {
      throw new Error('Xero integration not connected - no access token');
    }

    // Configure Xero client using standardized token manager
    const xeroClient = tokenManager.createXeroClient();
    await xeroClient.initialize();

    debugLog('Setting Token Set', {
      hasAccessToken: !!tokenData.accessToken,
      hasRefreshToken: !!tokenData.refreshToken,
      tokenExpiry: tokenData.expiresAt
    });

    const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);
    xeroClient.setTokenSet(xeroTokenSet);

    // Get the first connected tenant
    debugLog('Updating Tenants');
    const tenants = await xeroClient.updateTenants();
    
    debugLog('Found Tenants', {
      count: tenants?.length,
      firstTenantId: tenants?.[0]?.tenantId,
      savedTenantId: tokenData.tenantId
    });

    const activeTenant = tenants[0];

    if (!activeTenant) {
      throw new Error('No active Xero organization found');
    }

    // Fetch accounts from Xero
    debugLog('Fetching Accounts from Xero API');
    const response = await xeroClient.accountingApi.getAccounts(activeTenant.tenantId);
    const accounts = response.body.accounts || [];

    debugLog('Accounts Fetched', {
      count: accounts.length,
      firstAccount: accounts[0] ? {
        name: accounts[0].name,
        code: accounts[0].code,
        type: accounts[0].type
      } : null
    });

    // Save tenant ID if not already saved
    if (!tokenData.tenantId) {
      debugLog('Saving Tenant ID', {
        oldTenantId: tokenData.tenantId,
        newTenantId: activeTenant.tenantId
      });

      tokenData.tenantId = activeTenant.tenantId;
      tokenManager.setTokenData(integration, tokenData);
      await integration.save();
    }

    res.json(accounts);
  } catch (error) {
    debugLog('Account Fetch Error', {
      error: error.message,
      stack: error.stack,
      type: error.constructor.name
    });
    res.status(500).json({ error: error.message });
  }
});

router.get('/mappings', async (req, res) => {
  debugLog('Fetching Xero Mappings', {
    companyCode: req.params.companyCode,
    path: req.path,
    companyId: req.company?._id,
    method: req.method
  });

  try {
    if (!req.company) {
      debugLog('Company not found in request');
      return res.status(404).json({ error: 'Company not found' });
    }

    const mappings = await XeroAccountMapping.find({ company: req.company._id });
    debugLog('Found mappings', { 
      count: mappings.length,
      mappings: mappings.map(m => ({
        payrollItem: m.payrollItem,
        xeroAccountCode: m.xeroAccountCode
      }))
    });
    
    // Convert array of mappings to object format for frontend
    const mappingObject = {};
    mappings.forEach(mapping => {
      mappingObject[mapping.payrollItem] = mapping.xeroAccountCode;
    });

    debugLog('Mapped object', mappingObject);
    res.json(mappingObject);
  } catch (error) {
    debugLog('Mappings Fetch Error', {
      error: error.message,
      stack: error.stack,
      type: error.constructor.name
    });
    res.status(500).json({ error: error.message });
  }
});

router.post('/mappings', async (req, res) => {
  debug('=== Xero Mappings Save Debug ===');
  debug('Request params:', {
    companyCode: req.params.companyCode,
    path: req.path,
    method: req.method,
    body: req.body,
    company: req.company?._id
  });

  try {
    if (!req.company) {
      debug('Company not found in request');
      return res.status(404).json({ error: 'Company not found' });
    }

    // Validate required mappings
    const requiredItems = ['salary', 'paye', 'uif', 'sdl'];
    const missingRequired = requiredItems.filter(item => !req.body[item]);
    
    if (missingRequired.length > 0) {
      debug('Missing required mappings:', missingRequired);
      return res.status(400).json({
        error: 'Missing required mappings',
        missingItems: missingRequired
      });
    }

    // Update or create mappings
    const operations = Object.entries(req.body).map(([payrollItem, xeroAccountCode]) => ({
      updateOne: {
        filter: { company: req.company._id, payrollItem },
        update: { $set: { xeroAccountCode } },
        upsert: true
      }
    }));

    debug('Bulk write operations:', operations);
    await XeroAccountMapping.bulkWrite(operations);

    debug('Mappings saved successfully');
    res.json({ message: 'Mappings saved successfully' });
  } catch (error) {
    debug('Error in POST /xero/mappings:', error);
    console.error('Error saving account mappings:', error);
    res.status(500).json({ 
      error: 'Failed to save account mappings',
      details: error.message
    });
  }
});

// Export router and utility functions
module.exports = { router, getXeroClient };
