const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const Company = require("../models/Company");
const Employee = require("../models/Employee");
const PayRun = require("../models/payRun");
const PayrollPeriod = require("../models/PayrollPeriod");
const Payslip = require("../models/Payslip");
const Payroll = require("../models/Payroll");
const moment = require("moment");

// Pay Run Routes
router.get('/clients/:companyCode/pay-run/new', ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;
    
    // Find company and verify access
    const company = await Company.findOne({ companyCode });
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    // Verify user has access to this company
    if (company._id.toString() !== req.user.currentCompany.toString()) {
      req.flash("error", "Access denied");
      return res.redirect("/companies/select");
    }

    // Get pending payroll periods that are ready for pay run
    const pendingPayrollPeriods = await PayrollPeriod.find({
      company: company._id,
      isFinalized: true,  // Only get periods that are finalized
      payRun: { $exists: false }  // Not already in a pay run
    }).populate('employee').lean();

    console.log("Found pending payroll periods:", pendingPayrollPeriods.length);

    // Get all employees for these periods
    const employees = await Employee.find({
      company: company._id,
      $or: [
        { lastDayOfService: { $exists: false } },
        { lastDayOfService: null },
        { lastDayOfService: { $gt: new Date() } }
      ]
    }).select('firstName lastName employeeId globalEmployeeId companyEmployeeNumber').lean();

    // Get payroll records for all periods
    const payrollRecords = await Payroll.find({
      company: company._id,
      month: { 
        $in: pendingPayrollPeriods.map(p => p.endDate)
      }
    }).lean();

    // Create a map for quick payroll lookup
    const payrollMap = payrollRecords.reduce((map, payroll) => {
      const key = `${payroll.employee}_${moment(payroll.month).format('YYYY-MM')}`;
      map[key] = payroll;
      return map;
    }, {});

    // Create payslip data for display
    const formattedPayslips = [];
    
    for (const period of pendingPayrollPeriods) {
      for (const employee of employees) {
        // Look up payroll record
        const payrollKey = `${employee._id}_${moment(period.endDate).format('YYYY-MM')}`;
        const payroll = payrollMap[payrollKey] || {};
        
        // Calculate gross pay components
        const basicSalary = payroll.basicSalary || 0;
        const allowances = (payroll.travelAllowance?.fixedAllowanceAmount || 0) + 
                         (payroll.data?.get('allowances') || 0);
        const overtime = payroll.data?.get('overtime') || 0;
        const commission = payroll.commission || 0;
        
        // Calculate gross pay
        const grossPay = basicSalary + allowances + overtime + commission;

        // Calculate all deductions
        const paye = payroll.data?.get('paye') || 0;
        const uif = payroll.data?.get('uif') || 0;
        const medicalAid = (payroll.medicalAid?.employeeContribution || 0);
        const retirementFund = (payroll.retirementFund?.employeeContribution || 0);
        const otherDeductions = (payroll.data?.get('otherDeductions') || 0) + 
                              (payroll.loanRepayments?.monthlyRepayment || 0);

        // Sum up all deductions
        const totalDeductions = paye + uif + medicalAid + retirementFund + otherDeductions;

        // Calculate net pay
        const netPay = grossPay - totalDeductions;

        formattedPayslips.push({
          _id: `${period._id}_${employee._id}`, // Temporary ID for display
          employee: {
            firstName: employee.firstName,
            lastName: employee.lastName,
            employeeId: employee.companyEmployeeNumber
          },
          period: moment(period.startDate).format('MMMM YYYY'),
          startDate: moment(period.startDate).format('DD MMM YYYY'),
          endDate: moment(period.endDate).format('DD MMM YYYY'),
          frequency: period.frequency || 'monthly',
          grossPay: grossPay.toFixed(2),
          netPay: netPay.toFixed(2),
          isFinalized: true
        });
      }
    }

    console.log("Created display payslips:", formattedPayslips.length);
    if (formattedPayslips.length > 0) {
      console.log("Sample display payslip:", formattedPayslips[0]);
    }

    // Get unique pay periods for the filter
    const payPeriods = [...new Set(formattedPayslips.map(p => p.period))];
    
    res.render('payroll/pay-run/new', {
      title: 'Create New Pay Run',
      payslips: formattedPayslips,
      payPeriods,
      user: req.user,
      company,
      moment
    });
  } catch (error) {
    console.error("Error loading pay run creation page:", error);
    req.flash('error', 'Failed to load pay run creation page');
    res.redirect(`/clients/${req.params.companyCode}/dashboard`);
  }
});

router.post('/clients/:companyCode/pay-run/create', ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode } = req.params;
    const { 
      periodString, 
      monthYear, 
      taxPeriodString, 
      payRunType = 'regular', 
      description,
      payslipIds 
    } = req.body;

    // Find company and verify access
    const company = await Company.findOne({ companyCode });
    if (!company || company._id.toString() !== req.user.currentCompany.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Validate pay run type
    const validPayRunTypes = ['regular', 'bonus', 'commission', 'adjustment', 'termination', 'leave', 'supplementary', 'correction', 'additional'];
    if (!validPayRunTypes.includes(payRunType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid pay run type'
      });
    }

    // Find existing pay runs for this period and type
    const existingPayRuns = await PayRun.find({
      company: req.user.currentCompany,
      monthYear: monthYear,
      payRunType: payRunType
    }).sort({ sequence: -1 });

    // Calculate next sequence number
    const nextSequence = existingPayRuns.length > 0 ? existingPayRuns[0].sequence + 1 : 1;

    // Array to store results for each period
    const results = [];
    
    // Group payslips by period
    const payslipsByPeriod = {};
    for (const payslipId of payslipIds) {
      const [periodId, employeeId] = payslipId.split('_');
      if (!payslipsByPeriod[periodId]) {
        payslipsByPeriod[periodId] = [];
      }
      payslipsByPeriod[periodId].push(employeeId);
    }

    // Process each period
    for (const [periodId, employeeIds] of Object.entries(payslipsByPeriod)) {
      // Get period details
      const period = await PayrollPeriod.findById(periodId);
      if (!period) {
        continue;
      }

      // Create payslips
      const payslips = await Promise.all(
        employeeIds.map(async (employeeId) => ({
          company: req.user.currentCompany,
          employee: employeeId,
          payrollPeriod: periodId,
          startDate: period.startDate,
          endDate: period.endDate,
          grossPay: period.basicSalary || 0,
          netPay: period.basicSalary || 0,
          status: 'draft',
          paymentMethod: 'EFT',
          createdBy: req.user._id
        }))
      );

      const createdPayslips = await Payslip.create(payslips);

      // Create pay run
      const payRun = await PayRun.create({
        company: req.user.currentCompany,
        period: periodString,
        monthYear: monthYear,
        payRunType: payRunType,
        description: description,
        sequence: nextSequence,
        frequency: period.frequency,
        startDate: period.startDate,
        endDate: period.endDate,
        paymentDate: period.endDate,
        taxPeriod: taxPeriodString,
        status: 'draft',
        payrollPeriods: [period._id],
        payslips: createdPayslips.map(p => p._id),
        totalAmount: createdPayslips.reduce((sum, p) => sum + (p.grossPay || 0), 0),
        createdBy: req.user._id
      });

      // Update payslips with pay run reference
      await Payslip.updateMany(
        { _id: { $in: createdPayslips.map(p => p._id) } },
        { $set: { payRun: payRun._id } }
      );

      results.push({
        periodId: period._id,
        payRunId: payRun._id,
        employeeCount: createdPayslips.length,
        sequence: nextSequence,
        payRunType: payRunType
      });
    }

    res.json({
      success: true,
      message: results.length > 1 ? 'Pay runs created successfully' : 'Pay run created successfully',
      results
    });
  } catch (error) {
    console.error("Error creating pay run:", error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create pay run'
    });
  }
});

router.get('/clients/:companyCode/pay-run/:id/review', ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode, id } = req.params;

    // Verify company and access
    const company = await Company.findOne({ companyCode });
    if (!company || company._id.toString() !== req.user.currentCompany.toString()) {
      req.flash('error', 'Access denied');
      return res.redirect('/companies/select');
    }

    const payRun = await PayRun.findOne({
      _id: id,
      company: req.user.currentCompany
    }).populate({
      path: 'payslips',
      populate: {
        path: 'employee',
        select: 'firstName lastName employeeId'
      }
    });

    if (!payRun) {
      req.flash('error', 'Pay run not found');
      return res.redirect(`/clients/${companyCode}/dashboard`);
    }

    // Calculate totals
    const totalGrossPay = payRun.payslips.reduce((sum, p) => sum + p.grossPay, 0);
    const totalDeductions = payRun.payslips.reduce((sum, p) => sum + (p.totalDeductions || 0), 0);
    const totalNetPay = payRun.payslips.reduce((sum, p) => sum + p.netPay, 0);

    res.render('payroll/pay-run/review', {
      title: 'Review Pay Run',
      payRun,
      totalGrossPay,
      totalDeductions,
      totalNetPay,
      user: req.user,
      company
    });
  } catch (error) {
    console.error("Error loading pay run review:", error);
    req.flash('error', 'Failed to load pay run review');
    res.redirect(`/clients/${req.params.companyCode}/dashboard`);
  }
});

router.post('/clients/:companyCode/pay-run/:id/finalize', ensureAuthenticated, async (req, res) => {
  try {
    const { companyCode, id } = req.params;

    // Verify company and access
    const company = await Company.findOne({ companyCode });
    if (!company || company._id.toString() !== req.user.currentCompany.toString()) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    const payRun = await PayRun.findOne({
      _id: id,
      company: req.user.currentCompany,
      status: 'created'
    });

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found or already finalized"
      });
    }

    payRun.status = 'finalized';
    payRun.finalizedBy = req.user._id;
    payRun.finalizedAt = new Date();
    await payRun.save();

    // Update all associated payslips
    await Payslip.updateMany(
      { payRun: payRun._id },
      { $set: { status: 'paid' } }
    );

    res.json({
      success: true,
      message: "Pay run finalized successfully"
    });
  } catch (error) {
    console.error("Error finalizing pay run:", error);
    res.status(500).json({
      success: false,
      message: "Failed to finalize pay run",
      error: error.message
    });
  }
});

module.exports = router;
