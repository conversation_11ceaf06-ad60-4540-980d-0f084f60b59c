const express = require("express");
const router = express.Router();
const Notification = require("../models/notification"); // You'll need to create this model
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const User = require("../models/user");
const Company = require("../models/Company"); // Add this line

const API_KEY = "your-secret-api-key";

// Get all notifications
router.get("/", async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate("currentCompany");
    const company = user.currentCompany;

    // Fetch unread notifications
    const unreadNotifications = await Notification.find({
      user: req.user._id,
      read: false,
    }).sort({ createdAt: -1 });

    // Fetch read notifications
    const readNotifications = await Notification.find({
      user: req.user._id,
      read: true,
    })
      .sort({ createdAt: -1 })
      .limit(50);

    res.render("notifications", {
      user: req.user,
      company: company,
      unreadNotifications: unreadNotifications,
      readNotifications: readNotifications,
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Mark a notification as read
router.post("/:id/mark-as-read", ensureAuthenticated, async (req, res) => {
  try {
    const notificationId = req.params.id;
    await Notification.findByIdAndUpdate(notificationId, { read: true });
    res.status(200).send("Notification marked as read");
  } catch (error) {
    console.error("Error marking notification as read:", error);
    res.status(500).send("Internal Server Error");
  }
});

// API endpoint to fetch notifications
router.get("/api/notifications", async (req, res) => {
  const apiKey = req.headers["x-api-key"];
  if (apiKey !== process.env.BLOG_APP_API_KEY) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  try {
    const userId = req.user._id;
    const unreadNotifications = await Notification.find({
      userId,
      read: false,
    }).sort({ date: -1 });
    res.json(unreadNotifications);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// API endpoint to fetch unread notification count
router.get("/api/unread-count", async (req, res) => {
  try {
    const userId = req.user._id;
    const count = await Notification.countDocuments({ userId, read: false });
    res.json({ count });
  } catch (error) {
    console.error("Error fetching unread notification count:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// API endpoint to receive notifications from blog application
router.post("/api/notifications", async (req, res) => {
  const apiKey = req.headers["x-api-key"];
  if (apiKey !== process.env.BLOG_APP_API_KEY) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  try {
    const { title, content, type, blogPostId } = req.body;
    console.log("Received notification data:", {
      title,
      content,
      type,
      blogPostId,
    });

    const users = await User.find();
    console.log("Found users:", users.length);

    for (const user of users) {
      const notification = new Notification({
        userId: user._id,
        title,
        content,
        type,
        blogPost: {
          id: blogPostId,
          title: title.replace("New Blog Post: ", ""),
        },
      });
      await notification.save();
      console.log("Created notification for user:", user._id);
    }

    res.status(200).json({ message: "Notifications created successfully" });
  } catch (error) {
    console.error("Error creating notifications:", error);
    res
      .status(500)
      .json({ error: "Internal Server Error", details: error.message });
  }
});

// API endpoint to update notifications when a blog post is edited
router.post("/api/update", async (req, res) => {
  try {
    const { blogPostId, title, content, type } = req.body;

    // Update all notifications related to this blog post
    await Notification.updateMany(
      { "blogPost.id": blogPostId },
      {
        $set: {
          title,
          content,
          type,
        },
      }
    );

    res.status(200).json({ message: "Notifications updated successfully" });
  } catch (error) {
    console.error("Error updating notifications:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// API endpoint to delete notifications when a blog post is deleted
router.post("/api/delete", async (req, res) => {
  try {
    const { blogPostId, type } = req.body;

    if (type === "blog_delete") {
      // Delete all notifications related to this blog post
      const result = await Notification.deleteMany({
        "blogPost.id": blogPostId,
      });
      console.log(
        `Deleted ${result.deletedCount} notifications for blog post ${blogPostId}`
      );
    }

    res.status(200).json({ message: "Notifications deleted successfully" });
  } catch (error) {
    console.error("Error deleting notifications:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Get a single notification
router.get("/:id", async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);
    if (!notification) {
      return res.status(404).json({ message: "Notification not found" });
    }
    res.json(notification);
  } catch (error) {
    console.error("Error fetching notification:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
});

module.exports = router;
