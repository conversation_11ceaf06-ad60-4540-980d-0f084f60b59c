const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const Employee = require("../models/Employee");
const moment = require("moment-timezone");

// Render the main form
router.get("/", ensureAuthenticated, (req, res) => {
  res.render("miniEmployeeForm", { layout: "layouts/main" });
});

// // Handle form submission
// router.post("/submit", ensureAuthenticated, async (req, res) => {
//   try {
//     const { firstName, lastName, dob, doa } = req.body;

//     console.log("Received data:", { firstName, lastName, dob, doa });

//     const employeeData = {
//       firstName,
//       lastName,
//       dob: moment
//         .tz(dob, "YYYY-MM-DD", "Africa/Johannesburg")
//         .format("YYYY-MM-DD"),
//       doa: moment
//         .tz(doa, "YYYY-MM-DD", "Africa/Johannesburg")
//         .format("YYYY-MM-DD"),
//     };

//     console.log("Processed data:", employeeData);

//     const newEmployee = new Employee(employeeData);
//     await newEmployee.save();

//     console.log("Saved employee:", newEmployee);

//     res.json({
//       success: true,
//       message: "Employee added successfully",
//       employee: newEmployee,
//     });
//   } catch (error) {
//     console.error("Error adding employee:", error);
//     res
//       .status(500)
//       .json({ error: "An error occurred while adding the employee" });
//   }
// });

module.exports = router;
