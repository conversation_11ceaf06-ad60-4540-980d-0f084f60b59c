const express = require('express');
const router = express.Router();
const billingPreference = require('../models/billingPreference');
const Invoice = require('../models/invoice');
const Employee = require('../models/Employee');
const User = require('../models/user');
const { isAuthenticated } = require('../middleware/auth');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit-table');
const BillingStatementService = require('../services/billingStatementService');
const moment = require('moment');
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });
const crypto = require('crypto');
const paystack = require('../config/paystackConfig');
const Company = require('../models/Company');

// Debug middleware
router.use((req, res, next) => {
  console.log('\n=== Billing Route Debug ===');
  console.log('Path:', req.path);
  console.log('Method:', req.method);
  console.log('User:', req.user?._id);
  console.log('Company:', req.user?.currentCompany?._id);
  console.log('CSRF Token Present:', !!req.csrfToken());
  next();
});

// Helper function to calculate billing details
function calculateBillingDetails(activeEmployeesCount) {
  let monthlyRate = 0;
  let totalMonthlyPrice = 0;
  let averageRate = 0;
  
  if (activeEmployeesCount > 0) {
    monthlyRate = 25; // Base rate for first employee
    totalMonthlyPrice = monthlyRate;
    
    if (activeEmployeesCount > 1) {
      const additionalEmployees = activeEmployeesCount - 1;
      
      if (activeEmployeesCount <= 10) {
        totalMonthlyPrice += additionalEmployees * 7.50;
      } else if (activeEmployeesCount <= 50) {
        totalMonthlyPrice += 9 * 7.50; // First 9 additional employees at R7.50
        totalMonthlyPrice += (additionalEmployees - 9) * 5.50;
      } else if (activeEmployeesCount <= 100) {
        totalMonthlyPrice += 9 * 7.50;  // First 9 at R7.50
        totalMonthlyPrice += 40 * 5.50;  // Next 40 at R5.50
        totalMonthlyPrice += (additionalEmployees - 49) * 4.75;
      } else {
        totalMonthlyPrice += 9 * 7.50;   // First 9 at R7.50
        totalMonthlyPrice += 40 * 5.50;   // Next 40 at R5.50
        totalMonthlyPrice += 50 * 4.75;   // Next 50 at R4.75
        totalMonthlyPrice += (additionalEmployees - 99) * 3.75; // Rest at R3.75
      }
    }
    
    averageRate = totalMonthlyPrice / activeEmployeesCount;
  }

  return {
    monthlyRate,
    totalMonthlyPrice,
    averageRate,
    getPricingTierText() {
      if (activeEmployeesCount <= 0) return 'No active employees';
      if (activeEmployeesCount <= 1) return 'Base Rate: R25.00 per employee';
      if (activeEmployeesCount <= 10) return '2-10 employees: R25.00 base + R7.50 per additional employee';
      if (activeEmployeesCount <= 50) return '11-50 employees: R25.00 base + R5.50 per additional employee';
      if (activeEmployeesCount <= 100) return '51-100 employees: R25.00 base + R4.75 per additional employee';
      return '100+ employees: R25.00 base + R3.75 per additional employee';
    }
  };
}

// Get billing preferences page
router.get('/preferences', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Check if user has a current company
    if (!req.user?.currentCompany?._id) {
      req.flash('error', 'Please select a company first');
      return res.redirect('/companies/select');
    }

    console.log('Fetching billing preferences for company:', req.user.currentCompany._id);
    
    // Get or create billing preferences
    let preferences = await billingPreference.findOne({ 
      company: req.user.currentCompany._id 
    });

    // Create default preferences if none exist
    if (!preferences) {
      preferences = new billingPreference({
        company: req.user.currentCompany._id,
        paymentMethod: 'eft',
        statementDelivery: 'email',
        notificationEmail: req.user.email,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      try {
        await preferences.save();
        console.log('Created default billing preferences');
      } catch (saveError) {
        console.error('Error saving default preferences:', saveError);
        req.flash('error', 'Failed to create billing preferences');
        return res.redirect('/companies/select');
      }
    }
    
    // Get recent invoices
    const invoices = await Invoice.find({ 
      company: req.user.currentCompany._id 
    })
    .sort({ date: -1 })
    .limit(10);

    // Get active employees count
    const activeEmployees = await Employee.find({
      company: req.user.currentCompany._id,
      status: 'Active'
    });

    console.log('Active employees count:', activeEmployees.length);

    // Calculate trial period based on user creation date
    const user = await User.findById(req.user._id)
      .select('createdAt')
      .populate('currentCompany');  // Populate company details
      
    const trialDuration = 30; // 30 days trial
    
    let trialDaysLeft = 0;
    let isInTrial = false;
    let trialEndDate = new Date();

    if (user && user.createdAt) {
        // Ensure we have a valid date
        const userCreatedAt = new Date(user.createdAt);
        if (!isNaN(userCreatedAt.getTime())) {  // Check if it's a valid date
            trialEndDate = new Date(userCreatedAt.getTime());
            trialEndDate.setDate(trialEndDate.getDate() + trialDuration);
            
            const now = new Date();
            isInTrial = now <= trialEndDate;
            if (isInTrial) {
                const timeDiff = trialEndDate.getTime() - now.getTime();
                trialDaysLeft = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
            }
        } else {
            console.error('Invalid user creation date:', user.createdAt);
            isInTrial = false;
            trialDaysLeft = 0;
        }
    } else {
        console.error('User or user creation date not found');
        isInTrial = false;
        trialDaysLeft = 0;
    }

    // Create date range for statement period
    const startDate = new Date();
    startDate.setDate(1); // First day of current month
    const endDate = new Date(); // Current date

    // Calculate billing details
    const billing = calculateBillingDetails(activeEmployees.length);

    // Check if Paystack is configured
    const isPaystackConfigured = paystack.isConfigured ? paystack.isConfigured() : false;
    console.log('Is Paystack properly configured:', isPaystackConfigured);

    // Prepare the public key - only expose if configured
    const paystackPublicKey = isPaystackConfigured ? process.env.PAYSTACK_PUBLIC_KEY : '';

    const templateData = {
      user: req.user,
      company: req.user.currentCompany,  // Add company data for navigation
      preferences: preferences || {},
      invoices,
      activeEmployees: activeEmployees.length,
      averageRate: billing.averageRate.toFixed(2),
      totalMonthlyPrice: billing.totalMonthlyPrice.toFixed(2),
      isInTrial,
      trialDaysLeft: Number(trialDaysLeft),
      billingStartDate: trialEndDate,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      pricingTierText: billing.getPricingTierText(),
      csrfToken: req.csrfToken(),
      paystackPublicKey,
      isPaystackConfigured,
      req: req
    };

    console.log('Debug - Paystack public key for template:', paystackPublicKey ? 'Available (not shown for security)' : 'Not available');

    res.render('billing/preferences', templateData);

  } catch (error) {
    console.error('Error in billing preferences route:', error);
    req.flash('error', 'Failed to load billing preferences');
    res.redirect('/companies/select');
  }
});

// Download Excel statement
router.get('/statement/download/excel', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const activeEmployees = await Employee.find({
      company: req.user.currentCompany._id,
      status: 'Active'
    });

    const billing = calculateBillingDetails(activeEmployees.length);
    
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Billing Statement');

    // Add company header
    worksheet.mergeCells('A1:D1');
    worksheet.getCell('A1').value = 'Billing Statement';
    worksheet.getCell('A1').font = { size: 16, bold: true };
    worksheet.getCell('A1').alignment = { horizontal: 'center' };

    // Add statement details
    worksheet.addRow(['Date:', new Date().toLocaleDateString('en-ZA')]);
    worksheet.addRow(['Company:', req.user.currentCompany.name]);
    worksheet.addRow([]);

    // Add billing summary
    worksheet.addRow(['Billing Summary']);
    worksheet.addRow(['Active Employees:', activeEmployees.length]);
    worksheet.addRow(['Average Rate per Employee:', `R${billing.averageRate.toFixed(2)}`]);
    worksheet.addRow(['Total Monthly Price:', `R${billing.totalMonthlyPrice.toFixed(2)}`]);
    worksheet.addRow(['Current Pricing Tier:', billing.getPricingTierText()]);

    // Set column widths
    worksheet.columns = [
      { width: 25 },
      { width: 40 },
      { width: 15 },
      { width: 15 }
    ];

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=billing-statement.xlsx');

    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Error generating Excel:', error);
    res.status(500).send('Error generating Excel file');
  }
});

// Download PDF statement
router.get('/statement/download/pdf', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const activeEmployees = await Employee.find({
      company: req.user.currentCompany._id,
      status: 'Active'
    });

    const billing = calculateBillingDetails(activeEmployees.length);
    
    const pdfBuffer = await BillingStatementService.generatePDF(req.user.currentCompany, activeEmployees.length, billing);
  
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=billing-statement-${moment().format('YYYY-MM-DD')}.pdf`);
    res.send(pdfBuffer);

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).send('Error generating PDF file');
  }
});

// Download Excel statement
router.get('/statement/download/excel-new', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Billing Statement');
    
    const billingDetails = await getBillingDetails(req.user._id);
    
    // Add headers
    worksheet.columns = [
      { header: 'Metric', key: 'metric', width: 30 },
      { header: 'Value', key: 'value', width: 20 }
    ];

    // Add rows
    worksheet.addRow({ metric: 'Active Employees', value: billingDetails.activeEmployees });
    worksheet.addRow({ metric: 'Average Rate per Employee', value: `$${billingDetails.averageRate.toFixed(2)}` });
    worksheet.addRow({ metric: 'Total Monthly Price', value: `$${billingDetails.totalPrice.toFixed(2)}` });
    worksheet.addRow({ metric: 'Current Pricing Tier', value: billingDetails.pricingTier });

    // Style the headers
    worksheet.getRow(1).font = { bold: true };
    
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=billing-statement.xlsx');
    
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('Error generating Excel:', error);
    res.status(500).send('Error generating Excel file');
  }
});

// Download PDF statement
router.get('/statement/download/pdf-new', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const doc = new PDFDocument();
    const billingDetails = await getBillingDetails(req.user._id);
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=billing-statement.pdf');
    
    doc.pipe(res);
    
    // Add title
    doc.fontSize(20).text('Billing Statement', { align: 'center' });
    doc.moveDown();
    
    // Add billing details
    doc.fontSize(12);
    doc.text(`Active Employees: ${billingDetails.activeEmployees}`);
    doc.text(`Average Rate per Employee: $${billingDetails.averageRate.toFixed(2)}`);
    doc.text(`Total Monthly Price: $${billingDetails.totalPrice.toFixed(2)}`);
    doc.text(`Current Pricing Tier: ${billingDetails.pricingTier}`);
    
    doc.end();
  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).send('Error generating PDF file');
  }
});

// Initialize payment
router.post('/payment/initialize', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { invoiceId } = req.body;
    
    if (!invoiceId) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }
    
    // Find the invoice
    const invoice = await Invoice.findById(invoiceId).populate({
      path: 'company',
      populate: {
        path: 'owner',
        select: 'email'
      }
    });
    
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }
    
    // Create a unique reference
    const reference = `INV-${invoice.number}-${Date.now()}`;
    
    // Calculate callback URL
    const callbackUrl = `${req.protocol}://${req.get('host')}/billing/payment/callback`;
    
    // Check if user has saved card details
    const preferences = await billingPreference.findOne({ company: req.user.currentCompany._id });
    const hasSavedCard = preferences && preferences.paystackAuthorizationCode;
    
    // Create metadata as a string (Paystack expects a string, not an object)
    const metadata = JSON.stringify({
      invoice_id: invoice._id.toString(),
      company_id: invoice.company._id.toString(),
      custom_fields: [
        {
          display_name: "Invoice Number",
          variable_name: "invoice_number",
          value: invoice.number
        },
        {
          display_name: "Company Name",
          variable_name: "company_name",
          value: req.user.currentCompany.name
        }
      ]
    });
    
    let paystackParams = {
      amount: invoice.amount * 100, // Convert to kobo/cents (Paystack uses the smallest currency unit)
      email: req.user.email,
      reference,
      callback_url: callbackUrl,
      metadata // Already stringified as required by Paystack
    };
    
    // Add authorization code if user has saved card
    if (hasSavedCard) {
      paystackParams.authorization_code = preferences.paystackAuthorizationCode;
    }
    
    try {
      console.log('Initializing Paystack transaction with params:', {
        ...paystackParams,
        email: `${paystackParams.email.substring(0, 3)}...` // Hide full email in logs
      });
      
      // Initialize transaction with Paystack
      const response = await paystack.initializeTransaction(paystackParams);
      
      // Log the response structure to help with debugging
      console.log('Paystack response structure:', {
        hasData: !!response?.data,
        status: response?.data?.status,
        hasAuthUrl: !!response?.data?.authorization_url,
        responseKeys: response ? Object.keys(response) : [],
        dataKeys: response?.data ? Object.keys(response.data) : []
      });
      
      // Check if we have a valid response
      if (!response || !response.data) {
        console.error('Invalid response from Paystack:', response);
        return res.status(500).json({ error: 'Invalid response from payment gateway' });
      }
      
      // Update invoice with payment reference
      invoice.paymentReference = reference;
      await invoice.save();
      
      // If authorization code was used, payment is likely already completed
      if (hasSavedCard && response.data.status === 'success') {
        // Update invoice status
        invoice.status = 'paid';
        invoice.paymentDate = new Date();
        invoice.paymentMethod = 'card';
        invoice.paymentDetails = {
          gateway: 'paystack',
          reference,
          amount: invoice.amount,
          currency: 'ZAR',
          transactionDate: new Date(),
          cardType: preferences.cardDetails?.cardType || 'Card',
          last4: preferences.cardDetails?.last4 || '****'
        };
        await invoice.save();
        
        return res.json({ 
          success: true, 
          status: 'paid',
          message: 'Payment processed successfully!'
        });
      }
      
      // Check if we have an authorization URL to redirect to
      if (!response.data.authorization_url) {
        console.error('Missing authorization_url in Paystack response:', response.data);
        return res.status(500).json({ error: 'Invalid payment gateway response. Missing redirect URL.' });
      }
      
      // Otherwise, return the URL for redirect
      return res.json({ url: response.data.authorization_url });
    } catch (paystackError) {
      console.error('Paystack API error:', paystackError);
      return res.status(500).json({ error: 'Payment gateway error. Please try again.' });
    }
  } catch (error) {
    console.error('Error initializing payment:', error);
    return res.status(500).json({ error: 'Payment initialization failed' });
  }
});

// Handle payment callback
router.get('/payment/callback', isAuthenticated, async (req, res) => {
  try {
    const { reference } = req.query;
    
    if (!reference) {
      console.warn('Payment callback received without reference');
      req.flash('error', 'Payment reference not found');
      return res.redirect('/billing/preferences');
    }
    
    try {
      console.log('Verifying Paystack transaction with reference:', reference);
      
      // Verify the transaction
      const response = await paystack.verifyTransaction({ reference });
      console.log('Paystack verification response:', {
        status: response.data?.status,
        amount: response.data?.amount,
        currency: response.data?.currency
      });
      
      if (response.data.status === 'success') {
        // Find the invoice using reference
        const invoice = await Invoice.findOne({ paymentReference: reference });
        
        if (invoice) {
          console.log(`Processing successful payment for invoice ${invoice.number}`);
          invoice.status = 'paid';
          invoice.paymentDate = new Date();
          invoice.paymentMethod = 'card';
          invoice.paymentDetails = {
            gateway: 'paystack',
            reference,
            amount: response.data.amount / 100, // Convert from kobo/cents
            currency: response.data.currency,
            transactionDate: new Date(),
            cardType: response.data.authorization?.card_type || 'Unknown',
            last4: response.data.authorization?.last4 || 'Unknown'
          };
          
          await invoice.save();
          req.flash('success', 'Payment processed successfully! Your invoice has been marked as paid.');
        } else {
          console.warn('Payment successful but invoice not found. Reference:', reference);
          req.flash('warning', 'Payment was successful but invoice could not be found. Please contact support.');
        }
      } else {
        console.error('Payment verification failed. Status:', response.data.status);
        req.flash('error', 'Payment was not successful. Please try again or contact support.');
      }
    } catch (verificationError) {
      console.error('Paystack verification error:', verificationError);
      req.flash('error', 'Could not verify payment. Please contact support.');
    }
    
    // Redirect to billing preferences page in all cases
    return res.redirect('/billing/preferences');
  } catch (error) {
    console.error('Error processing payment callback:', error);
    req.flash('error', 'Error processing payment. Please contact support.');
    return res.redirect('/billing/preferences');
  }
});

// Restricted account page (trial restriction)
router.get('/restricted', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Check if user has a trial restriction
    if (!req.trialRestriction || !req.trialRestriction.isRestricted) {
      return res.redirect('/billing/preferences');
    }

    // Render the restricted page
    res.render('billing/restricted', {
      user: req.user,
      company: req.user.currentCompany,
      trialRestriction: req.trialRestriction,
      csrfToken: req.csrfToken()
    });
  } catch (error) {
    console.error('Error in restricted account page:', error);
    req.flash('error', 'An error occurred while loading the restricted page');
    res.redirect('/dashboard');
  }
});

// Payment restricted account page (overdue payment restriction)
router.get('/payment-restricted', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    // Check if user has a payment restriction
    if (!req.session.paymentRestriction || !req.session.paymentRestriction.isRestricted) {
      return res.redirect('/billing/preferences');
    }

    // Render the payment restricted page
    res.render('billing/payment-restricted', {
      user: req.user,
      company: req.user.currentCompany,
      paymentRestriction: req.session.paymentRestriction,
      csrfToken: req.csrfToken()
    });
  } catch (error) {
    console.error('Error in payment restricted account page:', error);
    req.flash('error', 'An error occurred while loading the restricted page');
    res.redirect('/dashboard');
  }
});

// Handle Paystack webhook
router.post('/payment/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    // Verify that the request is from Paystack
    const signature = req.headers['x-paystack-signature'];
    if (!signature) {
      console.warn('Webhook received without signature header');
      return res.status(400).send('No signature header');
    }
    
    // Create a hash with the secret key and the request body
    const hash = crypto.createHmac('sha512', process.env.PAYSTACK_SECRET_KEY)
      .update(JSON.stringify(req.body))
      .digest('hex');
    
    // Compare the hash with the signature
    if (hash !== signature) {
      console.warn('Webhook received with invalid signature');
      return res.status(400).send('Invalid signature');
    }
    
    // Process the webhook event
    const event = req.body;
    const { event: eventType, data } = event;
    
    console.log(`Processing Paystack webhook event: ${eventType}`);
    
    // Parse metadata if it's a string
    let metadata = {};
    if (data && data.metadata) {
      try {
        metadata = typeof data.metadata === 'string' 
          ? JSON.parse(data.metadata) 
          : data.metadata;
      } catch (parseError) {
        console.error('Error parsing metadata:', parseError);
        metadata = {};
      }
    }
    
    switch (eventType) {
      case 'charge.success':
        // Handle successful payment
        if (metadata && metadata.invoice_id) {
          console.log(`Processing webhook payment for invoice ID: ${metadata.invoice_id}`);
          const invoice = await Invoice.findById(metadata.invoice_id);
          
          if (invoice) {
            invoice.status = 'paid';
            invoice.paymentDate = new Date();
            invoice.paymentMethod = 'card';
            invoice.paymentDetails = {
              gateway: 'paystack',
              reference: data.reference,
              amount: data.amount / 100, // Convert from kobo/cents
              currency: data.currency,
              transactionDate: new Date(data.paid_at),
              cardType: data.authorization?.card_type || 'Unknown',
              last4: data.authorization?.last4 || 'Unknown'
            };
            
            await invoice.save();
            console.log(`Invoice ${invoice.number} marked as paid via Paystack webhook`);
          } else {
            console.warn(`Webhook referenced invoice ID ${metadata.invoice_id} which was not found`);
          }
        } else {
          console.warn('Webhook charge.success event missing invoice_id in metadata');
        }
        break;
        
      case 'charge.failed':
        // Handle failed payment
        if (metadata && metadata.invoice_id) {
          console.log(`Processing webhook failed payment for invoice ID: ${metadata.invoice_id}`);
          const invoice = await Invoice.findById(metadata.invoice_id);
          
          if (invoice) {
            invoice.paymentAttempts = (invoice.paymentAttempts || 0) + 1;
            invoice.lastPaymentAttempt = {
              date: new Date(),
              status: 'failed',
              gateway: 'paystack',
              reference: data.reference,
              error: data.gateway_response
            };
            
            await invoice.save();
            console.log(`Failed payment attempt for invoice ${invoice.number} via Paystack webhook`);
          } else {
            console.warn(`Webhook referenced invoice ID ${metadata.invoice_id} which was not found`);
          }
        } else {
          console.warn('Webhook charge.failed event missing invoice_id in metadata');
        }
        break;
        
      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }
    
    // Always acknowledge receipt of webhook
    return res.status(200).send('Webhook received');
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).send('Webhook processing failed');
  }
});

// Test route for simulating trial expiration
router.get('/test-trial', isAuthenticated, async (req, res) => {
  try {
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).send('Test routes are only available in development mode');
    }
    
    const { minutes } = req.query;
    const minutesAgo = minutes ? parseInt(minutes) : 0;
    
    // Get the current user
    const user = req.user;
    
    if (!user) {
      return res.status(400).send('User not found');
    }
    
    // Calculate a new createdAt date based on minutes parameter
    const newCreatedAt = new Date();
    newCreatedAt.setMinutes(newCreatedAt.getMinutes() - minutesAgo);
    
    // Update the user's createdAt date for testing
    await User.findByIdAndUpdate(user._id, { createdAt: newCreatedAt });
    
    // Return the updated user info
    const updatedUser = await User.findById(user._id);
    
    return res.json({
      success: true,
      message: `User createdAt date modified for testing. Set to ${minutesAgo} minutes ago.`,
      user: {
        _id: updatedUser._id,
        email: updatedUser.email,
        createdAt: updatedUser.createdAt,
        formattedCreatedAt: new Date(updatedUser.createdAt).toLocaleString()
      }
    });
  } catch (error) {
    console.error('Error in test route:', error);
    return res.status(500).json({ error: 'Test route failed' });
  }
});

// Check trial restriction status
router.get('/check-restriction', isAuthenticated, async (req, res) => {
  try {
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).send('Debug routes are only available in development mode');
    }
    
    const user = await User.findById(req.user._id).select('createdAt');
    
    if (!user) {
      return res.status(400).send('User not found');
    }
    
    // Recalculate the dates for display
    const testMode = true; // Match the value in the middleware
    
    const trialDuration = testMode ? 1 : 30; // Minutes or days
    const gracePeriod = testMode ? 1 : 7; // Minutes or days
    
    const userCreatedAt = new Date(user.createdAt);
    
    // Calculate trial end date
    const trialEndDate = new Date(userCreatedAt);
    if (testMode) {
      trialEndDate.setMinutes(trialEndDate.getMinutes() + trialDuration);
    } else {
      trialEndDate.setDate(trialEndDate.getDate() + trialDuration);
    }
    
    // Calculate grace period end date
    const gracePeriodEndDate = new Date(trialEndDate);
    if (testMode) {
      gracePeriodEndDate.setMinutes(gracePeriodEndDate.getMinutes() + gracePeriod);
    } else {
      gracePeriodEndDate.setDate(gracePeriodEndDate.getDate() + gracePeriod);
    }
    
    // Calculate current status
    const now = new Date();
    const isInTrial = now <= trialEndDate;
    const isInGracePeriod = !isInTrial && now <= gracePeriodEndDate;
    const isRestricted = !isInTrial && !isInGracePeriod;
    
    // Calculate time remaining
    let timeRemainingText = '';
    if (isInTrial) {
      const msRemaining = trialEndDate.getTime() - now.getTime();
      if (testMode) {
        const minutesRemaining = Math.ceil(msRemaining / (1000 * 60));
        timeRemainingText = `${minutesRemaining} minute(s) remaining in trial`;
      } else {
        const daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24));
        timeRemainingText = `${daysRemaining} day(s) remaining in trial`;
      }
    } else if (isInGracePeriod) {
      const msRemaining = gracePeriodEndDate.getTime() - now.getTime();
      if (testMode) {
        const minutesRemaining = Math.ceil(msRemaining / (1000 * 60));
        timeRemainingText = `${minutesRemaining} minute(s) remaining in grace period`;
      } else {
        const daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24));
        timeRemainingText = `${daysRemaining} day(s) remaining in grace period`;
      }
    } else {
      const msOverdue = now.getTime() - gracePeriodEndDate.getTime();
      if (testMode) {
        const minutesOverdue = Math.floor(msOverdue / (1000 * 60));
        timeRemainingText = `${minutesOverdue} minute(s) overdue`;
      } else {
        const daysOverdue = Math.floor(msOverdue / (1000 * 60 * 60 * 24));
        timeRemainingText = `${daysOverdue} day(s) overdue`;
      }
    }
    
    return res.json({
      user: {
        _id: user._id,
        createdAt: user.createdAt,
        formattedCreatedAt: new Date(user.createdAt).toLocaleString()
      },
      trial: {
        testMode,
        trialDuration: testMode ? `${trialDuration} minute(s)` : `${trialDuration} day(s)`,
        gracePeriod: testMode ? `${gracePeriod} minute(s)` : `${gracePeriod} day(s)`,
        trialEndDate,
        formattedTrialEndDate: trialEndDate.toLocaleString(),
        gracePeriodEndDate,
        formattedGracePeriodEndDate: gracePeriodEndDate.toLocaleString(),
        now,
        formattedNow: now.toLocaleString(),
        isInTrial,
        isInGracePeriod,
        isRestricted,
        status: isInTrial ? 'In Trial' : (isInGracePeriod ? 'In Grace Period' : 'Restricted'),
        timeRemainingText
      }
    });
  } catch (error) {
    console.error('Error checking restriction status:', error);
    return res.status(500).json({ error: 'Failed to check restriction status' });
  }
});

// Get latest invoice for current company
router.get('/latest-invoice', isAuthenticated, async (req, res) => {
  try {
    // Find the latest pending invoice for the current company
    const invoice = await Invoice.findOne({
      company: req.user.currentCompany._id,
      status: 'pending'
    }).sort({ date: -1 });
    
    if (!invoice) {
      return res.status(404).json({ message: 'No pending invoice found' });
    }
    
    return res.json({ invoice });
  } catch (error) {
    console.error('Error fetching latest invoice:', error);
    return res.status(500).json({ error: 'Failed to fetch invoice' });
  }
});

// Save card details
router.post('/payment/save-card', isAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { reference } = req.body;
    
    if (!reference) {
      return res.status(400).json({ success: false, message: 'Payment reference is required' });
    }
    
    // Verify the transaction with Paystack
    const verificationResult = await paystack.verifyTransaction({ reference });
    
    if (!verificationResult || !verificationResult.data || verificationResult.data.status !== 'success') {
      return res.status(400).json({ 
        success: false, 
        message: 'Card verification failed' 
      });
    }
    
    // Extract card details from the authorization
    const authorization = verificationResult.data.authorization;
    
    if (!authorization) {
      return res.status(400).json({ 
        success: false, 
        message: 'No card authorization found' 
      });
    }
    
    // Find or create billing preferences
    let preferences = await billingPreference.findOne({ company: req.user.currentCompany._id });
    
    if (!preferences) {
      preferences = new billingPreference({
        company: req.user.currentCompany._id,
        paymentMethod: 'card'
      });
    } else {
      preferences.paymentMethod = 'card';
    }
    
    // Save card details
    preferences.cardDetails = {
      cardType: authorization.card_type,
      last4: authorization.last4,
      expiryMonth: authorization.exp_month,
      expiryYear: authorization.exp_year,
      cardHolderName: req.user.firstName + ' ' + req.user.lastName,
      isDefault: true
    };
    
    // Save authorization codes
    preferences.paystackAuthorizationCode = authorization.authorization_code;
    preferences.paystackCustomerCode = verificationResult.data.customer.customer_code;
    
    await preferences.save();
    
    return res.json({ 
      success: true, 
      message: 'Card details saved successfully' 
    });
  } catch (error) {
    console.error('Error saving card details:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to save card details' 
    });
  }
});

// Promotional code route
router.post('/apply-promo', isAuthenticated, async (req, res) => {
    try {
        const { promoCode } = req.body;
        
        // Validate promo code format
        if (!promoCode || typeof promoCode !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Invalid promotional code format'
            });
        }

        // Get current user and company
        const user = await User.findById(req.user._id);
        const company = await Company.findById(user.currentCompany);

        if (!user || !company) {
            return res.status(404).json({
                success: false,
                message: 'User or company not found'
            });
        }

        // Check if user already has an active promotion
        if (user.promotionalCode) {
            return res.status(400).json({
                success: false,
                message: 'You already have an active promotion'
            });
        }

        // Validate the promotional code (you can modify this based on your needs)
        const validPromoCodes = ['PANDA2024', 'LAUNCH2024']; // Example valid codes
        if (!validPromoCodes.includes(promoCode.toUpperCase())) {
            return res.status(400).json({
                success: false,
                message: 'Invalid promotional code'
            });
        }

        // Calculate promotion end date (1 year from now)
        const promoEndDate = new Date();
        promoEndDate.setFullYear(promoEndDate.getFullYear() + 1);

        // Update user with promotional details
        user.promotionalCode = promoCode;
        user.promotionStartDate = new Date();
        user.promotionEndDate = promoEndDate;
        await user.save();

        // Update company billing status
        company.billingStatus = 'promotional';
        company.promotionEndDate = promoEndDate;
        await company.save();

        // Return success response
        res.json({
            success: true,
            message: 'Promotional code applied successfully',
            promotionEndDate: promoEndDate
        });

    } catch (error) {
        console.error('Error applying promotional code:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to apply promotional code'
        });
    }
});

module.exports = router;
