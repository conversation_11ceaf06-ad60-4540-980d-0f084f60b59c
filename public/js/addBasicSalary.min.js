class e{constructor(){this.form=document.getElementById("basicSalaryForm"),this.hourlyPaidCheckbox=document.getElementById("hourlyPaid"),this.rateLabel=document.getElementById("rateLabel"),this.dontAutoPayDiv=document.getElementById("dontAutoPay"),this.additionalHoursDiv=document.getElementById("additionalHours"),this.overrideRateContainer=document.getElementById("overrideRateContainer"),this.basicSalaryInput=document.getElementById("basicSalary"),this.csrfToken=document.querySelector('input[name="_csrf"]')?.value,this.isSubmitting=!1,this.beforeUnloadHandler=null,this.initializeFormState(),this.initializeEventListeners()}initializeFormState(){var e=this.hourlyPaidCheckbox?.checked||!1;this.updateUIForHourlyPaid(e)}initializeEventListeners(){this.form?.addEventListener("submit",async e=>{e.preventDefault(),await this.handleSubmit(e)}),this.hourlyPaidCheckbox?.addEventListener("change",async e=>{e=e.target.checked;this.updateUIForHourlyPaid(e),await this.updateHourlyPaidStatus(e)})}async updateHourlyPaidStatus(t){try{var e,a=this.getEmployeeId(),i=document.body.getAttribute("data-company-code"),o=await fetch(`/clients/${i}/employee/${a}/hourly-status`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":this.csrfToken},body:JSON.stringify({hourlyPaid:t})}),s=o.headers.get("content-type");if(!s||!s.includes("application/json"))throw new Error(`Server returned ${o.status} `+o.statusText);if(e=await o.json(),!o.ok)throw new Error(e.message||"Failed to update hourly paid status");this.updateUIForHourlyPaid(e.hourlyPaid),this.showToast(e.message,"success"),this.basicSalaryInput&&(this.basicSalaryInput.placeholder=t?"Enter hourly rate":"Enter monthly salary")}catch(e){this.showToast(e.message||"Failed to update hourly paid status","error"),this.hourlyPaidCheckbox.checked=!t,this.updateUIForHourlyPaid(!t)}}async handleSubmit(e){if(e.preventDefault(),!this.isSubmitting){this.isSubmitting=!0,this.showLoadingState();try{let e=new FormData(this.form);e.append("hourlyPaid",this.hourlyPaidCheckbox.checked?"on":"off");var a=Object.fromEntries(e);if(!a.basicSalary&&this.basicSalaryInput){let e=this.basicSalaryInput.value;null!=e&&""!==e&&(a.basicSalary=e)}if(!a.employeeId){let e=this.getEmployeeId();e&&(a.employeeId=e)}var i=await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":this.csrfToken},body:JSON.stringify(a)}),o=await i.json();if(!i.ok||!o.success)throw new Error(o.message||"Failed to save changes");this.showSuccessState(),this.showToast(o.message||"Changes saved successfully","success");let t=o.redirectUrl||this.form.getAttribute("data-redirect-url");t?(this.allowNavigation(),setTimeout(()=>{window.location.href=t},1500)):setTimeout(()=>{this.hideLoadingState()},2e3)}catch(e){this.showToast(e.message||"Failed to save changes","error"),this.allowNavigation(),this.hideLoadingState()}}}getEmployeeId(){var e=window.location.pathname.split("/");return e[e.indexOf("employeeProfile")+1]}updateUIForHourlyPaid(e){var t;if(this.rateLabel&&(this.rateLabel.textContent=e?"Hourly Rate:":"Basic Salary:"),this.dontAutoPayDiv&&(this.dontAutoPayDiv.style.display=e?"block":"none",t=document.getElementById("dontAutoPayPublicHolidays"),!e)&&t&&(t.checked=!1),this.additionalHoursDiv&&(this.additionalHoursDiv.style.display=e?"none":"block",e)){let e=document.getElementById("paidForAdditionalHours");if(e&&(e.checked=!1),this.overrideRateContainer){this.overrideRateContainer.style.display="none";let e=document.getElementById("overrideCalculatedHourlyRate");e&&(e.checked=!1)}}}showLoadingState(){this.createLoadingOverlay();var e=this.form.querySelector('button[type="submit"]');e&&(e.disabled=!0,e.classList.add("loading"),e.dataset.originalContent||(e.dataset.originalContent=e.innerHTML),e.innerHTML='<span class="button-content" style="opacity: 0;"><i class="ph ph-check"></i> Save Changes</span>'),this.form.querySelectorAll("input, select, textarea").forEach(e=>{e.disabled||(e.dataset.wasEnabled="true",e.disabled=!0)}),this.preventNavigation()}showSuccessState(){var t=document.querySelector(".form-loading-overlay");if(t){let e=t.querySelector(".loading-content");e&&(e.innerHTML='\n          <div style="color: #10b981; font-size: 48px; margin-bottom: 1rem;">\n            <i class="ph ph-check-circle"></i>\n          </div>\n          <div class="loading-text" style="color: #10b981;">Success!</div>\n          <div class="loading-subtext">Changes saved successfully</div>\n        ')}t=this.form.querySelector('button[type="submit"]');t&&(t.classList.remove("loading"),t.innerHTML='<i class="ph ph-check"></i> Saved Successfully',t.style.backgroundColor="#10b981")}hideLoadingState(){let e=document.querySelector(".form-loading-overlay");e&&(e.classList.remove("show"),setTimeout(()=>{e.remove()},300));var t=this.form.querySelector('button[type="submit"]');t&&(t.disabled=!1,t.classList.remove("loading"),t.style.backgroundColor="",t.dataset.originalContent)&&(t.innerHTML=t.dataset.originalContent,delete t.dataset.originalContent),this.form.querySelectorAll("input, select, textarea").forEach(e=>{"true"===e.dataset.wasEnabled&&(e.disabled=!1,delete e.dataset.wasEnabled)}),this.allowNavigation(),this.isSubmitting=!1}createLoadingOverlay(){var e=document.querySelector(".form-loading-overlay");e&&e.remove();let t=document.createElement("div");t.className="form-loading-overlay",t.innerHTML='\n      <div class="loading-content">\n        <div class="loading-spinner"></div>\n        <div class="loading-text">Saving Changes...</div>\n        <div class="loading-subtext">Please don\'t refresh or navigate away</div>\n      </div>\n    ',document.body.appendChild(t),setTimeout(()=>{t.classList.add("show")},10)}preventNavigation(){this.beforeUnloadHandler=e=>(e.preventDefault(),e.returnValue="Your changes are being saved. Are you sure you want to leave?"),window.addEventListener("beforeunload",this.beforeUnloadHandler)}allowNavigation(){this.beforeUnloadHandler&&(window.removeEventListener("beforeunload",this.beforeUnloadHandler),this.beforeUnloadHandler=null)}showToast(e,t="info"){let a=document.querySelector(".toast-container"),i=(a||((a=document.createElement("div")).className="toast-container",document.body.appendChild(a)),{success:"ph-check-circle",error:"ph-x-circle",info:"ph-info",warning:"ph-warning"}),o=document.createElement("div");o.className="toast toast-"+t,o.innerHTML=`
      <i class="ph ${i[t]||i.info}"></i>
      <span>${e}</span>
    `,a.appendChild(o),setTimeout(()=>{o.classList.add("show")},10),setTimeout(()=>{o.classList.remove("show"),setTimeout(()=>{o.remove(),0===a.children.length&&a.remove()},300)},4e3)}}document.addEventListener("DOMContentLoaded",()=>{new e}),window.handleHourlyPaidCheckbox=function(){return!1};