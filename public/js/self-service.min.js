document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("statusFilter"),n=document.getElementById("searchInputEmployee"),o=document.querySelector(".modern-table tbody"),t=Array.from(o.getElementsByTagName("tr")),s=10;let i=1;const a=[...t];function c(){const e=t.filter(e=>"none"!==e.style.display),n=e.length,o=(i-1)*s+1,a=Math.min(o+s-1,n);document.querySelector(".pagination-info").textContent=0===n?"No entries to show":`Showing ${o} to ${a} of ${n} entries`;const c=Math.ceil(n/s);document.getElementById("prevButton").disabled=1===i,document.getElementById("nextButton").disabled=i>=c,e.forEach((e,n)=>{e.style.display=n>=(i-1)*s&&n<i*s?"":"none"})}function r(){i=1;const o=n.value.toLowerCase(),t=e.value;a.forEach(e=>{const n=e.querySelector(".employee-name").textContent.toLowerCase(),s=e.cells[0].textContent.toLowerCase(),i=e.cells[2].textContent.toLowerCase(),a=e.querySelector(".status-badge").textContent.trim(),c=n.includes(o)||s.includes(o)||i.includes(o),r="all"===t||"Active"===t&&"Enabled"===a||"Inactive"===t&&"Disabled"===a;e.style.display=c&&r?"":"none"}),c()}if(window.prevPage=function(){i>1&&(i--,c())},window.nextPage=function(){const e=t.filter(e=>"none"!==e.style.display),n=Math.ceil(e.length/s);i<n&&(i++,c())},e&&e.addEventListener("change",r),n){let e;n.addEventListener("input",function(){clearTimeout(e),e=setTimeout(r,300)})}r()});const companyCode=window.location.pathname.split("/")[2];function showLoading(e){e.disabled=!0;const n=e.innerHTML;return e.innerHTML='\n    <div class="button-loader">\n      <i class="ph ph-circle-notch"></i>\n      Processing...\n    </div>\n  ',e.classList.add("loading"),n}function hideLoading(e,n){e.disabled=!1,e.innerHTML=n,e.classList.remove("loading")}async function enableSelfService(e){const n=event.currentTarget,o=showLoading(n);try{const t=document.querySelector('meta[name="csrf-token"]').content,s=`/clients/${companyCode}/employeeManagement/selfService/enable/${e}`,i=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":t}}),a=await i.json();a.success?(showNotification("success","Self-service enabled successfully. Setup email sent."),setTimeout(()=>window.location.reload(),1500)):(hideLoading(n,o),showNotification("error",a.message||"Failed to enable self service"))}catch(e){console.error("Error in enableSelfService:",e),hideLoading(n,o),showNotification("error","An error occurred while enabling self service")}}async function disableSelfService(e){const n=event.currentTarget,o=showLoading(n);if(confirm("Are you sure you want to disable self-service for this employee?"))try{const n=document.querySelector('meta[name="csrf-token"]').content;console.log("CSRF Token found:",!!n);const o=`/clients/${companyCode}/employeeManagement/selfService/disable/${e}`;console.log("Making request to:",o);const t=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":n}});console.log("Response status:",t.status);const s=await t.json();console.log("Response data:",s),s.success?(showNotification("success","Self-service disabled successfully"),console.log("Operation successful, reloading page in 1.5s"),setTimeout(()=>window.location.reload(),1500)):(console.error("Operation failed:",s.message),showNotification("error",s.message||"Failed to disable self service"))}catch(e){console.error("Error in disableSelfService:",e),showNotification("error","An error occurred while disabling self service")}else hideLoading(n,o)}async function resendInvite(e){const n=event.currentTarget,o=showLoading(n);try{const t=document.querySelector('meta[name="csrf-token"]').content;console.log("CSRF Token found:",!!t);const s=`/clients/${companyCode}/employeeManagement/selfService/resend/${e}`;console.log("Making request to:",s);const i=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":t}});console.log("Response status:",i.status);const a=await i.json();console.log("Response data:",a),a.success?(showNotification("success","Setup email resent successfully"),hideLoading(n,o)):(hideLoading(n,o),showNotification("error",a.message||"Failed to resend setup email"))}catch(e){console.error("Error in resendInvite:",e),hideLoading(n,o),showNotification("error","An error occurred while resending the setup email")}}function showNotification(e,n){console.log(`Showing notification - Type: ${e}, Message: ${n}`);const o=document.createElement("div");o.className=`notification ${e}`,o.textContent=n,document.body.appendChild(o),console.log("Notification element created and added to DOM"),setTimeout(()=>{o.remove(),console.log("Notification removed from DOM")},3e3)}console.log("Initialized with companyCode:",companyCode),document.addEventListener("DOMContentLoaded",()=>{console.log("DOM fully loaded"),console.log("Current pathname:",window.location.pathname),console.log("Extracted companyCode:",companyCode);const e=document.querySelector(".modern-table tbody");console.log("Table rows found:",e?.getElementsByTagName("tr").length);const n=document.querySelector('meta[name="csrf-token"]');console.log("CSRF token meta tag present:",!!n)});const style=document.createElement("style");style.textContent="\n    .notification {\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        padding: 12px 24px;\n        border-radius: 8px;\n        color: white;\n        font-weight: 500;\n        z-index: 1000;\n        animation: slideIn 0.3s ease-out;\n    }\n\n    .notification.success {\n        background: var(--success-color);\n    }\n\n    .notification.error {\n        background: var(--danger-color);\n    }\n\n    @keyframes slideIn {\n        from {\n            transform: translateX(100%);\n            opacity: 0;\n        }\n        to {\n            transform: translateX(0);\n            opacity: 1;\n        }\n    }\n",document.head.appendChild(style);