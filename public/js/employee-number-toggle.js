document.addEventListener('DOMContentLoaded', function() {
    const toggleSwitch = document.getElementById('employeeNumberMode');
    const infoMessage = document.getElementById('autoGenerateInfo');
    const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
    const csrfToken = csrfTokenMeta ? csrfTokenMeta.getAttribute('content') : null;

    // Check if required elements exist
    if (!toggleSwitch) {
        console.warn('Employee number toggle switch not found');
        return;
    }

    if (!csrfToken) {
        console.warn('CSRF token not found, employee number toggle may not work properly');
    }

    // Find the container where we'll insert the manual input field
    const toggleContainer = toggleSwitch.closest('.toggle-container');
    const parentSection = toggleContainer ? toggleContainer.parentElement : null;

    if (!parentSection) {
        console.warn('Could not find parent section for employee number toggle');
        return;
    }

    if (toggleSwitch) {
        toggleSwitch.addEventListener('change', async function() {
            const mode = this.checked ? 'automatic' : 'manual';

            try {

                const headers = {
                    'Content-Type': 'application/json'
                };

                // Only add CSRF token if it exists
                if (csrfToken) {
                    headers['CSRF-Token'] = csrfToken;
                }

                const response = await fetch('/api/settings/employee-number-mode', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ mode })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Failed to update employee number mode');
                }



                // Update UI based on mode
                updateUIForMode(mode);

                // Show success message
                showMessage(`Employee number mode updated to ${mode === 'automatic' ? 'automatic generation' : 'manual entry'}`, 'success');

            } catch (error) {
                console.error('Error updating employee number mode:', error);

                // Show error message
                showMessage(error.message || 'Failed to update employee number mode', 'error');

                // Revert toggle state
                this.checked = !this.checked;
            }
        });
    }

    // Function to create/remove manual employee number input field
    function updateUIForMode(mode) {
        // Look for both server-rendered and JavaScript-created fields
        const existingManualInput = document.getElementById('companyEmployeeNumber');
        const existingFormGroup = existingManualInput ? existingManualInput.closest('.form-group') : null;

        // Find all possible employee number form groups (in case there are multiple)
        const allFormGroups = document.querySelectorAll('.form-group');
        let employeeNumberFormGroups = [];

        allFormGroups.forEach(group => {
            const input = group.querySelector('#companyEmployeeNumber, input[name="companyEmployeeNumber"]');
            if (input) {
                employeeNumberFormGroups.push(group);
            }
        });



        if (mode === 'manual') {
            // Create manual input field if it doesn't exist
            if (!existingManualInput && employeeNumberFormGroups.length === 0) {
                const formGroup = document.createElement('div');
                formGroup.className = 'form-group';
                formGroup.innerHTML = `
                    <label for="companyEmployeeNumber">
                        <i class="ph ph-hash"></i>
                        Employee Number *
                    </label>
                    <div class="input-wrapper">
                        <input
                            type="text"
                            id="companyEmployeeNumber"
                            name="companyEmployeeNumber"
                            placeholder="Enter employee number"
                            pattern="[A-Za-z0-9\\-]+"
                            title="Can contain letters, numbers and hyphens"
                            required
                        />
                    </div>
                `;

                // Insert after the toggle container
                toggleContainer.insertAdjacentElement('afterend', formGroup);
            } else {
                // Show ALL existing employee number form groups
                employeeNumberFormGroups.forEach(group => {
                    // Force visibility with important styles
                    group.style.setProperty('display', 'block', 'important');
                    group.style.setProperty('visibility', 'visible', 'important');
                    group.style.removeProperty('opacity');

                    const input = group.querySelector('input[name="companyEmployeeNumber"]');
                    if (input) {
                        input.required = true;
                        input.style.setProperty('display', 'block', 'important');
                    }
                });
            }

            // Hide auto-generate info message
            if (infoMessage) {
                infoMessage.style.display = 'none';
            }
        } else {
            // Hide ALL manual input fields completely
            employeeNumberFormGroups.forEach(group => {
                // Force hide with important styles
                group.style.setProperty('display', 'none', 'important');

                const input = group.querySelector('input[name="companyEmployeeNumber"]');
                if (input) {
                    input.required = false;
                    input.style.setProperty('display', 'none', 'important');
                    // Don't clear the value to preserve user input in case they toggle back
                }
            });

            // Show auto-generate info message
            if (infoMessage) {
                infoMessage.style.display = 'block';
            }
        }
    }

    // Function to show messages
    function showMessage(text, type) {
        const messageClass = type === 'error' ? 'alert alert-danger' : 'alert alert-success';
        const message = document.createElement('div');
        message.className = messageClass;
        message.textContent = text;

        // Insert message at the top of the parent section
        parentSection.insertBefore(message, parentSection.firstChild);

        // Remove message after 3 seconds
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // Initialize UI based on current toggle state
    // Wait for DOM to be fully ready before initializing
    setTimeout(() => {
        const initialMode = toggleSwitch.checked ? 'automatic' : 'manual';
        updateUIForMode(initialMode);
    }, 0);
});
