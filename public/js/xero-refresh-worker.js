// Service Worker for Xero token refresh
const REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes
let refreshTimeout;

// Handle messages from the main thread
self.addEventListener('message', (event) => {
    if (event.data.type === 'START_REFRESH') {
        startRefreshCycle(event.data.companyCode);
    } else if (event.data.type === 'STOP_REFRESH') {
        stopRefreshCycle();
    }
});

// Start the refresh cycle
function startRefreshCycle(companyCode) {
    console.log('[XERO-WORKER] Starting refresh cycle');
    checkToken(companyCode);
    
    // Schedule next check
    refreshTimeout = setInterval(() => {
        checkToken(companyCode);
    }, REFRESH_INTERVAL);
}

// Stop the refresh cycle
function stopRefreshCycle() {
    console.log('[XERO-WORKER] Stopping refresh cycle');
    if (refreshTimeout) {
        clearInterval(refreshTimeout);
    }
}

// Check token status
async function checkToken(companyCode) {
    try {
        console.log('[XERO-WORKER] Checking token status');
        const response = await fetch(`/xero/health`);
        const health = await response.json();
        
        if (health.needsRefresh || (health.timeUntilExpiry && health.timeUntilExpiry < 600)) {
            await refreshToken(companyCode);
        }
    } catch (error) {
        console.error('[XERO-WORKER] Token check error:', error);
        self.postMessage({ type: 'ERROR', error: error.message });
    }
}

// Refresh token
async function refreshToken(companyCode) {
    try {
        console.log('[XERO-WORKER] Refreshing token');
        const response = await fetch(`/xero/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            self.postMessage({ type: 'REFRESH_SUCCESS' });
        } else {
            throw new Error(result.error || 'Token refresh failed');
        }
    } catch (error) {
        console.error('[XERO-WORKER] Token refresh error:', error);
        self.postMessage({ type: 'REFRESH_ERROR', error: error.message });
    }
} 