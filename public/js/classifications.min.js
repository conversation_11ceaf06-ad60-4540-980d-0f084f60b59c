document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("directorCheckbox"),t=document.getElementById("directorTypeGroup"),n=document.getElementById("typeOfDirector"),o=document.getElementById("contractorCheckbox"),l=document.getElementById("uifExemptContainer"),i=document.getElementById("uifExemptCheckbox"),d=document.getElementById("uifExemptReasonGroup"),c=document.getElementById("uifExemptReason"),y=document.getElementById("workingHoursGroup");function s(){o.checked?(l.style.display="none",i.checked=!1,d.style.display="none",c.value="",c.required=!1):l.style.display="block"}function a(){i.checked?(d.style.display="block",setTimeout(()=>{d.style.opacity="1",d.style.maxHeight="500px"},50),c.required=!0):(d.style.opacity="0",d.style.maxHeight="0",setTimeout(()=>{d.style.display="none",c.required=!1,c.value=""},300))}e.checked&&(t.style.display="block",t.style.opacity="1",t.style.maxHeight="500px",n.required=!0,y.style.display="none",y.style.opacity="0",y.style.maxHeight="0"),s(),a(),e.addEventListener("change",function(){e.checked?(y.style.opacity="0",y.style.maxHeight="0",setTimeout(()=>{y.style.display="none",t.style.display="block",t.style.opacity="1",t.style.maxHeight="500px",n.required=!0},300)):(t.style.opacity="0",t.style.maxHeight="0",setTimeout(()=>{t.style.display="none",n.required=!1,n.value="",y.style.display="block",setTimeout(()=>{y.style.opacity="1",y.style.maxHeight="500px"},50)},300))}),o.addEventListener("change",s),i.addEventListener("change",a),document.querySelector("form").addEventListener("submit",function(t){return e.checked&&!n.value?(t.preventDefault(),void alert("Please select a director type")):i.checked&&!c.value?(t.preventDefault(),void alert("Please select a reason for UIF exemption")):void 0})});