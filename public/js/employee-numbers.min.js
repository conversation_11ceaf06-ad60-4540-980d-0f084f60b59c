document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("employeeNumberMode"),n=document.getElementById("firstCompanyEmployeeNumber"),t=document.getElementById("updateExistingWrapper"),a=document.getElementById("updateExistingEmployees"),o=document.getElementById("employeeNumberSettingsForm"),d=e.value,i=n.dataset.currentValue;function c(){const a="automatic"===e.value,o=n.value!==i,c=e.value!==d;t.style.display=a&&(o||c)?"block":"none",n.disabled=!a}e.addEventListener("change",c),n.addEventListener("input",c),o.addEventListener("submit",async function(n){n.preventDefault(),("automatic"!==e.value||!a.checked||await async function(){return new Promise(e=>{const n=document.createElement("div");n.className="confirmation-dialog",n.innerHTML='\n                <div class="dialog-content">\n                    <h3>Confirm Update</h3>\n                    <p>This will update ALL existing employee numbers to match the new format. This action cannot be undone.</p>\n                    <p>Are you sure you want to continue?</p>\n                    <div class="dialog-actions">\n                        <button class="btn btn-secondary" data-action="cancel">Cancel</button>\n                        <button class="btn btn-primary" data-action="confirm">Confirm</button>\n                    </div>\n                </div>\n            ',document.body.appendChild(n),n.querySelector('[data-action="cancel"]').addEventListener("click",()=>{document.body.removeChild(n),e(!1)}),n.querySelector('[data-action="confirm"]').addEventListener("click",()=>{document.body.removeChild(n),e(!0)})})}())&&this.submit()}),c()});