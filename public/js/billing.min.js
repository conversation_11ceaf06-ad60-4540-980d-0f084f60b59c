document.addEventListener("DOMContentLoaded",function(){try{var e=document.querySelectorAll(".payment-method-option"),t=document.querySelectorAll('input[name="paymentMethod"]'),a=(0<e.length&&e.forEach(e=>{e.addEventListener("click",function(){var e=this.querySelector('input[type="radio"]');e&&(e.checked=!0,r())})}),0<t.length&&(t.forEach(e=>{e.addEventListener("change",r)}),r()),document.getElementById("fromDate"),document.getElementById("toDate"),document.getElementById("updateStatement")),o=document.getElementById("downloadExcel"),c=document.getElementById("downloadPDF");a&&a.addEventListener("click",async function(){var e,t,a,n,o=document.getElementById("fromDate").value,c=document.getElementById("toDate").value;try{var l=await fetch("/billing/statement/update",{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('input[name="_csrf"]').value},body:JSON.stringify({fromDate:o,toDate:c})});if(!l.ok)throw new Error("Failed to update statement");e=await l.json(),document.querySelector(".billing-period").textContent=new Date(e.startDate).toLocaleDateString()+" - "+new Date(e.endDate).toLocaleDateString(),document.querySelector(".stat-value:nth-child(1)").textContent="R "+e.currentBalance,document.querySelector(".stat-value:nth-child(2)").textContent=e.activeEmployees,t=e.activeEmployees<=10?"Starter":e.activeEmployees<=50?"Growth":e.activeEmployees<=100?"Business":"Enterprise",document.getElementById("currentTier").textContent=t,a=((25+(e.activeEmployees-1)*(e.activeEmployees<=10?11.5:e.activeEmployees<=50?7.5:e.activeEmployees<=100?6.75:5.75))/e.activeEmployees).toFixed(2),document.querySelector(".stat-value:nth-child(4)").textContent="R "+a,n=(25+(e.activeEmployees-1)*(e.activeEmployees<=10?11.5:e.activeEmployees<=50?7.5:e.activeEmployees<=100?6.75:5.75)).toFixed(2),document.getElementById("totalPrice").textContent="R "+n,d("Statement updated successfully","success")}catch(o){d("Failed to update statement. Please try again.","error")}}),o&&o.addEventListener("click",async function(){var e=document.getElementById("fromDate").value,t=document.getElementById("toDate").value;try{var a=await fetch(`/billing/statement/download/excel?fromDate=${e}&toDate=`+t,{headers:{"CSRF-Token":document.querySelector('input[name="_csrf"]').value}});if(!a.ok)throw new Error("Failed to download Excel statement");var n=await a.blob(),o=window.URL.createObjectURL(n),c=document.createElement("a");c.href=o,c.download=`statement_${e}_${t}.xlsx`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(o),d("Excel statement downloaded successfully","success")}catch(e){d("Failed to download Excel statement. Please try again.","error")}}),c&&c.addEventListener("click",async function(){var e=document.getElementById("fromDate").value,t=document.getElementById("toDate").value;try{var a=await fetch(`/billing/statement/download/pdf?fromDate=${e}&toDate=`+t,{headers:{"CSRF-Token":document.querySelector('input[name="_csrf"]').value}});if(!a.ok)throw new Error("Failed to download PDF statement");var n=await a.blob(),o=window.URL.createObjectURL(n),c=document.createElement("a");c.href=o,c.download=`statement_${e}_${t}.pdf`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(o),d("PDF statement downloaded successfully","success")}catch(e){d("Failed to download PDF statement. Please try again.","error")}});try{m=document.getElementById("trialStatus"),u=document.querySelector(".trial-status-section"),m&&u&&(u.classList.contains("trial-active")?(i=new Date(u.dataset.trialEnd),s=Math.ceil((i-new Date)/864e5),m.textContent=s+" days remaining in your trial"):m.textContent="Your account is fully activated")}catch(e){}let n=document.querySelector("form");n&&n.addEventListener("submit",async function(t){t.preventDefault();var e=new FormData(n),a={paymentMethod:e.get("paymentMethod"),autoPayEnabled:"on"===e.get("autoPayEnabled")};try{if(!(await fetch("/billing/preferences",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-Token":e.get("_csrf")},body:JSON.stringify(a)})).ok)throw new Error("Failed to save preferences");{let e=document.createElement("div");e.className="alert alert-success",e.textContent="Preferences saved successfully!",n.insertAdjacentElement("beforebegin",e),setTimeout(()=>{e.remove()},3e3)}}catch(t){let e=document.createElement("div");e.className="alert alert-error",e.textContent="Failed to save preferences. Please try again.",n.insertAdjacentElement("beforebegin",e),setTimeout(()=>{e.remove()},3e3)}});var l=document.querySelector('input[name="autoPayEnabled"]');function r(){document.querySelectorAll(".payment-method-option").forEach(e=>{var t=e.querySelector('input[type="radio"]');t&&t.checked?e.classList.add("selected"):e.classList.remove("selected")})}function d(e,t="success"){let a=document.getElementById("notification");var n,o;a&&(n=a.querySelector("i"),o=a.querySelector(".notification-message"),n)&&o&&(n.className="fas "+("success"===t?"fa-check-circle":"fa-exclamation-circle"),o.textContent=e,a.className="notification "+t,a.style.display="flex",setTimeout(()=>{a.style.display="none"},3e3))}l&&l.addEventListener("change",function(){}),document.querySelectorAll("[title]").forEach(e=>{new Tooltip(e,{placement:"top",trigger:"hover"})})}catch(e){}var i,s,m,u});