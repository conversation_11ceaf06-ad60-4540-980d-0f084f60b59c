document.addEventListener("DOMContentLoaded",()=>{document.getElementById("download-zip");const e=document.getElementById("from-date"),t=document.getElementById("to-date"),n=document.getElementById("employeeCount"),o=document.getElementById("combine-payslips"),a=document.getElementById("export-form");async function r(){const o=e.value,a=t.value;if(o&&a)try{const e=await fetch(`/reporting/finalized-employee-count?fromDate=${o}&toDate=${a}`),t=await e.json();n.textContent=t.count}catch(e){!function(e,t){console.error("Error fetching employee count:",t),n.textContent="Error"}(0,e)}}const c=new Date,d=new Date(c.getFullYear(),c.getMonth(),1),i=new Date(c.getFullYear(),c.getMonth()+1,0),l=flatpickr(e,{dateFormat:"Y-m-d",defaultDate:d,onChange:function(e){s.set("minDate",e[0]),r()}}),s=flatpickr(t,{dateFormat:"Y-m-d",defaultDate:i,onChange:function(e){l.set("maxDate",e[0]),r()}});r(),a.addEventListener("submit",n=>{n.preventDefault();const r=e.value,c=t.value;o.checked,r&&c?async function(e){try{const t=await fetch("/reporting/generate-payslips",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fromDate:e.get("fromDate"),toDate:e.get("toDate"),combinePayslips:"on"===e.get("combinePayslips")})});if(!t.ok){const e=await t.json();throw new Error(e.error||`HTTP error! status: ${t.status}`)}const n=await t.blob(),o=window.URL.createObjectURL(n),a=document.createElement("a");a.style.display="none",a.href=o,a.download="payslips.zip",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(o)}catch(e){alert(e.message||"An error occurred while generating payslips. Please try again.")}}(new FormData(a)):alert("Please select both From and To dates.")})});