function e(){document.addEventListener("change",function(t){var e,c,l;t.target.classList.contains("select-all-checkbox")&&(e=".payslip-checkbox:not([disabled])",(l=t.target.dataset.frequency)?(c=document.getElementById(l+"-payslips-body"))&&(c.querySelectorAll(e).forEach(e=>{e.checked=t.target.checked}),c=document.getElementById("finalize-selected-"+l))&&(c.style.display=t.target.checked?"inline-flex":"none"):(document.querySelectorAll(e).forEach(e=>{e.checked=t.target.checked}),(l=document.getElementById("finalize-selected"))&&(l.style.display=t.target.checked?"inline-flex":"none")))}),document.addEventListener("click",function(e){var t=e.target.closest("tr[data-payslip-id]");t&&(t=t.dataset.payslipId,e.target.closest(".checkbox-wrapper")||"function"==typeof viewPayslip&&t&&viewPayslip(t))}),document.addEventListener("change",function(e){if(e.target.classList.contains("payslip-checkbox")){e=e.target.closest("tr");if(e){var c=e.closest("tbody");if(c){var e=c.id.replace("-payslips-body",""),l=document.querySelector(`.select-all-checkbox[data-frequency="${e}"]`);if(l){let e=c.querySelectorAll(".payslip-checkbox:not([disabled])"),t=Array.from(e).every(e=>e.checked);l.checked=t}c=Array.from(document.querySelectorAll(".payslip-checkbox:not([disabled])")).some(e=>e.checked),l=document.getElementById("finalize-selected-"+e);l&&(l.style.display=c?"inline-flex":"none")}}}});let t=()=>{document.querySelectorAll("tr[data-payslip-id]").forEach(e=>{e.classList.contains("clickable-row")||(e.classList.add("clickable-row"),e.setAttribute("title","Click to view payslip details"))})},c=document.getElementById("payslipReviewModal"),e;c&&(new MutationObserver(function(e){e.forEach(function(e){"style"===e.attributeName&&"block"===c.style.display&&setTimeout(t,100)})}).observe(c,{attributes:!0}),(e=document.getElementById("frequency-payslips-body"))&&new MutationObserver(function(e){setTimeout(t,100)}).observe(e,{childList:!0,subtree:!0}),document.querySelectorAll(".tab-button").forEach(e=>{e.addEventListener("click",()=>{setTimeout(t,100)})}))}document.addEventListener("DOMContentLoaded",function(){e()});