function updatePaginationLinks(e){document.querySelectorAll(".pagination-button").forEach(function(t){let n=t.getAttribute("href");n=n.replace(/(&frequency=[^&]*)/,""),n.includes("?")?n+="&frequency="+e:n+="?frequency="+e,t.setAttribute("href",n)})}document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".progress-tab-link.active");if(e)updatePaginationLinks(e.getAttribute("data-tab").replace("progress-",""));else{const e=document.querySelector(".pay-run-history-tab.active");e&&updatePaginationLinks(e.id.replace("pay-run-history-",""))}});