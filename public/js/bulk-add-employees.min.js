document.addEventListener("DOMContentLoaded",function(){let r=document.getElementById("import-form"),c=document.getElementById("file-input"),s=document.getElementById("file-name"),p=document.querySelector('meta[name="csrf-token"]').content,m=document.body.getAttribute("data-company-code");function u(e){a(`
            <div class="modal-header">
                <h5 class="modal-title">Upload Preview</h5>
                <button type="button" class="close" onclick="closeModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <p><strong>File Upload Summary:</strong></p>
                    <p>Records found: ${e.validCount}</p>
                    <p>Preview of first 5 rows:</p>
                </div>
                ${e.validRecords&&0<e.validRecords.length?`
                    <div class="mt-3">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Row</th>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>Email</th>
                                        <th>Department</th>
                                        <th>Position</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${e.validRecords.slice(0,5).map(e=>`
                                        <tr>
                                            <td>${e.row}</td>
                                            <td>${e["First Name"]||""}</td>
                                            <td>${e["Last Name"]||""}</td>
                                            <td>${e.Email||""}</td>
                                            <td>${e.Department||""}</td>
                                            <td>${e.Position||""}</td>
                                        </tr>
                                    `).join("")}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `:""}
            </div>
            <div class="modal-footer">
                ${0<e.validCount?'\n                    <button type="button" class="btn btn-primary" onclick="confirmUpload()">Confirm Upload</button>\n                ':""}
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        `)}function a(e){let t=document.getElementById("resultsModal");t||((t=document.createElement("div")).id="resultsModal",t.className="modal",document.body.appendChild(t)),t.innerHTML=`
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    ${e}
                </div>
            </div>
        `,t.style.display="block"}r&&c&&s&&p&&m&&(c.addEventListener("change",function(e){e=e.target.files[0];return e?(s.textContent=e.name,e.name.match(/\.(xlsx|xls)$/)?10485760<e.size?(alert("File size exceeds 10MB limit"),c.value="",void(s.textContent="No file chosen")):void 0:(alert("Please select a valid Excel file (.xlsx or .xls)"),c.value="",void(s.textContent="No file chosen"))):void(s.textContent="No file chosen")}),r.addEventListener("submit",async function(t){t.preventDefault();var i=c.files[0];if(i)try{let e=new FormData,t=(e.append("file",i),r.querySelector('button[type="submit"]')),s=(t&&(t.disabled=!0,t.innerHTML='<i class="ph ph-spinner"></i> Uploading...'),c.disabled=!0,"/direct-upload/"+m),a=new AbortController,l=setTimeout(()=>a.abort(),3e4),o=await fetch(s,{method:"POST",body:e,signal:a.signal});clearTimeout(l),t&&(t.disabled=!1,t.innerHTML='<i class="ph ph-upload-simple"></i> Upload'),c.disabled=!1;var d,n=o.headers.get("content-type");if(!n||!n.includes("application/json"))throw await o.text(),new Error("Unexpected response format: "+(n||"unknown"));if(!(d=await o.json()).success)throw new Error(d.message||"Upload failed");if(window.uploadData={...d,companyCode:m,csrfToken:p,allEmployees:d.allEmployees||d.preview},u({message:`Processing ${d.totalRows} employees...`,previewMessage:5<d.totalRows?`Showing preview of first 5 employees out of ${d.totalRows} total employees.`:`Showing all ${d.totalRows} employees.`,validCount:d.totalRows,invalidCount:0,validRecords:d.preview.map((e,t)=>({row:t+2,...e})),invalidRecords:[],totalEmployees:d.totalRows,maxEmployees:d.maxEmployees}),!o.ok)throw new Error(d.message||d.error||"Upload failed with status: "+o.status);if(!d.success)throw new Error(d.message||"Upload failed");u({validCount:d.totalRows||0,invalidCount:0,validRecords:(d.preview||[]).map((e,t)=>({row:t+2,...e})),invalidRecords:[]})}catch(t){let e=r.querySelector('button[type="submit"]');e&&(e.disabled=!1,e.innerHTML='<i class="ph ph-upload-simple"></i> Upload'),c.disabled=!1,a(`
                <div class="modal-header">
                    <h5 class="modal-title">Upload Error</h5>
                    <button type="button" class="close" onclick="closeModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <p><strong>Error:</strong> ${t.message}</p>
                        <p>Error Type: ${t.name}</p>
                        <p>Please check:</p>
                        <ul>
                            <li>The file is a valid Excel file (.xlsx or .xls)</li>
                            <li>The file size is under 10MB</li>
                            <li>The file contains the required columns</li>
                            <li>Your session hasn't expired</li>
                        </ul>
                        <p><strong>Technical details:</strong> ${t.stack?t.stack.split("\n")[0]:"No stack trace available"}</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                    <button type="button" class="btn btn-primary" onclick="retryUpload()">Try Again</button>
                </div>
            `)}else alert("Please select a file first")}),window.retryUpload=function(){closeModal(),setTimeout(()=>{var e;0<c.files.length?(e=r.querySelector('button[type="submit"]'))&&e.click():c.click()},500)},window.closeModal=function(){var e=document.getElementById("resultsModal");e&&(e.style.display="none")},window.confirmUpload=async function(){if(window.uploadData)try{a('\n                <div class="modal-header">\n                    <h5 class="modal-title">Processing Employees</h5>\n                </div>\n                <div class="modal-body">\n                    <div class="text-center">\n                        <div class="spinner-border" role="status">\n                            <span class="sr-only">Loading...</span>\n                        </div>\n                        <p class="mt-3">Adding employees to the system...</p>\n                        <p>This may take a few moments. Please don\'t close this window.</p>\n                    </div>\n                </div>\n            ');var t=await fetch("/direct-confirm-upload/"+window.uploadData.companyCode,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({employees:window.uploadData.allEmployees||window.uploadData.preview})});if(!t.ok){let e=await t.json();throw new Error(e.message||"Upload failed")}var e=await t.json();if(!e.successfulEmployees&&0<e.successful&&window.uploadData&&window.uploadData.preview){let t=new Set(e.errors?e.errors.map(e=>e.name):[]);e.successfulEmployees=window.uploadData.preview.filter(e=>{e=`${e["First Name *"]||e["First Name"]||e.FirstName||""} `+(e["Last Name *"]||e["Last Name"]||e.LastName||"");return!t.has(e)})}a(`
                <div class="modal-header">
                    <h5 class="modal-title">Upload Complete</h5>
                    <button type="button" class="close" onclick="closeModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="summary-section">
                        <div class="summary-item">
                            <span class="label">Total Records</span>
                            <span class="value">${e.successful+e.failed}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">Successfully Added</span>
                            <span class="value success">${e.successful}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">Failed</span>
                            <span class="value error">${e.failed}</span>
                        </div>
                    </div>
                    
                    ${e.successfulEmployees&&0<e.successfulEmployees.length?`
                    <div class="details-section">
                        <h4>
                            <i class="ph ph-check-circle"></i> Successfully Added Employees
                        </h4>
                        <div class="records-list success-list">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>Email</th>
                                        <th>Department</th>
                                        <th>Position</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${e.successfulEmployees.map(e=>`
                                        <tr>
                                            <td>${e["First Name *"]||""}</td>
                                            <td>${e["Last Name *"]||""}</td>
                                            <td>${e.Email||""}</td>
                                            <td>${e.Department||""}</td>
                                            <td>${e.Position||""}</td>
                                        </tr>
                                    `).join("")}
                                </tbody>
                            </table>
                        </div>
                    </div>`:""}
                    
                    ${e.errors&&0<e.errors.length?`
                    <div class="details-section mt-4">
                        <h4>Error Details</h4>
                        <div class="records-list error-list">
                            <ul class="list-group">
                                ${e.errors.map(e=>`
                                    <li class="list-group-item list-group-item-danger">
                                        <strong>${e.name}</strong>: ${e.error}
                                    </li>
                                `).join("")}
                            </ul>
                        </div>
                    </div>`:""}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="window.location.reload()">Done</button>
                </div>
            `),c.value="",s.textContent="No file chosen",window.uploadData=null}catch(e){a(`
                <div class="modal-header">
                    <h5 class="modal-title">Upload Error</h5>
                    <button type="button" class="close" onclick="closeModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <p><strong>Error:</strong> ${e.message}</p>
                        <p>Please try again or contact support if the problem persists.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                </div>
            `)}})});