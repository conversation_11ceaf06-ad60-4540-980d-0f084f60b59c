function debug(e,n,t=null){const o=`[${(new Date).toISOString()}] [EmployeeActions] [${e.toUpperCase()}]`;t?console[e](`${o} ${n}`,t):console[e](`${o} ${n}`)}function showComingSoon(){const e=document.createElement("div");e.className="toast",e.style.cssText="\n        position: fixed;\n        bottom: 20px;\n        right: 20px;\n        background: var(--card-background);\n        color: var(--text-primary);\n        padding: 1rem 1.5rem;\n        border-radius: 8px;\n        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n        border: 1px solid var(--border-color);\n        z-index: 1000;\n        font-family: 'Inter', sans-serif;\n        font-size: 0.875rem;\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n        opacity: 0;\n        transform: translateY(20px);\n        transition: all 0.3s ease;\n    ",e.innerHTML='\n        <i class="ph ph-info" style="color: var(--primary-color)"></i>\n        <span>Coming Soon!</span>\n    ',document.body.appendChild(e),setTimeout(()=>{e.style.opacity="1",e.style.transform="translateY(0)"},100),setTimeout(()=>{e.style.opacity="0",e.style.transform="translateY(20px)",setTimeout(()=>e.remove(),300)},3e3)}function confirmDeleteEmployee(e){if(debug("log","Function called with employee ID",e),!e)return void debug("error","No employee ID provided to delete function");const n=document.createElement("div");n.className="confirmation-modal",n.style.cssText="\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(0, 0, 0, 0.5);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 1050;\n        opacity: 0;\n        transition: opacity 0.3s ease;\n    ",n.innerHTML='\n        <div class="modal-content" style="\n            background-color: white;\n            padding: 2rem;\n            border-radius: 8px;\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n            width: 90%;\n            max-width: 500px;\n            text-align: center;\n            position: relative;\n            transform: translateY(20px);\n            transition: transform 0.3s ease;\n            max-height: 90vh;\n            overflow-y: auto;\n            margin: 0 1rem;\n        ">\n            <i class="ph ph-warning-circle" style="\n                font-size: 3rem;\n                color: #f59e0b;\n                margin-bottom: 1rem;\n                display: inline-block;\n            "></i>\n            <h3 style="margin-bottom: 1rem; font-size: 1.5rem; color: #111827;">Delete Employee</h3>\n            <p style="margin-bottom: 2rem; color: #4b5563; line-height: 1.5;">Are you sure you want to delete this employee? This action cannot be undone.</p>\n            <div class="modal-actions" style="\n                display: flex;\n                justify-content: center;\n                gap: 1rem;\n                flex-wrap: wrap;\n            ">\n                <button id="cancelDeleteBtn" class="btn-secondary" style="\n                    padding: 0.75rem 1.5rem;\n                    background-color: #e5e7eb;\n                    color: #374151;\n                    border: none;\n                    border-radius: 0.375rem;\n                    cursor: pointer;\n                    font-weight: 500;\n                    transition: background-color 0.2s;\n                    min-width: 100px;\n                ">Cancel</button>\n                <button id="confirmDeleteBtn" class="btn-danger" style="\n                    padding: 0.75rem 1.5rem;\n                    background-color: #ef4444;\n                    color: white;\n                    border: none;\n                    border-radius: 0.375rem;\n                    cursor: pointer;\n                    font-weight: 500;\n                    transition: background-color 0.2s;\n                    min-width: 100px;\n                ">Delete</button>\n            </div>\n        </div>\n    ',debug("log","Appending modal to document body"),document.body.appendChild(n);const t=document.body.style.overflow;document.body.style.overflow="hidden",setTimeout(()=>{n.style.opacity="1";const e=n.querySelector(".modal-content");e&&(e.style.transform="translateY(0)")},10);const o=function(e){"cancelDeleteBtn"===e.target.id&&(e.target.style.backgroundColor="#d1d5db"),"confirmDeleteBtn"===e.target.id&&(e.target.style.backgroundColor="#dc2626")},r=function(e){"cancelDeleteBtn"===e.target.id&&(e.target.style.backgroundColor="#e5e7eb"),"confirmDeleteBtn"===e.target.id&&(e.target.style.backgroundColor="#ef4444")};n.addEventListener("mouseover",o),n.addEventListener("mouseout",r);const s=()=>{debug("log","Closing modal"),n.style.opacity="0";const e=n.querySelector(".modal-content");e&&(e.style.transform="translateY(20px)"),document.body.style.overflow=t,setTimeout(()=>{n.removeEventListener("mouseover",o),n.removeEventListener("mouseout",r),n.remove()},300)};n.addEventListener("click",function(e){debug("log","Modal click event",{isBackdrop:e.target===n}),e.target===n&&s()});const i=document.getElementById("cancelDeleteBtn");debug("log","Cancel button element found",{exists:!!i}),i?i.addEventListener("click",function(){debug("log","Cancel button clicked"),s()}):debug("error","Cancel button not found in the DOM");const a=document.getElementById("confirmDeleteBtn");debug("log","Confirm button element found",{exists:!!a}),a?a.addEventListener("click",function(){debug("log","Confirm button clicked");const n=document.body.getAttribute("data-company-code");if(debug("log","Company code from body attribute",{companyCode:n}),!n)return debug("error","Company code not found"),showToast("Error: Company code not found","error"),void s();s(),debug("log","Creating loading toast");const t=document.createElement("div");t.className="toast info",t.style.cssText="\n                position: fixed;\n                bottom: 20px;\n                right: 20px;\n                background: #eff6ff;\n                color: #3b82f6;\n                padding: 1rem 1.5rem;\n                border-radius: 8px;\n                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n                z-index: 1000;\n                font-family: 'Inter', sans-serif;\n                font-size: 0.875rem;\n                display: flex;\n                align-items: center;\n                gap: 0.5rem;\n            ",t.innerHTML='\n                <i class="ph ph-spinner ph-spin"></i>\n                <span>Deleting employee...</span>\n            ',document.body.appendChild(t);const o=`/clients/${n}/employeeProfile/${e}/delete`;debug("log","Preparing DELETE request",{url:o});const r=document.querySelector('meta[name="csrf-token"]')?.content;debug("log","CSRF Token retrieval",{found:!!r,token:r?r.substring(0,5)+"...":null}),fetch(o,{method:"DELETE",headers:{"Content-Type":"application/json","CSRF-Token":r||""}}).then(e=>{if(debug("log","DELETE response received",{status:e.status,ok:e.ok,statusText:e.statusText}),!e.ok)throw debug("error","Response not OK",{status:e.status,statusText:e.statusText}),new Error(`Network response was not ok: ${e.status} ${e.statusText}`);return e.json()}).then(e=>{if(debug("log","DELETE response data",e),t.remove(),!e.success)throw debug("error","Delete failed but response was OK",{message:e.message||"No error message provided"}),new Error(e.message||"Failed to delete employee");{debug("log","Delete successful, creating success toast");const e=document.createElement("div");e.className="toast success",e.style.cssText="\n                    position: fixed;\n                    bottom: 20px;\n                    right: 20px;\n                    background: #ecfdf5;\n                    color: #059669;\n                    padding: 1rem 1.5rem;\n                    border-radius: 8px;\n                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n                    z-index: 1000;\n                    font-family: 'Inter', sans-serif;\n                    font-size: 0.875rem;\n                    display: flex;\n                    align-items: center;\n                    gap: 0.5rem;\n                ",e.innerHTML='\n                    <i class="ph ph-check-circle"></i>\n                    <span>Employee deleted successfully</span>\n                ',document.body.appendChild(e),debug("log","Setting up redirect timeout"),setTimeout(()=>{debug("log","Redirecting to employee management page",{redirectUrl:`/clients/${n}/employeeManagement`}),window.location.href=`/clients/${n}/employeeManagement`},1500)}}).catch(e=>{debug("error","DELETE request failed",{message:e.message,stack:e.stack,toString:e.toString()}),t.remove();const n=document.createElement("div");n.className="toast error",n.style.cssText="\n                position: fixed;\n                bottom: 20px;\n                right: 20px;\n                background: #fef2f2;\n                color: #dc2626;\n                padding: 1rem 1.5rem;\n                border-radius: 8px;\n                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n                z-index: 1000;\n                font-family: 'Inter', sans-serif;\n                font-size: 0.875rem;\n                display: flex;\n                align-items: center;\n                gap: 0.5rem;\n            ",n.innerHTML=`\n                <i class="ph ph-x-circle"></i>\n                    <span>Failed to delete employee: ${e.message}</span>\n            `,document.body.appendChild(n),setTimeout(()=>n.remove(),3e3)})}):debug("error","Confirm button not found in the DOM")}function showToast(e,n="info"){const t=document.createElement("div");t.className=`toast ${n}`,t.textContent=e,document.body.appendChild(t),t.offsetHeight,t.classList.add("show"),setTimeout(()=>{t.classList.remove("show"),setTimeout(()=>t.remove(),300)},3e3)}document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("monthModal"),n=document.getElementById("selectMonthButton"),t=document.querySelector(".close"),o=document.getElementById("periodsList");function r(){e&&(e.style.display="none",document.body.style.overflow="")}n&&n.addEventListener("click",function(){e&&(e.style.display="block",document.body.style.overflow="hidden")}),t&&t.addEventListener("click",function(){r()}),window.addEventListener("click",function(n){e&&n.target===e&&r()}),o&&o.addEventListener("click",function(e){const t=e.target.closest(".period-item");if(!t)return;const o=t.dataset.period;if(!o)return;const s=n?.dataset.employeeId;if(!s)return;const i=document.body.getAttribute("data-company-code");i&&(fetch(`/clients/${i}/employeeProfile/${s}/updatePeriod`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({periodDate:o})}).then(e=>e.json()).then(e=>{e.success?window.location.reload():showToast("Error updating period","error")}).catch(e=>{console.error("Error:",e),showToast("Error updating period","error")}),r())})}),function(){let e=1;function n(){const n=document.querySelector(".page-indicator");n&&(n.textContent=`${e} of 4`)}window.prevPage=function(){if(e>1){const t=document.querySelector(`[data-page="${e}"]`);t&&(t.style.display="none"),e--;const o=document.querySelector(`[data-page="${e}"]`);o&&(o.style.display="block"),n()}},window.nextPage=function(){if(e<4){const t=document.querySelector(`[data-page="${e}"]`);t&&(t.style.display="none"),e++;const o=document.querySelector(`[data-page="${e}"]`);o&&(o.style.display="block"),n()}}}(),document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".calc-page").forEach((e,n)=>{e.style.display=0===n?"block":"none"}),updatePageIndicator()}),window.confirmDeleteEmployee=confirmDeleteEmployee,console.log("employeeActions.js loaded - confirmDeleteEmployee function available:",typeof window.confirmDeleteEmployee);