class Notifications{static show(e,t="info",s=4e3){let n=document.getElementById("toast-container");n||(n=document.createElement("div"),n.id="toast-container",n.style.cssText="position: fixed; top: 20px; right: 20px; z-index: 10000; display: flex; flex-direction: column; gap: 10px;",document.body.appendChild(n));const o=document.createElement("div");o.className=`toast toast-${t}`;const i=document.createElement("div");i.style.cssText="display: flex; align-items: center; justify-content: space-between; gap: 12px;";const c=document.createElement("div");c.style.cssText="display: flex; align-items: center; gap: 8px; flex: 1;";const a=document.createElement("i");switch(t){case"success":a.className="ph ph-check-circle";break;case"error":a.className="ph ph-x-circle";break;case"warning":a.className="ph ph-warning";break;default:a.className="ph ph-info"}const r=document.createElement("span");r.textContent=e,r.style.cssText="flex: 1;";const l=document.createElement("button");return l.innerHTML='<i class="ph ph-x"></i>',l.style.cssText="background: none; border: none; color: inherit; cursor: pointer; padding: 4px; border-radius: 4px; opacity: 0.7; transition: opacity 0.2s ease;",l.onmouseover=()=>l.style.opacity="1",l.onmouseout=()=>l.style.opacity="0.7",l.onclick=()=>{o.classList.remove("show"),setTimeout(()=>o.remove(),300)},c.appendChild(a),c.appendChild(r),i.appendChild(c),i.appendChild(l),o.appendChild(i),n.appendChild(o),setTimeout(()=>o.classList.add("show"),100),setTimeout(()=>{o.parentNode&&(o.classList.remove("show"),setTimeout(()=>{o.parentNode&&o.remove()},300))},s),o}static success(e){this.show(e,"success")}static error(e){this.show(e,"error")}static warning(e){this.show(e,"warning")}static info(e){this.show(e,"info")}}window.Notifications=Notifications;