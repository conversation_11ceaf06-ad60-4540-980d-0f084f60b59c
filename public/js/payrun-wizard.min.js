document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("payRunForm"),t=document.querySelectorAll(".wizard-step"),n=document.querySelectorAll(".progress-step"),a=document.getElementById("nextStep"),c=document.getElementById("prevStep"),o=document.getElementById("submitPayRun");let l=1;function d(e){t.forEach(t=>{t.classList.remove("active"),t.dataset.step==e&&t.classList.add("active")}),n.forEach(t=>{t.classList.remove("active"),t.dataset.step<=e&&t.classList.add("active")}),c.disabled=1===e,a.style.display=4===e?"none":"block",o.style.display=4===e?"block":"none",l=e}a.addEventListener("click",()=>{(function(e){switch(e){case 1:return function(){const e=document.getElementById("frequency").value,t=document.getElementById("startDate").value,n=document.getElementById("endDate").value,a=document.getElementById("paymentDate").value;return e&&t&&n&&a?new Date(n)<=new Date(t)?(alert("End date must be after start date"),!1):!(new Date(a)<new Date(n)&&(alert("Payment date must be after end date"),1)):(alert("Please fill in all fields"),!1)}();case 2:return 0!==document.querySelectorAll('input[name="selectedEmployees"]:checked').length||(alert("Please select at least one employee"),!1);default:return!0}})(l)&&(3===l&&function(){const e=new Date(document.getElementById("startDate").value),t=new Date(document.getElementById("endDate").value),n=new Date(document.getElementById("paymentDate").value),a=document.querySelectorAll('input[name="selectedEmployees"]:checked');document.getElementById("confirmPeriod").textContent=`${e.toLocaleDateString()} - ${t.toLocaleDateString()}`,document.getElementById("confirmPaymentDate").textContent=n.toLocaleDateString(),document.getElementById("confirmEmployees").textContent=a.length}(),d(l+1))}),c.addEventListener("click",()=>{d(l-1)}),e.addEventListener("submit",async t=>{if(t.preventDefault(),document.getElementById("confirmAccuracy").checked)try{const t=new FormData(e),n=await fetch("/api/payrun/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(t))});if(!n.ok)throw new Error("Failed to create pay run");{const e=await n.json();window.location.href=`/payroll/view/${e.payRunId}`}}catch(e){alert("Error creating pay run: "+e.message)}else alert("Please confirm the accuracy of the information")})});