document.addEventListener("DOMContentLoaded",function(){let t=document.getElementById("createCompanyModal"),n=document.getElementById("deactivateCompanyModal"),e=document.getElementById("createCompanyBtn"),a=document.querySelectorAll(".deactivate-btn"),o=document.querySelectorAll(".close, .close-modal");function i(){var e=document.querySelector('meta[name="csrf-token"]')?.content;if(e)return e;e=document.getElementById("csrfToken")?.value;if(e)return e;throw new Error("CSRF token not found")}function c(e){e.classList.add("show"),document.body.style.overflow="hidden"}function r(e){e.classList.remove("show"),document.body.style.overflow=""}e&&e.addEventListener("click",function(e){e.preventDefault(),c(t)}),a.forEach(e=>{e.addEventListener("click",function(e){e.preventDefault();e=this.dataset.companyId;document.getElementById("deactivateCompanyId").value=e,c(n)})}),o.forEach(e=>{e.addEventListener("click",function(){r(this.closest(".modal"))})}),window.addEventListener("click",function(e){e.target.classList.contains("modal")&&r(e.target)});var d=document.getElementById("createCompanyForm"),d=(d&&d.addEventListener("submit",async function(e){e.preventDefault();try{let e=new FormData(this),t=i(),n=await fetch("/companies/create",{method:"POST",headers:{"CSRF-Token":t,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(e))}),a=await n.json();if(!a.success)throw new Error(a.message||"Failed to create company");window.location.reload()}catch(e){alert(e.message||"An error occurred")}}),document.getElementById("deactivateCompanyForm"));d&&d.addEventListener("submit",async function(e){e.preventDefault();try{let e=new FormData(this),t=i(),n=document.getElementById("deactivateCompanyId").value,a="on"===e.get("deleteImmediately"),o=this.querySelector('button[type="submit"]');o.disabled=!0,o.innerHTML=`<i class="ph ph-spinner ph-spin"></i> ${a?"Deleting":"Deactivating"}...`;var c=await(await fetch(`/companies/${n}/deactivate`,{method:"POST",headers:{"CSRF-Token":t,"Content-Type":"application/json"},body:JSON.stringify({reason:e.get("reason"),deleteImmediately:a})})).json();if(!c.success)throw new Error(c.message||`Failed to ${a?"delete":"deactivate"} company`);alert(a?"Company deleted successfully":"Company deactivated successfully"),window.location.href=c.redirectUrl||"/companies/select"}catch(e){alert(e.message||"An error occurred");c=this.querySelector('button[type="submit"]');c.disabled=!1,c.innerHTML='<i class="ph ph-power"></i> Deactivate Company'}});let s=document.getElementById("showInactive"),l=document.getElementById("inactiveCount"),m=document.querySelectorAll(".company-card.inactive");s&&l&&(l.textContent=m.length,m.forEach(e=>{e.style.display=s.checked?"block":"none"}),s.addEventListener("change",function(){m.forEach(e=>{e.style.display=this.checked?"block":"none"})})),window.reactivateCompany=async function(e){try{var t=i(),n=event.currentTarget,a=(n.innerHTML,n.disabled=!0,n.innerHTML='<i class="ph ph-spinner ph-spin"></i> Reactivating...',await(await fetch(`/companies/${e}/reactivate`,{method:"POST",headers:{"CSRF-Token":t,"Content-Type":"application/json"}})).json());if(!a.success)throw new Error(a.message||"Failed to reactivate company");window.location.reload()}catch(e){alert(e.message||"An error occurred while reactivating the company"),submitButton.disabled=!1,submitButton.innerHTML='<i class="ph ph-power"></i> Reactivate'}}});