class e{constructor(e,t={}){this.form=document.getElementById(e),this.form&&(this.options={previewContainerId:"rfiPreview",...t},this.contributionMethodSelect=document.getElementById("contributionCalculation"),this.fixedAmountCard=document.getElementById("fixedAmountCard"),this.rfiPercentageCard=document.getElementById("rfiPercentageCard"),this.employeeRFIInput=document.getElementById("rfiEmployee"),this.employerRFIInput=document.getElementById("rfiEmployer"),this.previewContainer=document.getElementById(this.options.previewContainerId),this.companyCode=window.location.pathname.split("/")[2],this.initializeEventListeners(),this.contributionMethodSelect)&&this.handleContributionMethodChange(this.contributionMethodSelect.value)}initializeEventListeners(){this.form&&this.form.addEventListener("submit",this.handleSubmit.bind(this)),this.contributionMethodSelect&&this.contributionMethodSelect.addEventListener("change",e=>{this.handleContributionMethodChange(e.target.value)}),this.employeeRFIInput&&this.employeeRFIInput.addEventListener("change",()=>this.initializeCalculator()),this.employerRFIInput&&this.employerRFIInput.addEventListener("change",()=>this.initializeCalculator())}handleContributionMethodChange(e){this.fixedAmountCard&&(this.fixedAmountCard.style.display="fixedAmount"===e?"block":"none"),this.rfiPercentageCard&&(this.rfiPercentageCard.style.display="percentageRFI"===e?"block":"none"),"fixedAmount"===e?(this.employeeRFIInput&&(this.employeeRFIInput.value=""),this.employerRFIInput&&(this.employerRFIInput.value="")):"percentageRFI"===e&&(this.form.querySelectorAll('[name^="fixed"]').forEach(e=>e.value=""),this.initializeCalculator())}getEmployeeId(){return window.location.pathname.split("/")[4]}generatePreviewHTML(e){let t=e=>new Intl.NumberFormat("en-ZA",{style:"currency",currency:"ZAR"}).format(e),i=e.components?.map(e=>`
        <tr>
          <td>${e.name}</td>
          <td class="text-right">${t(e.amount)}</td>
          <td class="text-right">${e.percentage}%</td>
          <td class="text-center">
            <i class="ph ph-check"></i>
          </td>
        </tr>
      `).join("")||"";return`
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">RFI Calculation Preview</h5>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col">
              <strong>Total RFI Base:</strong> ${t(e.totalRFI||0)}
            </div>
          </div>
          
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>Component</th>
                <th class="text-right">Amount</th>
                <th class="text-right">Percentage</th>
                <th class="text-center">Included</th>
              </tr>
            </thead>
            <tbody>
              ${i}
            </tbody>
          </table>

          <div class="row mt-3">
            <div class="col">
              <h6>Contributions</h6>
              <table class="table table-sm">
                <tr>
                  <td>Employee (${e.employeePercentage||0}%)</td>
                  <td class="text-right">${t(e.employeeContribution||0)}</td>
                </tr>
                <tr>
                  <td>Employer (${e.employerPercentage||0}%)</td>
                  <td class="text-right">${t(e.employerContribution||0)}</td>
                </tr>
                <tr class="font-weight-bold">
                  <td>Total Contribution</td>
                  <td class="text-right">${t((e.employeeContribution||0)+(e.employerContribution||0))}</td>
                </tr>
              </table>
            </div>
          </div>
          
          <div class="text-muted small">
            Last updated: ${new Date(e.timestamp||Date.now()).toLocaleString()}
          </div>
        </div>
      </div>
    `}async initializeCalculator(){if("percentageRFI"===this.contributionMethodSelect?.value)try{var e=this.getEmployeeId();if(!this.companyCode)throw new Error("Company code not found");var t=await fetch(`/clients/${this.companyCode}/api/rfi/preview/`+e);if(!t.ok)throw new Error("HTTP error! status: "+t.status);var i,o,n,r=await t.json();r.success&&this.previewContainer&&(i=parseFloat(this.employeeRFIInput?.value||0),o=parseFloat(this.employerRFIInput?.value||0),n={...r.data,employeePercentage:i,employerPercentage:o,employeeContribution:r.data.totalRFI*i/100,employerContribution:r.data.totalRFI*o/100,timestamp:Date.now()},this.previewContainer.innerHTML=this.generatePreviewHTML(n))}catch(e){Notifications.error("Unable to load RFI calculation. Please try again.")}}async handleSubmit(e){e.preventDefault();try{let e=new FormData(this.form),t={};for(var[i,o]of e.entries())t[i]=o;var n=t.contributionCalculation;if(!n)throw new Error("Please select a contribution method");if("fixedAmount"===n){if(t.fixedContributionEmployee=parseFloat(t.fixedContributionEmployee||0),t.fixedContributionEmployer=parseFloat(t.fixedContributionEmployer||0),0===t.fixedContributionEmployee&&0===t.fixedContributionEmployer)throw new Error("Please enter at least one contribution amount");delete t.rfiEmployee,delete t.rfiEmployer}else if("percentageRFI"===n){if(t.rfiEmployee=parseFloat(t.rfiEmployee||0),t.rfiEmployer=parseFloat(t.rfiEmployer||0),0===t.rfiEmployee&&0===t.rfiEmployer)throw new Error("Please enter at least one contribution percentage");delete t.fixedContributionEmployee,delete t.fixedContributionEmployer}t.categoryFactor||(t.categoryFactor=1);var r=await(await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify(t)})).json();if(!r.success)throw new Error(r.message||"Failed to save pension fund details");{let e=window.location.pathname.split("/")[2],t=this.getEmployeeId();window.location.href=`/clients/${e}/employeeProfile/`+t}}catch(e){Notifications.error(e.message||"Failed to save pension fund details")}}}document.addEventListener("DOMContentLoaded",()=>{new e("pensionFundForm")});