// Enhanced openFrequency function
function openFrequency(evt, frequencyName) {
  // Hide all tab content
  const tabcontent = document.querySelectorAll(".frequency-group.tab-content");
  for (let i = 0; i < tabcontent.length; i++) {
    tabcontent[i].classList.remove("active");
  }

  // Remove active class from all tab buttons
  const tablinks = document.querySelectorAll(".tabs-navigation .tab-item");
  for (let i = 0; i < tablinks.length; i++) {
    tablinks[i].classList.remove("active");
  }

  // Show the current tab and add active class to the button
  document
    .getElementById("pending-payslips-" + frequencyName)
    .classList.add("active");
  evt.currentTarget.classList.add("active");

  // Also update the Pay Run History section to show the corresponding frequency
  // Hide all pay run history tabs
  const payRunHistoryTabs = document.querySelectorAll(".pay-run-history-tab");
  payRunHistoryTabs.forEach((tab) => {
    tab.classList.remove("active");
  });

  // Show the corresponding pay run history tab
  const payRunHistoryTab = document.getElementById(`pay-run-history-${frequencyName}`);
  if (payRunHistoryTab) {
    payRunHistoryTab.classList.add("active");
  }
  
  // Update pagination links to include the frequency
  if (typeof updatePaginationLinks === 'function') {
    updatePaginationLinks(frequencyName);
  }
  
  // Update URL with the selected frequency without reloading the page
  const url = new URL(window.location.href);
  url.searchParams.set('frequency', frequencyName);
  window.history.replaceState({}, '', url);
  
  // Save the selected frequency to localStorage
  localStorage.setItem('selectedFrequency', frequencyName);
}

// Override the original openFrequency function when the page loads
document.addEventListener('DOMContentLoaded', function() {
  if (typeof window.originalOpenFrequency === 'undefined') {
    window.originalOpenFrequency = window.openFrequency;
    window.openFrequency = openFrequency;
  }
});
