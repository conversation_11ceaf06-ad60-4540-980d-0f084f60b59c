class AlertService{static show(e,t="success",s=5e3){const a=document.getElementById("alert-container")||this.createAlertContainer(),c=document.createElement("div");c.className=`alert alert-${t} slide-in`;const n=this.getIconForType(t);c.innerHTML=`\n      ${n}\n      <span class="alert-message">${e}</span>\n      <button type="button" class="alert-close">\n        <i class="fas fa-times"></i>\n      </button>\n    `,a.appendChild(c),c.querySelector(".alert-close").addEventListener("click",()=>{this.removeAlert(c)}),setTimeout(()=>{this.removeAlert(c)},s)}static removeAlert(e){e.classList.add("slide-out"),setTimeout(()=>{e.remove()},300)}static createAlertContainer(){const e=document.createElement("div");return e.id="alert-container",document.body.appendChild(e),e}static getIconForType(e){const t={success:'<i class="fas fa-check-circle"></i>',error:'<i class="fas fa-exclamation-circle"></i>',warning:'<i class="fas fa-exclamation-triangle"></i>',info:'<i class="fas fa-info-circle"></i>'};return t[e]||t.info}}