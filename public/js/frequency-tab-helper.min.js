function openFrequency(e,t){const n=document.querySelectorAll(".frequency-group.tab-content");for(let e=0;e<n.length;e++)n[e].classList.remove("active");const o=document.querySelectorAll(".tabs-navigation .tab-item");for(let e=0;e<o.length;e++)o[e].classList.remove("active");document.getElementById("pending-payslips-"+t).classList.add("active"),e.currentTarget.classList.add("active"),document.querySelectorAll(".pay-run-history-tab").forEach(e=>{e.classList.remove("active")});const c=document.getElementById(`pay-run-history-${t}`);c&&c.classList.add("active"),"function"==typeof updatePaginationLinks&&updatePaginationLinks(t);const a=new URL(window.location.href);a.searchParams.set("frequency",t),window.history.replaceState({},"",a),localStorage.setItem("selectedFrequency",t)}document.addEventListener("DOMContentLoaded",function(){void 0===window.originalOpenFrequency&&(window.originalOpenFrequency=window.openFrequency,window.openFrequency=openFrequency)});