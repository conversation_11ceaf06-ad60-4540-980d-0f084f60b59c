class t{constructor(){this.form=document.getElementById("hoursInputForm"),this.normalHoursInputs=document.querySelectorAll('input[name^="hours"][name$="[normal]"]'),this.overtimeInputs=document.querySelectorAll('input[name^="hours"][name$="[overtime]"]'),this.doubleTimeInputs=document.querySelectorAll('input[name^="hours"][name$="[double]"]'),this.totalNormalHours=document.getElementById("totalNormalHours"),this.totalOvertimeHours=document.getElementById("totalOvertimeHours"),this.totalDoubleTimeHours=document.getElementById("totalDoubleTimeHours"),this.csrfToken=document.querySelector('input[name="_csrf"]')?.value,this.VALIDATION={HOURS:{MIN:0,MAX:24,STEP:.5,DECIMALS:1},DAILY_TOTAL:{MAX:24}},this.initializeEventListeners(),this.initializeValidation(),this.updateTotals()}initializeEventListeners(){[...this.normalHoursInputs,...this.overtimeInputs,...this.doubleTimeInputs].forEach(t=>{t.addEventListener("input",()=>{this.validateHourInput(t),this.validateDailyTotal(t),this.updateTotals()}),t.addEventListener("blur",()=>{this.formatHourInput(t)})}),this.form?.addEventListener("submit",t=>this.handleSubmit(t))}initializeValidation(){[...this.normalHoursInputs,...this.overtimeInputs,...this.doubleTimeInputs].forEach(t=>{t.setAttribute("min",this.VALIDATION.HOURS.MIN),t.setAttribute("max",this.VALIDATION.HOURS.MAX),t.setAttribute("step",this.VALIDATION.HOURS.STEP)})}validateHourInput(t){var e=parseFloat(t.value),{MIN:r,MAX:o,STEP:s}=this.VALIDATION.HOURS;return isNaN(e)||e<r||o<e||2*e%1!=0?(this.showError(t,`Hours must be between ${r} and ${o} in increments of `+s),!1):(this.clearError(t),!0)}validateDailyTotal(t){var t=t.closest(".day-row"),e=t.querySelectorAll('input[type="number"]');return Array.from(e).reduce((t,e)=>t+(parseFloat(e.value)||0),0)>this.VALIDATION.DAILY_TOTAL.MAX?(this.showError(t,"Total daily hours cannot exceed "+this.VALIDATION.DAILY_TOTAL.MAX),!1):(this.clearError(t),!0)}formatHourInput(t){var e;t.value&&(e=parseFloat(t.value),isNaN(e)||(t.value=e.toFixed(this.VALIDATION.HOURS.DECIMALS)))}updateTotals(){var t=this.calculateTotal(this.normalHoursInputs),e=this.calculateTotal(this.overtimeInputs),r=this.calculateTotal(this.doubleTimeInputs);this.totalNormalHours.textContent=t.toFixed(1),this.totalOvertimeHours.textContent=e.toFixed(1),this.totalDoubleTimeHours.textContent=r.toFixed(1)}calculateTotal(t){return Array.from(t).reduce((t,e)=>t+(parseFloat(e.value)||0),0)}async handleSubmit(t){if(t.preventDefault(),this.validateForm()){var e=this.form.querySelector('button[type="submit"]');e.disabled=!0,e.innerHTML='<i class="ph ph-spinner ph-spin"></i> Saving...';try{let t=new FormData(this.form),e=await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":this.csrfToken},body:JSON.stringify(Object.fromEntries(t))}),r=await e.json();if(!e.ok)throw new Error(r.message||"Failed to save hours");this.showToast("Hours saved successfully","success"),setTimeout(()=>{window.location.href=r.redirectUrl},1500)}catch(t){this.showToast(t.message||"Failed to save hours","error"),e.disabled=!1,e.innerHTML="Save Hours"}}}validateForm(){let e=!0;return[...this.normalHoursInputs,...this.overtimeInputs,...this.doubleTimeInputs].forEach(t=>{e=this.validateHourInput(t)&&e}),document.querySelectorAll(".day-row").forEach(t=>{t=t.querySelector('input[type="number"]');e=this.validateDailyTotal(t)&&e}),e}showError(t,e){var r=document.createElement("div");r.className="validation-error",r.textContent=e,this.clearError(t),(t.classList?(t.classList.add("error"),t.parentNode):t).appendChild(r)}clearError(t){var e;t.classList?(t.classList.remove("error"),(e=t.parentNode.querySelector(".validation-error"))&&e.remove()):(e=t.querySelector(".validation-error"))&&e.remove()}showToast(t,e="info"){let r=document.createElement("div");r.className="toast-notification "+e,r.innerHTML=`
            <i class="ph ${"success"===e?"ph-check-circle":"ph-warning-circle"}"></i>
            <span>${t}</span>
        `,document.body.appendChild(r),requestAnimationFrame(()=>{r.classList.add("show")}),setTimeout(()=>{r.classList.add("fade-out"),setTimeout(()=>r.remove(),300)},3e3)}}document.addEventListener("DOMContentLoaded",()=>{new t});