document.addEventListener('DOMContentLoaded', function() {
    const dobInput = document.getElementById('dob');

    // Only initialize if this is the add employee form (not edit form)
    // Edit form has its own Flatpickr initialization
    if (dobInput && !dobInput.value) {
        // Calculate default view date (18 years ago)
        const today = new Date();
        const defaultViewDate = new Date(
            today.getFullYear() - 18,
            today.getMonth(),
            today.getDate()
        );

        // Initialize flatpickr
        flatpickr(dobInput, {
            dateFormat: "Y-m-d",
            disableMobile: false,
            defaultDate: null, // Explicitly set to null to prevent any default date
            minDate: undefined, // Remove min date restriction
            maxDate: "today", // Set max date to today for DOB validation
            yearOffset: 18, // Start year selection from 18 years ago
            defaultView: "year"
        });

        // Set the initial view to 18 years ago when the picker opens
        dobInput.addEventListener('click', function() {
            const fp = this._flatpickr;
            if (fp) {
                fp.currentYear = today.getFullYear() - 18;
                fp.redraw();
            }
        });
    }
});
