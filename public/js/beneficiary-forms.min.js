async function deleteBeneficiary(e,t){if(confirm(`Are you sure you want to delete this ${t.replace("-"," ")}?`))try{const t=document.querySelector('meta[name="company-code"]').content,n=document.querySelector('input[name="_csrf"]').value,o=await fetch(`/clients/${t}/settings/beneficiaries-delete/${e}`,{method:"DELETE",headers:{Accept:"application/json","Content-Type":"application/json","CSRF-Token":n},credentials:"same-origin"});if(!o.ok){const e=o.headers.get("content-type");if(e&&e.includes("application/json")){const e=await o.json();throw new Error(e.message||"Failed to delete beneficiary")}throw new Error(`HTTP error! status: ${o.status}`)}const r=await o.json();if(!r.success)throw new Error(r.message||"Failed to delete beneficiary");alert(r.message),window.location.href=`/clients/${t}/settings/accounting/beneficiaries`}catch(e){console.error("Error:",e),alert("Error deleting beneficiary: "+e.message)}}document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll('form[id^="add"][id$="Form"], form[id^="edit"][id$="Form"]').forEach(e=>{e.addEventListener("submit",async function(e){e.preventDefault();try{const e=new FormData(this),t=document.querySelector('input[name="_csrf"]').value,n=this.id.startsWith("edit"),o={};e.forEach((e,t)=>{o[t]=e});const r=await fetch(this.action,{method:n?"PUT":"POST",headers:{Accept:"application/json","Content-Type":"application/json","CSRF-Token":t},credentials:"same-origin",body:JSON.stringify(o)});if(!r.ok){const e=r.headers.get("content-type");if(e&&e.includes("application/json")){const e=await r.json();throw new Error(e.message||"Failed to save beneficiary")}throw new Error(`HTTP error! status: ${r.status}`)}const i=await r.json();if(!i.success)throw new Error(i.message||"Failed to save beneficiary");{alert(i.message);const e=document.querySelector('meta[name="company-code"]').content;window.location.href=`/clients/${e}/settings/accounting/beneficiaries`}}catch(e){console.error("Error:",e),alert("Error saving beneficiary: "+e.message)}})})});