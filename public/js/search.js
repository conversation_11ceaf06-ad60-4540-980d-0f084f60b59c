document.addEventListener("DOMContentLoaded", () => {
  const searchInput = document.getElementById("searchInput");
  const searchIcon = document.getElementById("searchIcon");

  let debounceTimer;

  const performSearch = async (query) => {
    try {
      console.log(`Searching for: ${query}`);
      const response = await fetch(
        `/search-employees?q=${encodeURIComponent(query)}`
      );

      if (!response.ok) {
        console.log(`Response status: ${response.status}`);
        console.log(`Response URL: ${response.url}`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        throw new TypeError("Oops, we haven't got JSON!");
      }

      const results = await response.json();

      if (results.length > 0) {
        displaySearchResults(results);
      } else {
        hideSearchResults();
      }
    } catch (error) {
      console.error("Error searching employees:", error);
      console.error("Error details:", error.message);
      hideSearchResults();
    }
  };

  const displaySearchResults = (results) => {
    let resultsContainer = document.getElementById("searchResults");
    if (!resultsContainer) {
      resultsContainer = document.createElement("div");
      resultsContainer.id = "searchResults";
      resultsContainer.className = "search-results";
      searchInput.parentNode.appendChild(resultsContainer);
    }

    resultsContainer.innerHTML = "";
    results.forEach((employee) => {
      const resultItem = document.createElement("div");
      resultItem.className = "search-result-item";
      resultItem.innerHTML = `
        <span>${employee.firstName} ${employee.lastName}</span>
        <span>${employee.employeeNumber}</span>
        <span>${employee.costCentre}</span>
      `;
      resultItem.style.cursor = "pointer"; // Ensure the cursor is a pointer for clickable items
      resultItem.addEventListener("click", () => {
        window.location.href = `/employeeProfile/${employee._id}`;
      });
      resultsContainer.appendChild(resultItem);
    });

    resultsContainer.style.display = "block";
  };

  const hideSearchResults = () => {
    const resultsContainer = document.getElementById("searchResults");
    if (resultsContainer) {
      resultsContainer.style.display = "none";
    }
  };

  searchInput.addEventListener("input", (e) => {
    clearTimeout(debounceTimer);
    const query = e.target.value.trim();

    if (query.length >= 2) {
      debounceTimer = setTimeout(() => performSearch(query), 300);
    } else {
      hideSearchResults();
    }
  });

  searchIcon.addEventListener("click", () => {
    const query = searchInput.value.trim();
    if (query.length >= 2) {
      performSearch(query);
    }
  });

  // Hide search results when clicking outside
  document.addEventListener("click", (e) => {
    if (!e.target.closest(".search-container")) {
      hideSearchResults();
    }
  });
});
