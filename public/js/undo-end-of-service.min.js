document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("save-button"),t=document.getElementById("no-changes-text"),n=document.querySelectorAll(".undo-select"),o=document.querySelectorAll(".from-date");function a(){let a=!1;n.forEach((e,t)=>{"yes"===e.value&&o[t].value&&(a=!0)}),e.style.display=a?"block":"none",t.style.display=a?"none":"block"}function s(e,t="info"){const n=document.createElement("div");n.className=`alert alert-${t}`,n.textContent=e;const o=document.querySelector(".undo-end-of-service-container");o.insertBefore(n,o.firstChild),setTimeout(()=>{n.remove()},5e3)}n.forEach((e,t)=>{e.addEventListener("change",function(){"no"===this.value?(o[t].value="",o[t].disabled=!0):o[t].disabled=!1,a()})}),o.forEach(e=>{e.addEventListener("change",a)}),e.addEventListener("click",async function(){try{const t=[];if(n.forEach((e,n)=>{if("yes"===e.value&&o[n].value){const a=e.closest("tr");t.push({employeeNumber:a.querySelector("td:nth-child(3)").textContent.trim(),fromDate:o[n].value})}}),0===t.length)return void s("No changes to save","warning");e.disabled=!0,e.textContent="Saving...";const a=await fetch("/api/employees/undo-end-of-service",{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":csrfToken},body:JSON.stringify({updates:t})}),c=await a.json();if(!a.ok)throw new Error(c.message||"Failed to save changes");s("Changes saved successfully","success"),setTimeout(()=>{window.location.reload()},1500)}catch(t){console.error("Error saving changes:",t),s(t.message||"Failed to save changes","error"),e.disabled=!1,e.textContent="Save Changes"}}),n.forEach((e,t)=>{"no"===e.value&&(o[t].disabled=!0)})});