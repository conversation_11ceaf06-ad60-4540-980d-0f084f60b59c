document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("signatureCanvas"),t=new SignaturePad(e,{backgroundColor:"rgb(247, 250, 252)",penColor:"rgb(0, 0, 0)"});function n(){const n=Math.max(window.devicePixelRatio||1,1);e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").scale(n,n),t.clear()}window.addEventListener("resize",n),n(),document.getElementById("clearSignature").addEventListener("click",function(){t.clear(),document.getElementById("signatureData").value=""}),document.querySelector("form").addEventListener("submit",function(e){if(!t.isEmpty()){const e=t.toDataURL();document.getElementById("signatureData").value=e}});const a=document.getElementById("stampUpload"),i=document.getElementById("stampPreview");document.getElementById("triggerStampUpload").addEventListener("click",function(){a.click()}),a.addEventListener("change",function(e){const t=e.target.files[0];if(t){if(t.size>5242880)return void alert("Please select an image smaller than 5MB");const e=new FileReader;e.onload=function(e){i.innerHTML=`<img src="${e.target.result}" alt="Company Stamp" />`},e.readAsDataURL(t)}})});