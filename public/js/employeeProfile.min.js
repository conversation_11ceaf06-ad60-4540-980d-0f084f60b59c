class t{constructor(e,t={}){this.element=e,this.options={placement:t.placement||"top",trigger:t.trigger||"hover"},this.init()}init(){"hover"===this.options.trigger&&(this.element.addEventListener("mouseenter",()=>this.show()),this.element.addEventListener("mouseleave",()=>this.hide()))}show(){}hide(){}}function d(){showPage(1),updatePageIndicator()}function o(e){let t=document.createElement("div");t.className="error-message",t.innerHTML=`
    <i class="ph ph-warning"></i>
    <span>${e}</span>
  `,document.body.appendChild(t),setTimeout(()=>{t.classList.add("fade-out"),setTimeout(()=>t.remove(),300)},3e3)}function e(e,t){e&&(e.className="employee-status "+({Active:"active",Inactive:"inactive",Suspended:"suspended",Terminated:"terminated"}[t]||""))}function i(e,t){return!t||new Date(e)<=new Date(t)}window.currentPage=1,window.totalPages=4,window.updatePageIndicator=function(){var e=document.querySelector(".page-indicator");e&&(e.textContent=currentPage+" of "+totalPages)},window.showPage=function(e){document.querySelectorAll(".calc-page").forEach(e=>{e.style.display="none"});var t=document.querySelector(`.calc-page[data-page="${e}"]`),t=(t&&(t.style.display="block",t.style.animation="fadeIn 0.3s ease-out"),document.querySelector("#prevPageBtn")),n=document.querySelector("#nextPageBtn");t&&(t.disabled=1===e),n&&(n.disabled=e===totalPages)},window.prevPage=function(){1<currentPage&&(currentPage--,showPage(currentPage),updatePageIndicator())},window.nextPage=function(){currentPage<totalPages&&(currentPage++,showPage(currentPage),updatePageIndicator())},document.addEventListener("DOMContentLoaded",function(){d();let n=document.getElementById("monthModal"),o=document.getElementById("selectMonthButton");if(n&&o){let e=n.querySelector(".close"),t=document.getElementById("periodsList");o.addEventListener("click",function(){n.style.display="block"}),e&&e.addEventListener("click",function(){n.style.display="none"}),t&&t.addEventListener("click",function(e){e.target&&e.target.matches("li.period-item")&&(e.target.dataset.periodStart,e.target.dataset.periodEnd,o.dataset.employeeId)})}var e=document.getElementById("updateSalaryForm");function a(){return document.querySelector('meta[name="csrf-token"]')?.content}e&&e.addEventListener("submit",async function(e){e.preventDefault(),setTimeout(()=>{var e=new URL(window.location);e.searchParams.set("_refresh",Date.now()),window.location.href=e.toString()},1e3)});e=document.getElementById("deleteEmployeeBtn");if(e){let n=e.dataset.employeeId,o=document.body.dataset.companyCode;e.addEventListener("click",async function(e){if(e.preventDefault(),confirm("Are you sure you want to delete this employee? This action cannot be undone."))try{let e=await fetch(`/clients/${o}/employeeProfile/${n}/delete`,{method:"DELETE",headers:{"Content-Type":"application/json","CSRF-Token":a()}}),t=await e.json();t.success?(alert("Employee deleted successfully"),window.location.href=t.redirectUrl):alert("Failed to delete employee: "+t.message)}catch(e){alert("An error occurred while deleting the employee")}})}let i=a();if(i){let n=window.fetch;window.fetch=function(){var[e,t]=arguments;return void 0===(t=void 0===t?{}:t).headers&&(t.headers={}),t.headers instanceof Headers||(t.headers=new Headers(t.headers)),t.headers.set("CSRF-Token",i),n(e,t)}}document.querySelectorAll(".period-item").forEach(e=>{e.addEventListener("mouseenter",function(){this.style.transform="translateX(5px)"}),e.addEventListener("mouseleave",function(){this.style.transform="translateX(0)"})}),document.querySelectorAll("[title]").forEach(e=>{new t(e,{placement:"top",trigger:"hover"})});e=document.createElement("style");e.textContent="\n    .period-item.disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n      background-color: #f5f5f5;\n    }\n    \n    .period-status.terminated {\n      color: #dc3545;\n      font-weight: bold;\n    }\n    \n    .period-item.disabled:hover {\n      transform: none !important;\n    }\n    \n    .error-message {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background-color: #dc3545;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 4px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      z-index: 1000;\n      animation: slideIn 0.3s ease-out;\n    }\n    \n    .error-message.fade-out {\n      animation: fadeOut 0.3s ease-out;\n    }\n    \n    @keyframes slideIn {\n      from { transform: translateX(100%); opacity: 0; }\n      to { transform: translateX(0); opacity: 1; }\n    }\n    \n    @keyframes fadeOut {\n      from { opacity: 1; }\n      to { opacity: 0; }\n    }\n  ",document.head.appendChild(e)});let a=document.getElementById("monthModal");if(a){a.querySelector(".close");let e=a.querySelector("#periodsList"),n=document.body.dataset.terminationDate;e.addEventListener("click",function(e){var t=e.target.closest(".period-item");return!t||i(t.dataset.period,n)?void 0:(e.preventDefault(),void o("Cannot select payroll period beyond termination date"))})}function e(e,t){switch(e.classList.remove("status-active","status-inactive","status-notice"),t.toLowerCase()){case"active":e.classList.add("status-active");break;case"inactive":e.classList.add("status-inactive");break;case"serving notice":e.classList.add("status-notice")}}function n(e){document.querySelectorAll(".tab-button").forEach(e=>{e.classList.remove("active")}),event.currentTarget.classList.add("active")}function r(e){e.stopPropagation();let t=document.getElementById("dropdownContent"),n=e.currentTarget;document.querySelectorAll(".dropdown-content").forEach(e=>{e!==t&&e.classList.remove("show")}),t.classList.toggle("show"),n.classList.toggle("active"),t.classList.contains("show")&&document.addEventListener("click",s)}function s(e){if(!e.target.matches(".dropdown-content")&&!e.target.matches(".tab-button")){let e=document.getElementsByClassName("dropdown-content"),t=document.querySelectorAll(".dropdown .tab-button");Array.from(e).forEach(e=>{e.classList.remove("show")}),t.forEach(e=>{e.classList.remove("active")}),document.removeEventListener("click",s)}}function c(){let e=document.createElement("div");e.className="toast-notification",e.innerHTML='\n    <i class="ph ph-info"></i>\n    <span>Leave management coming soon!</span>\n  ',document.body.appendChild(e),setTimeout(()=>{e.classList.add("fade-out"),setTimeout(()=>e.remove(),300)},3e3)}function l(e){var t=document.createElement("div");t.className="confirmation-modal",t.innerHTML=`
    <div class="modal-content">
      <i class="ph ph-warning-circle"></i>
      <h3>Delete Employee</h3>
      <p>Are you sure you want to delete this employee? This action cannot be undone.</p>
      <div class="modal-actions">
        <button onclick="closeModal()" class="btn-secondary">Cancel</button>
        <button onclick="deleteEmployee('${e}')" class="btn-danger">Delete</button>
      </div>
    </div>
  `,document.body.appendChild(t)}function m(){document.getElementById("payslipMonths").value}function u(){}let p=document.getElementById("loadPayslipButtonId"),y=(p&&p.addEventListener("click",m),document.getElementById("finalisePayrollButtonId")),f=(y&&y.addEventListener("click",u),document.getElementById("selectMonthButton")),g=document.getElementById("monthModal")?.querySelector(".close"),h=document.getElementById("periodsList"),v=(f.addEventListener("click",function(){document.getElementById("monthModal").style.display="block"}),g&&g.addEventListener("click",function(){document.getElementById("monthModal").style.display="none"}),h&&h.addEventListener("click",function(e){var t,n;e.target&&e.target.matches("li.period-item")&&(t=e.target.dataset.periodStart,e=e.target.dataset.periodEnd,n=f.dataset.employeeId,f.textContent=moment(t).format("DD/MM/YYYY")+" - "+moment(e).format("DD/MM/YYYY"),document.getElementById("selectedMonth").value=moment(e).format("YYYY-MM-DD"),document.getElementById("monthModal").style.display="none",window.location.href=`/employeeProfile/${n}?month=`+moment(e).format("YYYY-MM-DD"))}),document.querySelector("#prevPageBtn")),w=document.querySelector("#nextPageBtn"),E=(v&&v.addEventListener("click",window.prevPage),w&&w.addEventListener("click",window.nextPage),document.querySelector(".employee-status"));E&&e(E,E.dataset.status),document.addEventListener("DOMContentLoaded",function(){let e=document.getElementById("periodsList"),t=(document.getElementById("selectMonthButton"),document.getElementById("finaliseForm")),n=document.body.dataset.terminationDate;function o(e){let t=document.createElement("div");t.className="error-message",t.innerHTML=`
      <i class="ph ph-warning"></i>
      <span>${e}</span>
    `,document.body.appendChild(t),setTimeout(()=>{t.classList.add("fade-out"),setTimeout(()=>t.remove(),300)},3e3)}e&&(e.querySelectorAll(".period-item").forEach(e=>{!i(e.dataset.period,n)&&(e.classList.add("disabled"),e.title="Period is beyond termination date",e=e.querySelector(".period-status"))&&(e.textContent="Beyond Termination",e.classList.add("terminated"))}),e.addEventListener("click",function(e){var t=e.target.closest(".period-item");return!t||t.classList.contains("disabled")?(e.preventDefault(),void(t?.classList.contains("disabled")&&o("Cannot select payroll period beyond termination date"))):i(t.dataset.period,n)?void 0:(e.preventDefault(),void o("Cannot select payroll period beyond termination date"))})),t&&t.addEventListener("submit",function(e){if(!i(this.querySelector('[name="currentPeriodEndDate"]').value,n))return e.preventDefault(),o("Cannot process payroll beyond termination date"),!1});var a=document.createElement("style");a.textContent="\n    .period-item.disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n      background-color: #f5f5f5;\n    }\n    \n    .period-status.terminated {\n      color: #dc3545;\n      font-weight: bold;\n    }\n    \n    .period-item.disabled:hover {\n      transform: none !important;\n    }\n    \n    .error-message {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background-color: #dc3545;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 4px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      z-index: 1000;\n      animation: slideIn 0.3s ease-out;\n    }\n    \n    .error-message.fade-out {\n      animation: fadeOut 0.3s ease-out;\n    }\n    \n    @keyframes slideIn {\n      from { transform: translateX(100%); opacity: 0; }\n      to { transform: translateX(0); opacity: 1; }\n    }\n    \n    @keyframes fadeOut {\n      from { opacity: 1; }\n      to { opacity: 0; }\n    }\n  ",document.head.appendChild(a)}),window.showTab=function(e){},window.toggleDropdown=function(){var e=document.getElementById("dropdownContent");e&&e.classList.toggle("show")},window.confirmDownloadPayslip=function(o,a,e){if(e||confirm("This period is not finalized. The payslip will be marked as DRAFT. Do you want to continue?"))try{let e=document.body.dataset.companyCode,t,n;e&&(t=`/clients/${e}/payslip/download/${o}/`+new Date(a).toISOString().split("T")[0],(n=document.createElement("a")).href=t,n.target="_blank",n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n))}catch(o){alert("Error downloading payslip. Please try again.")}};