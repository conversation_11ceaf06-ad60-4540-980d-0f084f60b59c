let currentFilingSeason,companyCode,filingSeasons=[];function initializeFilingSeasons(){const e=new Date,t=e.getFullYear(),n=e.getMonth()>=2?t:t-1,o=n+1;filingSeasons=[{value:`${n}-08-31`,label:`August ${n} (March to August)`},{value:`${o}-02-${o%4==0?"29":"28"}`,label:`February ${o} (September to February)`}],currentFilingSeason=filingSeasons[0].value,updateFilingSeasonDisplay()}function initializeOIDYears(){const e=(new Date).getFullYear(),t=document.getElementById("oidYear");for(let n=e-5;n<=e+2;n++){const e=document.createElement("option");e.value=n,e.textContent=n,t.appendChild(e)}t.value=e}function updateFilingSeasonDisplay(){const e=document.getElementById("filingSeason");e&&(e.innerHTML=filingSeasons.map(e=>`<option value="${e.value}">${e.label}</option>`).join(""),e.value=currentFilingSeason,loadBiAnnualFilingData())}function changeFilingSeason(){const e=document.getElementById("filingSeason");currentFilingSeason=e.value,loadBiAnnualFilingData(),loadCurrentOIDReturn()}function loadBiAnnualFilingData(){const e=document.getElementById("filingSeason");if(!e)return;const t=e.value;t&&fetch(`/clients/${companyCode}/filing/bi-annual/${t}`).then(e=>e.json()).then(e=>{const n=document.querySelector("#biAnnualFilingTable tbody");n.innerHTML="",e.employees.forEach(o=>{n.insertRow().innerHTML=`\n          <td>${o.firstName}</td>\n          <td>${o.lastName}</td>\n          <td>${o.employeeNumber}</td>\n          <td>${e.dateRange}</td>\n          <td>${e.periodType}</td>\n          <td>${o.certificateType}</td>\n          <td>R ${o.totalEarnings.toFixed(2)}</td>\n          <td>R ${o.totalPAYE.toFixed(2)}</td>\n          <td>R ${o.totalUIF.toFixed(2)}</td>\n          <td>R ${o.totalSDL.toFixed(2)}</td>\n          <td><button onclick="viewCertificate('${o.id}', '${t}')">View</button></td>\n        `}),document.getElementById("certificateCount").textContent=e.employees.length}).catch(e=>{console.error("Error loading bi-annual filing data:",e)})}function preValidateData(){const e=document.getElementById("filingSeason").value;if(!companyCode||!e)return console.error("Invalid companyCode or filingSeason"),void alert("Error: Missing company code or filing season");console.log(`Pre-validating data for company ${companyCode}, season ${e}`),fetch(`/clients/${companyCode}/filing/bi-annual/${e}/pre-validate`).then(e=>e.ok?e.json():e.text().then(t=>{throw new Error(`Network response was not ok: ${e.status} ${e.statusText}\n${t}`)})).then(e=>{let t="";e.errors.length>0&&(t+="Errors:\n"+e.errors.join("\n")+"\n\n"),e.warnings.length>0&&(t+="Warnings:\n"+e.warnings.join("\n")),""===t&&(t="No issues found. Your data is ready for submission."),alert(t)}).catch(e=>{console.error("Error pre-validating data:",e),alert("An error occurred while pre-validating the data: "+e.message)})}function formatDate(e){return new Date(e).toLocaleDateString("en-ZA",{day:"2-digit",month:"short",year:"numeric"})}function viewCertificate(e,t){window.open(`/clients/${companyCode}/filing/view-certificate/${e}/${t}`,"_blank")}function viewAllCertificates(){const e=document.getElementById("filingSeason").value;window.open(`/clients/${companyCode}/filing/view-all-certificates/${e}`,"_blank")}function exportToEasyfile(){const e=document.getElementById("filingSeason").value;window.location.href=`/clients/${companyCode}/filing/export-easyfile/${e}`}function generateEMP501(){const e=document.getElementById("filingSeason").value;window.location.href=`/clients/${companyCode}/filing/generate-emp501/${e}`}async function loadOIDReturn(){const e=document.getElementById("oidYear").value;try{const t=await fetch(`/filing/oid/current?year=${e}`);updateOIDUI(await t.json())}catch(e){console.error("Error loading OID return:",e)}}async function loadCurrentOIDReturn(){const e=(new Date).getFullYear();document.getElementById("oidYear").value=e,await loadOIDReturn()}function updateOIDUI(e){document.getElementById("oidYear").value=e.year,document.getElementById("totalEarnings").textContent=e.totalEarnings.toFixed(2),document.getElementById("numberOfEmployees").textContent=e.numberOfEmployees,document.getElementById("oidStatus").textContent=e.status,document.getElementById("annualThreshold").textContent=e.annualThreshold.toFixed(2);const t=document.getElementById("generateOIDBtn"),n=document.getElementById("submitOIDBtn"),o=document.getElementById("downloadOIDBtn");t.disabled="Submitted"===e.status,n.disabled="Submitted"===e.status,o.disabled="Draft"===e.status,checkSubmissionPeriod()}async function generateOIDReturn(){const e=document.getElementById("oidYear").value;fetch("/filing/oid/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({year:e})}).then(e=>e.json()).then(e=>{updateOIDUI(e),alert("OID Return generated successfully")}).catch(e=>{console.error("Error generating OID return:",e),alert("Failed to generate OID Return")})}async function submitOIDReturn(){const e=document.getElementById("oidYear").value;try{const t=await fetch("/filing/oid/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({year:e})}),n=await t.json();updateOIDUI(n),n.isLateSubmission?alert(`OID Return submitted successfully, but a late submission penalty of 10% (R${n.penaltyAmount.toFixed(2)}) has been applied. Interest may also be charged on the overdue amount.`):alert("OID Return submitted successfully")}catch(e){console.error("Error submitting OID return:",e),alert("Failed to submit OID Return")}}async function checkSubmissionPeriod(){try{const e=await fetch("/filing/oid/check-submission-period"),{isWithinSubmissionPeriod:t}=await e.json(),n=document.getElementById("submitOIDBtn");n.disabled=!t||n.disabled,t||alert("The submission period for OID returns is from April 1st to June 30th. You can still prepare the return, but submission is only allowed during this period.")}catch(e){console.error("Error checking submission period:",e)}}document.addEventListener("DOMContentLoaded",()=>{if(companyCode=document.querySelector('meta[name="company-code"]')?.getAttribute("content"),!companyCode)return console.error("Company code not found"),void showToast("error","Error: Company code not found. Please refresh the page or contact support.");console.log("Company code:",companyCode),document.getElementById("filingSeason")&&(initializeFilingSeasons(),loadBiAnnualFilingData()),document.getElementById("oidYear")&&(initializeOIDYears(),loadCurrentOIDReturn());const e=document.getElementById("statusFilter");e&&e.addEventListener("change",filterSubmissions)});const oidReturnTab=document.getElementById("oidReturnTab");async function downloadPDF(e){try{const t=document.querySelector('meta[name="company-code"]').content,n=await fetch(`/clients/${t}/filing/download-pdf/${e}`);if(!n.ok)throw new Error("Failed to download PDF");const o=await n.blob(),s=window.URL.createObjectURL(o),a=document.createElement("a");a.href=s,a.download=`EMP201_${e}.pdf`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(s),document.body.removeChild(a),showMessage("success","PDF downloaded successfully")}catch(e){console.error("Error downloading PDF:",e),showMessage("error","An error occurred while downloading the PDF")}}function viewFiling(e){console.log("Viewing filing:",e)}function editFiling(e){console.log("Editing filing:",e)}function submitFiling(e){console.log("Submitting filing:",e)}function toggleContent(e){const t=document.getElementById(`content-${e}`),n=t.closest(".filing-card").querySelector(".toggle-icon");document.querySelectorAll(".filing-card__content").forEach(t=>{t.id!==`content-${e}`&&t.classList.contains("active")&&(t.classList.remove("active"),t.closest(".filing-card").querySelector(".toggle-icon").classList.remove("active"))}),t.classList.toggle("active"),n.classList.toggle("active")}function showTab(e){document.querySelectorAll(".tab-content").forEach(e=>e.style.display="none"),document.getElementById(e).style.display="block"}async function finaliseMonthForFiling(e){try{const t=await fetch(`/filing/finalise/${e}`,{method:"POST"}),n=await t.json();n.success?(showMessage("success",n.message),updateMonthlySubmissionUI(e,n.filing)):showMessage("info",n.message)}catch(e){console.error("Error finalizing month:",e),showMessage("error","An error occurred while finalizing the month")}}function updateMonthlySubmissionUI(e,t){const n=document.querySelector(`#content-${e}`);if(n){const e=n.querySelector("tbody"),o=document.createElement("tr");o.innerHTML=`\n      <td>${new Date(t.createdAt).toLocaleDateString()}</td>\n      <td>${t.status}</td>\n      <td>${t.totalEmployeeCount}</td>\n      <td>${t.finalizedCount}</td>\n      <td>${t.unfinalizedCount}</td>\n      <td>R ${t.totalPAYE.toFixed(2)}</td>\n      <td>R ${t.totalUIF.toFixed(2)}</td>\n      <td>R ${t.totalSDL.toFixed(2)}</td>\n      <td>\n        <button class="action-btn view-submissions-btn" onclick="viewWebSubmission('${t._id}')">\n          View\n        </button>\n        <button class="action-btn download-pdf-btn" onclick="downloadPDF('${t._id}')">\n          Download PDF\n        </button>\n      </td>\n    `,e.appendChild(o);const s=n.querySelector(".finalise-btn");s&&(s.textContent="Submit Supplementary")}}async function viewWebSubmission(e){try{const t=await fetch(`/filing/web-submission/${e}`);if(!t.ok)throw new Error("Failed to fetch web submission");showEMP201Modal(await t.json())}catch(e){console.error("Error viewing web submission:",e),showMessage("error","An error occurred while loading the web submission")}}function showEMP201Modal(e){const t=document.createElement("div");t.className="modal",t.innerHTML=`\n    <div class="modal-content">\n      <span class="close">&times;</span>\n      <h2>EMP201 Submission</h2>\n      <p><strong>Company:</strong> ${e.companyName}</p>\n      <p><strong>PAYE Reference:</strong> ${e.payeReference}</p>\n      <p><strong>Month:</strong> ${e.month}</p>\n      <p><strong>Total PAYE:</strong> R ${e.totalPAYE.toFixed(2)}</p>\n      <p><strong>Total UIF:</strong> R ${e.totalUIF.toFixed(2)}</p>\n      <p><strong>Total SDL:</strong> R ${e.totalSDL.toFixed(2)}</p>\n      <p><strong>Total Payable:</strong> R ${e.totalPayable.toFixed(2)}</p>\n    </div>\n  `,document.body.appendChild(t),t.querySelector(".close").onclick=function(){document.body.removeChild(t)},window.onclick=function(e){e.target==t&&document.body.removeChild(t)}}async function downloadEMP201(e,t,n,o){try{e.preventDefault();const t=e.currentTarget,s=t.innerHTML;t.innerHTML='<i class="ph ph-spinner ph-spin"></i> Downloading...',t.disabled=!0;const a=n.split("T")[0],i=await fetch(`/clients/${o}/filing/download-pdf/${moment(a).format("YYYY-MM")}`,{method:"GET",headers:{Accept:"application/pdf"}});if(!i.ok){const e=await i.text();throw new Error(e||"Failed to download PDF")}const l=await i.blob(),r=window.URL.createObjectURL(l),c=document.createElement("a");c.href=r,c.download=`EMP201_${moment(a).format("YYYY-MM")}.pdf`,document.body.appendChild(c),c.click(),window.URL.revokeObjectURL(r),document.body.removeChild(c),t.innerHTML=s,t.disabled=!1}catch(t){console.error("Error downloading EMP201:",t),showToast("error",`Failed to download EMP201: ${t.message}`);const n=e.currentTarget;n.innerHTML='<i class="ph ph-file-pdf"></i> Download EMP201',n.disabled=!1}}function switchTab(e){document.querySelectorAll(".tab-content").forEach(e=>{e.classList.remove("active")}),document.querySelectorAll(".tab-button").forEach(e=>{e.classList.remove("active")}),document.getElementById(e).classList.add("active"),document.querySelector(`[data-tab="${e}"]`).classList.add("active")}function prepareEMP201(e,t){fetch(`/clients/${e}/prepare-emp201/${t}`,{headers:{Accept:"application/json"}}).then(e=>e.json().catch(()=>({success:!1,message:"Invalid server response"}))).then(n=>{n.success?window.location.href=`/clients/${e}/prepare-emp201/${t}`:showToast("error",n.message)}).catch(e=>{showToast("error",e.message||"Error preparing EMP201"),console.error("Error preparing EMP201:",e)})}function showToast(e,t){console.log("Showing toast:",{type:e,message:t}),document.querySelectorAll(".toast").forEach(e=>e.remove());const n=document.createElement("div");n.className=`toast toast-${e}`,n.innerHTML=`\n    <div class="toast-content">\n      <i class="ph ${"error"===e?"ph-x-circle":"ph-check-circle"}"></i>\n      <span>${t}</span>\n    </div>\n  `,document.body.appendChild(n),console.log("Toast added to DOM"),n.offsetHeight,requestAnimationFrame(()=>{n.classList.add("show"),console.log("Show class added")}),setTimeout(()=>{console.log("Starting toast removal"),n.classList.remove("show"),setTimeout(()=>{n.remove(),console.log("Toast removed")},300)},5e3)}function toggleContent(e){const t=document.getElementById(`content-${e}`),n=t.closest(".filing-card").querySelector(".toggle-icon");document.querySelectorAll(".filing-card__content").forEach(t=>{t.id!==`content-${e}`&&t.classList.contains("active")&&(t.classList.remove("active"),t.closest(".filing-card").querySelector(".toggle-icon").classList.remove("active"))}),t.classList.toggle("active"),n.classList.toggle("active")}function validateEMP201Submission(e){const t=[];e.unfinalizedEmployees>0&&t.push("All payrolls must be finalized before EMP201 submission");const n=new Date(e.submissionDeadline);return new Date>n&&t.push("Submission is past due date. Late submission penalties may apply."),12*e.totalPayroll>5e5&&0===e.totalSDL&&t.push("SDL is required for annual payroll exceeding R500,000"),e.payrolls.forEach(e=>{e.uif>177.12&&t.push(`UIF exceeds maximum threshold for employee ${e.employeeNumber}`)}),t}async function submitEMP201(e){try{const t=await validateEMP201Submission(e);if(t.length>0)return void showToast("error","Validation Errors:\n"+t.join("\n"));const n=document.querySelector('meta[name="company-code"]')?.getAttribute("content");if(!n)return void showToast("error","Company code not found");const o=await fetch(`/clients/${n}/submit-emp201/${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({totalEmployees:document.querySelector(`#content-${e} .stat-value`)?.textContent||0})});if(!o.ok)throw new Error("Submission failed");const s=await o.json();if(s.success){showToast("success","EMP201 submitted successfully");const t=document.querySelector(`#content-${e}`);if(t){const e=t.querySelector(".filing-stats");e&&(e.innerHTML=`\n            <div class="stat-item">\n              <span class="stat-label">Total Employees</span>\n              <span class="stat-value">${s.filing.totalEmployees}</span>\n            </div>\n            <div class="stat-item">\n              <span class="stat-label">Finalized</span>\n              <span class="stat-value">${s.filing.totalEmployees}</span>\n            </div>\n            <div class="stat-item">\n              <span class="stat-label">Completion</span>\n              <span class="stat-value">100%</span>\n            </div>\n          `)}await refreshFilingData()}}catch(e){console.error("EMP201 submission error:",e),showToast("error","Failed to submit EMP201: "+e.message)}}async function refreshFilingData(){try{const e=document.querySelector('meta[name="company-code"]')?.getAttribute("content");if(!e)return void console.error("Company code not found");const t=await fetch(`/clients/${e}/filing/data`);if(!t.ok)throw new Error("Failed to fetch filing data");const n=await t.json();if(!n.success)throw new Error(n.message);n.months.forEach(t=>{const n=document.querySelector(`#content-${t._id}`);if(!n)return;const o=n.querySelector(".filing-stats");o&&(o.innerHTML=`\n          <div class="stat-item">\n            <span class="stat-label">Total Employees</span>\n            <span class="stat-value">${t.totalEmployees}</span>\n          </div>\n          <div class="stat-item">\n            <span class="stat-label">Finalized</span>\n            <span class="stat-value">${t.finalizedEmployees}</span>\n          </div>\n          <div class="stat-item">\n            <span class="stat-label">Completion</span>\n            <span class="stat-value">\n              ${(t.finalizedEmployees/t.totalEmployees*100).toFixed(1)}%\n            </span>\n          </div>\n        `);const s=n.parentElement.querySelector(".status-badge");if(s){const e=t.submissions&&t.submissions.length?"Submitted":t.status;s.className=`status-badge ${helpers.getStatusClass(e)}`,s.textContent=e}if(t.submissions&&t.submissions.length){const o=n.querySelector(".download-actions")||document.createElement("div");o.className="download-actions",o.innerHTML=`\n          <button\n            class="action-button secondary download-emp201"\n            onclick="downloadEMP201(event, '${t._id}', '${t.month}', '${e}')"\n          >\n            <i class="ph ph-file-pdf"></i>\n            Download EMP201\n          </button>\n        `,n.querySelector(".download-actions")||n.insertBefore(o,n.querySelector(".card-actions"))}const a=n.querySelector(".card-actions");a&&t.submissions&&t.submissions.length&&(a.innerHTML="")}),filterSubmissions(),showToast("success","Filing data refreshed successfully")}catch(e){console.error("Error refreshing filing data:",e),showToast("error","Failed to refresh filing data")}}function filterSubmissions(){const e=document.getElementById("statusFilter").value;document.querySelectorAll(".filing-card").forEach(t=>{const n=t.querySelector(".status-badge"),o=n.textContent.trim().toLowerCase(),s=n.closest(".deadline-info")?.querySelector("span")?.classList.contains("overdue")||!1;switch(e){case"all":default:t.style.display="block";break;case"pending":t.style.display="draft"!==o&&"pending"!==o||s?"none":"block";break;case"submitted":t.style.display="submitted"===o?"block":"none";break;case"overdue":t.style.display=s?"block":"none"}});const t=document.querySelectorAll('.filing-card[style="display: block"]'),n=document.querySelector(".no-results-message")||createNoResultsMessage();0===t.length?n.style.display="block":n.style.display="none"}function createNoResultsMessage(){const e=document.createElement("div");return e.className="no-results-message",e.innerHTML='\n    <div class="empty-state">\n      <i class="ph ph-file-x"></i>\n      <p>No submissions found matching the selected filter.</p>\n    </div>\n  ',document.querySelector(".filing-grid").appendChild(e),e}oidReturnTab&&oidReturnTab.addEventListener("click",checkSubmissionPeriod);