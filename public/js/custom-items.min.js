class CustomItemManager{constructor(){this.form=document.getElementById("customItemForm"),this.calculationTypeSelect=document.getElementById("calculationType"),this.percentageBaseField=document.getElementById("percentageBaseField"),this.valueField=document.getElementById("valueField"),this.formulaField=document.getElementById("formulaField"),this.frequencySelect=document.getElementById("frequency"),this.monthsField=document.getElementById("monthsField"),this.valueLabel=document.querySelector('label[for="value"]'),this.valueInput=document.getElementById("value"),this.setupEventListeners()}setupEventListeners(){this.calculationTypeSelect&&this.calculationTypeSelect.addEventListener("change",()=>{this.handleCalculationTypeChange()}),this.frequencySelect&&this.frequencySelect.addEventListener("change",()=>{this.handleFrequencyChange()}),this.form&&this.form.addEventListener("submit",e=>this.handleSubmit(e))}handleCalculationTypeChange(){const e=this.calculationTypeSelect.value;switch(this.percentageBaseField.style.display="none",this.formulaField.style.display="none",this.valueField.style.display="none",e){case"fixed":this.valueField.style.display="block",this.valueLabel.textContent="Fixed Amount *",this.valueInput.step="0.01",this.valueInput.min="0";break;case"percentage":this.percentageBaseField.style.display="block",this.valueField.style.display="block",this.valueLabel.textContent="Percentage Value *",this.valueInput.step="0.01",this.valueInput.min="0",this.valueInput.max="100";break;case"formula":this.formulaField.style.display="block",this.valueField.style.display="none"}}handleFrequencyChange(){const e=this.frequencySelect.value;this.monthsField.style.display="specific"===e?"block":"none"}validateForm(){const e=this.calculationTypeSelect.value;if(!e)throw new Error("Please select a calculation type");if("formula"===e){if(!document.getElementById("formula").value.trim())throw new Error("Please enter a formula")}else{if(!this.valueInput.value)throw new Error("Please enter a value");if("percentage"===e&&!document.getElementById("percentageBase").value)throw new Error("Please select a percentage base")}return!0}async handleSubmit(e){e.preventDefault();try{this.validateForm();const e=new FormData(this.form),t={};if(e.forEach((e,a)=>{t[a]="taxable"===a||"requiresTaxDirective"===a?"on"===e:"value"===a||"processingOrder"===a?Number(e):e}),"specific"===this.frequencySelect.value){const e=Array.from(document.querySelectorAll('input[name="months"]:checked')).map(e=>Number(e.value));t.months=e}const a=await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('input[name="_csrf"]').value},body:JSON.stringify(t)}),n=await a.json();if(!n.success)throw new Error(n.message||"Failed to save custom item");{alert(n.message);const e=document.querySelector('meta[name="company-code"]').content;window.location.href=`/clients/${e}/settings/accounting/custom-items`}}catch(e){console.error("Error:",e),alert("Error saving custom item: "+e.message)}}}document.addEventListener("DOMContentLoaded",()=>{new CustomItemManager});