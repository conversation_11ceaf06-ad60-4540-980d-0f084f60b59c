class e{constructor(e,t={}){this.form=document.getElementById(e),this.options={previewContainerId:"rfiPreview",...t},this.previewContainer=document.getElementById(this.options.previewContainerId),this.initializeEventListeners(),this.initializeCalculator(),this.form&&this.form.addEventListener("submit",this.handleSubmit.bind(this))}initializeEventListeners(){let e=document.getElementById("contributionCalculation");e&&e.addEventListener("change",()=>{this.handleContributionMethodChange(e.value)})}handleContributionMethodChange(e){var t=document.getElementById("fixedAmountCard"),n=document.getElementById("rfiPercentageCard");t&&(t.style.display="fixedAmount"===e?"block":"none"),n&&(n.style.display="percentageRFI"===e?"block":"none"),"percentageRFI"===e&&this.initializeCalculator()}async handleSubmit(e){e.preventDefault();try{let e=new FormData(this.form),t=document.querySelector('meta[name="csrf-token"]').content,n=await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":t},body:JSON.stringify(Object.fromEntries(e))});if(!n.ok)throw new Error("Network response was not ok");var i=await n.json();if(!i.success)throw new Error(i.message||"Failed to save provident fund details");{let e=window.location.pathname,[,,t,,n]=e.split("/"),i=`/clients/${t}/employeeProfile/`+n;window.location.href=i}}catch(e){alert("Error saving provident fund details. Please try again.")}}initializeCalculator(){}}document.addEventListener("DOMContentLoaded",()=>{new e("providentFundForm",{previewContainerId:"rfiPreview"})});