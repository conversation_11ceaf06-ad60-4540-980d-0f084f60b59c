document.getElementById("employerDetailsForm").addEventListener("submit",async e=>{e.preventDefault();var a=new FormData(e.target);try{let e=await fetch("/settings/save-employer-details",{method:"POST",body:a}),t=await e.json();t.success?alert("Employer details saved successfully"):alert(`Failed to save employer details: ${t.message}

Error details: `+t.error)}catch(e){alert("Error saving employer details: "+e.message)}}),document.addEventListener("DOMContentLoaded",function(){function t(t){document.querySelectorAll(".tab-content").forEach(e=>{e.style.display="none"});var e=document.getElementById(t);e&&(e.style.display="block"),document.querySelectorAll(".tab-button").forEach(e=>{e.classList.remove("active"),e.getAttribute("onclick")===`showTab('${t}')`&&e.classList.add("active")})}document.querySelectorAll(".tab-button").forEach(e=>{e.addEventListener("click",function(){t(this.getAttribute("onclick").match(/'(.*?)'/)[1])})}),t("employerDetails")}),window.showTab=showTab;