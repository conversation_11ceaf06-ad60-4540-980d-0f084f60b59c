class e{constructor(){this.imageObserver=null,this.componentObserver=null,this.init()}init(){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>this.setup()):this.setup()}setup(){this.setupImageLazyLoading(),this.setupComponentLazyLoading(),this.setupContentLazyLoading()}setupImageLazyLoading(){"loading"in HTMLImageElement.prototype?this.enableNativeLazyLoading():this.enableIntersectionObserverLazyLoading()}enableNativeLazyLoading(){document.querySelectorAll("img[data-lazy]").forEach(e=>{e.loading="lazy",e.src=e.dataset.lazy,e.removeAttribute("data-lazy")})}enableIntersectionObserverLazyLoading(){"IntersectionObserver"in window?(this.imageObserver=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&(e=e.target,this.loadImage(e),this.imageObserver.unobserve(e))})},{rootMargin:"50px 0px",threshold:.01}),document.querySelectorAll("img[data-lazy]").forEach(e=>this.imageObserver.observe(e))):this.loadAllImages()}loadImage(e){let a=e.dataset.lazy,t;a&&((t=new Image).onload=()=>{e.src=a,e.classList.add("loaded"),e.removeAttribute("data-lazy")},t.onerror=()=>{e.classList.add("error")},t.src=a)}loadAllImages(){document.querySelectorAll("img[data-lazy]").forEach(e=>this.loadImage(e))}setupComponentLazyLoading(){"IntersectionObserver"in window&&(this.componentObserver=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&(e=e.target,this.loadComponent(e),this.componentObserver.unobserve(e))})},{rootMargin:"100px 0px",threshold:.1}),document.querySelectorAll("[data-lazy-component]").forEach(e=>this.componentObserver.observe(e)))}async loadComponent(a){var e=a.dataset.lazyComponent,t=a.dataset.componentData;try{switch(a.classList.add("loading"),e){case"chart":await this.loadChart(a,t);break;case"table":await this.loadTable(a,t);break;case"calendar":await this.loadCalendar(a,t);break;case"form":await this.loadForm(a,t);break;default:await this.loadGenericComponent(a,t)}a.classList.remove("loading"),a.classList.add("loaded")}catch(e){a.classList.remove("loading"),a.classList.add("error")}}async loadChart(e,a){window.ApexCharts||window.Chart||await this.loadScript("https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js");a=JSON.parse(a||"{}");window.ApexCharts&&new ApexCharts(e,a).render()}async loadTable(e,a){a=JSON.parse(a||"{}"),a=await(await fetch(a.url)).json();e.innerHTML=this.renderTable(a)}async loadCalendar(e,a){window.FullCalendar||await this.loadScript("https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js");a=JSON.parse(a||"{}");new FullCalendar.Calendar(e,a).render()}async loadForm(e,a){a=JSON.parse(a||"{}"),a=await(await fetch(a.url)).text();e.innerHTML=a,this.initializeFormHandlers(e)}async loadGenericComponent(e,a){var a=JSON.parse(a||"{}");a.url&&(a=await(await fetch(a.url)).text(),e.innerHTML=a)}setupContentLazyLoading(){this.deferNonCriticalContent(),this.setupProgressiveDisclosure()}deferNonCriticalContent(){let e=document.querySelectorAll("[data-defer-load]");setTimeout(()=>{e.forEach(e=>{e.classList.remove("deferred"),e.classList.add("loaded")})},100)}setupProgressiveDisclosure(){document.querySelectorAll("[data-progressive-list]").forEach(e=>{let a=e.querySelectorAll("[data-list-item]"),t=e.querySelector("[data-show-more]"),r=parseInt(e.dataset.initialCount)||10;a.forEach((e,a)=>{a>=r&&(e.style.display="none")}),t&&a.length>r&&(t.style.display="block",t.addEventListener("click",()=>{a.forEach(e=>{e.style.display=""}),t.style.display="none"}))})}loadScript(r){return new Promise((e,a)=>{var t=document.createElement("script");t.src=r,t.onload=e,t.onerror=a,document.head.appendChild(t)})}renderTable(e){if(!e.headers||!e.rows)return"<p>No data available</p>";let a='<table class="table"><thead><tr>';return e.headers.forEach(e=>{a+=`<th>${e}</th>`}),a+="</tr></thead><tbody>",e.rows.forEach(e=>{a+="<tr>",e.forEach(e=>{a+=`<td>${e}</td>`}),a+="</tr>"}),a+="</tbody></table>"}initializeFormHandlers(e){e.querySelectorAll("form").forEach(e=>{})}}let a=new e;window.LazyLoader=e;