class n{constructor(){this.warningModal=null,this.countdownInterval=null,this.sessionCheckInterval=null,this.warningThresholdMinutes=5,this.checkIntervalSeconds=30,this.isWarningShown=!1,this.init()}init(){this.startSessionMonitoring(),this.setupServerEventListeners(),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkSessionStatus()})}startSessionMonitoring(){this.sessionCheckInterval=setInterval(()=>{this.checkSessionStatus()},1e3*this.checkIntervalSeconds),this.checkSessionStatus()}async checkSessionStatus(){try{var n=await(await fetch("/api/session/status")).json();n.authenticated?n.minutesRemaining<=this.warningThresholdMinutes&&!this.isWarningShown?this.showSessionWarning(n.minutesRemaining):n.minutesRemaining>this.warningThresholdMinutes&&this.isWarningShown&&this.hideSessionWarning():this.handleSessionExpired(n.reason||"Session expired")}catch(n){}}setupServerEventListeners(){let n=window.fetch;window.fetch=async(...s)=>{s=await n(...s);if(s.headers.get("X-Session-Warning")){let n=parseInt(s.headers.get("X-Session-Minutes-Remaining"));n&&!this.isWarningShown&&this.showSessionWarning(n)}return s}}showSessionWarning(n){this.isWarningShown||(this.isWarningShown=!0,this.createWarningModal(n),this.startCountdown(n))}hideSessionWarning(){this.isWarningShown&&(this.isWarningShown=!1,this.warningModal&&(this.warningModal.remove(),this.warningModal=null),this.countdownInterval)&&(clearInterval(this.countdownInterval),this.countdownInterval=null)}createWarningModal(n){this.warningModal&&this.warningModal.remove(),this.warningModal=document.createElement("div"),this.warningModal.className="session-warning-modal",this.warningModal.innerHTML=`
      <div class="session-warning-overlay"></div>
      <div class="session-warning-content">
        <div class="session-warning-header">
          <i class="ph ph-warning-circle"></i>
          <h3>Session Expiring Soon</h3>
        </div>
        <div class="session-warning-body">
          <p>Your session will expire in <strong id="countdown-minutes">${n}</strong> minutes.</p>
          <p>Would you like to extend your session?</p>
        </div>
        <div class="session-warning-actions">
          <button class="btn-secondary" onclick="sessionManager.logout()">
            <i class="ph ph-sign-out"></i> Logout Now
          </button>
          <button class="btn-primary" onclick="sessionManager.extendSession()">
            <i class="ph ph-clock"></i> Extend Session
          </button>
        </div>
      </div>
    `,this.addModalStyles(),document.body.appendChild(this.warningModal)}addModalStyles(){var n;document.getElementById("session-warning-styles")||((n=document.createElement("style")).id="session-warning-styles",n.textContent='\n      .session-warning-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: "Syne", sans-serif;\n      }\n\n      .session-warning-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.5);\n        backdrop-filter: blur(4px);\n      }\n\n      .session-warning-content {\n        position: relative;\n        background: white;\n        border-radius: 12px;\n        padding: 2rem;\n        max-width: 400px;\n        width: 90%;\n        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n        animation: sessionWarningSlideIn 0.3s ease-out;\n      }\n\n      @keyframes sessionWarningSlideIn {\n        from {\n          opacity: 0;\n          transform: translateY(-20px) scale(0.95);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0) scale(1);\n        }\n      }\n\n      .session-warning-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 1.5rem;\n        color: #f59e0b;\n      }\n\n      .session-warning-header i {\n        font-size: 2rem;\n        margin-right: 0.75rem;\n      }\n\n      .session-warning-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .session-warning-body {\n        margin-bottom: 2rem;\n        color: #374151;\n        line-height: 1.6;\n      }\n\n      .session-warning-body p {\n        margin: 0 0 0.75rem 0;\n      }\n\n      .session-warning-body strong {\n        color: #f59e0b;\n        font-weight: 600;\n      }\n\n      .session-warning-actions {\n        display: flex;\n        gap: 1rem;\n        justify-content: flex-end;\n      }\n\n      .session-warning-actions .btn-primary,\n      .session-warning-actions .btn-secondary {\n        padding: 0.75rem 1.5rem;\n        border-radius: 6px;\n        font-weight: 500;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        border: none;\n        font-family: "Syne", sans-serif;\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n      }\n\n      .session-warning-actions .btn-primary {\n        background: #1e40af;\n        color: white;\n      }\n\n      .session-warning-actions .btn-primary:hover {\n        background: #1d4ed8;\n        transform: translateY(-1px);\n      }\n\n      .session-warning-actions .btn-secondary {\n        background: #f3f4f6;\n        color: #374151;\n        border: 1px solid #d1d5db;\n      }\n\n      .session-warning-actions .btn-secondary:hover {\n        background: #e5e7eb;\n        border-color: #9ca3af;\n      }\n\n      @media (max-width: 480px) {\n        .session-warning-content {\n          padding: 1.5rem;\n        }\n\n        .session-warning-actions {\n          flex-direction: column;\n        }\n      }\n    ',document.head.appendChild(n))}startCountdown(n){let s=n;this.countdownInterval=setInterval(()=>{s--;var n=document.getElementById("countdown-minutes");n&&((n.textContent=s)<=1?n.style.color="#dc2626":s<=2&&(n.style.color="#f59e0b")),s<=0&&this.handleSessionExpired("Session expired")},6e4)}async extendSession(){try{var n,s,e=await fetch("/api/session/extend",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({minutes:480})});e.ok?(n=await e.json(),this.hideSessionWarning(),this.showToast(`Session extended by ${n.extensionMinutes} minutes`,"success")):(s=await e.json(),this.showToast(s.message||"Failed to extend session","error"))}catch(n){this.showToast("Failed to extend session","error")}}async logout(){try{this.hideSessionWarning(),this.showToast("Logging out...","info"),window.location.href="/logout"}catch(n){window.location.href="/logout"}}handleSessionExpired(n){this.hideSessionWarning(),this.stopSessionMonitoring(),this.showToast(n||"Your session has expired","warning"),setTimeout(()=>{window.location.href="/login"},2e3)}stopSessionMonitoring(){this.sessionCheckInterval&&(clearInterval(this.sessionCheckInterval),this.sessionCheckInterval=null),this.countdownInterval&&(clearInterval(this.countdownInterval),this.countdownInterval=null)}showToast(s,e="info"){if(window.showToast)window.showToast(s,e);else{let n=document.createElement("div");n.style.cssText=`
      position: fixed; top: 20px; right: 20px; z-index: 10001;
      background: ${"error"===e?"#dc2626":"success"===e?"#10b981":"warning"===e?"#f59e0b":"#3b82f6"};
      color: white; padding: 1rem 1.5rem; border-radius: 8px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      font-family: "Syne", sans-serif; font-weight: 500;
      animation: slideInRight 0.3s ease-out;
    `,n.textContent=s,document.body.appendChild(n),setTimeout(()=>n.remove(),5e3)}}forceSessionCheck(){this.checkSessionStatus()}getSessionStatus(){return{isWarningShown:this.isWarningShown,isMonitoring:!!this.sessionCheckInterval}}}document.addEventListener("DOMContentLoaded",()=>{let s=window.location.pathname;["/login","/register","/forgot_password","/reset_password","/verify"].some(n=>s.startsWith(n))||(window.sessionManager=new n)}),"undefined"!=typeof module&&module.exports&&(module.exports=n);