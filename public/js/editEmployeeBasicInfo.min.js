let p={"ABSA Bank":{name:"ABSA Bank",universalCode:"632005"},"Capitec Bank":{name:"Capitec Bank",universalCode:"470010"},FNB:{name:"First National Bank",universalCode:"250655"},Nedbank:{name:"Nedbank",universalCode:"198765"},"Standard Bank":{name:"Standard Bank",universalCode:"051001"},"African Bank":{name:"African Bank",universalCode:"430000"},"Bidvest Bank":{name:"Bidvest Bank",universalCode:"462005"},"Discovery Bank":{name:"Discovery Bank",universalCode:"679000"},TymeBank:{name:"TymeBank",universalCode:"678910"},Other:{name:"Other Bank",universalCode:""}};document.addEventListener("DOMContentLoaded",function(){let o=document.getElementById("editEmployeeForm"),e=document.getElementById("idType"),t=document.getElementById("idNumberContainer"),a=document.getElementById("passportNumberContainer"),n=document.getElementById("dob"),r=(flatpickr("#dob",{dateFormat:"Y-m-d",maxDate:new Date,disableMobile:!0,defaultDate:null,allowInput:!0,placeholder:"Select date of birth",onChange:function(e){e[0]&&validateAge(e[0])}}),flatpickr("#doa",{dateFormat:"Y-m-d",maxDate:"today",disableMobile:!0,allowInput:!0,placeholder:"Select date of appointment"}),document.getElementById("bankName")),s=document.getElementById("branchCode");if(r){let a=r.getAttribute("data-current-bank");Object.keys(p).forEach(e=>{var t=new Option(p[e].name,e);e===a&&(t.selected=!0),r.appendChild(t)}),r.addEventListener("change",function(){var e=p[this.value];e&&e.universalCode&&(s.value=e.universalCode,s.setAttribute("data-original",e.universalCode))}),s.addEventListener("input",function(){var e=this.getAttribute("data-original");e&&this.value!==e?this.classList.add("modified"):this.classList.remove("modified")})}let i=document.getElementById("oidEligible"),d=document.getElementById("oidExemptReasonGroup"),l=document.getElementById("oidExemptReason");function c(e,t){e.classList.add("error");var a=document.createElement("div");a.className="error-message",a.textContent=t,e.parentNode.appendChild(a)}function u(e,t="info"){let a=document.createElement("div");a.className="toast-notification "+t,a.innerHTML=`
      <i class="ph ${"success"===t?"ph-check-circle":"ph-warning-circle"}"></i>
      <span>${e}</span>
    `,document.body.appendChild(a),setTimeout(()=>{a.classList.add("fade-out"),setTimeout(()=>a.remove(),300)},3e3)}i&&d&&i.addEventListener("change",function(){d.style.display=this.checked?"none":"block",this.checked&&(l.value="")}),o.addEventListener("submit",async function(e){e.preventDefault();var e=i.checked,t=l.value.trim();if(e||t)if((()=>{let e=o.querySelectorAll("[required]"),a=!0;return o.querySelectorAll(".error-message").forEach(e=>e.remove()),o.querySelectorAll(".error").forEach(e=>e.classList.remove("error")),e.forEach(e=>{if(e.value.trim())switch(e.id){case"idNumber":e.value&&(t=e.value,!/^\d{13}$/.test(t))&&(a=!1,c(e,"Invalid SA ID number"));break;case"accountNumber":e.value&&(t=e.value,!/^\d{5,}$/.test(t.replace(/\s/g,"")))&&(a=!1,c(e,"Invalid account number"));break;case"email":e.value&&(t=e.value,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))&&(a=!1,c(e,"Invalid email address"))}else a=!1,c(e,"This field is required");var t}),a})()){var s,e=o.querySelector('button[type="submit"]');e.disabled=!0,o.classList.add("form-loading");try{let e=new FormData(o),t={firstName:e.get("firstName"),lastName:e.get("lastName"),dob:e.get("dob")||null,idType:e.get("idType"),idNumber:"rsa"===e.get("idType")?e.get("idNumber"):null,passportNumber:"passport"===e.get("idType")?e.get("passportNumber"):null,jobTitle:e.get("jobTitle"),department:e.get("department"),costCentre:e.get("costCentre"),payFrequency:e.get("payFrequency"),doa:e.get("doa")||null,paymentMethod:e.get("paymentMethod"),bank:e.get("bankName"),accountType:e.get("accountType"),accountNumber:e.get("accountNumber"),branchCode:e.get("branchCode"),holderRelationship:e.get("holderRelationship"),streetAddress:e.get("streetAddress"),suburb:e.get("suburb"),city:e.get("city"),postalCode:e.get("postalCode"),email:e.get("email")||null,personalDetails:{mobileNumber:(s=e.get("personalDetails.mobileNumber"))?(s=s.replace(/[^\d]/g,"")).startsWith("0")&&10===s.length?"27"+s.slice(1):s:""}},a=document.querySelector('meta[name="csrf-token"]')?.content;if(!a)throw new Error("CSRF token not found");let n=await fetch(o.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":a},credentials:"same-origin",body:JSON.stringify(t)}),r=await n.json();if(!n.ok)throw new Error(r.message||"Server returned an error");if(!r.success)throw new Error(r.message||"Failed to save changes");u(r.message||"Changes saved successfully","success"),setTimeout(()=>{r.redirectUrl&&(window.location.href=r.redirectUrl)},1e3)}catch(e){u(e.message||"Failed to save changes","error")}finally{e.disabled=!1,o.classList.remove("form-loading")}}else u("Please check all required fields","error");else u("Please provide a reason for OID exemption","error"),l.focus()}),e.addEventListener("change",function(){var e=this.value;t.style.display="rsa"===e?"block":"none",a.style.display="passport"===e?"block":"none","rsa"===e?document.getElementById("passportNumber").value="":"passport"===e&&(document.getElementById("idNumber").value="")}),n.addEventListener("change",function(){var e=new Date(this.value),e=(new Date).getFullYear()-e.getFullYear();e<16||100<e?(this.setCustomValidity("Age must be between 16 and 100 years"),u("Age must be between 16 and 100 years","error")):this.setCustomValidity("")}),e.dispatchEvent(new Event("change"));var m=document.getElementById("payFrequency");m&&m.addEventListener("change",function(){var e=this.options[this.selectedIndex];e.getAttribute("data-frequency"),e.getAttribute("data-last-day")})});