class SessionManager{constructor(){this.warningModal=null,this.countdownInterval=null,this.sessionCheckInterval=null,this.warningThresholdMinutes=5,this.checkIntervalSeconds=30,this.isWarningShown=!1,this.init()}init(){this.startSessionMonitoring(),this.setupServerEventListeners(),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkSessionStatus()}),console.log("SessionManager initialized")}startSessionMonitoring(){this.sessionCheckInterval=setInterval(()=>{this.checkSessionStatus()},1e3*this.checkIntervalSeconds),this.checkSessionStatus()}async checkSessionStatus(){try{const n=await fetch("/api/session/status"),s=await n.json();if(!s.authenticated)return void this.handleSessionExpired(s.reason||"Session expired");s.minutesRemaining<=this.warningThresholdMinutes&&!this.isWarningShown?this.showSessionWarning(s.minutesRemaining):s.minutesRemaining>this.warningThresholdMinutes&&this.isWarningShown&&this.hideSessionWarning()}catch(n){console.error("Error checking session status:",n)}}setupServerEventListeners(){const n=window.fetch;window.fetch=async(...s)=>{const e=await n(...s);if(e.headers.get("X-Session-Warning")){const n=parseInt(e.headers.get("X-Session-Minutes-Remaining"));n&&!this.isWarningShown&&this.showSessionWarning(n)}return e}}showSessionWarning(n){this.isWarningShown||(this.isWarningShown=!0,this.createWarningModal(n),this.startCountdown(n))}hideSessionWarning(){this.isWarningShown&&(this.isWarningShown=!1,this.warningModal&&(this.warningModal.remove(),this.warningModal=null),this.countdownInterval&&(clearInterval(this.countdownInterval),this.countdownInterval=null))}createWarningModal(n){this.warningModal&&this.warningModal.remove(),this.warningModal=document.createElement("div"),this.warningModal.className="session-warning-modal",this.warningModal.innerHTML=`\n      <div class="session-warning-overlay"></div>\n      <div class="session-warning-content">\n        <div class="session-warning-header">\n          <i class="ph ph-warning-circle"></i>\n          <h3>Session Expiring Soon</h3>\n        </div>\n        <div class="session-warning-body">\n          <p>Your session will expire in <strong id="countdown-minutes">${n}</strong> minutes.</p>\n          <p>Would you like to extend your session?</p>\n        </div>\n        <div class="session-warning-actions">\n          <button class="btn-secondary" onclick="sessionManager.logout()">\n            <i class="ph ph-sign-out"></i> Logout Now\n          </button>\n          <button class="btn-primary" onclick="sessionManager.extendSession()">\n            <i class="ph ph-clock"></i> Extend Session\n          </button>\n        </div>\n      </div>\n    `,this.addModalStyles(),document.body.appendChild(this.warningModal)}addModalStyles(){if(document.getElementById("session-warning-styles"))return;const n=document.createElement("style");n.id="session-warning-styles",n.textContent='\n      .session-warning-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: "Syne", sans-serif;\n      }\n\n      .session-warning-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.5);\n        backdrop-filter: blur(4px);\n      }\n\n      .session-warning-content {\n        position: relative;\n        background: white;\n        border-radius: 12px;\n        padding: 2rem;\n        max-width: 400px;\n        width: 90%;\n        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n        animation: sessionWarningSlideIn 0.3s ease-out;\n      }\n\n      @keyframes sessionWarningSlideIn {\n        from {\n          opacity: 0;\n          transform: translateY(-20px) scale(0.95);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0) scale(1);\n        }\n      }\n\n      .session-warning-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 1.5rem;\n        color: #f59e0b;\n      }\n\n      .session-warning-header i {\n        font-size: 2rem;\n        margin-right: 0.75rem;\n      }\n\n      .session-warning-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .session-warning-body {\n        margin-bottom: 2rem;\n        color: #374151;\n        line-height: 1.6;\n      }\n\n      .session-warning-body p {\n        margin: 0 0 0.75rem 0;\n      }\n\n      .session-warning-body strong {\n        color: #f59e0b;\n        font-weight: 600;\n      }\n\n      .session-warning-actions {\n        display: flex;\n        gap: 1rem;\n        justify-content: flex-end;\n      }\n\n      .session-warning-actions .btn-primary,\n      .session-warning-actions .btn-secondary {\n        padding: 0.75rem 1.5rem;\n        border-radius: 6px;\n        font-weight: 500;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        border: none;\n        font-family: "Syne", sans-serif;\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n      }\n\n      .session-warning-actions .btn-primary {\n        background: #1e40af;\n        color: white;\n      }\n\n      .session-warning-actions .btn-primary:hover {\n        background: #1d4ed8;\n        transform: translateY(-1px);\n      }\n\n      .session-warning-actions .btn-secondary {\n        background: #f3f4f6;\n        color: #374151;\n        border: 1px solid #d1d5db;\n      }\n\n      .session-warning-actions .btn-secondary:hover {\n        background: #e5e7eb;\n        border-color: #9ca3af;\n      }\n\n      @media (max-width: 480px) {\n        .session-warning-content {\n          padding: 1.5rem;\n        }\n\n        .session-warning-actions {\n          flex-direction: column;\n        }\n      }\n    ',document.head.appendChild(n)}startCountdown(n){let s=n;this.countdownInterval=setInterval(()=>{s--;const n=document.getElementById("countdown-minutes");n&&(n.textContent=s,s<=1?n.style.color="#dc2626":s<=2&&(n.style.color="#f59e0b")),s<=0&&this.handleSessionExpired("Session expired")},6e4)}async extendSession(){try{const n=await fetch("/api/session/extend",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({minutes:480})});if(n.ok){const s=await n.json();this.hideSessionWarning(),this.showToast(`Session extended by ${s.extensionMinutes} minutes`,"success")}else{const s=await n.json();this.showToast(s.message||"Failed to extend session","error")}}catch(n){console.error("Error extending session:",n),this.showToast("Failed to extend session","error")}}async logout(){try{this.hideSessionWarning(),this.showToast("Logging out...","info"),window.location.href="/logout"}catch(n){console.error("Error during logout:",n),window.location.href="/logout"}}handleSessionExpired(n){this.hideSessionWarning(),this.stopSessionMonitoring(),this.showToast(n||"Your session has expired","warning"),setTimeout(()=>{window.location.href="/login"},2e3)}stopSessionMonitoring(){this.sessionCheckInterval&&(clearInterval(this.sessionCheckInterval),this.sessionCheckInterval=null),this.countdownInterval&&(clearInterval(this.countdownInterval),this.countdownInterval=null)}showToast(n,s="info"){if(window.showToast)return void window.showToast(n,s);const e=document.createElement("div");e.style.cssText=`\n      position: fixed; top: 20px; right: 20px; z-index: 10001;\n      background: ${"error"===s?"#dc2626":"success"===s?"#10b981":"warning"===s?"#f59e0b":"#3b82f6"};\n      color: white; padding: 1rem 1.5rem; border-radius: 8px;\n      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n      font-family: "Syne", sans-serif; font-weight: 500;\n      animation: slideInRight 0.3s ease-out;\n    `,e.textContent=n,document.body.appendChild(e),setTimeout(()=>e.remove(),5e3)}forceSessionCheck(){this.checkSessionStatus()}getSessionStatus(){return{isWarningShown:this.isWarningShown,isMonitoring:!!this.sessionCheckInterval}}}document.addEventListener("DOMContentLoaded",()=>{const n=window.location.pathname;["/login","/register","/forgot_password","/reset_password","/verify"].some(s=>n.startsWith(s))||(window.sessionManager=new SessionManager)}),"undefined"!=typeof module&&module.exports&&(module.exports=SessionManager);