console.log("[XERO-DEBUG] Xero integration JavaScript loaded!",(new Date).toISOString());let initSequence=0,tokenRefreshInterval=null,tokenRefreshRetryCount=0;const MAX_RETRY_ATTEMPTS=3,RETRY_DELAY=5e3;let worker=null;async function checkAndRefreshToken(){try{console.log("[XERO-DEBUG] Checking token status...",(new Date).toISOString());const e=await fetch("/xero/health"),n=await e.json();console.log("[XERO-DEBUG] Token health check:",n),tokenRefreshRetryCount=0,n.needsRefresh||n.timeUntilExpiry&&n.timeUntilExpiry<600?(console.log("[XERO-DEBUG] Token needs refresh, initiating refresh...",(new Date).toISOString()),await refreshToken()):"inactive"===n.status&&(console.error("[XERO-DEBUG] Xero integration is inactive"),showToast("Xero connection is inactive. Please reconnect.","error"),setTimeout(()=>window.location.reload(),2e3))}catch(e){console.error("[XERO-DEBUG] Error checking token:",e),await handleTokenCheckError()}}async function refreshToken(){try{const e=await fetch("/xero/refresh",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-Token":document.querySelector('meta[name="csrf-token"]').content}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const n=await e.json();if(console.log("[XERO-DEBUG] Token refresh result:",n),!n.success)throw new Error(n.error||"Token refresh failed");tokenRefreshRetryCount=0,showToast("Xero connection refreshed successfully","success"),window.handleTokenRefreshSuccess&&window.handleTokenRefreshSuccess()}catch(e){console.error("[XERO-DEBUG] Token refresh error:",e),await handleTokenRefreshError(e)}}async function handleTokenCheckError(){tokenRefreshRetryCount++,tokenRefreshRetryCount<=3?(console.log(`[XERO-DEBUG] Retrying token check (attempt ${tokenRefreshRetryCount}/3)...`),showToast(`Retrying connection check (attempt ${tokenRefreshRetryCount})...`,"info"),await new Promise(e=>setTimeout(e,5e3)),await checkAndRefreshToken()):(console.error("[XERO-DEBUG] Max retry attempts reached for token check"),showToast("Unable to verify Xero connection. Please try reconnecting.","error"),setTimeout(()=>window.location.reload(),2e3))}async function handleTokenRefreshError(e){tokenRefreshRetryCount++,tokenRefreshRetryCount<=3?(console.log(`[XERO-DEBUG] Retrying token refresh (attempt ${tokenRefreshRetryCount}/3)...`),showToast(`Retrying connection refresh (attempt ${tokenRefreshRetryCount})...`,"info"),await new Promise(e=>setTimeout(e,5e3)),await refreshToken()):(console.error("[XERO-DEBUG] Max retry attempts reached for token refresh"),showToast("Unable to refresh Xero connection. Please reconnect.","error"),window.handleTokenRefreshError&&window.handleTokenRefreshError(e),setTimeout(()=>window.location.reload(),2e3))}async function initializeWorker(){if("serviceWorker"in navigator)try{const e=await navigator.serviceWorker.register("/js/xero-refresh-worker.js");console.log("[XERO-DEBUG] Service Worker registered:",e),worker||(worker=e.active||e.waiting||e.installing),navigator.serviceWorker.addEventListener("message",e=>{switch(e.data.type){case"REFRESH_SUCCESS":console.log("[XERO-DEBUG] Background refresh successful"),showToast("Xero connection refreshed in background","success");break;case"REFRESH_ERROR":console.error("[XERO-DEBUG] Background refresh error:",e.data.error),showToast("Error refreshing Xero connection","error");break;case"ERROR":console.error("[XERO-DEBUG] Background worker error:",e.data.error)}}),worker&&worker.postMessage({type:"START_REFRESH",companyCode:document.querySelector('meta[name="company-code"]').content})}catch(e){console.error("[XERO-DEBUG] Service Worker registration failed:",e)}}function startTokenRefreshMonitoring(){tokenRefreshInterval&&clearInterval(tokenRefreshInterval),tokenRefreshRetryCount=0,initializeWorker(),checkAndRefreshToken(),tokenRefreshInterval=setInterval(checkAndRefreshToken,3e5),console.log("[XERO-DEBUG] Token refresh monitoring started with 5-minute intervals",(new Date).toISOString()),document.addEventListener("visibilitychange",handleVisibilityChange),window.addEventListener("beforeunload",handleBeforeUnload)}function handleVisibilityChange(){"visible"===document.visibilityState&&(console.log("[XERO-DEBUG] Page became visible, checking token status..."),checkAndRefreshToken())}function handleBeforeUnload(){tokenRefreshInterval&&clearInterval(tokenRefreshInterval),document.removeEventListener("visibilitychange",handleVisibilityChange),worker&&worker.postMessage({type:"STOP_REFRESH"})}function showToast(e,n="info"){const t=document.getElementById("tokenRefreshToast");if(!t)return;t.timeoutId&&clearTimeout(t.timeoutId);const o="success"===n?"ph-check-circle":"error"===n?"ph-x-circle":"info"===n?"ph-info":"ph-bell";t.innerHTML=`\n        <i class="ph ${o}"></i>\n        <span>${e}</span>\n    `,t.className=`toast ${n}`,t.style.display="block",t.timeoutId=setTimeout(()=>{t.style.display="none"},5e3)}function initializeXeroMapping(e){const n=++initSequence;console.log(`[XERO-DEBUG] [${n}] initializeXeroMapping started`,(new Date).toISOString());const t=document.getElementById("xeroMappingGrid"),o=document.getElementById("saveXeroMappings");t&&o?(console.log(`[XERO-DEBUG] [${n}] Clearing mapping grid`,(new Date).toISOString()),t.innerHTML="",console.log(`[XERO-DEBUG] [${n}] Fetching Xero accounts`,(new Date).toISOString()),fetchXeroAccounts(e).then(r=>{if(console.log(`[XERO-DEBUG] [${n}] Accounts fetched, count:`,r.length,(new Date).toISOString()),!r||0===r.length)return console.log(`[XERO-DEBUG] [${n}] No accounts found`,(new Date).toISOString()),void showMappingError("No Xero accounts found");console.log(`[XERO-DEBUG] [${n}] Fetching current mappings`,(new Date).toISOString()),fetchCurrentMappings(e).then(i=>{console.log(`[XERO-DEBUG] [${n}] Mappings fetched:`,i,(new Date).toISOString()),t.children.length>0&&"skeleton"!==t.children[0].className?console.log(`[XERO-DEBUG] [${n}] Grid already populated, skipping to prevent duplicates`,(new Date).toISOString()):(console.log(`[XERO-DEBUG] [${n}] Creating mapping cards`,(new Date).toISOString()),createMappingCards(t,r,i,n),o.disabled=!1,o.addEventListener("click",function(){saveMappings(e)}),console.log(`[XERO-DEBUG] [${n}] Mapping initialization complete`,(new Date).toISOString()))}).catch(e=>{console.error(`[XERO-DEBUG] [${n}] Error fetching mappings:`,e,(new Date).toISOString()),showMappingError("Failed to load current mappings")})}).catch(e=>{console.error(`[XERO-DEBUG] [${n}] Error fetching Xero accounts:`,e,(new Date).toISOString()),showMappingError("Failed to load Xero accounts")})):console.log(`[XERO-DEBUG] [${n}] Missing required elements, aborting initialization`,(new Date).toISOString())}function fetchXeroAccounts(e){return console.log(`[XERO-DEBUG] fetchXeroAccounts started for ${e}`,(new Date).toISOString()),fetch(`/clients/${e}/settings/accounting/xero/accounts`).then(e=>{if(console.log("[XERO-DEBUG] fetchXeroAccounts response status:",e.status,(new Date).toISOString()),!e.ok)throw new Error("Failed to fetch Xero accounts");return e.json()}).then(e=>(console.log(`[XERO-DEBUG] fetchXeroAccounts received ${e.length} accounts`,(new Date).toISOString()),e))}function fetchCurrentMappings(e){return console.log(`[XERO-DEBUG] fetchCurrentMappings started for ${e}`,(new Date).toISOString()),fetch(`/clients/${e}/settings/accounting/xero/mappings`).then(e=>{if(console.log("[XERO-DEBUG] fetchCurrentMappings response status:",e.status,(new Date).toISOString()),!e.ok)throw new Error("Failed to fetch current mappings");return e.json()}).then(e=>(console.log("[XERO-DEBUG] fetchCurrentMappings received data:",e,(new Date).toISOString()),e))}function createMappingCards(e,n,t,o){console.log(`[XERO-DEBUG] [${o}] createMappingCards started with ${n.length} accounts`,(new Date).toISOString()),console.log(`[XERO-DEBUG] [${o}] All Xero accounts:`,n.map(e=>`${e.name} (${e.code}) - ${e.type}`),(new Date).toISOString()),e.children.length>0&&"skeleton"!==e.children[0].className&&(console.log(`[XERO-DEBUG] [${o}] Grid already has ${e.children.length} children, clearing to prevent duplicates`,(new Date).toISOString()),e.innerHTML=""),[{id:"salary",name:"Basic Salary",required:!0,accountTypes:["EXPENSE","DIRECTCOSTS"]},{id:"commission",name:"Commission",required:!1,accountTypes:["EXPENSE","DIRECTCOSTS"]},{id:"bonus",name:"Bonus",required:!1,accountTypes:["EXPENSE","DIRECTCOSTS"]},{id:"medical",name:"Medical Aid",required:!1,accountTypes:["EXPENSE","LIABILITY","CURRLIAB"]},{id:"pension",name:"Pension Fund",required:!1,accountTypes:["EXPENSE","LIABILITY","CURRLIAB"]},{id:"paye",name:"PAYE",required:!0,accountTypes:["LIABILITY","CURRLIAB"]},{id:"uif",name:"UIF",required:!0,accountTypes:["LIABILITY","CURRLIAB"]}].forEach(r=>{console.log(`[XERO-DEBUG] [${o}] Creating card for ${r.name}`,(new Date).toISOString());const i=document.createElement("div");i.className="mapping-card",i.dataset.item=r.id,i.dataset.sequence=o;const c=document.createElement("h3");c.textContent=r.name+(r.required?" *":"");const s=document.createElement("select");s.name=r.id,s.required=r.required;const a=document.createElement("option");a.value="",a.textContent="Select an account",a.disabled=!0,a.selected=!t[r.id],s.appendChild(a);const l=n.filter(e=>!r.accountTypes||0===r.accountTypes.length||r.accountTypes.includes(e.type)),d=[],h=new Set;l.forEach(e=>{h.has(e.name)||(h.add(e.name),d.push(e))}),d.sort((e,n)=>e.name.localeCompare(n.name)),console.log(`[XERO-DEBUG] [${o}] Filtered accounts for ${r.name}:`,d.map(e=>`${e.name} (${e.code})`),(new Date).toISOString()),d.forEach(e=>{const n=document.createElement("option");n.value=e.accountID,n.textContent=`${e.name} (${e.code})`,n.selected=t[r.id]===e.accountID,s.appendChild(n)}),s.addEventListener("change",function(){saveButton.classList.add("active")}),i.appendChild(c),i.appendChild(s),e.appendChild(i),console.log(`[XERO-DEBUG] [${o}] Card created for ${r.name} with ${d.length} accounts`,(new Date).toISOString())}),console.log(`[XERO-DEBUG] [${o}] All cards created, grid now has ${e.children.length} children`,(new Date).toISOString())}function saveMappings(e){const n=document.getElementById("saveXeroMappings");n.disabled=!0,n.innerHTML='<i class="ph ph-spinner ph-spin"></i> Saving...';const t=document.querySelectorAll(".mapping-card"),o={};t.forEach(e=>{const n=e.dataset.item,t=e.querySelector("select");o[n]=t.value});const r=document.querySelector('meta[name="csrf-token"]').getAttribute("content");fetch(`/clients/${e}/settings/accounting/xero/mappings`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":r},body:JSON.stringify({mappings:o})}).then(e=>{if(!e.ok)throw new Error("Failed to save mappings");return e.json()}).then(e=>{n.innerHTML='<i class="ph ph-check"></i> Saved',n.classList.remove("active"),showToast("Mappings saved successfully","success"),setTimeout(()=>{n.innerHTML='<i class="ph ph-floppy-disk"></i> Save Mappings',n.disabled=!1},2e3)}).catch(e=>{console.error("Error saving mappings:",e),n.innerHTML='<i class="ph ph-x"></i> Error',showToast("Failed to save mappings","error"),setTimeout(()=>{n.innerHTML='<i class="ph ph-floppy-disk"></i> Save Mappings',n.disabled=!1},2e3)})}function showMappingError(e){const n=document.getElementById("xeroMappingGrid");n&&(n.innerHTML=`\n        <div class="mapping-error">\n            <i class="ph ph-warning"></i>\n            <p>${e}</p>\n        </div>\n    `)}document.addEventListener("DOMContentLoaded",function(){console.log("[XERO-DEBUG] Xero integration DOM loaded!",(new Date).toISOString()),"undefined"!=typeof isXeroConnected&&isXeroConnected?(console.log("[XERO-DEBUG] Xero is connected, initializing mapping",(new Date).toISOString()),initializeXeroMapping(companyCode),startTokenRefreshMonitoring()):(console.log("[XERO-DEBUG] Xero is not connected or isXeroConnected is undefined",(new Date).toISOString()),console.log("[XERO-DEBUG] isXeroConnected:",isXeroConnected));const e=document.getElementById("disconnectButton");e&&e.addEventListener("click",function(n){n.preventDefault();const t=e.getAttribute("data-company-code"),o=document.getElementById("disconnectModal");if(o){o.classList.add("active");const e=document.getElementById("disconnectCompanyCode");e&&(e.value=t)}}),document.querySelectorAll(".close-modal").forEach(e=>{e.addEventListener("click",function(){const e=document.getElementById("disconnectModal");e&&e.classList.remove("active")})});const n=document.getElementById("cancelDisconnect");n&&n.addEventListener("click",function(e){e.preventDefault();const n=document.getElementById("disconnectModal");n&&n.classList.remove("active")})});