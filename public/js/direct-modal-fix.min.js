function r(t){var e=document.getElementById("payslipReviewModal");if(e){if(t){var n=e.querySelector("h2");if(n){let e=t.charAt(0).toUpperCase()+t.slice(1);n.textContent=`Review ${e} Payslips`}}e.style.cssText="display: block !important; z-index: 9999 !important;",e.classList.remove("hide"),e.classList.add("show"),a(e)}else alert("Error: Modal element not found!")}function a(e){var t=document.createElement("button");t.innerText="Close Modal (Fix)",t.style.position="fixed",t.style.top="10px",t.style.right="10px",t.style.padding="10px",t.style.backgroundColor="#e74c3c",t.style.color="white",t.style.border="none",t.style.borderRadius="4px",t.style.zIndex="10000",t.style.cursor="pointer",t.onclick=function(){n(e),this.remove()},document.body.appendChild(t)}function n(e){e.style.cssText="display: none !important;",e.classList.remove("show"),e.classList.add("hide")}function o(){let e=document.querySelectorAll(".btn.btn-primary"),t=0;e.forEach(o=>{"Review"===o.textContent.trim()&&(o.onclick=function(e){let t;if(o.hasAttribute("data-frequency"))t=o.getAttribute("data-frequency");else if(o.hasAttribute("onclick")){let e=o.getAttribute("onclick").match(/openPayslipReviewModal\(['"]([^'"]+)['"]/);t=e?e[1]:null}if(!t){let e=o.closest(".period-tracker");if(e&&e.dataset.frequency)t=e.dataset.frequency;else{let e=o.closest(".frequency-tracker");t=e&&e.dataset.frequency?e.dataset.frequency:window.currentModalFrequency||"weekly"}}var n=o.getAttribute("data-start-date")||"",a=o.getAttribute("data-end-date")||"";if(r(window.currentModalFrequency=t),"function"==typeof loadPayslipsForFrequency)try{loadPayslipsForFrequency(t,n,a)}catch(e){i(t,n,a)}else i(t,n,a);return e.preventDefault(),e.stopPropagation(),!1},t++)})}function i(t,e,n){window.currentModalFrequency=t;let a=document.getElementById("frequency-payslips-body");a&&(a.innerHTML=`
    <tr>
      <td colspan="8" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading ${t} payslips...</span>
      </td>
    </tr>
  `,fetch(`/api/payslips/pending?frequency=${t}&startDate=${e}&endDate=`+n).then(e=>{if(e.ok)return e.json();throw new Error("HTTP error "+e.status)}).then(e=>{if(e&&e.payslips&&0!==e.payslips.length){let r="";e.payslips.forEach(e=>{var t=e.employee?e.employee.firstName+" "+e.employee.lastName:"Unknown Employee",n=s(e.payPeriod?.startDate,e.payPeriod?.endDate),a=l(e.incomeItems)||0,o=l(e.deductionItems)||0,i=a-o;r+=`
          <tr data-payslip-id="${e._id}">
            <td class="checkbox-col">
              <div class="checkbox-wrapper">
                <input
                  type="checkbox"
                  id="payslip-${e._id}"
                  class="payslip-checkbox"
                  data-payslip-id="${e._id}"
                  ${"finalized"===e.status?"disabled checked":""}
                />
                <label for="payslip-${e._id}"></label>
              </div>
            </td>
            <td class="employee-col">${t}</td>
            <td class="period-col">${n}</td>
            <td class="amount-col">${c(a)}</td>
            <td class="amount-col">${c(o)}</td>
            <td class="amount-col">${c(i)}</td>
            <td class="status-col">${e.status}</td>
            <td class="actions-col">
              <div class="action-buttons">
                <button
                  class="icon-button"
                  onclick="viewPayslip('${e._id}')"
                  title="View Payslip"
                >
                  <i class="ph ph-eye"></i>
                </button>
              </div>
            </td>
          </tr>
        `}),a.innerHTML=r;e=document.getElementById("finalize-selected");e&&(e.style.display="inline-block")}else a.innerHTML=`
          <tr>
            <td colspan="8" class="no-data">
              No pending ${t} payslips found
            </td>
          </tr>
        `}).catch(e=>{a.innerHTML=`
        <tr>
          <td colspan="8" class="error">
            Error loading payslips: ${e.message}
          </td>
        </tr>
      `}))}function s(e,t){var n;return e&&t?window.moment?moment(e).format("DD/MM/YYYY")+" - "+moment(t).format("DD/MM/YYYY"):(n=e=>{e=new Date(e);return`${e.getDate().toString().padStart(2,"0")}/${(e.getMonth()+1).toString().padStart(2,"0")}/`+e.getFullYear()})(e)+" - "+n(t):"N/A"}function l(e){return e&&Array.isArray(e)?e.reduce((e,t)=>e+(parseFloat(t.amount)||0),0):0}function c(e){return"function"==typeof window.formatCurrency?window.formatCurrency(e):e.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}document.addEventListener("DOMContentLoaded",function(){var e=document.createElement("button"),t=(e.innerText="Test Modal Fix",e.style.margin="10px",e.style.padding="5px 10px",e.style.backgroundColor="#ff9800",e.style.color="white",e.style.border="none",e.style.borderRadius="4px",e.style.cursor="pointer",e.onclick=function(){var e="weekly";if(r(window.currentModalFrequency=e),"function"==typeof loadPayslipsForFrequency)try{var t=new Date,n=new Date(t),a=(n.setDate(t.getDate()-7),t),o=n.toISOString().split("T")[0],i=a.toISOString().split("T")[0];loadPayslipsForFrequency(e,o,i)}catch(e){}},document.querySelector(".main-container"));t&&t.insertBefore(e,t.firstChild),setTimeout(o,1e3)});