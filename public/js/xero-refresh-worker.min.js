const REFRESH_INTERVAL=3e5;let refreshTimeout;function startRefreshCycle(e){console.log("[XERO-WORKER] Starting refresh cycle"),checkToken(e),refreshTimeout=setInterval(()=>{checkToken(e)},3e5)}function stopRefreshCycle(){console.log("[XERO-WORKER] Stopping refresh cycle"),refreshTimeout&&clearInterval(refreshTimeout)}async function checkToken(e){try{console.log("[XERO-WORKER] Checking token status");const r=await fetch("/xero/health"),s=await r.json();(s.needsRefresh||s.timeUntilExpiry&&s.timeUntilExpiry<600)&&await refreshToken(e)}catch(e){console.error("[XERO-WORKER] Token check error:",e),self.postMessage({type:"ERROR",error:e.message})}}async function refreshToken(e){try{console.log("[XERO-WORKER] Refreshing token");const e=await fetch("/xero/refresh",{method:"POST",headers:{"Content-Type":"application/json"}}),r=await e.json();if(!r.success)throw new Error(r.error||"Token refresh failed");self.postMessage({type:"REFRESH_SUCCESS"})}catch(e){console.error("[XERO-WORKER] Token refresh error:",e),self.postMessage({type:"REFRESH_ERROR",error:e.message})}}self.addEventListener("message",e=>{"START_REFRESH"===e.data.type?startRefreshCycle(e.data.companyCode):"STOP_REFRESH"===e.data.type&&stopRefreshCycle()});