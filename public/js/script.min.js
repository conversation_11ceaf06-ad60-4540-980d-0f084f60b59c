document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("csrfToken")?.value;if(e){const o=window.fetch;window.fetch=function(){const t=Array.from(arguments);return t[1]&&t[1].method&&"GET"!==t[1].method&&(t[1].headers||(t[1].headers={}),t[1].headers["CSRF-Token"]=e),o.apply(window,t)}}document.querySelectorAll("form").forEach(o=>{o.addEventListener("submit",async function(t){t.preventDefault();const n=new FormData(o),r={};for(let[e,o]of n.entries())r[e]=o;try{const t=await fetch(o.action,{method:o.method||"POST",headers:{"Content-Type":"application/json","CSRF-Token":e},body:JSON.stringify(r)});if(!t.ok)throw new Error("Network response was not ok");const n=await t.json();n.redirect&&(window.location.href=n.redirect)}catch(e){console.error("Error:",e)}})})});