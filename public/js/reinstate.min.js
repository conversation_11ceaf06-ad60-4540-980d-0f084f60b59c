document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("save-button"),t=document.getElementById("no-changes-text"),o=document.querySelectorAll(".reinstate-select"),n=document.querySelectorAll(".from-date");let s=!1;function r(){s=!1,o.forEach((e,t)=>{"yes"===e.value&&n[t].value&&(s=!0)}),e.style.display=s?"flex":"none",t.style.display=s?"none":"block"}o.forEach((e,t)=>{e.addEventListener("change",function(){const e=n[t];if("yes"===this.value){e.required=!0,e.disabled=!1;const t=(new Date).toISOString().split("T")[0];e.min=t,e.value||(e.value=t)}else e.required=!1,e.disabled=!0,e.value="";r()})}),n.forEach(e=>{e.addEventListener("change",r)}),e.addEventListener("click",async function(){const e=[];if(o.forEach((t,o)=>{if("yes"===t.value&&n[o].value){const s=t.closest("tr");e.push({employeeNumber:s.querySelector("td:nth-child(3)").textContent,fromDate:n[o].value})}}),0!==e.length)try{const t=await fetch("/api/employees/reinstate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({updates:e})}),o=await t.json();t.ok?(showToast("Employees reinstated successfully","success"),setTimeout(()=>{window.location.reload()},2e3)):showToast(o.message||"Error reinstating employees","error")}catch(e){console.error("Error:",e),showToast("Error reinstating employees","error")}else showToast("No employees selected for reinstatement","error")}),r()});