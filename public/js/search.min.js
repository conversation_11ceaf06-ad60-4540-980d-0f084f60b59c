document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("searchInput"),t=document.getElementById("searchIcon");let n;const s=async e=>{try{const t=await fetch(`/search-employees?q=${encodeURIComponent(e)}`);if(!t.ok)throw ,,new Error(`HTTP error! status: ${t.status}`);const n=t.headers.get("content-type");if(!n||!n.includes("application/json"))throw new TypeError("Oops, we haven't got JSON!");const s=await t.json();s.length>0?o(s):r()}catch(e){console.error("Error searching employees:",e),console.error("Error details:",e.message),r()}},o=t=>{let n=document.getElementById("searchResults");n||(n=document.createElement("div"),n.id="searchResults",n.className="search-results",e.parentNode.appendChild(n)),n.innerHTML="",t.forEach(e=>{const t=document.createElement("div");t.className="search-result-item",t.innerHTML=`\n        <span>${e.firstName} ${e.lastName}</span>\n        <span>${e.employeeNumber}</span>\n        <span>${e.costCentre}</span>\n      `,t.style.cursor="pointer",t.addEventListener("click",()=>{window.location.href=`/employeeProfile/${e._id}`}),n.appendChild(t)}),n.style.display="block"},r=()=>{const e=document.getElementById("searchResults");e&&(e.style.display="none")};e.addEventListener("input",e=>{clearTimeout(n);const t=e.target.value.trim();t.length>=2?n=setTimeout(()=>s(t),300):r()}),t.addEventListener("click",()=>{const t=e.value.trim();t.length>=2&&s(t)}),document.addEventListener("click",e=>{e.target.closest(".search-container")||r()})});