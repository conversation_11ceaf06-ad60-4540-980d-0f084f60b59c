document.addEventListener("DOMContentLoaded",function(){if("true"===document.body.getAttribute("data-payment-restricted")){let t=window.paymentRestrictionData||{},e=t.daysOverdue||60,o=t.invoiceNumber||"N/A",n=t.invoiceAmount||"0.00",r=document.createElement("div");r.className="payment-restriction-banner",r.innerHTML=`
    <div class="payment-restriction-content">
      <i class="ph ph-warning-circle"></i>
      <div class="restriction-message">
        <p><strong>Account Restricted:</strong> Payment is ${e} days overdue.</p>
        <p>Invoice ${o} (R${n}) is overdue. <a href="/billing/preferences">Make payment now</a> to restore full access.</p>
      </div>
      <button class="restriction-close" onclick="this.parentElement.parentElement.style.display='none'">
        <i class="ph ph-x"></i>
      </button>
    </div>
  `,document.body.insertBefore(r,document.body.firstChild);var i=document.createElement("style");function a(){document.querySelectorAll(".payment-restriction-toast").forEach(t=>t.remove());let t=document.createElement("div");t.className="payment-restriction-toast",t.innerHTML=`
      <div class="toast-content">
        <i class="ph ph-warning-circle toast-icon"></i>
        <div class="toast-message">
          <div class="toast-title">Account Restricted</div>
          <p class="toast-description">Payment is ${e} days overdue. Please make payment to restore full access.</p>
        </div>
      </div>
    `,document.body.appendChild(t),setTimeout(()=>{t.parentNode&&(t.style.animation="slideInRight 0.3s ease-out reverse",setTimeout(()=>t.remove(),300))},4e3)}i.textContent=`
    .payment-restriction-banner {
      background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
      border-bottom: 2px solid #FECACA;
      padding: 1rem;
      color: #991B1B;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .payment-restriction-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
    }
    
    .payment-restriction-content i.ph-warning-circle {
      font-size: 2rem;
      color: #DC2626;
      flex-shrink: 0;
    }
    
    .restriction-message {
      flex-grow: 1;
    }
    
    .restriction-message p {
      margin: 0;
      font-size: 0.875rem;
      line-height: 1.4;
    }
    
    .restriction-message p:first-child {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    .restriction-message a {
      color: #DC2626;
      font-weight: 600;
      text-decoration: underline;
      transition: color 0.2s ease;
    }
    
    .restriction-message a:hover {
      color: #B91C1C;
    }
    
    .restriction-close {
      background: none;
      border: none;
      color: #991B1B;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 0.25rem;
      transition: background-color 0.2s ease;
    }
    
    .restriction-close:hover {
      background-color: rgba(220, 38, 38, 0.1);
    }
    
    .restriction-close i {
      font-size: 1.25rem;
    }
    
    /* Make all form elements look disabled */
    input:not([type="submit"]):not([type="button"]):not([data-payment-action]), 
    select:not([data-payment-action]), 
    textarea:not([data-payment-action]) {
      background-color: #F9FAFB !important;
      border-color: #E5E7EB !important;
      color: #6B7280 !important;
      cursor: not-allowed !important;
      opacity: 0.75 !important;
    }
    
    /* Style buttons to look disabled except for payment-related buttons */
    button:not([data-payment-action]), 
    .btn:not([data-payment-action]), 
    [type="submit"]:not([data-payment-action]), 
    [type="button"]:not([data-payment-action]),
    .action-button:not([data-payment-action]) {
      background-color: #F3F4F6 !important;
      border-color: #E5E7EB !important;
      color: #9CA3AF !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
    }
    
    /* Keep payment-related buttons functional */
    [data-payment-action] {
      opacity: 1 !important;
      pointer-events: auto !important;
      cursor: pointer !important;
    }
    
    /* Add visual cue on hover for restricted elements */
    .restricted-element:hover::after {
      content: "Account restricted - payment ${e} days overdue";
      position: absolute;
      bottom: calc(100% + 8px);
      left: 50%;
      transform: translateX(-50%);
      background-color: #1F2937;
      color: white;
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      white-space: nowrap;
      z-index: 50;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    /* Toast notification styles */
    .payment-restriction-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
      border: 1px solid #FECACA;
      border-left: 4px solid #DC2626;
      color: #991B1B;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      z-index: 1001;
      max-width: 400px;
      animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .payment-restriction-toast .toast-content {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
    }
    
    .payment-restriction-toast .toast-icon {
      font-size: 1.25rem;
      color: #DC2626;
      flex-shrink: 0;
      margin-top: 0.125rem;
    }
    
    .payment-restriction-toast .toast-message {
      flex-grow: 1;
    }
    
    .payment-restriction-toast .toast-title {
      font-weight: 600;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }
    
    .payment-restriction-toast .toast-description {
      font-size: 0.8125rem;
      line-height: 1.4;
      margin: 0;
    }
  `,document.head.appendChild(i),document.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea').forEach(t=>{t.hasAttribute("data-payment-action")||(t.setAttribute("disabled","disabled"),t.setAttribute("readonly","readonly"),t.setAttribute("title",`Account restricted - payment ${e} days overdue`),t.classList.add("restricted-element"))}),document.querySelectorAll('button, .btn, [type="submit"], [type="button"], .action-button').forEach(t=>{t.hasAttribute("data-payment-action")||(t.setAttribute("disabled","disabled"),t.setAttribute("title",`Account restricted - payment ${e} days overdue`),t.classList.add("restricted-element"),t.addEventListener("click",function(t){return t.preventDefault(),t.stopPropagation(),a(),!1}))}),document.querySelectorAll('#pay-with-card, .pay-invoice-btn, [href*="/billing/"]').forEach(t=>{t.setAttribute("data-payment-action","true"),t.removeAttribute("disabled"),t.classList.remove("restricted-element")}),document.querySelectorAll('form:not([action*="/billing/"])').forEach(e=>{e.addEventListener("submit",function(t){if(!e.action.includes("/billing/"))return t.preventDefault(),t.stopPropagation(),a(),!1})})}});