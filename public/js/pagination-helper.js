// Function to update pagination links with the current frequency
function updatePaginationLinks(frequency) {
  const paginationLinks = document.querySelectorAll('.pagination-button');
  
  paginationLinks.forEach(function(link) {
    let href = link.getAttribute('href');
    
    // Remove any existing frequency parameter
    href = href.replace(/(&frequency=[^&]*)/, '');
    
    // Add the new frequency parameter
    if (href.includes('?')) {
      href += '&frequency=' + frequency;
    } else {
      href += '?frequency=' + frequency;
    }
    
    link.setAttribute('href', href);
  });
}

// Initialize pagination links when the page loads
document.addEventListener('DOMContentLoaded', function() {
  // Find the active progress tab
  const activeProgressTab = document.querySelector('.progress-tab-link.active');
  
  if (activeProgressTab) {
    // Extract the frequency from the tab ID
    const tabId = activeProgressTab.getAttribute('data-tab');
    const frequency = tabId.replace("progress-", "");
    
    // Update pagination links
    updatePaginationLinks(frequency);
  } else {
    // If no active tab, get the frequency from the active pay run history tab
    const activePayRunTab = document.querySelector('.pay-run-history-tab.active');
    if (activePayRunTab) {
      const frequency = activePayRunTab.id.replace("pay-run-history-", "");
      updatePaginationLinks(frequency);
    }
  }
});
