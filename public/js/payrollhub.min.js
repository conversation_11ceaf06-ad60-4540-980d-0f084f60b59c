function n(){var e=document.getElementById("loadingOverlay"),e=(e||((e=document.createElement("div")).id="loadingOverlay",e.className="xpay-loading-overlay xpay-loading-overlay",e.innerHTML='\n      <div class="xpay-loading-spinner"></div>\n      <div class="xpay-loading-text">Processing...</div>\n    ',document.body.appendChild(e)),document.getElementById("notification")),e=(e||((e=document.createElement("div")).id="notification",e.className="xpay-notification",document.body.appendChild(e)),document.getElementById("xpay-bank-file-modal")),e=(e||((e=document.createElement("div")).id="xpay-bank-file-modal",e.className="xpay-modal",e.innerHTML='\n      <div class="xpay-modal-content">\n        <div class="xpay-modal-header">\n          <h3>Generate Bank File</h3>\n          <button class="close-button" onclick="closeBankFileModal()">×</button>\n        </div>\n        <div class="xpay-modal-body">\n          <p>Are you ready to generate the bank file for this pay run?</p>\n          <p class="modal-note">\n            This will create a file that you can upload to your bank for\n            processing payments.\n          </p>\n        </div>\n        <div class="xpay-modal-actions">\n          <button\n            class="btn btn-secondary cancel-button"\n            onclick="closeBankFileModal()"\n          >\n            Cancel\n          </button>\n          <button\n            class="btn btn-primary confirm-button"\n            id="generateBankFileBtn"\n          >\n            Generate\n          </button>\n        </div>\n      </div>\n    ',document.body.appendChild(e)),document.getElementById("missingBankDetailsModal"));e||((e=document.createElement("div")).id="missingBankDetailsModal",e.className="xpay-modal",e.innerHTML='\n      <div class="xpay-modal-content">\n        <div class="xpay-modal-header">\n          <h2>Missing Bank Details</h2>\n          <span class="xpay-close" onclick="document.getElementById(\'missingBankDetailsModal\').style.display=\'none\'">&times;</span>\n        </div>\n        <div class="xpay-modal-body">\n          <p id="missingEmployeesDetails">Some employees are missing bank details.</p>\n        </div>\n      </div>\n    ',document.body.appendChild(e)),document.getElementById("loadingOverlay"),document.getElementById("notification"),document.getElementById("xpay-bank-file-modal"),document.getElementById("missingBankDetailsModal")}function a(){document.querySelectorAll(".select-all-payslips").forEach(e=>{e.addEventListener("change",function(){this.dataset.frequency;var e=this.closest(".frequency-group");e&&(e.querySelectorAll(".payslip-checkbox-input").forEach(e=>{e.checked=this.checked}),e=e.querySelector(".bulk-finalize-btn"))&&(e.style.display=this.checked?"block":"none")})}),document.querySelectorAll(".payslip-checkbox-input").forEach(e=>{e.addEventListener("change",function(){this.dataset.frequency;var e,t,n,a=this.closest(".frequency-group");a&&(n=a.querySelector(".select-all-payslips"),t=a.querySelectorAll(".payslip-checkbox-input"),e=Array.from(t).every(e=>e.checked),t=Array.from(t).some(e=>e.checked),n&&(n.checked=e,n.indeterminate=!e&&t),n=a.querySelector(".bulk-finalize-btn"))&&(n.style.display=t?"block":"none")})})}function o(){var e=document.getElementById("select-all");e&&e.addEventListener("change",function(){document.querySelectorAll(".payslip-checkbox:not([disabled])").forEach(e=>{e.checked=this.checked});var e=document.getElementById("finalize-selected");e&&(e.style.display=this.checked?"inline-flex":"none")}),document.querySelectorAll(".select-all-checkbox[data-frequency]").forEach(e=>{e.addEventListener("change",function(){var e=this.dataset.frequency,t=document.getElementById(e+"-payslips-body"),t=(t&&t.querySelectorAll(".payslip-checkbox:not([disabled])").forEach(e=>{e.checked=this.checked}),document.getElementById("finalize-selected-"+e));t&&(t.style.display=this.checked?"inline-flex":"none")})}),document.querySelectorAll(".payslip-checkbox").forEach(e=>{e.addEventListener("change",function(){var e=this.closest("tr");if(e){var n=e.closest("tbody");if(n){var e=n.id.replace("-payslips-body",""),a=document.querySelector(`.select-all-checkbox[data-frequency="${e}"]`);if(a){let e=n.querySelectorAll(".payslip-checkbox:not([disabled])"),t=Array.from(e).every(e=>e.checked);a.checked=t}n=Array.from(document.querySelectorAll(".payslip-checkbox:not([disabled])")).some(e=>e.checked),a=document.getElementById("finalize-selected-"+e);a&&(a.style.display=n?"inline-flex":"none")}}})})}function i(){document.querySelectorAll(".employee-list-container").forEach(e=>{e.style.display="none"}),document.querySelectorAll(".toggle-employees").forEach(e=>{e.setAttribute("aria-expanded","false")}),document.querySelectorAll(".frequency-group").forEach(n=>{let a=n.querySelector(".select-all-payslips").dataset.frequency,o=n.querySelectorAll(".payslip-checkbox-input"),e=n.querySelector(".select-all-payslips");n.querySelector(".bulk-finalize-btn"),o.forEach(e=>{e.addEventListener("change",()=>{l(n,a)})}),e.addEventListener("change",e=>{let t=e.target.checked;o.forEach(e=>{e.checked=t}),l(n,a)}),l(n,a)})}function l(e,t){var n=e.querySelectorAll(".payslip-checkbox-input"),e=e.querySelector(".bulk-finalize-btn"),n=Array.from(n).filter(e=>e.checked).length;e&&(e.style.display=0<n?"block":"none")}function r(){let t=document.getElementById("payrunWarningModal");if(t){let e=t.querySelector(".xpay-close");e&&(e.onclick=function(){t.style.display="none"})}let i=document.getElementById("pendingPayslipsModal");if(i){let e=i.querySelector(".xpay-close"),t=document.getElementById("cancelPayrun"),n=document.getElementById("continuePayrun"),a=document.getElementById("togglePendingList"),o=document.getElementById("pendingPayslipsList");e&&(e.onclick=()=>i.style.display="none"),t&&(t.onclick=()=>i.style.display="none"),n&&(n.onclick=k),a&&o&&(a.onclick=()=>{var e="none"===o.style.display;o.style.display=e?"block":"none",a.innerHTML=e?'<i class="ph ph-x"></i> Hide Unfinalized Payslips':'<i class="ph ph-list"></i> View Unfinalized Payslips'}),window.onclick=function(e){e.target===i&&(i.style.display="none")}}}function s(){let n=document.querySelector(".batch-actions-toolbar"),a=document.getElementById("selectedPeriodsCount");if(a){let t=new Set;document.querySelectorAll(".period-selector").forEach(e=>{e.addEventListener("change",function(){this.checked?t.add(this.dataset.periodId):t.delete(this.dataset.periodId),a.textContent=t.size,n&&(n.style.display=0<t.size?"flex":"none")})})}document.querySelectorAll(".create-payrun-btn").forEach(e=>{e.addEventListener("click",function(e){e.preventDefault(),this.dataset.periodId;var e=this.dataset.frequency,t=this.dataset.startDate,n=this.dataset.endDate,a=parseInt(this.dataset.pendingCount),e=(document.getElementById("modalTitle").textContent="Create Pay Run",document.getElementById("modalPeriodInfo").textContent=e.charAt(0).toUpperCase()+e.slice(1)+` - ${t} to `+n,document.getElementById("warningMessage").innerHTML=0<a?`
          <div class="xpay-alert xpay-alert-warning">
            <strong>Warning:</strong> There are ${a} pending payslips that will be excluded from this pay run. 
            If you confirm you will need to create another pay run later after finalising them.
          </div>
        `:"<p>Please confirm pay run creation.</p>",document.getElementById("payrunWarningModal"));e&&(e.style.display="block")})});var e=document.getElementById("exportExcel"),t=document.getElementById("exportPDF"),o=document.getElementById("exportCSV"),e=(e&&e.addEventListener("click",()=>x("excel")),t&&t.addEventListener("click",()=>x("pdf")),o&&o.addEventListener("click",()=>x("csv")),document.getElementById("batchCreatePayrun"));e&&e.addEventListener("click",async function(){if(confirm("Create pay runs for all selected periods?"))try{var e=await(await fetch("/payrollhub/create-batch-payruns",b("POST",{periodIds:Array.from(selectedPeriods)}))).json();e.success?(h("Pay runs created successfully"),location.reload()):p(e.message||"Error creating pay runs")}catch(e){p("Error creating pay runs")}})}function c(){let e=document.querySelectorAll("[data-period-id]"),n={},i=(e.forEach(e=>{var t=e.getAttribute("data-period-id");t&&(currentPayRunId=t,n[t]||(n[t]=[]),n[t].push(e))}),document.querySelector(".pending-payslips-container")),t;i&&(t=Array.from(i.querySelectorAll(".payslip-item")).reduce((e,t)=>{var n=t.dataset.frequency,a=t.dataset.period;return e[n]||(e[n]={}),e[n][a]||(e[n][a]=[]),e[n][a].push(t),e},{}),i.innerHTML="",Object.entries(t).forEach(([e,t])=>{let l=document.createElement("div");l.className="xpay-frequency-section";var n=document.createElement("div"),a=(n.className="xpay-frequency-header",Object.values(t).flat().length),o=Object.values(t).flat().filter(e=>"true"===e.dataset.isFinalized).length;n.innerHTML=`
      <h3>${e.charAt(0).toUpperCase()+e.slice(1)}</h3>
      <span class="xpay-status-badge">
        ${o}/${a} Finalized
      </span>
    `,l.appendChild(n),Object.entries(t).forEach(([e,t])=>{var n=document.createElement("div"),a=(n.className="xpay-period-section",document.createElement("div")),o=(a.className="xpay-period-header",t.filter(e=>"true"===e.dataset.isFinalized).length);a.innerHTML=`
        <h4>${e}</h4>
        <span class="xpay-status-badge">
          ${o}/${t.length} Finalized
        </span>
      `;let i=document.createElement("div");i.className="xpay-payslips-list",t.forEach(e=>i.appendChild(e.cloneNode(!0))),n.appendChild(a),n.appendChild(i),l.appendChild(n)}),i.appendChild(l)}))}function e(e){var t=window.location.pathname.split("/")[2];window.location.href=`/clients/${t}/payruns/`+e}function t(e){window.location.href=`/payrun/${e}/edit`}function d(n){I("Are you sure you want to finalize this pay run? This action cannot be undone.",async()=>{try{var e=await fetch(`/payrun/${n}/finalize`,b("POST")),t=await v(e);t.success?(h("Pay run finalized successfully"),setTimeout(()=>{window.location.reload()},1500)):p(t.message||"Error finalizing pay run")}catch(e){p("Error finalizing pay run")}},()=>{})}function y(e){window.location.href=`/payrun/${e}/reports/download`}function $(e){window.location.href=`/payrun/${e}/reports`}function j(n){let t=document.getElementById("confirmationModal"),e=document.getElementById("confirmationMessage");if(t&&e)return e.textContent="Are you sure you want to release this pay run to self service?",t.style.display="block",new Promise(e=>{document.getElementById("confirmYes").onclick=async()=>{t.style.display="none";try{let e=await fetch(`/payrun/${n}/release`,b("POST")),t=await v(e);t.success?(h("Pay run released to self-service successfully"),setTimeout(()=>window.location.reload(),1500)):p(t.message||"Error releasing pay run")}catch(e){p("Error releasing pay run")}},document.getElementById("confirmNo").onclick=()=>{t.style.display="none",e(!1)}})}function D(n){let t=document.getElementById("confirmationModal"),e=document.getElementById("confirmationMessage");if(t&&e)return e.textContent="Are you sure you want to delete this pay run? This action cannot be undone.",t.style.display="block",new Promise(e=>{document.getElementById("confirmYes").onclick=async()=>{t.style.display="none";try{let e=await fetch("/payrun/"+n,b("DELETE")),t=await v(e);t.success?(h("Pay run deleted successfully"),setTimeout(()=>window.location.reload(),1500)):p(t.message||"Error deleting pay run")}catch(e){p("Error deleting pay run")}},document.getElementById("confirmNo").onclick=()=>{t.style.display="none",e(!1)}})}async function N(e,t){try{if(m("Generating bank file..."),!t){var n=await fetch(`/payrun/${e}/eft-format`,{method:"GET",headers:{"Content-Type":"application/json","X-CSRF-Token":document.querySelector('meta[name="csrf-token"]').getAttribute("content")}});if(!n.ok){let e=await n.text();throw new Error("Failed to retrieve EFT format: "+e)}t=(await n.json()).format}var a=await T(t).show(),o={"csrf-token":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),"Content-Type":"application/json",Accept:`text/${t},application/json`},i=await fetch(`/payrun/${e}/bank-file/`+t,{method:"POST",headers:o,body:JSON.stringify({actionDate:a})});if(!i.ok){let e=i.headers.get("content-type");if(e&&e.includes("application/json")){let n=await i.json();if("Cannot generate bank file - Missing bank details"!==n.message)throw new Error(n.message||"Failed to generate bank file");{let e=document.getElementById("missingBankDetailsModal"),t=document.getElementById("missingEmployeesDetails");throw e&&t?(t.innerHTML=n.details||"No details available",e.style.display="block"):p("Some employees are missing bank details. Please update their information before generating the bank file."),new Error("Missing Bank Details")}}throw new Error("HTTP error! status: "+i.status)}var l=await i.blob(),r=window.URL.createObjectURL(l),s=document.createElement("a");s.href=r,s.download=`bank-file-${t}-${e}.txt`,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(r),document.body.removeChild(s),h("Bank file downloaded successfully")}catch(e){"Missing Bank Details"!==e.message&&p(e.message)}finally{f()}}function p(e){let t=document.getElementById("notification");t&&(t.textContent=e,t.className="xpay-notification xpay-error",t.style.display="block",t.style.whiteSpace="pre-line",setTimeout(()=>{t.style.display="none"},1e4))}function u(){var e=document.getElementById("xpay-bank-file-modal");e&&(e.style.display="none",e.classList.remove("show"))}function O(e){currentPayRunId=e;e=document.getElementById("xpay-bank-file-modal");e&&(e.style.display="flex",e.classList.add("show"))}async function R(){var e,t=document.querySelector('.action-button.primary[onclick="bulkFinalizePayslips()"]'),t=t?t.dataset.companyCode:null;t?0===(e=Array.from(document.querySelectorAll(".period-selector:checked")).map(e=>e.dataset.periodId)).length?p("Please select at least one payslip to finalize"):confirm(`Are you sure you want to finalize ${e.length} payslip(s)? This action cannot be undone.`)&&(m("Finalizing payslips..."),e=b("POST",{periodIds:e}),fetch(`/clients/${t}/payroll/bulk-finalize`,e).then(e=>e.json()).then(e=>{e.success?(h(e.message),setTimeout(()=>{window.location.reload()},1e3)):p(e.error||"Failed to finalize payslips")}).catch(e=>{p("An unexpected error occurred while finalizing payslips")}).finally(()=>{f()})):p("Company code not found. Please refresh the page.")}function H(e){M(e)}async function R(e){var t=document.querySelector(".frequency-group");if(t){let e=t.querySelectorAll(".payslip-checkbox-input:checked"),a=Array.from(e).map(e=>e.dataset.id);0!==a.length?I(`Are you sure you want to finalize ${a.length} payslip(s)?`,async()=>{try{m("Finalizing payslips...");var e=await Promise.all(a.map(async e=>S(e))),t=e.every(e=>e.success),n=e.filter(e=>!e.success);t?(h("Successfully finalized all selected payslips"),window.location.reload()):p(`Failed to finalize ${n.length} payslip(s). Please try again.`)}catch(e){p("An error occurred while finalizing payslips")}finally{f()}}):p("Please select at least one payslip to finalize")}}function m(e="Processing..."){var t,n=document.getElementById("loadingOverlay");n&&((t=n.querySelector(".xpay-loading-text"))&&(t.textContent=e),n.style.display="flex")}function f(){var e=document.getElementById("loadingOverlay");e&&(e.style.display="none")}function h(e){let t=document.getElementById("notification");t?(t.textContent=e,t.className="xpay-notification xpay-success",t.style.display="block",setTimeout(()=>{t.style.display="none"},3e3)):alert(e)}function g(){var e=document.querySelector('meta[name="csrf-token"]');return e?e.getAttribute("content"):null}function b(e,t=null){e={method:e,headers:{"Content-Type":"application/json","CSRF-Token":g()},credentials:"same-origin"};return t&&(e.body=JSON.stringify(t)),e}function v(e){return e.ok?e.json():e.json().then(e=>{throw new Error(e.message||"API request failed")})}function x(e){var t=Array.from(selectedPeriods);window.location.href=`/payrollhub/export/${e}?periodIds=`+t.join(",")}async function k(){var e=document.getElementById("payrunWarningModal");if(currentPayRunId)try{let e=await fetch("/payrollhub/create-period-payrun",b("POST",{periodId:currentPayRunId})),t=await e.json();t.success?(h("Pay run created successfully"),location.reload()):p(t.message||"Error creating pay run")}catch(e){p("Error creating pay run")}finally{e&&(e.style.display="none")}}function v(e){return e.ok?e.json():e.json().then(e=>{throw new Error(e.message||"API request failed")})}function x(e){var t=Array.from(selectedPeriods);window.location.href=`/payrollhub/export/${e}?periodIds=`+t.join(",")}async function k(){var e=document.getElementById("payrunWarningModal");if(currentPayRunId)try{let e=await fetch("/payrollhub/create-period-payrun",b("POST",{periodId:currentPayRunId})),t=await e.json();t.success?(h("Pay run created successfully"),location.reload()):p(t.message||"Error creating pay run")}catch(e){p("Error creating pay run")}finally{e&&(e.style.display="none")}}async function w(){let e=document.querySelector('button[onclick="createNewPayRun()"]');e&&(e.disabled=!0,setTimeout(()=>e.disabled=!1,2e3));try{let e=document.querySelector('meta[name="company-code"]');if(!e)throw new Error("Company code meta tag not found");let t=e.getAttribute("content");if(!t)throw new Error("Company code not found");m("Preparing new pay run...");let n=Array.from(document.querySelectorAll(".payslip-item")),a=n.filter(e=>"true"===e.dataset.isFinalized).map(e=>e.dataset.id),o=n.filter(e=>"true"!==e.dataset.isFinalized);if(0<o.length){let e=document.getElementById("payrunWarningModal"),n=(e&&e.remove(),document.createElement("div"));n.id="payrunWarningModal",n.className="xpay-modal";var i=document.createElement("div"),l=(i.className="xpay-modal-content",document.createElement("div")),r=(l.className="xpay-modal-header",l.innerHTML='\n        <h2>Unfinalized Payslips Warning</h2>\n        <button class="xpay-modal-close">&times;</button>\n      ',document.createElement("div")),s=(r.className="xpay-modal-body",r.innerHTML=`
        <p>You have ${o.length} unfinalized payslip(s). These cannot be included in a pay run.</p>
        <div class="xpay-unfinalized-list">
          ${o.map(e=>{var t=e.closest(".employee-item");return`
              <div class="xpay-unfinalized-payslip">
                <span class="xpay-employee-name">${t?t.querySelector(".employee-name").textContent:"Unknown Employee"}</span>
                <button class="xpay-modal-btn primary" data-payslip-id="${e.dataset.id}">
                  Finalize Now
                </button>
              </div>
            `}).join("")}
        </div>
      `,document.createElement("div")),c=(s.className="xpay-modal-footer",s.innerHTML=`
        <button class="xpay-modal-btn secondary">Cancel</button>
        ${0<a.length?`
          <button class="xpay-modal-btn primary" id="proceedWithFinalizedBtn">
            Create Pay Run with ${a.length} Finalized Payslips
          </button>
        `:""}
      `,i.appendChild(l),i.appendChild(r),i.appendChild(s),n.appendChild(i),document.body.appendChild(n),l.querySelector(".xpay-modal-close")),d=s.querySelector(".xpay-modal-btn.secondary"),y=s.querySelector("#proceedWithFinalizedBtn");c.addEventListener("click",()=>{n.classList.remove("show"),setTimeout(()=>n.remove(),300)}),d.addEventListener("click",()=>{n.classList.remove("show"),setTimeout(()=>n.remove(),300)}),y&&y.addEventListener("click",()=>{n.classList.remove("show"),setTimeout(()=>{n.remove(),E(t,a.join(","))},300)}),r.querySelectorAll(".xpay-modal-btn[data-payslip-id]").forEach(t=>{t.addEventListener("click",async()=>{let e=t.dataset.payslipId;n.classList.remove("show"),setTimeout(async()=>{n.remove(),await S(e),w()},300)})}),requestAnimationFrame(()=>{n.classList.add("show")}),f()}else 0===a.length?(p("No finalized payslips found. Please finalize payslips first."),f()):await E(t,a.join(","))}catch(e){p(e.message||"Error creating pay run")}finally{f()}}async function E(e,t){try{m("Creating pay run...");var n=await fetch(`/clients/${e}/payruns`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({periodIds:t.split(","),releaseToSelfService:!1})}),a=await n.json();if(n.ok){if(!a.success)throw new Error(a.error||"Failed to create pay run");h("Pay run created successfully"),setTimeout(()=>location.reload(),1e3)}else{let e=a.error||"Failed to create pay run";p(e=a.details&&a.details.existingPayRun?(e+=`
Existing pay run found for period: `+a.details.existingPayRun.period)+`
Status: `+a.details.existingPayRun.status:e)}}catch(e){p(e.message||"Failed to create pay run")}finally{f()}}async function S(n){document.querySelector(`.payslip-item[data-id="${n}"]`);try{m("Finalizing payslip...");let e=await fetch(`/payslip/${n}/finalize`,{...b("POST")}),t=await e.json();if(!e.ok)throw new Error(t.error||"Failed to finalize payslip");var a=document.querySelector(`.payslip-item[data-id="${n}"]`);if(a){a.dataset.isFinalized="true";let e=a.querySelector(".status-badge");e&&(e.textContent="Finalized",e.classList.remove("pending"),e.classList.add("finalized")),a.classList.remove("pending"),a.classList.add("finalized")}h("Payslip finalized successfully"),q(),setTimeout(()=>{window.location.reload()},1e3)}catch(n){p(n.message)}finally{f()}}function q(){var e,t,n=document.querySelector(".pending-payslips-card");n&&(e=n.querySelectorAll(".payslip-item:not(.finalized)"),(t=n.querySelector(".stat-item .stat-value"))&&(t.textContent=e.length),t=n.querySelector(".empty-state"),n=n.querySelector(".pending-payslips"),t)&&n&&(0===e.length?(n.style.display="none",t.style.display="block"):(n.style.display="block",t.style.display="none")),c()}function R(){var t=Array.from(document.querySelectorAll(".payslip-checkbox:checked"));if(0===t.length)p("Please select payslips to finalize");else{let e=t.map(e=>e.getAttribute("data-payslip-id"));confirm(`Are you sure you want to finalize ${e.length} payslip(s)?`)&&Promise.all(e.map(async e=>S(e))).then(()=>{h(`Successfully finalized ${e.length} payslips`),setTimeout(()=>{window.location.reload()},1e3)}).catch(e=>{p("Error finalizing payslips")})}}function L(n){document.querySelectorAll(".progress-step").forEach((e,t)=>{t+1<n?(e.classList.remove("active","pending"),e.classList.add("completed")):t+1===n?(e.classList.remove("completed","pending"),e.classList.add("active")):(e.classList.remove("completed","active"),e.classList.add("pending"))})}function C(){var e,t,n=document.getElementById("confirmationModal");return n||((n=document.createElement("div")).id="confirmationModal",Object.assign(n.style,{position:"fixed",top:"0",left:"0",width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, 0.5)",display:"none",zIndex:"9999",justifyContent:"center",alignItems:"center"}),e=document.createElement("div"),Object.assign(e.style,{backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",width:"100%",maxWidth:"400px",padding:"20px",position:"relative",maxHeight:"90%",overflowY:"auto"}),(t=document.createElement("div")).innerHTML='\n    <div style="display: flex; align-items: center; margin-bottom: 20px;">\n      <div style="\n        background-color: #FEF3C7; \n        color: #D97706; \n        border-radius: 9999px; \n        padding: 10px; \n        margin-right: 15px; \n        display: flex; \n        align-items: center; \n        justify-content: center;\n      ">\n        <svg style="width: 24px; height: 24px;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />\n        </svg>\n      </div>\n      <h3 style="\n        font-size: 18px; \n        font-weight: 600; \n        color: #111827;\n        margin: 0;\n      ">\n        Confirm Action\n      </h3>\n    </div>\n    \n    <p id="confirmationMessage" style="\n      font-size: 14px; \n      color: #6B7280; \n      margin-bottom: 20px;\n      padding-left: 50px;\n    ">\n      Are you sure you want to proceed?\n    </p>\n    \n    <div style="\n      display: flex; \n      justify-content: flex-end; \n      gap: 10px;\n    ">\n      <button \n        id="confirmNo" \n        style="\n          padding: 8px 16px;\n          font-size: 14px;\n          font-weight: 500;\n          color: #374151;\n          background-color: #F3F4F6;\n          border-radius: 6px;\n          border: none;\n          cursor: pointer;\n          transition: background-color 0.2s;\n        "\n        onmouseover="this.style.backgroundColor=\'#E5E7EB\'"\n        onmouseout="this.style.backgroundColor=\'#F3F4F6\'"\n      >\n        Cancel\n      </button>\n      <button \n        id="confirmYes" \n        style="\n          padding: 8px 16px;\n          font-size: 14px;\n          font-weight: 500;\n          color: white;\n          background-color: #2563EB;\n          border-radius: 6px;\n          border: none;\n          cursor: pointer;\n          transition: background-color 0.2s;\n        "\n        onmouseover="this.style.backgroundColor=\'#1D4ED8\'"\n        onmouseout="this.style.backgroundColor=\'#2563EB\'"\n      >\n        Confirm\n      </button>\n    </div>\n  ',e.appendChild(t),n.appendChild(e),document.body.appendChild(n)),n}function I(e,t,n){let a=C(),o=a.querySelector("#confirmationMessage"),i=a.querySelector("#confirmYes"),l=a.querySelector("#confirmNo");e&&(o.textContent=e),i.onclick=null,l.onclick=null,i.onclick=()=>{B(),t&&t()},l.onclick=()=>{B(),n&&n()},a.onclick=e=>{e.target===a&&B()},a.style.display="flex"}function B(){var e=document.getElementById("confirmationModal");e&&(e.style.display="none")}function P(){try{var t=document.body.getAttribute("data-company-code")||document.querySelector('meta[name="company-code"]')?.getAttribute("content")||window.location.pathname.split("/")[2];if(t){var n,a,o,i,l,r,s,c,d,y,p,u="payrollhub_state_"+t;if(p=sessionStorage.getItem(u)||localStorage.getItem(u)){let e=JSON.parse(p);e.frequency&&e.startDate&&e.endDate?e.companyCode&&e.companyCode!==t||(e.timestamp&&Date.now()-e.timestamp<864e5?"finalize"===e.lastAction&&(n="progress-"+e.frequency,(a=document.querySelector(`.tab-item[onclick*="${n}"]`))&&(o=document.getElementById(n))&&(document.querySelectorAll(".tab-content").forEach(e=>{e.classList.remove("active")}),document.querySelectorAll(".tab-item").forEach(e=>{e.classList.remove("active")}),o.classList.add("active"),a.classList.add("active")),i=`.period-tracker[data-start-date="${e.startDate}"][data-end-date="${e.endDate}"]`,l=document.querySelector(i))&&(l.style.display="block",(r=l.querySelector(".tracker-status"))&&(r.innerHTML='\n            <span class="status-badge finalized">\n              <i class="ph ph-check-circle"></i>\n              Ready for Pay Run\n            </span>\n          '),2<=(s=l.querySelectorAll(".step")).length)&&(s[0].classList.remove("active"),s[0].classList.add("completed"),(c=s[0].querySelector(".step-status"))&&(c.innerHTML='\n              <span class="progress-status-pill completed">\n                <i class="ph ph-check-circle"></i> Completed\n              </span>\n            '),s[1].classList.remove("completed"),s[1].classList.add("active"),d=s[1].querySelector(".step-actions"))&&(d.innerHTML="",(y=document.createElement("button")).className="btn btn-primary",y.innerHTML="Select",y.addEventListener("click",function(){createPayRun(e.frequency,e.startDate,e.endDate)}),d.appendChild(y)):A(u)):A(u)}}}catch(e){z()}}function A(e){localStorage.removeItem(e),sessionStorage.removeItem(e)}function z(){Object.keys(localStorage).forEach(e=>{e.startsWith("payrollhub_state_")&&localStorage.removeItem(e)}),Object.keys(sessionStorage).forEach(e=>{e.startsWith("payrollhub_state_")&&sessionStorage.removeItem(e)})}function T(e){let i=document.createElement("div"),l=(i.className="xpay-modal-container action-date-modal",i.style.cssText="\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 1000;\n    background-color: rgba(30, 41, 59, 0.5); /* Primary text color with opacity */\n    opacity: 0;\n    pointer-events: none;\n    transition: opacity 0.3s ease;\n  ",document.createElement("div"));return l.className="xpay-modal-content",l.style.cssText="\n    background-color: #ffffff; /* Card background */\n    width: 400px;\n    max-width: 90%;\n    padding: 24px;\n    border-radius: 12px;\n    box-shadow: 0 10px 15px rgba(99, 102, 241, 0.1); /* Primary color shadow */\n    border: 1px solid #e2e8f0; /* Border color */\n    transform: scale(0.7);\n    opacity: 0;\n    transition: all 0.3s ease;\n  ",l.innerHTML=`
    <div class="xpay-modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h2 style="color: #1e293b; font-size: 1.25rem; font-weight: 600; margin: 0;">Generate Bank File</h2>
      <span class="xpay-close" style="cursor: pointer; color: #64748b; font-size: 24px; line-height: 1;">&times;</span>
    </div>
    <div class="xpay-modal-body">
      <div class="eft-details" style="background-color: #f8fafc; padding: 12px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
        <p style="color: #1e293b; margin: 0;"><strong>Bank File Format:</strong> ${e.toUpperCase()}</p>
      </div>
      <div class="form-group" style="margin-bottom: 20px;">
        <label for="actionDate" style="display: block; margin-bottom: 8px; color: #1e293b; font-weight: 500;">Action Date</label>
        <input type="date" id="actionDate" name="actionDate" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 8px; color: #1e293b; background-color: #ffffff;">
      </div>
      <div class="button-group" style="display: flex; justify-content: space-between; gap: 12px;">
        <button class="btn-secondary" style="flex-grow: 1; padding: 12px; background-color: #818cf8; color: white; border: none; border-radius: 8px; font-weight: 500; transition: background-color 0.2s ease;">Cancel</button>
        <button class="btn-primary" style="flex-grow: 1; padding: 12px; background-color: #6366f1; color: white; border: none; border-radius: 8px; font-weight: 500; transition: background-color 0.2s ease;">Generate</button>
      </div>
    </div>
  `,i.appendChild(l),document.body.appendChild(i),{show:()=>new Promise((t,n)=>{i.offsetWidth,i.style.opacity="1",i.style.pointerEvents="auto",l.style.transform="scale(1)",l.style.opacity="1";let e=l.querySelector(".xpay-close"),a=l.querySelector("#actionDate"),o=(a.valueAsDate=new Date,(e=!1)=>{i.style.opacity="0",i.style.pointerEvents="none",l.style.transform="scale(0.7)",l.style.opacity="0",setTimeout(()=>{document.body.removeChild(i),e?t(a.value):n(new Error("Action date selection cancelled"))},300)});e.onclick=()=>o(!1),l.querySelector(".btn-primary").onclick=()=>{a.value?o(!0):p("Please select an action date")}})}}function M(o){let i=document.createElement("div"),l=(i.className="xpay-modal-container payslip-review-modal",i.style.cssText="\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 1000;\n    background-color: rgba(30, 41, 59, 0.5); /* Primary text color with opacity */\n    opacity: 0;\n    pointer-events: none;\n    transition: opacity 0.3s ease;\n  ",document.createElement("div"));return l.className="xpay-modal-content",l.style.cssText="\n    background-color: #ffffff; /* Card background */\n    width: 800px;\n    max-width: 90%;\n    padding: 24px;\n    border-radius: 12px;\n    box-shadow: 0 10px 15px rgba(99, 102, 241, 0.1); /* Primary color shadow */\n    border: 1px solid #e2e8f0; /* Border color */\n    transform: scale(0.7);\n    opacity: 0;\n    transition: all 0.3s ease;\n  ",l.innerHTML=`
    <div class="xpay-modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h2 style="color: #1e293b; font-size: 1.25rem; font-weight: 600; margin: 0;">Review Payslips</h2>
      <span class="xpay-close" style="cursor: pointer; color: #64748b; font-size: 24px; line-height: 1;">&times;</span>
    </div>
    <div class="xpay-modal-body">
      <div class="payslip-review-container">
        <div class="payslip-review-header">
          <h3 style="color: #1e293b; font-size: 1.125rem; font-weight: 600; margin: 0;">${o.charAt(0).toUpperCase()+o.slice(1)} Payslips</h3>
        </div>
        <div class="payslip-review-list">
          <!-- Payslip items will be rendered here -->
        </div>
      </div>
    </div>
  `,i.appendChild(l),document.body.appendChild(i),{show:()=>new Promise((e,t)=>{i.offsetWidth,i.style.opacity="1",i.style.pointerEvents="auto",l.style.transform="scale(1)",l.style.opacity="1";let n=l.querySelector(".xpay-close"),a=l.querySelector(".payslip-review-list");Array.from(document.querySelectorAll(".payslip-item")).filter(e=>e.dataset.frequency===o).forEach(e=>{var t=document.createElement("div");t.className="payslip-review-item",t.innerHTML=`
            <div class="payslip-review-item-header">
              <h4 style="color: #1e293b; font-size: 1rem; font-weight: 600; margin: 0;">${e.dataset.employeeName}</h4>
            </div>
            <div class="payslip-review-item-body">
              <p style="color: #6B7280; font-size: 0.875rem; margin: 0;">${e.dataset.payslipNumber}</p>
            </div>
          `,a.appendChild(t)}),n.onclick=()=>{i.style.opacity="0",i.style.pointerEvents="none",l.style.transform="scale(0.7)",l.style.opacity="0",setTimeout(()=>{document.body.removeChild(i),t(new Error("Payslip review modal closed"))},300)}})}}function F(e){switch(e.toLowerCase()){case"new":return"ph-plus-circle";case"draft":case"pending":return"ph-clock";case"processing":return"ph-spinner";case"finalized":return"ph-check-circle";case"released":return"ph-upload";default:return"ph-circle"}}document.addEventListener("DOMContentLoaded",function(){i();var e,t=document.getElementById("xpay-bank-file-modal");t&&(e=t.querySelector("#generateBankFileBtn"),t=t.querySelector(".close-button"),e&&e.addEventListener("click",function(){generateBankFile(currentPayRunId)}),t)&&t.addEventListener("click",u),n(),r(),s(),a(),o(),c(),P(),document.querySelectorAll(".progress-status-pill").forEach(e=>{let t=e.classList[1],n=e.querySelector("i.ph");n&&(n.classList.forEach(e=>{e.startsWith("ph-")&&n.classList.remove(e)}),n.classList.add(F(t)))})}),document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll('[data-action="review"]').forEach(e=>{e.addEventListener("click",function(){setTimeout(o,500)})})}),document.addEventListener("DOMContentLoaded",function(){var e=document.getElementById("generateBankFileBtn");e&&e.addEventListener("click",function(){generateBankFile(currentPayRunId)})}),document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".finalize-payslip-btn").forEach(e=>{e.addEventListener("click",function(){L(1)})});var e=document.querySelector(".create-payrun-btn"),e=(e&&e.addEventListener("click",function(){L(2)}),document.querySelector(".finalize-payrun-btn")),e=(e&&e.addEventListener("click",function(){L(3)}),document.querySelector(".generate-bankfile-btn"));e&&e.addEventListener("click",function(){L(4)})}),window.getProgressStatusIcon=F;