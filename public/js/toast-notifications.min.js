class ToastNotificationSystem{constructor(){this.container=null,this.toasts=[],this.initialized=!1,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>this.init()):this.init()}init(){if(!this.initialized){if(!document.body)return console.warn("ToastNotificationSystem: document.body not available, retrying..."),void setTimeout(()=>this.init(),10);this.container=document.getElementById("toast-container"),this.container||(this.container=document.createElement("div"),this.container.id="toast-container",this.container.className="toast-container",document.body.appendChild(this.container)),this.addStyles(),this.initialized=!0}}addStyles(){if(document.getElementById("toast-notification-styles"))return;const t=document.createElement("style");t.id="toast-notification-styles",t.textContent="\n            .toast-container {\n                position: fixed;\n                top: 20px;\n                right: 20px;\n                z-index: 9999;\n                pointer-events: none;\n            }\n\n            .toast-notification {\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                min-width: 300px;\n                max-width: 400px;\n                margin-bottom: 10px;\n                padding: 16px 20px;\n                border-radius: 8px;\n                background: #ffffff;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n                transform: translateX(120%);\n                transition: all 0.3s ease-in-out;\n                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n                font-size: 14px;\n                line-height: 1.4;\n                pointer-events: auto;\n                border-left: 4px solid #e2e8f0;\n            }\n\n            .toast-notification.show {\n                transform: translateX(0);\n            }\n\n            .toast-notification.hide {\n                transform: translateX(120%);\n                opacity: 0;\n            }\n\n            .toast-notification .toast-icon {\n                font-size: 20px;\n                flex-shrink: 0;\n            }\n\n            .toast-notification .toast-content {\n                flex: 1;\n                color: #1e293b;\n            }\n\n            .toast-notification .toast-title {\n                font-weight: 600;\n                margin-bottom: 4px;\n                color: #0f172a;\n            }\n\n            .toast-notification .toast-message {\n                color: #64748b;\n                font-size: 13px;\n            }\n\n            .toast-notification .toast-close {\n                background: none;\n                border: none;\n                font-size: 18px;\n                color: #94a3b8;\n                cursor: pointer;\n                padding: 0;\n                margin-left: 8px;\n                flex-shrink: 0;\n                transition: color 0.2s ease;\n            }\n\n            .toast-notification .toast-close:hover {\n                color: #64748b;\n            }\n\n            /* Success Toast */\n            .toast-notification.success {\n                border-left-color: #10b981;\n            }\n\n            .toast-notification.success .toast-icon {\n                color: #10b981;\n            }\n\n            /* Error Toast */\n            .toast-notification.error {\n                border-left-color: #ef4444;\n            }\n\n            .toast-notification.error .toast-icon {\n                color: #ef4444;\n            }\n\n            /* Warning Toast */\n            .toast-notification.warning {\n                border-left-color: #f59e0b;\n            }\n\n            .toast-notification.warning .toast-icon {\n                color: #f59e0b;\n            }\n\n            /* Info Toast */\n            .toast-notification.info {\n                border-left-color: #6366f1;\n            }\n\n            .toast-notification.info .toast-icon {\n                color: #6366f1;\n            }\n\n            /* Responsive Design */\n            @media (max-width: 768px) {\n                .toast-container {\n                    top: 10px;\n                    right: 10px;\n                    left: 10px;\n                }\n\n                .toast-notification {\n                    min-width: auto;\n                    max-width: none;\n                }\n            }\n        ",document.head.appendChild(t)}show(t){if(!this.initialized&&(this.init(),!this.initialized))return void setTimeout(()=>this.show(t),100);const{type:n="info",title:o="",message:e="",duration:i=4e3,closable:s=!0}=t,a=document.createElement("div");a.className=`toast-notification ${n}`;let r=`<i class="ph ${this.getIconClass(n)} toast-icon"></i>`;return r+='<div class="toast-content">',o&&(r+=`<div class="toast-title">${this.escapeHtml(o)}</div>`),e&&(r+=`<div class="toast-message">${this.escapeHtml(e)}</div>`),r+="</div>",s&&(r+='<button class="toast-close" aria-label="Close"><i class="ph ph-x"></i></button>'),a.innerHTML=r,s&&a.querySelector(".toast-close").addEventListener("click",()=>this.remove(a)),this.container.appendChild(a),this.toasts.push(a),setTimeout(()=>{a.classList.add("show")},10),i>0&&setTimeout(()=>{this.remove(a)},i),a}remove(t){t&&t.parentNode&&(t.classList.add("hide"),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t);const n=this.toasts.indexOf(t);n>-1&&this.toasts.splice(n,1)},300))}getIconClass(t){const n={success:"ph-check-circle",error:"ph-x-circle",warning:"ph-warning-circle",info:"ph-info"};return n[t]||n.info}escapeHtml(t){const n=document.createElement("div");return n.textContent=t,n.innerHTML}clearAll(){this.toasts.forEach(t=>this.remove(t))}}const toastSystem=new ToastNotificationSystem;function createGlobalFunctions(){window.showToast=function(t){return"string"==typeof t?toastSystem.show({message:t}):toastSystem.show(t)},window.showSuccessToast=function(t,n=""){return toastSystem.show({type:"success",message:t,title:n})},window.showErrorToast=function(t,n=""){return toastSystem.show({type:"error",message:t,title:n})},window.showWarningToast=function(t,n=""){return toastSystem.show({type:"warning",message:t,title:n})},window.showInfoToast=function(t,n=""){return toastSystem.show({type:"info",message:t,title:n})}}createGlobalFunctions(),"loading"===document.readyState&&document.addEventListener("DOMContentLoaded",createGlobalFunctions),"undefined"!=typeof module&&module.exports&&(module.exports={ToastNotificationSystem,toastSystem});