// Enhanced openProgressTab function
function openProgressTab(evt, tabId) {
  // Hide all tab content
  const tabcontent = document.querySelectorAll(".progress-tab-content");
  for (let i = 0; i < tabcontent.length; i++) {
    tabcontent[i].classList.remove("active");
  }

  // Remove active class from all tab buttons
  const tablinks = document.querySelectorAll(".progress-tab-link");
  for (let i = 0; i < tablinks.length; i++) {
    tablinks[i].classList.remove("active");
  }

  // Show the current tab and add active class to the button
  document.getElementById(tabId).classList.add("active");
  evt.currentTarget.classList.add("active");
  
  // Extract frequency from the tab ID (e.g., "progress-weekly" -> "weekly")
  const frequency = tabId.replace("progress-", "");
  
  // Also update the Pay Run History section to show the corresponding frequency
  // Hide all pay run history tabs
  const payRunHistoryTabs = document.querySelectorAll(".pay-run-history-tab");
  payRunHistoryTabs.forEach((tab) => {
    tab.classList.remove("active");
  });
  
  // Show the corresponding pay run history tab
  const payRunHistoryTab = document.getElementById(`pay-run-history-${frequency}`);
  if (payRunHistoryTab) {
    payRunHistoryTab.classList.add("active");
  }
  
  // Update pagination links to include the frequency
  if (typeof updatePaginationLinks === 'function') {
    updatePaginationLinks(frequency);
  }
}

// Override the original openProgressTab function when the page loads
document.addEventListener('DOMContentLoaded', function() {
  if (typeof window.originalOpenProgressTab === 'undefined') {
    window.originalOpenProgressTab = window.openProgressTab;
    window.openProgressTab = openProgressTab;
  }
});
