class LogoUploader{constructor(e){this.companyCode=e,this.maxFileSize=5242880,this.allowedTypes=["image/jpeg","image/jpg","image/png","image/gif","image/webp"],this.init()}init(){this.setupEventListeners(),this.createUploadInterface()}setupEventListeners(){document.addEventListener("change",e=>{e.target&&"logo-file-input"===e.target.id&&this.handleFileSelect(e.target.files[0])}),document.addEventListener("dragover",e=>{e.target&&e.target.classList.contains("logo-drop-zone")&&(e.preventDefault(),e.target.classList.add("drag-over"))}),document.addEventListener("dragleave",e=>{e.target&&e.target.classList.contains("logo-drop-zone")&&e.target.classList.remove("drag-over")}),document.addEventListener("drop",e=>{if(e.target&&e.target.classList.contains("logo-drop-zone")){e.preventDefault(),e.target.classList.remove("drag-over");const o=e.dataTransfer.files[0];this.handleFileSelect(o)}})}createUploadInterface(){const e=document.getElementById("logo-container");e&&(e.innerHTML='\n      <div class="logo-upload-interface">\n        <div class="logo-preview-container">\n          <img id="logo-preview" src="" alt="Company Logo" style="display: none; max-width: 200px; max-height: 100px; object-fit: contain;">\n          <div id="logo-placeholder" class="logo-placeholder">\n            <i class="fas fa-building"></i>\n            <p>No logo uploaded</p>\n          </div>\n        </div>\n        \n        <div class="logo-upload-controls">\n          <div class="logo-drop-zone" id="logo-drop-zone">\n            <i class="fas fa-cloud-upload-alt"></i>\n            <p>Drag & drop your logo here or <button type="button" class="btn-link" onclick="document.getElementById(\'logo-file-input\').click()">browse files</button></p>\n            <small>Supported formats: JPG, PNG, GIF, WebP (max 5MB)</small>\n          </div>\n          \n          <input type="file" id="logo-file-input" accept="image/*" style="display: none;">\n          \n          <div class="logo-actions" style="display: none;">\n            <button type="button" class="btn btn-primary btn-sm" id="upload-logo-btn">\n              <i class="fas fa-upload"></i> Upload Logo\n            </button>\n            <button type="button" class="btn btn-danger btn-sm" id="delete-logo-btn" style="display: none;">\n              <i class="fas fa-trash"></i> Delete Logo\n            </button>\n          </div>\n        </div>\n        \n        <div class="upload-progress" id="upload-progress" style="display: none;">\n          <div class="progress">\n            <div class="progress-bar" role="progressbar" style="width: 0%"></div>\n          </div>\n          <small class="progress-text">Uploading...</small>\n        </div>\n      </div>\n    ',this.loadExistingLogo())}async loadExistingLogo(){const e=document.querySelector('input[name="logo"]');,,,e&&e.value&&""!==e.value.trim()?(,this.displayLogo(e.value)):}handleFileSelect(e){if(!e)return;const o=this.validateFile(e);o.valid?(this.previewFile(e),document.getElementById("upload-logo-btn").style.display="inline-block",document.getElementById("upload-logo-btn").onclick=()=>this.uploadFile(e)):this.showError(o.message)}validateFile(e){return this.allowedTypes.includes(e.type)?e.size>this.maxFileSize?{valid:!1,message:"File size too large. Maximum size is 5MB."}:{valid:!0}:{valid:!1,message:"Invalid file type. Please select a JPG, PNG, GIF, or WebP image."}}previewFile(e){const o=new FileReader;o.onload=e=>{this.displayLogo(e.target.result)},o.readAsDataURL(e)}displayLogo(e){const o=document.getElementById("logo-preview"),t=document.getElementById("logo-placeholder"),l=document.getElementById("delete-logo-btn");o&&t&&(o.src=e,o.style.display="block",t.style.display="none",l&&(l.style.display="inline-block",l.onclick=()=>this.deleteLogo()))}async uploadFile(e){const o=new FormData;o.append("logo",e),this.showProgress(!0);try{const e=await fetch(`/clients/${this.companyCode}/upload-logo`,{method:"POST",body:o,headers:{"X-Requested-With":"XMLHttpRequest"}}),t=await e.json();if(t.success){this.showSuccess("Logo uploaded successfully!"),this.displayLogo(t.logoUrl);const e=document.querySelector('input[name="logo"]');e?(,e.value=t.logoUrl,):console.error("Hidden logo input field not found!"),document.getElementById("upload-logo-btn").style.display="none"}else this.showError(t.message||"Upload failed")}catch(e){console.error("Upload error:",e),this.showError("Upload failed. Please try again.")}finally{this.showProgress(!1)}}async deleteLogo(){if(confirm("Are you sure you want to delete the company logo?"))try{const e=await fetch(`/clients/${this.companyCode}/delete-logo`,{method:"DELETE",headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json"}}),o=await e.json();o.success?(this.showSuccess("Logo deleted successfully!"),this.clearLogo()):this.showError(o.message||"Delete failed")}catch(e){console.error("Delete error:",e),this.showError("Delete failed. Please try again.")}}clearLogo(){const e=document.getElementById("logo-preview"),o=document.getElementById("logo-placeholder"),t=document.getElementById("delete-logo-btn"),l=document.getElementById("upload-logo-btn");e&&o&&(e.style.display="none",o.style.display="block"),t&&(t.style.display="none"),l&&(l.style.display="none");const s=document.querySelector('input[name="logo"]');s&&(s.value="");const n=document.getElementById("logo-file-input");n&&(n.value="")}showProgress(e){const o=document.getElementById("upload-progress");o&&(o.style.display=e?"block":"none")}showSuccess(e){"function"==typeof showToast?showToast(e,"success"):alert(e)}showError(e){"function"==typeof showToast?showToast(e,"error"):alert(e)}}document.addEventListener("DOMContentLoaded",function(){const e=window.location.pathname.split("/")[2];e&&(window.logoUploader=new LogoUploader(e))});