document.addEventListener("DOMContentLoaded",function(){var t;"true"===document.body.getAttribute("data-account-restricted")&&((t=document.createElement("div")).className="account-restriction-banner",t.innerHTML='\n    <div class="account-restriction-content">\n      <i class="ph ph-warning-circle"></i>\n      <div class="restriction-message">\n        <p><strong>Account Restricted:</strong> Your trial period has expired.</p>\n        <p>The system is in read-only mode. <a href="/billing/preferences">Update your billing information</a> to restore full access.</p>\n      </div>\n    </div>\n  ',document.body.insertBefore(t,document.body.firstChild),(t=document.createElement("style")).textContent='\n    .account-restriction-banner {\n      background-color: #FEF2F2;\n      border-bottom: 1px solid #FECACA;\n      padding: 0.75rem 1rem;\n      color: #991B1B;\n      position: sticky;\n      top: 0;\n      z-index: 50;\n    }\n    \n    .account-restriction-content {\n      display: flex;\n      align-items: center;\n      gap: 0.75rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n    \n    .account-restriction-content i {\n      font-size: 1.5rem;\n      color: #DC2626;\n    }\n    \n    .restriction-message p {\n      margin: 0;\n      font-size: 0.875rem;\n    }\n    \n    .restriction-message a {\n      color: #DC2626;\n      font-weight: 600;\n      text-decoration: underline;\n    }\n    \n    /* Make all form elements look disabled */\n    input:not([type="submit"]):not([type="button"]), \n    select, \n    textarea {\n      background-color: #F3F4F6 !important;\n      border-color: #D1D5DB !important;\n      color: #6B7280 !important;\n      cursor: not-allowed !important;\n      opacity: 0.75 !important;\n    }\n    \n    /* Style buttons to look disabled except for billing-related buttons */\n    button:not([data-billing-action]), \n    .btn:not([data-billing-action]), \n    [type="submit"]:not([data-billing-action]), \n    [type="button"]:not([data-billing-action]),\n    .action-button:not([data-billing-action]) {\n      background-color: #E5E7EB !important;\n      border-color: #D1D5DB !important;\n      color: #9CA3AF !important;\n      cursor: not-allowed !important;\n      pointer-events: none !important;\n    }\n    \n    /* Add visual cue on hover */\n    table tr:hover {\n      position: relative;\n    }\n    \n    table tr:hover:after {\n      content: "System in read-only mode";\n      position: absolute;\n      bottom: calc(100% + 5px);\n      left: 50%;\n      transform: translateX(-50%);\n      background-color: #1F2937;\n      color: white;\n      padding: 0.25rem 0.5rem;\n      border-radius: 0.25rem;\n      font-size: 0.75rem;\n      white-space: nowrap;\n      z-index: 10;\n    }\n  ',document.head.appendChild(t),document.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea').forEach(t=>{t.setAttribute("disabled","disabled"),t.setAttribute("readonly","readonly"),t.setAttribute("title","System is in read-only mode")}),document.querySelectorAll('button:not([data-billing-action]), .btn:not([data-billing-action]), [type="submit"]:not([data-billing-action]), [type="button"]:not([data-billing-action]), .action-button:not([data-billing-action])').forEach(t=>{t.setAttribute("disabled","disabled"),t.setAttribute("title","System is in read-only mode"),t.addEventListener("click",function(t){t.preventDefault(),t.stopPropagation();let n=document.createElement("div");return n.className="restriction-tooltip",n.textContent="System is in read-only mode. Please update your billing information.",n.style.cssText="\n        position: absolute;\n        top: -40px;\n        left: 50%;\n        transform: translateX(-50%);\n        background-color: #1F2937;\n        color: white;\n        padding: 0.5rem;\n        border-radius: 0.25rem;\n        font-size: 0.75rem;\n        white-space: nowrap;\n        z-index: 50;\n      ",this.style.position="relative",this.appendChild(n),setTimeout(()=>{n.remove()},3e3),!1})}),document.querySelectorAll("#pay-with-card, .pay-invoice-btn").forEach(t=>{t.setAttribute("data-billing-action","true")}),document.querySelectorAll('form:not([action^="/billing/"])').forEach(n=>{n.addEventListener("submit",function(t){if(!n.action.includes("/billing/"))return t.preventDefault(),t.stopPropagation(),alert("Your account is currently in read-only mode. Please update your billing information to enable form submissions."),!1})}))});