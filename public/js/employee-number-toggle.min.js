document.addEventListener("DOMContentLoaded",function(){let e=document.getElementById("employeeNumberMode"),r=document.getElementById("autoGenerateInfo"),t=document.querySelector('meta[name="csrf-token"]'),l=t?t.getAttribute("content"):null;if(e){l;let a=e.closest(".toggle-container"),o=a?a.parentElement:null;function m(e){var t=document.getElementById("companyEmployeeNumber"),n=t?t.closest(".form-group"):null;if("manual"===e){if(t)n.style.display="block",t.required=!0;else{let e=document.createElement("div");e.className="form-group",e.innerHTML='\n                    <label for="companyEmployeeNumber">\n                        <i class="ph ph-hash"></i>\n                        Employee Number *\n                    </label>\n                    <div class="input-wrapper">\n                        <input\n                            type="text"\n                            id="companyEmployeeNumber"\n                            name="companyEmployeeNumber"\n                            placeholder="Enter employee number"\n                            pattern="[A-Za-z0-9\\-]+"\n                            title="Can contain letters, numbers and hyphens"\n                            required\n                        />\n                    </div>\n                ',a.insertAdjacentElement("afterend",e)}r&&(r.style.display="none")}else n&&(n.style.display="none",t)&&(t.required=!1,t.value=""),r&&(r.style.display="block")}function c(e,t){let n="error"===t?"alert alert-danger":"alert alert-success",a=document.createElement("div");a.className=n,a.textContent=e,o.insertBefore(a,o.firstChild),setTimeout(()=>{a.remove()},3e3)}o&&(e&&e.addEventListener("change",async function(){var e=this.checked?"automatic":"manual";try{var t={"Content-Type":"application/json"},n=(l&&(t["CSRF-Token"]=l),await fetch("/api/settings/employee-number-mode",{method:"POST",headers:t,body:JSON.stringify({mode:e})})),a=await n.json();if(!n.ok)throw new Error(a.message||"Failed to update employee number mode");m(e),c("Employee number mode updated to "+("automatic"==e?"automatic generation":"manual entry"),"success")}catch(e){c(e.message||"Failed to update employee number mode","error"),this.checked=!this.checked}}),m(e.checked?"automatic":"manual"))}});