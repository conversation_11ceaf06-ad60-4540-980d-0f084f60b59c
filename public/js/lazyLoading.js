/**
 * Lazy Loading Utility for PandaPayroll
 * Implements progressive loading for images, components, and content
 */

class LazyLoader {
  constructor() {
    this.imageObserver = null;
    this.componentObserver = null;
    this.init();
  }

  init() {
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    this.setupImageLazyLoading();
    this.setupComponentLazyLoading();
    this.setupContentLazyLoading();
  }

  /**
   * Set up lazy loading for images
   */
  setupImageLazyLoading() {
    // Check for native lazy loading support
    if ('loading' in HTMLImageElement.prototype) {
      // Native lazy loading is supported
      this.enableNativeLazyLoading();
    } else {
      // Fallback to Intersection Observer
      this.enableIntersectionObserverLazyLoading();
    }
  }

  enableNativeLazyLoading() {
    const images = document.querySelectorAll('img[data-lazy]');
    images.forEach(img => {
      img.loading = 'lazy';
      img.src = img.dataset.lazy;
      img.removeAttribute('data-lazy');
    });
  }

  enableIntersectionObserverLazyLoading() {
    if (!('IntersectionObserver' in window)) {
      // Fallback for older browsers
      this.loadAllImages();
      return;
    }

    this.imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          this.loadImage(img);
          this.imageObserver.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px 0px', // Start loading 50px before the image comes into view
      threshold: 0.01
    });

    const lazyImages = document.querySelectorAll('img[data-lazy]');
    lazyImages.forEach(img => this.imageObserver.observe(img));
  }

  loadImage(img) {
    const src = img.dataset.lazy;
    if (!src) return;

    // Create a new image to preload
    const imageLoader = new Image();
    imageLoader.onload = () => {
      img.src = src;
      img.classList.add('loaded');
      img.removeAttribute('data-lazy');
    };
    imageLoader.onerror = () => {
      img.classList.add('error');
      console.warn('Failed to load image:', src);
    };
    imageLoader.src = src;
  }

  loadAllImages() {
    const lazyImages = document.querySelectorAll('img[data-lazy]');
    lazyImages.forEach(img => this.loadImage(img));
  }

  /**
   * Set up lazy loading for components
   */
  setupComponentLazyLoading() {
    if (!('IntersectionObserver' in window)) return;

    this.componentObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const component = entry.target;
          this.loadComponent(component);
          this.componentObserver.unobserve(component);
        }
      });
    }, {
      rootMargin: '100px 0px', // Start loading 100px before component comes into view
      threshold: 0.1
    });

    const lazyComponents = document.querySelectorAll('[data-lazy-component]');
    lazyComponents.forEach(component => this.componentObserver.observe(component));
  }

  async loadComponent(element) {
    const componentType = element.dataset.lazyComponent;
    const componentData = element.dataset.componentData;

    try {
      element.classList.add('loading');
      
      switch (componentType) {
        case 'chart':
          await this.loadChart(element, componentData);
          break;
        case 'table':
          await this.loadTable(element, componentData);
          break;
        case 'calendar':
          await this.loadCalendar(element, componentData);
          break;
        case 'form':
          await this.loadForm(element, componentData);
          break;
        default:
          await this.loadGenericComponent(element, componentData);
      }

      element.classList.remove('loading');
      element.classList.add('loaded');
    } catch (error) {
      console.error('Failed to load component:', componentType, error);
      element.classList.remove('loading');
      element.classList.add('error');
    }
  }

  async loadChart(element, data) {
    // Load chart libraries if not already loaded
    if (!window.ApexCharts && !window.Chart) {
      await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js');
    }

    // Initialize chart
    const chartConfig = JSON.parse(data || '{}');
    if (window.ApexCharts) {
      const chart = new ApexCharts(element, chartConfig);
      chart.render();
    }
  }

  async loadTable(element, data) {
    // Load table data via AJAX
    const tableConfig = JSON.parse(data || '{}');
    const response = await fetch(tableConfig.url);
    const tableData = await response.json();
    
    // Render table
    element.innerHTML = this.renderTable(tableData);
  }

  async loadCalendar(element, data) {
    // Load FullCalendar if not already loaded
    if (!window.FullCalendar) {
      await this.loadScript('https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js');
    }

    // Initialize calendar
    const calendarConfig = JSON.parse(data || '{}');
    const calendar = new FullCalendar.Calendar(element, calendarConfig);
    calendar.render();
  }

  async loadForm(element, data) {
    // Load form via AJAX
    const formConfig = JSON.parse(data || '{}');
    const response = await fetch(formConfig.url);
    const formHTML = await response.text();
    
    element.innerHTML = formHTML;
    
    // Initialize form functionality
    this.initializeFormHandlers(element);
  }

  async loadGenericComponent(element, data) {
    // Generic component loading
    const config = JSON.parse(data || '{}');
    if (config.url) {
      const response = await fetch(config.url);
      const content = await response.text();
      element.innerHTML = content;
    }
  }

  /**
   * Set up lazy loading for content sections
   */
  setupContentLazyLoading() {
    // Defer loading of non-critical content
    this.deferNonCriticalContent();
    
    // Set up progressive disclosure for large lists
    this.setupProgressiveDisclosure();
  }

  deferNonCriticalContent() {
    const nonCriticalElements = document.querySelectorAll('[data-defer-load]');
    
    // Load after a short delay to prioritize critical content
    setTimeout(() => {
      nonCriticalElements.forEach(element => {
        element.classList.remove('deferred');
        element.classList.add('loaded');
      });
    }, 100);
  }

  setupProgressiveDisclosure() {
    const progressiveLists = document.querySelectorAll('[data-progressive-list]');
    
    progressiveLists.forEach(list => {
      const items = list.querySelectorAll('[data-list-item]');
      const showMore = list.querySelector('[data-show-more]');
      const initialCount = parseInt(list.dataset.initialCount) || 10;
      
      // Hide items beyond initial count
      items.forEach((item, index) => {
        if (index >= initialCount) {
          item.style.display = 'none';
        }
      });
      
      // Set up show more functionality
      if (showMore && items.length > initialCount) {
        showMore.style.display = 'block';
        showMore.addEventListener('click', () => {
          items.forEach(item => {
            item.style.display = '';
          });
          showMore.style.display = 'none';
        });
      }
    });
  }

  /**
   * Utility methods
   */
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  renderTable(data) {
    // Simple table renderer
    if (!data.headers || !data.rows) return '<p>No data available</p>';
    
    let html = '<table class="table"><thead><tr>';
    data.headers.forEach(header => {
      html += `<th>${header}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    data.rows.forEach(row => {
      html += '<tr>';
      row.forEach(cell => {
        html += `<td>${cell}</td>`;
      });
      html += '</tr>';
    });
    
    html += '</tbody></table>';
    return html;
  }

  initializeFormHandlers(container) {
    // Initialize form validation and submission handlers
    const forms = container.querySelectorAll('form');
    forms.forEach(form => {
      // Add form handling logic here
      console.log('Form initialized:', form);
    });
  }
}

// Initialize lazy loader
const lazyLoader = new LazyLoader();

// Export for use in other scripts
window.LazyLoader = LazyLoader;
