function $(){var e=document.querySelector('meta[name="csrf-token"]');return e?e.getAttribute("content"):null}function l(e,t=null){e={method:e,headers:{"Content-Type":"application/json","CSRF-Token":$()},credentials:"same-origin"};return t&&(e.body=JSON.stringify(t)),e}async function e(e){if(companyCode)try{var t=document.querySelector(`.frequency-group:has(.select-all-payslips[data-frequency="${e}"])`);if(t){var a=t.querySelectorAll(".payslip-checkbox-input:checked"),n=(0<a.length&&E(a[0],"First selected checkbox"),Array.from(a).map(e=>{var t=e.closest(".payslip-item"),a=e.dataset.id||t.dataset.id,n=t.querySelector(".employee-info").querySelector(".period"),i=e.dataset.employeeId;if(!i){E(t,"Payslip item row");let e=t.querySelector('.action-icon[onclick^="viewPayslip"]');if(e){let t=e.getAttribute("onclick");if(t){let e=t.match(/viewPayslip\(['"]([^'"]+)['"]\)/);e&&e[1]}}}let o=e.dataset.endDate;if(!o&&n){let e=n.textContent.match(/- (\d+ \w+ \d+)/);e&&e[1]&&(o=moment(e[1],"D MMM YYYY").format("YYYY-MM-DD"))}return{payslipId:a,employeeId:i,periodEndDate:o}}));if(0===n.length)D({type:"error",message:"Please select at least one payslip to finalize",duration:5e3});else if(n.some(e=>!e.employeeId||!e.periodEndDate))D({type:"error",message:"Missing employee ID or period end date for some payslips. Please ensure data attributes are correctly set.",duration:5e3});else{P("Finalizing payslips...");var i=n.map(async t=>{try{var a=await fetch(`/clients/${companyCode}/employeeProfile/${t.employeeId}/finalize`,l("POST",{employeeId:t.employeeId,currentPeriodEndDate:t.periodEndDate}));if(a.redirected||302===a.status)return{payslipId:t.payslipId,employeeId:t.employeeId,success:!0,status:a.status,message:"Successfully finalized"};let e={};try{e=await a.json()}catch(t){}return{payslipId:t.payslipId,employeeId:t.employeeId,success:a.ok,status:a.status,message:e.message||(a.ok?"Success":"Failed")}}catch(e){return{payslipId:t.payslipId,employeeId:t.employeeId,success:!1,error:e.message}}}),o=await Promise.all(i),s=o.filter(e=>e.success).length,r=o.length-s;if(0<r&&o.filter(e=>!e.success).forEach(e=>{}),0<s){D({type:"success",message:0<r?`Successfully finalized ${s} payslips. ${r} payslips failed.`:`Successfully finalized ${s} payslips and generated next periods`,duration:5e3});let a=document.querySelector(`.frequency-tracker:has([data-frequency="${e}"])`);a&&a.querySelectorAll(".step").forEach((t,e)=>{if(0===e){t.classList.add("completed"),t.classList.remove("active");let e=t.querySelector(".step-status");e&&(e.textContent="Completed")}else if(1===e){t.classList.add("active"),t.classList.remove("completed");let e=a.querySelector(".create-pay-run-btn");e&&(e.style.display="block")}}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}else D({type:"error",message:"No payslips were successfully finalized",duration:5e3})}}else D({type:"error",message:"No payslips found for frequency: "+e,duration:5e3})}catch(e){D({type:"error",message:e.message||"Failed to finalize payslips",duration:5e3})}finally{F()}else D({type:"error",message:"Company code not found",duration:5e3})}async function L(i,o,s){try{if(!i||"string"!=typeof i||""===i.trim())throw new Error("Invalid frequency provided");if(!o||!s)throw new Error("Start date and end date are required");if(!moment(o).isValid()||!moment(s).isValid())throw new Error("Invalid date format provided");P("Creating pay run...");let t=document.body.dataset.companyCode;if(!(t=t||document.body.getAttribute("data-company-code"))){let e=document.querySelector('meta[name="company-code"]');e&&(t=e.getAttribute("content"))}if(!t){let e=window.location.pathname.match(/\/clients\/([^\/]+)/);e&&e[1]&&(t=e[1])}if(!t||""===t.trim())throw new Error("Company code not found. Please refresh the page.");var r=$();if(!r||""===r.trim())throw new Error("Security token not found. Please refresh the page.");let e={frequency:i,startDate:o,endDate:s,forceCreate:!1},a=await fetch(`/clients/${t}/payruns`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":r},body:JSON.stringify(e)}),n;try{n=await a.json()}catch(i){throw new Error("Invalid response from server. Please try again.")}if(!a.ok){if(n.warning)return F(),await A("Unfinalized Payslips",`There are ${n.unfinalizedCount} unfinalized payslips out of ${n.totalCount} total payslips. Do you want to proceed with creating the pay run for only the ${n.finalizedCount} finalized payslips?`,"Proceed","Cancel")?c(i,o,s):void 0;if(n.error&&n.existingPayRun)return F(),void D({type:"error",message:"A pay run already exists for this period.",duration:5e3});let e=n.error||"Failed to create pay run";throw 403===a.status?e="You don't have permission to create pay runs.":404===a.status?e="Pay run service not found. Please contact support.":500===a.status&&(e="Server error occurred. Please try again later."),new Error(e)}F(),D({type:"success",message:"Pay run created successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${t}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(i){F();let e="Failed to create pay run";i.message&&(e=i.message.includes("Company code not found")?"Company information not found. Please refresh the page and try again.":i.message.includes("Security token")?"Security token expired. Please refresh the page and try again.":i.message.includes("Invalid frequency")?"Invalid frequency selected. Please try again.":i.message.includes("Invalid date")?"Invalid date range. Please check the dates and try again.":i.message.includes("Network")?"Network error. Please check your connection and try again.":i.message),D({type:"error",message:e,duration:7e3})}}async function c(e,a,n){try{P("Creating pay run...");let t=document.body.dataset.companyCode;if(!t)throw new Error("Company code not found");var i=await fetch(`/clients/${t}/payruns`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":$()},body:JSON.stringify({frequency:e,startDate:a,endDate:n,forceCreate:!0})}),o=await i.json();if(!i.ok)throw new Error(o.error||"Failed to create pay run");F(),D({type:"success",message:"Pay run created successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${t}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){F(),D({type:"error",message:e.message||"Failed to create pay run",duration:5e3})}}async function t(e){if(companyCode)try{P("Finalizing pay run...");var t=await fetch(`/clients/${companyCode}/payruns/latest/`+e,{method:"GET",headers:{"CSRF-Token":$()}}),a=await t.json();if(!t.ok)throw new Error(a.error||"Failed to get latest pay run");if(!a.payRun||!a.payRun._id)throw new Error("No pay run found to finalize");var n=await fetch(`/clients/${companyCode}/payruns/${a.payRun._id}/finalize`,{method:"POST",headers:{"CSRF-Token":$()}}),i=await n.json();if(!n.ok)throw new Error(i.error||"Failed to finalize pay run");D({type:"success",message:"Successfully finalized pay run",duration:5e3});var o=document.querySelector(`.frequency-tracker:has([data-frequency="${e}"])`);o&&o.querySelectorAll(".step").forEach((t,e)=>{if(e<=1){t.classList.remove("active"),t.classList.add("completed");let e=t.querySelector(".step-status");e&&(e.innerHTML='<i class="ph ph-check-circle"></i> Completed')}}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){D({type:"error",message:e.message||"Failed to finalize pay run",duration:5e3})}finally{F()}else D({type:"error",message:"Company code not found",duration:5e3})}async function a(e,t){try{var a=document.body.getAttribute("data-company-code"),n=await fetch(`/clients/${a}/payruns/${e}/bank-file/standard`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({actionDate:t})});if(!n.ok){let e=await n.json();return 400===n.status&&"Missing bank details"===e.error?void I("Missing Bank Details",e.message||"Some employees have missing bank details",e.missingBankDetails,"warning"):void D(e.error||"Failed to generate bank file","error")}var i=n.headers.get("Content-Disposition"),o=i&&i.match(/filename="(.+)"/),s=o?o[1]:`bank_file_${e}_${moment().format("YYYYMMDD_HHmmss")}.txt`,r=await n.text(),l=new Blob([r],{type:"text/plain"}),c=window.URL.createObjectURL(l),d=document.createElement("a");d.href=c,d.download=s,document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(c),document.body.removeChild(d),D("Bank file generated successfully","success")}catch(e){D("Failed to generate bank file","error")}}async function n(e){if(companyCode)try{var t=await fetch(`/clients/${companyCode}/payruns/${e}/upload-to-bank`,l("POST")),a=await t.json();if(!t.ok)throw new Error(a.error||"Failed to upload to bank");D({type:"success",message:"Successfully uploaded to bank",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){D({type:"error",message:e.message,duration:5e3})}else D({type:"error",message:"Company code not found",duration:5e3})}async function i(){try{P("Creating and finalizing pay run...");var e=await fetch(`/clients/${companyCode}/payruns/create-and-finalize`,l("POST")),t=await e.json();if(!e.ok)throw new Error(t.error||"Failed to create and finalize pay run");D({type:"success",message:"Pay run created and finalized successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){D({type:"error",message:e.message||"Failed to create and finalize pay run",duration:5e3})}finally{F()}}async function o(e){try{var t=await fetch(`/clients/${companyCode}/payruns/${e}/confirm-upload`,l("POST")),a=await t.json();if(!t.ok)throw new Error(a.error||"Failed to confirm bank upload");D({type:"success",message:"Bank upload confirmed successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){D({type:"error",message:e.message||"Failed to confirm bank upload",duration:5e3})}}function s(e){r(e);e=document.getElementById("pending-payslips");e&&e.scrollIntoView({behavior:"smooth"})}function r(e,t){var a=document.querySelectorAll(".frequency-group.tab-content");for(let e=0;e<a.length;e++)a[e].classList.remove("active");var n=document.querySelectorAll(".tabs-navigation .tab-item");for(let e=0;e<n.length;e++)n[e].classList.remove("active");document.getElementById("pending-payslips-"+t).classList.add("active"),e.currentTarget.classList.add("active")}function d(e,t){var a=e.currentTarget.closest(".tabs-container");a.nextElementSibling.querySelectorAll(".tab-content").forEach(e=>{e.classList.remove("active")}),a.querySelectorAll(".tab-item").forEach(e=>{e.classList.remove("active")}),document.getElementById(t).classList.add("active"),e.currentTarget.classList.add("active")}function p(){var e,t=document.querySelector(".frequency-tabs .tab-button.active");t&&(t=t.getAttribute("data-frequency"))&&(e=document.querySelectorAll(`#${t}-payslips-body tr[data-payslip-id].finalized`),0!==(e=Array.from(e).map(e=>e.getAttribute("data-payslip-id"))).length?(q(),u(t,e),D({type:"success",message:'Payslips ready for pay run. Click "Select" to create the pay run.',duration:5e3})):D({type:"error",message:"No finalized payslips found. Please finalize payslips first.",duration:5e3}))}function u(i,o){var s=document.querySelector(`#progress-${i} .step:nth-child(2) .step-content`);if(s){let t=s.querySelector(".select-pay-run-btn"),e=(t||(t=document.createElement("button")).classList.add("btn","btn-primary","select-pay-run-btn"),document.querySelector("#progress-"+i)),a,n;if(e&&e.dataset.startDate&&e.dataset.endDate)a=e.dataset.startDate,n=e.dataset.endDate;else{let e=document.querySelector(`.payslip-item[data-frequency="${i}"]`);e&&e.dataset.startDate&&e.dataset.endDate&&(a=e.dataset.startDate,n=e.dataset.endDate)}if(a&&n){t.dataset.startDate=a,t.dataset.endDate=n,t.innerHTML='<i class="ph ph-check-circle"></i> Select',t.onclick=function(){L(i,a,n)};let e=s.querySelector(".step-status");e||((e=document.createElement("span")).classList.add("step-status"),s.appendChild(e)),e.innerHTML=`<i class="ph ph-check-circle"></i> ${o.length} payslips ready for pay run`,s.querySelector(".select-pay-run-btn")||e.insertAdjacentElement("afterend",t);o=s.closest(".step"),s=(o&&(o.classList.remove("active"),o.classList.add("completed")),o?.nextElementSibling);s&&(s.classList.remove("completed"),s.classList.add("active"))}else D({type:"error",message:"Could not determine pay period dates. Please try again.",duration:5e3})}}function y(o){var s=document.querySelector(".pay-runs-history");if(s){var r=s.querySelector(".empty-state");r&&r.remove();let e=s.querySelector("table.modern-table"),t=(e||((e=document.createElement("table")).className="modern-table",e.innerHTML='\n        <thead>\n          <tr>\n            <th>Period</th>\n            <th>Status</th>\n            <th>Employees</th>\n            <th class="amount">Total Amount</th>\n            <th></th>\n          </tr>\n        </thead>\n        <tbody></tbody>\n      ',s.appendChild(e)),e.querySelector("tbody")),a=document.createElement("tr"),n=moment(o.startDate).format("MMMM YYYY"),i=0;o.totals&&"number"==typeof o.totals.netPay?i=o.totals.netPay:"number"==typeof o.totalAmount?i=o.totalAmount:Array.isArray(o.payslips)&&(i=o.payslips.reduce((e,t)=>{let a=0;return t.calculations&&"number"==typeof t.calculations.netPay?a=t.calculations.netPay:"number"==typeof t.netPay&&(a=t.netPay),e+a},0));r=new Intl.NumberFormat("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2}).format(i);a.innerHTML=`
      <td>${n}</td>
      <td>
        <span class="status-badge ${o.status}">
          ${o.status.charAt(0).toUpperCase()+o.status.slice(1)}
        </span>
      </td>
      <td>${o.payslips.length}</td>
      <td class="amount"><span>R ${r}</span></td>
      <td>
        <div class="action-buttons">
          <button class="icon-button" onclick="viewPayRun('${o._id}')" title="View Details">
            <i class="ph ph-eye"></i>
          </button>
          <button class="icon-button" onclick="downloadPayslips('${o._id}')" title="Download Payslips">
            <i class="ph ph-download"></i>
          </button>
          ${"finalized"===o.status?`
            <button class="icon-button" onclick="releaseToSelfService('${o._id}')" title="Release to Self-Service">
              <i class="ph ph-share-network"></i>
            </button>
          `:""}
          <button class="icon-button" onclick="sendToXero('${o._id}')" title="Send to Xero">
            <i class="ph ph-arrow-square-out"></i>
          </button>
          <button class="icon-button" onclick="viewAccountingInfo('${o._id}')" title="Accounting Info">
            <i class="ph ph-calculator"></i>
          </button>
        </div>
      </td>
    `,t.insertBefore(a,t.firstChild)}}function m(e){var t=document.querySelector("#progress-"+e);t&&(f(t,e),setTimeout(()=>{var e=(new Date).getTime(),e=window.location.href.split("?")[0]+`?t=${e}&refresh=true`;window.location.replace(e)},1e3))}function f(e,t){e.querySelectorAll(".step").forEach((t,e)=>{if(e<2){t.classList.remove("active"),t.classList.add("completed");let e=t.querySelector(".step-status");e&&(e.innerHTML='<i class="ph ph-check-circle"></i> Completed')}});e=e.querySelector(".tracker-header .tracker-status");e&&(e.innerHTML='\n        <span class="status-badge finalized">\n          Finalized\n        </span>\n      ')}function h(e){P("Loading payslip details..."),fetch(`/clients/${window.companyCode}/payslips/`+e,{method:"GET",headers:{"CSRF-Token":$()}}).then(e=>e.json()).then(w=>{if(w.success){let e=document.getElementById("payslipDetails"),t=w.payslip,a=t.employee,n=moment(t.startDate),i=moment(t.endDate),o=t.basicSalary||0,s=t.calculations?.totalIncome||o,r=Math.min(.01*o,177.12),l=t.calculations?.paye||t.calculations?.deductions?.statutory?.paye||0,c=t.deductions?.find(e=>"benefits"===e.type&&"Pension Fund"===e.description)?.amount||0,d=t.deductions?.find(e=>"benefits"===e.type&&"Provident Fund"===e.description)?.amount||0,p=t.deductions?.find(e=>"benefits"===e.type&&"Retirement Annuity"===e.description)?.amount||0,u=t.deductions?.find(e=>"benefits"===e.type&&"Garnishee"===e.description)?.amount||0,y=t.deductions?.find(e=>"benefits"===e.type&&"Maintenance Order"===e.description)?.amount||0,m=t.deductions?.find(e=>"benefits"===e.type&&"Medical Aid"===e.description)?.amount||0,f=l+r+(c+d+p+u+y+m),h=s-f,g=`
          <div class="payslip-view">
            <div class="employee-section">
              <h4>${a.firstName} ${a.lastName}</h4>
              <p>Employee ID: ${a.employeeNumber||"N/A"}</p>
              <p>Pay Period: ${n.format("D MMM")} - ${i.format("D MMM YYYY")}</p>
              <p>Payment Frequency: ${t.frequency.charAt(0).toUpperCase()+t.frequency.slice(1)}</p>
            </div>
            
            <div class="payslip-section">
              <div class="payslip-row">
                <span>Basic Salary:</span>
                <span>R ${(t.basicSalary||0).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>
              
              <div class="payslip-row">
                <span>Total Income:</span>
                <span>R ${(t.calculations?.totalIncome||o).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>
              
              <div class="payslip-row deduction">
                <span>PAYE:</span>
                <span>R ${(t.calculations?.paye||t.calculations?.deductions?.statutory?.paye||0).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>
              
              <div class="payslip-row deduction">
                <span>UIF - Employee:</span>
                <span>R ${r.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`;0<c&&(g+=`
              <div class="payslip-row deduction">
                <span>Pension Fund:</span>
                <span>R ${c.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),0<d&&(g+=`
              <div class="payslip-row deduction">
                <span>Provident Fund:</span>
                <span>R ${d.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),0<p&&(g+=`
              <div class="payslip-row deduction">
                <span>Retirement Annuity:</span>
                <span>R ${p.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),0<u&&(g+=`
              <div class="payslip-row deduction">
                <span>Garnishee:</span>
                <span>R ${u.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),0<y&&(g+=`
              <div class="payslip-row deduction">
                <span>Maintenance Order:</span>
                <span>R ${y.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),0<m&&(g+=`
              <div class="payslip-row deduction">
                <span>Medical Aid:</span>
                <span>R ${m.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`);var v=(t.calculations?.totalDeductions||0)-l-r-c-d-p-u-y-m;0<v&&(g+=`
              <div class="payslip-row deduction">
                <span>Other Deductions:</span>
                <span>R ${v.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>`),g+=`
              <div class="payslip-row">
                <span>Total Deductions:</span>
                <span>R ${f.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>
              
              <div class="payslip-row total">
                <span>Net Pay:</span>
                <span>R ${h.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</span>
              </div>
            </div>
          </div>`,e.innerHTML=g,document.getElementById("viewPayslipModal").style.display="block"}else D({type:"error",message:w.error||"Failed to load payslip details",duration:5e3})}).catch(e=>{D({type:"error",message:"An error occurred while loading the payslip",duration:5e3})}).finally(()=>{F()})}function g(){document.getElementById("viewPayslipModal").style.display="none"}function R(a){P("Generating PDF..."),fetch(`/clients/${window.companyCode}/payslips/${a}/download`,{method:"GET",headers:{"CSRF-Token":$()}}).then(e=>{if(e.ok)return e.blob();throw new Error("Failed to generate PDF")}).then(e=>{var e=window.URL.createObjectURL(e),t=document.createElement("a");t.href=e,t.download=`payslip-${a}.pdf`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e),D({type:"success",message:"Payslip downloaded successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}).catch(e=>{D({type:"error",message:"An error occurred while downloading the payslip",duration:5e3})}).finally(()=>{F()})}function z(e){window.location.href=`/clients/${window.companyCode}/payslips/${e}/edit`}function N(){window.location.href=`/clients/${window.companyCode}/payslips/create`}function B(e){if(e){P("Preparing payslips for download...");try{let e=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");e}catch(e){}let o="/payslip/bulk-download/"+e,s=document.createElement("iframe"),r=(s.style.display="none",document.body.appendChild(s),setTimeout(()=>{F(),D({type:"success",message:"Payslips download started",duration:5e3})},5e3)),l=(s.onerror=function(){clearTimeout(r),F(),D({type:"error",message:"Failed to download payslips. Please try again later.",duration:5e3}),s&&s.parentNode&&document.body.removeChild(s)},window.onerror);window.onerror=function(e,t,a,n,i){return t&&t.includes(o)?(clearTimeout(r),F(),D({type:"error",message:"Error downloading payslips: "+e,duration:5e3}),s&&s.parentNode&&document.body.removeChild(s),!0):!!l&&l(e,t,a,n,i)},s.src=o,s.onload=function(){clearTimeout(r),F();try{var e=s.contentDocument?.body?.textContent||"";e&&(e.includes("Error")||e.includes("error"))?D({type:"error",message:"Failed to download payslips: "+e,duration:5e3}):D({type:"success",message:"Payslips downloaded successfully",duration:5e3})}catch(e){D({type:"success",message:"Payslips download started",duration:5e3})}setTimeout(()=>{s&&s.parentNode&&document.body.removeChild(s),window.onerror=l},1e3)}}else D({type:"error",message:"Invalid pay run ID",duration:5e3})}function O(e){confirm("Are you sure you want to release this pay run to self-service? Employees will be able to view their payslips.")&&(P("Releasing to self-service..."),fetch(`/clients/${window.companyCode}/payruns/${e}/release`,{method:"POST",headers:{"CSRF-Token":$()}}).then(e=>e.json()).then(e=>{e.success?(D({type:"success",message:"Pay run released to self-service successfully",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)):D({type:"error",message:e.error||"Failed to release pay run",duration:5e3})}).catch(e=>{D({type:"error",message:"An error occurred while releasing the pay run",duration:5e3})}).finally(()=>{F()}))}function j(e){window.location.href=`/clients/${window.companyCode}/payruns/`+e}function Y(e){D({message:"Sending to Xero feature will be implemented soon",type:"info",duration:3e3})}function H(e){D({message:"Accounting Info feature will be implemented soon",type:"info",duration:3e3})}function w(e,t,a){currentModalFrequency=e;let n=document.getElementById("payslipReviewModal");if(n){window.getComputedStyle(n);var i=n.querySelector(".modal-header h2"),i=(i&&(o="weekly"===e||"biweekly"===e?moment(t).format("DD MMM")+" - "+moment(a).format("DD MMM YYYY"):moment(a).format("MMMM YYYY"),i.textContent=`Review ${e.charAt(0).toUpperCase()+e.slice(1)} Payslips - `+o),n.setAttribute("data-start-date",t),n.setAttribute("data-end-date",a),document.getElementById("frequency-payslips-body")),o=(i&&(i.innerHTML='<tr><td colspan="8" class="loading-indicator"><div class="spinner"></div><span>Loading payslips...</span></td></tr>'),document.getElementById("finalize-selected")),i=(o&&o.setAttribute("onclick",`finalizeSelectedPayslips('${e}')`),document.getElementById("modal-frequency-title"));i&&(i.textContent=e.charAt(0).toUpperCase()+e.slice(1)+" Payslips");try{v(e,t,a)}catch(e){}n.style.display="block",n.classList.remove("hide"),n.classList.add("show"),n.style.zIndex="9999",window.getComputedStyle(n),setTimeout(()=>{"none"===window.getComputedStyle(n).display&&(n.style.display="block !important"),n.querySelector(".payslips-table-container"),document.getElementById("frequency-payslips-body")},500)}else document.querySelectorAll('[id*="modal"]').forEach(e=>{}),T("Error: Payslip review modal not found")}async function v(e,o,s){var r=document.getElementById("frequency-payslips-body");if(r){var t=document.getElementById("modal-frequency-title");t&&(t.textContent=e.charAt(0).toUpperCase()+e.slice(1)+" Payslips"),r.innerHTML='<tr><td colspan="8" class="loading-indicator"><div class="spinner"></div><span>Loading payslips...</span></td></tr>';try{let t=document.body.getAttribute("data-company-code");if(!t){let e=document.querySelector('meta[name="company-code"]');e&&(t=e.getAttribute("content"))}if(!t){let e=window.location.pathname.match(/\/clients\/([^\/]+)/);e&&e[1]&&(t=e[1])}if(!t)throw new Error("Company code not found. Please refresh the page.");var a=$();if(!a)return void(r.innerHTML='<tr><td colspan="8" class="text-center text-danger">Error: Security token missing</td></tr>');var l=await(await fetch(`/clients/${t}/payroll/payslips/${e}?startDate=${o}&endDate=`+s,{method:"GET",headers:{"CSRF-Token":a}})).json();if(!l.success)throw new Error(l.message||"Failed to fetch payslips");var n=document.getElementById("finalize-selected");if(n){let e=l.payslips&&l.payslips.some(e=>!e.isFinalized);n.style.display=e?"inline-block":"none"}if(l.payslips&&0<l.payslips.length){let t=`${o?new Date(o).toLocaleDateString():""} - `+(s?new Date(s).toLocaleDateString():""),e=(r.innerHTML=l.payslips.map(e=>`
          <tr data-payslip-id="${e._id}" ${e.isFinalized?'class="finalized"':""}>
              <td>
                <div class="checkbox-wrapper">
                <input type="checkbox"
                       id="payslip-${e._id}"
                       class="payslip-checkbox"
                       data-payslip-id="${e._id}"
                       ${e.isFinalized?"checked disabled":""}>
                  <label for="payslip-${e._id}"></label>
                </div>
              </td>
            <td>${e.employeeName||"N/A"}</td>
            <td>${t}</td>
            <td class="amount text-right">R ${(e.grossPay||0).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</td>
            <td class="amount text-right">R ${(e.totalDeductions||0).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</td>
            <td class="amount text-right">R ${(e.netPay||0).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}</td>
            <td><span class="status-badge ${e.isFinalized?"finalized":"pending"}">${e.isFinalized?"Finalized":"Pending"}</span></td>
            <td>
              <div class="action-buttons">
                <button
                  class="icon-button"
                  onclick="viewPayslip('${e._id}')"
                  title="View Payslip"
                >
                  <i class="ph ph-eye"></i>
                </button>
              </div>
            </td>
          </tr>
        `).join(""),document.getElementById("stat-total-employees")),a=document.getElementById("stat-total-gross"),n=document.getElementById("stat-total-deductions"),i=document.getElementById("stat-total-net");e&&(e.textContent=l.payslips.length),a&&(a.textContent="R "+l.totals.totalGross.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})),n&&(n.textContent="R "+l.totals.totalDeductions.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})),i&&(i.textContent="R "+l.totals.totalNet.toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2}));var c=l.payslips.every(e=>!0===e.isFinalized),d=document.getElementById("proceed-to-pay-run-btn");d&&(c&&0<l.payslips.length?(d.style.display="inline-block",d.setAttribute("data-payslip-ids",JSON.stringify(l.payslips.map(e=>e._id)))):d.style.display="none")}else r.innerHTML='<tr><td colspan="8" class="text-center">No payslips found for this period</td></tr>'}catch(e){r.innerHTML=`<tr><td colspan="8" class="text-center text-danger">Error: ${e.message}</td></tr>`}k()}}async function b(o){try{var e,t,a,n,i,s=document.getElementById("payslipReviewModal");if(s&&(e=s.getAttribute("data-start-date"),t=s.getAttribute("data-end-date"),e)&&t&&o&&(n="payrollhub_state_"+(a=document.body.getAttribute("data-company-code")||document.querySelector('meta[name="company-code"]')?.getAttribute("content")||window.location.pathname.split("/")[2]),i={frequency:o,startDate:e,endDate:t,lastAction:"finalize",timestamp:Date.now(),companyCode:a},localStorage.setItem(n,JSON.stringify(i)),sessionStorage.setItem(n,JSON.stringify(i))),(o=o||currentModalFrequency)&&"string"==typeof o&&""!==o.trim())if(["weekly","biweekly","monthly"].includes(o.toLowerCase())){var r=document.querySelectorAll(".payslip-checkbox:checked:not(:disabled)");if(Array.from(r).forEach((e,t)=>{}),0===r.length)D({type:"error",message:"Please select at least one payslip to finalize.",duration:5e3});else{P("Finalizing selected payslips...");let a=document.body.getAttribute("data-company-code");if(!a){let e=document.querySelector('meta[name="company-code"]');e&&(a=e.getAttribute("content"))}if(!a){let e=window.location.pathname.match(/\/clients\/([^\/]+)/);e&&e[1]&&(a=e[1])}if(a&&""!==a.trim()){var l=$();if(l&&""!==l.trim()){let t=Array.from(r).map(e=>e.getAttribute("data-payslip-id")).filter(e=>e&&""!==e.trim());if(0===t.length)F(),D({type:"error",message:"No valid payslips selected. Please try again.",duration:5e3});else{var c=document.getElementById("payslipReviewModal");if(c){let n=c.getAttribute("data-start-date"),i=c.getAttribute("data-end-date");if(n&&i&&""!==n.trim()&&""!==i.trim()){var d={payslipIds:t,frequency:o,generateNextPeriod:!0,startDate:n,endDate:i},p=await fetch(`/clients/${a}/payroll/bulk-finalize`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":l},body:JSON.stringify(d)});if(!p.ok){let t="Failed to finalize payslips",e="";try{var u=await p.text();try{var y=JSON.parse(u);t=y.error||y.message||t,e=y.details||""}catch(e){t=u||t}}catch(e){t=`HTTP ${p.status}: `+p.statusText}throw 404===p.status?t="Payroll service not found. Please contact support.":403===p.status?t="You don't have permission to finalize payslips.":500===p.status&&(t="Server error occurred. Please try again later."),new Error(t+(e?` (${e})`:""))}let e;try{e=await p.json()}catch(o){e={success:p.ok}}F(),D({type:"success",message:t.length+" payslips finalized successfully!",duration:5e3}),t.forEach(e=>{var t,e=document.querySelector(`tr[data-payslip-id="${e}"]`);e&&(e.classList.add("finalized"),(t=e.querySelector(".payslip-checkbox"))&&(t.disabled=!0,t.checked=!0),t=e.querySelector("td:nth-child(7)"))&&(t.innerHTML='<span class="status-badge finalized">Finalized</span>')});var m=0===document.querySelectorAll(".payslip-checkbox:not(:disabled):not(:checked)").length;if(m){let e=document.getElementById("proceed-to-pay-run-btn");e&&(e.style.display="inline-block",f=document.querySelectorAll("tr[data-payslip-id]"),h=Array.from(f).map(e=>e.getAttribute("data-payslip-id")),e.setAttribute("data-payslip-ids",JSON.stringify(h)));var f,h,g=document.getElementById("finalize-selected");g&&(g.style.display="none")}var w,v,b,S,k,C=`.period-tracker[data-start-date="${n}"][data-end-date="${i}"]`,E=document.querySelector(C);E&&m&&((w=E.querySelector(".tracker-status"))&&(w.innerHTML='\n            <span class="status-badge finalized">\n              <i class="ph ph-check-circle"></i>\n              Ready for Pay Run\n            </span>\n          '),2<=(v=E.querySelectorAll(".step")).length)&&(v[0].classList.remove("active"),v[0].classList.add("completed"),v[1].classList.remove("completed"),v[1].classList.add("active"),(b=v[1].querySelector(".btn"))?b.style.display="inline-block":(S=v[1].querySelector(".step-content"))&&((k=document.createElement("button")).className="btn btn-primary",k.textContent="Select",k.addEventListener("click",function(){L(o,n,i)}),S.innerHTML="",S.appendChild(k))),setTimeout(()=>{q(),D({type:"success",message:`Successfully finalized ${t.length} payslips. Look for "Select" button in the progress tracker to create a pay run.`,duration:5e3}),setTimeout(()=>{let e=document.createElement("div");e.className="xpay-notification success refresh-notification",e.innerHTML='\n            <i class="ph ph-arrows-clockwise ph-spin"></i>\n            Refreshing progress trackers...\n          ',document.body.appendChild(e),document.querySelectorAll(".period-tracker").forEach(e=>{var t=e.getAttribute("data-start-date"),a=e.getAttribute("data-end-date");if(t===n&&a===i){t=e.querySelector(".tracker-status"),a=(t&&(t.innerHTML='\n                  <span class="status-badge finalized">\n                    <i class="ph ph-check-circle"></i>\n                    Ready for Pay Run\n                  </span>\n                '),e.querySelectorAll(".step"));if(2<=a.length){a[0].classList.remove("active"),a[0].classList.add("completed");let e=a[0].querySelector(".step-status"),t=(e&&(e.innerHTML='\n                    <span class="progress-status-pill completed">\n                      <i class="ph ph-check-circle"></i> Completed\n                    </span>\n                  '),a[1].classList.remove("completed"),a[1].classList.add("active"),a[1].querySelector(".step-actions"));if(t){let e=document.createElement("button");e.className="btn btn-primary",e.textContent="Select",e.addEventListener("click",function(){L(o,n,i)}),t.innerHTML="",t.appendChild(e)}}}}),setTimeout(()=>{document.body.removeChild(e)},2e3)},1e3)},1e3)}else F(),D({type:"error",message:"Date range information missing. Please close and reopen the modal.",duration:5e3})}else F(),D({type:"error",message:"Modal not found. Please close and reopen the modal.",duration:5e3})}}else F(),D({type:"error",message:"Security token not found. Please refresh the page and try again.",duration:5e3})}else F(),D({type:"error",message:"Company code not found. Please refresh the page and try again.",duration:5e3})}}else D({type:"error",message:`Error: Invalid frequency '${o}'. Please try again.`,duration:5e3});else D({type:"error",message:"Error: Unable to determine frequency. Please close the modal and try again.",duration:5e3})}catch(o){F();let e="Failed to finalize payslips. Please try again.";o.message&&(e=o.message.includes("Company code not found")?"Company information not found. Please refresh the page and try again.":o.message.includes("CSRF")?"Security token expired. Please refresh the page and try again.":o.message.includes("Network")?"Network error. Please check your connection and try again.":o.message.includes("403")?"You don't have permission to perform this action.":o.message.includes("404")?"Service not found. Please contact support.":o.message),D({type:"error",message:e,duration:7e3})}}function _(){let e=document.querySelectorAll("#frequency-payslips-body tr[data-payslip-id]"),n=[],a=!0;if(e.forEach(e=>{var t=e.getAttribute("data-payslip-id");e.classList.contains("finalized")?n.push(t):a=!1}),a&&0<n.length){u(currentModalFrequency,n),q(),D({type:"success",message:"All payslips have been finalized. Click 'Select' to create the pay run.",duration:5e3});let a=document.querySelector(`#progress-${currentModalFrequency} .step:nth-child(1) .step-content`);if(a){let e=a.querySelector(".step-status"),t=(e&&(e.innerHTML='<i class="ph ph-check-circle"></i> Completed'),a.querySelector(".review-btn"));t&&(t.style.display="none")}}return a}function S(){var t=currentModalFrequency;if(t){var a=document.getElementById("proceed-to-pay-run-btn");if(a){a=a.getAttribute("data-payslip-ids");if(a){let e;try{e=JSON.parse(a)}catch(t){return}Array.isArray(e)&&0!==e.length&&(q(),u(t,e),D({type:"success",message:'Payslips ready for pay run. Click "Select" to create the pay run.',duration:5e3}))}}}}function q(){var e=document.getElementById("payslipReviewModal");e&&(e.removeAttribute("data-start-date"),e.removeAttribute("data-end-date"),e.style.display="none",e.classList.remove("show"),e.classList.add("hide"),currentModalFrequency="")}function k(){let a=document.getElementById("select-all");if(a){let t=document.querySelectorAll(".payslip-checkbox:not([disabled])");a.addEventListener("change",function(){t.forEach(e=>{e.checked=a.checked})}),t.forEach(e=>{e.addEventListener("change",function(){var e=Array.from(t).every(e=>e.checked);a.checked=e})})}}function C(e){return parseFloat(e).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2})}function Z(t,a){if("function"==typeof D)D({type:t,message:a,duration:5e3});else{let e=document.getElementById("notification");e&&(e.textContent=a,e.className="xpay-notification "+t,e.style.display="block",setTimeout(()=>{e.style.display="none"},3e3))}}function E(e,t=0){}function D(e){let t=document.createElement("div");t.className="toast toast-"+e.type,t.textContent=e.message,document.body.appendChild(t),setTimeout(()=>{t.classList.add("show"),setTimeout(()=>{t.classList.remove("show"),setTimeout(()=>document.body.removeChild(t),300)},e.duration||3e3)},100)}function A(c,d,p="Confirm",u="Cancel"){return new Promise(t=>{let a=document.createElement("div");a.className="modal confirmation-modal",a.style.display="block",a.style.zIndex="10000";var e=document.createElement("div"),n=(e.className="modal-content",document.createElement("div")),i=(n.className="modal-header",n.innerHTML=`<h3>${c}</h3>`,document.createElement("div")),o=(i.className="modal-body",i.innerHTML=`<p>${d}</p>`,document.createElement("div")),s=(o.className="modal-footer",document.createElement("button")),r=(s.className="btn btn-primary",s.textContent=p,s.onclick=()=>{document.body.removeChild(a),t(!0)},document.createElement("button"));r.className="btn btn-secondary",r.textContent=u,r.onclick=()=>{document.body.removeChild(a),t(!1)},o.appendChild(r),o.appendChild(s),e.appendChild(n),e.appendChild(i),e.appendChild(o),a.appendChild(e),document.body.appendChild(a);let l=e=>{"Escape"===e.key&&(document.body.removeChild(a),document.removeEventListener("keydown",l),t(!1))};document.addEventListener("keydown",l)})}function P(e="Processing..."){var t,a=document.getElementById("loadingOverlay");a&&((t=a.querySelector(".loading-text"))&&(t.textContent=e),a.style.display="flex")}function F(){var e=document.getElementById("loadingOverlay");e&&(e.style.display="none")}function U(){Object.keys(localStorage).forEach(e=>{e.startsWith("payrollhub_state_")&&localStorage.removeItem(e)}),Object.keys(sessionStorage).forEach(e=>{e.startsWith("payrollhub_state_")&&sessionStorage.removeItem(e)})}function J(e){D({type:"success",message:e,duration:5e3})}function T(e){D({type:"error",message:e,duration:5e3})}async function G(e){D({type:"info",message:"Finalizing pay run...",duration:5e3}),P("Finalizing pay run...");var t=$();t?fetch(`/clients/${companyCode}/payruns/${e}/manual-finalize`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":t}}).then(e=>e.ok?e.json():e.json().then(e=>{throw new Error(e.error||"Failed to finalize pay run")})).then(t=>{F(),D({type:"success",message:"Pay run finalized successfully!",duration:5e3});var a=document.querySelector(`#progress-${t.frequency} .finalize-pay-run-btn`),a=(a&&(a.disabled=!0,a.classList.add("disabled"),a.innerHTML='<i class="ph ph-check"></i> Pay Run Finalized'),document.getElementById("progress-"+t.frequency));if(a){let e=a.querySelector(".step:nth-child(2)"),t=a.querySelector(".step:nth-child(3)");e&&(e.classList.remove("active"),e.classList.add("completed")),t&&t.classList.add("active")}t=document.querySelector(`tr[data-payrun-id="${e}"]`);if(t){let e=t.querySelector(".status-badge");e&&(e.className="status-badge finalized",e.textContent="Finalized")}}).catch(e=>{F(),D({type:"error",message:e.message||"Failed to finalize pay run. Please try again.",duration:5e3})}):(F(),D({type:"error",message:"CSRF token not found. Please refresh the page.",duration:5e3}))}async function Y(e){try{P("Sending to Xero...");var t=await fetch(`/api/pay-runs/${e}/xero`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":$()}});if(!t.ok){let e=await t.json();throw new Error(e.message||"Failed to send to Xero")}await t.json(),D({type:"success",message:"Successfully sent to Xero",duration:5e3}),setTimeout(()=>{var e=(new Date).getTime(),e=`/clients/${companyCode}/payrollhub?t=${e}&refresh=true`;window.location.replace(e)},1e3)}catch(e){D({type:"error",message:e.message||"Failed to send to Xero",duration:5e3})}finally{F()}}function V(e){var t=calculateGrossIncome(e),a=calculateDeductions(e),n=t-a,i="finalized"===e.status,o=document.createElement("tr"),s=(o.setAttribute("data-payslip-id",e._id),document.createElement("td")),r=(s.className="checkbox-col",document.createElement("div")),l=(r.className="checkbox-wrapper",document.createElement("input")),i=(l.type="checkbox",l.id="payslip-"+e._id,l.className="payslip-checkbox",l.setAttribute("data-payslip-id",e._id),i&&(l.disabled=!0,l.checked=!0),document.createElement("label")),l=(i.setAttribute("for","payslip-"+e._id),r.appendChild(l),r.appendChild(i),s.appendChild(r),document.createElement("td")),i=(l.className="employee-col",l.textContent=e.employee?e.employee.firstName+" "+e.employee.lastName:"Unknown Employee",document.createElement("td")),r=(i.className="period-col",i.textContent=formatPayPeriod(e.payPeriod?.startDate,e.payPeriod?.endDate),document.createElement("td")),t=(r.className="amount-col",r.textContent="R "+C(t),document.createElement("td")),a=(t.className="amount-col",t.textContent="R "+C(a),document.createElement("td")),n=(a.className="amount-col",a.textContent="R "+C(n),document.createElement("td"));return n.className="status-col",n.textContent=e.status,o.appendChild(s),o.appendChild(l),o.appendChild(i),o.appendChild(r),o.appendChild(t),o.appendChild(a),o.appendChild(n),o}function x(){var e=document.getElementById("xpay-bank-file-modal");e&&(e.style.display="none",e.classList.remove("show"))}function M(){let e=document.getElementById("payslipReviewModal");e&&("none"===window.getComputedStyle(e).display?(e.style.display="block",e.classList.add("show"),e.classList.remove("hide")):(e.style.display="none",e.classList.remove("show"),e.classList.add("hide")),setTimeout(()=>{window.getComputedStyle(e).display},100))}function I(e,t,a=[],n="error"){n=`
          <div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                  <div class="modal-content">
                      <div class="modal-header ${"error"===n?"bg-danger":"bg-warning"} text-white">
                          <h5 class="modal-title" id="notificationModalLabel">${e}</h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      </div>
                      <div class="modal-body">
                          <p>${t}</p>
                          ${0<a.length?'<div class="mt-3"><h6>Affected Employees:</h6><ul>'+a.map(e=>`<li>${e}</li>`).join("")+"</ul></div>":""}
                      </div>
                      <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                          <a href="/employees" class="btn btn-primary">Update Employee Details</a>
                      </div>
                  </div>
              </div>
          </div>
      `,e=document.getElementById("notificationModal");e&&e.remove(),document.body.insertAdjacentHTML("beforeend",n),new bootstrap.Modal(document.getElementById("notificationModal")).show()}function D(e,t="error"){t=`
          <div class="toast-container position-fixed top-0 end-0 p-3">
              <div class="toast align-items-center text-white bg-${"error"===t?"danger":"warning"} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                  <div class="d-flex">
                      <div class="toast-body">
                          ${e}
                      </div>
                      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                  </div>
              </div>
          </div>
      `,e=document.querySelector(".toast-container"),e&&e.remove(),document.body.insertAdjacentHTML("beforeend",t),e=document.querySelector(".toast");new bootstrap.Toast(e,{delay:5e3}).show()}document.addEventListener("DOMContentLoaded",function(){setTimeout(()=>{document.querySelectorAll('button[onclick*="openPayslipReviewModal"]').forEach((e,t)=>{e.addEventListener("click",function(e){window.openPayslipReviewModal})}),window.addEventListener("error",function(e){})},1e3),document.querySelectorAll(".select-all-payslips").forEach(e=>{e.addEventListener("change",function(){var e=this.getAttribute("data-frequency"),e=document.querySelectorAll(`.payslip-checkbox-input[data-frequency="${e}"]`),t=this.closest(".payslips-header").querySelector(".bulk-finalize-btn");e.forEach(e=>{e.checked=this.checked}),this.checked&&0<e.length?t.style.display="block":t.style.display="none"})}),document.querySelectorAll(".payslip-checkbox-input").forEach(e=>{e.addEventListener("change",function(){var e=this.getAttribute("data-frequency"),t=document.querySelectorAll(`.payslip-checkbox-input[data-frequency="${e}"]`),a=document.querySelectorAll(`.payslip-checkbox-input[data-frequency="${e}"]:checked`),e=document.querySelector(`.select-all-payslips[data-frequency="${e}"]`),n=e.closest(".payslips-header").querySelector(".bulk-finalize-btn");e.checked=t.length===a.length,0<a.length?n.style.display="block":n.style.display="none"})}),window.addEventListener("click",function(e){var t=document.getElementById("viewPayslipModal");e.target===t&&g()});var e=document.querySelector("#payslipReviewModal .close");e&&e.addEventListener("click",q);let t=document.getElementById("payslipReviewModal");t&&t.addEventListener("click",function(e){e.target===t&&q()}),document.addEventListener("keydown",function(e){if("Escape"===e.key){let e=document.getElementById("payslipReviewModal");e&&"block"===e.style.display&&q()}})}),window.generateBankFile=a,window.closeBankFileModal=x,window.openPayslipReviewModal=w,window.closePayslipReviewModal=q,window.finalizeSelectedPayslips=b,window.proceedToPayRun=S,window.createPayRun=L,window.testModalOpen=function(){try{window.openPayslipReviewModal("monthly","2024-01-01","2024-01-31")}catch(e){}},window.testModalVisibility=function(){var e=document.getElementById("payslipReviewModal");e&&(window.getComputedStyle(e),e.style.display="block",e.style.visibility="visible",e.style.opacity="1",e.style.zIndex="9999")},window.testModalVisibility=M;