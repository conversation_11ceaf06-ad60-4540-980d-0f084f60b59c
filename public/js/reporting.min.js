let r={log:(e,t)=>{},error:(e,t)=>{}};class e{constructor(){this.modal=document.getElementById("reportSettingsModal"),this.form=document.getElementById("reportSettingsForm"),this.openBtn=document.getElementById("openSettingsBtn"),this.closeBtn=document.querySelector(".close"),this.companyCode=document.querySelector('meta[name="company-code"]')?.content,this.initializeEventListeners(),this.loadSettings()}initializeEventListeners(){this.openBtn?.addEventListener("click",()=>{r.log("Opening modal"),this.modal.classList.add("show")}),this.closeBtn?.addEventListener("click",()=>{r.log("Closing modal"),this.modal.classList.remove("show")}),this.form?.addEventListener("submit",e=>this.handleSubmit(e)),document.getElementById("sendTestEmail")?.addEventListener("click",()=>this.sendTestEmail()),this.initializeEmailTabs()}async loadSettings(){try{r.log("Loading settings",this.companyCode);var e=await fetch(`/clients/${this.companyCode}/reporting/settings`);if(!e.ok)throw new Error("HTTP error! status: "+e.status);var t=await e.json();r.log("Settings loaded",t),this.populateForm(t.settings)}catch(e){r.error("Failed to load settings",e)}}populateForm(e){var t;e&&((t=document.getElementById("defaultFormat"))&&(t.value=e.defaultFormat||"pdf"),t={maskSensitiveData:e.payslips?.maskSensitiveData,includeYTDTotals:e.payslips?.includeYTDTotals,showBankDetails:e.payslips?.showBankDetails,maskAccountNumbers:e.bankingReport?.maskAccountNumbers,groupByBank:e.bankingReport?.groupByBank,includePersonalInfo:e.employeeReports?.includePersonalInfo,includeBankDetails:e.employeeReports?.includeBankDetails,autoEmailReports:e.emailSettings?.autoEmailReports},Object.entries(t).forEach(([e,t])=>{e=document.getElementById(e);e&&(e.checked=t)}),(t=document.getElementById("emailRecipients"))&&e.emailSettings?.emailRecipients&&(t.value=Array.isArray(e.emailSettings.emailRecipients)?e.emailSettings.emailRecipients.join(", "):e.emailSettings.emailRecipients),(t=document.getElementById("payrollReportsDefault"))&&e.dateRangePresets?.payrollReports?.default&&(t.value=e.dateRangePresets.payrollReports.default),(t=document.getElementById("employeeReportsDefault"))&&e.dateRangePresets?.employeeReports?.default&&(t.value=e.dateRangePresets.employeeReports.default),this.populateReportTypeEmailSettings(e))}async handleSubmit(e){e.preventDefault(),r.log("Submitting settings form");try{let e=new FormData(this.form),t={defaultFormat:e.get("defaultFormat"),payslips:{maskSensitiveData:"on"===e.get("maskSensitiveData"),includeYTDTotals:"on"===e.get("includeYTDTotals"),showBankDetails:"on"===e.get("showBankDetails")},bankingReport:{maskAccountNumbers:"on"===e.get("maskAccountNumbers"),groupByBank:"on"===e.get("groupByBank")},employeeReports:{includePersonalInfo:"on"===e.get("includePersonalInfo"),includeBankDetails:"on"===e.get("includeBankDetails")},emailSettings:{autoEmailReports:"on"===e.get("autoEmailReports"),emailRecipients:e.get("emailRecipients")?.split(",").map(e=>e.trim())},dateRangePresets:{payrollReports:{default:e.get("dateRangePresets.payrollReports.default")||"currentMonth"},employeeReports:{default:e.get("dateRangePresets.employeeReports.default")||"allTime"}},payslips:{maskSensitiveData:"on"===e.get("payslips.maskSensitiveData"),includeYTDTotals:"on"===e.get("payslips.includeYTDTotals"),showBankDetails:"on"===e.get("payslips.showBankDetails"),emailSettings:{autoEmailReports:"on"===e.get("payslips.emailSettings.autoEmailReports"),emailRecipients:e.get("payslips.emailSettings.emailRecipients")?.split(",").map(e=>e.trim()).filter(e=>0<e.length)||[]}},bankingReport:{maskAccountNumbers:"on"===e.get("bankingReport.maskAccountNumbers"),groupByBank:"on"===e.get("bankingReport.groupByBank"),emailSettings:{autoEmailReports:"on"===e.get("bankingReport.emailSettings.autoEmailReports"),emailRecipients:e.get("bankingReport.emailSettings.emailRecipients")?.split(",").map(e=>e.trim()).filter(e=>0<e.length)||[]}},employeeReports:{includePersonalInfo:"on"===e.get("employeeReports.includePersonalInfo"),includeBankDetails:"on"===e.get("employeeReports.includeBankDetails"),emailSettings:{autoEmailReports:"on"===e.get("employeeReports.emailSettings.autoEmailReports"),emailRecipients:e.get("employeeReports.emailSettings.emailRecipients")?.split(",").map(e=>e.trim()).filter(e=>0<e.length)||[]}}},a=await fetch(`/clients/${this.companyCode}/reporting/settings`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]')?.content},body:JSON.stringify(t)});if(!a.ok)throw new Error("HTTP error! status: "+a.status);(await a.json()).success&&(this.modal.classList.remove("show"),c("success","Settings saved successfully"))}catch(e){r.error("Failed to save settings",e),c("error","Failed to save settings")}}initializeEmailTabs(){let a=document.querySelectorAll(".tab-button"),s=document.querySelectorAll(".tab-content");a.forEach(t=>{t.addEventListener("click",()=>{var e=t.getAttribute("data-tab"),e=(a.forEach(e=>e.classList.remove("active")),s.forEach(e=>e.classList.remove("active")),t.classList.add("active"),document.getElementById(e+"-tab"));e&&e.classList.add("active")})})}populateReportTypeEmailSettings(e){var t=document.getElementById("payslipsAutoEmail"),t=(t&&e.payslips?.emailSettings?.autoEmailReports&&(t.checked=e.payslips.emailSettings.autoEmailReports),document.getElementById("payslipsEmailRecipients")),t=(t&&e.payslips?.emailSettings?.emailRecipients&&(t.value=Array.isArray(e.payslips.emailSettings.emailRecipients)?e.payslips.emailSettings.emailRecipients.join(", "):e.payslips.emailSettings.emailRecipients),document.getElementById("bankingAutoEmail")),t=(t&&e.bankingReport?.emailSettings?.autoEmailReports&&(t.checked=e.bankingReport.emailSettings.autoEmailReports),document.getElementById("bankingEmailRecipients")),t=(t&&e.bankingReport?.emailSettings?.emailRecipients&&(t.value=Array.isArray(e.bankingReport.emailSettings.emailRecipients)?e.bankingReport.emailSettings.emailRecipients.join(", "):e.bankingReport.emailSettings.emailRecipients),document.getElementById("employeeAutoEmail")),t=(t&&e.employeeReports?.emailSettings?.autoEmailReports&&(t.checked=e.employeeReports.emailSettings.autoEmailReports),document.getElementById("employeeEmailRecipients"));t&&e.employeeReports?.emailSettings?.emailRecipients&&(t.value=Array.isArray(e.employeeReports.emailSettings.emailRecipients)?e.employeeReports.emailSettings.emailRecipients.join(", "):e.employeeReports.emailSettings.emailRecipients)}async sendTestEmail(){try{var e=document.getElementById("testEmail"),t=e?.value?.trim();if(t)if(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)){var a=document.getElementById("sendTestEmail"),s=(a.innerHTML,a.disabled=!0,a.innerHTML='<i class="ph ph-spinner ph-spin"></i> Sending...',r.log("Sending test email",t),await fetch(`/clients/${this.companyCode}/reporting/settings/test-email`,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]')?.content},body:JSON.stringify({testEmail:t})}));if(!s.ok)throw new Error("HTTP error! status: "+s.status);var i=await s.json();if(!i.success)throw new Error(i.message||"Failed to send test email");c("success","Test email sent successfully to "+t,"Email Test"),e.value=""}else c("error","Please enter a valid email address");else c("error","Please enter a test email address")}catch(e){r.error("Failed to send test email",e),c("error",e.message||"Failed to send test email","Email Test")}finally{a=document.getElementById("sendTestEmail");a.disabled=!1,a.innerHTML='<i class="ph ph-paper-plane-tilt"></i> Send Test'}}}class t{constructor(){this.form=document.getElementById("reportForm"),this.formatSelect=document.getElementById("format"),this.dateRangeSelect=document.getElementById("dateRange"),this.companyCode=document.querySelector('meta[name="company-code"]')?.content,this.initializeEventListeners(),this.loadDefaultSettings(),this.initializeDatePickers()}initializeDatePickers(){this.startDatePicker=flatpickr("#startDate",{dateFormat:"Y-m-d",altInput:!0,altFormat:"F j, Y",onChange:e=>{e[0]&&this.endDatePicker.set("minDate",e[0])}}),this.endDatePicker=flatpickr("#endDate",{dateFormat:"Y-m-d",altInput:!0,altFormat:"F j, Y",onChange:e=>{e[0]&&this.startDatePicker.set("maxDate",e[0])}}),this.dateRangeSelect?.addEventListener("change",e=>{var t=document.querySelector(".custom-range");"custom"===e.target.value?(t.style.display="block",this.startDatePicker.clear(),this.endDatePicker.clear()):t.style.display="none"})}async loadDefaultSettings(){try{var e=await fetch(`/clients/${this.companyCode}/reporting/settings`);if(!e.ok)throw new Error("HTTP error! status: "+e.status);var t=await e.json();t.success&&t.settings&&(t.settings.defaultFormat&&this.formatSelect&&(this.formatSelect.value=t.settings.defaultFormat),this.updateDateRangeOptions(t.settings))}catch(e){r.error("Failed to load default settings",e)}}updateDateRangeOptions(t){var a=this.form.querySelector("#reportType").value;if(t.dateRangePresets&&this.dateRangeSelect){let e;a.includes("payroll")?e=t.dateRangePresets.payrollReports?.default:a.includes("employee")&&(e=t.dateRangePresets.employeeReports?.default),e&&(this.dateRangeSelect.value=e)}}initializeEventListeners(){this.form?.addEventListener("submit",e=>this.handleSubmit(e)),this.form?.querySelector("#reportType")?.addEventListener("change",e=>{this.loadDefaultSettings()}),this.dateRangeSelect?.addEventListener("change",e=>{var t=document.querySelector(".custom-range");"custom"===e.target.value?t.style.display="block":t.style.display="none"}),this.form?.querySelector("#reportType")?.addEventListener("change",e=>{var e=e.target.value,t=this.dateRangeSelect,a=document.querySelector(".employee-selection"),e=(t.querySelectorAll("optgroup").forEach(e=>{e.style.display="none"}),"payrollVariance"===e?(t.querySelector(".payroll-ranges").style.display="",a.style.display="none"):e.includes("payslip")||e.includes("payroll")?(t.querySelector(".payroll-ranges").style.display="",a.style.display="payslips"===e?"block":"none"):"employeeList"===e?(t.querySelector(".employee-ranges").style.display="",a.style.display="block"):(e.includes("emp201")||e.includes("uif")||e.includes("eti"))&&(t.querySelector(".statutory-ranges").style.display="",a.style.display="none"),t.querySelector('option[value="custom"]').style.display="",t.querySelector('option:not([style*="display: none"])'));e&&(t.value=e.value)}),this.form?.querySelector("#reportType")?.addEventListener("change",e=>{var t=document.querySelector(".employee-selection");"employeeList"===e.target.value?t.style.display="block":t.style.display="none"})}async handleSubmit(e){e.preventDefault(),r.log("Generating report");try{let e=new FormData(this.form),s=e.get("reportType"),i=e.get("format"),t=e.get("dateRange"),a=Array.from(this.form.querySelector("#selectedEmployees")?.selectedOptions||[]).map(e=>e.value).filter(e=>"all"!==e),n="employeeBasicInfo"===s?Array.from(this.form.querySelectorAll('input[name="fields"]:checked')).map(e=>e.value):[];if("employeeBasicInfo"===s){n.forEach((e,t)=>{});let e=this.form.querySelectorAll('input[name="fields"]');e.forEach((e,t)=>{})}r.log("Report request:",{reportType:s,format:i,dateRange:t,selectedEmployees:a,selectedFields:n});var o=await fetch(`/clients/${this.companyCode}/reporting/`+s,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]')?.content},body:JSON.stringify({format:i,dateRange:t,selectedEmployees:0<a.length?a:null,selectedFields:0<n.length?n:null})});if(!o.ok){let e=await o.json();throw new Error(e.message||"HTTP error! status: "+o.status)}var l=o.headers.get("content-type");if(l&&l.includes("application/json")){let e=await o.json();if(!e.success)throw new Error(e.message)}else{let e=await o.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`${s}_${(new Date).toISOString().split("T")[0]}.`+(()=>{switch(i){case"pdf":return"pdf";case"excel":return"xlsx";case"csv":return"csv";default:return"txt"}})(),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),a.remove(),c("success","Report generated successfully")}}catch(e){r.error("Failed to generate report",e),c("error",e.message||"Failed to generate report")}}}function c(e,t,a=""){if("function"==typeof window.showToast)return window.showToast({type:e,message:t,title:a,duration:4e3,closable:!0});let s=document.createElement("div");s.className="notification "+e,s.textContent=t,document.body.appendChild(s),setTimeout(()=>s.remove(),3e3)}async function a(e){try{var t=document.querySelector('meta[name="company-code"]')?.content;if(!t)throw new Error("Company code not found");var a=await fetch(`/clients/${t}/reporting/download/`+e,{headers:{"CSRF-Token":document.querySelector('meta[name="csrf-token"]')?.content}});if(!a.ok)throw new Error("HTTP error! status: "+a.status);var s=a.headers.get("Content-Disposition"),i=s?s.split("filename=")[1].replace(/['"]/g,""):"report",n=await a.blob(),o=window.URL.createObjectURL(n),l=document.createElement("a");l.href=o,l.download=i,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(o),l.remove(),c("success","Report downloaded successfully")}catch(e){c("error","Failed to download report")}}function s(){var e=document.querySelector('meta[name="company-code"]')?.content;e&&fetch(`/clients/${e}/reporting/recent`,{headers:{"CSRF-Token":document.querySelector('meta[name="csrf-token"]')?.content}}).then(e=>e.json()).then(e=>{e.success&&i(document.querySelector(".reports-list"),e.reports)}).catch(e=>{})}function i(e,t){t&&0!==t.length?e.innerHTML=`
    <div class="reports-grid">
      ${t.map(e=>`
        <div class="report-card">
          <div class="report-icon">
            <i class="ph ph-file-${"pdf"===e.format?"pdf":"excel"===e.format?"xls":"csv"}"></i>
          </div>
          <div class="report-info">
            <h4>${e.type.replace(/([A-Z])/g," $1").trim()}</h4>
            <p class="report-meta">
              <span class="format">${e.format.toUpperCase()}</span>
              <span class="date">${moment(e.generatedAt).fromNow()}</span>
            </p>
            <p class="generated-by">Generated by ${e.generatedBy}</p>
          </div>
          <div class="report-actions">
            <button onclick="downloadReport('${e.id}')" class="action-button secondary small">
              <i class="ph ph-download"></i>
              Download
            </button>
          </div>
        </div>
      `).join("")}
    </div>
  `:e.innerHTML='\n      <div class="empty-state">\n        <i class="ph ph-file-text"></i>\n        <p>No recent reports</p>\n      </div>\n    '}document.addEventListener("DOMContentLoaded",()=>{new e,new t}),setInterval(s,6e4);