document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("sidebar-toggle"),t=document.querySelector(".sidebar"),s=document.querySelector(".main-content");e&&(e.addEventListener("click",function(){t.classList.toggle("collapsed"),s.classList.toggle("expanded");const e=t.classList.contains("collapsed");localStorage.setItem("sidebarCollapsed",e)}),"true"===localStorage.getItem("sidebarCollapsed")&&(t.classList.add("collapsed"),s.classList.add("expanded")));const n=window.location.pathname;document.querySelectorAll(".sidebar-menu a").forEach(e=>{const t=e.getAttribute("href");if(t&&n.includes(t)&&"/"!==t){e.classList.add("active");const t=e.closest(".dropdown-menu");if(t){t.classList.add("show");const e=t.previousElementSibling;e&&(e.classList.add("active"),e.setAttribute("aria-expanded","true"))}}}),document.querySelectorAll(".sidebar-menu .dropdown-toggle").forEach(e=>{e.addEventListener("click",function(e){e.preventDefault();const t=this.nextElementSibling;t.classList.toggle("show");const s=t.classList.contains("show");this.setAttribute("aria-expanded",s)})})});