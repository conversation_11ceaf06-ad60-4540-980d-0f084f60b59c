let l={"ABSA Bank":{name:"ABSA Bank",universalCode:"632005"},"Capitec Bank":{name:"Capitec Bank",universalCode:"470010"},FNB:{name:"First National Bank",universalCode:"250655"},Nedbank:{name:"Nedbank",universalCode:"198765"},"Standard Bank":{name:"Standard Bank",universalCode:"051001"},"African Bank":{name:"African Bank",universalCode:"430000"},"Bidvest Bank":{name:"Bidvest Bank",universalCode:"462005"},"Discovery Bank":{name:"Discovery Bank",universalCode:"679000"},TymeBank:{name:"TymeBank",universalCode:"678910"},Other:{name:"Other Bank",universalCode:""}};class i{constructor(){this.form=document.getElementById("addEmployeeForm"),this.currentStep=1,this.totalSteps=3,this.employeeData={},this.steps=document.querySelectorAll(".form-step"),this.initializeSteps(),this.initializeEventListeners(),this.initializeDirectorFields(),this.initializeFlatpickr(),this.updateStepIndicator(1),this.initializeRegularHours(),this.initializeBankingDetails()}initializeBankingDetails(){let t=document.getElementById("bankName"),a=document.getElementById("branchCode"),r=document.getElementById("accountHolder"),n=document.getElementById("firstName"),i=document.getElementById("lastName");if(t){for(;1<t.options.length;)t.remove(1);Object.keys(l).forEach(e=>{e=l[e],e=new Option(e.name,e.name);t.add(e)}),t.addEventListener("change",()=>{var e=l[t.value];e?(a.value=e.universalCode,a.readOnly=!0,a.classList.add("auto-populated")):(a.value="",a.readOnly=!1,a.classList.remove("auto-populated"))});var e=()=>{var e,t;n&&i&&(e=n.value.trim(),t=i.value.trim(),e)&&t&&(r.value=e+" "+t)};n&&i&&(n.addEventListener("input",e),i.addEventListener("input",e))}}async handleSubmit(e){e.preventDefault();try{let e=new FormData(this.form),t=document.getElementById("directorCheckbox").checked,a={firstName:e.get("firstName"),lastName:e.get("lastName"),idType:e.get("idType")||"none",idNumber:e.get("idNumber"),dob:e.get("dob")||null,email:e.get("email"),personalDetails:{mobileNumber:e.get("personalDetails.mobileNumber")||null},streetAddress:e.get("streetAddress"),suburb:e.get("suburb"),city:e.get("city"),postalCode:e.get("postalCode"),bankName:e.get("bankName"),accountType:e.get("accountType"),accountNumber:e.get("accountNumber"),branchCode:e.get("branchCode"),holderRelationship:e.get("holderRelationship"),payFrequency:e.get("payFrequency"),workingHours:e.get("workingHours"),doa:e.get("doa"),jobTitle:e.get("jobTitle"),department:e.get("department"),costCentre:e.get("costCentre"),isDirector:t,typeOfDirector:t?e.get("typeOfDirector"):"",regularHours:{hourlyPaid:document.getElementById("hourlyPaid").checked,hoursPerDay:parseFloat(e.get("hoursPerDay"))||8,schedule:e.get("schedule"),workingDays:Array.from(document.querySelectorAll('input[name="workingDays[]"]:checked')).map(e=>e.value),fullDaysPerWeek:parseFloat(document.getElementById("fullDaysPerWeek").value)||5},payFrequencyDetails:{id:e.get("payFrequency"),frequency:document.querySelector(`option[value="${e.get("payFrequency")}"]`)?.getAttribute("data-frequency"),lastDayOfPeriod:document.querySelector(`option[value="${e.get("payFrequency")}"]`)?.getAttribute("data-lastday")}},r=document.querySelector('input[name="_csrf"]'),n={"Content-Type":"application/json"},i=(r&&r.value&&(n["CSRF-Token"]=r.value),await fetch(this.form.action,{method:"POST",headers:n,body:JSON.stringify(a)}));if(!i.ok){let e=await i.json();throw new Error(e.message||"Failed to create employee")}await i.json(),window.location.href=`/clients/${companyCode}/employeeManagement`}catch(e){alert(e.message||"Failed to create employee. Please try again.")}}validateStep(e){let t=this.form.querySelectorAll(`[data-step="${e}"] [required]`),a=!0;return t.forEach(e=>{this.clearFieldError(e),e.value.trim()||(this.showFieldError(e,"This field is required"),a=!1),"payFrequency"!==e.id||e.value||(this.showFieldError(e,"Please select a pay frequency"),a=!1)}),a}initializeSteps(){this.steps=document.querySelectorAll(".form-step"),this.steps.forEach((e,t)=>{0===t?(e.classList.add("active"),e.style.display="block"):(e.classList.remove("active"),e.style.display="none")})}initializeEventListeners(){var e=document.querySelectorAll(".next-step"),t=document.querySelectorAll(".prev-step"),a=document.querySelector('button[type="submit"]'),e=(e.forEach(e=>{e.addEventListener("click",e=>{e=parseInt(e.target.closest(".form-step").dataset.step);this.nextStep(e)})}),t.forEach(e=>{e.addEventListener("click",e=>{e=parseInt(e.target.closest(".form-step").dataset.step);this.prevStep(e)})}),a&&a.addEventListener("click",e=>this.handleSubmit(e)),document.querySelectorAll('input[name="workingDays[]"]').forEach(e=>{e.addEventListener("change",()=>this.updateFullDaysPerWeek())}),document.getElementById("idType")),t=(e&&(e.addEventListener("change",()=>this.handleIdTypeChange()),this.handleIdTypeChange()),document.getElementById("sameAsResidential"));t&&(t.addEventListener("change",()=>this.handlePostalAddressChange()),this.handlePostalAddressChange()),this.form.addEventListener("submit",e=>this.handleSubmit(e))}nextStep(e){this.validateStep(e)&&e<this.totalSteps&&(this.steps[e-1].classList.remove("active"),this.steps[e-1].style.display="none",this.steps[e].classList.add("active"),this.steps[e].style.display="block",this.currentStep=e+1,this.updateStepIndicator(this.currentStep),this.scrollToTop())}prevStep(e){1<e&&(this.steps[e-1].classList.remove("active"),this.steps[e-1].style.display="none",this.steps[e-2].classList.add("active"),this.steps[e-2].style.display="block",this.currentStep=e-1,this.updateStepIndicator(this.currentStep),this.scrollToTop())}updateStepIndicator(r){var e=document.querySelectorAll(".step-number"),t=document.querySelector(".progress-line");if(e.forEach((t,e)=>{e+=1;if(t.classList.remove("active","completed"),e<r){t.classList.add("completed");var a=t.querySelector(".tick-mark");a||((a=document.createElement("i")).className="ph ph-check tick-mark",t.appendChild(a))}else if(e===r){t.classList.add("active");let e=t.querySelector(".tick-mark");e&&e.remove()}else{let e=t.querySelector(".tick-mark");e&&e.remove()}}),t){let e=(r-1)/(this.totalSteps-1)*100;t.style.width=e+"%"}}scrollToTop(){var e=document.querySelector(".employee-section")||document.querySelector(".step-indicator")||document.querySelector(".main-container");e?e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}):window.scrollTo({top:0,behavior:"smooth"})}showFieldError(e,t){e.classList.add("invalid"),this.clearFieldError(e);var a=document.createElement("div");a.className="error-message",a.textContent=t,a.style.color="red",a.style.fontSize="0.875rem",a.style.marginTop="0.25rem",e.parentNode.appendChild(a),e.style.borderColor="red"}clearFieldError(e){e.classList.remove("invalid"),e.style.borderColor="";e=e.parentNode.querySelector(".error-message");e&&e.remove()}handleFieldChange(e){var{name:e,value:t,type:a,checked:r}=e.target;this.employeeData[e]="checkbox"===a?r:t,this.updatePreview(),this.saveToLocalStorage()}updatePreview(){var t=document.querySelector(".preview-details");if(t){let e="";this.employeeData.isDirector&&(e+=this.createPreviewItem("Employment Type","Director"),this.employeeData.typeOfDirector)&&(e+=this.createPreviewItem("Director Type",this.employeeData.typeOfDirector)),(this.employeeData.firstName||this.employeeData.lastName)&&(e+=this.createPreviewItem("Name",`${this.employeeData.firstName||""} `+(this.employeeData.lastName||""))),this.employeeData.workingHours&&(e+=this.createPreviewItem("Working Hours",this.employeeData.workingHours)),t.innerHTML=e}}createPreviewItem(e,t){return`
      <div class="preview-item">
        <div class="preview-label">${e}</div>
        <div class="preview-value">${t}</div>
      </div>
    `}saveToLocalStorage(){localStorage.setItem("employeeFormData",JSON.stringify(this.employeeData))}populateFormFields(){Object.entries(this.employeeData).forEach(([e,t])=>{e=this.form.elements[e];e&&("checkbox"===e.type?e.checked=t:e.value=t)})}handleIdTypeChange(){var e=document.getElementById("idType").value,t=document.getElementById("idNumberContainer"),a=document.getElementById("passportNumberContainer"),r=document.getElementById("idNumber"),n=document.getElementById("passportNumber");switch(t.style.display="none",a.style.display="none",r.removeAttribute("required"),n.removeAttribute("required"),"none"===e&&(r.value="",n.value=""),e){case"rsa":t.style.display="block",r.setAttribute("required","required"),n.value="";break;case"passport":a.style.display="block",n.setAttribute("required","required"),r.value=""}}handlePostalAddressChange(){var e=document.getElementById("sameAsResidential").checked,t=document.getElementById("postalAddressFields");e?(t.style.display="none",document.querySelectorAll(".postal-address-field").forEach(e=>{e.value="",e.disabled=!0})):(t.style.display="block",document.querySelectorAll(".postal-address-field").forEach(e=>{e.disabled=!1}))}populatePayFrequencies(e){let a=document.getElementById("payFrequency");a&&e&&(a.innerHTML='<option value="">Select a pay frequency</option>',e.forEach(e=>{var t=document.createElement("option");t.value=e._id,t.textContent=e.name,t.dataset.frequency=e.frequency,t.dataset.lastDay=e.lastDayOfPeriod,a.appendChild(t)}))}initializeRegularHours(){var e=document.getElementById("hourlyPaid");e&&e.addEventListener("change",e=>{this.handleHourlyPaidChange(e.target.checked)}),document.querySelectorAll('input[name="workingDays[]"]').forEach(e=>{e.addEventListener("change",()=>this.updateFullDaysPerWeek())});let t=document.getElementById("overrideButton"),a=document.getElementById("overrideInput"),r=document.querySelector(".btn-cancel-override"),n=document.getElementById("fullDaysPerWeek"),i=document.getElementById("fullDaysPerWeekOverride");t&&t.addEventListener("click",()=>{t.style.display="none",a.style.display="flex",i.value=n.textContent,i.focus()}),r&&r.addEventListener("click",()=>{a.style.display="none",t.style.display="flex",this.updateFullDaysPerWeek()}),i&&i.addEventListener("change",e=>{e=parseFloat(e.target.value);0<=e&&e<=7&&(n.textContent=e.toFixed(1))})}updateFullDaysPerWeek(){var e=document.querySelectorAll('input[name="workingDays[]"]:checked').length,t=document.getElementById("fullDaysPerWeek"),a=document.querySelector('input[name="fullDaysPerWeek"]');t&&a&&(t.textContent=e.toFixed(1),a.value=e.toFixed(1))}handleHourlyPaidChange(e){}initializeFlatpickr(){if("undefined"!=typeof flatpickr){var e={dateFormat:"Y-m-d",allowInput:!0,altInput:!0,altFormat:"F j, Y",disableMobile:!1,animate:!0,monthSelectorType:"static",nextArrow:'<i class="ph ph-caret-right"></i>',prevArrow:'<i class="ph ph-caret-left"></i>',theme:"modern"},t=document.getElementById("dob");if(t)try{flatpickr(t,{...e,maxDate:"today",defaultDate:null,allowInput:!0,placeholder:"Select date of birth"})}catch(e){}t=document.getElementById("doa");if(t)try{flatpickr(t,{...e})}catch(e){}}}initializeDirectorFields(){let t=document.getElementById("directorTypeGroup"),a=document.getElementById("directorCheckbox"),r=document.getElementById("typeOfDirector");t&&a&&r&&(t.setAttribute("style","display: none !important"),r.required=!1,r.value="",a.addEventListener("change",e=>{t.setAttribute("style",a.checked?"display: block !important":"display: none !important"),r.required=a.checked,a.checked||(r.value="")}),a.dispatchEvent(new Event("change")))}}function e(r){var e=document.querySelector(".step-indicator"),t=e.querySelectorAll(".step-number");e.dataset.currentStep=r,t.forEach((t,e)=>{e+=1;if(t.classList.remove("active","completed"),e<r){t.classList.add("completed");var a=t.querySelector(".tick-mark");a||((a=document.createElement("i")).className="ph ph-check tick-mark",t.appendChild(a))}else if(e===r){t.classList.add("active");let e=t.querySelector(".tick-mark");e&&e.remove()}else{let e=t.querySelector(".tick-mark");e&&e.remove()}})}document.addEventListener("DOMContentLoaded",()=>{var e=new i,t=document.getElementById("idType"),t=(t&&(t.value="rsa",e.handleIdTypeChange()),document.getElementById("sameAsResidential"));t&&t.checked&&e.handlePostalAddressChange(),window.payFrequencies&&e.populatePayFrequencies(window.payFrequencies);let a=document.getElementById("paymentMethod"),r=document.getElementById("bankingDetailsFields");function n(e){var t;r&&(t=r.querySelectorAll("select[required], input[required]"),"eft"===e?(r.style.display="block",r.classList.add("show"),["bankName","accountType","accountNumber","accountHolder"].forEach(e=>{e=document.getElementById(e);e&&e.setAttribute("required","")})):(r.style.display="none",r.classList.remove("show"),t.forEach(e=>{e.removeAttribute("required")})))}a&&(n(a.value),a.addEventListener("change",function(){n(this.value)}))}),window.nextStep=function(e){window.employeeForm&&window.employeeForm.nextStep(e)},window.prevStep=function(e){window.employeeForm&&window.employeeForm.prevStep(e)};let t=document.getElementById("bankName"),a=document.getElementById("branchCode"),r=document.getElementById("accountHolder"),n=document.getElementById("firstName"),o=document.getElementById("lastName");function s(){var e,t;n&&o&&(e=n.value.trim(),t=o.value.trim(),e)&&t&&(r.value=e+" "+t)}t&&(Object.keys(l).forEach(e=>{e=l[e],e=new Option(e.name,e.name);t.appendChild(e)}),t.addEventListener("change",function(){var e=l[t.value];e&&(a.value=e.universalCode,e.universalCode?(a.readOnly=!0,a.classList.add("auto-populated")):(a.readOnly=!1,a.classList.remove("auto-populated")))}),n)&&o&&(n.addEventListener("input",s),o.addEventListener("input",s));