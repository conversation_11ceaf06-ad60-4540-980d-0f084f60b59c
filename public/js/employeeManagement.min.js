document.addEventListener("DOMContentLoaded",function(){try{let a={statusFilter:document.getElementById("statusFilter"),searchInput:document.getElementById("searchInputEmployee"),tableBody:document.getElementById("employeeTableBody"),paginationInfo:document.getElementById("paginationInfo"),prevButton:document.getElementById("prevButton"),nextButton:document.getElementById("nextButton")};if(a.tableBody){let r={allEmployees:[],currentPage:1,rowsPerPage:10,filteredEmployees:[]};if(window.prevPage=function(){1<r.currentPage&&(r.currentPage--,l(),s())},window.nextPage=function(){var e=Math.ceil(r.filteredEmployees.length/r.rowsPerPage);r.currentPage<e&&(r.currentPage++,l(),s())},e=Array.from(a.tableBody.getElementsByTagName("tr")),r.allEmployees=e.map(e=>({element:e,employeeNumber:e.cells[0].textContent.toLowerCase(),name:e.querySelector(".employee-name").textContent.toLowerCase(),costCentre:e.cells[2].textContent.toLowerCase(),status:e.querySelector(".status-badge").textContent.trim()})),r.filteredEmployees=[...r.allEmployees],a.statusFilter&&a.statusFilter.addEventListener("change",()=>{r.currentPage=1,t()}),a.searchInput){let e;a.searchInput.addEventListener("input",()=>{clearTimeout(e),e=setTimeout(()=>{r.currentPage=1,t()},300)})}function t(){let n=a.searchInput.value.toLowerCase(),o=a.statusFilter.value;r.filteredEmployees=r.allEmployees.filter(e=>{var t=e.name.includes(n)||e.employeeNumber.includes(n)||e.costCentre.includes(n),e="all"===o||e.status.toLowerCase()===o.toLowerCase();return t&&e}),l(),s()}function l(){var e=(r.currentPage-1)*r.rowsPerPage,t=e+r.rowsPerPage,e=r.filteredEmployees.slice(e,t);a.tableBody.innerHTML="",e.forEach(e=>{a.tableBody.appendChild(e.element.cloneNode(!0))}),n()}function s(){var e=Math.ceil(r.filteredEmployees.length/r.rowsPerPage);a.paginationInfo.textContent=`Showing ${Math.min(r.filteredEmployees.length,r.rowsPerPage)} of ${r.filteredEmployees.length} entries`,a.prevButton.disabled=1===r.currentPage,a.nextButton.disabled=r.currentPage>=e}function n(){document.querySelectorAll("#employeeTableBody tr").forEach(e=>{e.addEventListener("click",function(){var e,t=this.dataset.employeeId;t&&(e=document.body.dataset.companyCode,window.location.href=`/clients/${e}/employeeManagement/employee/`+t)}),e.addEventListener("mouseenter",function(){this.style.cursor="pointer",this.style.backgroundColor="rgba(99, 102, 241, 0.05)"}),e.addEventListener("mouseleave",function(){this.style.backgroundColor=""})})}n(),s()}}catch(e){}var e});