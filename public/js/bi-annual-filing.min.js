let i,o=[];function e(){var e=new Date,t=e.getFullYear();e.getMonth(),o=[],[t-1,t,t+1].forEach(e=>{o.push({id:e+"-Feb",label:e+` February (Sep ${e-1} - Feb ${e})`,startDate:new Date(e-1,8,1),endDate:new Date(e,1,29),dueDate:new Date(e,4,31)}),o.push({id:e+"-Aug",label:e+` August (Mar ${e} - Aug ${e})`,startDate:new Date(e,2,1),endDate:new Date(e,7,31),dueDate:new Date(e,9,31)})}),o.sort((e,t)=>t.startDate-e.startDate);let a=new Date,n=(i=o.find(e=>a>=e.startDate&&a<=e.dueDate)?.id||o[0].id,document.getElementById("filingSeason"));n&&(n.innerHTML=o.map(e=>`
      <option value="${e.id}" ${e.id===i?"selected":""}>
        ${e.label}
      </option>
    `).join(""),n.addEventListener("change",()=>{i=n.value,r()})),r()}function r(){var t=o.find(e=>e.id===i);if(t){var e=document.querySelector(".period-display"),a=(e&&(e.innerHTML=`
      <div class="period-info">
        <span class="label">Period:</span>
        <span class="value">${t.label}</span>
      </div>
      <div class="period-info">
        <span class="label">Due Date:</span>
        <span class="value ${new Date>t.dueDate?"overdue":""}">
          ${t.dueDate.toLocaleDateString("en-ZA",{day:"numeric",month:"long",year:"numeric"})}
        </span>
      </div>
    `),document.querySelector(".submit-button"));if(a){let e=new Date>t.dueDate;(a.disabled=e)&&(a.title="Submission period has ended")}}}function t(){var e=document.getElementById("filingSeason").value;window.location.href=`/clients/${companyCode}/bi-annual-filing?season=`+e}async function a(){try{let t=o.find(e=>e.id===i);if(t){var e=document.querySelector(".modern-table tbody"),a=(e.innerHTML='<tr><td colspan="7" class="loading">Loading employee data...</td></tr>',await fetch(`/clients/${companyCode}/filing/bi-annual/employees?period=`+i,{method:"GET",headers:{"Content-Type":"application/json"}}));if(!a.ok)throw new Error("Failed to fetch employee data");var n=await a.json();if(!n.success)throw new Error(n.error||"Failed to load employee data");n.employees&&0<n.employees.length?(e.innerHTML=n.employees.map(e=>`
        <tr data-employee-id="${e._id}">
          <td>${e.firstName}</td>
          <td>${e.lastName}</td>
          <td>${e.companyEmployeeNumber||"Not assigned"}</td>
          <td>${t.label}</td>
          <td>Bi-annual</td>
          <td>IRP5</td>
          <td>
            <button class="action-button primary" 
                    onclick="viewCertificate('${e._id}', '${i}')">
              <i class="ph ph-eye"></i>
              View
            </button>
          </td>
        </tr>
      `).join(""),document.getElementById("certificateCount").textContent=n.employees.length,document.querySelectorAll(".bulk-actions-section button").forEach(e=>{e.disabled=!1})):(e.innerHTML='\n        <tr>\n          <td colspan="7" class="no-data">\n            No employees found for the selected filing season\n          </td>\n        </tr>\n      ',document.getElementById("certificateCount").textContent="0",document.querySelectorAll(".bulk-actions-section button").forEach(e=>{e.disabled=!0}))}else m("error","No filing season selected")}catch(e){document.querySelector(".modern-table tbody").innerHTML=`
      <tr>
        <td colspan="7" class="error">
          Error loading employee data: ${e.message}
        </td>
      </tr>
    `,m("error","Failed to load employee data")}}async function n(){try{var e,t=await fetch(`/clients/${companyCode}/filing/bi-annual/pre-validate?period=`+i,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw e=await t.json(),new Error(e.errors?.join("\n")||"Validation failed");var a=await t.json();if(a.success)return m("success","All validations passed. You can now proceed with the submission."),!0;{let t="Validation failed:\n\n";return 0<a.unfinalizedEmployees?.length&&(t+="Unfinalized Payrolls:\n",a.unfinalizedEmployees.forEach(e=>{t+=`- ${e.name} (${e.month})
`})),0<a.employeesWithMissingInfo?.length&&(t+="\nMissing Tax Information:\n",a.employeesWithMissingInfo.forEach(e=>{t+=`- ${e.name} (${e.missingFields.join(", ")})
`})),m("error",t),!1}}catch(e){return m("error",e.message),!1}}async function l(t,a){try{var n=`/clients/${companyCode}/filing/bi-annual/${a}/certificate/`+t,i=await fetch(n,{method:"GET",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(i.ok){let e=i.headers.get("content-type");if(e&&e.includes("application/pdf"))window.open(n,"_blank"),m("success","IRP5 certificate downloaded successfully");else{let e=await i.json();m("error",e.error||"Unexpected response format")}}else{let e=await i.json(),t=e.error||"Failed to generate IRP5 certificate";e,m("error",t)}}catch(e){try{window.open(`/clients/${companyCode}/filing/bi-annual/${a}/certificate/`+t,"_blank")}catch(t){m("error","Failed to download IRP5 certificate. Please try again.")}}}async function s(){try{var a=document.getElementById("filingSeason").value,n=event.currentTarget,e=n.innerHTML,i=(n.innerHTML='<i class="ph ph-spinner ph-spin"></i> Generating...',n.disabled=!0,Array.from(document.querySelectorAll("tr[data-employee-id]")).filter(e=>e.dataset.employeeId)),o=i.length;if(0===o)m("warning","No certificates to generate");else{var r,l=document.createElement("div"),s=(l.className="progress-bar",l.innerHTML=`<div class="progress-text">Generating 0/${o} certificates...</div>`,n.parentNode.insertBefore(l,n.nextSibling),[]);let e=0,t=0;for(r of i){var c=r.dataset.employeeId;if(c)try{var d=await fetch(`/clients/${companyCode}/filing/bi-annual/${a}/certificate/`+c,{method:"GET",headers:{Accept:"application/pdf"}});if(!d.ok)throw new Error("Failed to generate certificate for employee "+c);var u=await d.blob();s.push({blob:u,employeeNumber:r.querySelector("td:nth-child(3)").textContent.trim()}),e++,l.innerHTML=`
          <div class="progress-text">
            Generating ${e}/${o} certificates...
            ${0<t?`(${t} errors)`:""}
          </div>`}catch(e){t++}}if(0===s.length)throw new Error("No certificates were generated successfully");var p=await f(s);download(p,`Tax_Certificates_${a}.pdf`,"application/pdf"),l.remove(),m(0<t?"warning":"success",`Generated ${e-t} certificates`+(0<t?` (${t} failed)`:""))}n.innerHTML=e,n.disabled=!1}catch(e){m("error","Failed to generate certificates: "+e.message),currentButton&&(currentButton.innerHTML=originalText,currentButton.disabled=!1)}}async function f(e){let a=PDFLib.PDFDocument,n=await a.create();for(var i of e){let e=await i.blob.arrayBuffer(),t=await a.load(e);(await n.copyPages(t,t.getPageIndices())).forEach(e=>n.addPage(e))}return new Blob([await n.save()],{type:"application/pdf"})}function m(e,t){t=t.replace(/</g,"&lt;").replace(/>/g,"&gt;");let a=document.createElement("div"),n;switch(a.className="toast toast-"+e,e){case"success":n="ph-check-circle";break;case"error":n="ph-x-circle";break;case"warning":n="ph-warning-circle";break;default:n="ph-info"}a.innerHTML=`
    <i class="ph ${n}"></i>
    <span>${t}</span>
  `,document.body.appendChild(a);t=document.querySelectorAll(".toast");if(1<t.length){let e=Array.from(t).slice(0,-1).reduce((e,t)=>e+t.offsetHeight+10,0);a.style.top=20+e+"px"}setTimeout(()=>{a.classList.add("fade-out"),setTimeout(()=>{a.parentNode&&(a.parentNode.removeChild(a),c())},500)},5e3)}function c(){document.querySelectorAll(".toast").forEach((e,t)=>{e.style.top=20+t*(e.offsetHeight+10)+"px"})}function d(){var e=document.getElementById("filingSeason").value;window.location.href=`/clients/${companyCode}/filing/bi-annual/${e}/export`}function u(){var e=document.getElementById("filingSeason").value;window.location.href=`/clients/${companyCode}/filing/bi-annual/${e}/emp501`}document.addEventListener("DOMContentLoaded",()=>{companyCode?(e(),a()):alert("Error: Company code not found. Please refresh the page or contact support.")});