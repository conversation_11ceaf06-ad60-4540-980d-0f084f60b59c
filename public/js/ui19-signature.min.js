document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("ui19Form");if(!e)return void console.log("Not on UI19 form page, skipping initialization");let t;const n=document.getElementById("clearSignature");n&&n.addEventListener("click",function(){if(console.log("\n=== Clearing Signature ==="),t){t.clear();const e=document.getElementById("signatureData");e&&(e.value="",console.log("Signature data cleared"))}}),console.log("\n=== Starting UI19 Signature Initialization ==="),function(){console.log("\n=== Initializing Signature Pad ===");const e=document.getElementById("signatureCanvas");function n(){const n=Math.max(window.devicePixelRatio||1,1);e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").scale(n,n),t.clear()}e?(t=new SignaturePad(e,{backgroundColor:"rgba(255, 255, 255, 0)",penColor:"rgb(0, 0, 0)",minWidth:.5,maxWidth:2.5}),t.toDataURL=function(e="image/png"){console.log("\n=== Processing Signature for Transparency ===");const t=this._canvas,n=(t.getContext("2d").getImageData(0,0,t.width,t.height).data,document.createElement("canvas"));n.width=t.width,n.height=t.height;const o=n.getContext("2d");o.globalCompositeOperation="source-over",o.clearRect(0,0,n.width,n.height),o.drawImage(t,0,0);const a=n.toDataURL(e);return console.log("Signature processed with transparency:",{width:n.width,height:n.height,hasTransparency:!0,imageType:e}),a},window.addEventListener("resize",n),n()):console.error("Error: Signature canvas element not found")}(),e.addEventListener("submit",async t=>{t.preventDefault(),console.log("Form submission started");const n=new FormData;e.querySelectorAll("input, select, textarea").forEach(e=>{e.name&&e.value&&(n.set(e.name,e.value),console.log(`Added field: ${e.name}=${e.value}`))});const o=document.getElementById("signatureData");o&&(n.set("signatureData",o.value),n.set("signatureType","draw"));const a=document.querySelector('input[name="_csrf"]');a&&n.set("_csrf",a.value);try{const t=await fetch(e.action,{method:"POST",body:n,headers:a?{"csrf-token":a.value}:{}});if(t.ok){const e=await t.blob(),n=window.URL.createObjectURL(e),o=document.createElement("a");o.href=n,o.download="UI19.pdf",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(n)}else console.error("Form submission failed:",t.statusText),alert("Failed to submit form. Please try again.")}catch(e){console.error("Error submitting form:",e),alert("Error submitting form. Please try again.")}})});