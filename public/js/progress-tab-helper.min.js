function openProgressTab(e,t){const o=document.querySelectorAll(".progress-tab-content");for(let e=0;e<o.length;e++)o[e].classList.remove("active");const n=document.querySelectorAll(".progress-tab-link");for(let e=0;e<n.length;e++)n[e].classList.remove("active");document.getElementById(t).classList.add("active"),e.currentTarget.classList.add("active");const s=t.replace("progress-","");document.querySelectorAll(".pay-run-history-tab").forEach(e=>{e.classList.remove("active")});const r=document.getElementById(`pay-run-history-${s}`);r&&r.classList.add("active"),"function"==typeof updatePaginationLinks&&updatePaginationLinks(s)}document.addEventListener("DOMContentLoaded",function(){void 0===window.originalOpenProgressTab&&(window.originalOpenProgressTab=window.openProgressTab,window.openProgressTab=openProgressTab)});