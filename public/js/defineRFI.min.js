let e=0,t=100;class n{constructor(){this.components=new Map,this.selectedMethod="",this.totalRFI=0}addComponent(e,t,n=100){var o=parseFloat(t).toLocaleString("en-ZA",{minimumFractionDigits:2,maximumFractionDigits:2});this.components.set(e,{amount:parseFloat(t)||0,percentage:parseFloat(n)||100,formattedAmount:o}),this.calculate()}removeComponent(e){this.components.delete(e),this.calculate()}updatePercentage(e,t){this.components.has(e)&&(this.components.get(e).percentage=parseFloat(t)||0,this.calculate())}calculate(){return this.totalRFI=0,this.components.forEach((e,t)=>{e=e.amount*e.percentage/100;this.totalRFI+=e}),this.totalRFI}getComponentBreakdown(){let n=[];return this.components.forEach((e,t)=>{n.push({name:t,amount:e.amount,percentage:e.percentage,contribution:e.amount*e.percentage/100})}),n}}class a{constructor(e){this.calculator=e,this.form=document.getElementById("rfiForm"),this.methodSelect=document.getElementById("rfiOptions"),this.selectedIncomeCard=document.getElementById("selectedIncomeCard"),this.percentageCard=document.getElementById("percentageCard"),this.componentContainer=document.querySelector(".component-grid"),this.PAY_COMPONENTS={EARNINGS:["basicSalary","overtime","bonus","commission","allowance","leaveEncashment","noticePayment","otherEarnings"],BENEFITS:["travelAllowance","accommodationBenefit","companyCarBenefit","companyCarUnderOperatingLeaseBenefit","incomeProtectionBenefit","retirementAnnuityFundBenefit"],DEDUCTIONS:["medicalAid","pensionFund","providentFund","uif","paye","garnishee","maintenanceOrder","unionMembershipFee","voluntaryTaxOverDeduction"]},this.initializeEventListeners(),this.loadExistingConfiguration()}initializeEventListeners(){this.methodSelect?.addEventListener("change",e=>{this.handleMethodChange(e.target.value)}),this.form?.addEventListener("submit",e=>this.handleSubmit(e))}handleMethodChange(e){this.selectedIncomeCard.style.display="selectedIncome"===e?"block":"none",this.percentageCard.style.display="percentagePerIncome"===e?"block":"none",this.populateComponents(e)}populateComponents(t){this.componentContainer&&(this.componentContainer.innerHTML="",[...this.PAY_COMPONENTS.EARNINGS,...this.PAY_COMPONENTS.BENEFITS].forEach(e=>{e=this.createComponentCard(e,t);this.componentContainer.appendChild(e)}))}createComponentCard(e,t){var n=document.createElement("div");return n.className="component-item",n.dataset.component=e,n.innerHTML="selectedIncome"===t?`
        <div class="checkbox-wrapper">
          <input type="checkbox" 
                 id="${e}" 
                 name="selectedIncomes[]" 
                 value="${e}">
          <label for="${e}">
            <i class="ph ph-check"></i>
            ${this.formatComponentName(e)}
          </label>
        </div>
      `:`
        <div class="component-details">
          <span class="component-name">${this.formatComponentName(e)}</span>
          <div class="percentage-input-wrapper">
            <input type="number" 
                   name="percentageValues[${e}]" 
                   class="percentage-input"
                   min="0"
                   max="100"
                   step="0.01"
                   placeholder="Enter %">
            <i class="ph ph-percent"></i>
          </div>
        </div>
      `,n}formatComponentName(e){return e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()).trim()}loadExistingConfiguration(){var e=this.methodSelect?.value;e&&(this.handleMethodChange(e),window.existingConfig)&&this.populateSavedValues(window.existingConfig)}populateSavedValues(e){"selectedIncome"===e.option?e.selectedIncomes?.forEach(e=>{e=document.querySelector(`input[value="${e}"]`);e&&(e.checked=!0)}):"percentagePerIncome"===e.option&&Object.entries(e.percentageValues||{}).forEach(([e,t])=>{e=document.querySelector(`input[name="percentageValues[${e}]"]`);e&&(e.value=t)})}showToast(e,t="info"){let n=document.createElement("div");n.className="toast-notification "+t,n.innerHTML=`
      <i class="ph ${"success"===t?"ph-check-circle":"ph-warning-circle"}"></i>
      <span>${e}</span>
    `,document.body.appendChild(n),setTimeout(()=>{n.classList.add("fade-out"),setTimeout(()=>n.remove(),300)},3e3)}async handleSubmit(e){e.preventDefault();try{let e=new FormData(this.form),t=await fetch(this.form.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({option:e.get("rfiOption"),selectedIncomes:Array.from(e.getAll("selectedIncomes[]")),percentageValues:Object.fromEntries(Array.from(e.entries()).filter(([e])=>e.startsWith("percentageValues[")).map(([e,t])=>[e.match(/\[(.*?)\]/)[1],parseFloat(t)||0]))})}),n=await t.json();if(!n.success)throw new Error(n.message||"Failed to save RFI configuration");this.showToast("RFI configuration saved successfully","success"),setTimeout(()=>{window.location.href=n.redirectUrl},1500)}catch(e){this.showToast(e.message||"Failed to save RFI configuration","error")}}}document.addEventListener("DOMContentLoaded",()=>{let o=new n;document.querySelectorAll(".checkbox-wrapper").forEach(e=>{var t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".amount");if(n){let e=parseFloat(n.textContent.replace(/[^0-9.-]+/g,""));o.addComponent(t.value,e)}}),window.rfiForm=new a(o)});