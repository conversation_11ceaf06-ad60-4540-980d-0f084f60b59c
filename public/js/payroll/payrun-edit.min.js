document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("editPayRunForm");window.viewEmployeeDetails=e=>{window.location.href=`/employees/${e}/profile`},e.addEventListener("submit",async t=>{t.preventDefault();try{const t=new FormData(e),n={};for(let[e,a]of t.entries()){const t=e.match(/^payslips\[(\d+)\]\[(\w+)\]$/);if(t){const e=t[1],o=t[2];n.payslips||(n.payslips=[]),n.payslips[e]||(n.payslips[e]={}),n.payslips[e][o]=a}else e.startsWith("payslips")||(n[e]=a)}const a=await fetch(e.action,{method:"POST",headers:{"Content-Type":"application/json","CSRF-Token":document.querySelector('input[name="_csrf"]').value},body:JSON.stringify(n)}),o=await a.json();o.success?window.location.href=`/payrun/${o.payRunId}/view`:alert(o.message||"Failed to update pay run")}catch(e){console.error("Error updating pay run:",e),alert("An error occurred while updating the pay run")}})});