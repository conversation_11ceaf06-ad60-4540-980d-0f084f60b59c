*{font-family:Inter,sans-serif}.main-container{padding:2rem;background:var(--background-color)}.page-header{background:#fff;border-radius:.75rem;padding:2rem;margin-bottom:2rem;display:flex;justify-content:space-between;align-items:center;box-shadow:0 4px 6px -1px rgba(0,0,0,.1)}main{flex:1;margin-left:260px;margin-top:80px;padding:24px;width:calc(100vw - 280px);min-height:calc(100vh - 80px);transition:.3s;position:relative;background:#f8fafc}.header-content h1{font-size:1.875rem;font-weight:600;color:#1e293b;margin-bottom:.5rem}.header-content .description{color:#64748b;font-size:.875rem}.company-badge{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;background:#f8fafc;border-radius:.5rem;color:#64748b;font-size:.875rem}.company-badge i{color:#6366f1}.regular-items{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:1.5rem;padding:.5rem}.card{background:#fff;border-radius:.75rem;box-shadow:0 1px 3px rgba(0,0,0,.1);border:1px solid #e2e8f0;transition:.2s;overflow:hidden}.card:hover{transform:translateY(-2px);box-shadow:0 4px 6px -1px rgba(0,0,0,.1)}.card-header{background:#6366f1;padding:1rem 1.25rem;border-bottom:1px solid #e2e8f0}.card-header h2{color:#fff;font-size:1.125rem;font-weight:600;margin:0;display:flex;align-items:center;gap:.75rem}.card-header h2::before{font-family:Phosphor;font-size:1.25rem}.card-header h2[data-category=Income]::before{content:"\f1c1"}.card-header h2[data-category=Allowance]::before{content:"\f1c2"}.card-header h2[data-category=Benefit]::before{content:"\f1c3"}.card-header h2[data-category=Deduction]::before{content:"\f1c4"}.card ul{list-style:none;padding:1rem;margin:0;display:flex;flex-direction:column;gap:.5rem}.pay-component-item{transition:.2s}.pay-component-item a{display:flex;align-items:center;padding:.875rem 1rem;color:#1e293b;text-decoration:none;font-weight:500;border-radius:8px;background:#f8fafc;transition:.2s;border:1px solid #e2e8f0;gap:.5rem}.pay-component-item a:hover{background:#f1f5f9;border-color:#cbd5e1;color:#6366f1}.pay-component-item a i{color:#6366f1;font-size:1.25rem}@keyframes shimmer{0%{background-position:200% 0}100%{background-position:-200% 0}}@media screen and (max-width:768px){.page-header{flex-direction:column;gap:1rem;text-align:center}.main-container{padding:1rem}.regular-items{grid-template-columns:1fr}}@keyframes fadeIn{from{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.page-header,.regular-items{animation:.5s ease-out forwards fadeIn}