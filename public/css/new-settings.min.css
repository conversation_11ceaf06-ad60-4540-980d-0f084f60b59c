main{margin-left:250px;padding:2rem;min-height:100vh;background:#f8f9fa}.title-section{background:linear-gradient(135deg,#fff 0,#f8f9fa 100%);border-radius:16px;padding:2rem;margin-bottom:2rem;box-shadow:0 4px 20px rgba(0,0,0,.05);border:1px solid rgba(63,81,181,.1);height:160px;display:flex;flex-direction:column;justify-content:center}.banner-content{display:flex;align-items:center;gap:1.5rem}.banner-icon{display:flex;align-items:center;justify-content:center;width:48px;height:48px;background:#3f51b5;border-radius:12px;color:#fff}.banner-text h1{color:#1a237e;font-size:1.75rem;font-weight:600;margin:0;line-height:1.2}.banner-subtitle{color:#5c6bc0;font-size:.95rem;margin:.5rem 0 0}.tabs-container{position:sticky;top:0;z-index:100;background:#fff;margin:0 -2rem 2rem;padding:1rem 2rem;border-bottom:1px solid #e8eaf6}.tabs-section{max-width:1400px;margin:0 auto}.tab-group{display:flex;gap:.5rem;padding:.5rem;background:#f8f9fa;border-radius:12px;border:1px solid #e8eaf6;width:100%}.tab-button{flex:1;display:flex;align-items:center;justify-content:flex-start;gap:.75rem;padding:.875rem 1.25rem;color:#666;text-decoration:none;border-radius:8px;font-weight:500;transition:.2s;white-space:nowrap;font-size:.95rem}.tab-button:hover{background:#fff;color:#3f51b5;transform:translateY(-1px)}.tab-button.active{background:#3f51b5;color:#fff}.tab-button svg{flex-shrink:0}.settings-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;padding:1.5rem}.settings-card{background:#fff;padding:1.5rem;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.05);border:1px solid #e8eaf6;text-decoration:none;transition:.2s}.settings-card:hover{transform:translateY(-4px);box-shadow:0 8px 16px rgba(63,81,181,.1);border-color:#3f51b5}.settings-card h3{color:#1a237e;margin:0 0 .5rem;font-size:1.2rem;font-weight:600}.settings-card p{color:#666;font-size:.95rem;margin:0;line-height:1.5}@media screen and (max-width:768px){main{margin-left:0;padding:1rem}.title-section{height:auto;min-height:160px;padding:1.5rem}.tabs-container{margin:0 -1rem 1.5rem;padding:.75rem}.tab-group{overflow-x:auto;-webkit-overflow-scrolling:touch;scrollbar-width:none;-ms-overflow-style:none}.tab-group::-webkit-scrollbar{display:none}.tab-button{flex:1 0 auto;justify-content:center;padding:.75rem 1rem}.settings-grid{grid-template-columns:1fr;padding:1rem}}@media print{.banner-icon,.tabs-container{display:none}main{margin:0;padding:0}.settings-card{break-inside:avoid;page-break-inside:avoid}}