@media screen and (max-width:768px){.main-container{padding:1rem;margin:60px 0 70px!important;width:100%!important}.page-header{padding:1rem;margin-bottom:1rem}.page-header h1{font-size:1.5rem;margin-bottom:.5rem}.description{font-size:.875rem;line-height:1.4}.company-badge{margin-top:.75rem;padding:.5rem .75rem;font-size:.75rem}.quick-actions-grid{display:flex;flex-direction:column;gap:1rem;margin-bottom:1rem}.action-card{width:100%;margin:0;border-radius:12px;box-shadow:0 1px 3px rgba(0,0,0,.1)}.card-header{padding:1rem}.header-title{gap:.5rem}.header-title h3{font-size:1rem}.header-actions{flex-wrap:wrap;gap:.5rem}.action-button{padding:.5rem .75rem;font-size:.875rem}.pending-payslips{padding:0}.frequency-section{margin-bottom:1.5rem}.frequency-header{padding:.75rem 1rem;background:#f8fafc;border-radius:8px;margin-bottom:.75rem}.frequency-header h3{font-size:.875rem}.period-section{margin-bottom:1rem}.period-header{padding:.5rem 1rem;margin-bottom:.5rem}.period-header h4{font-size:.875rem;flex-direction:column;align-items:flex-start;gap:.25rem}.period-range{font-size:.75rem;opacity:.7}.payslip-item{padding:.75rem;margin:0 .5rem .5rem;border-radius:8px;background:#fff;box-shadow:0 1px 2px rgba(0,0,0,.05)}.payslip-main{gap:.75rem}.employee-avatar{width:2.5rem;height:2.5rem;font-size:.875rem}.employee-info,.payslip-details{gap:.25rem}.employee-info .name{font-size:.875rem}.employee-info .employee-number{font-size:.75rem}.payslip-meta{gap:1rem;font-size:.75rem}.payslip-actions{padding-top:.75rem;margin-top:.75rem;gap:.5rem}.table-container{margin-top:1rem;background:#fff;border-radius:12px;box-shadow:0 1px 3px rgba(0,0,0,.1);overflow:hidden}.table-header{padding:1rem;flex-direction:column;gap:.75rem}.table-header h3{font-size:1rem}.table-actions{width:100%;justify-content:flex-start;gap:.5rem}.modern-table,.modern-table tbody,.modern-table td,.modern-table th,.modern-table thead,.modern-table tr{display:block}.modern-table thead tr{position:absolute;top:-9999px;left:-9999px}.modern-table tbody tr{margin:.5rem;padding:1rem;background:#fff;border-radius:8px;box-shadow:0 1px 2px rgba(0,0,0,.05);position:relative}.modern-table td{position:relative;padding:.5rem 0;padding-left:40%;text-align:right;border-bottom:1px solid #e2e8f0}.modern-table td:last-child{border-bottom:none}.modern-table td:before{position:absolute;left:0;width:35%;padding-right:10px;text-align:left;font-weight:500;color:#64748b}.modern-table td:first-of-type:before{content:"Period"}.modern-table td:nth-of-type(2):before{content:"Status"}.modern-table td:nth-of-type(3):before{content:"Employees"}.modern-table td:nth-of-type(4):before{content:"Total Net Pay"}.modern-table td:nth-of-type(5):before{content:"Actions"}.status-badge{display:inline-block;padding:.25rem .5rem;border-radius:100px;font-size:.75rem}.pagination-container{padding:1rem;flex-direction:column;gap:.75rem}.pagination-info{text-align:center;font-size:.875rem}.pagination-buttons{display:grid;grid-template-columns:1fr 1fr;gap:.5rem}.pagination-button{padding:.75rem;font-size:.875rem;width:100%;justify-content:center}.empty-state{padding:2rem 1rem}.empty-state i{font-size:2rem;margin-bottom:.75rem}.fab-new-payrun{position:fixed;right:1rem;bottom:calc(70px + 1rem);width:56px;height:56px;border-radius:50%;background:#6366f1;color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px rgba(99,102,241,.25);border:none;cursor:pointer;z-index:100}.fab-new-payrun i{font-size:1.5rem}}@media screen and (min-width:769px){.fab-new-payrun{display:none!important}}