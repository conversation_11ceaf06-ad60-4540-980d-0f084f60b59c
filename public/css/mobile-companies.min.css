@media screen and (max-width:768px){.sidebar{display:none!important}.main-container{margin-top:60px!important;margin-left:0!important;padding:1rem!important;width:100%!important;margin-bottom:70px!important}.page-header{flex-direction:column;gap:1rem;padding:1rem;margin:-1rem -1rem 1rem;background:#fff;border-bottom:1px solid var(--border-color)}.header-content h1{font-size:1.25rem}.header-content .description{font-size:.875rem}.header-actions{display:flex;flex-direction:column;gap:.75rem;width:100%}.action-button{width:100%;justify-content:center}.companies-grid{grid-template-columns:1fr!important;gap:1rem}.company-card{padding:1rem}.toggle-switch{width:100%}.switch-label{width:100%;justify-content:space-between}.modal-content{width:90%!important;max-width:none;margin:20px;padding:1.25rem}.modal-header{padding:0 0 1rem}.modal-body{padding:1rem 0}.modal-footer{padding:1rem 0 0}}