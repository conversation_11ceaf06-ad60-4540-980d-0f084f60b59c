.button {
  padding: 10px 20px;
  font-family: "Lato", sans-serif;
  font-size: 16px;
  width: 375px;
  background: black;
  color: white;
  text-align: center;
  border-radius: 10px;
  position: relative;
  overflow: visible;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
}

.button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: black;
  border-radius: 10px;
  z-index: -1;
}

.button::after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background-image: conic-gradient(
    from var(--angle),
    #5500ff,
    #00ffd0,
    #0000ff,
    #00b7ff
  );
  border-radius: 14px;
  z-index: -2;
  padding: 10px;
  filter: blur(5px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

@keyframes rotate {
  from {
    --angle: 0deg;
  }
  to {
    --angle: 360deg;
  }
}

.button:hover::after {
  opacity: 1;
  animation: rotate 4s linear infinite;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.button:active {
  transform: translateY(1px);
}
