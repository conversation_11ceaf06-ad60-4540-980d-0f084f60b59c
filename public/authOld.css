
:root {
 --primary-color: #4D5DFF;
 --primary-hover: #4D5DFF;
 --primary-glow: rgba(77, 93, 255, 0.5);
 --text-primary: #000000;
 --text-secondary: #666666;
 --background-color: #ffffff;
 --input-border: #e5e7eb;
 --input-focus: #4D5DFF;
 --gradient-start: #ffffff;
 --gradient-end: #ffffff;
}

body {
 display: flex;
 min-height: 100vh;
 margin: 0;
 background: #ffffff !important;
 background-image: none !important;
 font-family: "Syne", sans-serif;
}

.left-side {
 flex: 1;
 background: #000C33 !important;
 display: flex;
 align-items: center;
 justify-content: center;
 position: relative;
 overflow: hidden;
 color: white !important;
}

.left-side * {
 color: white !important;
}

body .left-side {
 background: #000C33 !important;
 color: white !important;
}

.right-side {
 flex: 1;
 padding: 3rem 2rem;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 background: #ffffff !important;
 background-image: none !important;
}

.right-side::before {
 content: none !important;
 background: none !important;
}

.container {
 width: 100%;
 max-width: 380px;
 margin: 0 auto;
 padding: 0 1rem;
 box-sizing: border-box;
 background-color: #ffffff !important;
 background-image: none !important;
}

.logo-side {
 margin-bottom: 2.5rem;
 text-align: center;
}

.logo-side img {
 width: 100px;
 height: 100px;
 transition: transform 0.3s ease;
 animation: pulse 3s infinite ease-in-out;
}

.welcome-text {
 text-align: center;
 margin-bottom: 2.5rem;
}

.welcome-text h2 {
 font-family: "Syne", sans-serif;
 font-weight: 700;
 font-size: 2.5rem;
 color: var(--text-primary);
 margin: 0 0 0.5rem 0;
 letter-spacing: -0.025em;
 background: none;
 -webkit-background-clip: initial;
 -webkit-text-fill-color: initial;
 position: relative;
 color: black;
}

.welcome-text h2::after {
 display: none;
}

.welcome-text p {
 font-size: 0.975rem;
 color: var(--text-secondary);
 margin: 0;
 font-family: "Syne", sans-serif;
}

.form-group {
 margin-bottom: 1.25rem;
 position: relative;
 width: 100%;
}

.form-group label {
 display: block;
 font-size: 0.875rem;
 font-weight: 500;
 color: var(--text-primary);
 margin-bottom: 0.5rem;
}

.form-control {
 width: 100%;
 padding: 0.75rem 1rem;
 font-size: 0.95rem;
 border: 1.5px solid var(--input-border);
 border-radius: 0.5rem;
 transition: all 0.3s ease;
 background: rgba(255, 255, 255, 0.9);
 backdrop-filter: blur(4px);
 color: var(--text-primary);
 font-family: "Syne", sans-serif;
 box-sizing: border-box;
}

.form-control:focus {
 border-color: var(--primary-color);
 box-shadow: 0 0 0 4px rgba(77, 93, 255, 0.1), 0 0 15px rgba(77, 93, 255, 0.2);
 outline: none;
}

.password-group {
 position: relative;
}

.toggle-password {
 position: absolute;
 right: 0.1rem;
 top: 70%;
 transform: translateY(-50%);
 color: var(--text-secondary);
 cursor: pointer;
 padding: 0.25rem;
 transition: color 0.2s ease;
}

.toggle-password:hover {
 color: var(--text-primary);
}

.login-button {
 width: 100%;
 padding: 0.875rem;
 background-color: var(--primary-color);
 color: white;
 border: none;
 border-radius: 0.5rem;
 font-size: 0.95rem;
 font-weight: 500;
 cursor: pointer;
 transition: all 0.3s ease;
 font-family: "Syne", sans-serif;
 margin-top: 1.5rem;
 position: relative;
 overflow: hidden;
}

.login-button::before {
 content: "";
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 background: linear-gradient(
   90deg,
   transparent 0%,
   rgba(255, 255, 255, 0.1) 50%,
   transparent 100%
 );
 transform: translateX(-100%);
 transition: transform 0.5s ease;
}

.login-button:hover {
 transform: translateY(-2px);
 box-shadow: 0 0 20px var(--primary-glow);
}

.login-button:hover::before {
 transform: translateX(100%);
}

.form-footer {
 margin-top: 1.5rem;
 display: flex;
 justify-content: space-between;
 align-items: center;
 font-size: 0.875rem;
 flex-wrap: wrap;
 gap: 1rem;
}

.signup-prompt {
 color: var(--text-secondary);
}

.signup-link {
 color: var(--primary-color);
 text-decoration: none;
 font-weight: 500;
 margin-left: 0.25rem;
 transition: color 0.2s ease;
}

.signup-link:hover {
 color: var(--primary-hover);
 text-decoration: underline;
}

.forgot-password {
 color: var(--primary-color);
 text-decoration: none;
 font-weight: 500;
 transition: color 0.2s ease;
}

.forgot-password:hover {
 color: var(--primary-hover);
 text-decoration: underline;
}

.custom-alert {
 position: relative;
 padding: 1rem 1.5rem 1rem 3rem;
 border-radius: 12px;
 margin-bottom: 2rem;
 font-family: "Syne", sans-serif;
 backdrop-filter: blur(10px);
 border: 1px solid;
 animation: slideInDown 0.5s ease forwards;
 display: flex;
 align-items: center;
 gap: 12px;
 overflow: hidden;
 width: 100%;
}

.custom-alert.error-message {
 background: rgba(254, 226, 226, 0.8);
 border-color: rgba(248, 113, 113, 0.5);
 color: #991b1b;
}

.custom-alert.success-message {
 background: rgba(209, 250, 229, 0.8);
 border-color: rgba(74, 222, 128, 0.5);
 color: #065f46;
}

.custom-alert::before {
 font-family: "Font Awesome 5 Free";
 font-weight: 900;
 font-size: 1.1rem;
 position: absolute;
 left: 1rem;
 top: 50%;
 transform: translateY(-50%);
}

.error-message::before {
 content: "\f071";
 color: #dc2626;
}

.success-message::before {
 content: "\f00c";
 color: #059669;
}

.custom-alert-message {
 position: relative;
 z-index: 1;
 font-size: 0.95rem;
 font-weight: 500;
 line-height: 1.5;
 flex: 1;
 margin-left: 0.5rem;
}

.custom-alert::after {
 content: "";
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 background: linear-gradient(
   90deg,
   transparent,
   rgba(255, 255, 255, 0.2),
   transparent
 );
 animation: shine 2s infinite;
}

@keyframes shine {
 0% {
   transform: translateX(-100%);
 }
 20%,
 100% {
   transform: translateX(100%);
 }
}

@keyframes slideInDown {
 from {
   opacity: 0;
   transform: translateY(-20px);
 }
 to {
   opacity: 1;
   transform: translateY(0);
 }
}

.custom-alert.hiding {
 animation: slideOutUp 0.3s ease forwards;
}

@keyframes slideOutUp {
 from {
   opacity: 1;
   transform: translateY(0);
 }
 to {
   opacity: 0;
   transform: translateY(-20px);
 }
}

@keyframes slideIn {
 from {
   transform: translateY(-20px);
   opacity: 0;
 }
 to {
   transform: translateY(0);
   opacity: 1;
 }
}

@keyframes float {
 0%,
 100% {
   transform: translateY(0) rotate(0deg);
 }
 50% {
   transform: translateY(-20px) rotate(10deg);
 }
}

@keyframes pulse {
 0%,
 100% {
   transform: scale(1);
 }
 50% {
   transform: scale(1.05);
 }
}

@media (max-width: 768px) {
 body {
   flex-direction: column;
 }

 .left-side {
   display: none;
 }

 .right-side {
   padding: 2rem 1rem;
   min-height: 100vh;
 }

 .container {
   padding: 0 1rem;
   width: 100%;
   max-width: 100%;
 }

 .form-footer {
   flex-direction: column;
   text-align: center;
   gap: 1rem;
 }

 .signup-prompt,
 .forgot-password {
   width: 100%;
   text-align: center;
 }

 .logo-side {
   margin-bottom: 2rem;
 }

 .logo-side img {
   width: 80px;
   height: 80px;
 }

 .welcome-text {
   margin-bottom: 2rem;
 }

 .welcome-text h2 {
   font-size: 1.5rem;
 }

 .welcome-text p {
   font-size: 0.9rem;
 }

 .form-group {
   margin-bottom: 1rem;
 }

 .login-button {
   margin-top: 1.25rem;
 }
}

@media (max-width: 380px) {
 .right-side {
   padding: 1.5rem 0.75rem;
 }

 .container {
   padding: 0 0.75rem;
 }

 .welcome-text h2 {
   font-size: 1.25rem;
 }

 .form-footer {
   font-size: 0.8rem;
 }
}

.geometric-shapes {
 position: absolute;
 width: 100%;
 height: 100%;
 overflow: hidden;
}

.shape {
 position: absolute;
 border: 2px solid rgba(77, 93, 255, 0.1);
 animation: float 15s infinite ease-in-out;
}

.shape-1 {
 width: 100px;
 height: 100px;
 border-radius: 15px;
 top: 20%;
 left: 20%;
 transform: rotate(45deg);
 animation-delay: 0s;
}

.shape-2 {
 width: 150px;
 height: 150px;
 border-radius: 50%;
 bottom: 20%;
 right: 20%;
 animation-delay: -2s;
}

.shape-3 {
 width: 80px;
 height: 80px;
 border: 2px solid rgba(255, 255, 255, 0.05);
 top: 50%;
 left: 50%;
 animation-delay: -4s;
}

.brand-message {
 position: relative;
 z-index: 1;
 text-align: center;
 color: black;
 padding: 2rem;
 background: rgba(255, 255, 255, 0.1);
 backdrop-filter: blur(10px);
 border-radius: 20px;
 border: 1px solid rgba(255, 255, 255, 0.2);
}

.brand-message h1 {
 font-size: 2.5rem;
 margin-bottom: 1rem;
 font-weight: 600;
 text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.brand-message p {
 font-size: 1.1rem;
 opacity: 0.9;
}

.shape-4 {
 width: 120px;
 height: 120px;
 border: 2px solid rgba(255, 255, 255, 0.08);
 clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
 top: 15%;
 right: 25%;
 animation-delay: -6s;
}

.shape-5 {
 width: 70px;
 height: 70px;
 border: 2px solid rgba(255, 255, 255, 0.15);
 clip-path: polygon(
   50% 0%,
   61% 35%,
   98% 35%,
   68% 57%,
   79% 91%,
   50% 70%,
   21% 91%,
   32% 57%,
   2% 35%,
   39% 35%
 );
 bottom: 15%;
 left: 15%;
 animation-delay: -8s;
}

.brand-message {
 position: relative;
 z-index: 1;
 text-align: center;
 color: black;
 padding: 3rem;
 background: rgba(255, 255, 255, 0.05);
 backdrop-filter: blur(10px);
 border-radius: 20px;
 border: 1px solid rgba(255, 255, 255, 0.1);
 box-shadow: 0 0 30px rgba(77, 93, 255, 0.3);
}

.brand-message h1 {
 font-size: 2.8rem;
 margin-bottom: 1rem;
 font-weight: 600;
 color: black;
 text-shadow: 0 0 7px #fff, 0 0 10px #fff, 0 0 21px #fff, 0 0 42px #4D5DFF,
   0 0 82px #4D5DFF, 0 0 92px #4D5DFF, 0 0 102px #4D5DFF, 0 0 151px #4D5DFF;
 animation: neon 1.5s ease-in-out infinite alternate;
}

@keyframes neon {
 from {
   text-shadow: 0 0 7px #fff, 0 0 10px #fff, 0 0 21px #fff, 0 0 42px #4D5DFF;
 }
 to {
   text-shadow: 0 0 7px #fff, 0 0 10px #fff, 0 0 21px #fff, 0 0 42px #4D5DFF,
     0 0 102px #4D5DFF, 0 0 151px #4D5DFF;
 }
}

.right-side {
 background-color: #ffffff;
 position: relative;
}

.right-side::before {
 content: none;
}

.form-control {
 background: rgba(255, 255, 255, 0.9);
 border: 1.5px solid rgba(77, 93, 255, 0.2);
 box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-control:focus {
 border-color: var(--primary-color);
 box-shadow: 0 0 0 4px rgba(77, 93, 255, 0.1), 0 0 15px rgba(77, 93, 255, 0.2),
   inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.login-button {
 clip-path: polygon(
   0 10px,
   10px 0,
   calc(100% - 10px) 0,
   100% 10px,
   100% calc(100% - 10px),
   calc(100% - 10px) 100%,
   10px 100%,
   0 calc(100% - 10px)
 );
}

.tech-decoration {
 position: relative;
 margin-top: 2rem;
 height: 2px;
 background: rgba(255, 255, 255, 0.1);
 overflow: hidden;
}

.tech-line {
 position: absolute;
 height: 100%;
 width: 50%;
 background: linear-gradient(90deg, transparent, #4D5DFF, transparent);
 animation: techLine 7s infinite;
}

@keyframes techLine {
 0% {
   left: -50%;
 }
 100% {
   left: 100%;
 }
}

.password-input-wrapper {
 position: relative;
 display: flex;
 align-items: center;
 width: 100%;
}

.password-input-wrapper input {
 width: 100%;
}

.password-toggle {
 position: absolute;
 right: 12px;
 top: 50%;
 transform: translateY(-50%);
 background: none;
 border: none;
 padding: 0;
 cursor: pointer;
 display: flex;
 align-items: center;
 justify-content: center;
 color: #6b7280;
 transition: color 0.2s;
}

.password-toggle:hover {
 color: #374151;
}

.eye-icon {
 width: 20px;
 height: 20px;
 transition: opacity 0.2s;
}

.eye-hide {
 display: none;
}

/* When password is visible */
.password-toggle.showing .eye-show {
 display: none;
}

.password-toggle.showing .eye-hide {
 display: block;
}

/* Ensure the login form has consistent widths */
.login-form {
 width: 100%;
 max-width: 100%;
}

/* Loading Button Animation */
.btn-login {
 position: relative;
 transition: all 0.3s ease;
}

.btn-login.loading {
 color: transparent !important;
 pointer-events: none;
}

.btn-login .btn-spinner {
 display: none;
}

.btn-login.loading .btn-spinner {
 display: block;
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
}

.btn-spinner {
 width: 24px;
 height: 24px;
 border: 3px solid rgba(255, 255, 255, 0.3);
 border-radius: 50%;
 border-top-color: #ffffff;
 animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
 0% {
   transform: translate(-50%, -50%) rotate(0deg);
 }
 100% {
   transform: translate(-50%, -50%) rotate(360deg);
 }
}