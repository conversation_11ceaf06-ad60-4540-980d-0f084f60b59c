/**
 * MongoDB Utilities
 * Utilities for handling MongoDB connections and providing fallback behavior
 */

const mongoose = require('mongoose');

/**
 * Check if we're in Vercel environment
 * @returns {boolean} True if running in Vercel environment
 */
const isVercelEnv = () => {
  // If explicitly self-hosted, it's not a Vercel environment
  if (process.env.IS_SELF_HOSTED === 'true') {
    return false;
  }
  // Otherwise, check for Vercel environment variables
  return process.env.VERCEL || process.env.VERCEL_ENV || 
         (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL);
};

/**
 * Check if MongoDB is connected
 * @returns {boolean} True if MongoDB is connected
 */
const isMongoConnected = () => {
  return mongoose.connection.readyState === 1;
};

/**
 * Get MongoDB connection state as a string
 * @returns {string} Connection state as a string
 */
const getMongoConnectionState = () => {
  const stateMap = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
    99: 'uninitialized'
  };
  
  return stateMap[mongoose.connection.readyState] || 
         `unknown (${mongoose.connection.readyState})`;
};

/**
 * Safely execute MongoDB operation with fallback
 * @param {Function} dbOperation - Function that performs MongoDB operation
 * @param {*} fallbackValue - Value to return if MongoDB is not available
 * @param {boolean} throwError - Whether to throw errors (default: false)
 * @returns {Promise<*>} - Result of operation or fallback value
 */
const safeMongoOp = async (dbOperation, fallbackValue, throwError = false) => {
  try {
    // Skip operation if we're in Vercel and MongoDB isn't connected
    if (isVercelEnv() && !isMongoConnected()) {
      console.log('MongoDB not connected in Vercel environment. Using fallback value.');
      return fallbackValue;
    }
    
    // Try MongoDB operation
    return await dbOperation();
  } catch (error) {
    console.error('MongoDB operation failed:', error.message);
    
    if (throwError) {
      throw error;
    }
    
    return fallbackValue;
  }
};

module.exports = {
  isVercelEnv,
  isMongoConnected,
  getMongoConnectionState,
  safeMongoOp
}; 