/**
 * Timezone-Independent Period Calculations for PandaPayroll
 *
 * This utility provides pure business date arithmetic using YYYY-MM-DD string format
 * to eliminate timezone dependencies in payroll period calculations.
 *
 * Enhanced to use BusinessDate utility for complete timezone independence.
 */

const BusinessDate = require('./BusinessDate');

class PeriodCalculations {
  /**
   * Parse YYYY-MM-DD string to Date object for calculations only
   * @deprecated Use BusinessDate.parseDate instead
   * @param {string} dateStr - Date string in YYYY-MM-DD format
   * @returns {Date} Date object
   */
  static parseDate(dateStr) {
    return BusinessDate.parseDate(dateStr);
  }

  /**
   * Format a Date object to YYYY-MM-DD string
   * @deprecated Use BusinessDate.formatDate instead
   * @param {Date} date - Date object to format
   * @returns {string} Date in YYYY-MM-DD format
   */
  static formatDate(date) {
    return BusinessDate.formatDate(date);
  }

  /**
   * Validate YYYY-MM-DD date string format
   * @deprecated Use BusinessDate.isValid instead
   * @param {string} dateStr - Date string to validate
   * @returns {boolean} True if valid
   */
  static isValidDateString(dateStr) {
    return BusinessDate.isValid(dateStr);
  }

  /**
   * Add days to a date string
   * @deprecated Use BusinessDate.addDays instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} days - Number of days to add
   * @returns {string} New date in YYYY-MM-DD format
   */
  static addDays(dateStr, days) {
    return BusinessDate.addDays(dateStr, days);
  }

  /**
   * Add months to a date string
   * @deprecated Use BusinessDate.addMonths instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} months - Number of months to add
   * @returns {string} New date in YYYY-MM-DD format
   */
  static addMonths(dateStr, months) {
    return BusinessDate.addMonths(dateStr, months);
  }

  /**
   * Get the last day of the month for a given date
   * @deprecated Use BusinessDate.endOfMonth instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {string} Last day of month in YYYY-MM-DD format
   */
  static endOfMonth(dateStr) {
    return BusinessDate.endOfMonth(dateStr);
  }

  /**
   * Set a specific day of the month
   * @deprecated Use BusinessDate.setDayOfMonth instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} day - Day of month (1-31)
   * @returns {string} Date with specified day in YYYY-MM-DD format
   */
  static setDayOfMonth(dateStr, day) {
    return BusinessDate.setDayOfMonth(dateStr, day);
  }

  /**
   * Get the day of the month (1-31)
   * @deprecated Use BusinessDate.getDayOfMonth instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Day of month (1-31)
   */
  static getDayOfMonth(dateStr) {
    return BusinessDate.getDayOfMonth(dateStr);
  }

  /**
   * Get the day of week for a date (0 = Sunday, 6 = Saturday)
   * @deprecated Use BusinessDate.getDayOfWeek instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Day of week (0-6)
   */
  static getDayOfWeek(dateStr) {
    return BusinessDate.getDayOfWeek(dateStr);
  }

  /**
   * Convert various date inputs to YYYY-MM-DD string format
   * @deprecated Use BusinessDate.normalize instead
   * @param {string|Date} input - Date input to convert
   * @returns {string} Date in YYYY-MM-DD format
   */
  static normalize(input) {
    return BusinessDate.normalize(input);
  }

  /**
   * Calculate period end date based on pay frequency and DOA
   * @param {string} doaStr - Employee DOA in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Period end date in YYYY-MM-DD format
   */
  static calculatePeriodEndDate(doaStr, payFrequency) {
    const startDate = BusinessDate.normalize(doaStr);

    console.log('🗓️  PeriodCalculations.calculatePeriodEndDate:', {
      doa: doaStr,
      startDate,
      frequency: payFrequency.frequency,
      lastDayOfPeriod: payFrequency.lastDayOfPeriod
    });

    if (payFrequency.frequency === 'weekly') {
      return this.calculateWeeklyPeriodEnd(startDate, payFrequency.lastDayOfPeriod);
    } else if (payFrequency.frequency === 'monthly') {
      return this.calculateMonthlyPeriodEnd(startDate, payFrequency.lastDayOfPeriod);
    } else if (payFrequency.frequency === 'biweekly' || payFrequency.frequency === 'bi-weekly') {
      return BusinessDate.addDays(startDate, 13);
    } else {
      // Default to month end
      return BusinessDate.endOfMonth(startDate);
    }
  }

  /**
   * Calculate next period end date based on current period end date and pay frequency
   * @param {string} currentPeriodEndDate - Current period end date in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Next period end date in YYYY-MM-DD format
   */
  static calculateNextPeriodEndDate(currentPeriodEndDate, payFrequency) {
    const nextStartDate = BusinessDate.addDays(currentPeriodEndDate, 1);

    console.log('🗓️  PeriodCalculations.calculateNextPeriodEndDate:', {
      currentPeriodEndDate,
      nextStartDate,
      frequency: payFrequency.frequency,
      lastDayOfPeriod: payFrequency.lastDayOfPeriod
    });

    if (payFrequency.frequency === 'weekly') {
      return this.calculateWeeklyPeriodEnd(nextStartDate, payFrequency.lastDayOfPeriod);
    } else if (payFrequency.frequency === 'monthly') {
      return this.calculateMonthlyPeriodEnd(nextStartDate, payFrequency.lastDayOfPeriod);
    } else if (payFrequency.frequency === 'biweekly' || payFrequency.frequency === 'bi-weekly') {
      // Handle bi-weekly with custom dates
      if (payFrequency.lastDayOfPeriod !== 'monthend' && payFrequency.biWeeklyInterimDay) {
        return this.calculateBiWeeklyCustomPeriodEnd(currentPeriodEndDate, nextStartDate, payFrequency);
      }
      // Default bi-weekly: add 14 days
      return BusinessDate.addDays(nextStartDate, 13);
    } else {
      // Default to month end
      return BusinessDate.endOfMonth(nextStartDate);
    }
  }

  /**
   * Calculate weekly period end date
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} lastDayOfPeriod - Target day name (e.g., 'friday')
   * @returns {string} Period end date in YYYY-MM-DD format
   */
  static calculateWeeklyPeriodEnd(startDate, lastDayOfPeriod) {
    // Map day names to numbers (0 = Sunday, 6 = Saturday)
    const daysOfWeek = {
      sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
      thursday: 4, friday: 5, saturday: 6
    };

    const targetDay = daysOfWeek[lastDayOfPeriod.toLowerCase()];
    const currentDay = BusinessDate.getDayOfWeek(startDate);

    // Calculate days to add to reach the next target day
    let daysToAdd = (targetDay - currentDay + 7) % 7;

    // If we're already on the target day, use today as the end date
    if (daysToAdd === 0) {
      return startDate;
    } else {
      return BusinessDate.addDays(startDate, daysToAdd);
    }
  }

  /**
   * Calculate monthly period end date
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} lastDayOfPeriod - 'monthend' or day number (e.g., '25')
   * @returns {string} Period end date in YYYY-MM-DD format
   */
  static calculateMonthlyPeriodEnd(startDate, lastDayOfPeriod) {
    if (lastDayOfPeriod === 'monthend') {
      return BusinessDate.endOfMonth(startDate);
    } else {
      // Use the custom day of month
      const day = parseInt(lastDayOfPeriod);
      let endDate = BusinessDate.setDayOfMonth(startDate, day);

      // If the start date is after the target day, move to next month
      if (BusinessDate.getDayOfMonth(startDate) > day) {
        endDate = BusinessDate.addMonths(endDate, 1);
      }

      return endDate;
    }
  }

  /**
   * Calculate bi-weekly custom period end date with alternating pattern
   * @param {string} currentPeriodEndDate - Current period end date
   * @param {string} nextStartDate - Next period start date
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Next period end date in YYYY-MM-DD format
   */
  static calculateBiWeeklyCustomPeriodEnd(currentPeriodEndDate, nextStartDate, payFrequency) {
    const lastDay = parseInt(payFrequency.lastDayOfPeriod);
    const interimDay = parseInt(payFrequency.biWeeklyInterimDay);
    const currentPeriodEndDay = BusinessDate.getDayOfMonth(currentPeriodEndDate);

    // If current period ended on the last day (e.g., 7th)
    if (currentPeriodEndDay === lastDay) {
      // Next period should end on interim day (e.g., 1st)
      let endDate = BusinessDate.setDayOfMonth(nextStartDate, interimDay);
      if (BusinessDate.getDayOfMonth(nextStartDate) > interimDay) {
        endDate = BusinessDate.addMonths(endDate, 1);
      }
      return endDate;
    }
    // If current period ended on interim day (e.g., 1st)
    else if (currentPeriodEndDay === interimDay) {
      // Next period should end on last day (e.g., 7th)
      let endDate = BusinessDate.setDayOfMonth(nextStartDate, lastDay);
      if (BusinessDate.getDayOfMonth(nextStartDate) > lastDay) {
        endDate = BusinessDate.addMonths(endDate, 1);
      }
      return endDate;
    }

    // Default fallback
    return BusinessDate.addDays(nextStartDate, 13);
  }

  /**
   * Check if first date is after second date
   * @deprecated Use BusinessDate.isAfter instead
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is after date2
   */
  static isAfter(date1, date2) {
    return BusinessDate.isAfter(date1, date2);
  }

  /**
   * Check if first date is before second date
   * @deprecated Use BusinessDate.isBefore instead
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is before date2
   */
  static isBefore(date1, date2) {
    return BusinessDate.isBefore(date1, date2);
  }

  /**
   * Check if two dates are the same
   * @deprecated Use BusinessDate.isSame instead
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if dates are the same
   */
  static isSame(date1, date2) {
    return BusinessDate.isSame(date1, date2);
  }

  /**
   * Check if first date is same or before second date
   * @deprecated Use BusinessDate.isSameOrBefore instead
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is same or before date2
   */
  static isSameOrBefore(date1, date2) {
    return BusinessDate.isSameOrBefore(date1, date2);
  }

  /**
   * Check if first date is same or after second date
   * @deprecated Use BusinessDate.isSameOrAfter instead
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is same or after date2
   */
  static isSameOrAfter(date1, date2) {
    return BusinessDate.isSameOrAfter(date1, date2);
  }

  /**
   * Convert business date to Date object for database storage
   * @deprecated Use BusinessDate.toDate instead
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {boolean} endOfDay - If true, set to end of day (23:59:59.999Z)
   * @returns {Date} Date object for database storage
   */
  static toDate(dateStr, endOfDay = false) {
    return BusinessDate.toDate(dateStr, endOfDay);
  }

  /**
   * Convert Date object back to YYYY-MM-DD string using UTC to avoid timezone issues
   * @deprecated Use BusinessDate.fromDate instead
   * @param {Date} dateObj - Date object to convert
   * @returns {string} Date in YYYY-MM-DD format
   */
  static fromDate(dateObj) {
    return BusinessDate.fromDate(dateObj);
  }
}

module.exports = PeriodCalculations;
