/**
 * Logger utility for consistent logging across the application
 * Optimized for Vercel environment with minimal dependencies
 */
class Logger {
    constructor(context) {
        this.context = context || 'app';
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            http: 3,
            verbose: 4,
            debug: 5,
            silly: 6
        };
    }

    /**
     * Format a log message
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     * @returns {string} Formatted log message
     */
    _formatMessage(level, message, meta) {
        const timestamp = new Date().toISOString();
        let formatted = `[${timestamp}] [${level.toUpperCase()}] [${this.context}] ${message}`;
        
        if (meta) {
            try {
                if (meta instanceof Error) {
                    formatted += `\n  Stack: ${meta.stack || 'No stack trace'}`;
                } else if (typeof meta === 'object') {
                    formatted += `\n  ${JSON.stringify(meta, this._errorReplacer, 2)}`;
                } else {
                    formatted += ` ${meta}`;
                }
            } catch (err) {
                formatted += ` [Error serializing metadata: ${err.message}]`;
            }
        }
        
        return formatted;
    }
    
    /**
     * Handle Error objects in JSON.stringify
     */
    _errorReplacer(key, value) {
        if (value instanceof Error) {
            return {
                message: value.message,
                name: value.name,
                stack: value.stack,
                ...value
            };
        }
        return value;
    }

    /**
     * Should this message be logged at the current level?
     * @param {string} level - Level to check
     * @returns {boolean}
     */
    _shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    /**
     * Log a message at the specified level
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    log(level, message, meta) {
        if (!this._shouldLog(level)) return;
        
        const formatted = this._formatMessage(level, message, meta);
        
        // In self-hosted environment, use appropriate console methods directly
        switch (level) {
            case 'error':
                console.error(formatted);
                break;
            case 'warn':
                break;
            case 'info':
                break;
            case 'http':
            case 'verbose':
            case 'debug':
            case 'silly':
            default:
                break;
        }
    }

    /**
     * Log an error message
     * @param {string} message - Log message
     * @param {Error|Object} [meta] - Error object or additional metadata
     */
    error(message, meta) {
        this.log('error', message, meta);
    }

    /**
     * Log a warning message
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    warn(message, meta) {
        this.log('warn', message, meta);
    }

    /**
     * Log an info message
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    info(message, meta) {
        this.log('info', message, meta);
    }

    /**
     * Log an HTTP request message
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    http(message, meta) {
        this.log('http', message, meta);
    }

    /**
     * Log a verbose message
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    verbose(message, meta) {
        this.log('verbose', message, meta);
    }

    /**
     * Log a debug message
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    debug(message, meta) {
        this.log('debug', message, meta);
    }

    /**
     * Log a silly message (most detailed)
     * @param {string} message - Log message
     * @param {Object} [meta] - Additional metadata
     */
    silly(message, meta) {
        this.log('silly', message, meta);
    }
}

module.exports = Logger; 