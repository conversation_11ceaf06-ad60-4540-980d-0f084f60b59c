const moment = require('moment');

async function generateFNBBankFile(company, transactions, actionDate) {
    console.log('\n=== Generating FNB Bank File ===');

    // Validate inputs
    if (!company || !transactions || !actionDate) {
        console.error('Missing required parameters for bank file generation');
        throw new Error('Missing required parameters');
    }

    try {
        const bankFileContent = [];
        let totalAmount = 0;
        let recordCount = 0;

        // Helper functions for padding
        const padLeft = (str, length, char = '0') => {
            str = str.toString();
            return (char.repeat(length) + str).slice(-length);
        };
        const padRight = (str, length, char = ' ') => {
            str = (str || '').toString();
            return (str + char.repeat(length)).substring(0, length);
        };

        // Get formatted dates
        const today = moment();
        const formattedCreationDate = today.format('YYMMDD');
        const formattedActionDate = moment(actionDate).format('YYMMDD');

        // Record Type 02 - Header Record
        const headerRecord = [
            '02',                                   // Record Type (2)
            formattedCreationDate,                 // Creation Date (6)
            formattedActionDate,                   // Action Date (6)
            padLeft('1', 4),                       // Generation Number (4)
            padLeft('0', 180)                      // Filler (180)
        ].join('');
        console.log('Header Record (198):', headerRecord.length, '\n', headerRecord);
        bankFileContent.push(headerRecord);

        // Record Type 04 - User Header Record
        const userHeaderRecord = [
            '04',                                   // Record Type (2)
            padLeft('0', 196)                       // Filler (196)
        ].join('');
        console.log('User Header Record (198):', userHeaderRecord.length, '\n', userHeaderRecord);
        bankFileContent.push(userHeaderRecord);

        console.log('Processing transactions...');
        
        // Process transactions
        for (const transaction of transactions) {
            recordCount++;
            const amountInCents = Math.round(transaction.amount * 100);
            totalAmount += transaction.amount;

            // Record Type 10 - Transaction Record
            let transactionRecord = [
                '10',                                           // Record Type (2)
                padLeft(transaction.branchCode, 9),             // Branch Code (9)
                padLeft(transaction.accountNumber, 8),          // Account Number (8)
                padLeft('0', 9),                               // Zeros (9)
                padLeft(recordCount.toString(), 6),            // Sequence Number (6)
                padLeft(amountInCents.toString(), 11),         // Amount in cents (11)
                padLeft('0', 7),                               // Zeros (7)
                padLeft(transaction.branchCode, 6),            // Employee Branch Code (6)
                padLeft(transaction.accountNumber, 9),         // Employee Account Number (9)
                padLeft('0', 13),                              // Zeros (13)
                padRight(company.companyName, 40),             // Company Name (40)
                padRight(transaction.employeeName, 30),        // Employee Name (30)
                padRight('', 48)                               // Filler (48)
            ].join('');
            
            // Ensure exactly 198 characters
            transactionRecord = transactionRecord.padEnd(198, '0');
            
            console.log('Transaction Record (198):', transactionRecord.length, '\n', transactionRecord);
            bankFileContent.push(transactionRecord);
        }

        if (recordCount === 0) {
            console.error('No valid records found for bank file generation');
            throw new Error('No valid records to process');
        }

        // Record Type 92 - Contra Record
        const contraRecord = [
            '92',                                   // Record Type (2)
            padLeft(transactions[0].branchCode, 9), // Branch Code (9)
            padLeft(transactions[0].accountNumber, 8), // Account Number (8)
            padLeft('0', 9),                        // Zeros (9)
            padLeft((recordCount + 1).toString(), 6), // Sequence Number (6)
            padLeft((totalAmount * 100).toString(), 11), // Total Amount in cents (11)
            padLeft('0', 153)                       // Filler (153)
        ].join('');
        console.log('Contra Record (198):', contraRecord.length, '\n', contraRecord);
        bankFileContent.push(contraRecord);

        // Record Type 94 - Trailer Record
        const trailerRecord = [
            '94',                                   // Record Type (2)
            padLeft((recordCount + 2).toString(), 6), // Number of Records (6)
            padLeft((totalAmount * 100).toString(), 12), // Total Amount in cents (12)
            padLeft('0', 178)                       // Filler (178)
        ].join('');
        console.log('Trailer Record (198):', trailerRecord.length, '\n', trailerRecord);
        bankFileContent.push(trailerRecord);

        console.log('Bank File Generated Successfully');
        console.log(`Total Records: ${recordCount}`);
        console.log(`Total Amount: R${totalAmount.toFixed(2)}`);

        return bankFileContent.join('\r\n');

    } catch (error) {
        console.error('Error generating FNB Bank File:', error);
        throw error;
    }
}

module.exports = {
    generateFNBBankFile
};
