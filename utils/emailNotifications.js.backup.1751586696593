const nodemailer = require("nodemailer");
const moment = require("moment");

// Create transporter using environment variables
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

/**
 * Send email notification for a new leave request
 * @param {Object} leaveRequest - The leave request document
 */
async function sendLeaveRequestNotification(leaveRequest) {
  try {
    // Populate necessary fields
    await leaveRequest
      .populate("employee")
      .populate("leaveType")
      .populate("company")
      .execPopulate();

    const { employee, leaveType, company } = leaveRequest;
    const startDate = moment(leaveRequest.startDate).format("DD MMM YYYY");
    const endDate = moment(leaveRequest.endDate).format("DD MMM YYYY");

    // Get manager's email (assuming it's stored in company.managerEmail)
    const managerEmail =
      company.managerEmail || process.env.DEFAULT_MANAGER_EMAIL;

    if (!managerEmail) {
      throw new Error("No manager email configured for notifications");
    }

    // Email to manager
    await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: managerEmail,
      subject: `New Leave Request from ${employee.firstName} ${employee.lastName}`,
      html: `
        <h2>New Leave Request</h2>
        <p>A new leave request has been submitted with the following details:</p>
        <ul>
          <li><strong>Employee:</strong> ${employee.firstName} ${employee.lastName}</li>
          <li><strong>Leave Type:</strong> ${leaveType.name}</li>
          <li><strong>Start Date:</strong> ${startDate}</li>
          <li><strong>End Date:</strong> ${endDate}</li>
          <li><strong>Number of Days:</strong> ${leaveRequest.numberOfDays}</li>
          <li><strong>Reason:</strong> ${leaveRequest.reason}</li>
        </ul>
        <p>Please log in to the system to approve or reject this request.</p>
      `,
    });

    // Email to employee
    await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: employee.email,
      subject: "Leave Request Submitted",
      html: `
        <h2>Leave Request Submitted</h2>
        <p>Your leave request has been submitted successfully with the following details:</p>
        <ul>
          <li><strong>Leave Type:</strong> ${leaveType.name}</li>
          <li><strong>Start Date:</strong> ${startDate}</li>
          <li><strong>End Date:</strong> ${endDate}</li>
          <li><strong>Number of Days:</strong> ${leaveRequest.numberOfDays}</li>
          <li><strong>Reason:</strong> ${leaveRequest.reason}</li>
        </ul>
        <p>You will be notified once your request has been reviewed.</p>
      `,
    });

    // Update notification status
    leaveRequest.notificationSent = true;
    await leaveRequest.save();
  } catch (error) {
    console.error("Error sending leave request notification:", error);
    throw error;
  }
}

/**
 * Send email notification for leave request status update
 * @param {Object} leaveRequest - The leave request document
 * @param {String} status - The new status ('approved' or 'rejected')
 * @param {String} rejectionReason - Reason for rejection (if applicable)
 */
async function sendLeaveStatusNotification(
  leaveRequest,
  status,
  rejectionReason = ""
) {
  try {
    // Populate necessary fields
    await leaveRequest.populate([
      { path: "employee", select: "firstName lastName email" },
      { path: "leaveType", select: "name" },
      { path: "approvedBy", select: "firstName lastName email" },
      { path: "company", select: "name" },
      { path: "updatedBy", select: "firstName lastName email" },
    ]);

    const { employee, leaveType, approvedBy, company, updatedBy } =
      leaveRequest;
    const startDate = moment(leaveRequest.startDate).format("DD MMM YYYY");
    const endDate = moment(leaveRequest.endDate).format("DD MMM YYYY");
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);

    let statusColor;
    switch (status) {
      case "approved":
        statusColor = "#28a745";
        break;
      case "rejected":
        statusColor = "#dc3545";
        break;
      case "cancelled":
        statusColor = "#ffc107";
        break;
      default:
        statusColor = "#6c757d";
    }

    // Determine who processed the request
    const processedBy = status === "cancelled" ? updatedBy : approvedBy;
    const actionText = status === "cancelled" ? "cancelled" : "processed";

    await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: employee.email,
      cc: processedBy?.email,
      subject: `Leave Request ${statusText} - ${company.name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <h2 style="color: ${statusColor}; margin-top: 0;">Leave Request ${statusText}</h2>
            <p style="color: #666;">Your leave request has been ${status} with the following details:</p>
          </div>
          
          <div style="background-color: #ffffff; padding: 20px; border-radius: 5px; border: 1px solid #dee2e6;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Employee:</strong></td>
                <td style="padding: 8px 0;">${employee.firstName} ${
        employee.lastName
      }</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Leave Type:</strong></td>
                <td style="padding: 8px 0;">${leaveType.name}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Duration:</strong></td>
                <td style="padding: 8px 0;">${
                  leaveRequest.numberOfDays
                } day(s)</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Start Date:</strong></td>
                <td style="padding: 8px 0;">${startDate}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>End Date:</strong></td>
                <td style="padding: 8px 0;">${endDate}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Reason:</strong></td>
                <td style="padding: 8px 0;">${leaveRequest.reason}</td>
              </tr>
              ${
                status === "rejected" || status === "cancelled"
                  ? `
                  <tr>
                    <td style="padding: 8px 0; color: #666;"><strong>${
                      status === "cancelled" ? "Cancellation" : "Rejection"
                    } Reason:</strong></td>
                    <td style="padding: 8px 0;">${
                      rejectionReason || "Not provided"
                    }</td>
                  </tr>
                  `
                  : ""
              }
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>${
                  status === "cancelled" ? "Cancelled" : "Processed"
                } by:</strong></td>
                <td style="padding: 8px 0;">${
                  processedBy
                    ? `${processedBy.firstName} ${processedBy.lastName}`
                    : "System"
                }</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>${
                  status === "cancelled" ? "Cancelled" : "Processed"
                } on:</strong></td>
                <td style="padding: 8px 0;">${moment().format(
                  "DD MMM YYYY, hh:mm A"
                )}</td>
              </tr>
            </table>
          </div>
          
          <div style="margin-top: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 5px; font-size: 12px; color: #666;">
            <p style="margin: 0;">This is an automated email from ${
              company.name
            }'s leave management system. Please do not reply to this email.</p>
          </div>
        </div>
      `,
    });
  } catch (error) {
    console.error("Error sending leave status notification:", error);
    throw error;
  }
}

module.exports = {
  sendLeaveRequestNotification,
  sendLeaveStatusNotification,
};
