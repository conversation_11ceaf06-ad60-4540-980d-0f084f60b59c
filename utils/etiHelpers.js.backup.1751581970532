const calculateAge = (birthDate, employmentDate) => {
  const birth = new Date(birthDate);
  const employment = new Date(employmentDate);
  let age = employment.getFullYear() - birth.getFullYear();

  // Adjust age if employment date hasn't reached birth month/day
  const m = employment.getMonth() - birth.getMonth();
  if (m < 0 || (m === 0 && employment.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

const calculateETIAmount = (employee, monthlyRemuneration) => {
  // Constants for ETI calculations
  const ETI_THRESHOLDS = {
    MIN_WAGE: 2000, // Minimum wage threshold
    PHASE_1_MAX: 4500, // Maximum for full ETI
    PHASE_2_MAX: 6500, // Maximum qualifying remuneration
  };

  // ETI amounts vary based on employment duration
  const ETI_MAX_AMOUNTS = {
    FIRST_12_MONTHS: 1750, // Maximum monthly value for first 12 months
    NEXT_12_MONTHS: 1250, // Maximum monthly value for next 12 months
  };

  try {
    // Check basic eligibility
    if (!isEligibleForETI(employee)) {
      return {
        amount: 0,
        reason: "Employee does not meet basic ETI eligibility criteria",
      };
    }

    // Get employment duration in months
    const employmentDuration = getEmploymentDuration(
      employee.employmentStartDate
    );

    // If beyond 24 months, no ETI applies
    if (employmentDuration > 24) {
      return {
        amount: 0,
        reason: "Employment duration exceeds 24 months",
      };
    }

    // Calculate ETI amount based on monthly remuneration brackets
    let etiAmount = 0;

    if (monthlyRemuneration < ETI_THRESHOLDS.MIN_WAGE) {
      // Below minimum wage - no ETI
      return {
        amount: 0,
        reason: "Remuneration below minimum wage threshold",
      };
    } else if (monthlyRemuneration <= ETI_THRESHOLDS.PHASE_1_MAX) {
      // Phase 1: R2000 to R4500 - maximum ETI applies
      etiAmount =
        employmentDuration <= 12
          ? ETI_MAX_AMOUNTS.FIRST_12_MONTHS
          : ETI_MAX_AMOUNTS.NEXT_12_MONTHS;
    } else if (monthlyRemuneration <= ETI_THRESHOLDS.PHASE_2_MAX) {
      // Phase 2: R4500 to R6500 - formula applies
      const base =
        employmentDuration <= 12
          ? ETI_MAX_AMOUNTS.FIRST_12_MONTHS
          : ETI_MAX_AMOUNTS.NEXT_12_MONTHS;

      etiAmount =
        base -
        (base * (monthlyRemuneration - ETI_THRESHOLDS.PHASE_1_MAX)) /
          (ETI_THRESHOLDS.PHASE_2_MAX - ETI_THRESHOLDS.PHASE_1_MAX);
    }

    return {
      amount: Math.round(etiAmount * 100) / 100, // Round to 2 decimal places
      monthsRemaining: 24 - employmentDuration,
      employmentDuration,
      phase: employmentDuration <= 12 ? 1 : 2,
      calculationBasis: {
        monthlyRemuneration,
        thresholdUsed:
          monthlyRemuneration <= ETI_THRESHOLDS.PHASE_1_MAX
            ? "PHASE_1"
            : "PHASE_2",
      },
    };
  } catch (error) {
    console.error("Error calculating ETI:", error);
    return {
      amount: 0,
      error: "Error calculating ETI amount",
      details: error.message,
    };
  }
};

const isEligibleForETI = (employee) => {
  // Get employee age at employment start
  const ageAtEmployment = calculateAge(
    employee.dob,
    employee.employmentStartDate
  );

  return (
    ageAtEmployment >= 18 &&
    ageAtEmployment <= 29 &&
    !employee.isDirector &&
    !employee.isContractor &&
    employee.employmentStartDate >= new Date("2013-10-01") // ETI started October 2013
  );
};

const getEmploymentDuration = (startDate) => {
  const start = new Date(startDate);
  const now = new Date();

  // Calculate months between dates
  return (
    (now.getFullYear() - start.getFullYear()) * 12 +
    (now.getMonth() - start.getMonth())
  );
};

// Add this method to Employee schema
const employeeSchema = {
  methods: {
    calculateMonthlyETI: function () {
      // Get total monthly remuneration
      const monthlyRemuneration = this.calculateTotalRemuneration();

      // Calculate ETI
      const etiCalculation = calculateETIAmount(this, monthlyRemuneration);

      // Update ETI calculation details in employee record
      this.etiCalculation = {
        monthlyAmount: etiCalculation.amount,
        yearToDate:
          (this.etiCalculation?.yearToDate || 0) + etiCalculation.amount,
        totalMonthsClaimed: this.etiCalculation?.totalMonthsClaimed || 0,
        currentClaimPeriod: {
          startDate: this.employmentStartDate,
          endDate: new Date(
            this.employmentStartDate.getTime() + 24 * 30 * 24 * 60 * 60 * 1000
          ), // 24 months from start
        },
      };

      return etiCalculation;
    },
  },
};

module.exports = {
  calculateAge,
  calculateETIAmount,
  isEligibleForETI,
  getEmploymentDuration,
};
