const { PDFDocument } = require("pdf-lib");
const fs = require("fs/promises");
const path = require("path");

async function analyzeIRP5Template() {
  try {
    // Load the template
    const templatePath = path.join(
      __dirname,
      "..",
      "templates",
      "IRP5_template.pdf"
    );
    const templateBytes = await fs.readFile(templatePath);
    const pdfDoc = await PDFDocument.load(templateBytes);

    // Get form fields
    const form = pdfDoc.getForm();
    const fields = form.getFields();

    console.log("\n=== IRP5 Template Analysis ===");
    console.log(`Total form fields found: ${fields.length}`);
    console.log("\nField Details:");

    fields.forEach((field, index) => {
      console.log(`\n${index + 1}. Field Name: ${field.getName()}`);
      console.log(`   Type: ${field.constructor.name}`);

      // Get field type-specific properties
      if (field.constructor.name === "PDFTextField") {
        console.log(`   Max Length: ${field.getMaxLength()}`);
        console.log(`   Default Value: ${field.getValue()}`);
      } else if (field.constructor.name === "PDFCheckBox") {
        console.log(`   Checked: ${field.isChecked()}`);
      }
    });

    // Group fields by sections
    const fieldsBySection = {
      employeeInfo: fields.filter((f) =>
        f.getName().toLowerCase().includes("employee")
      ),
      companyInfo: fields.filter((f) =>
        f.getName().toLowerCase().includes("employer")
      ),
      taxInfo: fields.filter((f) => f.getName().toLowerCase().includes("tax")),
      incomeInfo: fields.filter((f) =>
        f.getName().toLowerCase().includes("income")
      ),
      deductionInfo: fields.filter((f) =>
        f.getName().toLowerCase().includes("deduction")
      ),
    };

    console.log("\n=== Fields By Section ===");
    Object.entries(fieldsBySection).forEach(([section, fields]) => {
      console.log(`\n${section}: ${fields.length} fields`);
      fields.forEach((field) => console.log(`   - ${field.getName()}`));
    });
  } catch (error) {
    console.error("Error analyzing IRP5 template:", error);
  }
}

// Run the analysis
analyzeIRP5Template();
