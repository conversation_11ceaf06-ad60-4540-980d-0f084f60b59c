/**
 * SouthAfricaPayrollEngine - Pure business logic for South African payroll period calculations
 * 
 * This engine implements timezone-independent payroll period calculation rules
 * specifically for South African business requirements. All calculations are based
 * on employee DOA and pay frequency settings, completely independent of server
 * timezone or company-wide settings.
 */

const BusinessDate = require('./BusinessDate');

class SouthAfricaPayrollEngine {
  /**
   * Calculate the first payroll period for an employee based on their DOA and pay frequency
   * @param {string} employeeDOA - Employee Date of Appointment in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {object} First period with startDate and endDate
   */
  static calculateEmployeeFirstPeriod(employeeDOA, payFrequency) {
    BusinessDate.debug('calculateEmployeeFirstPeriod', {
      employeeDOA,
      payFrequency
    });

    if (!BusinessDate.isValid(employeeDOA)) {
      throw new Error(`Invalid employee DOA: ${employeeDOA}`);
    }

    const frequency = payFrequency.frequency.toLowerCase();
    const lastDayOfPeriod = payFrequency.lastDayOfPeriod;

    switch (frequency) {
      case 'monthly':
        return this._calculateMonthlyFirstPeriod(employeeDOA, lastDayOfPeriod);
      
      case 'weekly':
        return this._calculateWeeklyFirstPeriod(employeeDOA, lastDayOfPeriod);
      
      case 'bi-weekly':
      case 'biweekly':
        return this._calculateBiWeeklyFirstPeriod(employeeDOA, lastDayOfPeriod);
      
      default:
        throw new Error(`Unsupported pay frequency: ${frequency}`);
    }
  }

  /**
   * Calculate monthly first period
   * @private
   */
  static _calculateMonthlyFirstPeriod(employeeDOA, lastDayOfPeriod) {
    if (lastDayOfPeriod === 'monthend') {
      // Period runs from DOA to end of DOA month
      const endDate = BusinessDate.endOfMonth(employeeDOA);
      
      const result = {
        startDate: employeeDOA,
        endDate: endDate,
        duration: BusinessDate.daysBetween(employeeDOA, endDate),
        isPartial: !BusinessDate.isSame(employeeDOA, BusinessDate.startOfMonth(employeeDOA))
      };

      BusinessDate.debug('monthlyFirstPeriod_monthend', result);
      return result;
    } else {
      // Period runs from DOA to specific day of month
      const targetDay = parseInt(lastDayOfPeriod);
      const [year, month, day] = employeeDOA.split('-').map(Number);
      
      let endDate;
      if (day <= targetDay) {
        // Target day is in same month as DOA
        endDate = `${year}-${String(month).padStart(2, '0')}-${String(targetDay).padStart(2, '0')}`;
      } else {
        // Target day is in next month
        const nextMonth = month === 12 ? 1 : month + 1;
        const nextYear = month === 12 ? year + 1 : year;
        endDate = `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(targetDay).padStart(2, '0')}`;
      }

      const result = {
        startDate: employeeDOA,
        endDate: endDate,
        duration: BusinessDate.daysBetween(employeeDOA, endDate),
        isPartial: true
      };

      BusinessDate.debug('monthlyFirstPeriod_specificDay', {
        ...result,
        targetDay,
        doaDay: day
      });
      return result;
    }
  }

  /**
   * Calculate weekly first period
   * @private
   */
  static _calculateWeeklyFirstPeriod(employeeDOA, lastDayOfPeriod) {
    const dayMap = {
      'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
      'thursday': 4, 'friday': 5, 'saturday': 6
    };

    const targetDay = dayMap[lastDayOfPeriod.toLowerCase()];
    if (targetDay === undefined) {
      throw new Error(`Invalid weekly last day: ${lastDayOfPeriod}`);
    }

    const endDate = BusinessDate.getNextDayOfWeek(employeeDOA, targetDay);
    
    const result = {
      startDate: employeeDOA,
      endDate: endDate,
      duration: BusinessDate.daysBetween(employeeDOA, endDate),
      isPartial: true
    };

    BusinessDate.debug('weeklyFirstPeriod', {
      ...result,
      targetDay: lastDayOfPeriod,
      targetDayNumber: targetDay
    });
    return result;
  }

  /**
   * Calculate bi-weekly first period
   * @private
   */
  static _calculateBiWeeklyFirstPeriod(employeeDOA, lastDayOfPeriod) {
    // For bi-weekly, first period is 14 days from DOA
    const endDate = BusinessDate.addDays(employeeDOA, 13); // 14 days total (inclusive)
    
    const result = {
      startDate: employeeDOA,
      endDate: endDate,
      duration: BusinessDate.daysBetween(employeeDOA, endDate),
      isPartial: true
    };

    BusinessDate.debug('biWeeklyFirstPeriod', result);
    return result;
  }

  /**
   * Calculate the next period end date based on current period end and pay frequency
   * @param {string} currentPeriodEnd - Current period end date in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Next period end date in YYYY-MM-DD format
   */
  static calculateNextPeriodEnd(currentPeriodEnd, payFrequency) {
    const frequency = payFrequency.frequency.toLowerCase();
    const lastDayOfPeriod = payFrequency.lastDayOfPeriod;

    switch (frequency) {
      case 'monthly':
        if (lastDayOfPeriod === 'monthend') {
          // Next month end: add 1 day to current end, then go to end of that month
          const nextDayAfterEnd = BusinessDate.addDays(currentPeriodEnd, 1);
          return BusinessDate.endOfMonth(nextDayAfterEnd);
        } else {
          // Next occurrence of specific day
          const targetDay = parseInt(lastDayOfPeriod);
          const nextDayAfterEnd = BusinessDate.addDays(currentPeriodEnd, 1);
          const [year, month] = nextDayAfterEnd.split('-').map(Number);
          return `${year}-${String(month).padStart(2, '0')}-${String(targetDay).padStart(2, '0')}`;
        }

      case 'weekly':
        return BusinessDate.addDays(currentPeriodEnd, 7);

      case 'bi-weekly':
      case 'biweekly':
        return BusinessDate.addDays(currentPeriodEnd, 14);

      default:
        throw new Error(`Unsupported pay frequency: ${frequency}`);
    }
  }

  /**
   * Generate all payroll periods for an employee up to a specified end date
   * @param {object} employee - Employee object with doa and payFrequency
   * @param {string} maxEndDate - Maximum end date for period generation (optional)
   * @returns {array} Array of period objects
   */
  static generateAllPeriods(employee, maxEndDate = null) {
    BusinessDate.debug('generateAllPeriods', {
      employeeId: employee._id,
      doa: employee.doa,
      payFrequency: employee.payFrequency,
      maxEndDate
    });

    // Normalize employee DOA to string format
    const employeeDOA = BusinessDate.normalize(employee.doa);
    
    // Set default max end date to 2 years from DOA if not provided
    const defaultMaxEnd = BusinessDate.addDays(employeeDOA, 730); // 2 years
    const actualMaxEnd = maxEndDate || defaultMaxEnd;

    const periods = [];
    
    // Calculate first period
    const firstPeriod = this.calculateEmployeeFirstPeriod(employeeDOA, employee.payFrequency);
    periods.push(firstPeriod);

    // Generate subsequent periods
    let currentEnd = firstPeriod.endDate;
    let iterationCount = 0;
    const maxIterations = 50; // Safety limit

    while (BusinessDate.isBefore(currentEnd, actualMaxEnd) && iterationCount < maxIterations) {
      const nextStart = BusinessDate.addDays(currentEnd, 1);
      const nextEnd = this.calculateNextPeriodEnd(currentEnd, employee.payFrequency);
      
      // Validate period
      if (!BusinessDate.isAfter(nextEnd, nextStart)) {
        console.error('❌ Invalid period generated:', { nextStart, nextEnd });
        break;
      }

      periods.push({
        startDate: nextStart,
        endDate: nextEnd,
        duration: BusinessDate.daysBetween(nextStart, nextEnd),
        isPartial: false
      });

      currentEnd = nextEnd;
      iterationCount++;
    }

    BusinessDate.debug('generateAllPeriods_result', {
      totalPeriods: periods.length,
      firstPeriod: periods[0],
      lastPeriod: periods[periods.length - 1],
      iterationCount
    });

    return periods;
  }

  /**
   * Validate that a period follows South African business rules
   * @param {object} period - Period object with startDate and endDate
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {object} Validation result with isValid and errors
   */
  static validatePeriod(period, payFrequency) {
    const errors = [];
    
    // Check date format
    if (!BusinessDate.isValid(period.startDate)) {
      errors.push(`Invalid start date format: ${period.startDate}`);
    }
    if (!BusinessDate.isValid(period.endDate)) {
      errors.push(`Invalid end date format: ${period.endDate}`);
    }

    // Check date order
    if (!BusinessDate.isBefore(period.startDate, period.endDate)) {
      errors.push(`End date must be after start date`);
    }

    // Check duration based on frequency
    const duration = BusinessDate.daysBetween(period.startDate, period.endDate);
    const frequency = payFrequency.frequency.toLowerCase();

    switch (frequency) {
      case 'monthly':
        if (duration < 28 || duration > 31) {
          errors.push(`Monthly period duration ${duration} days is outside valid range (28-31 days)`);
        }
        break;
      case 'weekly':
        if (duration < 1 || duration > 7) {
          errors.push(`Weekly period duration ${duration} days is outside valid range (1-7 days)`);
        }
        break;
      case 'bi-weekly':
      case 'biweekly':
        if (duration < 1 || duration > 14) {
          errors.push(`Bi-weekly period duration ${duration} days is outside valid range (1-14 days)`);
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      duration: duration
    };
  }

  /**
   * Convert legacy Date-based period to string-based period
   * @param {object} legacyPeriod - Period with Date objects
   * @returns {object} Period with string dates
   */
  static convertLegacyPeriod(legacyPeriod) {
    return {
      ...legacyPeriod,
      startDate: BusinessDate.normalize(legacyPeriod.startDate),
      endDate: BusinessDate.normalize(legacyPeriod.endDate)
    };
  }
}

module.exports = SouthAfricaPayrollEngine;
