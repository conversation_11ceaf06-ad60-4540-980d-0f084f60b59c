const mongoose = require('mongoose');
const moment = require('moment');
const Payroll = require('../models/Payroll');
const PayrollPeriod = require('../models/PayrollPeriod');

/**
 * Utility to link existing Payroll records to their corresponding PayrollPeriod records
 * This fixes the data relationship issue where many Payroll records don't have payrollPeriod references
 */
class PayrollDataMigration {
  
  /**
   * Links Payroll records to PayrollPeriod records based on date matching
   * @param {string} companyId - Optional: limit to specific company
   * @param {boolean} dryRun - If true, only shows what would be updated without making changes
   * @returns {Promise<object>} - Migration results
   */
  static async linkPayrollToPayrollPeriods(companyId = null, dryRun = true) {
    console.log('\n=== Payroll Data Migration: Linking Payroll to PayrollPeriod ===');
    console.log('Mode:', dryRun ? 'DRY RUN (no changes)' : 'LIVE UPDATE');
    
    const results = {
      totalPayrollRecords: 0,
      unlinkedPayrollRecords: 0,
      successfulLinks: 0,
      failedLinks: 0,
      errors: [],
      linkedRecords: []
    };

    try {
      // Build query for payroll records without payrollPeriod references
      const payrollQuery = {
        $or: [
          { payrollPeriod: { $exists: false } },
          { payrollPeriod: null }
        ]
      };
      
      if (companyId) {
        payrollQuery.company = companyId;
      }

      console.log('🔍 Searching for unlinked payroll records...');
      const unlinkedPayrolls = await Payroll.find(payrollQuery).populate('employee company');
      
      results.totalPayrollRecords = await Payroll.countDocuments(companyId ? { company: companyId } : {});
      results.unlinkedPayrollRecords = unlinkedPayrolls.length;
      
      console.log(`📊 Found ${results.unlinkedPayrollRecords} unlinked payroll records out of ${results.totalPayrollRecords} total`);

      if (unlinkedPayrolls.length === 0) {
        console.log('✅ No unlinked payroll records found. Migration not needed.');
        return results;
      }

      // Process each unlinked payroll record
      for (const payroll of unlinkedPayrolls) {
        try {
          console.log(`\n🔗 Processing payroll ${payroll._id} for employee ${payroll.employee?.firstName} ${payroll.employee?.lastName}`);
          console.log(`📅 Payroll month: ${moment(payroll.month).format('YYYY-MM-DD')}`);

          // Find matching payroll periods for this employee
          const periodQuery = {
            employee: payroll.employee._id,
            company: payroll.company._id
          };

          const payrollPeriods = await PayrollPeriod.find(periodQuery);
          console.log(`📋 Found ${payrollPeriods.length} payroll periods for this employee`);

          // Find the best matching period based on date overlap
          let bestMatch = null;
          let bestScore = 0;

          for (const period of payrollPeriods) {
            const payrollMonth = moment(payroll.month);
            const periodStart = moment(period.startDate);
            const periodEnd = moment(period.endDate);

            console.log(`🔍 Checking period ${period._id}: ${periodStart.format('YYYY-MM-DD')} to ${periodEnd.format('YYYY-MM-DD')}`);

            // Calculate match score based on date proximity
            let score = 0;

            // Perfect match: payroll month falls within period
            if (payrollMonth.isBetween(periodStart, periodEnd, 'day', '[]')) {
              score = 100;
              console.log('✅ Perfect match: payroll month within period');
            }
            // Good match: same month as period end
            else if (payrollMonth.isSame(periodEnd, 'month')) {
              score = 90;
              console.log('✅ Good match: same month as period end');
            }
            // Acceptable match: within 1 month of period end
            else if (Math.abs(payrollMonth.diff(periodEnd, 'months')) <= 1) {
              score = 70;
              console.log('⚠️ Acceptable match: within 1 month of period end');
            }
            // Weak match: within 3 months of period end
            else if (Math.abs(payrollMonth.diff(periodEnd, 'months')) <= 3) {
              score = 50;
              console.log('⚠️ Weak match: within 3 months of period end');
            }

            if (score > bestScore) {
              bestMatch = period;
              bestScore = score;
            }
          }

          if (bestMatch && bestScore >= 50) {
            console.log(`🎯 Best match found: Period ${bestMatch._id} (score: ${bestScore})`);
            
            if (!dryRun) {
              // Update the payroll record with the payrollPeriod reference
              await Payroll.updateOne(
                { _id: payroll._id },
                { $set: { payrollPeriod: bestMatch._id } }
              );
              console.log('✅ Payroll record updated with payrollPeriod reference');
            } else {
              console.log('🔍 DRY RUN: Would update payroll record with payrollPeriod reference');
            }

            results.successfulLinks++;
            results.linkedRecords.push({
              payrollId: payroll._id,
              periodId: bestMatch._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              payrollMonth: moment(payroll.month).format('YYYY-MM-DD'),
              periodRange: `${moment(bestMatch.startDate).format('YYYY-MM-DD')} to ${moment(bestMatch.endDate).format('YYYY-MM-DD')}`,
              matchScore: bestScore
            });
          } else {
            console.log('❌ No suitable matching period found');
            results.failedLinks++;
            results.errors.push({
              payrollId: payroll._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              payrollMonth: moment(payroll.month).format('YYYY-MM-DD'),
              reason: 'No matching payroll period found'
            });
          }

        } catch (error) {
          console.error(`❌ Error processing payroll ${payroll._id}:`, error.message);
          results.failedLinks++;
          results.errors.push({
            payrollId: payroll._id,
            employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
            reason: error.message
          });
        }
      }

      console.log('\n=== Migration Results ===');
      console.log(`📊 Total payroll records: ${results.totalPayrollRecords}`);
      console.log(`🔗 Unlinked records found: ${results.unlinkedPayrollRecords}`);
      console.log(`✅ Successfully linked: ${results.successfulLinks}`);
      console.log(`❌ Failed to link: ${results.failedLinks}`);
      
      if (results.errors.length > 0) {
        console.log('\n❌ Errors:');
        results.errors.forEach(error => {
          console.log(`  - ${error.employeeName} (${error.payrollId}): ${error.reason}`);
        });
      }

      if (dryRun && results.successfulLinks > 0) {
        console.log('\n🔍 To apply these changes, run with dryRun=false');
      }

      return results;

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Validates the integrity of payroll-period relationships
   * @param {string} companyId - Optional: limit to specific company
   * @returns {Promise<object>} - Validation results
   */
  static async validatePayrollPeriodLinks(companyId = null) {
    console.log('\n=== Validating Payroll-Period Relationships ===');
    
    const results = {
      totalPayrollRecords: 0,
      linkedRecords: 0,
      unlinkedRecords: 0,
      invalidLinks: 0,
      validationErrors: []
    };

    try {
      const query = companyId ? { company: companyId } : {};
      const payrollRecords = await Payroll.find(query).populate('payrollPeriod employee');
      
      results.totalPayrollRecords = payrollRecords.length;

      for (const payroll of payrollRecords) {
        if (payroll.payrollPeriod) {
          results.linkedRecords++;
          
          // Validate the link
          const payrollMonth = moment(payroll.month);
          const periodStart = moment(payroll.payrollPeriod.startDate);
          const periodEnd = moment(payroll.payrollPeriod.endDate);
          
          if (!payrollMonth.isBetween(periodStart, periodEnd, 'day', '[]') && 
              !payrollMonth.isSame(periodEnd, 'month')) {
            results.invalidLinks++;
            results.validationErrors.push({
              payrollId: payroll._id,
              periodId: payroll.payrollPeriod._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              issue: 'Payroll month does not align with period dates'
            });
          }
        } else {
          results.unlinkedRecords++;
        }
      }

      console.log('📊 Validation Results:');
      console.log(`  Total records: ${results.totalPayrollRecords}`);
      console.log(`  Linked: ${results.linkedRecords}`);
      console.log(`  Unlinked: ${results.unlinkedRecords}`);
      console.log(`  Invalid links: ${results.invalidLinks}`);

      return results;

    } catch (error) {
      console.error('❌ Validation failed:', error);
      throw error;
    }
  }
}

module.exports = PayrollDataMigration;
