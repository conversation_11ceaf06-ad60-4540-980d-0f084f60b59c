module.exports = {
  formatDate: function (date) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  },

  formatCurrency: function (amount) {
    return new Intl.NumberFormat("en-ZA", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  },

  getStatusClass: function (status) {
    switch (status.toLowerCase()) {
      case "pending":
        return "status-pending";
      case "submitted":
        return "status-submitted";
      case "overdue":
        return "status-overdue";
      default:
        return "";
    }
  },

  getDaysRemaining: function (deadline) {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  },

  getDaysRemainingClass: function (deadline) {
    const daysRemaining = this.getDaysRemaining(deadline);
    if (daysRemaining <= 3) return "days-critical";
    if (daysRemaining <= 7) return "days-warning";
    return "days-normal";
  },
};
