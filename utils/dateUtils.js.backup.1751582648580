const mongoose = require("mongoose");
const moment = require("moment-timezone");

// Set the default timezone
const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Payroll model getter function
let PayrollModel;
const getPayrollModel = () => {
  if (!PayrollModel) {
    try {
      PayrollModel = mongoose.model("Payroll");
    } catch (error) {
      const Schema = mongoose.Schema;
      const payrollSchema = new Schema({
        company: {
          type: Schema.Types.ObjectId,
          ref: "Company",
          required: true,
        },
        employee: {
          type: Schema.Types.ObjectId,
          ref: "Employee",
          required: true,
        },
        month: {
          type: Date,
        },
        // Add other necessary fields here
      });

      payrollSchema.index(
        { employee: 1, company: 1, month: 1 },
        { unique: true }
      );

      PayrollModel = mongoose.model("Payroll", payrollSchema);
    }
  }
  return PayrollModel;
};

const formatDateForMongoDB = (date) => {
  try {
    // If date is already a Date object
    if (date instanceof Date) {
      // CRITICAL FIX: Use UTC-first approach to prevent double timezone conversion
      return moment.utc(date).tz("Africa/Johannesburg").endOf("day").toDate();
    }

    // If it's a string, try parsing it
    if (typeof date === "string") {
      // Try parsing as ISO date first
      let parsedDate = moment(date);

      // If not valid, try other formats
      if (!parsedDate.isValid()) {
        parsedDate = moment(date, [
          "YYYY-MM-DD",
          "DD-MM-YYYY",
          "MM/DD/YYYY",
          "DD/MM/YYYY",
        ]);
      }

      if (parsedDate.isValid()) {
        // CRITICAL FIX: Use UTC-first approach to prevent double timezone conversion
        return moment.utc(parsedDate.toDate()).tz("Africa/Johannesburg").endOf("day").toDate();
      }
    }

    // If all else fails, return current date
    console.log("Using current date as fallback");
    return moment().tz("Africa/Johannesburg").endOf("day").toDate();
  } catch (error) {
    console.error("Date formatting error:", error);
    // Return current date as fallback
    return moment().tz("Africa/Johannesburg").endOf("day").toDate();
  }
};

const getEndOfMonthDate = (date) => {
  // CRITICAL FIX: Use UTC-first approach to prevent double timezone conversion
  return moment
    .utc(date)
    .tz("Africa/Johannesburg")
    .endOf("month")
    .hour(23)
    .minute(59)
    .second(59)
    .millisecond(999)
    .toDate();
};

const getNextMonthEndDate = (date) => {
  // CRITICAL FIX: Use UTC-first approach to prevent double timezone conversion
  return moment
    .utc(date)
    .tz(DEFAULT_TIMEZONE)
    .add(1, "month")
    .endOf("month")
    .hour(23)
    .minute(59)
    .second(59)
    .millisecond(999)
    .toDate();
};

// Utility functions
function parseAndFormatDate(dateString) {
  if (!dateString) return null;

  // First, try to parse the date as is
  let parsedDate = moment.tz(dateString, "Africa/Johannesburg");

  // If it's not valid, try to parse it as YYYY/MM/DD
  if (!parsedDate.isValid()) {
    parsedDate = moment.tz(dateString, "YYYY/MM/DD", "Africa/Johannesburg");
  }

  // If it's still not valid, return null
  if (!parsedDate.isValid()) {
    console.warn(`Invalid date format: ${dateString}`);
    return null;
  }

  // Format the date as YYYY-MM-DD
  return parsedDate.format("YYYY-MM-DD");
}

const formatDateForDisplay = (date) => {
  return moment(date).format("MMMM YYYY");
};

const toStartOfMonth = (date) => {
  return moment.tz(date, DEFAULT_TIMEZONE).startOf("month").toDate();
};

const getLastDayOfMonth = (date) => {
  return moment.tz(date, DEFAULT_TIMEZONE).endOf("month").toDate();
};

const getCurrentOrNextLastDayOfMonth = () => {
  const today = moment.tz(DEFAULT_TIMEZONE);
  return today.endOf("month").toDate();
};

const getNextMonth = (date) => {
  return moment
    .tz(date, DEFAULT_TIMEZONE)
    .add(1, "month")
    .endOf("month")
    .startOf("day")
    .toDate();
};

const getLastDayOfPreviousMonth = (date) => {
  return moment
    .tz(date, DEFAULT_TIMEZONE)
    .subtract(1, "month")
    .endOf("month")
    .toDate();
};

// Function to check if a date is the last day of the month
const isLastDayOfMonth = (date) => {
  const momentDate = moment.tz(date, DEFAULT_TIMEZONE);
  return momentDate.date() === momentDate.daysInMonth();
};

// Update createOrUpdatePayrollEntry to use the new date formatting
const createOrUpdatePayrollEntry = async (employeeId, month, payrollData) => {
  try {
    const Payroll = getPayrollModel();

    if (!payrollData.company) {
      throw new Error("Company field is required for payroll entry");
    }

    const formattedMonth = formatDateForMongoDB(getEndOfMonthDate(month));

    const existingPayroll = await Payroll.findOne({
      employee: employeeId,
      month: formattedMonth,
    });

    if (existingPayroll) {
      Object.assign(existingPayroll, payrollData);
      await existingPayroll.save();
      return existingPayroll;
    } else {
      const newPayroll = new Payroll({
        employee: employeeId,
        month: formattedMonth,
        ...payrollData,
      });
      await newPayroll.save();
      return newPayroll;
    }
  } catch (error) {
    console.warn("Error creating or updating payroll entry:", error);
    throw error;
  }
};

const getSubmissionDeadline = (submissionPeriod, submissionType) => {
  const periodEnd = getEndOfMonthDate(submissionPeriod);
  let deadline;

  if (submissionType === "EMP201") {
    // EMP201 is due on the 7th of the following month
    deadline = moment(periodEnd).add(1, "month").date(7);
  } else if (submissionType === "UIF") {
    // UIF is due on the 15th of the following month
    deadline = moment(periodEnd).add(1, "month").date(15);
  } else {
    throw new Error("Invalid submission type");
  }

  return deadline.endOf("day").toDate();
};

module.exports = {
  parseAndFormatDate,
  formatDateForDisplay,
  toStartOfMonth,
  getEndOfMonthDate,
  getLastDayOfMonth,
  getNextMonth,
  getLastDayOfPreviousMonth,
  createOrUpdatePayrollEntry,
  getCurrentOrNextLastDayOfMonth,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
  getSubmissionDeadline,
};
