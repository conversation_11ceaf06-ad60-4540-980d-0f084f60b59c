const { PDFDocument } = require('pdf-lib');
const fs = require('fs').promises;
const path = require('path');
const moment = require('moment');
const IRP5FieldHelpers = require('./irp5FieldHelpers');
const IRP5_FIELD_MAPPING = require('../config/irp5_field_mapping');

/**
 * Generates an IRP5 PDF document for an employee for a specific tax year
 * @param {object} employee - The employee document (populated with company and payroll data)
 * @param {string} taxYear - The tax year (e.g., "2025" for 2024/2025 tax year)
 * @param {object} payrollData - Aggregated payroll data for the tax year
 * @returns {Promise<Buffer>} - A promise that resolves with the PDF buffer
 */
async function generateIRP5PdfLib(employee, taxYear, payrollData) {
  try {

    // Load the IRP5 template
    const templatePath = path.join(__dirname, '..', 'templates', 'IRP5_template.pdf');
    
    let templateBytes;
    try {
      templateBytes = await fs.readFile(templatePath);
    } catch (error) {
      console.error('IRP5 template not found, creating basic PDF instead');
      return await generateBasicIRP5PDF(employee, taxYear, payrollData);
    }

    const pdfDoc = await PDFDocument.load(templateBytes);
    const form = pdfDoc.getForm();

    // Calculate tax year dates
    const taxYearStart = `${parseInt(taxYear) - 1}-03-01`;
    const taxYearEnd = `${taxYear}-02-28`;

    // Generate certificate number
    const certificateNumber = `IRP5-${employee.company.companyCode || 'COMP'}-${employee.employeeId || employee._id.toString().slice(-6)}-${taxYear}`;

    // Prepare employee data for form filling
    const employeeData = {
      certificateInfo: {
        certificateNumber: certificateNumber,
        taxYear: taxYear,
        yearStart: IRP5FieldHelpers.formatDate(taxYearStart),
        yearEnd: IRP5FieldHelpers.formatDate(taxYearEnd),
        etiBenefitAmount: IRP5FieldHelpers.formatCurrency(payrollData?.etiAmount || 0)
      },
      employeeInfo: {
        personal: {
          surname: employee.lastName || '',
          firstNames: employee.firstName || '',
          initials: employee.firstName ? employee.firstName.charAt(0).toUpperCase() : '',
          identityNumber: employee.personalDetails?.idNumber || employee.idNumber || '',
          passportNumber: employee.personalDetails?.passportNumber || employee.passportNumber || '',
          passportCountry: employee.personalDetails?.passportCountry || '',
          taxReference: employee.personalDetails?.taxNumber || employee.taxNumber || '',
          dateOfBirth: IRP5FieldHelpers.formatDate(employee.personalDetails?.dateOfBirth || employee.dateOfBirth),
          companyNumber: employee.employeeId || employee.companyEmployeeNumber || ''
        },
        contact: {
          mobileNumber: employee.personalDetails?.mobileNumber || employee.mobileNumber || '',
          emailAddress: employee.personalDetails?.email || employee.email || ''
        },
        address: {
          line1: employee.personalDetails?.address?.street || employee.address?.street || '',
          line2: employee.personalDetails?.address?.suburb || employee.address?.suburb || '',
          city: employee.personalDetails?.address?.city || employee.address?.city || '',
          postalCode: employee.personalDetails?.address?.postalCode || employee.address?.postalCode || '',
          country: employee.personalDetails?.address?.country || employee.address?.country || 'South Africa'
        }
      },
      employerInfo: {
        name: employee.company?.name || '',
        tradingName: employee.company?.employerDetails?.tradingName || employee.company?.name || '',
        payeReference: employee.company?.payeReference || '',
        registrationNumber: employee.company?.registrationNumber || '',
        address: {
          line1: employee.company?.employerDetails?.address?.street || '',
          line2: employee.company?.employerDetails?.address?.suburb || '',
          city: employee.company?.employerDetails?.address?.city || '',
          postalCode: employee.company?.employerDetails?.address?.postalCode || '',
          country: 'South Africa'
        }
      },
      income: {
        grossRemuneration: IRP5FieldHelpers.formatCurrency(payrollData?.grossPay || 0),
        taxableIncome: IRP5FieldHelpers.formatCurrency(payrollData?.taxableIncome || payrollData?.grossPay || 0),
        payeDeducted: IRP5FieldHelpers.formatCurrency(payrollData?.totalPAYE || 0),
        uifContributions: IRP5FieldHelpers.formatCurrency(payrollData?.totalUIF || 0),
        pensionFundContributions: IRP5FieldHelpers.formatCurrency(payrollData?.pensionContributions || 0),
        medicalAidContributions: IRP5FieldHelpers.formatCurrency(payrollData?.medicalAidContributions || 0),
        retirementAnnuityContributions: IRP5FieldHelpers.formatCurrency(payrollData?.retirementAnnuity || 0)
      }
    };

    // Fill the form fields using the mapping
    try {
      fillFormFields(form, IRP5_FIELD_MAPPING, employeeData);
    } catch (fieldError) {
    }

    // Set certificate type to IRP5
    try {
      const irp5Button = form.getCheckBox(IRP5_FIELD_MAPPING.certificateInfo.certificateType.irp5);
      irp5Button.check();
    } catch (error) {
    }

    // Set identity type based on employee data
    try {
      if (employee.personalDetails?.idType === 'rsa' || employee.idType === 'rsa') {
        const rsaIdButton = form.getCheckBox(IRP5_FIELD_MAPPING.employeeInfo.personal.identityType.rsaId);
        rsaIdButton.check();
      } else if (employee.personalDetails?.idType === 'passport' || employee.idType === 'passport') {
        const passportButton = form.getCheckBox(IRP5_FIELD_MAPPING.employeeInfo.personal.identityType.passport);
        passportButton.check();
      }
    } catch (error) {
    }

    // Generate the PDF
    const pdfBytes = await pdfDoc.save();

    return Buffer.from(pdfBytes);

  } catch (error) {
    console.error('❌ Error generating IRP5 PDF:', {
      error: error.message,
      stack: error.stack,
      employeeId: employee._id,
      taxYear
    });
    // Fallback to basic PDF generation
    return await generateBasicIRP5PDF(employee, taxYear, payrollData);
  }
}

/**
 * Recursively fills form fields based on the mapping structure
 */
function fillFormFields(form, mapping, data) {
  Object.entries(mapping).forEach(([key, value]) => {
    if (typeof value === 'string') {
      // This is a field name, try to fill it
      try {
        const field = form.getTextField(value);
        const fieldData = getNestedValue(data, key);
        if (fieldData !== undefined && fieldData !== null) {
          field.setText(String(fieldData));
        }
      } catch (error) {
        // Field not found or not a text field, skip
      }
    } else if (typeof value === 'object' && !Array.isArray(value)) {
      // This is a nested object, recurse
      const nestedData = data[key] || {};
      fillFormFields(form, value, nestedData);
    }
  });
}

/**
 * Gets a nested value from an object using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Generates a basic IRP5 PDF when template is not available
 */
async function generateBasicIRP5PDF(employee, taxYear, payrollData) {
  
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595, 842]); // A4 size
  const { width, height } = page.getSize();
  
  // Add content to the basic PDF
  page.drawText('IRP5 - EMPLOYEES TAX CERTIFICATE', {
    x: 50,
    y: height - 50,
    size: 16
  });
  
  page.drawText(`Tax Year: ${parseInt(taxYear) - 1}/${taxYear}`, {
    x: 50,
    y: height - 80,
    size: 12
  });
  
  page.drawText(`Employee: ${employee.firstName} ${employee.lastName}`, {
    x: 50,
    y: height - 110,
    size: 12
  });
  
  page.drawText(`Company: ${employee.company?.name || 'N/A'}`, {
    x: 50,
    y: height - 140,
    size: 12
  });
  
  page.drawText(`Gross Remuneration: R${(payrollData?.grossPay || 0).toFixed(2)}`, {
    x: 50,
    y: height - 170,
    size: 12
  });
  
  page.drawText(`PAYE Deducted: R${(payrollData?.totalPAYE || 0).toFixed(2)}`, {
    x: 50,
    y: height - 200,
    size: 12
  });
  
  page.drawText('This is a basic IRP5 certificate. Please contact HR for the official version.', {
    x: 50,
    y: height - 250,
    size: 10
  });
  
  const pdfBytes = await pdfDoc.save();
  return Buffer.from(pdfBytes);
}

module.exports = generateIRP5PdfLib;
