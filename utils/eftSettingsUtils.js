const EFTDetails = require('../models/eftDetails');
const EFTSettings = require('../models/EFTSettings');
const Company = require('../models/Company');

/**
 * Map EFT format from EFTDetails to bankFileFormat for EFTSettings
 * @param {string} eftFormat - The EFT format from EFTDetails
 * @returns {string} The corresponding bank file format
 */
function mapEftFormatToBankFileFormat(eftFormat) {
  if (!eftFormat) return 'fnb'; // Default to FNB

  const formatMapping = {
    // ABSA formats
    'ABSA Cash Focus': 'absa',
    'ABSA Business Integrator (.txt)': 'absa',
    'ABSA Business Integrator (.csv)': 'absa',
    'ABSA Business Integrator SAP (beta)': 'absa',
    'ABSA Business Integrator Online (.csv)': 'absa',

    // FNB formats
    'FNB Bankit': 'fnb',
    'FNB CAMS': 'fnb',
    'FNB Enterprise CSV': 'fnb',
    'FNB Online Banking (ACB)': 'fnb',
    'FNB PACS': 'fnb',

    // Standard Bank formats
    'Standard Bank CATS': 'standard',
    'Standard Bank Value Dated / EFTS': 'standard',
    'Standard Bank SSVS': 'standard',

    // Nedbank formats
    'NedBank Business Banking (CSV)': 'nedbank',
    'NedInform': 'nedbank',

    // Other banks default to FNB format
    'Albaraka': 'fnb',
    'Capitec ACB': 'fnb',
    'Investec CSV': 'fnb',
    'Netcash': 'fnb',
    'Sage Pay': 'fnb'
  };

  return formatMapping[eftFormat] || 'fnb';
}

/**
 * Get or create default EFT settings for a company
 * @param {ObjectId} companyId - The ID of the company
 * @param {Object} [defaultSettings] - Optional default settings to use
 * @returns {Promise<EFTSettings>} The EFT settings for the company
 */
async function getOrCreateEFTSettings(companyId, defaultSettings = {}) {

  // Validate companyId
  if (!companyId) {
    throw new Error('Company ID is required');
  }

  try {
    // First, check if the company exists
    const company = await Company.findById(companyId);
    if (!company) {
      throw new Error(`Company not found with ID: ${companyId}`);
    }

    // Try to find existing EFT details
    let eftDetails = await EFTDetails.findOne({ company: companyId });

    // If no details exist, create default details
    if (!eftDetails) {

      // Prepare default details
      const defaultEFTDetails = {
        company: companyId,
        eftFormat: defaultSettings.eftFormat || 'FNB Bankit',
        bank: defaultSettings.bankName || company.name || 'First National Bank',
        branchCode: defaultSettings.branchCode || '123456', // Placeholder
        accountNumber: defaultSettings.accountNumber || '**********', // Placeholder
        accountType: defaultSettings.accountType || 'Current',
        accountHolder: defaultSettings.accountHolder || company.name
      };

      // Create and save new EFT details
      eftDetails = new EFTDetails(defaultEFTDetails);
      await eftDetails.save();

    }

    // Create or find EFT settings using EFT details
    let eftSettings = await EFTSettings.findOne({ company: companyId });
    if (!eftSettings) {
      // Map the EFT format to bank file format
      const bankFileFormat = mapEftFormatToBankFileFormat(eftDetails.eftFormat);

      eftSettings = new EFTSettings({
        company: companyId,
        bankFileFormat: bankFileFormat,
        bankName: eftDetails.bank,
        branchCode: eftDetails.branchCode,
        accountNumber: eftDetails.accountNumber,
        accountHolder: eftDetails.accountHolder || company.name || 'Company Account'
      });
      await eftSettings.save();
    } else {
      // Update existing EFT settings with latest EFT details
      const bankFileFormat = mapEftFormatToBankFileFormat(eftDetails.eftFormat);

      eftSettings.bankFileFormat = bankFileFormat;
      eftSettings.bankName = eftDetails.bank;
      eftSettings.branchCode = eftDetails.branchCode;
      eftSettings.accountNumber = eftDetails.accountNumber;
      eftSettings.accountHolder = eftDetails.accountHolder || company.name || 'Company Account';
      eftSettings.updatedAt = new Date();

      await eftSettings.save();
    }

    return eftSettings;
  } catch (error) {
    console.error('Error in getOrCreateEFTSettings:', error);

    // Log additional context if the error is related to model registration
    if (error.name === 'OverwriteModelError') {
      console.error('Model registration error. Ensure EFTSettings model is imported only once.');
    }

    throw error;
  }
}

module.exports = {
  getOrCreateEFTSettings,
  mapEftFormatToBankFileFormat
};
