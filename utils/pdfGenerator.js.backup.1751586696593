const htmlPdf = require("html-pdf-node");

async function convertHtmlToPdf(html) {
  const options = { format: "A4" };
  const file = { content: html };
  return new Promise((resolve, reject) => {
    htmlPdf
      .generatePdf(file, options)
      .then((pdfBuffer) => {
        resolve(pdfBuffer);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

module.exports = convertHtmlToPdf;
