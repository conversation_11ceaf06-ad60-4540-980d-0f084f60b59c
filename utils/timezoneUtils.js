const moment = require("moment-timezone");

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

/**
 * Centralized timezone-safe date parsing utilities
 * These functions ensure consistent date handling across development and production
 */

/**
 * Parse a date string or Date object to UTC, avoiding timezone conversion issues
 * @param {string|Date} dateInput - The date to parse
 * @returns {moment.Moment} - UTC moment object
 */
function parseToUTC(dateInput) {
  if (!dateInput) return null;
  
  // If it's already a Date object from MongoDB, treat it as UTC
  if (dateInput instanceof Date) {
    return moment.utc(dateInput);
  }
  
  // If it's a string, parse it as UTC to avoid local timezone interpretation
  if (typeof dateInput === 'string') {
    // Handle YYYY-MM-DD format specifically
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      return moment.utc(dateInput + 'T00:00:00.000Z');
    }
    return moment.utc(dateInput);
  }
  
  return moment.utc(dateInput);
}

/**
 * Convert UTC moment to South African timezone for display
 * @param {moment.Moment} utcMoment - UTC moment object
 * @returns {moment.Moment} - SA timezone moment object
 */
function toSATimezone(utcMoment) {
  if (!utcMoment) return null;
  return utcMoment.tz(DEFAULT_TIMEZONE);
}

/**
 * Format date for database storage (always UTC)
 * @param {string|Date} dateInput - The date to format
 * @param {string} timeOfDay - 'start' or 'end' of day
 * @returns {Date} - UTC Date object for MongoDB
 */
function formatForDatabase(dateInput, timeOfDay = 'end') {
  const utcMoment = parseToUTC(dateInput);
  if (!utcMoment) return null;
  
  if (timeOfDay === 'start') {
    return utcMoment.startOf('day').toDate();
  } else {
    return utcMoment.endOf('day').toDate();
  }
}

/**
 * Format date for display in templates
 * @param {string|Date} dateInput - The date to format
 * @param {string} format - Moment.js format string
 * @returns {string} - Formatted date string in SA timezone
 */
function formatForDisplay(dateInput, format = 'YYYY-MM-DD') {
  const utcMoment = parseToUTC(dateInput);
  if (!utcMoment) return '';
  
  return toSATimezone(utcMoment).format(format);
}

/**
 * Create date range for MongoDB queries to avoid exact date matching issues
 * @param {string|Date} dateInput - The target date
 * @returns {Object} - Object with $gte and $lte for MongoDB query
 */
function createDateRange(dateInput) {
  const utcMoment = parseToUTC(dateInput);
  if (!utcMoment) return null;
  
  return {
    $gte: utcMoment.startOf('day').toDate(),
    $lte: utcMoment.endOf('day').toDate()
  };
}

/**
 * Compare two dates for equality (ignoring time)
 * @param {string|Date} date1 - First date
 * @param {string|Date} date2 - Second date
 * @returns {boolean} - True if dates are the same day
 */
function isSameDay(date1, date2) {
  const utc1 = parseToUTC(date1);
  const utc2 = parseToUTC(date2);
  
  if (!utc1 || !utc2) return false;
  
  return utc1.format('YYYY-MM-DD') === utc2.format('YYYY-MM-DD');
}

/**
 * Find period by date using string comparison (timezone-safe)
 * @param {Array} periods - Array of PayrollPeriod objects
 * @param {string|Date} targetDate - Date to find
 * @returns {Object|null} - Found period or null
 */
function findPeriodByDate(periods, targetDate) {
  if (!periods || !targetDate) return null;
  
  const targetDateString = parseToUTC(targetDate).format('YYYY-MM-DD');
  
  return periods.find(period => {
    if (!period.endDate) return false;
    const periodDateString = parseToUTC(period.endDate).format('YYYY-MM-DD');
    return periodDateString === targetDateString;
  });
}

/**
 * Debug function to log date transformations
 * @param {string} label - Debug label
 * @param {string|Date} dateInput - Date to debug
 */
function debugDate(label, dateInput) {
  if (!dateInput) {
    return;
  }
  
  const utcMoment = parseToUTC(dateInput);
  const saMoment = toSATimezone(utcMoment);
  
}

module.exports = {
  parseToUTC,
  toSATimezone,
  formatForDatabase,
  formatForDisplay,
  createDateRange,
  isSameDay,
  findPeriodByDate,
  debugDate,
  DEFAULT_TIMEZONE
};
