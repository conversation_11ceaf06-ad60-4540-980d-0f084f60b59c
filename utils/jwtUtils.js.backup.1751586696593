const jwt = require('jsonwebtoken');

// JWT Secret should be in environment variables
const getJwtSecret = () => {
  const secret = process.env.JWT_SECRET || process.env.SESSION_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET or SESSION_SECRET must be set in environment variables');
  }
  return secret;
};

const JWT_EXPIRES_IN = '24h'; // Match your session expiry

/**
 * Get cookie configuration options based on environment
 * @param {Boolean} forVercel - Set to true when deploying to Vercel
 * @returns {Object} Cookie options
 */
const getCookieOptions = (forVercel = false) => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Base cookie options
  const options = {
    httpOnly: true,
    path: '/',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  };
  
  // For Vercel deployment, we need specific settings
  if (isProduction || forVercel) {
    options.secure = true; // Required when sameSite is 'none'
    options.sameSite = 'none';
    
    // Don't set domain - let the browser set it automatically
    // This is critical for Vercel deployments
  } else {
    options.secure = false;
    options.sameSite = 'lax';
  }
  
  // Log cookie options in non-production environments
  if (process.env.NODE_ENV !== 'production') {
  }
  
  return options;
};

/**
 * Generate a JWT token for a user
 * @param {Object} user - The user object to encode in the token
 * @returns {String} JWT token
 */
const generateToken = (user) => {
  // Ensure we have a valid user ID in the correct format
  const userId = user.id || user._id;
  
  // Only log in development
  if (process.env.NODE_ENV !== 'production') {
    console.log('Generating token for user:', {
      id: userId,
      username: user.username || user.email,
      email: user.email,
      role: user.role || user.roleName
    });
  }
  
  const payload = {
    id: userId,
    username: user.username || user.email,
    email: user.email,
    role: user.role || user.roleName,
    firstName: user.firstName,
    lastName: user.lastName
  };

  return jwt.sign(payload, getJwtSecret(), {
    expiresIn: JWT_EXPIRES_IN
  });
};

/**
 * Verify and decode a JWT token
 * @param {String} token - The JWT token to verify
 * @returns {Object|null} Decoded token payload or null if invalid
 */
const verifyToken = (token) => {
  try {
    const decoded = jwt.verify(token, getJwtSecret());
    

    
    return decoded;
  } catch (error) {
    // Log errors in all environments but limit sensitive data in production
    if (process.env.NODE_ENV === 'production') {
      console.error('JWT Verification Error:', error.name);
    } else {
      console.error('JWT Verification Error:', error.message);
    }
    return null;
  }
};

/**
 * Extract JWT token from request
 * @param {Object} req - Express request object
 * @returns {String|null} JWT token or null if not found
 */
const extractToken = (req) => {
  // Try to get token from cookie first
  if (req.cookies?.jwt) {
    return req.cookies.jwt;
  }
  
  // Then try Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
};

/**
 * Create JWT middleware that works alongside session
 * Enhanced to better integrate with session authentication
 */
const jwtMiddleware = async (req, res, next) => {

  try {
    const token = extractToken(req);

    if (token) {
      const decoded = verifyToken(token);

      if (decoded) {
        // Store JWT user info
        req.jwtUser = decoded;

        // If no session exists, try to create one for the JWT user
        if (!req.isAuthenticated() && !req.session.passport) {
          req.session.passport = { user: decoded.id };

          // Don't wait for session save here to avoid blocking the request
          req.session.save(err => {
            if (err) {
              console.error('Error saving session for JWT user:', err);
            } else {
            }
          });
        }
      } else {
        // Invalid token - clear any existing JWT cookie
        res.clearCookie('jwt', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/'
        });
      }
    }

    // Log authentication state
    console.log('Auth State:', {
      hasJWT: !!req.jwtUser,
      hasSession: req.isAuthenticated?.(),
      sessionID: req.sessionID,
      sessionPassport: req.session?.passport
    });

    next();
  } catch (error) {
    console.error('JWT Middleware Error:', error);
    // Clear potentially corrupted JWT cookie on error
    res.clearCookie('jwt', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    });
    next();
  }
};

module.exports = {
  generateToken,
  verifyToken,
  extractToken,
  jwtMiddleware,
  getCookieOptions
}; 