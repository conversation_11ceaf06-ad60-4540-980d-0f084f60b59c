const PayrollPeriod = require("../models/PayrollPeriod");
const PayRun = require("../models/payRun");
const moment = require("moment-timezone");

/**
 * PeriodUnlockingUtils - Handles unlocking of PayrollPeriods when pay runs are deleted
 * Ensures proper state transitions and data integrity
 */
class PeriodUnlockingUtils {
  /**
   * Unlock periods associated with a pay run
   * @param {string} payRunId - Pay Run ID
   * @param {string} userId - User performing the action
   * @returns {Object} Result with success boolean and details
   */
  static async unlockPeriodsForPayRun(payRunId, userId) {
    try {
      // Find the pay run with associated periods
      const payRun = await PayRun.findById(payRunId).populate("payrollPeriods");
      
      if (!payRun) {
        return {
          success: false,
          message: "Pay run not found",
        };
      }

      if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
        return {
          success: true,
          message: "No periods to unlock",
          unlockedPeriods: [],
        };
      }

      const periodIds = payRun.payrollPeriods.map(p => p._id);
      const unlockedPeriods = [];

      // Update each period individually for better tracking
      for (const period of payRun.payrollPeriods) {
        if (period.status === "locked" && period.payRun?.toString() === payRunId) {
          // Update the period
          await PayrollPeriod.findByIdAndUpdate(
            period._id,
            {
              $set: { 
                status: "finalized",
                unfinalizedAt: new Date(),
                unfinalizedBy: userId,
              },
              $unset: { payRun: 1 }
            }
          );

          unlockedPeriods.push({
            _id: period._id,
            employee: period.employee,
            startDate: period.startDate,
            endDate: period.endDate,
            previousStatus: "locked",
            newStatus: "finalized",
          });
        }
      }

      return {
        success: true,
        message: `Successfully unlocked ${unlockedPeriods.length} periods`,
        unlockedPeriods: unlockedPeriods,
        totalPeriods: payRun.payrollPeriods.length,
      };
    } catch (error) {
      console.error("Error unlocking periods for pay run:", error);
      return {
        success: false,
        message: "Failed to unlock periods",
        error: error.message,
      };
    }
  }

  /**
   * Unlock a specific period (used when manually unlocking)
   * @param {string} periodId - PayrollPeriod ID
   * @param {string} userId - User performing the action
   * @returns {Object} Result with success boolean and details
   */
  static async unlockPeriod(periodId, userId) {
    try {
      const period = await PayrollPeriod.findById(periodId);
      
      if (!period) {
        return {
          success: false,
          message: "Period not found",
        };
      }

      if (period.status !== "locked") {
        return {
          success: false,
          message: `Period is not locked (current status: ${period.status})`,
        };
      }

      // Check if there's an associated pay run
      if (period.payRun) {
        const payRun = await PayRun.findById(period.payRun);
        if (payRun && payRun.status !== "deleted") {
          return {
            success: false,
            message: "Cannot unlock period - associated pay run still exists",
            payRun: {
              _id: payRun._id,
              reference: payRun.reference,
              status: payRun.status,
            },
          };
        }
      }

      // Update the period
      period.status = "finalized";
      period.unfinalizedAt = new Date();
      period.unfinalizedBy = userId;
      period.payRun = undefined;

      await period.save();

      return {
        success: true,
        message: "Period unlocked successfully",
        period: {
          _id: period._id,
          employee: period.employee,
          startDate: period.startDate,
          endDate: period.endDate,
          previousStatus: "locked",
          newStatus: "finalized",
        },
      };
    } catch (error) {
      console.error("Error unlocking period:", error);
      return {
        success: false,
        message: "Failed to unlock period",
        error: error.message,
      };
    }
  }

  /**
   * Get all locked periods for a company (for administrative purposes)
   * @param {string} companyId - Company ID
   * @returns {Array} Array of locked periods
   */
  static async getLockedPeriods(companyId) {
    try {
      const lockedPeriods = await PayrollPeriod.find({
        company: companyId,
        status: "locked",
      })
      .populate("employee", "firstName lastName")
      .populate("payRun", "reference status")
      .sort({ endDate: -1 });

      return lockedPeriods.map(period => ({
        _id: period._id,
        employee: period.employee,
        startDate: period.startDate,
        endDate: period.endDate,
        period: `${moment(period.startDate).format('DD/MM/YYYY')} - ${moment(period.endDate).format('DD/MM/YYYY')}`,
        payRun: period.payRun,
        lockedSince: period.updatedAt,
      }));
    } catch (error) {
      console.error("Error getting locked periods:", error);
      return [];
    }
  }

  /**
   * Validate period unlocking constraints
   * @param {string} periodId - PayrollPeriod ID
   * @returns {Object} Validation result
   */
  static async validateUnlocking(periodId) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      const period = await PayrollPeriod.findById(periodId).populate("payRun");
      
      if (!period) {
        validationResult.errors.push("Period not found");
        validationResult.isValid = false;
        return validationResult;
      }

      if (period.status !== "locked") {
        validationResult.errors.push(`Period is not locked (status: ${period.status})`);
        validationResult.isValid = false;
      }

      if (period.payRun) {
        if (period.payRun.status === "finalized") {
          validationResult.warnings.push("Associated pay run is finalized");
        }
        
        if (period.payRun.status === "released") {
          validationResult.warnings.push("Associated pay run has been released");
        }
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating period unlocking:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Validation error occurred");
      return validationResult;
    }
  }

  /**
   * Bulk unlock periods (for administrative purposes)
   * @param {Array} periodIds - Array of PayrollPeriod IDs
   * @param {string} userId - User performing the action
   * @returns {Object} Result with success details
   */
  static async bulkUnlockPeriods(periodIds, userId) {
    try {
      const results = [];
      
      for (const periodId of periodIds) {
        const result = await this.unlockPeriod(periodId, userId);
        results.push({
          periodId: periodId,
          ...result,
        });
      }

      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      return {
        success: failed.length === 0,
        message: `Unlocked ${successful.length} of ${periodIds.length} periods`,
        successful: successful,
        failed: failed,
        summary: {
          total: periodIds.length,
          successful: successful.length,
          failed: failed.length,
        },
      };
    } catch (error) {
      console.error("Error in bulk unlock:", error);
      return {
        success: false,
        message: "Bulk unlock failed",
        error: error.message,
      };
    }
  }
}

module.exports = PeriodUnlockingUtils;
