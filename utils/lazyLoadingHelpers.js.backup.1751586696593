/**
 * Helper functions for implementing lazy loading in templates
 */

/**
 * Generate lazy loading attributes for images
 * @param {string} src - Image source URL
 * @param {string} alt - Alt text
 * @param {boolean} isAboveFold - Whether image is above the fold
 * @param {string} placeholder - Placeholder image URL
 * @returns {object} - Attributes object for image tag
 */
function getLazyImageAttributes(src, alt = '', isAboveFold = false, placeholder = null) {
  const attributes = {
    alt: alt,
    class: 'lazy-image'
  };

  if (isAboveFold) {
    // Above-the-fold images should load immediately
    attributes.src = src;
    attributes.loading = 'eager';
  } else {
    // Below-the-fold images should lazy load
    attributes['data-lazy'] = src;
    attributes.loading = 'lazy';
    attributes.src = placeholder || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGM0Y0RjYiLz48L3N2Zz4=';
    attributes.class += ' lazy-loading';
  }

  return attributes;
}

/**
 * Generate lazy loading attributes for components
 * @param {string} componentType - Type of component (chart, table, etc.)
 * @param {object} componentData - Data for the component
 * @param {boolean} isAboveFold - Whether component is above the fold
 * @returns {object} - Attributes object for component container
 */
function getLazyComponentAttributes(componentType, componentData = {}, isAboveFold = false) {
  const attributes = {
    class: 'lazy-component'
  };

  if (!isAboveFold) {
    attributes['data-lazy-component'] = componentType;
    attributes['data-component-data'] = JSON.stringify(componentData);
    attributes.class += ' lazy-loading';
  }

  return attributes;
}

/**
 * Generate attributes for progressive lists
 * @param {number} initialCount - Number of items to show initially
 * @param {number} totalCount - Total number of items
 * @returns {object} - Attributes object for list container
 */
function getProgressiveListAttributes(initialCount = 10, totalCount = 0) {
  return {
    'data-progressive-list': 'true',
    'data-initial-count': initialCount.toString(),
    'data-total-count': totalCount.toString(),
    class: 'progressive-list'
  };
}

/**
 * Generate attributes for deferred content
 * @param {number} delay - Delay in milliseconds before loading
 * @returns {object} - Attributes object for deferred content
 */
function getDeferredContentAttributes(delay = 100) {
  return {
    'data-defer-load': 'true',
    'data-defer-delay': delay.toString(),
    class: 'deferred-content deferred'
  };
}

/**
 * Create a placeholder for lazy-loaded content
 * @param {string} type - Type of placeholder (image, chart, table, etc.)
 * @param {object} dimensions - Width and height
 * @returns {string} - HTML for placeholder
 */
function createPlaceholder(type = 'content', dimensions = { width: '100%', height: '200px' }) {
  const placeholders = {
    image: `
      <div class="image-placeholder" style="width: ${dimensions.width}; height: ${dimensions.height}; background: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="8.5" cy="8.5" r="1.5"/>
          <polyline points="21,15 16,10 5,21"/>
        </svg>
      </div>
    `,
    chart: `
      <div class="chart-placeholder" style="width: ${dimensions.width}; height: ${dimensions.height}; background: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
        </svg>
      </div>
    `,
    table: `
      <div class="table-placeholder" style="width: ${dimensions.width}; height: ${dimensions.height}; background: #f3f4f6; border-radius: 8px; padding: 1rem;">
        <div style="height: 20px; background: #e5e7eb; border-radius: 4px; margin-bottom: 1rem;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem;"></div>
      </div>
    `,
    content: `
      <div class="content-placeholder" style="width: ${dimensions.width}; height: ${dimensions.height}; background: #f3f4f6; border-radius: 8px; padding: 1rem;">
        <div style="height: 24px; background: #e5e7eb; border-radius: 4px; margin-bottom: 1rem; width: 60%;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem; width: 80%;"></div>
        <div style="height: 16px; background: #e5e7eb; border-radius: 4px; width: 70%;"></div>
      </div>
    `
  };

  return placeholders[type] || placeholders.content;
}

/**
 * Generate CSS for lazy loading animations
 * @returns {string} - CSS string
 */
function getLazyLoadingCSS() {
  return `
    .lazy-loading {
      opacity: 0.6;
      transition: opacity 0.3s ease;
    }
    
    .lazy-image.loaded {
      opacity: 1;
    }
    
    .lazy-component.loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #6366f1;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .lazy-component.loaded {
      opacity: 1;
    }
    
    .deferred-content.deferred {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.5s ease, transform 0.5s ease;
    }
    
    .deferred-content.loaded {
      opacity: 1;
      transform: translateY(0);
    }
    
    .progressive-list [data-list-item]:nth-child(n+11) {
      display: none;
    }
    
    .progressive-list.expanded [data-list-item] {
      display: block;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }
  `;
}

/**
 * EJS helper function to render lazy image
 * @param {string} src - Image source
 * @param {string} alt - Alt text
 * @param {boolean} isAboveFold - Whether image is above fold
 * @param {object} additionalAttributes - Additional HTML attributes
 * @returns {string} - HTML img tag
 */
function renderLazyImage(src, alt = '', isAboveFold = false, additionalAttributes = {}) {
  const lazyAttrs = getLazyImageAttributes(src, alt, isAboveFold);
  const allAttrs = { ...lazyAttrs, ...additionalAttributes };
  
  const attrString = Object.entries(allAttrs)
    .map(([key, value]) => `${key}="${value}"`)
    .join(' ');
  
  return `<img ${attrString} />`;
}

module.exports = {
  getLazyImageAttributes,
  getLazyComponentAttributes,
  getProgressiveListAttributes,
  getDeferredContentAttributes,
  createPlaceholder,
  getLazyLoadingCSS,
  renderLazyImage
};
