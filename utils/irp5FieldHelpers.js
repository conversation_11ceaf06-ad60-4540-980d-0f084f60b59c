const moment = require("moment");
const fs = require("fs");
const path = require("path");

class IRP5FieldHelpers {
  // Format currency values
  static formatCurrency(value) {
    return value ? Number(value).toFixed(2) : "0.00";
  }

  // Format dates to CCYYMMDD format
  static formatDate(date) {
    return date ? moment(date).format("YYYYMMDD") : "";
  }

  // Format tax reference number
  static formatTaxRef(taxRef) {
    return taxRef ? taxRef.replace(/\D/g, "") : "";
  }

  // Format ID number with validation
  static formatIDNumber(idNumber) {
    if (!idNumber) return "";
    const cleaned = idNumber.replace(/\D/g, "");
    return cleaned.length === 13 ? cleaned : "";
  }

  // Format address fields
  static formatAddress(address) {
    if (!address) return {};
    return {
      unitNumber: address.unitNumber || "",
      complexName: address.complexName || "",
      streetNumber: address.streetNumber || "",
      streetName: address.streetName || "",
      suburb: address.suburb || "",
      city: address.city || "",
      postalCode: address.postalCode || "",
      countryCode: address.countryCode || "ZA",
    };
  }

  // Calculate period worked
  static calculatePeriodWorked(startDate, endDate) {
    if (!startDate || !endDate) return 0;
    const start = moment(startDate);
    const end = moment(endDate);
    return end.diff(start, "months") + 1;
  }

  // Format income amounts for specific codes
  static formatIncomeAmount(amount, code) {
    const formattedAmount = this.formatCurrency(amount);
    return {
      code,
      amount: formattedAmount,
      description: this.getIncomeDescription(code),
    };
  }

  // Get descriptions for income codes
  static getIncomeDescription(code) {
    const descriptions = {
      3601: "Income (Salary)",
      3701: "Travel Allowance",
      3801: "Capital Payments",
      3810: "Medical Aid",
      3813: "Medical Service Costs",
      3815: "Non-Taxable Earnings",
      3816: "Travel Reimbursement",
      3820: "Fringe Benefits",
      3821: "Use of Motor Vehicle",
      3822: "Accommodation",
      3825: "Cell Phone Benefits",
    };
    return descriptions[code] || "Other Income";
  }

  // Populate employee information
  static populateEmployeeInfo(form, employee, fieldMapping) {
    try {
      // Personal Details
      const personal = fieldMapping.employeeInfo.personal;
      form.getTextField(personal.surname)?.setText(employee.lastName || "");
      form.getTextField(personal.firstNames)?.setText(employee.firstName || "");
      form
        .getTextField(personal.initials)
        ?.setText(this.getInitials(employee.firstName));
      form
        .getTextField(personal.identityNumber)
        ?.setText(this.formatIDNumber(employee.idNumber));
      form
        .getTextField(personal.taxReference)
        ?.setText(this.formatTaxRef(employee.incomeTaxNumber));
      form
        .getTextField(personal.dateOfBirth)
        ?.setText(this.formatDate(employee.dob));
      form
        .getTextField(personal.companyNumber)
        ?.setText(employee.companyEmployeeNumber || "");

      // Identity Type Checkboxes
      if (employee.idType === "rsa") {
        form.getCheckBox(personal.identityType.rsaId)?.check();
      } else if (employee.idType === "passport") {
        form.getCheckBox(personal.identityType.passport)?.check();
      } else {
        form.getCheckBox(personal.identityType.other)?.check();
      }

      // Contact Details
      form
        .getTextField(fieldMapping.employeeInfo.contact.mobileNumber)
        ?.setText(employee.phone || "");
      form
        .getTextField(fieldMapping.employeeInfo.contact.emailAddress)
        ?.setText(employee.email || "");

      // Residential Address
      if (employee.residentialAddress) {
        const residential = fieldMapping.employeeInfo.address.residential;
        Object.entries(this.formatAddress(employee.residentialAddress)).forEach(
          ([key, value]) => {
            form.getTextField(residential[key])?.setText(value);
          }
        );
      }

      // Postal Address
      if (employee.postalAddress) {
        const postal = fieldMapping.employeeInfo.address.postal;
        if (
          this.addressesMatch(
            employee.residentialAddress,
            employee.postalAddress
          )
        ) {
          form.getCheckBox(postal.sameAsResidential)?.check();
        } else {
          Object.entries(this.formatAddress(employee.postalAddress)).forEach(
            ([key, value]) => {
              form.getTextField(postal[key])?.setText(value);
            }
          );
        }
      }

      // Employment Status
      const status = fieldMapping.employeeInfo.employmentStatus;
      if (employee.employmentType === "permanent") {
        form.getCheckBox(status.permanent)?.check();
      } else if (employee.employmentType === "temporary") {
        form.getCheckBox(status.temporary)?.check();
      }
    } catch (error) {
      console.error("Error populating employee info:", error);
      throw error;
    }
  }

  // Populate financial information
  static populateFinancialInfo(form, financialData, fieldMapping) {
    try {
      const financial = fieldMapping.financialInfo;

      // Income
      if (financialData.income) {
        // Normal Income (3601)
        form
          .getTextField(financial.income.normalIncome)
          ?.setText(this.formatCurrency(financialData.income[3601]));

        // Allowances
        if (financialData.income.allowances) {
          const allowances = financial.income.allowances;
          form
            .getTextField(allowances.travel)
            ?.setText(
              this.formatCurrency(financialData.income.allowances.travel)
            );
          form
            .getTextField(allowances.publicTransport)
            ?.setText(
              this.formatCurrency(
                financialData.income.allowances.publicTransport
              )
            );
          // ... other allowances
        }

        // Benefits
        if (financialData.income.benefits) {
          const benefits = financial.income.benefits;
          form
            .getTextField(benefits.medical)
            ?.setText(
              this.formatCurrency(financialData.income.benefits.medical)
            );
          form
            .getTextField(benefits.pension)
            ?.setText(
              this.formatCurrency(financialData.income.benefits.pension)
            );
          // ... other benefits
        }
      }

      // Deductions
      if (financialData.deductions) {
        const deductions = financial.deductions;
        form
          .getTextField(deductions.paye)
          ?.setText(this.formatCurrency(financialData.deductions.paye));
        form
          .getTextField(deductions.uif)
          ?.setText(this.formatCurrency(financialData.deductions.uif));
        form
          .getTextField(deductions.sdl)
          ?.setText(this.formatCurrency(financialData.deductions.sdl));
        // ... other deductions
      }

      // ETI Information
      if (financialData.eti) {
        const eti = financial.eti;
        form
          .getTextField(eti.monthsInEmployment)
          ?.setText(financialData.eti.monthsInEmployment.toString());
        form
          .getTextField(eti.qualifyingMonths)
          ?.setText(financialData.eti.qualifyingMonths.toString());
        form
          .getTextField(eti.monthlyRemuneration)
          ?.setText(this.formatCurrency(financialData.eti.monthlyRemuneration));
        if (financialData.eti.employedAfterDate) {
          form.getCheckBox(eti.employedAfterDate)?.check();
        }
      }
    } catch (error) {
      console.error("Error populating financial info:", error);
      throw error;
    }
  }

  // Helper method to check if addresses match
  static addressesMatch(addr1, addr2) {
    if (!addr1 || !addr2) return false;
    return (
      addr1.streetNumber === addr2.streetNumber &&
      addr1.streetName === addr2.streetName &&
      addr1.suburb === addr2.suburb &&
      addr1.city === addr2.city &&
      addr1.postalCode === addr2.postalCode
    );
  }

  // Helper method to format currency values
  static formatCurrency(value) {
    if (!value) return "0.00";
    return Number(value).toFixed(2);
  }

  // Helper method to format dates
  static formatDate(date) {
    if (!date) return "";
    return moment(date).format("YYYYMMDD");
  }

  // Helper method to get initials
  static getInitials(firstName) {
    if (!firstName) return "";
    return firstName
      .split(" ")
      .map((name) => name[0])
      .join("")
      .toUpperCase();
  }

  // Add this method to the IRP5FieldHelpers class
  static async verifyFieldPositions(pdfDoc, fieldMapping) {
    try {
      const form = pdfDoc.getForm();

      // Test data for verification
      const testData = {
        employeeInfo: {
          personal: {
            surname: "TEST SURNAME",
            firstNames: "TEST FIRST NAME",
            initials: "TFN",
            identityNumber: "8001015009087",
            taxReference: "1234567890",
          },
          contact: {
            mobileNumber: "0821234567",
            emailAddress: "<EMAIL>",
          },
        },
        employerInfo: {
          tradingName: "TEST COMPANY",
          registrationNumbers: {
            paye: "7160705219",
            sdl: "L160705219",
            uif: "U160705219",
          },
        },
        financialInfo: {
          income: {
            normalIncome: "120000.00",
            allowances: {
              travel: "5000.00",
            },
          },
          deductions: {
            paye: "2500.00",
            uif: "177.12",
            sdl: "1200.00",
          },
        },
      };

      // Fill form with test data
      Object.entries(fieldMapping).forEach(([section, fields]) => {

        const fillFields = (obj, data) => {
          Object.entries(obj).forEach(([key, value]) => {
            if (typeof value === "string") {
              const field = form.getTextField(value);
              const testValue = data?.[key] || `TEST_${key.toUpperCase()}`;
              if (field) {
                field.setText(testValue);
              } else {
                console.log(`  ERROR: Field not found for ${key}: ${value}`);
              }
            } else if (typeof value === "object") {
              fillFields(value, data?.[key]);
            }
          });
        };

        fillFields(fields, testData[section]);
      });

      // Save test PDF for verification
      const testPdfPath = path.join(
        __dirname,
        "..",
        "temp",
        "irp5_field_test.pdf"
      );
      const pdfBytes = await pdfDoc.save();
      await fs.writeFile(testPdfPath, pdfBytes);


      return {
        success: true,
        testPdfPath,
        message: "Field verification complete. Please check the test PDF.",
      };
    } catch (error) {
      console.error("Error verifying field positions:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Add this method to help identify correct field positions
  static async identifyFieldPosition(pdfDoc, searchText) {
    try {
      const form = pdfDoc.getForm();
      const fields = form.getFields();

      // Clear all fields
      fields.forEach((field) => {
        if (field.constructor.name === "PDFTextField") {
          field.setText("");
        }
      });

      // Fill each field with its name for identification
      fields.forEach((field) => {
        if (field.constructor.name === "PDFTextField") {
          field.setText(field.getName());
        }
      });

      // Save identification PDF
      const identifyPdfPath = path.join(
        __dirname,
        "..",
        "temp",
        "irp5_field_identify.pdf"
      );
      const pdfBytes = await pdfDoc.save();
      await fs.writeFile(identifyPdfPath, pdfBytes);


      return {
        success: true,
        identifyPdfPath,
        message: "Field identification complete. Please check the PDF.",
      };
    } catch (error) {
      console.error("Error identifying field positions:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = IRP5FieldHelpers;
