const Notification = require("../models/notification");
const User = require("../models/user");

async function createNotification(userId, title, category, content) {
  try {
    const notification = new Notification({
      userId,
      title,
      category,
      content, // Store the rich text content
    });

    await notification.save();

    await User.findByIdAndUpdate(userId, {
      $push: { notifications: notification._id },
    });

    return notification;
  } catch (error) {
    console.error("Error creating notification:", error);
    throw error;
  }
}

module.exports = { createNotification };
