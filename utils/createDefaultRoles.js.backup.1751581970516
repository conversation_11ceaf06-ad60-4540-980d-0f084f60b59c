const Role = require("../models/Role");
const logger = require("../logger");

const createDefaultRoles = async (retries = 3) => {
  try {
    const roles = ["admin", "manager", "employee"];
    for (const roleName of roles) {
      await Role.findOneAndUpdate(
        { name: roleName },
        { name: roleName },
        { upsert: true, new: true, maxTimeMS: 20000 } // Increase operation timeout
      );
    }
    logger.info("Default roles created successfully");
  } catch (error) {
    logger.error("Error creating default roles:", error);
    if (retries > 0) {
      logger.info(`Retrying... (${retries} attempts left)`);
      await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds before retrying
      return createDefaultRoles(retries - 1);
    }
    throw error;
  }
};

module.exports = createDefaultRoles;
