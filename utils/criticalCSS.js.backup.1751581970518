const fs = require('fs');
const path = require('path');

/**
 * Critical CSS utility for extracting and inlining above-the-fold styles
 */
class CriticalCSSExtractor {
  constructor() {
    this.criticalSelectors = [
      // Layout and structure
      'html', 'body', '*', '*::before', '*::after',
      '.layout-wrapper', '.content-wrapper', '.main-container',
      
      // Header and navigation
      '.header', '.navbar', '.nav', '.sidebar', '.mobile-nav',
      '.header-container', '.nav-item', '.nav-link',
      
      // Dashboard critical elements
      '.greeting-card', '.greeting-content', '.greeting-text',
      '.summary-section', '.summary-header', '.summary-grid',
      '.metric-card', '.metric-value', '.metric-label',
      
      // Form elements (for login)
      '.form-group', '.form-control', 'input', 'button',
      '.btn', '.btn-primary', '.btn-secondary',
      
      // Typography
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span',
      
      // Utility classes
      '.container', '.row', '.col', '.d-flex', '.justify-content-between',
      '.align-items-center', '.text-center', '.text-left', '.text-right',
      
      // Loading states
      '.loading', '.spinner', '.skeleton',
      
      // Toast notifications (critical for UX)
      '.toast-notification', '.toast-notification.success', 
      '.toast-notification.error', '.toast-notification.info'
    ];
  }

  /**
   * Extract critical CSS from multiple CSS files
   */
  extractCriticalCSS() {
    const cssFiles = [
      'public/css/styles.css',
      'public/css/dashboard.css',
      'public/css/header.css',
      'public/css/sidebar.css'
    ];

    let criticalCSS = '';

    // Add CSS variables first
    criticalCSS += `
:root {
  --primary-color: #6366f1;
  --secondary-color: #818cf8;
  --success-color: #22c55e;
  --danger-color: #ef4444;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
}

/* Critical base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout structure */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-container {
  margin-left: 0;
  margin-top: 80px;
  padding: 2rem;
  background: var(--background-color);
  min-height: calc(100vh - 80px);
}

/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: white;
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 2rem;
}

/* Sidebar styles */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid var(--border-color);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 999;
  overflow-y: auto;
}

/* Dashboard greeting card */
.greeting-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.greeting-text h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* Summary section */
.summary-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(99, 102, 241, 0.08);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.metric-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-control, input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-control:focus, input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #5048e5;
  transform: translateY(-1px);
}

/* Toast notifications */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  max-width: 400px;
  min-width: 300px;
}

.toast-notification.success {
  border-left: 4px solid var(--success-color);
}

.toast-notification.error {
  border-left: 4px solid var(--danger-color);
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .main-container {
    margin-left: 0;
    padding: 1rem;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
}
`;

    return criticalCSS;
  }

  /**
   * Generate critical CSS file
   */
  generateCriticalCSSFile() {
    const criticalCSS = this.extractCriticalCSS();
    const outputPath = path.join(__dirname, '../public/css/critical.css');
    
    fs.writeFileSync(outputPath, criticalCSS);
    console.log('Critical CSS generated at:', outputPath);
    
    return criticalCSS;
  }

  /**
   * Get critical CSS as inline string for templates
   */
  getInlineCSS() {
    return this.extractCriticalCSS();
  }
}

module.exports = new CriticalCSSExtractor();
