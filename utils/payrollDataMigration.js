const mongoose = require('mongoose');
const moment = require('moment');
const Payroll = require('../models/Payroll');
const PayrollPeriod = require('../models/PayrollPeriod');

/**
 * Utility to link existing Payroll records to their corresponding PayrollPeriod records
 * This fixes the data relationship issue where many Payroll records don't have payrollPeriod references
 */
class PayrollDataMigration {
  
  /**
   * Links Payroll records to PayrollPeriod records based on date matching
   * @param {string} companyId - Optional: limit to specific company
   * @param {boolean} dryRun - If true, only shows what would be updated without making changes
   * @returns {Promise<object>} - Migration results
   */
  static async linkPayrollToPayrollPeriods(companyId = null, dryRun = true) {
    
    const results = {
      totalPayrollRecords: 0,
      unlinkedPayrollRecords: 0,
      successfulLinks: 0,
      failedLinks: 0,
      errors: [],
      linkedRecords: []
    };

    try {
      // Build query for payroll records without payrollPeriod references
      const payrollQuery = {
        $or: [
          { payrollPeriod: { $exists: false } },
          { payrollPeriod: null }
        ]
      };
      
      if (companyId) {
        payrollQuery.company = companyId;
      }

      const unlinkedPayrolls = await Payroll.find(payrollQuery).populate('employee company');
      
      results.totalPayrollRecords = await Payroll.countDocuments(companyId ? { company: companyId } : {});
      results.unlinkedPayrollRecords = unlinkedPayrolls.length;
      

      if (unlinkedPayrolls.length === 0) {
        return results;
      }

      // Process each unlinked payroll record
      for (const payroll of unlinkedPayrolls) {
        try {

          // Find matching payroll periods for this employee
          const periodQuery = {
            employee: payroll.employee._id,
            company: payroll.company._id
          };

          const payrollPeriods = await PayrollPeriod.find(periodQuery);

          // Find the best matching period based on date overlap
          let bestMatch = null;
          let bestScore = 0;

          for (const period of payrollPeriods) {
            const payrollMonth = moment(payroll.month);
            const periodStart = moment(period.startDate);
            const periodEnd = moment(period.endDate);


            // Calculate match score based on date proximity
            let score = 0;

            // Perfect match: payroll month falls within period
            if (payrollMonth.isBetween(periodStart, periodEnd, 'day', '[]')) {
              score = 100;
            }
            // Good match: same month as period end
            else if (payrollMonth.isSame(periodEnd, 'month')) {
              score = 90;
            }
            // Acceptable match: within 1 month of period end
            else if (Math.abs(payrollMonth.diff(periodEnd, 'months')) <= 1) {
              score = 70;
            }
            // Weak match: within 3 months of period end
            else if (Math.abs(payrollMonth.diff(periodEnd, 'months')) <= 3) {
              score = 50;
            }

            if (score > bestScore) {
              bestMatch = period;
              bestScore = score;
            }
          }

          if (bestMatch && bestScore >= 50) {
            
            if (!dryRun) {
              // Update the payroll record with the payrollPeriod reference
              await Payroll.updateOne(
                { _id: payroll._id },
                { $set: { payrollPeriod: bestMatch._id } }
              );
            } else {
            }

            results.successfulLinks++;
            results.linkedRecords.push({
              payrollId: payroll._id,
              periodId: bestMatch._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              payrollMonth: moment(payroll.month).format('YYYY-MM-DD'),
              periodRange: `${moment(bestMatch.startDate).format('YYYY-MM-DD')} to ${moment(bestMatch.endDate).format('YYYY-MM-DD')}`,
              matchScore: bestScore
            });
          } else {
            results.failedLinks++;
            results.errors.push({
              payrollId: payroll._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              payrollMonth: moment(payroll.month).format('YYYY-MM-DD'),
              reason: 'No matching payroll period found'
            });
          }

        } catch (error) {
          console.error(`❌ Error processing payroll ${payroll._id}:`, error.message);
          results.failedLinks++;
          results.errors.push({
            payrollId: payroll._id,
            employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
            reason: error.message
          });
        }
      }

      
      if (results.errors.length > 0) {
        results.errors.forEach(error => {
        });
      }

      if (dryRun && results.successfulLinks > 0) {
      }

      return results;

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Validates the integrity of payroll-period relationships
   * @param {string} companyId - Optional: limit to specific company
   * @returns {Promise<object>} - Validation results
   */
  static async validatePayrollPeriodLinks(companyId = null) {
    
    const results = {
      totalPayrollRecords: 0,
      linkedRecords: 0,
      unlinkedRecords: 0,
      invalidLinks: 0,
      validationErrors: []
    };

    try {
      const query = companyId ? { company: companyId } : {};
      const payrollRecords = await Payroll.find(query).populate('payrollPeriod employee');
      
      results.totalPayrollRecords = payrollRecords.length;

      for (const payroll of payrollRecords) {
        if (payroll.payrollPeriod) {
          results.linkedRecords++;
          
          // Validate the link
          const payrollMonth = moment(payroll.month);
          const periodStart = moment(payroll.payrollPeriod.startDate);
          const periodEnd = moment(payroll.payrollPeriod.endDate);
          
          if (!payrollMonth.isBetween(periodStart, periodEnd, 'day', '[]') && 
              !payrollMonth.isSame(periodEnd, 'month')) {
            results.invalidLinks++;
            results.validationErrors.push({
              payrollId: payroll._id,
              periodId: payroll.payrollPeriod._id,
              employeeName: `${payroll.employee?.firstName} ${payroll.employee?.lastName}`,
              issue: 'Payroll month does not align with period dates'
            });
          }
        } else {
          results.unlinkedRecords++;
        }
      }


      return results;

    } catch (error) {
      console.error('❌ Validation failed:', error);
      throw error;
    }
  }
}

module.exports = PayrollDataMigration;
