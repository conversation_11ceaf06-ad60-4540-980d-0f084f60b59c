const moment = require('moment');

// Fixed public holidays that occur on the same date every year
const FIXED_HOLIDAYS = [
    { day: 1, month: 1, name: "New Year's Day" },
    { day: 21, month: 3, name: "Human Rights Day" },
    { day: 27, month: 4, name: "Freedom Day" },
    { day: 1, month: 5, name: "Workers' Day" },
    { day: 16, month: 6, name: "Youth Day" },
    { day: 9, month: 8, name: "National Women's Day" },
    { day: 24, month: 9, name: "Heritage Day" },
    { day: 16, month: 12, name: "Day of Reconciliation" },
    { day: 25, month: 12, name: "Christmas Day" },
    { day: 26, month: 12, name: "Day of Goodwill" }
];

function formatDate(date) {
    return moment(date).format('YYYY-MM-DD');
}

// Calculate Easter dates using the Meeus/Jones/Butcher algorithm
function calculateEaster(year) {
    try {
        const a = year % 19;
        const b = Math.floor(year / 100);
        const c = year % 100;
        const d = Math.floor(b / 4);
        const e = b % 4;
        const f = Math.floor((b + 8) / 25);
        const g = Math.floor((b - f + 1) / 3);
        const h = (19 * a + b - d - g + 15) % 30;
        const i = Math.floor(c / 4);
        const k = c % 4;
        const l = (32 + 2 * e + 2 * i - h - k) % 7;
        const m = Math.floor((a + 11 * h + 22 * l) / 451);
        const month = Math.floor((h + l - 7 * m + 114) / 31);
        const day = ((h + l - 7 * m + 114) % 31) + 1;
        
        return { month, day };
    } catch (error) {
        console.error(`Error calculating Easter for year ${year}:`, error);
        return null;
    }
}

function createHolidayEvent(title, date) {
    return {
        title,
        name: title, // Add name field for compatibility
        start: formatDate(date),
        date: formatDate(date), // Add date field for compatibility
        backgroundColor: '#fee2e2',
        borderColor: '#fecaca',
        textColor: '#991b1b',
        allDay: true,
        className: 'holiday public-holiday',
        extendedProps: {
            type: 'holiday',
            isPublicHoliday: true,
            description: `South African Public Holiday: ${title}`
        }
    };
}

// Get all public holidays for a specific year
function getPublicHolidays(year) {
    const holidays = [];

    try {
        // Add fixed holidays
        FIXED_HOLIDAYS.forEach(holiday => {
            const date = moment(`${year}-${holiday.month}-${holiday.day}`, 'YYYY-M-D');
            if (date.isValid()) {
                holidays.push(createHolidayEvent(holiday.name, date));
                console.log(`Added fixed holiday: ${holiday.name} on ${date.format('YYYY-MM-DD')}`);
            } else {
                console.error(`Invalid date for holiday: ${holiday.name} in year ${year}`);
            }
        });

        // Calculate Easter dates
        const easter = calculateEaster(year);
        if (easter) {
            const easterSunday = moment(`${year}-${easter.month}-${easter.day}`, 'YYYY-M-D');
            if (easterSunday.isValid()) {
                const goodFriday = moment(easterSunday).subtract(2, 'days');
                const familyDay = moment(easterSunday).add(1, 'days');

                holidays.push(
                    createHolidayEvent('Good Friday', goodFriday),
                    createHolidayEvent('Family Day', familyDay)
                );
                console.log(`Added Easter holidays for ${year}: Good Friday on ${goodFriday.format('YYYY-MM-DD')}, Family Day on ${familyDay.format('YYYY-MM-DD')}`);
            }
        }

        // Handle public holidays that fall on Sundays
        holidays.forEach(holiday => {
            const holidayDate = moment(holiday.start);
            if (holidayDate.day() === 0) { // 0 is Sunday
                const observedDate = moment(holidayDate).add(1, 'days');
                holidays.push(createHolidayEvent(`${holiday.title} (Observed)`, observedDate));
                console.log(`Added observed holiday for ${holiday.title} on ${observedDate.format('YYYY-MM-DD')}`);
            }
        });

        return holidays;
    } catch (error) {
        console.error(`Error generating holidays for year ${year}:`, error);
        return [];
    }
}

// Get holidays for a range of years
function getHolidaysForRange(startYear, endYear) {
    let holidays = [];
    
    try {
        for (let year = startYear; year <= endYear; year++) {
            const yearHolidays = getPublicHolidays(year);
            holidays = holidays.concat(yearHolidays);
        }
        return holidays;
    } catch (error) {
        console.error('Error generating holidays for range:', error);
        return [];
    }
}

module.exports = {
    getPublicHolidays,
    getHolidaysForRange
};
