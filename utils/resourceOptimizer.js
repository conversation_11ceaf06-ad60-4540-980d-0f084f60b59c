/**
 * Resource Optimizer for PandaPayroll
 * Handles render-blocking resource elimination and asset optimization
 */

class ResourceOptimizer {
  constructor() {
    this.criticalResources = new Set();
    this.deferredResources = new Set();
    this.preloadResources = new Set();
  }

  /**
   * Categorize resources based on criticality
   */
  categorizeResources() {
    // Critical resources that must load immediately
    this.criticalResources = new Set([
      '/css/critical.css',
      '/js/critical.js',
      'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
    ]);

    // Resources that can be deferred
    this.deferredResources = new Set([
      'https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js',
      'https://cdn.jsdelivr.net/npm/chart.js',
      'https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js',
      'https://unpkg.com/@phosphor-icons/web@2.0.3/dist/index.umd.js',
      '/js/notifications.js',
      '/js/main.js',
      '/js/ui19-signature.js'
    ]);

    // Resources to preload
    this.preloadResources = new Set([
      '/css/dashboard.css',
      '/css/header.css',
      '/css/sidebar.css'
    ]);
  }

  /**
   * Generate optimized script loading HTML
   */
  generateOptimizedScriptLoading(scripts = []) {
    let html = '';
    
    // Add critical scripts first (blocking)
    scripts.forEach(script => {
      if (this.criticalResources.has(script)) {
        html += `<script src="${script}"></script>\n`;
      }
    });

    // Add deferred scripts
    html += `
<script>
  // Optimized script loader
  (function() {
    const deferredScripts = [
      ${Array.from(this.deferredResources)
        .filter(script => script.endsWith('.js'))
        .map(script => `'${script}'`)
        .join(',\n      ')}
    ];
    
    // Load scripts after DOM is ready
    function loadDeferredScripts() {
      deferredScripts.forEach((src, index) => {
        setTimeout(() => {
          const script = document.createElement('script');
          script.src = src;
          script.async = true;
          script.defer = true;
          
          // Add error handling
          script.onerror = function() {
          };
          
          document.head.appendChild(script);
        }, index * 50); // Stagger loading to prevent blocking
      });
    }
    
    // Load after DOM content is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadDeferredScripts);
    } else {
      // DOM already loaded
      setTimeout(loadDeferredScripts, 0);
    }
  })();
</script>`;

    return html;
  }

  /**
   * Generate optimized CSS loading HTML
   */
  generateOptimizedCSSLoading(stylesheets = []) {
    let html = '';
    
    // Preload critical CSS
    this.preloadResources.forEach(css => {
      html += `<link rel="preload" href="${css}" as="style" onload="this.onload=null;this.rel='stylesheet'">\n`;
    });
    
    // Add fallback for preloaded CSS
    html += `<noscript>\n`;
    this.preloadResources.forEach(css => {
      html += `  <link rel="stylesheet" href="${css}">\n`;
    });
    html += `</noscript>\n`;

    // Load non-critical CSS asynchronously
    const nonCriticalCSS = stylesheets.filter(css => 
      !this.criticalResources.has(css) && !this.preloadResources.has(css)
    );

    if (nonCriticalCSS.length > 0) {
      html += `
<script>
  // Async CSS loader
  (function() {
    const nonCriticalCSS = [
      ${nonCriticalCSS.map(css => `'${css}'`).join(',\n      ')}
    ];
    
    function loadCSS(href) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.media = 'print';
      link.onload = function() {
        this.media = 'all';
        this.onload = null;
      };
      link.onerror = function() {
      };
      document.head.appendChild(link);
    }
    
    // Load after critical rendering
    setTimeout(() => {
      nonCriticalCSS.forEach(loadCSS);
    }, 100);
  })();
</script>`;
    }

    return html;
  }

  /**
   * Generate resource hints (preload, prefetch, preconnect)
   */
  generateResourceHints() {
    let html = '';
    
    // DNS prefetch for external domains
    const externalDomains = [
      'cdnjs.cloudflare.com',
      'cdn.jsdelivr.net',
      'unpkg.com',
      'fonts.googleapis.com',
      'fonts.gstatic.com'
    ];
    
    externalDomains.forEach(domain => {
      html += `<link rel="dns-prefetch" href="//${domain}">\n`;
    });
    
    // Preconnect for critical external resources
    html += `<link rel="preconnect" href="https://fonts.googleapis.com">\n`;
    html += `<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>\n`;
    
    // Preload critical resources
    this.preloadResources.forEach(resource => {
      const asType = resource.endsWith('.css') ? 'style' : 
                    resource.endsWith('.js') ? 'script' : 'fetch';
      html += `<link rel="preload" href="${resource}" as="${asType}">\n`;
    });
    
    return html;
  }

  /**
   * Generate service worker for caching
   */
  generateServiceWorkerRegistration() {
    return `
<script>
  // Service Worker registration for caching
  if ('serviceWorker' in navigator && location.protocol === 'https:') {
    window.addEventListener('load', function() {
      navigator.serviceWorker.register('/sw.js')
        .then(function(registration) {
        })
        .catch(function(registrationError) {
        });
    });
  }
</script>`;
  }

  /**
   * Generate complete optimized head section
   */
  generateOptimizedHead(options = {}) {
    this.categorizeResources();
    
    const {
      title = 'PandaPayroll',
      description = 'Modern payroll management system',
      scripts = [],
      stylesheets = []
    } = options;
    
    let html = `
<!-- Optimized Head Section for Performance -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="${description}">

${this.generateResourceHints()}

<!-- Critical CSS (inlined) -->
<style>
  /* Critical CSS will be injected here by the middleware */
</style>

${this.generateOptimizedCSSLoading(stylesheets)}

<!-- Critical JavaScript -->
<script>
  // Performance monitoring
  performance.mark('head-start');
  
  // Early error handling
  window.addEventListener('error', function(e) {
    console.error('Early error:', e.error);
  });
  
  // Loading state management
  document.documentElement.classList.add('loading');
</script>

${this.generateOptimizedScriptLoading(scripts)}

${this.generateServiceWorkerRegistration()}
`;

    return html;
  }
}

module.exports = new ResourceOptimizer();
