const moment = require("moment-timezone");

/**
 * Payroll Period Business Rule Engine
 * 
 * This engine enforces strict business rules for payroll period calculation
 * based on employee DOA and pay frequency settings, ensuring consistent
 * results regardless of server timezone or environment.
 */

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

/**
 * Calculate the first payroll period end date based on employee DOA and pay frequency
 * This is the foundation for all subsequent period calculations
 * 
 * @param {Date|string} employeeDOA - Employee's date of appointment
 * @param {Object} payFrequency - Pay frequency configuration
 * @returns {Date} - First period end date (always in UTC for consistency)
 */
function calculateFirstPeriodEndDate(employeeDOA, payFrequency) {
  // Parse DOA as UTC to avoid timezone interpretation issues
  const doa = moment.utc(employeeDOA).startOf('day');
  
  console.log("🔧 Calculating first period end date:", {
    doa: doa.format('YYYY-MM-DD'),
    frequency: payFrequency.frequency,
    lastDayOfPeriod: payFrequency.lastDayOfPeriod
  });

  switch (payFrequency.frequency.toLowerCase()) {
    case "monthly":
      return calculateMonthlyFirstPeriod(doa, payFrequency);
    
    case "weekly":
      return calculateWeeklyFirstPeriod(doa, payFrequency);
    
    case "bi-weekly":
    case "biweekly":
      return calculateBiWeeklyFirstPeriod(doa, payFrequency);
    
    default:
      throw new Error(`Unsupported pay frequency: ${payFrequency.frequency}`);
  }
}

/**
 * Calculate first period end date for monthly pay frequency
 */
function calculateMonthlyFirstPeriod(doa, payFrequency) {
  if (payFrequency.lastDayOfPeriod === "monthend") {
    // For monthend: first period ends at the end of DOA month
    const firstPeriodEnd = doa.clone().endOf('month').endOf('day');
    
    console.log("📅 Monthly monthend calculation:", {
      doa: doa.format('YYYY-MM-DD'),
      firstPeriodEnd: firstPeriodEnd.format('YYYY-MM-DD'),
      rule: "End of DOA month"
    });
    
    return firstPeriodEnd.toDate();
  } else {
    // For specific day (e.g., 25th): find the target day in DOA month or next month
    const targetDay = parseInt(payFrequency.lastDayOfPeriod);
    let firstPeriodEnd = doa.clone().date(targetDay).endOf('day');
    
    // If DOA is after the target day, move to next month's target day
    if (doa.date() > targetDay) {
      firstPeriodEnd = doa.clone().add(1, 'month').date(targetDay).endOf('day');
    }
    
    console.log("📅 Monthly specific day calculation:", {
      doa: doa.format('YYYY-MM-DD'),
      targetDay: targetDay,
      firstPeriodEnd: firstPeriodEnd.format('YYYY-MM-DD'),
      rule: `Target day ${targetDay} of month`
    });
    
    return firstPeriodEnd.toDate();
  }
}

/**
 * Calculate first period end date for weekly pay frequency
 */
function calculateWeeklyFirstPeriod(doa, payFrequency) {
  // Map day names to numbers (0 = Sunday, 6 = Saturday)
  const daysOfWeek = {
    sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
    thursday: 4, friday: 5, saturday: 6
  };

  const targetDayNum = daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
  const doaDayNum = doa.day();

  // Calculate days to add to reach the next target day
  let daysToAdd = (targetDayNum - doaDayNum + 7) % 7;

  let firstPeriodEnd;
  if (daysToAdd === 0) {
    // DOA is already on the target day
    firstPeriodEnd = doa.clone().endOf('day');
  } else {
    // Go to the next occurrence of the target day
    firstPeriodEnd = doa.clone().add(daysToAdd, 'days').endOf('day');
  }

  console.log("📅 Weekly calculation:", {
    doa: doa.format('YYYY-MM-DD dddd'),
    targetDay: payFrequency.lastDayOfPeriod,
    daysToAdd: daysToAdd,
    firstPeriodEnd: firstPeriodEnd.format('YYYY-MM-DD dddd'),
    rule: `First ${payFrequency.lastDayOfPeriod} after DOA`
  });

  return firstPeriodEnd.toDate();
}

/**
 * Calculate first period end date for bi-weekly pay frequency
 */
function calculateBiWeeklyFirstPeriod(doa, payFrequency) {
  // For bi-weekly, first period is 14 days from DOA
  const firstPeriodEnd = doa.clone().add(13, 'days').endOf('day');
  
  console.log("📅 Bi-weekly calculation:", {
    doa: doa.format('YYYY-MM-DD'),
    firstPeriodEnd: firstPeriodEnd.format('YYYY-MM-DD'),
    rule: "DOA + 13 days"
  });
  
  return firstPeriodEnd.toDate();
}

/**
 * Generate all payroll periods from first period end date to current date
 * 
 * @param {Date} firstPeriodEndDate - The calculated first period end date
 * @param {Object} payFrequency - Pay frequency configuration
 * @param {Date} currentDate - Current date (defaults to today)
 * @returns {Array} - Array of period objects with startDate and endDate
 */
function generatePayrollPeriods(firstPeriodEndDate, payFrequency, currentDate = new Date()) {
  const periods = [];
  const firstEnd = moment.utc(firstPeriodEndDate);
  const today = moment.utc(currentDate);
  
  // Start with the first period
  let currentPeriodEnd = firstEnd.clone();

  // Generate periods until we reach today + 1 future period
  while (currentPeriodEnd.isSameOrBefore(today.clone().add(1, 'month'))) {
    const periodStart = periods.length === 0
      ? calculatePeriodStart(currentPeriodEnd, payFrequency)
      : moment.utc(periods[periods.length - 1].endDate).add(1, 'day').startOf('day');
    
    periods.push({
      startDate: periodStart.toDate(),
      endDate: currentPeriodEnd.toDate(),
      isFinalized: false,
      isPartial: false
    });
    
    // Calculate next period end date
    currentPeriodEnd = calculateNextPeriodEndDate(currentPeriodEnd, payFrequency);
  }
  
  console.log("📊 Generated periods:", {
    totalPeriods: periods.length,
    firstPeriod: periods[0] ? {
      start: moment.utc(periods[0].startDate).format('YYYY-MM-DD'),
      end: moment.utc(periods[0].endDate).format('YYYY-MM-DD')
    } : null,
    lastPeriod: periods[periods.length - 1] ? {
      start: moment.utc(periods[periods.length - 1].startDate).format('YYYY-MM-DD'),
      end: moment.utc(periods[periods.length - 1].endDate).format('YYYY-MM-DD')
    } : null
  });
  
  return periods;
}

/**
 * Calculate the start date for a period based on its end date
 */
function calculatePeriodStart(periodEndDate, payFrequency) {
  const endDate = moment.utc(periodEndDate);
  
  switch (payFrequency.frequency.toLowerCase()) {
    case "weekly":
      return endDate.clone().subtract(6, 'days').startOf('day');
    
    case "bi-weekly":
    case "biweekly":
      return endDate.clone().subtract(13, 'days').startOf('day');
    
    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        return endDate.clone().startOf('month').startOf('day');
      } else {
        // For specific day periods, calculate the start based on the previous period end
        const targetDay = parseInt(payFrequency.lastDayOfPeriod);
        const prevMonth = endDate.clone().subtract(1, 'month');
        return prevMonth.date(targetDay).add(1, 'day').startOf('day');
      }
    
    default:
      return endDate.clone().startOf('month').startOf('day');
  }
}

/**
 * Calculate the next period end date based on current period end date
 */
function calculateNextPeriodEndDate(currentPeriodEnd, payFrequency) {
  const currentEnd = moment.utc(currentPeriodEnd);
  
  switch (payFrequency.frequency.toLowerCase()) {
    case "weekly":
      return currentEnd.clone().add(1, 'week').endOf('day');
    
    case "bi-weekly":
    case "biweekly":
      return currentEnd.clone().add(2, 'weeks').endOf('day');
    
    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        return currentEnd.clone().add(1, 'month').endOf('month').endOf('day');
      } else {
        const targetDay = parseInt(payFrequency.lastDayOfPeriod);
        return currentEnd.clone().add(1, 'month').date(targetDay).endOf('day');
      }
    
    default:
      return currentEnd.clone().add(1, 'month').endOf('month').endOf('day');
  }
}

/**
 * Validate that a period follows the business rules
 */
function validatePeriod(period, payFrequency, employeeDOA) {
  const endDate = moment.utc(period.endDate);
  const doa = moment.utc(employeeDOA);
  
  // Period end date must be after DOA
  if (endDate.isBefore(doa)) {
    return { isValid: false, error: "Period end date cannot be before employee DOA" };
  }
  
  // Validate based on pay frequency rules
  switch (payFrequency.frequency.toLowerCase()) {
    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        if (!endDate.isSame(endDate.clone().endOf('month'), 'day')) {
          return { isValid: false, error: "Monthly monthend period must end on last day of month" };
        }
      } else {
        const targetDay = parseInt(payFrequency.lastDayOfPeriod);
        if (endDate.date() !== targetDay) {
          return { isValid: false, error: `Monthly period must end on day ${targetDay}` };
        }
      }
      break;
    
    case "weekly":
      const expectedDayName = payFrequency.lastDayOfPeriod.toLowerCase();
      const actualDayName = endDate.format('dddd').toLowerCase();
      if (actualDayName !== expectedDayName) {
        return { isValid: false, error: `Weekly period must end on ${expectedDayName}` };
      }
      break;
  }
  
  return { isValid: true };
}

module.exports = {
  calculateFirstPeriodEndDate,
  generatePayrollPeriods,
  calculateNextPeriodEndDate,
  validatePeriod,
  calculatePeriodStart
};
