const moment = require('moment');

// Helper functions for padding
const padLeft = (str, length, char = ' ') => {
  return (str + '').padStart(length, char);
};

const padRight = (str, length, char = ' ') => {
  return (str + '').padEnd(length, char);
};

// Format account numbers and branch codes
const formatBranchCode = (code) => {
  if (!code) throw new Error('Branch code is required');
  const formatted = code.replace(/\D/g, '');
  if (formatted.length < 6) throw new Error('Branch code must be at least 6 digits');
  return padLeft(formatted, 6, '0');
};

const formatAccountNumber = (number) => {
  if (!number) throw new Error('Account number is required');
  const formatted = number.replace(/\D/g, '');
  if (formatted.length < 6) throw new Error('Account number must be at least 6 digits');
  return padLeft(formatted, 11, '0');
};

// Format employee name for reference
const formatEmployeeName = (name) => {
  if (!name) throw new Error('Account holder name is required');
  return name.replace(/[^a-zA-Z0-9\s]/g, '').substring(0, 20).trim();
};

// Validate bank details
const validateBankDetails = (employee) => {
  if (!employee) throw new Error('Employee details are required');
  
  const { bank, accountNumber, branchCode, accountType, firstName, lastName } = employee;
  
  if (!bank) throw new Error('Bank name is required');
  if (!accountNumber) throw new Error('Account number is required');
  if (!branchCode) throw new Error('Branch code is required');
  if (!accountType) throw new Error('Account type is required');
  
  return true;
};

// Function to generate FNB bank file
const generateFNBBankFile = async (payRun, eftSettings, actionDate, employerDetails) => {
  console.log('\n=== Bank File Generation Start ===');
  
  if (!payRun || !payRun.payrollPeriods) {
    throw new Error('Invalid pay run data');
  }

  if (!eftSettings || !eftSettings.bankName || !eftSettings.branchCode || !eftSettings.accountNumber || !eftSettings.accountHolder) {
    throw new Error('Invalid EFT settings');
  }

  console.log('Input parameters:', {
    payRunId: payRun._id,
    payRunType: payRun.payRunType,
    sequence: payRun.sequence,
    payRunStatus: payRun.status,
    hasPayrollPeriods: Array.isArray(payRun.payrollPeriods),
    payrollPeriodsCount: payRun.payrollPeriods?.length || 0,
    eftSettings: {
      bankName: eftSettings.bankName,
      branchCode: eftSettings.branchCode,
      accountNumber: eftSettings.accountNumber,
      accountHolder: eftSettings.accountHolder
    },
    actionDate,
    employerName: employerDetails?.tradingName || 'Unknown'
  });

  try {
    const bankFileContent = [];
    let totalAmount = 0;
    let recordCount = 0;
    const missingBankDetails = [];

    // Format company bank details
    const formattedBranchCode = formatBranchCode(eftSettings.branchCode);
    const formattedAccountNumber = formatAccountNumber(eftSettings.accountNumber);

    console.log('\n=== Company Bank Details ===');
    console.log({
      original: {
        branchCode: eftSettings.branchCode,
        accountNumber: eftSettings.accountNumber
      },
      formatted: {
        branchCode: formattedBranchCode,
        accountNumber: formattedAccountNumber
      }
    });

    // Get formatted dates
    const today = moment().format('YYMMDD');
    const formattedActionDate = actionDate ? moment(actionDate).format('YYMMDD') : today;

    console.log('\n=== Dates ===');
    console.log({
      today,
      actionDate,
      formattedActionDate
    });

    // Header Record (Type 02)
    const headerRecord = padRight('02' + padLeft('0', 196, '0'), 198, '0');
    bankFileContent.push(headerRecord);

    // User Header Record (Type 04)
    const userHeaderRecord = padRight('04' + padLeft('0', 196, '0'), 198, '0');
    bankFileContent.push(userHeaderRecord);

    // Transaction Records (Type 10) - One for each record
    let sequenceNumber = 1;
    
    console.log('\n=== Processing Transaction Records ===');
    console.log('Number of records to process:', payRun.payrollPeriods?.length || 0);
    
    const records = payRun.payrollPeriods || [];
    
    if (!records || !Array.isArray(records)) {
      throw new Error('No valid records found for processing');
    }

    for (const record of records) {
      console.log('\nProcessing record:', {
        employeeName: `${record.employee?.firstName} ${record.employee?.lastName}`,
        amount: record.netPay
      });

      try {
        validateBankDetails(record.employee);
        
        const { bank, accountNumber, branchCode, accountType, firstName, lastName } = record.employee;
        
        // Format bank details - DO NOT pad these values, use them as is
        const formattedEmployeeAccountNumber = accountNumber.replace(/\D/g, '');
        const formattedEmployeeBranchCode = branchCode.replace(/\D/g, '');
        const formattedName = formatEmployeeName(`${firstName} ${lastName}`);
        
        // Format amount - ensure 2 decimal places and multiply by 100
        const amountInCents = Math.round(parseFloat(record.netPay) * 100);
        const formattedAmountInCents = padLeft(amountInCents.toString(), 11, '0');

        console.log('Processing record details:', {
          employeeName: formattedName,
          branchCode: formattedEmployeeBranchCode,
          accountNumber: formattedEmployeeAccountNumber,
          originalAmount: record.netPay,
          amountInCents: amountInCents,
          formattedAmount: formattedAmountInCents
        });

        // Create fixed-width transaction record
        const createFixedWidthRecord = () => {
            // Create a buffer of 198 spaces
            let recordBuffer = new Array(198).fill(' ');

            // Helper to place string at exact position with debug logging
            const placeAt = (str, startPos, length = null) => {
                // If length is specified, pad with spaces, otherwise use as is
                const value = length ? str.substring(0, length).padEnd(length, ' ') : str;
                console.log(`Placing field at position ${startPos}:`, {
                    field: str,
                    length: value.length,
                    paddedValue: value,
                    startPos: startPos
                });
                const chars = value.split('');
                chars.forEach((char, i) => {
                    if (startPos + i < recordBuffer.length) {
                        recordBuffer[startPos + i] = char;
                    }
                });
            };

            // Place each field at its exact starting position
            placeAt('10', 0);                              // Record type: starts at 0 (2)
            placeAt(formattedBranchCode, 2);              // Company branch: starts at 2 (6)
            placeAt(formattedAccountNumber, 8);           // Company account: starts at 8 (11)
            placeAt('120', 19);                           // Fixed value: starts at 19 (3)
            placeAt('*********', 22);                     // Zeros: starts at 22 (9)
            placeAt(padLeft(sequenceNumber.toString(), 4, '0'), 31); // Sequence: starts at 31 (4)
            placeAt(formattedEmployeeAccountNumber, 35);  // Employee account: starts at 35 (11)
            placeAt(formattedEmployeeBranchCode, 46);     // Employee branch: starts at 46 (6)
            placeAt(formattedAmountInCents, 52);         // Amount in cents: starts at 52 (11)
            placeAt('*********000', 63);                  // Fixed zeros: starts at 63 (12)
            placeAt(formattedName, 75, 30);              // Account holder name: starts at 75 (30)
            placeAt(formattedName.substring(0, 10), 105, 10);  // Own Reference: starts at 105 (10)
            placeAt('Panda Tech', 115, 10);              // Recipient Reference: starts at 115 (10)
            
            // Fill remaining space with zeros (positions 125-197)
            for (let i = 125; i < 198; i++) {
                recordBuffer[i] = '0';
            }

            // Debug: Show exact content at each position
            console.log('\nField Contents:');
            console.log('Branch Code:', recordBuffer.slice(46, 52).join(''));
            console.log('Amount:', recordBuffer.slice(52, 63).join(''));
            console.log('Recipient Reference:', recordBuffer.slice(115, 125).join(''));

            return recordBuffer.join('');
        };

        // Create and validate the transaction record
        const transactionRecord = createFixedWidthRecord();

        bankFileContent.push(transactionRecord);
        totalAmount += record.netPay;
        recordCount++;
        sequenceNumber++;
      } catch (error) {
        console.error('Error processing record:', error);
        missingBankDetails.push({
          employeeName: record.employee ? `${record.employee.firstName} ${record.employee.lastName}` : 'Unknown Employee',
          error: error.message
        });
      }
    }

    if (missingBankDetails.length > 0) {
      throw new Error(JSON.stringify({
        type: 'MISSING_BANK_DETAILS',
        message: 'Some employees are missing bank details',
        details: missingBankDetails
      }));
    }

    if (recordCount === 0) {
      throw new Error('No valid records to process');
    }

    // Contra Record (Type 12)
    const contraRecord = [
      '12',                                    // Record type
      padRight('0', 196, '0')                 // All zeros for the rest
    ].join('');

    bankFileContent.push(contraRecord);

    // Trailer Record (Type 92)
    const trailerRecord = [
      '92',                                    // Record type
      padRight('0', 196, '0')                 // All zeros for the rest
    ].join('');

    bankFileContent.push(trailerRecord);

    // Final Record (Type 94)
    const finalRecord = [
      '94',                                    // Record type
      padRight('0', 196, '0')                 // All zeros for the rest
    ].join('');

    bankFileContent.push(finalRecord);

    console.log('\n=== Bank File Generation Complete ===');
    console.log('Summary:', {
      recordsProcessed: recordCount,
      totalAmount: totalAmount,
      contentLength: bankFileContent.length
    });

    return bankFileContent.join('\n');
  } catch (error) {
    console.error('Error generating bank file:', error);
    throw error;
  }
};

module.exports = {
  generateFNBBankFile
}; 