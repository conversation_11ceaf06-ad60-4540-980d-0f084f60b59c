const PDFDocument = require("pdfkit");
const path = require("path");
const fs = require("fs");
const EmployerDetails = require("../models/employerDetails");
const moment = require("moment");

async function generatePayslip(employee, month, payroll) {
  return new Promise(async (resolve, reject) => {
    try {
      const doc = new PDFDocument({ size: "A4" });
      const buffers = [];

      doc.on("data", buffers.push.bind(buffers));
      doc.on("end", () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Ensure month is a Date object
      const payrollMonth = moment(month).toDate();

      // Calculate the start and end dates of the payroll period
      const startDate = moment(payrollMonth).startOf("month").toDate();
      const endDate = moment(payrollMonth).endOf("month").toDate();

      // Fetch employer details
      const employerDetails = await EmployerDetails.findOne();

      // Add company logo if it exists
      if (employerDetails && employerDetails.logo) {
        const logoPath = path.join(__dirname, "..", employerDetails.logo);
        if (fs.existsSync(logoPath)) {
          doc.image(logoPath, {
            fit: [45, 45],
            align: "center",
            valign: "top",
          });
        }
      }

      // Set specific Y positions to control the header placement directly
      const headerYPosition = 50;
      const lineYPosition = 160;
      const contentYPosition = headerYPosition + 50;
      const labelXPosition = 50;
      const valueXPosition = 150;
      const lineSpacing = 15;

      // Add header
      doc
        .fontSize(10)
        .text(employerDetails.tradingName || "Company Name", {
          align: "center",
          baseline: "middle",
        })
        .moveTo(50, lineYPosition)
        .lineTo(575, lineYPosition)
        .stroke();

      // Add employee details
      doc
        .fontSize(8)
        .font("Helvetica-Bold")
        .text(`Employee Name:`, labelXPosition, contentYPosition)
        .font("Helvetica")
        .text(
          `${employee.firstName} ${employee.lastName}`,
          valueXPosition,
          contentYPosition
        )
        .font("Helvetica-Bold")
        .text(
          `Date Joined Group:`,
          labelXPosition,
          contentYPosition + lineSpacing
        )
        .font("Helvetica")
        .text(
          `${employee.doa.toISOString().substring(0, 10)}`,
          valueXPosition,
          contentYPosition + lineSpacing
        )
        .font("Helvetica-Bold")
        .text(`Pay Period:`, labelXPosition, contentYPosition + 2 * lineSpacing)
        .font("Helvetica")
        .text(
          `${moment(startDate).format("YYYY-MM-DD")} to ${moment(
            endDate
          ).format("YYYY-MM-DD")}`,
          valueXPosition,
          contentYPosition + 2 * lineSpacing
        )
        .font("Helvetica-Bold")
        .text(`Job Title:`, labelXPosition, contentYPosition + 3 * lineSpacing)
        .font("Helvetica")
        .text(
          `${employee.jobTitle}`,
          valueXPosition,
          contentYPosition + 3 * lineSpacing
        );

      // Add more payslip details here, following the structure from payslip.js
      // This includes sections for Income, Deductions, Employer Contributions, and Tax Credits

      // Add more payslip details here
      doc
        .fontSize(8)
        .font("Helvetica-Bold")
        .text(
          `Basic Salary:`,
          labelXPosition,
          contentYPosition + 4 * lineSpacing
        )
        .font("Helvetica")
        .text(
          formatCurrency(payroll.basicSalary),
          valueXPosition,
          contentYPosition + 4 * lineSpacing
        )
        .font("Helvetica-Bold")
        .text(`Deductions:`, labelXPosition, contentYPosition + 5 * lineSpacing)
        .font("Helvetica")
        .text(
          formatCurrency(payroll.deductions),
          valueXPosition,
          contentYPosition + 5 * lineSpacing
        )
        .font("Helvetica-Bold")
        .text(`Net Pay:`, labelXPosition, contentYPosition + 6 * lineSpacing)
        .font("Helvetica")
        .text(
          formatCurrency(payroll.netPay),
          valueXPosition,
          contentYPosition + 6 * lineSpacing
        );

      // Add more payroll details as needed

      // Get the page height
      const pageHeight = doc.page.height;

      // Position for the Nett Pay text, slightly above the page's bottom edge
      const nettPayYPosition = pageHeight - 80;

      // Add total Nett Pay at the bottom of the page
      doc
        .fontSize(10)
        .font("Helvetica-Bold")
        .text(
          `NETT PAY: ${formatCurrency(calculateNettPay(employee, payroll))}`,
          80,
          nettPayYPosition,
          {
            align: "right",
          }
        );

      doc
        .moveTo(50, 725)
        .lineTo(575, 725)
        .lineWidth(1)
        .stroke()
        .moveTo(50, 705)
        .lineTo(575, 705)
        .lineWidth(1)
        .stroke();

      // Finalize PDF
      doc.end();
    } catch (error) {
      console.error("Error generating payslip:", error);
      reject(error);
    }
  });
}

// Helper functions (implement these based on your payroll calculations)
function formatCurrency(amount) {
  return `R ${parseFloat(amount || 0)
    .toFixed(2)
    .toLocaleString()}`;
}

function calculateNettPay(employee, payroll) {
  // Implement your nett pay calculation logic here
  // Use the payroll data to calculate the nett pay
  return payroll.netPay || 0; // Assuming netPay is stored in the payroll object
}

module.exports = { generatePayslip };
