const OID_ANNUAL_THRESHOLD = 568959; // 2024 tax year threshold

/**
 * Calculates OID earnings for a given set of payroll items
 * @param {Array} payrollItems - Array of payroll items
 * @returns {Object} OID earnings calculation result
 */
function calculateOIDEarnings(payrollItems) {
    const oidEarnings = {
        amount: 0,
        items: []
    };

    // Define which item types are included in OID earnings
    const includedTypes = {
        BASIC_SALARY: true,
        OVERTIME: true,
        ALLOWANCE: true,
        COMMISSION: true
    };

    // Define which specific allowances to exclude (reimbursive allowances)
    const excludedAllowances = [
        'TRAVEL_REIMBURSEMENT',
        'SUBSISTENCE_ALLOWANCE',
        'TOOL_ALLOWANCE'
    ];

    for (const item of payrollItems) {
        if (!includedTypes[item.type]) continue;
        
        // Skip reimbursive allowances
        if (item.type === 'ALLOWANCE' && excludedAllowances.includes(item.code)) {
            continue;
        }

        // Add to OID earnings
        oidEarnings.items.push({
            itemType: item.type.toLowerCase(),
            amount: item.amount,
            description: item.description
        });
        
        oidEarnings.amount += item.amount;
    }

    // Cap at annual threshold (pro-rated for the period)
    const monthlyThreshold = OID_ANNUAL_THRESHOLD / 12;
    if (oidEarnings.amount > monthlyThreshold) {
        oidEarnings.amount = monthlyThreshold;
    }

    return oidEarnings;
}

/**
 * Checks if an employee is eligible for OID
 * @param {Object} employee - Employee object
 * @returns {Object} Eligibility check result
 */
function checkOIDEligibility(employee) {
    if (!employee.oidEligible) {
        return {
            eligible: false,
            reason: employee.oidExemptReason
        };
    }

    // Directors are not eligible
    if (employee.isDirector) {
        return {
            eligible: false,
            reason: 'Employee is a director'
        };
    }

    // Independent contractors are not eligible
    if (employee.employmentType === 'INDEPENDENT_CONTRACTOR') {
        return {
            eligible: false,
            reason: 'Employee is an independent contractor'
        };
    }

    return {
        eligible: true
    };
}

module.exports = {
    calculateOIDEarnings,
    checkOIDEligibility,
    OID_ANNUAL_THRESHOLD
};
