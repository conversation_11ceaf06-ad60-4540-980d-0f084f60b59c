exports.saveSessionPromise = (session) => {
  return new Promise((resolve, reject) => {
    session.save((err) => {
      if (err) reject(err);
      resolve();
    });
  });
};

exports.logSessionState = (req) => {
  console.log("=== Session State ===", {
    id: req.sessionID,
    twoFactorToken: req.session.twoFactorToken,
    twoFactorPending: req.session.twoFactorPending,
    user: req.user ? { id: req.user._id, email: req.user.email } : null,
  });
};
