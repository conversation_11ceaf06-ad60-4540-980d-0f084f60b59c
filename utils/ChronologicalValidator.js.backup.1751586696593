const PayrollPeriod = require("../models/PayrollPeriod");
const moment = require("moment-timezone");
const PeriodCalculations = require("./periodCalculations");

/**
 * ChronologicalValidator - Handles sequential period validation
 * Ensures periods are finalized/unfinalized in chronological order
 */
class ChronologicalValidator {
  /**
   * Validate chronological constraints for finalization
   * (This preserves existing logic - used by existing finalization process)
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Object} Validation result
   */
  static async validateFinalizationOrder(employeeId, company, periodEndDate) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Normalize the period end date to ensure timezone-independent handling
      const periodEndDateStr = PeriodCalculations.normalize(periodEndDate);

      // The PayrollPeriod model automatically converts YYYY-MM-DD strings to T23:59:59.999Z
      // So we need to query using the exact same format that would be stored
      const periodEndDateObj = new Date(periodEndDateStr + 'T23:59:59.999Z');

      console.log("🔍 ChronologicalValidator.validateFinalizationOrder:", {
        employeeId,
        company,
        originalPeriodEndDate: periodEndDate,
        normalizedPeriodEndDate: periodEndDateStr,
        periodEndDateObj: periodEndDateObj.toISOString()
      });

      // Check for previous unfinalized periods (existing logic)
      // Use the start of the target day to ensure we catch all previous periods
      const startOfTargetDay = new Date(periodEndDateStr + 'T00:00:00.000Z');

      const previousUnfinalized = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: { $lt: startOfTargetDay },
        isFinalized: false,
      });

      if (previousUnfinalized) {
        const previousEndDateStr = PeriodCalculations.normalize(previousUnfinalized.endDate);
        validationResult.errors.push(
          `Previous period ending ${moment(previousUnfinalized.endDate).format('DD/MM/YYYY')} must be finalized first`
        );
        validationResult.isValid = false;

        console.log("❌ Previous unfinalized period found:", {
          previousPeriodEndDate: previousEndDateStr,
          currentPeriodEndDate: periodEndDateStr
        });
      } else {
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating finalization order:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Validation error occurred");
      return validationResult;
    }
  }

  /**
   * Validate chronological constraints for unfinalization
   * (NEW - reverse logic for unfinalization)
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Object} Validation result
   */
  static async validateUnfinalizationOrder(employeeId, company, periodEndDate) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Check for newer finalized periods (reverse logic)
      const targetEndDate = new Date(periodEndDate);

      // Find the current period to exclude it from the search
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: targetEndDate,
      });

      if (!currentPeriod) {
        validationResult.errors.push('Period not found');
        validationResult.isValid = false;
        return validationResult;
      }

      const newerFinalized = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        _id: { $ne: currentPeriod._id }, // Exclude the current period itself
        endDate: { $gt: currentPeriod.endDate }, // Use actual period endDate
        isFinalized: true,
      });

      if (newerFinalized) {
        validationResult.errors.push(
          `Cannot unfinalize - newer period ending ${moment(newerFinalized.endDate).format('DD/MM/YYYY')} is finalized. Unfinalize newer periods first.`
        );
        validationResult.isValid = false;
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating unfinalization order:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Validation error occurred");
      return validationResult;
    }
  }

  /**
   * Get the chronological sequence of periods for an employee
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @returns {Array} Sorted array of periods
   */
  static async getPeriodSequence(employeeId, company) {
    try {
      const periods = await PayrollPeriod.find({
        employee: employeeId,
        company: company,
      }).sort({ endDate: 1 });

      return periods.map(period => ({
        _id: period._id,
        startDate: period.startDate,
        endDate: period.endDate,
        isFinalized: period.isFinalized,
        status: period.status,
        sequence: moment(period.endDate).format('YYYY-MM-DD'),
      }));
    } catch (error) {
      console.error("Error getting period sequence:", error);
      return [];
    }
  }

  /**
   * Find periods that would be affected by unfinalization
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Object} Analysis of affected periods
   */
  static async analyzeUnfinalizationImpact(employeeId, company, periodEndDate) {
    try {
      const targetDate = new Date(periodEndDate);
      
      // Find all periods after the target period
      const laterPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company,
        endDate: { $gt: targetDate },
      }).sort({ endDate: 1 });

      // Categorize the impact
      const finalizedLater = laterPeriods.filter(p => p.isFinalized);
      const unfinalizedLater = laterPeriods.filter(p => !p.isFinalized);
      const lockedLater = laterPeriods.filter(p => p.status === "locked");

      return {
        canUnfinalize: finalizedLater.length === 0,
        blockedBy: finalizedLater,
        wouldAffect: unfinalizedLater,
        lockedPeriods: lockedLater,
        totalLaterPeriods: laterPeriods.length,
        analysis: {
          hasBlockingPeriods: finalizedLater.length > 0,
          hasLockedPeriods: lockedLater.length > 0,
          nextBlockingPeriod: finalizedLater[0] || null,
        }
      };
    } catch (error) {
      console.error("Error analyzing unfinalization impact:", error);
      return {
        canUnfinalize: false,
        error: "Analysis failed",
        blockedBy: [],
        wouldAffect: [],
        lockedPeriods: [],
      };
    }
  }

  /**
   * Validate the entire sequence integrity
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @returns {Object} Sequence validation result
   */
  static async validateSequenceIntegrity(employeeId, company) {
    try {
      const periods = await this.getPeriodSequence(employeeId, company);
      const issues = [];
      
      let lastFinalizedIndex = -1;
      let firstUnfinalizedIndex = -1;

      // Find the pattern of finalized/unfinalized periods
      periods.forEach((period, index) => {
        if (period.isFinalized) {
          lastFinalizedIndex = index;
        } else if (firstUnfinalizedIndex === -1) {
          firstUnfinalizedIndex = index;
        }
      });

      // Check for gaps in finalization
      if (lastFinalizedIndex > firstUnfinalizedIndex && firstUnfinalizedIndex !== -1) {
        issues.push("Finalization sequence has gaps - some earlier periods are unfinalized while later periods are finalized");
      }

      return {
        isValid: issues.length === 0,
        issues: issues,
        sequence: periods,
        lastFinalizedIndex: lastFinalizedIndex,
        firstUnfinalizedIndex: firstUnfinalizedIndex,
      };
    } catch (error) {
      console.error("Error validating sequence integrity:", error);
      return {
        isValid: false,
        issues: ["Validation failed"],
        error: error.message,
      };
    }
  }
}

module.exports = ChronologicalValidator;
