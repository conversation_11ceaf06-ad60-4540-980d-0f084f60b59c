/**
 * Utility functions for handling URLs in the application
 */

/**
 * Generates an application URL that is environment-aware
 * In production, uses BASE_URL from env variables
 * In development, uses the protocol and host from the request
 * 
 * @param {Object|null} req - Express request object (needed for dev environment, can be null in production)
 * @param {String} path - The path to append to the base URL
 * @returns {String} The complete URL
 */
const getAppUrl = (req, path) => {
  // Ensure path starts with a slash
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // If in production, use the BASE_URL from environment variables
  if (process.env.NODE_ENV === 'production') {
    // Remove trailing slash from BASE_URL if it exists
    const baseUrl = process.env.BASE_URL?.endsWith('/') 
      ? process.env.BASE_URL.slice(0, -1) 
      : (process.env.BASE_URL || 'https://payroll.pss-group.co.za');
    
    return `${baseUrl}${normalizedPath}`;
  } 
  
  // In development or test, generate URL from request
  if (!req) {
    // Fallback for development when no request is available
    return `http://localhost:${process.env.PORT || 3000}${normalizedPath}`;
  }
  
  return `${req.protocol}://${req.get("host")}${normalizedPath}`;
};

module.exports = {
  getAppUrl
}; 