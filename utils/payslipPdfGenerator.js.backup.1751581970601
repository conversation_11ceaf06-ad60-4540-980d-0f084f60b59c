const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const fs = require("fs").promises;
const path = require("path");
const moment = require("moment-timezone");
const PayrollService = require("../services/PayrollService");
const { execFile } = require("child_process");
const os = require("os");

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Helper function to format currency safely
const formatCurrency = (amount) => {
  const safeAmount = Number(amount) || 0;
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(safeAmount);
};

function runQpdfEncrypt(inputPath, outputPath, password) {
  return new Promise((resolve, reject) => {
    execFile(
      "qpdf",
      [
        "--encrypt",
        password,
        password,
        "256",
        "--",
        inputPath,
        outputPath,
      ],
      (error, stdout, stderr) => {
        if (error) {
          reject(
            new Error(
              `qpdf encryption failed: ${stderr || error.message}`
            )
          );
        } else {
          resolve();
        }
      }
    );
  });
}

/**
 * Generates a payslip PDF using pdf-lib based on the web route design (routes/payslip.js).
 * If pdfPassword is provided, uses qpdf to password-protect the PDF.
 * @param {object} employee - The employee document (expected to be populated with company and payFrequency).
 * @param {object} payrollPeriod - The payroll period document.
 * @param {object} calculations - The payroll calculations object.
 * @param {string} [pdfPassword] - Optional password to protect the PDF (employee ID or passport number).
 * @returns {Promise<Buffer>} - A promise that resolves with the PDF buffer.
 */
async function generatePayslipPdfLib(employee, payrollPeriod, calculations, pdfPassword) {
  try {
    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
    const { width, height } = page.getSize();

    // Load fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Helper functions (matching the route)
    const drawText = (text, x, y, options = {}) => {
      const {
        font = helveticaFont,
        size = 8,
        color = rgb(0, 0, 0),
        align = "left",
      } = options;
      if (typeof text === "number") {
        text = text.toLocaleString("en-ZA", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      }
      const textWidth = font.widthOfTextAtSize(text.toString(), size);
      const textX =
        align === "center"
          ? x - textWidth / 2
          : align === "right"
          ? x - textWidth
          : x;
      page.drawText(text.toString(), { x: textX, y, font, size, color });
    };
    const drawTextWithSpace = (label, amount, x, y, options = {}) => {
      drawText(label, x, y, options);
      drawText(formatCurrency(amount), x + 180, y, {
        ...options,
        align: "right",
      });
    };

    // Destructure all needed values - Fixed data structure mapping
    const {
      totalIncome = calculations?.totalIncome || calculations?.grossIncome || 0,
      basicSalary = calculations?.basicSalary || 0,
      deductions: {
        statutory: { paye = 0, uif = 0 } = {},
        courtOrders: { maintenanceOrder = 0 } = {},
        total: totalDeductions = 0,
      } = {},
      netPay = calculations?.netPay || 0,
    } = calculations || {};

    // Extract basic salary value properly (handle object structure)
    const basicSalaryValue = typeof basicSalary === 'object'
      ? (basicSalary.full || basicSalary.prorated || 0)
      : (basicSalary || 0);

    // Ensure totalIncome is never undefined - add fallback
    const safeTotalIncome = totalIncome || basicSalaryValue || 0;

    // Calculate SDL (1% of total income) with safe value
    const sdl = safeTotalIncome * 0.01;

    // Calculate medical aid credit
    let medicalAidCredit = 0;
    if (employee?.payFrequency?.frequency) {
      const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
        payrollPeriod?.medical || {},
        employee.payFrequency.frequency,
        totalIncome
      );
      medicalAidCredit = medicalAidImpact?.taxCredit || 0;
    }

    // Format dates
    const formattedStartDate = moment(payrollPeriod?.startDate || new Date()).format("DD/MM/YYYY");
    const formattedEndDate = moment(payrollPeriod?.endDate || new Date()).format("DD/MM/YYYY");
    const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;

    // Add DRAFT watermark if period is not finalized
    if (!payrollPeriod?.isFinalized) {
      const draftText = "DRAFT";
      const fontSize = 60;
      const draftWidth = helveticaBoldFont.widthOfTextAtSize(draftText, fontSize);
      page.drawText(draftText, {
        x: (width - draftWidth) / 2,
        y: height / 2,
        font: helveticaBoldFont,
        size: fontSize,
        color: rgb(0.9, 0.3, 0.3),
        opacity: 0.3,
        rotate: {
          type: "degrees",
          angle: 45,
          origin: {
            x: width / 2,
            y: height / 2,
          },
        },
      });
      const noticeText = "This is a draft payslip - Not finalized";
      page.drawText(noticeText, {
        x: 50,
        y: height - 30,
        font: helveticaBoldFont,
        size: 10,
        color: rgb(0.9, 0.3, 0.3),
      });
    }

    // Add logo if it exists
    let companyLogo = employee?.company?.employerDetails?.logo;
    if (companyLogo) {
      try {
        const logoPath = path.join(__dirname, "..", companyLogo);
        const logoFile = await fs.readFile(logoPath);
        const logoImage = await pdfDoc.embedPng(logoFile);
        const logoWidth = 100;
        const logoHeight = (logoWidth / logoImage.width) * logoImage.height;
        page.drawImage(logoImage, {
          x: 50,
          y: height - 20 - logoHeight,
          width: logoWidth,
          height: logoHeight,
        });
      } catch (error) {
        console.error("Error loading logo:", error);
      }
    }

    // Company name (centered, 8pt)
    const companyName = employee?.company?.employerDetails?.tradingName || employee?.company?.name || "Company Name";
    drawText(companyName, width / 2, height - 50, { font: helveticaBoldFont, size: 8, align: "center" });

    // Company address (right-aligned)
    const addressY = height - 100;
    const cityTown = employee?.company?.employerDetails?.physicalAddress?.cityTown || "City";
    const postalCode = employee?.company?.employerDetails?.physicalAddress?.code || "Postal Code";
    drawText(cityTown, width - 50, addressY, { align: "right" });
    drawText(postalCode, width - 50, addressY - 15, { align: "right" });

    // Update the period display based on frequency
    drawText(`Period: ${periodDisplay}`, 50, height - 145, {
      font: helveticaBoldFont,
      size: 8,
    });

    // Update employee details section with period-specific information
    const startY = height - 100;
    drawText(`Employee Name: ${employee?.firstName || ""} ${employee?.lastName || ""}`.trim(), 50, startY);
    drawText(`Pay Frequency: ${employee?.payFrequency?.frequency ? employee.payFrequency.frequency.charAt(0).toUpperCase() + employee.payFrequency.frequency.slice(1) : "Not specified"}`, 50, startY - 15);
    const employmentDate = employee?.doa ? moment(employee.doa).format("DD/MM/YYYY") : "Not specified";
    drawText(`Employment Date: ${employmentDate}`, 50, startY - 30);

    // Horizontal line under employee details
    const lineY = startY - 50;
    page.drawLine({
      start: { x: 50, y: lineY },
      end: { x: width - 50, y: lineY },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Create a table for income, deductions, and employer contributions
    const tableStartY = lineY - 20;
    const lineHeight = 20;
    const tableCol1 = 50;
    const tableCol2 = 200;
    const tableCol3 = 350;
    const tableCol4 = 450;

    // Headers (all bold headers now 8pt)
    drawText("Income", tableCol1, tableStartY, { font: helveticaBoldFont, size: 8 });
    drawText("Employer Contribution", tableCol3, tableStartY, { font: helveticaBoldFont, size: 8 });

    // Income
    drawText("Basic Salary", tableCol1, tableStartY - lineHeight);
    drawTextWithSpace("Basic Salary", totalIncome, tableCol1, tableStartY - lineHeight);
    drawText(formatCurrency(totalIncome), tableCol2 - 8, tableStartY - 2 * lineHeight, { font: helveticaBoldFont, size: 8, align: "right" });

    // Deductions
    drawText("Deduction", tableCol1, tableStartY - 3 * lineHeight, { font: helveticaBoldFont, size: 8 });
    drawText("Medical Aid", tableCol1, tableStartY - 4 * lineHeight);
    drawText(formatCurrency(payrollPeriod?.medical?.medicalAid || 0), tableCol2 - 8, tableStartY - 4 * lineHeight, { align: "right" });
    drawText("UIF", tableCol1, tableStartY - 5 * lineHeight);
    drawTextWithSpace("UIF", uif, tableCol1, tableStartY - 5 * lineHeight);
    drawText("PAYE", tableCol1, tableStartY - 6 * lineHeight);
    drawTextWithSpace("PAYE", paye, tableCol1, tableStartY - 6 * lineHeight);
    drawText("Total Deductions:", tableCol1, tableStartY - 7 * lineHeight, { font: helveticaBoldFont, size: 8 });
    drawTextWithSpace("Total Deductions:", totalDeductions, tableCol1, tableStartY - 7 * lineHeight, { font: helveticaBoldFont, size: 8 });

    // Employer Contributions
    drawText("Employer Contribution", tableCol3, tableStartY, { font: helveticaBoldFont, size: 8 });
    drawTextWithSpace("SDL", sdl, tableCol3, tableStartY - lineHeight);
    drawTextWithSpace("UIF - Employer", uif, tableCol3, tableStartY - 2 * lineHeight);
    drawText("Tax Credit", tableCol3, tableStartY - 4 * lineHeight, { font: helveticaBoldFont, size: 8 });
    drawTextWithSpace("Medical Aid Tax Credit", medicalAidCredit, tableCol3, tableStartY - 5 * lineHeight);

    // NETT PAY (at the bottom)
    const nettPayY = 50;
    page.drawLine({
      start: { x: 50, y: nettPayY + 25 },
      end: { x: 545, y: nettPayY + 25 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });
    drawText("NETT PAY", 50, nettPayY, { font: helveticaBoldFont, size: 8 });
    drawTextWithSpace("NETT PAY", netPay, 50, nettPayY, { font: helveticaBoldFont, size: 8 });
    page.drawLine({
      start: { x: 50, y: nettPayY - 5 },
      end: { x: 545, y: nettPayY - 5 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Add finalization status to the footer
    const statusText = payrollPeriod?.isFinalized ? "FINALIZED PAYSLIP" : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";
    drawText(statusText, width / 2, 30, {
      font: helveticaBoldFont,
      size: 8,
      color: payrollPeriod?.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
      align: "center",
    });

    // Serialize the PDF
    let pdfBytes = await pdfDoc.save();

    // If no password, return the buffer as before
    if (!pdfPassword) {
      return Buffer.from(pdfBytes);
    }

    // If password is set, use qpdf to encrypt the PDF
    // Write the unprotected PDF to a temp file
    const tmpDir = os.tmpdir();
    const inputPath = path.join(tmpDir, `payslip_unprotected_${Date.now()}_${Math.random().toString(36).slice(2)}.pdf`);
    const outputPath = path.join(tmpDir, `payslip_protected_${Date.now()}_${Math.random().toString(36).slice(2)}.pdf`);
    await fs.writeFile(inputPath, pdfBytes);

    try {
      await runQpdfEncrypt(inputPath, outputPath, pdfPassword);
      const protectedBuffer = await fs.readFile(outputPath);
      // Clean up temp files
      await fs.unlink(inputPath).catch(() => {});
      await fs.unlink(outputPath).catch(() => {});
      return protectedBuffer;
    } catch (err) {
      // Clean up temp files on error
      await fs.unlink(inputPath).catch(() => {});
      await fs.unlink(outputPath).catch(() => {});
      throw err;
    }
  } catch (error) {
    console.error("Error generating payslip PDF via utility:", error);
    throw error;
  }
}

module.exports = generatePayslipPdfLib; 