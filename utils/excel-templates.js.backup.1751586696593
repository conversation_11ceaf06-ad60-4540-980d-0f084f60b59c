const ExcelJS = require("exceljs");

class EmployeeTemplateGenerator {
  async generateTemplate() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Employee Upload Template");

    // Define columns with validation
    worksheet.columns = [
      { header: "First Name *", key: "firstName", width: 15 },
      { header: "Last Name *", key: "lastName", width: 15 },
      { header: "Date of Birth *", key: "dateOfBirth", width: 15 },
      { header: "Date of Appointment *", key: "dateOfAppointment", width: 15 },
      { header: "ID Type *", key: "idType", width: 15 },
      { header: "ID Number", key: "idNumber", width: 20 }, // Optional based on ID Type
      { header: "Payment Method *", key: "paymentMethod", width: 15 },
      // Bank details - only required if Payment Method is EFT
      { header: "Bank Name", key: "bankName", width: 20 },
      { header: "Account Type", key: "accountType", width: 15 },
      { header: "Account Number", key: "accountNumber", width: 20 },
      { header: "Branch Code", key: "branchCode", width: 15 },
      { header: "Account Holder Name", key: "accountHolderName", width: 25 },
      { header: "Pay Frequency *", key: "payFrequency", width: 15 },
      { header: "Basic Salary", key: "basicSalary", width: 15 },
      { header: "Email", key: "email", width: 25 },
    ];

    // Add header row styling
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Highlight required fields in yellow
    const requiredColumns = ["A", "B", "C", "D", "E", "G", "M"];
    requiredColumns.forEach((col) => {
      worksheet.getCell(`${col}1`).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFFFF00" },
      };
    });

    // Add ID Type validation
    worksheet.dataValidations.add("E2:E1000", {
      type: "list",
      allowBlank: false,
      formulae: ['"South African ID,Passport,None"'],
      showErrorMessage: true,
      errorStyle: "error",
      errorTitle: "Invalid ID Type",
      error: "Please select: South African ID, Passport, or None",
      promptTitle: "ID Type",
      prompt: "South African ID = RSA ID Number\nPassport = Passport Number\nNone = Other ID Types"
    });

    // Add Payment Method validation
    worksheet.dataValidations.add("G2:G1000", {
      type: "list",
      allowBlank: false,
      formulae: ['"EFT,Cash"'],
      showErrorMessage: true,
      errorStyle: "error",
      errorTitle: "Invalid Payment Method",
      error: "Please select EFT or Cash",
    });

    // Add Account Type validation (only if Payment Method is EFT)
    worksheet.dataValidations.add("I2:I1000", {
      type: "list",
      allowBlank: true,
      formulae: ['"cheque,savings,transmission,other"'],
      showErrorMessage: true,
      errorStyle: "error",
      errorTitle: "Invalid Account Type",
      error: "Please select: cheque, savings, transmission, or other",
    });

    // Add Pay Frequency validation
    worksheet.dataValidations.add("M2:M1000", {
      type: "list",
      allowBlank: false,
      formulae: ['"Monthly Month End,Monthly 25th,Weekly Friday,Bi-Weekly 15th and Month End"'],
      showErrorMessage: true,
      errorStyle: "error",
      errorTitle: "Invalid Pay Frequency",
      error: "Please select a valid pay frequency",
      promptTitle: "Pay Frequency",
      prompt: "Choose from Monthly, Weekly, or Bi-Weekly pay frequencies"
    });

    // Add date validation for DOB and DOA
    ["C", "D"].forEach((col) => {
      worksheet.dataValidations.add(`${col}2:${col}1000`, {
        type: "date",
        operator: "between",
        showErrorMessage: true,
        errorStyle: "error",
        errorTitle: "Invalid Date",
        error: "Please enter a valid date",
        promptTitle: "Date Format",
        prompt: "Enter date in dd/mm/yyyy format"
      });

      // Set proper number format for date columns
      worksheet.getColumn(col).numFmt = "dd/mm/yyyy";
    });

    // Add sample data row
    const sampleRow = worksheet.addRow({
      firstName: "John",
      lastName: "Smith",
      dateOfBirth: new Date(1990, 0, 15), // January 15, 1990
      dateOfAppointment: new Date(2023, 0, 1), // January 1, 2023
      idType: "South African ID",
      idNumber: "*************",
      paymentMethod: "EFT",
      bankName: "FNB",
      accountType: "cheque",
      accountNumber: "***********",
      branchCode: "250655",
      accountHolderName: "",
      payFrequency: "Monthly Month End",
      basicSalary: 15000,
      email: "<EMAIL>",
    });

    // Format the sample dates as actual Excel dates
    const dobCell = sampleRow.getCell(3);
    const doaCell = sampleRow.getCell(4);
    dobCell.numFmt = "dd/mm/yyyy";
    doaCell.numFmt = "dd/mm/yyyy";

    // Style the sample row
    sampleRow.eachCell((cell) => {
      cell.font = { italic: true, color: { argb: 'FF808080' } };
    });

    // Add instructions sheet with improved guidance
    const instructionsSheet = workbook.addWorksheet("Instructions");
    instructionsSheet.columns = [
      { width: 90 }
    ];
    
    instructionsSheet.addRow(["EMPLOYEE UPLOAD TEMPLATE INSTRUCTIONS"]).font = { bold: true, size: 14 };
    instructionsSheet.addRow([""]);
    instructionsSheet.addRow(["REQUIRED FIELDS"]).font = { bold: true };
    instructionsSheet.addRow(["1. Fields marked with * are required"]);
    instructionsSheet.addRow(["2. ID Number is required only when ID Type is 'South African ID'"]);
    instructionsSheet.addRow(["3. Bank details are required only when Payment Method is 'EFT'"]);
    instructionsSheet.addRow([""]);
    
    instructionsSheet.addRow(["DATE FORMATS"]).font = { bold: true };
    instructionsSheet.addRow(["4. Dates must be in dd/mm/yyyy format (e.g., 15/01/1990 for 15 January 1990)"]);
    instructionsSheet.addRow(["5. If Excel displays dates as numbers, use Format Cells > Date > 14/03/2012 format"]);
    instructionsSheet.addRow(["6. Sample data row shows correct date formatting"]);
    instructionsSheet.addRow([""]);
    
    instructionsSheet.addRow(["PAY FREQUENCY"]).font = { bold: true };
    instructionsSheet.addRow(["7. Pay Frequency must be one of the following:"]);
    instructionsSheet.addRow(["   - Monthly Month End (paid on the last day of each month)"]);
    instructionsSheet.addRow(["   - Monthly 25th (paid on the 25th of each month)"]);
    instructionsSheet.addRow(["   - Weekly Friday (paid every Friday)"]);
    instructionsSheet.addRow(["   - Bi-Weekly 15th and Month End (paid twice a month on the 15th and last day)"]);
    instructionsSheet.addRow(["8. If no Pay Frequency is specified, a company default will be used if available"]);
    instructionsSheet.addRow([""]);
    
    instructionsSheet.addRow(["AUTO-GENERATED FIELDS"]).font = { bold: true };
    instructionsSheet.addRow(["9. The following will be automatically generated:"]);
    instructionsSheet.addRow(["   - Employee Number"]);
    instructionsSheet.addRow(["   - Work Schedule (Monday to Friday)"]);
    instructionsSheet.addRow(["   - Working Hours (Full Time)"]);
    instructionsSheet.addRow([""]);
    
    instructionsSheet.addRow(["TIPS"]).font = { bold: true };
    instructionsSheet.addRow(["10. Basic Salary is optional but recommended for payroll setup"]);
    instructionsSheet.addRow(["11. Email is optional but useful for employee communication"]);
    instructionsSheet.addRow(["12. The first row of data contains sample values - replace or delete this row"]);
    instructionsSheet.addRow(["13. Address fields are optional and can be added later in the system"]);

    return workbook;
  }
}

module.exports = EmployeeTemplateGenerator;
