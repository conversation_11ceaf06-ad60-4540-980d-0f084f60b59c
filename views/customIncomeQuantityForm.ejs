<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= customIncomeItem.name %> - Quantity Input - <%= employee.firstName %> <%= employee.lastName %></title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/regularinputs.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/notifications.css" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .form-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .form-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        }
        
        .form-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .form-header h1 {
            color: #1e293b;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .form-header p {
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }
        
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            border: none;
            font-size: 1rem;
            transition: all 0.2s ease;
        }
        
        .btn-secondary {
            background: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #f1f5f9;
            color: #475569;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5855eb;
        }
        
        .calculation-preview {
            background: rgba(99, 102, 241, 0.02);
            border: 1px solid rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .calculation-preview h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .calculation-preview p {
            margin: 0;
            color: #64748b;
            font-size: 0.875rem;
        }
        
        .calculation-result {
            font-weight: 600;
            color: #6366f1;
            font-size: 1.1rem;
        }
    </style>
</head>

<body>
    <%- include('partials/header') %>
    <nav><%- include('partials/sidebar') %></nav>

    <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h1><%= customIncomeItem.name %> - Quantity Input</h1>
                <p>Enter the quantity for this pay period</p>
            </div>

            <form id="quantityForm" method="POST" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income-quantity/<%= customIncomeItem._id %>">
                <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                <input type="hidden" name="customIncomeId" value="<%= customIncomeItem._id %>" />

                <div class="form-group">
                    <label for="quantity">Quantity *</label>
                    <input type="number" id="quantity" name="quantity" step="0.01" min="0" required 
                           value="<%= existingQuantity || '' %>" />
                    <div class="help-text">Enter the quantity (e.g., hours worked) for this pay period.</div>
                </div>

                <% if (customIncomeItem.customRate && !customIncomeItem.differentRateForEveryEmployee) { %>
                <div class="calculation-preview">
                    <h4>Calculation Preview</h4>
                    <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                    <p>Quantity: <span id="quantityDisplay">0</span></p>
                    <p class="calculation-result">Total: R <span id="totalAmount">0.00</span></p>
                </div>
                <% } %>

                <div class="form-actions">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>" class="btn btn-secondary">
                        <i class="ph ph-arrow-left"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph ph-check"></i>
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const quantityInput = document.getElementById('quantity');
            const quantityDisplay = document.getElementById('quantityDisplay');
            const totalAmountDisplay = document.getElementById('totalAmount');
            const customRate = <%= customIncomeItem.customRate || 0 %>;

            function updateCalculation() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const total = quantity * customRate;
                
                if (quantityDisplay) quantityDisplay.textContent = quantity;
                if (totalAmountDisplay) totalAmountDisplay.textContent = total.toFixed(2);
            }

            if (quantityInput) {
                quantityInput.addEventListener('input', updateCalculation);
                updateCalculation(); // Initial calculation
            }
        });
    </script>

    <%- include('partials/mobile-bottom-nav', { req: req }) %>
</body>
</html>
