<!DOCTYPE html>
<html lang="en">
 <head>
   <meta charset="UTF-8" />
   <meta name="viewport" content="width=device-width, initial-scale=1.0" />
   <title>Login | Panda</title>

   <!-- Favicon -->
   <link rel="icon" type="image/svg+xml" href="/images/pandalogo1.svg" />

   <!-- Combine font imports -->
   <link rel="preconnect" href="https://fonts.googleapis.com" />
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
   <link
     href="https://fonts.googleapis.com/css2?family=Syne:wght@400;500;600;700&display=swap"
     rel="stylesheet"
   />
   <link
     href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Poppins:wght@400;500;600&display=swap"
     rel="stylesheet"
   />

   <!-- External stylesheets -->
   <link
     rel="stylesheet"
     href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"
   />
   <link rel="stylesheet" href="/auth.css" />
   <link rel="stylesheet" href="/css/alerts.css" />

   <style>
     body,
     h1,
     h2,
     p,
     label,
     input,
     button {
       font-family: "Syne", sans-serif;
     }

     .right-side {
       background-color: white;
     }

     .welcome-text h2 {
       font-size: 2.5rem;
       font-weight: 700;
       color: black;
       margin-bottom: 0.5rem;
       font-family: "Syne", sans-serif;
     }

     .welcome-text p {
       color: #666;
       font-family: "Syne", sans-serif;
     }

     /* Button Styles */
     .btn-primary {
       background-color: #4d5dff;
       border-radius: 50px;
       border: none;
       color: white;
       padding: 12px 24px;
       font-weight: 600;
       transition: all 0.3s ease;
     }

     .btn-primary:hover {
       background-color: #3a47c9;
       transform: translateY(-2px);
       box-shadow: 0 4px 6px rgba(77, 93, 255, 0.2);
     }

     .btn-spinner {
       display: none;
       width: 20px;
       height: 20px;
       border: 3px solid #fff;
       border-top: 3px solid #3a47c9;
       border-radius: 50%;
       animation: spin 1s linear infinite;
     }

     .loading .btn-spinner {
       display: inline-block;
       margin-left: 10px;
     }

     @keyframes spin {
       0% {
         transform: rotate(0deg);
       }
       100% {
         transform: rotate(360deg);
       }
     }

     /* Verification Email Styles */
     .verification-notice {
       margin: 20px 0;
       padding: 15px;
       background-color: #f8f9ff;
       border-radius: 8px;
       border-left: 4px solid #4d5dff;
     }

     .verification-notice p {
       margin-bottom: 12px;
       font-size: 14px;
       color: #333;
     }

     .resend-btn {
       background: none;
       border: none;
       color: #4d5dff;
       font-weight: 600;
       font-size: 14px;
       padding: 0;
       cursor: pointer;
       text-decoration: underline;
       transition: color 0.2s ease;
     }

     .resend-btn:hover {
       color: #3a47c9;
     }

     .divider {
       margin: 20px 0;
       border: 0;
       border-top: 1px solid #eaeaea;
     }

     /* Toast Notification Styles */
     .toast-container {
       position: fixed;
       top: 20px;
       right: 20px;
       z-index: 9999;
       max-width: 350px;
     }

     .toast {
       display: flex;
       align-items: flex-start;
       width: 100%;
       background-color: #4d5dff;
       color: white;
       padding: 16px;
       border-radius: 8px;
       margin-bottom: 10px;
       box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
       animation: slideIn 0.3s ease forwards, fadeOut 0.5s ease 4.5s forwards;
       opacity: 0;
       transform: translateX(100%);
       overflow: hidden;
       position: relative;
     }

     .toast::after {
       content: '';
       position: absolute;
       bottom: 0;
       left: 0;
       height: 3px;
       width: 100%;
       background: rgba(255, 255, 255, 0.3);
     }

     .toast::before {
       content: '';
       position: absolute;
       bottom: 0;
       left: 0;
       height: 3px;
       width: 100%;
       background: rgba(255, 255, 255, 0.6);
       animation: countdown 5s linear forwards;
       transform-origin: left center;
     }

     @keyframes countdown {
       to {
         transform: scaleX(0);
       }
     }

     .toast-icon {
       margin-right: 12px;
       flex-shrink: 0;
       margin-top: 2px;
       width: 22px;
       height: 22px;
     }

     .toast-content {
       flex-grow: 1;
       font-size: 14px;
       line-height: 1.4;
     }

     .toast-close {
       background: none;
       border: none;
       color: white;
       font-size: 18px;
       cursor: pointer;
       margin-left: 8px;
       padding: 0;
       opacity: 0.7;
       transition: opacity 0.2s;
     }

     .toast-close:hover {
       opacity: 1;
     }

     .toast-warning {
       background-color: #ff9800;
       border-left: 4px solid #e68200;
     }

     .toast-error {
       background-color: #f44336;
       border-left: 4px solid #d32f2f;
     }

     .toast-success {
       background-color: #4caf50;
       border-left: 4px solid #388e3c;
     }

     .toast-info {
       background-color: #2196f3;
       border-left: 4px solid #1976d2;
     }

     @keyframes slideIn {
       to {
         opacity: 1;
         transform: translateX(0);
       }
     }

     @keyframes fadeOut {
       to {
         opacity: 0;
         transform: translateY(-20px);
       }
     }
   </style>
 </head>
 <body>
   <!-- Toast notification container -->
   <div class="toast-container" id="toastContainer"></div>

   <div class="left-side">
     <div class="geometric-shapes">
       <div class="shape shape-1"></div>
       <div class="shape shape-2"></div>
       <div class="shape shape-3"></div>
       <div class="shape shape-4"></div>
       <div class="shape shape-5"></div>
     </div>
     <div class="brand-message">
       <h1>Welcome to Panda</h1>
       <p>Compensate. Compliance. Calculate.</p>
       <div class="tech-decoration">
         <span class="tech-line"></span>
       </div>
     </div>
   </div>

   <div class="right-side">
     <div class="container">
       <div class="logo-side">
         <img src="/images/pandalogo.svg" alt="Panda Logo" />
       </div>

       <div class="welcome-text">
         <h2>Welcome back</h2>
         <p>Please enter your details to sign in</p>
       </div>

       <% if (locals.errorMessages && errorMessages.length > 0) { %>
       <div class="alert alert-error" role="alert">
         <div class="alert-content">
           <svg
             class="alert-icon"
             xmlns="http://www.w3.org/2000/svg"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"

             <path
               stroke-linecap="round"
               stroke-linejoin="round"
               stroke-width="2"
               d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
             />
           </svg>
           <p><%= errorMessages %></p>
         </div>
         <button class="alert-close" onclick="this.parentElement.remove()">
           ×
         </button>
       </div>
       <% } %> <% if (locals.successMessages && successMessages.length > 0) {
       %>
       <div class="alert alert-success" role="alert">
         <div class="alert-content">
           <svg
             class="alert-icon"
             xmlns="http://www.w3.org/2000/svg"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"

             <path
               stroke-linecap="round"
               stroke-linejoin="round"
               stroke-width="2"
               d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
             />
           </svg>
           <p><%= successMessages %></p>
         </div>
         <button class="alert-close" onclick="this.parentElement.remove()">
           ×
         </button>
       </div>
       <% } %>

       <form id="loginForm" action="/login" class="login-form" method="POST">
         <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

         <div class="form-group">
           <label for="email">Email</label>
           <input
             type="email"
             class="form-control"
             placeholder="Enter your email"
             id="email"
             name="email"
             required
             autocomplete="email"
           />
         </div>

         <div class="form-group password-group">
           <label for="password">Password</label>
           <div class="password-input-wrapper">
             <input
               type="password"
               class="form-control"
               placeholder="Enter your password"
               id="password"
               name="password"
               required
               autocomplete="current-password"
             />
             <button
               type="button"
               class="password-toggle"
               aria-label="Toggle password visibility"

               <svg
                 class="eye-icon eye-show"
                 xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round"

                 <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                 <circle cx="12" cy="12" r="3"></circle>
               </svg>
               <svg
                 class="eye-icon eye-hide"
                 xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round"

                 <path
                   d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"
</path>
                 <line x1="1" y1="1" x2="23" y2="23"></line>
               </svg>
             </button>
           </div>
         </div>

         <button type="submit" class="btn-primary btn-login">
           Sign in
           <div class="btn-spinner"></div>
         </button>

         <div class="form-footer">
           <!-- Resend verification email form -->
           <% if (locals.email && locals.errorMessages && errorMessages.includes('verify your email')) { %>
             <div class="verification-notice">
               <p>Need a new verification email?</p>
               <form action="/auth/resend-verification" method="POST" class="resend-form">
                 <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                 <input type="hidden" name="email" value="<%= email %>" />
                 <button type="submit" class="resend-btn">Resend verification email</button>
               </form>
             </div>
             <hr class="divider" />
           <% } %>

           <span class="signup-prompt">
             Don't have an account?
             <a href="/register" class="signup-link">Sign up</a>
           </span>
           <a href="/forgot_password" class="forgot-password">
             Forgot password?
           </a>
         </div>
       </form>
     </div>
   </div>

   <script src="client.js"></script>
   <script>
     document.addEventListener("DOMContentLoaded", () => {
       // Show toast notification function
       function showToast(message, type = 'info') {
         const toastContainer = document.getElementById('toastContainer');

         // Limit to 3 visible toasts at a time - remove oldest if exceeded
         const existingToasts = toastContainer.querySelectorAll('.toast');
         if (existingToasts.length >= 3) {
           toastContainer.removeChild(existingToasts[0]);
         }

         // Create toast element
         const toast = document.createElement('div');
         toast.className = `toast toast-${type}`;

         // Create icon based on type
         let iconSvg = '';
         if (type === 'warning') {
           iconSvg = '<svg class="toast-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
         } else if (type === 'error') {
           iconSvg = '<svg class="toast-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
         } else if (type === 'success') {
           iconSvg = '<svg class="toast-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
         } else {
           iconSvg = '<svg class="toast-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>';
         }

         // Set toast content with close button
         toast.innerHTML = `
           ${iconSvg}
           <div class="toast-content">${message}</div>
           <button class="toast-close" aria-label="Close notification">×</button>
         `;

         // Add to container
         toastContainer.appendChild(toast);

         // Add click event to close button
         const closeButton = toast.querySelector('.toast-close');
         closeButton.addEventListener('click', () => {
           toast.style.opacity = '0';
           toast.style.transform = 'translateY(-20px)';
           setTimeout(() => toast.remove(), 300);
         });

         // Auto remove after 5 seconds
         const toastTimeout = setTimeout(() => {
           if (toast.parentElement) {
             toast.remove();
           }
         }, 5000);

         // Clear timeout if toast is manually closed
         toast.addEventListener('remove', () => {
           clearTimeout(toastTimeout);
         });

         // Pause animation on hover
         toast.addEventListener('mouseenter', () => {
           toast.style.animationPlayState = 'paused';
         });

         toast.addEventListener('mouseleave', () => {
           toast.style.animationPlayState = 'running';
         });

         return toast;
       }

       // Function to clear all toast notifications
       function clearAllToasts() {
         const toastContainer = document.getElementById('toastContainer');
         const toasts = toastContainer.querySelectorAll('.toast');

         toasts.forEach(toast => {
           toast.style.opacity = '0';
           toast.style.transform = 'translateY(-20px)';
           setTimeout(() => toast.remove(), 300);
         });
       }

       // Get form elements
       const form = document.querySelector(".login-form");
       const emailInput = document.getElementById("email");
       const passwordInput = document.getElementById("password");
       const toggleButton = document.querySelector(".password-toggle");

       // Check if we need to show verification toast message based on URL query params or error message
       const urlParams = new URLSearchParams(window.location.search);
       if (urlParams.get('needsVerification') === 'true') {
         showToast('Please verify your email before logging in. Check your inbox for the verification link.', 'warning');
       }

       // Display appropriate toast notifications based on error messages
       const errorAlert = document.querySelector('.alert-error');
       if (errorAlert) {
         const errorText = errorAlert.textContent.trim();

         if (errorText.includes('verify your email')) {
           showToast('Please verify your email before logging in. Check your inbox for the verification link.', 'warning');
         }
         else if (errorText.includes('Invalid email') || errorText.includes('User not found') || errorText.includes('No account found')) {
           showToast('No account found with this email address. Please check your email or register.', 'error');
         }
         else if (errorText.includes('Invalid password') || errorText.includes('Incorrect password')) {
           showToast('Incorrect password. Please try again or reset your password.', 'error');
         }
         else if (errorText.includes('locked') || errorText.includes('too many failed')) {
           showToast('Your account has been temporarily locked due to too many failed login attempts.', 'error');
         }
         else if (errorText.includes('session')) {
           showToast('Failed to establish session. Please try again later.', 'error');
         }
         else if (errorText.length > 0) {
           // Generic error toast for any other errors
           showToast(errorText, 'error');
         }

         // Hide the standard error alert if we're showing a toast
         errorAlert.style.display = 'none';
       }

       // Also show toast for success messages
       const successAlert = document.querySelector('.alert-success');
       if (successAlert) {
         const successText = successAlert.textContent.trim();
         if (successText.length > 0) {
           showToast(successText, 'success');
           // Hide the standard success alert
           successAlert.style.display = 'none';
         }
       }

       // Only add password toggle if elements exist
       if (toggleButton && passwordInput) {
         toggleButton.addEventListener("click", () => {
           const type =
             passwordInput.type === "password" ? "text" : "password";
           passwordInput.type = type;
           toggleButton.classList.toggle("showing");
           toggleButton.setAttribute(
             "aria-label",
             type === "password" ? "Show password" : "Hide password"
           );
         });
       }

       // Form submission handler
       const loginForm = document.getElementById("loginForm");
       if (loginForm) {
         loginForm.addEventListener("submit", function (e) {
           const loginButton = document.querySelector(".btn-login");

           // Prevent multiple submissions
           if (loginButton.classList.contains("loading")) {
             e.preventDefault();
             return;
           }

           // Clear any existing toast notifications
           clearAllToasts();

           // Add loading state to button
           loginButton.classList.add("loading");

           // Add a short timeout before submitting to show the loading state
           setTimeout(() => {
             loginForm.submit();
           }, 500);

           // Prevent the default form submission
           e.preventDefault();

           // Show a loading toast
           showToast('Logging in, please wait...', 'info');

           // Optional: Remove loading state if login takes too long
           setTimeout(() => {
             loginButton.classList.remove("loading");
           }, 10000); // 10 seconds timeout
         });
       }

       // Auto-hide alerts
       const alerts = document.querySelectorAll(".alert");
       alerts.forEach((alert) => {
         setTimeout(() => {
           alert.style.opacity = "0";
           setTimeout(() => alert.remove(), 300);
         }, 5000);
       });

       // Add floating animation to shapes
       const shapes = document.querySelectorAll(".shape");
       shapes.forEach((shape, index) => {
         shape.style.animationDelay = `${index * -2}s`;
       });

       // Add ripple effect to button
       const button = document.querySelector(".login-button");
       if (button) {
         button.addEventListener("click", function (e) {
           let x = e.clientX - e.target.offsetLeft;
           let y = e.clientY - e.target.offsetTop;

           let ripple = document.createElement("span");
           ripple.style.left = `${x}px`;
           ripple.style.top = `${y}px`;

           this.appendChild(ripple);
           setTimeout(() => ripple.remove(), 600);
         });
       }
     });
   </script>
 </body>
</html>