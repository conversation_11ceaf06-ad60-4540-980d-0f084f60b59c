<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Security Settings | <%= company.name %></title>

    <!-- Prevent iOS text size adjust -->
    <meta name="format-detection" content="telephone=no">
    
    <!-- Ensure proper rendering on all devices -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Syne:wght@400..800&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/index.css" />

    <meta name="company-code" content="<%= company.companyCode %>" />

    <style>
      /* Security Settings Specific Styles - PandaPayroll Design System */
      .security-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .security-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .security-header h1 {
        font-family: "Syne", sans-serif;
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
      }

      .security-header p {
        opacity: 0.9;
        margin: 0;
        font-size: 1.1rem;
      }

      .security-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .security-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease;
      }

      .security-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border-color: #d1d5db;
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .card-icon {
        width: 40px;
        height: 40px;
        background: #f3f4f6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #1e40af;
        font-size: 1.25rem;
      }

      .card-title {
        font-family: "Syne", sans-serif;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .card-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }

      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f3f4f6;
      }

      .setting-item:last-child {
        border-bottom: none;
      }

      .setting-label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.25rem;
      }

      .setting-description {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .toggle-switch {
        position: relative;
        width: 44px;
        height: 24px;
        background-color: #d1d5db;
        border-radius: 12px;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .toggle-switch.active {
        background-color: #1e40af;
      }

      .toggle-slider {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .toggle-switch.active .toggle-slider {
        transform: translateX(20px);
      }

      .select-input {
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        font-size: 0.875rem;
        color: #374151;
        min-width: 120px;
      }

      .btn-primary {
        background: #1e40af;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: "Syne", sans-serif;
      }

      .btn-primary:hover {
        background: #1d4ed8;
        transform: translateY(-1px);
      }

      .btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: "Syne", sans-serif;
      }

      .btn-secondary:hover {
        background: #e5e7eb;
        border-color: #9ca3af;
      }

      .btn-danger {
        background: #dc2626;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: "Syne", sans-serif;
      }

      .btn-danger:hover {
        background: #b91c1c;
        transform: translateY(-1px);
      }

      .device-list {
        margin-top: 1rem;
      }

      .device-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 8px;
        margin-bottom: 0.75rem;
        border: 1px solid #f3f4f6;
      }

      .device-info {
        flex: 1;
      }

      .device-name {
        font-weight: 500;
        color: #1f2937;
        margin-bottom: 0.25rem;
      }

      .device-details {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .device-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-right: 1rem;
      }

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #10b981;
      }

      .status-indicator.warning {
        background: #f59e0b;
      }

      .status-indicator.danger {
        background: #ef4444;
      }

      .trust-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
      }

      .trust-high {
        background: #d1fae5;
        color: #065f46;
      }

      .trust-medium {
        background: #fef3c7;
        color: #92400e;
      }

      .trust-low {
        background: #fee2e2;
        color: #991b1b;
      }

      .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
      }

      .full-width-card {
        grid-column: 1 / -1;
      }

      @media (max-width: 768px) {
        .security-grid {
          grid-template-columns: 1fr;
        }

        .security-container {
          padding: 1rem;
        }

        .device-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
        }

        .device-status {
          margin-right: 0;
        }
      }
    </style>
  </head>

  <body>
    <%- include('../partials/header') %>
    <nav><%- include('../partials/sidebar') %></nav>

    <main class="main-container">
      <div class="security-container">
        <!-- Security Header -->
        <div class="security-header">
          <h1><i class="ph ph-shield-check"></i> Security Settings</h1>
          <p>Manage your account security, trusted devices, and session preferences</p>
        </div>

        <!-- Security Settings Grid -->
        <div class="security-grid">
          <!-- Remember Me Settings -->
          <div class="security-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="ph ph-clock-clockwise"></i>
              </div>
              <h3 class="card-title">Remember Me</h3>
            </div>
            <p class="card-description">
              Configure automatic login settings for trusted devices
            </p>
            
            <div class="setting-item">
              <div>
                <div class="setting-label">Enable Remember Me</div>
                <div class="setting-description">Stay signed in on trusted devices</div>
              </div>
              <div class="toggle-switch" id="rememberMeToggle">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Token Expiration</div>
                <div class="setting-description">How long to stay signed in</div>
              </div>
              <select class="select-input" id="tokenExpiration">
                <option value="7">7 days</option>
                <option value="14">14 days</option>
                <option value="30" selected>30 days</option>
                <option value="60">60 days</option>
                <option value="90">90 days</option>
              </select>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Maximum Devices</div>
                <div class="setting-description">Limit trusted devices</div>
              </div>
              <select class="select-input" id="maxDevices">
                <option value="3">3 devices</option>
                <option value="5" selected>5 devices</option>
                <option value="10">10 devices</option>
              </select>
            </div>
          </div>

          <!-- Session Security -->
          <div class="security-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="ph ph-timer"></i>
              </div>
              <h3 class="card-title">Session Security</h3>
            </div>
            <p class="card-description">
              Configure session timeouts and security policies
            </p>
            
            <div class="setting-item">
              <div>
                <div class="setting-label">Session Timeout</div>
                <div class="setting-description">Auto logout after inactivity</div>
              </div>
              <select class="select-input" id="sessionTimeout">
                <option value="30">30 minutes</option>
                <option value="60">1 hour</option>
                <option value="120">2 hours</option>
                <option value="240">4 hours</option>
                <option value="480" selected>8 hours</option>
              </select>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Inactivity Timeout</div>
                <div class="setting-description">Logout after no activity</div>
              </div>
              <select class="select-input" id="inactivityTimeout">
                <option value="15">15 minutes</option>
                <option value="30">30 minutes</option>
                <option value="60" selected>1 hour</option>
                <option value="120">2 hours</option>
              </select>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Max Concurrent Sessions</div>
                <div class="setting-description">Limit active sessions</div>
              </div>
              <select class="select-input" id="maxSessions">
                <option value="1">1 session</option>
                <option value="2">2 sessions</option>
                <option value="3" selected>3 sessions</option>
                <option value="5">5 sessions</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Trusted Devices (Full Width) -->
        <div class="security-card full-width-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="ph ph-devices"></i>
            </div>
            <h3 class="card-title">Trusted Devices</h3>
          </div>
          <p class="card-description">
            Manage devices that can automatically sign you in
          </p>
          
          <div class="device-list" id="deviceList">
            <!-- Devices will be loaded here -->
          </div>

          <div class="action-buttons">
            <button class="btn-secondary" onclick="refreshDevices()">
              <i class="ph ph-arrow-clockwise"></i> Refresh
            </button>
            <button class="btn-danger" onclick="revokeAllDevices()">
              <i class="ph ph-x-circle"></i> Revoke All Devices
            </button>
          </div>
        </div>

        <!-- Security Notifications -->
        <div class="security-grid">
          <div class="security-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="ph ph-bell"></i>
              </div>
              <h3 class="card-title">Security Notifications</h3>
            </div>
            <p class="card-description">
              Configure when to receive security alerts
            </p>
            
            <div class="setting-item">
              <div>
                <div class="setting-label">New Device Login</div>
                <div class="setting-description">Alert when signing in from new device</div>
              </div>
              <div class="toggle-switch active" id="newDeviceNotify">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Suspicious Activity</div>
                <div class="setting-description">Alert for unusual login patterns</div>
              </div>
              <div class="toggle-switch active" id="suspiciousActivityNotify">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div>
                <div class="setting-label">Session Expiry Warning</div>
                <div class="setting-description">Warn before session expires</div>
              </div>
              <div class="toggle-switch active" id="sessionExpiryNotify">
                <div class="toggle-slider"></div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="security-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="ph ph-lightning"></i>
              </div>
              <h3 class="card-title">Quick Actions</h3>
            </div>
            <p class="card-description">
              Immediate security actions you can take
            </p>
            
            <div class="action-buttons">
              <button class="btn-primary" onclick="extendCurrentSession()">
                <i class="ph ph-clock"></i> Extend Session
              </button>
              <button class="btn-secondary" onclick="viewActiveSessions()">
                <i class="ph ph-list"></i> Active Sessions
              </button>
              <button class="btn-danger" onclick="logoutAllDevices()">
                <i class="ph ph-sign-out"></i> Logout All
              </button>
            </div>
          </div>
        </div>

        <!-- Save Settings -->
        <div class="action-buttons" style="justify-content: flex-end; margin-top: 2rem;">
          <button class="btn-secondary" onclick="resetSettings()">
            <i class="ph ph-arrow-counter-clockwise"></i> Reset
          </button>
          <button class="btn-primary" onclick="saveSecuritySettings()">
            <i class="ph ph-floppy-disk"></i> Save Settings
          </button>
        </div>
      </div>
    </main>

    <!-- Toast notification container -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Include footer -->
    <%- include('../partials/footer') %>
    <%- include('../partials/mobile-bottom-nav') %>

    <script>
      // Security Settings JavaScript
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize page
        loadSecuritySettings();
        loadTrustedDevices();
        setupEventListeners();

        // Show toast notification function (matching PandaPayroll pattern)
        function showToast(message, type = 'info') {
          const toastContainer = document.getElementById('toastContainer');

          // Limit to 3 visible toasts at a time
          const existingToasts = toastContainer.querySelectorAll('.toast');
          if (existingToasts.length >= 3) {
            toastContainer.removeChild(existingToasts[0]);
          }

          const toast = document.createElement('div');
          toast.className = `toast toast-${type}`;

          let iconSvg = '';
          if (type === 'success') {
            iconSvg = '<i class="ph ph-check-circle"></i>';
          } else if (type === 'error') {
            iconSvg = '<i class="ph ph-x-circle"></i>';
          } else if (type === 'warning') {
            iconSvg = '<i class="ph ph-warning"></i>';
          } else {
            iconSvg = '<i class="ph ph-info"></i>';
          }

          toast.innerHTML = `
            ${iconSvg}
            <div class="toast-content">${message}</div>
            <button class="toast-close" aria-label="Close">×</button>
          `;

          toastContainer.appendChild(toast);

          const closeButton = toast.querySelector('.toast-close');
          closeButton.addEventListener('click', () => {
            toast.remove();
          });

          setTimeout(() => {
            if (toast.parentElement) {
              toast.remove();
            }
          }, 5000);

          return toast;
        }

        // Setup event listeners
        function setupEventListeners() {
          // Toggle switches
          document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
              this.classList.toggle('active');
            });
          });
        }

        // Load current security settings
        async function loadSecuritySettings() {
          try {
            const response = await fetch('/api/session/security/settings');
            if (response.ok) {
              const settings = await response.json();
              populateSettings(settings);
            }
          } catch (error) {
            console.error('Error loading security settings:', error);
            showToast('Failed to load security settings', 'error');
          }
        }

        // Populate settings form
        function populateSettings(settings) {
          // Remember Me settings
          const rememberMeToggle = document.getElementById('rememberMeToggle');
          if (settings.rememberMeSettings?.enabled) {
            rememberMeToggle.classList.add('active');
          }

          document.getElementById('tokenExpiration').value =
            settings.rememberMeSettings?.tokenExpirationDays || 30;
          document.getElementById('maxDevices').value =
            settings.rememberMeSettings?.maxDevices || 5;

          // Session settings
          document.getElementById('sessionTimeout').value =
            settings.sessionSecurity?.sessionTimeoutMinutes || 480;
          document.getElementById('inactivityTimeout').value =
            settings.sessionSecurity?.inactivityTimeoutMinutes || 60;
          document.getElementById('maxSessions').value =
            settings.sessionSecurity?.maxConcurrentSessions || 3;

          // Notification settings
          const newDeviceToggle = document.getElementById('newDeviceNotify');
          if (settings.securityPreferences?.notifyOnNewDevice !== false) {
            newDeviceToggle.classList.add('active');
          }

          const suspiciousToggle = document.getElementById('suspiciousActivityNotify');
          if (settings.securityPreferences?.notifyOnSuspiciousActivity !== false) {
            suspiciousToggle.classList.add('active');
          }

          const sessionExpiryToggle = document.getElementById('sessionExpiryNotify');
          if (settings.securityPreferences?.notifyOnSessionExpiry !== false) {
            sessionExpiryToggle.classList.add('active');
          }
        }

        // Load trusted devices
        async function loadTrustedDevices() {
          try {
            const response = await fetch('/api/session/devices');
            if (response.ok) {
              const data = await response.json();
              displayDevices(data.devices);
            }
          } catch (error) {
            console.error('Error loading devices:', error);
            showToast('Failed to load trusted devices', 'error');
          }
        }

        // Display devices in the list
        function displayDevices(devices) {
          const deviceList = document.getElementById('deviceList');

          if (!devices || devices.length === 0) {
            deviceList.innerHTML = `
              <div style="text-align: center; padding: 2rem; color: #6b7280;">
                <i class="ph ph-devices" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                <p>No trusted devices found</p>
                <p style="font-size: 0.875rem;">Devices will appear here when you use "Remember Me" during login</p>
              </div>
            `;
            return;
          }

          deviceList.innerHTML = devices.map(device => `
            <div class="device-item">
              <div class="device-info">
                <div class="device-name">${device.deviceName}</div>
                <div class="device-details">
                  ${device.browser} • ${device.os} • Last used: ${formatDate(device.lastUsed)}
                </div>
              </div>
              <div class="device-status">
                <div class="status-indicator ${getStatusClass(device)}"></div>
                <span class="trust-badge trust-${device.trustLevel}">${device.trustLevel}</span>
              </div>
              <button class="btn-secondary" onclick="revokeDevice('${device._id}')">
                <i class="ph ph-x"></i> Revoke
              </button>
            </div>
          `).join('');
        }

        // Get status class for device
        function getStatusClass(device) {
          if (device.isSuspicious) return 'danger';
          if (device.trustLevel === 'low') return 'warning';
          return '';
        }

        // Format date for display
        function formatDate(dateString) {
          const date = new Date(dateString);
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays === 1) return 'Yesterday';
          if (diffDays < 7) return `${diffDays} days ago`;
          return date.toLocaleDateString();
        }

        // Global functions for button actions
        window.refreshDevices = loadTrustedDevices;

        window.revokeDevice = async function(deviceId) {
          if (!confirm('Are you sure you want to revoke this device? You will need to sign in again on this device.')) {
            return;
          }

          try {
            const response = await fetch(`/api/session/devices/${deviceId}`, {
              method: 'DELETE'
            });

            if (response.ok) {
              showToast('Device revoked successfully', 'success');
              loadTrustedDevices();
            } else {
              const error = await response.json();
              showToast(error.message || 'Failed to revoke device', 'error');
            }
          } catch (error) {
            console.error('Error revoking device:', error);
            showToast('Failed to revoke device', 'error');
          }
        };

        window.revokeAllDevices = async function() {
          if (!confirm('Are you sure you want to revoke ALL trusted devices? You will need to sign in again on all devices.')) {
            return;
          }

          try {
            const response = await fetch('/api/session/devices/revoke-all', {
              method: 'POST'
            });

            if (response.ok) {
              const data = await response.json();
              showToast(`All devices revoked successfully (${data.revokedCount} devices)`, 'success');
              loadTrustedDevices();
            } else {
              const error = await response.json();
              showToast(error.message || 'Failed to revoke all devices', 'error');
            }
          } catch (error) {
            console.error('Error revoking all devices:', error);
            showToast('Failed to revoke all devices', 'error');
          }
        };

        window.extendCurrentSession = async function() {
          try {
            const response = await fetch('/api/session/extend', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ minutes: 480 }) // 8 hours
            });

            if (response.ok) {
              const data = await response.json();
              showToast(`Session extended by ${data.extensionMinutes} minutes`, 'success');
            } else {
              const error = await response.json();
              showToast(error.message || 'Failed to extend session', 'error');
            }
          } catch (error) {
            console.error('Error extending session:', error);
            showToast('Failed to extend session', 'error');
          }
        };

        window.viewActiveSessions = async function() {
          try {
            const response = await fetch('/api/session/active');
            if (response.ok) {
              const data = await response.json();
              showSessionModal(data.sessions);
            }
          } catch (error) {
            console.error('Error loading active sessions:', error);
            showToast('Failed to load active sessions', 'error');
          }
        };

        window.logoutAllDevices = async function() {
          if (!confirm('Are you sure you want to logout from all devices? This will end all active sessions.')) {
            return;
          }

          try {
            const response = await fetch('/api/session/logout-all', {
              method: 'POST'
            });

            if (response.ok) {
              showToast('Logged out from all devices successfully', 'success');
              // Redirect to login after a short delay
              setTimeout(() => {
                window.location.href = '/login';
              }, 2000);
            } else {
              const error = await response.json();
              showToast(error.message || 'Failed to logout from all devices', 'error');
            }
          } catch (error) {
            console.error('Error logging out from all devices:', error);
            showToast('Failed to logout from all devices', 'error');
          }
        };

        window.saveSecuritySettings = async function() {
          const settings = {
            rememberMeSettings: {
              enabled: document.getElementById('rememberMeToggle').classList.contains('active'),
              tokenExpirationDays: parseInt(document.getElementById('tokenExpiration').value),
              maxDevices: parseInt(document.getElementById('maxDevices').value)
            },
            sessionSecurity: {
              sessionTimeoutMinutes: parseInt(document.getElementById('sessionTimeout').value),
              inactivityTimeoutMinutes: parseInt(document.getElementById('inactivityTimeout').value),
              maxConcurrentSessions: parseInt(document.getElementById('maxSessions').value)
            },
            securityPreferences: {
              notifyOnNewDevice: document.getElementById('newDeviceNotify').classList.contains('active'),
              notifyOnSuspiciousActivity: document.getElementById('suspiciousActivityNotify').classList.contains('active'),
              notifyOnSessionExpiry: document.getElementById('sessionExpiryNotify').classList.contains('active')
            }
          };

          try {
            const response = await fetch('/api/session/security/settings', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(settings)
            });

            if (response.ok) {
              showToast('Security settings saved successfully', 'success');
            } else {
              const error = await response.json();
              showToast(error.message || 'Failed to save settings', 'error');
            }
          } catch (error) {
            console.error('Error saving settings:', error);
            showToast('Failed to save settings', 'error');
          }
        };

        window.resetSettings = function() {
          if (confirm('Are you sure you want to reset all security settings to defaults?')) {
            loadSecuritySettings();
            showToast('Settings reset to defaults', 'info');
          }
        };

        // Show session modal (simplified version)
        function showSessionModal(sessions) {
          const modal = document.createElement('div');
          modal.style.cssText = `
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.5); z-index: 1000;
            display: flex; align-items: center; justify-content: center;
          `;

          modal.innerHTML = `
            <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
              <h3 style="margin: 0 0 1rem 0; font-family: Syne, sans-serif;">Active Sessions</h3>
              <div>
                ${sessions.map(session => `
                  <div style="padding: 1rem; border: 1px solid #e5e7eb; border-radius: 8px; margin-bottom: 1rem;">
                    <div style="font-weight: 500;">${session.deviceName}</div>
                    <div style="font-size: 0.875rem; color: #6b7280;">
                      ${session.isCurrent ? 'Current session • ' : ''}
                      ${session.minutesUntilExpiry} minutes remaining
                    </div>
                  </div>
                `).join('')}
              </div>
              <button onclick="this.closest('div').parentElement.remove()"
                      style="background: #1e40af; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">
                Close
              </button>
            </div>
          `;

          document.body.appendChild(modal);
          modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
          });
        }
      });
    </script>
  </body>
</html>
