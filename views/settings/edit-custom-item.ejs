<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit <%= customItem.name %> - <%= company.name %></title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/regularinputs.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/notifications.css" />
    <link rel="stylesheet" href="/css/custom-items.css" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .form-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .form-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        }
        
        .form-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .form-header h1 {
            color: #1e293b;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .form-header p {
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(99, 102, 241, 0.02);
            border-radius: 10px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .checkbox-group:hover {
            background: rgba(99, 102, 241, 0.05);
            border-color: rgba(99, 102, 241, 0.2);
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            border: none;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5855eb;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #f1f5f9;
            color: #475569;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .help-text {
            font-size: 0.8rem;
            color: #64748b;
            margin-top: 0.25rem;
        }
        
        .item-type-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #6366f1;
            color: white;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <%- include('../partials/header') %>
    <nav><%- include('../partials/sidebar') %></nav>

    <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h1>Edit Custom Item: <%= customItem.name %></h1>
                <p>
                    <span class="item-type-badge"><%= customItem.type %></span>
                    <% if (customItem.inputType) { %>
                        • Input Type: <%= customItem.inputType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) %>
                    <% } %>
                </p>
            </div>

            <form id="editCustomItemForm" method="POST" action="/clients/<%= company.companyCode %>/settings/custom-items/edit/<%= customItem._id %>">
                <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" value="<%= customItem.name %>" required />
                    </div>
                    
                    <div class="form-group">
                        <label for="code">Code</label>
                        <input type="text" id="code" name="code" value="<%= customItem.code || '' %>" />
                        <div class="help-text">Optional unique identifier for this item</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <input type="text" id="description" name="description" value="<%= customItem.description || '' %>" />
                </div>

                <% if (customItem.type === 'income') { %>
                    <div class="form-group">
                        <label for="inputType">Input Type *</label>
                        <select id="inputType" name="inputType" required onchange="toggleConditionalFields()">
                            <option value="">Select input type...</option>
                            <option value="fixedAmount" <%= customItem.inputType === 'fixedAmount' ? 'selected' : '' %>>Fixed Amount</option>
                            <option value="amountPerEmployee" <%= customItem.inputType === 'amountPerEmployee' ? 'selected' : '' %>>Amount Per Employee</option>
                            <option value="differentEveryPayslip" <%= customItem.inputType === 'differentEveryPayslip' ? 'selected' : '' %>>Different Every Payslip</option>
                            <option value="onceOffSpecificPayslips" <%= customItem.inputType === 'onceOffSpecificPayslips' ? 'selected' : '' %>>Once-off Specific Payslips</option>
                            <option value="customRateQuantity" <%= customItem.inputType === 'customRateQuantity' ? 'selected' : '' %>>Custom Rate × Quantity</option>
                            <option value="percentageOfIncome" <%= customItem.inputType === 'percentageOfIncome' ? 'selected' : '' %>>Percentage of Income</option>
                            <option value="monthlyForNonMonthly" <%= customItem.inputType === 'monthlyForNonMonthly' ? 'selected' : '' %>>Monthly for Non-Monthly</option>
                        </select>
                    </div>

                    <!-- Conditional fields for income items -->
                    <div id="conditionalFields">
                        <!-- These will be populated by JavaScript based on inputType -->
                    </div>
                <% } %>

                <div class="form-actions">
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="ph ph-trash"></i>
                        Delete Item
                    </button>
                    <a href="/clients/<%= company.companyCode %>/settings/accounting/custom-items" class="btn btn-secondary">
                        <i class="ph ph-arrow-left"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph ph-check"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const customItem = <%- JSON.stringify(customItem) %>;
        
        function toggleConditionalFields() {
            const inputType = document.getElementById('inputType').value;
            const conditionalFields = document.getElementById('conditionalFields');
            
            if (!inputType) {
                conditionalFields.innerHTML = '';
                return;
            }
            
            let fieldsHtml = '';
            
            switch (inputType) {
                case 'fixedAmount':
                case 'amountPerEmployee':
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="amount">Amount</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0" 
                                   value="${customItem.amount || ''}" />
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="enableProRata" name="enableProRata" 
                                       ${customItem.enableProRata ? 'checked' : ''} />
                                <label for="enableProRata">Enable Pro-Rata</label>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'customRateQuantity':
                    fieldsHtml = `
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customRate">Custom Rate</label>
                                <input type="number" id="customRate" name="customRate" step="0.01" min="0" 
                                       value="${customItem.customRate || ''}" />
                            </div>
                            <div class="form-group">
                                <label for="hoursWorkedFactor">Hours Worked Factor</label>
                                <input type="number" id="hoursWorkedFactor" name="hoursWorkedFactor" step="0.01" min="0" 
                                       value="${customItem.hoursWorkedFactor || 1}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="differentRateForEveryEmployee" name="differentRateForEveryEmployee" 
                                       ${customItem.differentRateForEveryEmployee ? 'checked' : ''} />
                                <label for="differentRateForEveryEmployee">Different Rate for Every Employee</label>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'monthlyForNonMonthly':
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="monthlyAmount">Monthly Amount</label>
                            <input type="number" id="monthlyAmount" name="monthlyAmount" step="0.01" min="0" 
                                   value="${customItem.monthlyAmount || ''}" />
                        </div>
                    `;
                    break;
            }
            
            conditionalFields.innerHTML = fieldsHtml;
        }
        
        function confirmDelete() {
            if (confirm(`Are you sure you want to delete "${customItem.name}"? This action cannot be undone.`)) {
                fetch(`/clients/<%= company.companyCode %>/settings/custom-items/<%= customItem._id %>`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        window.location.href = '/clients/<%= company.companyCode %>/settings/accounting/custom-items';
                    } else {
                        alert('Error: ' + (result.message || 'Failed to delete item'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting item: ' + error.message);
                });
            }
        }
        
        // Initialize conditional fields on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleConditionalFields();
        });
    </script>
</body>
</html>
