<!-- Optimized Head Section for Performance -->
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="description" content="<%= typeof metaDescription !== 'undefined' ? metaDescription : 'PandaPayroll - Modern payroll management system' %>" />

<!-- DNS Prefetch for external resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//fonts.gstatic.com" />
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
<link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
<link rel="dns-prefetch" href="//unpkg.com" />

<!-- Preconnect for critical external resources -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

<% if (typeof preloadResources !== 'undefined' && preloadResources.length > 0) { %>
  <!-- Preload critical resources -->
  <% preloadResources.forEach(resource => { %>
    <link rel="preload" href="<%= resource.href %>" as="<%= resource.as %>" 
          <% if (resource.crossorigin) { %>crossorigin="<%= resource.crossorigin %>"<% } %>
          <% if (resource.type) { %>type="<%= resource.type %>"<% } %> />
  <% }); %>
<% } %>

<% if (typeof fontPreloads !== 'undefined' && fontPreloads.length > 0) { %>
  <!-- Preload critical fonts -->
  <% fontPreloads.forEach(font => { %>
    <link rel="preload" href="<%= font.href %>" as="<%= font.as %>" 
          type="<%= font.type %>" crossorigin="<%= font.crossorigin %>" />
  <% }); %>
<% } %>

<!-- Critical CSS inlined for fastest rendering -->
<% if (typeof shouldInlineCSS !== 'undefined' && shouldInlineCSS && typeof criticalCSS !== 'undefined') { %>
  <style>
    <%= criticalCSS %>
  </style>
<% } else { %>
  <!-- Development mode - load CSS normally -->
  <link rel="stylesheet" href="/css/styles.css" />
  <link rel="stylesheet" href="/css/dashboard.css" />
  <link rel="stylesheet" href="/css/header.css" />
  <link rel="stylesheet" href="/css/sidebar.css" />
<% } %>

<!-- Load fonts with optimized display -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

<!-- Favicon and app icons -->
<link rel="icon" type="image/png" href="/images/favicon.png" />
<link rel="apple-touch-icon" href="/images/favicon.png" />

<% if (typeof optimizeCSS !== 'undefined' && optimizeCSS) { %>
  <!-- Load non-critical CSS asynchronously -->
  <script>
    // Function to load CSS asynchronously
    function loadCSS(href) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.media = 'print';
      link.onload = function() {
        this.media = 'all';
      };
      document.head.appendChild(link);
    }
    
    // Load non-critical CSS files
    <% if (typeof nonCriticalCSS !== 'undefined' && nonCriticalCSS.length > 0) { %>
      <% nonCriticalCSS.forEach(cssFile => { %>
        loadCSS('<%= cssFile %>?v=<%= cssVersion %>');
      <% }); %>
    <% } %>
  </script>
  
  <!-- Fallback for users with JavaScript disabled -->
  <noscript>
    <% if (typeof nonCriticalCSS !== 'undefined' && nonCriticalCSS.length > 0) { %>
      <% nonCriticalCSS.forEach(cssFile => { %>
        <link rel="stylesheet" href="<%= cssFile %>?v=<%= cssVersion %>" />
      <% }); %>
    <% } %>
  </noscript>
<% } %>

<!-- Toast Notifications CSS (critical for UX) -->
<link rel="stylesheet" href="/css/toast.css" />

<!-- Critical JavaScript that needs to run early -->
<script>
  // Performance mark for measuring load time
  performance.mark('head-end');
  
  // Critical JavaScript for immediate functionality
  window.PandaPayroll = window.PandaPayroll || {};
  window.PandaPayroll.config = {
    csrfToken: '<%= typeof csrfToken !== "undefined" ? csrfToken : "" %>',
    userId: '<%= typeof user !== "undefined" && user ? user._id : "" %>',
    companyCode: '<%= typeof currentCompanyCode !== "undefined" ? currentCompanyCode : "" %>'
  };
  
  // Early error handling
  window.addEventListener('error', function(e) {
    console.error('Early error:', e.error);
  });
  
  // Loading state management
  document.documentElement.classList.add('loading');
  window.addEventListener('load', function() {
    document.documentElement.classList.remove('loading');
    performance.mark('page-loaded');
    
    // Measure performance
    try {
      const navigation = performance.getEntriesByType('navigation')[0];
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      console.log('Page load time:', loadTime + 'ms');
    } catch (e) {
      // Ignore performance measurement errors
    }
  });
</script>

<!-- Service Worker registration for caching (production only) -->
<% if (typeof optimizeCSS !== 'undefined' && optimizeCSS) { %>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
          .then(function(registration) {
            console.log('SW registered: ', registration);
          })
          .catch(function(registrationError) {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
<% } %>
