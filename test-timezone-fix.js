const moment = require("moment-timezone");
const timezoneUtils = require("./utils/timezoneUtils");

/**
 * Test script to validate timezone fix
 * Run this to verify the new timezone handling works correctly
 */

console.log("🧪 TIMEZONE FIX VALIDATION TEST");
console.log("================================\n");

// Test 1: Date parsing consistency
console.log("📅 Test 1: Date Parsing Consistency");
const testDate = "2024-01-31";
console.log(`Input: "${testDate}"`);

const oldWay = moment.tz(testDate, "Africa/Johannesburg").endOf('day').toDate();
const newWay = timezoneUtils.formatForDatabase(testDate, 'end');

console.log("Old way (problematic):", oldWay.toISOString());
console.log("New way (UTC-first):", newWay.toISOString());
console.log("Difference:", oldWay.getTime() - newWay.getTime(), "ms");
console.log("");

// Test 2: Period finding simulation
console.log("🔍 Test 2: Period Finding Simulation");
const mockPeriods = [
  { _id: "1", endDate: new Date("2024-01-31T22:00:00.000Z") }, // UTC date from MongoDB
  { _id: "2", endDate: new Date("2024-02-29T22:00:00.000Z") },
  { _id: "3", endDate: new Date("2024-03-31T21:00:00.000Z") }
];

const searchDate = "2024-01-31";
console.log(`Searching for: "${searchDate}"`);

// Old way (string comparison with timezone conversion)
const oldResult = mockPeriods.find(p => 
  moment.utc(p.endDate).tz('Africa/Johannesburg').format('YYYY-MM-DD') === 
  moment.tz(searchDate, 'Africa/Johannesburg').format('YYYY-MM-DD')
);

// New way (UTC string comparison)
const newResult = timezoneUtils.findPeriodByDate(mockPeriods, searchDate);

console.log("Old way result:", oldResult ? `Found ID: ${oldResult._id}` : "Not found");
console.log("New way result:", newResult ? `Found ID: ${newResult._id}` : "Not found");
console.log("");

// Test 3: Date range query simulation
console.log("📊 Test 3: Date Range Query Simulation");
const queryDate = "2024-01-31";
const dateRange = timezoneUtils.createDateRange(queryDate);

console.log(`Query date: "${queryDate}"`);
console.log("Generated range:");
console.log("  $gte:", dateRange.$gte.toISOString());
console.log("  $lte:", dateRange.$lte.toISOString());

// Check if our mock period falls within the range
const testPeriod = mockPeriods[0];
const inRange = testPeriod.endDate >= dateRange.$gte && testPeriod.endDate <= dateRange.$lte;
console.log("Period in range:", inRange);
console.log("");

// Test 4: Environment simulation
console.log("🌍 Test 4: Environment Simulation");
console.log("Current environment:");
console.log("  Server timezone:", process.env.TZ || "Not set");
console.log("  Node.js offset:", new Date().getTimezoneOffset(), "minutes");
console.log("  Moment guess:", moment.tz.guess());

// Simulate production vs development
const utcDate = moment.utc("2024-01-31T22:00:00.000Z");
const localInterpretation = moment("2024-01-31T22:00:00.000Z");

console.log("\nDate interpretation test:");
console.log("  UTC interpretation:", utcDate.format('YYYY-MM-DD HH:mm:ss Z'));
console.log("  Local interpretation:", localInterpretation.format('YYYY-MM-DD HH:mm:ss Z'));
console.log("  SA timezone result:", utcDate.tz('Africa/Johannesburg').format('YYYY-MM-DD HH:mm:ss Z'));
console.log("");

// Test 5: Debug output
console.log("🔧 Test 5: Debug Output");
timezoneUtils.debugDate("Test Date", testDate);
timezoneUtils.debugDate("MongoDB Date", mockPeriods[0].endDate);

console.log("✅ TIMEZONE FIX VALIDATION COMPLETE");
console.log("====================================");
console.log("\nIf the new way shows consistent results across environments,");
console.log("the timezone fix should resolve the +1 day discrepancy issue.");
