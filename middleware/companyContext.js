const User = require("../models/user");

module.exports = async function companyContext(req, res, next) {
  if (req.user) {
    try {
      // Fetch the user with populated companies
      const user = await User.findById(req.user._id).populate("companies");

      // Set the current company if it's not already set
      if (!req.user.currentCompany && user.companies.length > 0) {
        req.user.currentCompany = user.companies[0]._id;
        await User.findByIdAndUpdate(req.user._id, {
          currentCompany: req.user.currentCompany,
        });
      }

      // Add company context to the request object
      req.companyContext = {
        currentCompany: req.user.currentCompany,
        companies: user.companies.map((company) => ({
          _id: company._id,
          name: company.name,
        })),
      };

      // Make company context available to all views
      res.locals.companyContext = req.companyContext;
    } catch (error) {
      console.error("Error setting company context:", error);
    }
  }
  next();
};
