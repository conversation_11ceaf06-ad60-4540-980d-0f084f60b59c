const RedisManager = require("../config/redis");

async function cleanupSessions(req, res, next) {
  try {
    if (req.session.twoFactorPending) {
      const sessionKey = `2fa:${req.sessionID}`;
      const twoFactorData = await RedisManager.get(sessionKey);

      if (!twoFactorData) {
        delete req.session.twoFactorPending;
        await new Promise((resolve) => req.session.save(resolve));
      }
    }
  } catch (error) {
    console.error("Cleanup error:", error);
  }
  next();
}

module.exports = cleanupSessions;
