const mongoose = require("mongoose");
const User = require("../models/user");
const Company = require("../models/Company");
const TwoFactorVerification = require("../models/twoFactorVerification");
const TokenManager = require("../services/tokenManager");
const jwt = require("jsonwebtoken");

// Debug session state
function debugSession(req, res, next) {
  console.log("\n=== Session State ===");
  console.log({
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated(),
    hasSession: !!req.session,
    hasUser: !!req.user,
    user: req.user
      ? {
          id: req.user._id,
          email: req.user.email,
          twoFactorEnabled: req.user.twoFactorEnabled,
          twoFactorAuthenticated: req.session?.twoFactorAuthenticated
        }
      : null,
    session: {
      cookie: req.session?.cookie,
      returnTo: req.session?.returnTo,
      flash: req.session?.flash
    }
  });
  next();
}

// Check if user is authenticated
const isAuthenticated = async (req, res, next) => {
  if (req.isAuthenticated()) {
    try {
      // Populate user data with companies and defaultCompany
      const user = await User.findById(req.user._id)
        .populate('companies')
        .populate('currentCompany')
        .populate('defaultCompany')
        .exec();

      if (!user) {
        req.logout((err) => {
          if (err) console.error("Error during logout:", err);
          return res.redirect("/login");
        });
        return;
      }

      // Extract company code from URL if present
      const urlCompanyCode = req.path.split('/clients/')[1]?.split('/')[0];
      
      // Handle company selection logic
      if (urlCompanyCode) {
        const selectedCompany = user.companies.find(c => c.companyCode === urlCompanyCode);
        if (selectedCompany) {
          // Only update if the company is different
          if (!user.currentCompany || user.currentCompany.companyCode !== urlCompanyCode) {
            user.currentCompany = selectedCompany;
            await User.findByIdAndUpdate(user._id, { 
              currentCompany: selectedCompany._id 
            }).exec();
          }
        }
      }

      // If no current company is set but we have a default or any company
      if (!user.currentCompany) {
        const companyToUse = user.defaultCompany || (user.companies.length > 0 ? user.companies[0] : null);
        
        if (companyToUse) {
          user.currentCompany = companyToUse;
          if (!user.defaultCompany) {
            user.defaultCompany = companyToUse;
            await User.findByIdAndUpdate(user._id, {
              currentCompany: companyToUse._id,
              defaultCompany: companyToUse._id
            }).exec();
          } else {
            await User.findByIdAndUpdate(user._id, {
              currentCompany: companyToUse._id
            }).exec();
          }
        }
      }

      // Store the fully populated user object
      req.user = user;

      // Make company data available to all views
      res.locals.user = user;
      res.locals.currentCompany = user.currentCompany;
      res.locals.companies = user.companies;
      res.locals.defaultCompany = user.defaultCompany;

      // Save session with updated user data
      req.session.user = user;
      req.session.currentCompany = user.currentCompany;

      // Check if we need to redirect to company selection
      const isAuthPath = req.path.startsWith('/auth/') || req.path === '/login';
      const isCompanyPath = req.path.startsWith('/companies/');
      const isPublicPath = req.path === '/' || req.path.startsWith('/public/');
      
      if (!user.currentCompany && !isAuthPath && !isCompanyPath && !isPublicPath) {
        return res.redirect('/companies/select');
      }

      // Save session explicitly
      await new Promise((resolve, reject) => {
        req.session.save(err => {
          if (err) {
            console.error("Error saving session:", err);
            reject(err);
          } else {
            resolve();
          }
        });
      });

      next();
    } catch (error) {
      console.error("Error in isAuthenticated middleware:", error);
      res.redirect("/login");
    }
  } else {
    res.redirect("/login");
  }
};

// Role-based redirection middleware
const handleRoleRedirect = async (user) => {
  try {
    console.log("\n=== Role-based Redirection ===");
    console.log("User:", {
      id: user._id,
      email: user.email,
      role: user.role?.name,
      onboardingComplete: user.onboardingComplete,
      currentCompany: user.currentCompany?._id
    });

    // If user is an employee, redirect to their portal
    if (user.role?.name === 'employee') {
      console.log("User is an employee, redirecting to employee portal");
      return `/employee-portal/${user._id}`;
    }

    // For owners/admins, check onboarding status
    if (!user.onboardingComplete) {
      console.log("User onboarding not complete, redirecting to onboarding");
      if (user.currentCompany) {
        return `/clients/${user.currentCompany.companyCode}/onboarding/welcome`;
      } else {
        return '/onboarding/step1';
      }
    }

    // Default redirect for owners/admins with completed onboarding
    if (user.currentCompany) {
      console.log("User has current company, redirecting to dashboard");
      return `/clients/${user.currentCompany.companyCode}/dashboard`;
    } else {
      console.log("User has no current company, redirecting to company selection");
      return '/companies/select';
    }
  } catch (error) {
    console.error("Error in handleRoleRedirect:", error);
    return '/login';
  }
};

// Ensure user is company owner
async function ensureOwner(req, res, next) {
  console.log("ensureOwner middleware called");
  console.log("User object:", req.user);
  console.log("Company identifier:", req.params.companyIdentifier);
  console.log("Session ID:", req.sessionID);

  if (!req.user) {
    console.log("User not authenticated in ensureOwner, redirecting to login");
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }

  try {
    const companyIdentifier = req.params.companyIdentifier;
    let company;

    if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
      company = await Company.findById(companyIdentifier);
    } else {
      company = await Company.findOne({ companyCode: companyIdentifier });
    }

    if (!company) {
      console.log("Company not found");
      return res.status(404).send("Company not found");
    }

    console.log("Company found:", company);
    console.log("User ID:", req.user._id);
    console.log("Company owner ID:", company.owner);

    if (company.owner.toString() !== req.user._id.toString()) {
      console.log("User is not the owner of this company");
      req.flash("error", "You don't have permission to view this resource");
      return res.redirect("/login");
    }

    req.company = company;
    next();
  } catch (error) {
    console.error("Error in ensureOwner middleware:", error);
    res.status(500).send("An error occurred");
  }
}

// Ensure user is authenticated for API routes
function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({
    error: 'Unauthorized',
    message: 'Authentication required'
  });
}

// Ensure user has company access
function ensureCompanyAccess(req, res, next) {
  console.log('=== Company Access Check ===');
  console.log('User:', req.user);
  console.log('Session:', req.session);

  if (!req.user?.currentCompany) {
    console.error('Company access denied:', {
      user: req.user?.email,
      sessionID: req.sessionID,
      currentCompany: req.user?.currentCompany
    });
    
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Company access required'
    });
  }

  // Add company to request for convenience
  req.companyId = req.user.currentCompany;
  next();
}

// Middleware to ensure user is an employee
const ensureEmployee = async (req, res, next) => {
  try {
    console.log("\n=== Employee Role Check ===");
    console.log("User:", {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role?.name
    });
    
    const user = await User.findById(req.user._id)
      .populate('role')
      .lean();
    
    if (!user.role?.name || user.role.name !== 'employee') {
      console.log("Access denied: User is not an employee");
      req.flash("error", "Access denied. Employee role required.");
      return res.redirect("/login");
    }

    const employee = await Employee.findOne({ user: user._id });
    if (!employee) {
      console.log("Access denied: No employee record found");
      req.flash("error", "Employee record not found");
      return res.redirect("/login");
    }

    // Add employee data to request for convenience
    req.employee = employee;
    next();
  } catch (error) {
    console.error("Error in ensureEmployee middleware:", error);
    req.flash("error", "An error occurred");
    res.redirect("/login");
  }
};

// Clean up expired verifications
async function cleanupExpiredVerifications(req, res, next) {
  try {
    await TwoFactorVerification.deleteMany({
      expiresAt: { $lt: new Date() }
    });
    next();
  } catch (error) {
    console.error('Error cleaning up verifications:', error);
    next(error);
  }
}

// Require authentication with JWT
function requireAuth(req, res, next) {
  // First check session authentication
  if (req.isAuthenticated && req.isAuthenticated()) {
    console.log('User authenticated via session');
    return next();
  }

  // Then check JWT authentication
  const token = req.cookies.jwt || 
                (req.headers.authorization && req.headers.authorization.split(' ')[1]);

  if (!token) {
    console.log('No authentication token found');
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Store the decoded user in both req.user and req.jwtUser
    req.user = decoded;
    req.jwtUser = decoded;
    
    // Create a session for the JWT user if one doesn't exist
    if (!req.session.passport) {
      req.session.passport = { user: decoded.id };
      req.session.save((err) => {
        if (err) console.error('Error saving session:', err);
      });
    }
    
    console.log('User authenticated via JWT');
    next();
  } catch (error) {
    console.error('JWT verification error:', error);
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid authentication token'
    });
  }
}

// Check 2FA status
function check2FAStatus(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  // Check both session and direct 2FA flags
  const requires2FA = req.user.requires2FA || req.user.twoFactorEnabled;
  const isVerified = req.session.verified2FA || req.session.twoFactorAuthenticated;

  if (requires2FA && !isVerified) {
    return res.status(403).json({
      error: 'Forbidden',
      message: '2FA verification required'
    });
  }

  next();
}

// Check verification status with improved session handling
function checkVerification(req, res, next) {
  if (!req.session.twoFactorToken) {
    return res.redirect('/login');
  }

  TwoFactorVerification.findOne({
    token: req.session.twoFactorToken,
    expiresAt: { $gt: new Date() }
  }).then(verification => {
    if (!verification) {
      // Clear 2FA related session data
      delete req.session.twoFactorToken;
      delete req.session.verified2FA;
      delete req.session.twoFactorAuthenticated;
      
      // Save session explicitly
      req.session.save((err) => {
        if (err) console.error('Error saving session:', err);
        res.redirect('/login');
      });
      return;
    }
    next();
  }).catch(error => {
    console.error('Error checking verification:', error);
    res.redirect('/login');
  });
}

// Export all middleware functions
module.exports = {
  debugSession,
  isAuthenticated,
  handleRoleRedirect,
  ensureOwner,
  ensureAuthenticated,
  ensureCompanyAccess,
  ensureEmployee,
  cleanupExpiredVerifications,
  requireAuth,
  check2FAStatus,
  checkVerification
};
