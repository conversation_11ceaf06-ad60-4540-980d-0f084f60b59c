const rateLimit = require('express-rate-limit');
const whatsappConfig = require('../config/whatsapp');

// Rate limiting middleware
const rateLimiter = rateLimit({
  windowMs: whatsappConfig.rateLimit.windowMs,
  max: whatsappConfig.rateLimit.maxRequests,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// IP whitelist middleware
const ipWhitelist = (req, res, next) => {
  const clientIp = req.ip || req.connection.remoteAddress;
  
  if (whatsappConfig.security.ipWhitelist.length === 0 || 
      whatsappConfig.security.ipWhitelist.includes(clientIp)) {
    return next();
  }
  
  res.status(403).json({ error: 'Access denied' });
};

// Phone number validation middleware
const validatePhoneNumber = (req, res, next) => {
  const messages = req.body.entry?.[0]?.changes?.[0]?.value?.messages;
  if (!messages || !messages.length) {
    return next();
  }

  const from = messages[0].from;
  const countryCode = from.substring(0, 2);

  if (!whatsappConfig.security.allowedCountryCodes.includes(countryCode)) {
    return res.status(403).json({
      error: 'Phone numbers from this country are not supported'
    });
  }

  next();
};

// Request signature validation middleware
const validateSignature = (req, res, next) => {
  // Skip signature validation for GET requests (webhook verification)
  if (req.method === 'GET') {
    return next();
  }

  const signature = req.headers['x-hub-signature-256'];
  if (!signature) {
    console.warn('WhatsApp webhook: No signature provided');
    return res.status(401).json({ error: 'No signature provided' });
  }

  // Get the app secret from environment variables
  const appSecret = process.env.WHATSAPP_APP_SECRET;
  if (!appSecret) {
    console.warn('WhatsApp webhook: WHATSAPP_APP_SECRET not configured');
    // In development, we might not have the app secret configured
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Skipping signature validation in development mode');
      return next();
    }
    return res.status(500).json({ error: 'Server configuration error' });
  }

  try {
    const crypto = require('crypto');

    // Get the raw body for signature verification
    const rawBody = req.rawBody || JSON.stringify(req.body);

    // Create expected signature
    const expectedSignature = 'sha256=' + crypto
      .createHmac('sha256', appSecret)
      .update(rawBody, 'utf8')
      .digest('hex');

    // Compare signatures
    if (signature !== expectedSignature) {
      console.warn('WhatsApp webhook: Invalid signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    console.log('WhatsApp webhook: Signature validated successfully');
    next();
  } catch (error) {
    console.error('WhatsApp webhook signature validation error:', error);
    return res.status(500).json({ error: 'Signature validation failed' });
  }
};

// Raw body capture middleware for signature validation
const captureRawBody = (req, res, next) => {
  if (req.method === 'POST') {
    let data = '';
    req.on('data', chunk => {
      data += chunk;
    });
    req.on('end', () => {
      req.rawBody = data;
      next();
    });
  } else {
    next();
  }
};

// Lightweight middleware for webhook endpoints (no rate limiting for Meta's servers)
const webhookMiddleware = [
  captureRawBody,
  validateSignature,
];

// Full middleware for other WhatsApp endpoints
const whatsappMiddleware = [
  rateLimiter,
  ipWhitelist,
  validatePhoneNumber,
  validateSignature,
];

module.exports = {
  whatsappMiddleware,
  webhookMiddleware,
  rateLimiter,
  ipWhitelist,
  validatePhoneNumber,
  validateSignature,
  captureRawBody,
};