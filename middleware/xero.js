const { XeroClient } = require("xero-node");

// Define required scopes
const XERO_SCOPES = [
  'offline_access',
  'openid',
  'profile',
  'email',
  'accounting.transactions',
  'accounting.settings',
  'accounting.contacts'
].join(' ');

// Initialize Xero client configuration
const xeroConfig = {
  clientId: process.env.XERO_CLIENT_ID,
  clientSecret: process.env.XERO_CLIENT_SECRET,
  redirectUris: [process.env.XERO_REDIRECT_URI],
  scopes: XERO_SCOPES.split(" "),
  state: null, // Will be set dynamically
  defaultChecks: ["state", "pkce"],
  clockTolerance: 5,
  responseTypes: ["code"],
  tokenEndpointAuthMethod: "client_secret_post",
  grantTypes: ["authorization_code", "refresh_token"],
};

// Create Xero client instance with optional state
const getXeroClient = (state = null) => {
  const config = { ...xeroConfig };
  if (state) {
    config.state = state;
  }
  return new XeroClient(config);
};

const xeroCallbackMiddleware = async (req, res, next) => {
  console.log("=== Xero Callback Middleware ===");
  console.log("Full URL:", req.url);
  console.log("Query Parameters:", req.query);
  console.log("Session:", {
    id: req.sessionID,
    state: req.session?.xeroState,
    companyCode: req.session?.companyCode,
  });

  try {
    // Ensure session exists
    if (!req.session) {
      console.error("No session available");
      return res.status(500).json({ error: "Session unavailable" });
    }

    // Get URL parameters
    const url = new URL(req.url, `http://${req.headers.host}`);
    const urlState = url.searchParams.get("state");
    const code = url.searchParams.get("code");

    console.log("[Xero Integration] Callback State Check", {
      urlState,
      sessionState: req.session.xeroState,
      hasXeroState: !!req.session.xeroState,
    });

    // Verify state matches session state
    if (
      !urlState ||
      !req.session.xeroState ||
      urlState !== req.session.xeroState
    ) {
      console.error("State mismatch", {
        urlState,
        sessionState: req.session.xeroState,
      });
      return res.status(400).json({ error: "Invalid state parameter" });
    }

    // Initialize Xero client with state
    const xero = getXeroClient(req.session.xeroState);
    await xero.initialize();

    // Exchange code for tokens
    const tokenSet = await xero.callback(req.url, {
      code_verifier: req.session.codeVerifier,
      state: req.session.xeroState,
      response_type: "code",
    });

    // Clean up session
    delete req.session.xeroState;
    delete req.session.xeroConnectStarted;
    delete req.session.codeVerifier;
    delete req.session.xeroAuthParams;

    // Store tokens in session
    req.session.xeroTokenSet = tokenSet;

    // Save session before redirect
    await new Promise((resolve, reject) => {
      req.session.save((err) => {
        if (err) {
          console.error("Session save error:", err);
          reject(err);
        } else {
          resolve();
        }
      });
    });

    // Get return URL from session or construct default
    const companyCode = req.session?.companyCode;
    const defaultReturnUrl = companyCode
      ? `/clients/${companyCode}/settings/accounting`
      : "/settings/accounting";

    // Send redirect response
    res.redirect(defaultReturnUrl);
  } catch (error) {
    console.error("Xero callback error:", error);
    res.status(500).json({
      error: "Failed to process Xero callback",
      details: error.message,
    });
  }
};

module.exports = {
  xeroCallbackMiddleware,
  getXeroClient,
  XERO_SCOPES
};
