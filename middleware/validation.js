const validateEmployerDetails = (req, res, next) => {
  console.log("\n=== Employer Details Validation ===");

  try {
    const { tradingName, registeredName, registrationNumber, physicalAddress } =
      req.body;

    const errors = [];

    // Basic field validation
    if (!tradingName?.trim()) errors.push("Trading name is required");
    if (!registeredName?.trim()) errors.push("Registered name is required");

    // Registration Number Format (YYYY/XXXXXX/XX)
    if (!registrationNumber?.match(/^\d{4}\/\d{6}\/\d{2}$/)) {
      errors.push("Registration number must be in format: YYYY/XXXXXX/XX");
    }

    // Parse physical address from form data
    const address = {
      street: physicalAddress?.street || "",
      suburbDistrict: physicalAddress?.suburbDistrict || "",
      cityTown: physicalAddress?.cityTown || "",
      code: physicalAddress?.code || "",
    };

    // Address validation
    if (!address.street?.trim()) errors.push("Street address is required");
    if (!address.suburbDistrict?.trim())
      errors.push("Suburb/District is required");
    if (!address.cityTown?.trim()) errors.push("City/Town is required");
    if (!address.code?.match(/^\d{4}$/))
      errors.push("Postal code must be 4 digits");

    if (errors.length > 0) {
      console.log("Validation errors:", errors);
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    // If validation passes, format the data for the next middleware
    req.validatedData = {
      tradingName,
      registeredName,
      registrationNumber,
      physicalAddress: {
        ...physicalAddress,
        // Ensure all address fields exist
        unitNumber: physicalAddress?.unitNumber || "",
        complex: physicalAddress?.complex || "",
        streetNumber: physicalAddress?.streetNumber || "",
        street: address.street,
        suburbDistrict: address.suburbDistrict,
        cityTown: address.cityTown,
        code: address.code,
      },
    };

    next();
  } catch (error) {
    console.error("Validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error processing form data",
    });
  }
};

module.exports = { validateEmployerDetails };
