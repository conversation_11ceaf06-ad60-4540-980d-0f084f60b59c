const RedisManager = require("../config/redis");

async function syncRedisSession(req, res, next) {
  if (!process.env.REDIS_URL) {
    console.warn('[redisSession] REDIS_URL not set. Skipping Redis session sync.');
    return next();
  }
  const originalEnd = res.end;

  res.end = async function (...args) {
    if (req.session) {
      try {
        await RedisManager.setSession(req.sessionID, req.session);

        if (req.path === "/verify-2fa" && req.method === "POST") {
          const twoFAState = await RedisManager.get2FAState(req.sessionID);
          if (twoFAState && twoFAState.verified) {
            req.session.twoFactorVerified = true;
          }
        }
      } catch (error) {
        console.error("Error syncing session to Redis:", error);
      }
    }
    originalEnd.apply(res, args);
  };

  next();
}

module.exports = syncRedisSession;
