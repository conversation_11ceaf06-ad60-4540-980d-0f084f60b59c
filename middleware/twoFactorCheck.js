const TwoFactorStateManager = require("../services/twoFactorStateManager");

async function checkTwoFactorState(req, res, next) {
  if (req.session.twoFactorPending) {
    const state = await TwoFactorStateManager.getState(req.sessionID);
    if (state) {
      return res.redirect("/verify-2fa");
    }
    delete req.session.twoFactorPending;
    await req.session.save();
  }
  next();
}

module.exports = checkTwoFactorState;
