const Invoice = require('../models/invoice');

async function checkBillingRestrictions(req, res, next) {
  try {
    // Skip for certain routes
    const unrestrictedRoutes = [
      '/billing/pay',
      '/billing/preferences',
      '/billing/invoices',
      '/billing/payment',
      '/billing/statement',
      '/billing/restricted',
      '/billing/payment-restricted',
      '/auth/logout',
      '/login',
      '/auth/'
    ];

    if (unrestrictedRoutes.some(route => req.path.startsWith(route))) {
      return next();
    }

    // Get user's company
    const company = req.user?.currentCompany;
    if (!company) {
      return next();
    }

    // Find overdue invoices
    const overdueInvoices = await Invoice.find({
      company: company._id,
      status: { $ne: 'paid' },
      dueDate: { $lt: new Date() }
    }).sort({ dueDate: 1 });

    if (overdueInvoices.length > 0) {
      const oldestOverdueInvoice = overdueInvoices[0];
      const daysOverdue = oldestOverdueInvoice.getDaysOverdue();

      if (oldestOverdueInvoice.shouldRestrictAccount()) {
        // Store restriction info in session for template access
        req.session.billingRestricted = true;
        req.session.paymentRestriction = {
          isRestricted: true,
          type: 'payment_overdue',
          daysOverdue: daysOverdue,
          invoice: {
            number: oldestOverdueInvoice.number,
            amount: oldestOverdueInvoice.amount,
            dueDate: oldestOverdueInvoice.dueDate
          },
          reason: `Payment is ${daysOverdue} days overdue`
        };

        // Make restriction data available to templates
        res.locals.paymentRestriction = req.session.paymentRestriction;

        // For non-GET requests, block them unless they're billing-related
        if (req.method !== 'GET' && !req.path.startsWith('/billing/')) {
          if (req.xhr || req.headers.accept?.includes('application/json')) {
            return res.status(402).json({
              error: 'Account restricted due to overdue payment',
              restriction: req.session.paymentRestriction
            });
          }

          // For form submissions, redirect to payment restricted page
          req.flash('error', `Your account is restricted due to payment being ${daysOverdue} days overdue. Please update your payment to restore full access.`);
          return res.redirect('/billing/payment-restricted');
        }

        // For GET requests to non-billing pages, redirect to payment restricted page
        if (!req.path.startsWith('/billing/') && !req.path.startsWith('/dashboard') && !req.path.startsWith('/api/')) {
          return res.redirect('/billing/payment-restricted');
        }

        // For allowed GET requests, add restriction context
        req.paymentRestriction = req.session.paymentRestriction;
      }
    } else {
      // Clear any existing restrictions if no overdue invoices
      req.session.billingRestricted = false;
      req.session.paymentRestriction = null;
      res.locals.paymentRestriction = null;
    }

    next();
  } catch (error) {
    console.error('Error checking billing restrictions:', error);
    next();
  }
}

module.exports = checkBillingRestrictions;
