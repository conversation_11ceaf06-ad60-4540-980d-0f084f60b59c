const csrfDebug = (req, res, next) => {
  const originalCsrfToken = req.csrfToken;
  req.csrfToken = function () {
    const token = originalCsrfToken.apply(this);
    console.log("\n=== CSRF Token Generation ===");
    console.log({
      path: req.path,
      method: req.method,
      tokenGenerated: !!token,
      cookiePresent: !!req.cookies["_csrf"],
    });
    return token;
  };
  next();
};

module.exports = { csrfDebug };
