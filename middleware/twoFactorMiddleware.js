const TwoFactorService = require("../services/twoFactorService");
const { saveSessionPromise } = require("../utils/sessionHelpers");

exports.initializeTwoFactor = async (req, res, next) => {
  try {
    const { user } = req;

    if (!user.twoFactorEnabled || !user.twoFactorSecret) {
      return next();
    }

    const redirectUrl =
      user.companies?.length > 0
        ? user.currentCompany
          ? `/clients/${user.currentCompany}/dashboard`
          : "/companies/select"
        : "/companies/create";

    const { token, verification, session } =
      await TwoFactorService.createVerification(user, redirectUrl, req);

    // Store minimal data in session
    req.session.twoFactorToken = token;
    req.session.twoFactorPending = true;
    req.session.verificationId = verification._id;
    await saveSessionPromise(req.session);

    res.redirect("/verify-2fa");
  } catch (error) {
    console.error("2FA initialization error:", error);
    next(error);
  }
};

exports.verifyTwoFactor = async (req, res, next) => {
  try {
    if (!req.session.twoFactorToken || !req.session.twoFactorPending) {
      return res.redirect("/login");
    }

    const result = await TwoFactorService.verifyToken(
      req.session.twoFactorToken,
      req.session.passport.user
    );

    if (!result) {
      delete req.session.twoFactorToken;
      delete req.session.twoFactorPending;
      await req.session.save();
      return res.redirect("/login");
    }

    req.twoFactorVerification = result;
    next();
  } catch (error) {
    next(error);
  }
};
