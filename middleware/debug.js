const debugAuth = (req, res, next) => {
  console.log("\n=== Auth Debug ===");
  console.log({
    path: req.path,
    method: req.method,
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated(),
    user: req.user
      ? {
          id: req.user._id,
          email: req.user.email,
          onboardingStep: req.user.onboardingStep,
        }
      : null,
    session: {
      passport: req.session?.passport,
      returnTo: req.session?.returnTo,
      twoFactorAuthenticated: req.session?.twoFactorAuthenticated,
    },
  });
  next();
};

const debugTemplateRendering = (req, res, next) => {
  const originalRender = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== Template Rendering Debug ===");
    console.log("View:", view);
    console.log("Options:", {
      hasUser: !!options?.user,
      hasCompany: !!options?.company,
      hasMessages: !!options?.messages,
      hasCsrfToken: !!options?.csrfToken,
      messageKeys: options?.messages ? Object.keys(options.messages) : [],
      flashKeys: options?.flash ? Object.keys(options.flash) : [],
    });

    // Ensure messages object exists
    options.messages = options.messages || { error: [], success: [], info: [] };

    return originalRender.call(this, view, options, callback);
  };
  next();
};

const debugTemplateData = (req, res, next) => {
  const originalRender = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== Template Data Debug ===");
    console.log("View:", view);
    console.log("Data:", {
      user: options?.user
        ? {
            id: options.user._id,
            email: options.user.email,
          }
        : null,
      company: options?.company
        ? {
            id: options.company._id,
            code: options.company.companyCode,
          }
        : null,
      employerDetails: options?.employerDetails
        ? {
            exists: true,
            fields: Object.keys(options.employerDetails),
          }
        : null,
      csrfToken: !!options?.csrfToken,
      messages: options?.messages ? Object.keys(options.messages) : null,
    });
    return originalRender.call(this, view, options, callback);
  };
  next();
};

const templateDebug = (req, res, next) => {
  const originalRender = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== Template Render Debug ===");
    console.log("View:", view);
    console.log("Data:", {
      employerDetails: options?.employerDetails
        ? {
            exists: true,
            fields: Object.keys(options.employerDetails),
          }
        : "undefined",
      user: options?.user
        ? {
            id: options.user._id,
            email: options.user.email,
          }
        : "undefined",
      messages: options?.messages ? Object.keys(options.messages) : "undefined",
      csrfToken: options?.csrfToken ? "present" : "undefined",
    });
    return originalRender.call(this, view, options, callback);
  };
  next();
};

const layoutDebug = (req, res, next) => {
  const originalRender = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== Layout Rendering Debug ===");
    console.log("View:", view);
    console.log("Layout:", options?.layout);
    console.log("Data:", {
      hasUser: !!options?.user,
      hasCompany: !!options?.company,
      companyDetails: options?.company
        ? {
            id: options.company._id,
            name: options.company.name,
            code: options.company.companyCode,
          }
        : null,
      hasMessages: !!options?.messages,
      hasCsrfToken: !!options?.csrfToken,
    });
    return originalRender.call(this, view, options, callback);
  };
  next();
};

module.exports = {
  debugAuth,
  debugTemplateRendering,
  debugTemplateData,
  templateDebug,
  layoutDebug,
};
