const { isFeatureAvailable, getFeatureStatus } = require('../config/featureFlags');

const checkFeatureAccess = (featureKey) => {
  return (req, res, next) => {
    if (isFeatureAvailable(featureKey)) {
      return next();
    }

    // Store the attempted feature access for displaying a message
    req.flash('error', `This feature is currently under development (${getFeatureStatus(featureKey)}% complete) and not available in production yet.`);
    
    // Redirect to the previous page or dashboard
    return res.redirect(req.headers.referer || '/dashboard');
  };
};

// Middleware to add feature flags to all templates
const addFeatureFlagsToTemplates = (req, res, next) => {
  res.locals.isFeatureAvailable = isFeatureAvailable;
  res.locals.getFeatureStatus = getFeatureStatus;
  next();
};

module.exports = {
  checkFeatureAccess,
  addFeatureFlagsToTemplates
}; 