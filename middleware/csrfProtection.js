 const csrf = require('csurf');

// Create CSRF protection middleware with cookie-based tokens
const csrfProtection = csrf({
    cookie: {
        key: '_csrf',
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
    }
});

// Debug wrapper for CSRF protection
const csrfDebugWrapper = (req, res, next) => {
    console.log('\n=== CSRF Protection Debug ===');
    console.log('Request details:', {
        path: req.path,
        method: req.method,
        hasCSRFCookie: !!req.cookies?._csrf,
        csrfToken: req.headers['csrf-token'] || req.headers['x-csrf-token']
    });

    csrfProtection(req, res, (err) => {
        if (err) {
            console.error('CSRF Error:', {
                message: err.message,
                code: err.code,
                type: err.type
            });
            return next(err);
        }
        next();
    });
};

// Export both the raw protection and debug wrapper
module.exports = process.env.NODE_ENV === 'development' ? csrfDebugWrapper : csrfProtection;