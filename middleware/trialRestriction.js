const User = require('../models/user');
const Invoice = require('../models/invoice');
const moment = require('moment');

/**
 * Trial restriction middleware
 * Makes the application read-only if the trial period has expired and 
 * payment hasn't been made within 7 days after trial expiration
 */
async function checkTrialRestriction(req, res, next) {
  try {
    // Testing flags - set to true to enable quick testing with short periods
    const testMode = true; // Change to false in production
    
    // Skip for authenticated routes, public pages, login, and billing pages
    const unrestrictedRoutes = [
      '/login',
      '/auth/',
      '/billing/preferences',
      '/billing/payment',
      '/billing/invoices',
      '/billing/statement'
    ];
    
    // Check if route should be unrestricted
    if (unrestrictedRoutes.some(route => req.path.startsWith(route))) {
      return next();
    }

    // Skip if no authenticated user or no current company
    if (!req.isAuthenticated() || !req.user?.currentCompany?._id) {
      return next();
    }

    // Get the user with company info
    const user = req.user;
    const company = user.currentCompany;
    
    // Calculate trial expiration
    const trialDuration = testMode ? 1 : 30; // 1 minute in test mode, 30 days in production
    const gracePeriod = testMode ? 1 : 7; // 1 minute in test mode, 7 days in production
    
    let trialRestriction = {
      isRestricted: false,
      reason: null,
      trialEndDate: null,
      gracePeriodEndDate: null,
      daysOverdue: 0,
      testMode: testMode // Add test mode flag to show in templates
    };

    // Check if in trial period
    if (user && user.createdAt) {
      const userCreatedAt = new Date(user.createdAt);
      
      // In test mode, use minutes instead of days for quicker testing
      const trialEndDate = new Date(userCreatedAt);
      if (testMode) {
        trialEndDate.setMinutes(trialEndDate.getMinutes() + trialDuration);
      } else {
        trialEndDate.setDate(trialEndDate.getDate() + trialDuration);
      }
      
      const gracePeriodEndDate = new Date(trialEndDate);
      if (testMode) {
        gracePeriodEndDate.setMinutes(gracePeriodEndDate.getMinutes() + gracePeriod);
      } else {
        gracePeriodEndDate.setDate(gracePeriodEndDate.getDate() + gracePeriod);
      }
      
      const now = new Date();
      
      // Store dates for use in templates
      trialRestriction.trialEndDate = trialEndDate;
      trialRestriction.gracePeriodEndDate = gracePeriodEndDate;
      
      // Check if trial expired and grace period exceeded
      if (now > gracePeriodEndDate) {
        // Check if there's a paid invoice after trial end
        const paidInvoice = await Invoice.findOne({
          company: company._id,
          status: 'paid',
          paymentDate: { $gt: trialEndDate }
        });
        
        if (!paidInvoice) {
          // Calculate days or minutes overdue
          if (testMode) {
            trialRestriction.daysOverdue = Math.floor((now - gracePeriodEndDate) / (1000 * 60)); // Minutes in test mode
          } else {
            trialRestriction.daysOverdue = Math.floor((now - gracePeriodEndDate) / (1000 * 60 * 60 * 24)); // Days in production
          }
          trialRestriction.isRestricted = true;
          trialRestriction.reason = 'Trial period and grace period have expired without payment';
        }
      }
    }
    
    // Store restriction info in request and make available to templates
    req.trialRestriction = trialRestriction;
    res.locals.trialRestriction = trialRestriction;
    
    // If restricted, make the system read-only
    if (trialRestriction.isRestricted) {
      // Allow GET requests (read-only)
      if (req.method !== 'GET' && !req.path.startsWith('/billing/')) {
        // If it's an API request, return a JSON response
        if (req.xhr || req.headers.accept?.includes('application/json')) {
          return res.status(402).json({
            error: 'Account restricted',
            message: 'Your trial period has expired. Please update your billing information to continue using all features.',
            trialRestriction
          });
        }
        
        // For form submissions, redirect to billing page with warning
        req.flash('error', 'Your trial period has expired. Please update your billing information to continue using all features.');
        return res.redirect('/billing/preferences');
      }
    }
    
    next();
  } catch (error) {
    console.error('Error in trial restriction middleware:', error);
    next(); // Continue in case of error
  }
}

module.exports = checkTrialRestriction; 