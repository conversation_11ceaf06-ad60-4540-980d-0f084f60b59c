const sessionMonitor = (req, res, next) => {
  const originalSave = req.session.save;
  req.session.save = function (cb) {
    console.log("\n=== Session Save Called ===");
    console.log("Stack trace:", new Error().stack);
    console.log("Session data before save:", {
      ...this,
      cookie: this.cookie ? "Present" : "Missing",
    });

    return originalSave.call(this, (err) => {
      if (err) {
        console.error("Session save error:", err);
      } else {
        console.log("Session saved successfully");
      }
      if (cb) cb(err);
    });
  };

  next();
};

module.exports = sessionMonitor;
