const SupportAccess = require('../models/SupportAccess');
const Company = require('../models/Company');

/**
 * Middleware to check if there's valid support access for a company
 * This should be used in conjunction with company-specific routes
 */
const checkSupportAccess = async (req, res, next) => {
  try {
    console.log("\n=== Support Access Check ===");
    console.log({
      path: req.path,
      originalUrl: req.originalUrl,
      companyCode: req.params.companyCode,
      query: req.query,
      supportToken: req.query.supportToken
    });

    // Skip check if user is already authenticated and has access to the company
    if (req.isAuthenticated() && req.user.currentCompany) {
      console.log("User is authenticated with company access, skipping support token check");
      return next();
    }

    // Check for support access token in query parameters
    const { supportToken } = req.query;
    if (!supportToken) {
      console.log("No support token provided, redirecting to login");
      return res.status(403).redirect('/login');
    }

    // Get company code from URL parameters
    const companyCode = req.params.companyCode;
    if (!companyCode) {
      console.log("No company code in URL, redirecting to login");
      return res.status(400).redirect('/login');
    }

    console.log(`Looking for company with code: ${companyCode}`);
    
    // Try to find the company by company code (case insensitive)
    // First try exact match
    let company = await Company.findOne({ companyCode: companyCode });
    
    // If not found, try case-insensitive match
    if (!company) {
      console.log(`Company not found with exact match, trying case-insensitive match`);
      company = await Company.findOne({ 
        companyCode: { $regex: new RegExp(`^${companyCode}$`, 'i') } 
      });
    }
    
    if (!company) {
      console.log(`Company with code ${companyCode} not found`);
      req.flash('error', 'Company not found');
      return res.status(404).redirect('/login');
    }

    console.log(`Found company: ${company.name} (${company._id})`);

    // Verify the support access token
    const supportAccess = await SupportAccess.findOne({
      token: supportToken,
      company: company._id,
      isRevoked: false,
      expiresAt: { $gt: new Date() }
    });

    if (!supportAccess) {
      console.log("Support access token is invalid or expired");
      req.flash('error', 'Support access token is invalid or expired');
      return res.status(403).redirect('/login');
    }

    console.log("Valid support access token found");

    // Add support context to the request
    req.supportAccess = true;
    req.supportCompany = company;
    
    next();
  } catch (error) {
    console.error('Error checking support access:', error);
    res.status(500).redirect('/login');
  }
};

module.exports = { checkSupportAccess }; 