const User = require('../models/user');

/**
 * Middleware to ensure user companies are populated
 * This prevents the "companies not populated" error in routes that need company access
 */
const populateUserCompanies = async (req, res, next) => {
    try {
        if (req.isAuthenticated() && req.user) {
            // Only populate if companies aren't already populated
            if (!req.user.companies || !req.user.companies[0]?.name) {
                const populatedUser = await User.findById(req.user._id)
                    .populate('companies')
                    .populate('currentCompany')
                    .populate('defaultCompany');
                
                if (populatedUser) {
                    req.user = populatedUser;
                }
            }
        }
        next();
    } catch (error) {
        console.error('Error populating user companies:', error);
        next();
    }
};

module.exports = populateUserCompanies; 