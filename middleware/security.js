const Session = require("../models/session");
const LoginHistory = require("../models/loginHistory");
const geoip = require("geoip-lite");
const UAParser = require("ua-parser-js");

async function trackLogin(req, res, next) {
  try {
    const parser = new UAParser(req.headers["user-agent"]);
    const userAgent = parser.getResult();
    const ip = req.ip || req.connection.remoteAddress;
    const geo = geoip.lookup(ip);

    // Create login history entry
    await LoginHistory.create({
      user: req.user.id,
      ipAddress: ip,
      userAgent: req.headers["user-agent"],
      location: geo ? `${geo.city}, ${geo.country}` : "Unknown",
      success: true,
    });

    // Create or update session
    await Session.create({
      user: req.user.id,
      sessionId: req.sessionID,
      deviceType: getDeviceType(userAgent),
      deviceName: getDeviceName(userAgent),
      userAgent: req.headers["user-agent"],
      ipAddress: ip,
      location: geo ? `${geo.city}, ${geo.country}` : "Unknown",
    });

    next();
  } catch (error) {
    console.error("Error tracking login:", error);
    next();
  }
}

function getDeviceType(userAgent) {
  if (userAgent.device.type) return userAgent.device.type;
  if (userAgent.device.model) return "mobile";
  return "desktop";
}

function getDeviceName(userAgent) {
  return `${userAgent.browser.name} on ${userAgent.os.name}`;
}

exports.handleCSRFError = (err, req, res, next) => {
  if (err.code === "EBADCSRFTOKEN") {
    // Handle CSRF token errors
    if (req.xhr || req.headers.accept.includes("json")) {
      return res.status(403).json({
        error: "Invalid CSRF token. Please refresh the page and try again.",
      });
    }
    // For regular form submissions
    req.flash("error", "Invalid form submission. Please try again.");
    return res.redirect("back");
  }
  next(err);
};

module.exports = { trackLogin };
