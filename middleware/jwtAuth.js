/**
 * JWT Authentication Middleware
 * 
 * This middleware provides JWT authentication capabilities to routes.
 * It can be used alongside session-based authentication as a parallel 
 * authentication method, or as the primary authentication method.
 */

const { verifyToken, extractToken, generateToken, getCookieOptions } = require('../utils/jwtUtils');
const jwt = require('jsonwebtoken');
const User = require('../models/user');

/**
 * Middleware to protect routes with JWT authentication
 * Does not interfere with session authentication
 */
const jwtAuth = async (req, res, next) => {
  // Skip if already authenticated via session
  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }

  // Get token from cookie or Authorization header
  const token = req.cookies.jwt || 
                (req.headers.authorization && req.headers.authorization.split(' ')[1]);

  if (!token) {
    console.log('No JWT token found');
    return next(); // Allow request to continue for session auth
  }

  try {
    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Find user and exclude sensitive fields
    const user = await User.findById(decoded.id).select('-password -twoFactorSecret');
    
    if (!user) {
      console.log('JWT user not found in database');
      return next();
    }

    // Store user in request object
    req.jwtUser = user;
    
    // Create session for JWT user if needed
    if (!req.session.passport) {
      req.session.passport = { user: user._id };
      await new Promise((resolve, reject) => {
        req.session.save(err => {
          if (err) {
            console.error('Error saving session for JWT user:', err);
            reject(err);
          } else {
            resolve();
          }
        });
      });
    }

    next();
  } catch (error) {
    console.error('JWT verification error:', error);
    // Clear invalid token
    res.clearCookie('jwt');
    next();
  }
};

/**
 * Middleware that requires either JWT or session authentication
 * Enhanced with Vercel-specific fixes and better debugging
 */
const anyAuth = (req, res, next) => {
  try {
    console.log('\n=== Any Auth Middleware ===');
    console.log('Request path:', req.path);
    console.log('Is Vercel?', isVercelEnvironment());
    console.log('Cookies present:', !!req.cookies);
    if (req.cookies) {
      console.log('Cookie keys:', Object.keys(req.cookies));
      console.log('JWT cookie present:', !!req.cookies.jwt);
    }
    
    // Check session authentication
    const isSessionAuthenticated = req.isAuthenticated && req.isAuthenticated();
    console.log('Session authenticated:', isSessionAuthenticated);
    
    if (isSessionAuthenticated) {
      console.log('Using session authentication');
      return next();
    }

    // Extract and verify JWT
    const token = extractToken(req);
    console.log('JWT token extracted:', !!token);
    
    if (!token) {
      console.log('No JWT token found, clearing any invalid cookies');
      // Clear any invalid cookies
      res.clearCookie('jwt', getCookieOptions(isVercelEnvironment()));
      return res.redirect('/login');
    }

    const decoded = verifyToken(token);
    console.log('JWT verification result:', !!decoded);
    
    if (!decoded) {
      console.log('Invalid JWT token, clearing cookies');
      // Clear invalid token
      res.clearCookie('jwt', getCookieOptions(isVercelEnvironment()));
      return res.redirect('/login');
    }

    // Attach JWT user to request
    req.jwtUser = decoded;
    console.log('JWT user attached to request:', decoded.id);
    
    // For serverless - refresh the token to extend session
    const refreshedToken = generateToken(decoded);
    console.log('Token refreshed for extended session');
    
    // Set cookie with Vercel-compatible options
    res.cookie('jwt', refreshedToken, getCookieOptions(isVercelEnvironment()));
    
    // Continue to next middleware
    next();
  } catch (error) {
    console.error('Authentication Error:', error);
    // Clear cookies on error
    res.clearCookie('jwt', getCookieOptions(isVercelEnvironment()));
    res.redirect('/login');
  }
};

/**
 * Helper function to determine if running in Vercel environment
 */
function isVercelEnvironment() {
  // If explicitly self-hosted, it's not a Vercel environment
  if (process.env.IS_SELF_HOSTED === 'true') {
    return false;
  }
  return process.env.VERCEL || process.env.VERCEL_ENV || 
         (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL);
}

/**
 * Middleware that requires both JWT and session authentication
 * This provides extra security for sensitive operations
 */
const dualAuth = async (req, res, next) => {
  // Check session authentication
  if (!req.isAuthenticated || !req.isAuthenticated()) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Session authentication required'
    });
  }

  // Check JWT authentication
  const token = req.cookies.jwt || 
                (req.headers.authorization && req.headers.authorization.split(' ')[1]);

  if (!token) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'JWT authentication required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Verify that JWT and session users match
    if (decoded.id !== req.user._id.toString()) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'JWT and session user mismatch'
      });
    }

    next();
  } catch (error) {
    console.error('JWT verification error in dualAuth:', error);
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid JWT token'
    });
  }
};

/**
 * API-specific JWT authentication middleware
 * Does not redirect, always returns JSON responses
 */
const apiJwtAuth = (req, res, next) => {
  try {
    console.log('\n=== API JWT Auth Middleware ===');
    console.log('Request path:', req.path);
    console.log('Request method:', req.method);
    console.log('Cookies present:', !!req.cookies);
    console.log('Auth header present:', !!req.headers.authorization);
    
    // Extract JWT from request
    const token = extractToken(req);
    console.log('JWT token extracted:', !!token);
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'No authentication token provided'
      });
    }

    // Verify JWT
    const decoded = verifyToken(token);
    console.log('JWT verification result:', !!decoded);
    
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Invalid or expired token'
      });
    }

    // Attach JWT user to request
    req.jwtUser = decoded;
    console.log('JWT user attached:', decoded.id);
    
    // For API calls in serverless - refresh the token to extend session
    const refreshedToken = generateToken(decoded);
    console.log('Token refreshed for extended session');
    
    // Set cookie with Vercel-compatible options
    res.cookie('jwt', refreshedToken, getCookieOptions(isVercelEnvironment()));
    
    // Continue to next middleware
    next();
  } catch (error) {
    console.error('API JWT Authentication Error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication error',
      error: error.message
    });
  }
};

// Export middleware functions
module.exports = {
  jwtAuth,
  anyAuth,
  dualAuth,
  apiJwtAuth
}; 