const RouteTracker = {
  registeredRoutes: new Set(),
  requestCounts: new Map(),

  trackRoute(clientId) {
    return (req, res, next) => {
      const routeKey = `${clientId}:${req.path}`;

      if (this.registeredRoutes.has(routeKey)) {
        return next();
      }

      console.log(`=== First time registration for route: ${routeKey} ===`);
      this.registeredRoutes.add(routeKey);
      next();
    };
  },

  trackRequests(req, res, next) {
    const sessionId = req.sessionID;
    const count = this.requestCounts.get(sessionId) || 0;

    if (count > 20) {
      this.cleanupSession(req);
      console.error("Loop detected for session:", sessionId);
      return res.status(508).send("Request loop detected");
    }

    this.requestCounts.set(sessionId, count + 1);

    // Reset count after response
    res.on("finish", () => {
      this.cleanupSession(req);
    });

    next();
  },

  cleanupSession(req) {
    const sessionId = req.sessionID;
    this.requestCounts.delete(sessionId);
    console.log(`Cleaned up session tracking for: ${sessionId}`);
  },
};

module.exports = RouteTracker;
