const Company = require("../models/Company");

module.exports = async (req, res, next) => {
  try {
    if (req.user && req.user.currentCompany) {
      req.company = await Company.findById(req.user.currentCompany);
      if (!req.company) {
        console.log(`Company not found for ID: ${req.user.currentCompany}`);
      }
    }
    next();
  } catch (error) {
    console.error("Error in setCurrentCompany middleware:", error);
    next(error);
  }
};
