const sessionSerializer = (req, res, next) => {
  const originalSave = req.session.save;

  req.session.save = function (cb) {
    // Preserve important session data
    const preservedData = {
      twoFactorToken: this.twoFactorToken,
      twoFactorPending: this.twoFactorPending,
      userId: this.userId,
      passport: this.passport,
    };

    // Call original save
    originalSave.call(this, (err) => {
      if (!err) {
        // Restore preserved data
        Object.assign(this, preservedData);
      }
      if (cb) cb(err);
    });
  };

  next();
};

module.exports = sessionSerializer;
