function checkRole(roles) {
  return (req, res, next) => {
    if (!req.user || !req.user.role) {
      return res.status(403).send("Access denied");
    }

    const userRole = req.user.role.name || req.user.role; // Handle both populated and unpopulated roles

    if (Array.isArray(roles) ? roles.includes(userRole) : userRole === roles) {
      next();
    } else {
      res.status(403).send("Access denied");
    }
  };
}

module.exports = { checkRole };
