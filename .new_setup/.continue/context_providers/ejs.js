const fs = require('fs');
const ejs = require('ejs');

module.exports = {
  name: 'ejs',
  async getContext(files) {
    const partials = {};
    
    files.forEach(file => {
      if (file.endsWith('.ejs')) {
        const content = fs.readFileSync(file, 'utf8');
        partials[file] = ejs.compile(content);
      }
    });

    return {
      ejsPartials: partials,
      helpers: {
        include: (path) => partials[path]?.()
      }
    };
  }
};