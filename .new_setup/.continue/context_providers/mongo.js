const mongoose = require('mongoose');

module.exports = {
  name: 'mongo',
  async getContext() {
    const schemas = {};
    
    // Auto-detect Mongoose models
    mongoose.modelNames().forEach(modelName => {
      schemas[modelName] = mongoose.model(modelName).schema.obj;
    });

    return {
      mongoSchemas: schemas,
      dbStatus: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
    };
  }
};