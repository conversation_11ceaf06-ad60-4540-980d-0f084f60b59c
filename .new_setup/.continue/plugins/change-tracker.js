const vscode = require('vscode');
const difflib = require('difflib');

const changeHistory = new Map();

module.exports = {
  name: "ChangeTracker",
  hooks: {
    preApplyDiff: (diff, filepath) => {
      const doc = vscode.workspace.textDocuments.find(d => d.fileName === filepath);
      const original = doc?.getText() || "";
      const modified = difflib.patch(original, diff);
      
      const changes = difflib.unifiedDiff(original.split('\n'), modified.split('\n'));
      const stats = {
        added: changes.filter(l => l.startsWith('+')).length,
        removed: changes.filter(l => l.startsWith('-')).length
      };
      
      const changeId = Date.now().toString();
      changeHistory.set(changeId, { diff, original, filepath });
      
      return { id: changeId, ...stats };
    }
  },
  commands: {
    revertChange: (changeId) => {
      const change = changeHistory.get(changeId);
      if (!change) return;
      
      const doc = vscode.workspace.textDocuments.find(d => d.fileName === change.filepath);
      const edit = new vscode.WorkspaceEdit();
      edit.replace(
        doc.uri,
        new vscode.Range(0, 0, doc.lineCount, 0),
        change.original
      );
      
      vscode.workspace.applyEdit(edit);
      changeHistory.delete(changeId);
    }
  }
};