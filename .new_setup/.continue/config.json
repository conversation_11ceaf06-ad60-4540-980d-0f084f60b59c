{"models": [{"title": "DeepSeek Coder", "provider": "openai", "model": "deepseek-chat", "apiKey": "***********************************", "apiBase": "https://api.deepseek.com/v1", "contextLength": 128000, "completionOptions": {"temperature": 0.2, "maxTokens": 4096}}, {"title": "Claude 3.5 Sonnet", "provider": "anthropic", "model": "claude-3-5-sonnet-20240620", "apiKey": "************************************************************************************************************", "contextLength": 200000, "completionOptions": {"temperature": 0.3}}, {"title": "Gemini 1.5 Flash", "provider": "google", "model": "gemini-1.5-flash-latest", "apiKey": "AIzaSyA70C1r6qtiS3c-GTsxehot7yJgT35VpMM", "contextLength": 1000000, "completionOptions": {"temperature": 0.3}}], "contextProviders": [{"name": "file", "patterns": ["**/*.js", "**/*.ejs", "**/*.css"]}, {"name": "mongo", "script": "./.continue/context_providers/mongo.js"}, {"name": "ejs", "script": "./.continue/context_providers/ejs.js"}], "ui": {"sidebarView": "webview", "position": "right", "diffPreview": true}, "autopilot": {"autoApplyDiffs": false, "confirmApplyAll": true}, "experimental": {"renderMarkdown": "(props) => `\n<div class=\"cursor-style-change\">\n  <div class=\"diff-stats\">\n    🟢 ${props.diff?.added || 0} additions\n    🔴 ${props.diff?.removed || 0} deletions\n  </div>\n  ${props.children}\n  <button onclick=\"window.acceptChange('${props.id}')\">Accept</button>\n  <button onclick=\"window.revertChange('${props.id}')\">Revert</button>\n</div>\n`", "css": ".cursor-style-change {\n  border: 1px solid #2ecc7133;\n  border-radius: 8px;\n  padding: 12px;\n  margin: 8px 0;\n}\n.diff-stats {\n  color: #95a5a6;\n  font-size: 0.9em;\n  margin-bottom: 8px;\n}\nbutton {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  margin: 4px;\n  cursor: pointer;\n}"}, "git": {"autoCommit": true, "commitMessage": "AI change ${timestamp}", "preCommitHook": "npm run lint"}}