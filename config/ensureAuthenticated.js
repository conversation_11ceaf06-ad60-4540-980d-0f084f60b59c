function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.redirect("/login");
}

function ensureOwner(req, res, next) {
  if (req.isAuthenticated() && req.user.role.name === "owner") {
    return next();
  }
  req.flash("error", "You don't have permission to view this resource");
  res.redirect("/login");
}

module.exports = { ensureAuthenticated, ensureOwner };
