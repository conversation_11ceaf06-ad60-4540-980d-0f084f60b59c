const User = require("../models/user");

module.exports = {
  ensureAuthenticated: async (req, res, next) => {
    console.log('\n=== Enhanced Authentication Check ===');
    console.log('Path:', req.path);
    console.log('Session Auth:', req.isAuthenticated());
    console.log('JWT User:', !!req.jwtUser);
    console.log('Session ID:', req.sessionID);
    console.log('Session Passport:', req.session?.passport);

    // Check session authentication first
    if (req.isAuthenticated()) {
      console.log('✓ Session authentication successful');
      try {
        // Get fresh user data from database
        const user = await User.findById(req.user._id).populate(
          "currentCompany"
        );

        if (!user.currentCompany) {
          req.flash("error", "No company associated with account");
          return res.redirect("/login");
        }

        const companyCode = user.currentCompany.companyCode;

        // Check if onboarding is complete and user is trying to access onboarding pages
        if (user.onboardingComplete && req.path.includes("/onboarding")) {
          return res.redirect(`/clients/${companyCode}/dashboard`);
        }

        // Check if onboarding is incomplete and user is trying to access other pages
        if (
          !user.onboardingComplete &&
          !req.path.includes("/onboarding") &&
          !req.path.includes("/public") &&
          !req.path.includes("/api")
        ) {
          return res.redirect(`/clients/${companyCode}/onboarding/welcome`);
        }

        // Redirect bare /dashboard to proper company dashboard
        if (req.path === "/dashboard") {
          return res.redirect(`/clients/${companyCode}/dashboard`);
        }

        return next();
      } catch (error) {
        console.error("Error in session auth middleware:", error);
        return res.redirect("/login");
      }
    }

    // If session auth failed, check JWT authentication
    if (req.jwtUser) {
      console.log('✓ JWT authentication found, attempting to create session');
      try {
        // Get fresh user data from database using JWT user ID
        const user = await User.findById(req.jwtUser.id).populate(
          "currentCompany"
        );

        if (!user) {
          console.log('✗ JWT user not found in database');
          req.flash("error", "User account not found");
          return res.redirect("/login");
        }

        if (!user.currentCompany) {
          console.log('✗ JWT user has no company associated');
          req.flash("error", "No company associated with account");
          return res.redirect("/login");
        }

        // Set user in request object for compatibility
        req.user = user;

        // Create/update session for JWT user
        if (!req.session.passport) {
          req.session.passport = { user: user._id };
          console.log('Created session for JWT user:', user._id);
        }

        // Save session before continuing
        await new Promise((resolve, reject) => {
          req.session.save(err => {
            if (err) {
              console.error('Error saving session for JWT user:', err);
              reject(err);
            } else {
              console.log('✓ Session saved successfully for JWT user');
              resolve();
            }
          });
        });

        const companyCode = user.currentCompany.companyCode;

        // Check if onboarding is complete and user is trying to access onboarding pages
        if (user.onboardingComplete && req.path.includes("/onboarding")) {
          return res.redirect(`/clients/${companyCode}/dashboard`);
        }

        // Check if onboarding is incomplete and user is trying to access other pages
        if (
          !user.onboardingComplete &&
          !req.path.includes("/onboarding") &&
          !req.path.includes("/public") &&
          !req.path.includes("/api")
        ) {
          return res.redirect(`/clients/${companyCode}/onboarding/welcome`);
        }

        // Redirect bare /dashboard to proper company dashboard
        if (req.path === "/dashboard") {
          return res.redirect(`/clients/${companyCode}/dashboard`);
        }

        console.log('✓ JWT authentication successful, proceeding');
        return next();
      } catch (error) {
        console.error("Error in JWT auth middleware:", error);
        return res.redirect("/login");
      }
    }

    // Neither session nor JWT authentication succeeded
    console.log('✗ No valid authentication found');
    req.flash("error", "Please log in to view this resource");
    res.redirect("/login");
  },

  ensure2FA: (req, res, next) => {
    console.log("\n=== 2FA Middleware Check ===");
    console.log({
      user: req.user?._id,
      twoFactorEnabled: req.user?.twoFactorEnabled,
      twoFactorAuthenticated: req.session?.twoFactorAuthenticated,
      path: req.path,
    });

    if (!req.user?.twoFactorEnabled) {
      return next();
    }

    if (req.session?.twoFactorAuthenticated) {
      return next();
    }

    req.session.returnTo = req.originalUrl;
    res.redirect("/verify-2fa");
  },
};
