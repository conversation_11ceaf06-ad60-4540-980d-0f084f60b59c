const featureFlags = {
  // Completed features (100%)
  LOGIN: { enabled: true, completionStatus: 100 },
  EMPLOYEE_MANAGEMENT: { enabled: true, completionStatus: 100 },
  LEAVE_SETTINGS: { enabled: true, completionStatus: 100 },
  PAY_FREQUENCY_SETTINGS: { enabled: true, completionStatus: 100 },
  CUSTOM_ITEMS_SETTINGS: { enabled: true, completionStatus: 100 },
  EFT_SETTINGS: { enabled: true, completionStatus: 100 },
  ACCOUNTING_INTEGRATIONS: { enabled: true, completionStatus: 100 },
  EMPLOYEE_NUMBERS_SETTINGS: { enabled: true, completionStatus: 100 },
  EMPLOYER_DETAILS_SETTINGS: { enabled: true, completionStatus: 100 },
  EMPLOYER_FILING_SETTINGS: { enabled: true, completionStatus: 100 },
  BULK_EXCEL_IMPORTS: { enabled: true, completionStatus: 100 },
  
  // Partially complete features
  PAYROLL_PROCESSING: { enabled: true, completionStatus: 80 },
  REPORTING: { enabled: false, completionStatus: 40 },
  ROLE_MANAGEMENT: { enabled: false, completionStatus: 80 },
  OTHER_SETTINGS: { enabled: false, completionStatus: 50 },
  PAY_POINTS_SETTINGS: { enabled: false, completionStatus: 60 },
  PAYROLL_CALCULATIONS_SETTINGS: { enabled: false, completionStatus: 45 },
  PAYSLIP_SETTINGS: { enabled: false, completionStatus: 30 },
  BENEFICIARIES_SETTINGS: { enabled: false, completionStatus: 55 },
  ACCOUNTING_SPLITS: { enabled: false, completionStatus: 40 },
  JOB_GRADES_SETTINGS: { enabled: false, completionStatus: 35 },
  OID_RETURN: { enabled: false, completionStatus: 85 },
  BULK_EMPLOYEE_ACTIONS: { enabled: false, completionStatus: 65 },
  BULK_INFORMATION_INPUTS: { enabled: false, completionStatus: 45 },
  BULK_PAYROLL_INPUTS: { enabled: false, completionStatus: 40 },
  
  // Add more features as needed
};

// Minimum completion status required for a feature to be enabled in production
const MINIMUM_COMPLETION_STATUS = 100;

// Check if we're in production environment
const isProduction = process.env.NODE_ENV === 'production';

// Function to check if a feature is available
const isFeatureAvailable = (featureKey) => {
  const feature = featureFlags[featureKey];
  if (!feature) return false;
  
  if (isProduction) {
    return feature.enabled && feature.completionStatus >= MINIMUM_COMPLETION_STATUS;
  }
  
  return feature.enabled;
};

// Function to get feature completion status
const getFeatureStatus = (featureKey) => {
  return featureFlags[featureKey]?.completionStatus || 0;
};

module.exports = {
  featureFlags,
  isFeatureAvailable,
  getFeatureStatus,
  MINIMUM_COMPLETION_STATUS
}; 