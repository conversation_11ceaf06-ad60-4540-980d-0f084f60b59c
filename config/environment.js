const config = {
  production: {
    mongoURI: process.env.MONGODB_URI,
    port: process.env.PORT || 3002,
    baseUrl: process.env.BASE_URL || "https://payroll.pss-group.co.za",
    email: {
      host: "smtppro.zoho.com",
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    },
    sessionConfig: {
      cookie: {
        secure: true,
        sameSite: "lax",
      },
    },
  },
  development: {
    mongoURI: process.env.MONGODB_URI,
    port: process.env.DEV_PORT || 3001,
    baseUrl: "http://localhost:3001",
    email: {
      host: "smtppro.zoho.com",
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    },
    sessionConfig: {
      cookie: {
        secure: false,
        sameSite: "lax",
      },
    },
  },
};

module.exports = config[process.env.NODE_ENV || "production"];
