const Queue = require('bull');
const Redis = require('ioredis');

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  tls: process.env.REDIS_TLS === 'true' ? {} : undefined
};

let whatsappQueue;
let redisClient;

if (process.env.REDIS_URL) {
  // Create Redis client
  redisClient = new Redis(redisConfig);

  // Create queues
  whatsappQueue = new Queue('whatsapp-messages', {
    redis: redisConfig,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000
      },
      removeOnComplete: true,
      removeOnFail: false
    }
  });

  // Add error handlers
  whatsappQueue.on('error', (error) => {
    console.error('WhatsApp Queue Error:', error);
  });

  whatsappQueue.on('failed', (job, error) => {
    console.error(`Job ${job.id} failed:`, error);
  });
} else {
  // Fallback: fake queue for environments without Redis
  console.warn('[queue.js] REDIS_URL not set. WhatsApp queue will run jobs synchronously.');
  whatsappQueue = {
    add: async (jobName, data) => {
      console.warn(`[queue.js] Running WhatsApp job "${jobName}" synchronously (no Redis).`);
      console.log(`[queue.js] Would process job:`, jobName, data);
      await Promise.resolve(); // Simulate async operation
      return { id: 'sync-job', data };
    },
    process: (jobName, handler) => {
       console.warn(`[queue.js] Sync queue process called for ${jobName}. Handler not automatically executed.`);
    }
  };
  redisClient = null;
}

module.exports = {
  whatsappQueue,
  redisClient
}; 