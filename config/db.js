const mongoose = require("mongoose");

const connectDB = async () => {
  try {
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Standard timeout
      socketTimeoutMS: 45000,
      family: 4,
      retryWrites: true,
      retryReads: true,
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverApi: {
        version: '1',
        strict: true,
        deprecationErrors: true
      }
    };
    
    let mongoUri = process.env.MONGODB_URI;
    
    // Add connection retry logic
    let retries = 0;
    const maxRetries = 1; // Standard retries
    
    while (retries < maxRetries) {
      try {
        console.log(`MongoDB connection attempt ${retries + 1} of ${maxRetries}`);
        const conn = await mongoose.connect(mongoUri, mongoOptions);
        console.log(`MongoDB Connected: ${conn.connection.host}`);
        
        // Successfully connected
        break;
      } catch (connError) {
        retries++;
        if (retries >= maxRetries) {
          throw connError; // Re-throw if we've exhausted our retries
        }
        console.log(`Connection attempt ${retries} failed. Retrying in 2 seconds...`);
        // Wait 2 seconds before retrying
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Handle connection errors
    mongoose.connection.on("error", (err) => {
      console.error("MongoDB connection error:", err);
      process.exit(1);
    });

    mongoose.connection.on("disconnected", () => {
      console.log("MongoDB disconnected.");
      console.log("Attempting to reconnect...");
    });

    mongoose.connection.on("reconnected", () => {
      console.log("MongoDB reconnected");
    });

    // Graceful shutdown
    process.on("SIGINT", async () => {
      try {
        await mongoose.connection.close();
        console.log("MongoDB connection closed through app termination");
        process.exit(0);
      } catch (err) {
        console.error("Error during MongoDB shutdown:", err);
        process.exit(1);
      }
    });
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
    process.exit(1);
  }
};

module.exports = connectDB;
