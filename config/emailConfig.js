const nodemailer = require("nodemailer");

// Create reusable transporter object using Zoho SMTP
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST, // "smtppro.zoho.com"
  port: process.env.EMAIL_PORT, // 587
  secure: process.env.EMAIL_SECURE === "true", // false for 587, true for 465
  auth: {
    user: process.env.EMAIL_USER, // "<EMAIL>"
    pass: process.env.EMAIL_PASS, // "2n1Qv9Aqm4UP"
  },
});

// Verify connection configuration with detailed logging
transporter.verify(function (error, success) {
  if (error) {
    console.error("Email config error:", error);
    console.log("Email configuration:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === "true",
      user: process.env.EMAIL_USER ? "Set" : "Not set",
      pass: process.env.EMAIL_PASS ? "Set" : "Not set",
    });
  } else {
    console.log("Email server is ready to send messages");
    console.log("Using Zoho SMTP configuration with:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      user: process.env.EMAIL_USER,
    });
  }
});

// Add a helper function to send emails with retries
transporter.sendMailWithRetry = async function (mailOptions, maxRetries = 3) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Sending email attempt ${attempt}/${maxRetries}`, {
        to: mailOptions.to,
        subject: mailOptions.subject,
      });

      const info = await this.sendMail({
        ...mailOptions,
        from: {
          name: process.env.EMAIL_FROM_NAME || "Panda Software Solutions Group",
          address: process.env.EMAIL_USER,
        },
      });

      console.log("Email sent successfully:", {
        messageId: info.messageId,
        to: mailOptions.to,
        attempt,
      });

      return info;
    } catch (error) {
      lastError = error;
      console.error(`Email sending attempt ${attempt} failed:`, error);

      if (attempt < maxRetries) {
        console.log(`Waiting before retry ${attempt + 1}...`);
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  throw new Error(
    `Failed to send email after ${maxRetries} attempts: ${lastError.message}`
  );
};

module.exports = transporter;
