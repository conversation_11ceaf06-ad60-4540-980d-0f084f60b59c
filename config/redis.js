const Redis = require("redis");

class RedisManager {
  static memoryStore = new Map();
  static client = null;
  static useMemoryFallback = !process.env.REDIS_URL;

  static async getClient() {
    if (RedisManager.useMemoryFallback) {
      return null;
    }

    if (!RedisManager.client) {
      try {
        // Only create and connect the client if not using memory fallback
        if (!RedisManager.useMemoryFallback) {
          RedisManager.client = Redis.createClient({
            url: process.env.REDIS_URL,
            legacyMode: false,
          });
          RedisManager.client.on("error", (err) => {
            console.error("Redis Error:", err);
            // If an error occurs, switch to memory fallback for future operations
            RedisManager.useMemoryFallback = true;
            console.log('Falling back to memory storage due to Redis error');
          });
           RedisManager.client.on("connect", () => {
            console.log("Redis Connected Successfully");
          });
          await RedisManager.client.connect();
        }
      } catch (error) {
        console.error('Redis connection error during client creation:', error);
        // If initial connection fails, force memory fallback
        RedisManager.useMemoryFallback = true;
        console.log('Forcing memory fallback due to Redis connection error');
        return null; // Return null on failure
      }
    }
    // Return the client if successfully created/connected, otherwise null (due to fallback/error)
    return RedisManager.client;
  }

  // Helper to ensure connection or get fallback
  static async ensureConnection() {
      return this.getClient();
  }

  // Memory fallback methods
  static getMemoryKey(key, ttl = 300) {
    const item = RedisManager.memoryStore.get(key);
    if (!item) return null;
    // Check for expiry - although setTimeout handles cleanup, this prevents reading stale data
    if (item.expires < Date.now()) {
      RedisManager.memoryStore.delete(key);
      RedisManager.memoryStore.delete(`${key}:expiry`);
      return null;
    }
     return item;
  }

  static setMemoryKey(key, value, ttl = 300) {
    const expiryTime = Date.now() + (ttl * 1000);
    RedisManager.memoryStore.set(key, { value, expires: expiryTime });

    // Clear any existing timeout for this key before setting a new one
    if (RedisManager.memoryStore.has(`${key}:timeout`)) {
        clearTimeout(RedisManager.memoryStore.get(`${key}:timeout`));
    }

    // Set up automatic cleanup and store the timeout ID
    const timeoutId = setTimeout(() => {
      RedisManager.memoryStore.delete(key);
      RedisManager.memoryStore.delete(`${key}:expiry`);
      RedisManager.memoryStore.delete(`${key}:timeout`); // Clean up the timeout ID itself
    }, ttl * 1000);
     RedisManager.memoryStore.set(`${key}:timeout`, timeoutId);
  }

  static deleteMemoryKey(key) {
    // Clear any scheduled timeout before deleting
     if (RedisManager.memoryStore.has(`${key}:timeout`)) {
        clearTimeout(RedisManager.memoryStore.get(`${key}:timeout`));
        RedisManager.memoryStore.delete(`${key}:timeout`);
     }
    RedisManager.memoryStore.delete(key);
    RedisManager.memoryStore.delete(`${key}:expiry`);
  }

  // 2FA State Management
  static async set2FAState(sessionId, state) {
    const key = `2fa:${sessionId}`;

    const client = await this.ensureConnection();
    if (client) {
      try {
        await client.setEx(key, 300, JSON.stringify(state)); // 5 minute expiry
      } catch (error) {
        console.error('Error in set2FAState (Redis):', error);
        // Fallback to memory on Redis error for this operation
        RedisManager.setMemoryKey(key, JSON.stringify(state), 300);
      }
    } else {
      // Use memory fallback if no client connection
      RedisManager.setMemoryKey(key, JSON.stringify(state), 300);
    }
  }

  static async get2FAState(sessionId) {
    const key = `2fa:${sessionId}`;

    const client = await this.ensureConnection();
    if (client) {
      try {
        const data = await client.get(key);
        return data ? JSON.parse(data) : null;
      } catch (error) {
        console.error('Error in get2FAState (Redis):', error);
         // Fallback to memory on Redis error for this operation
        const item = RedisManager.getMemoryKey(key);
        return item && item.value ? JSON.parse(item.value) : null;
      }
    } else {
      // Use memory fallback if no client connection
      const item = RedisManager.getMemoryKey(key);
      return item && item.value ? JSON.parse(item.value) : null;
    }
  }

  static async clear2FAState(sessionId) {
    const key = `2fa:${sessionId}`;

    const client = await this.ensureConnection();
    if (client) {
       try {
        await client.del(key);
      } catch (error) {
        console.error('Error in clear2FAState (Redis):', error);
        // Fallback to memory on Redis error for this operation
         RedisManager.deleteMemoryKey(key);
      }
    } else {
      // Use memory fallback if no client connection
      RedisManager.deleteMemoryKey(key);
    }
  }

  static async verify2FAState(sessionId) {
     const client = await this.ensureConnection();
    if (client) {
      try {
        const state = await this.get2FAState(sessionId); // get2FAState already handles fallback
        if (state) {
          state.verified = true;
          await this.set2FAState(sessionId, state); // set2FAState already handles fallback
        }
      } catch (error) {
        console.error('Error in verify2FAState (Redis):', error);
        // The underlying get/set methods handle fallback, so no need for explicit memory logic here
      }
    } else {
       // If no client, get2FAState and set2FAState will use memory
      const state = await this.get2FAState(sessionId);
      if (state) {
        state.verified = true;
        await this.set2FAState(sessionId, state);
      }
    }
  }

  // Return URL Management
  static async setReturnTo(sessionId, url) {
    const key = `returnTo:${sessionId}`;

    const client = await this.ensureConnection();
     if (client) {
      try {
        await client.setEx(key, 300, url);
      } catch (error) {
         console.error('Error in setReturnTo (Redis):', error);
        // Fallback to memory on Redis error for this operation
         RedisManager.setMemoryKey(key, url, 300);
      }
    } else {
      // Use memory fallback if no client connection
      RedisManager.setMemoryKey(key, url, 300);
    }
  }

  static async getReturnTo(sessionId) {
    const key = `returnTo:${sessionId}`;

    const client = await this.ensureConnection();
    if (client) {
      try {
        return await client.get(key);
      } catch (error) {
        console.error('Error in getReturnTo (Redis):', error);
        // Fallback to memory on Redis error for this operation
        const item = RedisManager.getMemoryKey(key);
        return item ? item.value : null;
      }
    } else {
       // Use memory fallback if no client connection
      const item = RedisManager.getMemoryKey(key);
      return item ? item.value : null;
    }
  }

  static async clearReturnTo(sessionId) {
    const key = `returnTo:${sessionId}`;

    const client = await this.ensureConnection();
    if (client) {
      try {
        await client.del(key);
      } catch (error) {
        console.error('Error in clearReturnTo (Redis):', error);
        // Fallback to memory on Redis error for this operation
        RedisManager.deleteMemoryKey(key);
      }
    } else {
       // Use memory fallback if no client connection
      RedisManager.deleteMemoryKey(key);
    }
  }
}

module.exports = RedisManager;
