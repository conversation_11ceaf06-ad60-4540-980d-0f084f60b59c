<!-- newMedicalAid.ejs -->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script
      type="module"
      src="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule=""
      src="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons/ionicons.js"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />

    <link rel="stylesheet" href="/regularInputs.css" />
    <!-- Link to your CSS file -->
    <link rel="stylesheet" href="/header.css" />
    <link rel="stylesheet" href="/design.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Familjen+Grotesk&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito+Sans:opsz,wght@6..12,200&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/form.css" />

    <title>
      Add Medical Aid - <%= employee.firstName %> <%= employee.lastName %>
    </title>
  </head>
  <body>
    <%- include('partials/header') %>

    <nav><%- include('partials/sidebar') %></nav>
    <main>
      <h1>
        Add Medical Aid - <%= employee.firstName %> <%= employee.lastName %>
        Fill Form for Date: <%= formattedDate %>
      </h1>

      <form
        action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/medical-aid"
        method="POST"
        class="pay-component-form"
      >
        <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
        <input type="hidden" name="relevantDate" value="<%= relevantDate %>" />

        <label for="amount">Total amount per period:</label>
        <input
          type="number"
          id="amount"
          name="amount"
          placeholder="Enter Amount"
          step="0.01"
          required
        />
        <p>
          This is the total of the medical aid premium, including the employer
          and employee’s contribution.
        </p>

        <label for="employerContribution"
          >Portion contributed by employer:</label
        >
        <input
          type="number"
          id="employerContribution"
          name="employerContribution"
          placeholder="Enter Amount"
          step="0.01"
        />
        <p>
          The system deducts the employer portion from the Total amount per
          period to determine the employee contribution automatically.
        </p>

        <label for="employeeHandlesPayment">
          <input
            type="checkbox"
            id="employeeHandlesPayment"
            name="employeeHandlesPayment"
          />
          Employee handles the payment
        </label>

        <div id="dontApplyTaxCreditsContainer" style="display: none">
          <label>
            <input type="checkbox" name="dontApplyTaxCredits" />
            Don't apply tax credits
          </label>
        </div>

        <script>
          const employeeHandlesPaymentCheckbox = document.getElementById(
            "employeeHandlesPayment"
          );
          const dontApplyTaxCreditsContainer = document.getElementById(
            "dontApplyTaxCreditsContainer"
          );

          employeeHandlesPaymentCheckbox.addEventListener(
            "change",
            function () {
              dontApplyTaxCreditsContainer.style.display = this.checked
                ? "block"
                : "none";
            }
          );
        </script>

        <label class="label-members" for="members">
          Members (incl. employee):
        </label>
        <input
          type="number"
          id="members"
          name="members"
          placeholder="Enter Number of Members"
          required
        />

        <div class="form-buttons">
          <a
            href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>"
            class="button-links"
            >Back</a
          >
          <button type="submit">Submit</button>
        </div>
      </form>
    </main>
  </body>
</html>
