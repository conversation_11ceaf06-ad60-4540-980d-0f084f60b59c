require('dotenv').config();
const PayStack = require('paystack-node');

// Get API keys from environment variables
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_PUBLIC_KEY = process.env.PAYSTACK_PUBLIC_KEY;

// Determine environment
const isProduction = process.env.NODE_ENV === 'production';
const mode = isProduction ? 'PRODUCTION' : 'TEST';

console.log(`Initializing Paystack in ${mode} mode`);

// Verify if Paystack is configured properly
if (!PAYSTACK_SECRET_KEY) {
  console.error('Error: PAYSTACK_SECRET_KEY is not defined in environment variables');
}

// Create a Paystack instance
let paystackInstance;

try {
  paystackInstance = new PayStack(PAYSTACK_SECRET_KEY, mode);
  
  // Add a method to check if the configuration is valid
  paystackInstance.isConfigured = function() {
    return !!PAYSTACK_SECRET_KEY && PAYSTACK_SECRET_KEY.length > 10;
  };
  
  // Enhance the initializeTransaction method with better error handling
  const originalInitializeTransaction = paystackInstance.initializeTransaction;
  paystackInstance.initializeTransaction = async function(params) {
    try {
      if (!this.isConfigured()) {
        console.error('Paystack is not properly configured. Missing or invalid API keys.');
        throw new Error('Payment gateway not properly configured');
      }
      
      const rawResponse = await originalInitializeTransaction.call(this, params);
      
      // Parse the response data - the actual response is in the body property
      // This is because the paystack-node library returns the entire HTTP response
      if (!rawResponse || !rawResponse.body) {
        console.error('Invalid response from Paystack API:', rawResponse);
        throw new Error('Invalid response from payment gateway');
      }
      
      // Return the processed response in the format our code expects
      return {
        status: rawResponse.statusCode,
        data: rawResponse.body.data || {},
        message: rawResponse.body.message || ''
      };
    } catch (error) {
      console.error('Paystack API error in initializeTransaction:', error);
      
      // Rethrow the error to be handled by the caller
      throw error;
    }
  };
  
  // Enhance the verifyTransaction method as well
  const originalVerifyTransaction = paystackInstance.verifyTransaction;
  paystackInstance.verifyTransaction = async function(params) {
    try {
      if (!this.isConfigured()) {
        console.error('Paystack is not properly configured. Missing or invalid API keys.');
        throw new Error('Payment gateway not properly configured');
      }
      
      const rawResponse = await originalVerifyTransaction.call(this, params);
      
      // Parse the response data
      if (!rawResponse || !rawResponse.body) {
        console.error('Invalid response from Paystack API during verification:', rawResponse);
        throw new Error('Invalid response from payment gateway');
      }
      
      // Return the processed response
      return {
        status: rawResponse.statusCode,
        data: rawResponse.body.data || {},
        message: rawResponse.body.message || ''
      };
    } catch (error) {
      console.error('Paystack API error in verifyTransaction:', error);
      throw error;
    }
  };
  
  console.log('Paystack initialized successfully');
} catch (error) {
  console.error('Failed to initialize Paystack:', error);
  
  // Create a mock Paystack instance that will gracefully error out
  paystackInstance = {
    isConfigured: () => false,
    initializeTransaction: () => {
      throw new Error('Paystack is not properly configured');
    },
    verifyTransaction: () => {
      throw new Error('Paystack is not properly configured');
    }
  };
}

module.exports = paystackInstance; 