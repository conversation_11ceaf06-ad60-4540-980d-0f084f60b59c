const mongoose = require("mongoose");

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI;

    if (!mongoURI) {
      throw new Error("MongoDB connection string is not defined");
    }

    // Connect to MongoDB
    const conn = await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      maxPoolSize: 10,
    });

    // Get database instance
    const db = mongoose.connection.db;

    // Create or update sessions collection with proper indexes
    try {
      // Check if collection exists
      const collections = await db
        .listCollections({ name: "sessions" })
        .toArray();

      if (collections.length > 0) {
        // Drop existing indexes
        await db.collection("sessions").dropIndexes();
      } else {
        // Create collection if it doesn't exist
        await db.createCollection("sessions");
      }

      // Create new indexes
      await db
        .collection("sessions")
        .createIndex({ expires: 1 }, { expireAfterSeconds: 0 });

      await db
        .collection("sessions")
        .createIndex(
          { "session.lastAccess": 1 },
          { expireAfterSeconds: 86400 }
        );

      await db.collection("sessions").createIndex({ _id: 1 }, { unique: true });

      console.log("Session indexes created successfully");
    } catch (error) {
      console.error("Error managing session collection:", error);
    }

    console.log("MongoDB Connected:", conn.connection.host);
    return conn;
  } catch (error) {
    console.error("MongoDB connection error:", error);
    process.exit(1);
  }
};

module.exports = connectDB;
