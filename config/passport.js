const passport = require("passport");
const LocalStrategy = require("passport-local").Strategy;
const bcrypt = require("bcrypt");
const User = require("../models/user");
const LoginHistory = require("../models/loginHistory");

const SALT_ROUNDS = 12;

module.exports = function (app) {
  passport.use(
    new LocalStrategy(
      {
        usernameField: "email",
        passwordField: "password",
        passReqToCallback: true,
      },
      async (req, email, password, done) => {
        try {
          console.log("\n=== Passport Local Strategy ===");
          console.log("Attempting login for email:", email);
          console.log(
            "Password provided length:",
            password ? password.length : 0
          );

          const user = await User.findOne({ email: email.toLowerCase() });

          // Create login history entry
          const loginEntry = {
            user: user?._id,
            ipAddress: req.ip,
            userAgent: req.get("user-agent"),
            location:
              req.get("x-forwarded-for") || req.connection.remoteAddress,
            success: false,
          };

          if (!user) {
            console.log("User not found:", email);
            loginEntry.failureReason = "User not found";
            await LoginHistory.create(loginEntry);
            return done(null, false, { message: "User not found with this email address" });
          }

          // Check if user's email is verified
          if (!user.isVerified) {
            console.log("User's email is not verified:", email);
            loginEntry.failureReason = "Email not verified";
            await LoginHistory.create(loginEntry);
            return done(null, false, { 
              message: "Please verify your email before logging in. Check your inbox for the verification link." 
            });
          }

          // Check if account is locked
          if (user.accountLocked) {
            // Check if lock period has expired
            if (user.lockUntil && user.lockUntil > Date.now()) {
              console.log("Account is locked:", email);
              loginEntry.failureReason = "Account locked";
              await LoginHistory.create(loginEntry);
              return done(null, false, { 
                message: "Your account is temporarily locked due to too many failed login attempts. Please try again later." 
              });
            } else {
              // Reset lock if expired
              user.accountLocked = false;
              user.failedLoginAttempts = 0;
              user.lockUntil = undefined;
              await user.save();
            }
          }

          console.log("User found:", {
            id: user._id,
            email: user.email,
            hasPassword: !!user.password,
            storedPasswordLength: user.password ? user.password.length : 0,
            storedPasswordHash: user.password
              ? user.password.substring(0, 10) + "..."
              : null,
            passwordAlgorithm: user.password
              ? user.password.split("$")[1]
              : null,
          });

          if (!user.password) {
            console.log("User has no password stored");
            loginEntry.failureReason = "No password set";
            await LoginHistory.create(loginEntry);
            return done(null, false, { message: "Please reset your password to continue" });
          }

          try {
            console.log("Attempting password comparison");
            const isMatch = await user.comparePassword(password);

            if (!isMatch) {
              console.log("Password does not match");
              
              // Increment failed login attempts
              user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
              
              // Lock account after 5 failed attempts
              if (user.failedLoginAttempts >= 5) {
                user.accountLocked = true;
                user.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // Lock for 30 minutes
                await user.save();
                
                loginEntry.failureReason = "Account locked after failed attempts";
                await LoginHistory.create(loginEntry);
                
                return done(null, false, {
                  message: "Your account has been temporarily locked due to too many failed login attempts."
                });
              }
              
              await user.save();
              
              loginEntry.failureReason = "Invalid password";
              await LoginHistory.create(loginEntry);
              
              return done(null, false, {
                message: "Incorrect password. Please try again."
              });
            }

            // Reset failed login attempts on successful login
            if (user.failedLoginAttempts > 0) {
              user.failedLoginAttempts = 0;
              user.accountLocked = false;
              user.lockUntil = undefined;
              await user.save();
            }

            // Update login entry for successful login
            loginEntry.success = true;
            delete loginEntry.failureReason;
            await LoginHistory.create(loginEntry);

            console.log("Authentication successful");
            return done(null, user);
          } catch (bcryptError) {
            console.error("Password comparison error:", bcryptError);
            loginEntry.failureReason = "Password comparison error";
            await LoginHistory.create(loginEntry);
            return done(bcryptError);
          }
        } catch (err) {
          console.error("Error in passport strategy:", err);
          return done(err);
        }
      }
    )
  );

  passport.serializeUser((user, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id, done) => {
    try {
      const user = await User.findById(id).select("-password").populate('currentCompany');

      if (!user) {
        return done(null, false); // User not found, clear session
      }

      done(null, user);
    } catch (err) {
      console.error('Error deserializing user:', err);
      done(err);
    }
  });

  return passport;
};
