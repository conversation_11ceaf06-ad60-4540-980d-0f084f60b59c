const IRP5_FIELD_MAPPING = {
  // Certificate Information
  certificateInfo: {
    certificateNumber: "Text163",
    taxYear: "Text164",
    yearStart: "Text167",
    yearEnd: "Text171",
    etiBenefitAmount: "Text175",
    certificateType: {
      irp5: "Button173",
      it3a: "Button174",
    },
  },

  // Employee Information
  employeeInfo: {
    personal: {
      surname: "Text176",
      firstNames: "Text178",
      initials: "Text180",
      identityType: {
        rsaId: "Button207",
        passport: "Button208",
        other: "Button209",
      },
      identityNumber: "Text181",
      passportNumber: "Text182",
      passportCountry: "Text184",
      taxReference: "Text185",
      dateOfBirth: "Text186",
      companyNumber: "Text187",
    },
    contact: {
      mobileNumber: "Text189",
      emailAddress: "Text190",
    },
    address: {
      residential: {
        unitNumber: "Text191",
        complexName: "Text193",
        streetNumber: "Text194",
        streetName: "Text195",
        suburb: "Text196",
        city: "Text197",
        postalCode: "Text198",
        countryCode: "Text199",
      },
      postal: {
        sameAsResidential: "Button210",
        poBox: "Text200",
        privateBox: "Text201",
        postNet: "Text202",
        postalCode: "Text203",
        countryCode: "Text213",
      },
    },
    employmentStatus: {
      permanent: "Button211",
      temporary: "Button212",
    },
  },

  // Employer Information
  employerInfo: {
    tradingName: "Text214",
    tradingNameChange: "Button211",
    registrationNumbers: {
      paye: "Text215",
      sdl: "Text216",
      uif: "Text217",
      companyRegistration: "Text218",
    },
    contactDetails: {
      telephone: "Text219",
      emailAddress: "Text220",
      physicalAddress: {
        unitNumber: "Text221",
        complexName: "Text222",
        streetNumber: "Text223",
        streetName: "Text224",
        suburb: "Text225",
        city: "Text226",
        postalCode: "Text227",
      },
    },
  },

  // Financial Information
  financialInfo: {
    // Income Sources (3601-3619)
    income: {
      normalIncome: "Text229", // 3601
      allowances: {
        travel: "Text231", // 3701
        publicTransport: "Text232", // 3702
        otherAllowance: "Text233", // 3703
        reimbursive: "Text234", // 3704
        subsistence: "Text236", // 3705
        entertainment: "Text237", // 3706
        cellphone: "Text238", // 3707
        shareOptions: "Text239", // 3708
      },
      benefits: {
        medical: "Text250", // 3810
        medicalDependants: "Text251", // 3813
        medicalCreditNonRecoverable: "Text252", // 3815
        pension: "Text253", // 3815
        providentFund: "Text254", // 3816
        retirementAnnuity: "Text255", // 3817
        groupLifeInsurance: "Text256", // 3801
      },
    },

    // Deductions (4001-4472)
    deductions: {
      pension: "Text258", // 4001
      providentFund: "Text259", // 4003
      retirementAnnuity: "Text260", // 4006
      medicalAid: "Text261", // 4005
      paye: "Text262", // 4102
      uif: "Text263", // 4141
      sdl: "Text264", // 4142
    },

    // ETI Information
    eti: {
      monthsInEmployment: "Text265",
      qualifyingMonths: "Text266",
      employedAfterDate: "Button241",
      monthlyRemuneration: "Text267",
      calculatedEti: "Text268",
      allowableEti: "Text269",
    },
  },

  // Employment Period Information
  employmentInfo: {
    startDate: "Text270",
    endDate: "Text271",
    payPeriods: "Text272",
    employmentStatus: {
      current: "Button242",
      retired: "Button243",
      retrenched: "Button244",
    },
    taxDirectiveInfo: {
      number: "Text273",
      percentage: "Text274",
    },
  },

  // Bank Account Details
  bankDetails: {
    accountHolder: "Text275",
    accountType: {
      current: "Button245",
      savings: "Button246",
      transmission: "Button247",
    },
    branchCode: "Text276",
    accountNumber: "Text277",
    bankName: "Text278",
    branchName: "Text279",
    accountHolderRelationship: {
      own: "Button248",
      joint: "Button249",
    },
  },

  // Foreign Employment Information
  foreignEmployment: {
    countryCode: "Text280",
    taxPaidAmount: "Text281",
    taxPaidCurrency: "Text282",
    convertedAmount: "Text283",
  },

  // Directive Information
  directiveInfo: {
    number: "Text284",
    percentage: "Text285",
  },
};

module.exports = IRP5_FIELD_MAPPING;
