const Employee = require("../models/Employee");
const Company = require("../models/Company");
const EmployerDetails = require("../models/employerDetails");
const Report = require("../models/Report");
const excel = require("exceljs"); // Make sure to install this package
const PDFDocument = require("pdfkit"); // And this one
const PayRun = require("../models/payRun");
const PayrollPeriod = require("../models/PayrollPeriod");
const moment = require("moment");
const mongoose = require("mongoose");
const Payroll = require("../models/Payroll");
const ReportSettings = require("../models/ReportSettings");
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const LeaveRequest = require("../models/LeaveRequest");

// Helper function to calculate date ranges
function calculateDateRange(dateRange) {
  const now = moment();

  // If dateRange is an object with startDate and endDate, return it as is
  if (dateRange && typeof dateRange === 'object' && dateRange.startDate && dateRange.endDate) {
    return {
      startDate: new Date(dateRange.startDate),
      endDate: new Date(dateRange.endDate)
    };
  }

  // Handle string-based date range presets
  switch (dateRange) {
    case "currentMonth":
      return {
        startDate: now.clone().startOf("month").toDate(),
        endDate: now.clone().endOf("month").toDate(),
      };
    case "previousMonth":
      return {
        startDate: now.clone().subtract(1, "month").startOf("month").toDate(),
        endDate: now.clone().subtract(1, "month").endOf("month").toDate(),
      };
    case "currentYear":
      return {
        startDate: now.clone().startOf("year").toDate(),
        endDate: now.clone().endOf("year").toDate(),
      };
    case "last3Months":
      return {
        startDate: now.clone().subtract(3, "months").startOf("month").toDate(),
        endDate: now.clone().endOf("month").toDate(),
      };
    case "last6Months":
      return {
        startDate: now.clone().subtract(6, "months").startOf("month").toDate(),
        endDate: now.clone().endOf("month").toDate(),
      };
    default:
      // Default to current month if no valid range provided
      return {
        startDate: now.clone().startOf("month").toDate(),
        endDate: now.clone().endOf("month").toDate(),
      };
  }
}

class ReportController {
  // Generate Employee List Report
  static async generateEmployeeList(
    companyId,
    format,
    settings,
    selectedEmployees = null
  ) {
    console.log("\n=== Generating Employee List Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees });

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({
        company: companyId,
      });

      if (!company) throw new Error("Company not found");

      // Get total employee count (including inactive)
      const totalEmployees = await Employee.countDocuments({
        company: companyId,
      });
      const activeEmployees = await Employee.countDocuments({
        company: companyId,
        status: "Active",
      });

      // Build employee query
      let query = { company: companyId };
      if (selectedEmployees && selectedEmployees.length > 0) {
        query._id = { $in: selectedEmployees };
      }

      // Get employees with all fields
      const employees = await Employee.find(query)
        .populate("payFrequency")
        .populate("role")
        .sort("lastName");

      console.log(`Found ${employees.length} employees`);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails
          ? this.formatAddress(employerDetails.physicalAddress)
          : "",
        postalAddress: employerDetails
          ? this.formatAddress(employerDetails.postalAddress)
          : "",
        reportName: "Employee List Report",
        generatedDate: new Date().toISOString(),
        employeeStats: {
          total: totalEmployees,
          active: activeEmployees,
          inactive: totalEmployees - activeEmployees,
          included: employees.length,
        },
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "pdf":
          reportData = await this.generateEmployeeListPDF(
            employees,
            reportMetadata,
            settings
          );
          break;
        case "excel":
          reportData = await this.generateEmployeeListExcel(
            employees,
            reportMetadata,
            settings
          );
          break;
        case "csv":
          reportData = await this.generateEmployeeListCSV(
            employees,
            reportMetadata,
            settings
          );
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating employee list:", error);
      throw error;
    }
  }

  // Helper method for content type and extensions
  static getFileDetails(format) {
    switch (format) {
      case "pdf":
        return {
          contentType: "application/pdf",
          extension: "pdf",
        };
      case "excel":
        return {
          contentType:
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          extension: "xlsx",
        };
      case "csv":
        return {
          contentType: "text/csv",
          extension: "csv",
        };
      default:
        return {
          contentType: "application/octet-stream",
          extension: "txt",
        };
    }
  }

  // Helper methods for different formats
  static async generateEmployeeListPDF(employees, metadata, settings) {
    const doc = new PDFDocument();
    const chunks = [];

    doc.on("data", (chunk) => chunks.push(chunk));
    doc.on("end", () => {});

    // Add header
    doc.fontSize(16).text("Employee List", { align: "center" });
    doc.moveDown();

    // Add employees
    employees.forEach((emp) => {
      doc
        .fontSize(12)
        .text(
          [
            `${emp.firstName} ${emp.lastName}`,
            `Employee #: ${emp.employeeNumber}`,
            `Position: ${emp.position}`,
            `Status: ${emp.status}`,
            "------------------------",
          ].join("\n")
        );
      doc.moveDown();
    });

    doc.end();

    return Buffer.concat(chunks);
  }

  static async generateEmployeeListExcel(employees, metadata, settings) {
    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Employee List");

    // Add company header
    worksheet.mergeCells("A1:F1");
    worksheet.getCell("A1").value = metadata.companyName;
    worksheet.getCell("A1").font = { size: 16, bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add addresses
    if (metadata.physicalAddress) {
      worksheet.mergeCells("A2:F2");
      worksheet.getCell(
        "A2"
      ).value = `Physical Address: ${metadata.physicalAddress}`;
    }
    if (metadata.postalAddress) {
      worksheet.mergeCells("A3:F3");
      worksheet.getCell(
        "A3"
      ).value = `Postal Address: ${metadata.postalAddress}`;
    }

    // Add report info
    worksheet.mergeCells("A4:F4");
    worksheet.getCell("A4").value = metadata.reportName;
    worksheet.getCell("A4").font = { size: 14, bold: true };

    worksheet.mergeCells("A5:F5");
    worksheet.getCell(
      "A5"
    ).value = `Generated on: ${new Date().toLocaleDateString()}`;

    // Add employee stats
    worksheet.mergeCells("A6:F6");
    worksheet.getCell(
      "A6"
    ).value = `Total Employees: ${metadata.employeeStats.total} | Active: ${metadata.employeeStats.active} | Inactive: ${metadata.employeeStats.inactive}`;
    if (metadata.employeeStats.included !== metadata.employeeStats.active) {
      worksheet.mergeCells("A7:F7");
      worksheet.getCell(
        "A7"
      ).value = `Showing ${metadata.employeeStats.included} selected employees`;
    }

    // Add a blank row for spacing
    worksheet.addRow([]);

    // Define columns
    const columns = [
      { header: "Employee #", key: "companyEmployeeNumber", width: 15 },
      { header: "First Name", key: "firstName", width: 20 },
      { header: "Last Name", key: "lastName", width: 20 },
      { header: "ID Number", key: "idNumber", width: 15 },
      { header: "Email", key: "email", width: 30 },
      { header: "Phone", key: "phone", width: 15 },
      { header: "Job Title", key: "jobTitle", width: 25 },
      { header: "Department", key: "costCentre", width: 20 },
      { header: "Start Date", key: "doa", width: 15 },
      { header: "Status", key: "status", width: 15 },
      { header: "Pay Frequency", key: "payFrequencyName", width: 15 },
      { header: "Basic Salary", key: "basicSalary", width: 15 },
      { header: "Bank", key: "bank", width: 20 },
      { header: "Account Number", key: "accountNumber", width: 20 },
      { header: "Branch Code", key: "branchCode", width: 15 },
      { header: "Tax Number", key: "incomeTaxNumber", width: 15 },
      { header: "Address", key: "fullAddress", width: 40 },
      { header: "Gender", key: "gender", width: 10 },
      { header: "Race", key: "race", width: 15 },
      { header: "Occupation Level", key: "occupationLevel", width: 30 },
      { header: "Province", key: "province", width: 20 },
      { header: "Working Hours", key: "workingHours", width: 20 },
      { header: "ETI Status", key: "etiStatus", width: 25 },
    ];

    // Set column widths without creating duplicate headers
    columns.forEach((col, index) => {
      worksheet.getColumn(index + 1).width = col.width;
    });

    // Add header row at row 8 (after metadata and blank row)
    const headerValues = columns.map(col => col.header);
    const employeeListHeaderRow = worksheet.getRow(8);
    headerValues.forEach((header, index) => {
      employeeListHeaderRow.getCell(index + 1).value = header;
    });

    // Add employee data
    const rows = employees.map((emp) => ({
      companyEmployeeNumber: emp.companyEmployeeNumber,
      firstName: emp.firstName,
      lastName: emp.lastName,
      idNumber: emp.idNumber,
      email: emp.email,
      phone: emp.phone,
      jobTitle: emp.jobTitle,
      costCentre: emp.costCentre,
      doa: emp.doa,
      status: emp.status,
      payFrequencyName: emp.payFrequency?.name || "N/A",
      basicSalary: emp.basicSalary,
      bank: settings?.employeeReports?.includeBankDetails ? emp.bank : "***",
      accountNumber: settings?.employeeReports?.includeBankDetails
        ? emp.accountNumber
        : "***",
      branchCode: settings?.employeeReports?.includeBankDetails
        ? emp.branchCode
        : "***",
      incomeTaxNumber: emp.incomeTaxNumber,
      fullAddress: `${emp.unitNumber || ""} ${emp.complex || ""} ${
        emp.streetNumber || ""
      } ${emp.street || ""}, ${emp.suburb || ""}, ${emp.city || ""}`,
      gender: emp.gender,
      race: emp.race,
      occupationLevel: emp.occupationLevel,
      province: emp.province,
      workingHours: emp.workingHours,
      etiStatus: emp.etiStatus,
    }));

    worksheet.addRows(rows);

    // Style the header row (row 8)
    const styledHeaderRow = worksheet.getRow(8);
    styledHeaderRow.font = { name: 'Arial', size: 9, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Add borders and background to header row - only for columns with data
    for (let i = 1; i <= columns.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.font = { name: 'Arial', size: 9, bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    }

    return await workbook.xlsx.writeBuffer();
  }

  static async generateEmployeeListCSV(employees, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress
        ? `Physical Address,${metadata.physicalAddress}`
        : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Total Employees,${metadata.employeeStats.total}`,
      `Active Employees,${metadata.employeeStats.active}`,
      `Inactive Employees,${metadata.employeeStats.inactive}`,
      metadata.employeeStats.included !== metadata.employeeStats.active
        ? `Selected Employees,${metadata.employeeStats.included}`
        : "",
      "", // Empty line for spacing
    ]
      .filter(Boolean)
      .join("\n");

    // Define headers
    const headers = [
      "Employee #",
      "First Name",
      "Last Name",
      "ID Number",
      "Email",
      "Phone",
      "Job Title",
      "Department",
      "Start Date",
      "Status",
      "Pay Frequency",
      "Basic Salary",
      "Bank",
      "Account Number",
      "Branch Code",
      "Tax Number",
      "Address",
      "Gender",
      "Race",
      "Occupation Level",
      "Province",
      "Working Hours",
      "ETI Status",
    ].join(",");

    // Generate employee rows
    const employeeRows = employees.map((emp) =>
      [
        emp.companyEmployeeNumber,
        emp.firstName,
        emp.lastName,
        emp.idNumber,
        emp.email,
        emp.phone,
        emp.jobTitle,
        emp.costCentre,
        emp.doa,
        emp.status,
        emp.payFrequency?.name || "N/A",
        emp.basicSalary,
        settings?.employeeReports?.includeBankDetails ? emp.bank : "***",
        settings?.employeeReports?.includeBankDetails
          ? emp.accountNumber
          : "***",
        settings?.employeeReports?.includeBankDetails ? emp.branchCode : "***",
        emp.incomeTaxNumber,
        `${emp.unitNumber || ""} ${emp.complex || ""} ${
          emp.streetNumber || ""
        } ${emp.street || ""}, ${emp.suburb || ""}, ${emp.city || ""}`.trim(),
        emp.gender,
        emp.race,
        emp.occupationLevel,
        emp.province,
        emp.workingHours,
        emp.etiStatus,
      ]
        .map((field) => {
          // Escape commas and quotes in fields
          if (field && field.toString().includes(",")) {
            return `"${field.toString().replace(/"/g, '""')}"`;
          }
          return field || "";
        })
        .join(",")
    );

    // Combine all parts
    const csvContent = [metadataRows, headers, ...employeeRows].join("\n");

    return Buffer.from(csvContent);
  }

  // Get Recent Reports
  static async getRecentReports(req, res) {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        throw new Error("Company not found");
      }

      const reports = await Report.find({ company: company._id })
        .sort({ createdAt: -1 })
        .limit(6)
        .populate("generatedBy", "firstName lastName");

      res.json({
        success: true,
        reports: reports.map((report) => ({
          id: report._id,
          type: report.type,
          format: report.format,
          generatedBy: `${report.generatedBy.firstName} ${report.generatedBy.lastName}`,
          generatedAt: report.createdAt,
          status: report.status,
        })),
      });
    } catch (error) {
      console.error("Error fetching recent reports:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch recent reports",
        error: error.message,
      });
    }
  }

  // Download Report
  static async downloadReport(req, res) {
    try {
      const { reportId } = req.params;
      const report = await Report.findById(reportId);

      if (!report) {
        throw new Error("Report not found");
      }

      // Verify user has access to this report
      if (report.company.toString() !== req.user.currentCompany.toString()) {
        throw new Error("Unauthorized access to report");
      }

      // Get file details
      const { contentType, extension } = this.getFileDetails(report.format);

      // Set headers
      res.setHeader("Content-Type", contentType);
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${report.type}_${
          new Date(report.createdAt).toISOString().split("T")[0]
        }.${extension}`
      );

      // Send report data
      res.send(report.data);
    } catch (error) {
      console.error("Error downloading report:", error);
      res.status(500).json({
        success: false,
        message: "Failed to download report",
        error: error.message,
      });
    }
  }

  // Delete Report
  static async deleteReport(req, res) {
    try {
      const { reportId } = req.params;
      const report = await Report.findById(reportId);

      if (!report) {
        throw new Error("Report not found");
      }

      // Verify user has access to delete this report
      if (report.company.toString() !== req.user.currentCompany.toString()) {
        throw new Error("Unauthorized access to report");
      }

      await report.remove();

      res.json({
        success: true,
        message: "Report deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting report:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete report",
        error: error.message,
      });
    }
  }

  // Helper function to format address
  static formatAddress(addressObj) {
    if (!addressObj) return "";

    if (addressObj.line1) {
      // Postal address
      return [
        addressObj.line1,
        addressObj.line2,
        addressObj.line3,
        addressObj.code,
      ]
        .filter(Boolean)
        .join(", ");
    } else {
      // Physical address
      return [
        addressObj.unitNumber,
        addressObj.complex,
        addressObj.streetNumber,
        addressObj.street,
        addressObj.suburbDistrict,
        addressObj.cityTown,
        addressObj.code,
      ]
        .filter(Boolean)
        .join(", ");
    }
  }

  static async generatePayrollVarianceReport(companyId, format, settings, dateRange) {
    try {
      console.log("\n=== Generating Payroll Variance Report ===");
      console.log("Parameters:", { companyId, format, dateRange });

      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Calculate comparison periods
      const currentPeriodStart = moment(dateRange.startDate);
      const currentPeriodEnd = moment(dateRange.endDate);
      const previousPeriodStart = moment(dateRange.startDate).subtract(1, 'month');
      const previousPeriodEnd = moment(dateRange.endDate).subtract(1, 'month');

      // Get payroll data for both periods with populated payslips
      const currentPeriod = await PayRun.find({
        company: companyId,
        startDate: { 
          $gte: currentPeriodStart.toDate(), 
          $lte: currentPeriodEnd.toDate() 
        }
      }).populate('payslips');

      const previousPeriod = await PayRun.find({
        company: companyId,
        startDate: { 
          $gte: previousPeriodStart.toDate(), 
          $lte: previousPeriodEnd.toDate() 
        }
      }).populate('payslips');

      console.log(`Found ${currentPeriod.length} current period payrolls and ${previousPeriod.length} previous period payrolls`);

      // Calculate variances
      const variances = await this.calculateVariances(currentPeriod, previousPeriod);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        reportName: "Payroll Variance Report",
        period: {
          current: `${currentPeriodStart.format('MMMM YYYY')}`,
          previous: `${previousPeriodStart.format('MMMM YYYY')}`,
        },
        generatedDate: new Date().toISOString(),
        summary: variances.summary
      };

      // Generate report in requested format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generatePayrollVarianceExcel(variances, reportMetadata);
          break;
        case "pdf":
          reportData = await this.generatePayrollVariancePDF(variances, reportMetadata);
          break;
        case "csv":
          reportData = await this.generatePayrollVarianceCSV(variances, reportMetadata);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating payroll variance report:", error);
      throw error;
    }
  }

  // Helper function to calculate variances
  static async calculateVariances(currentPeriod, previousPeriod) {
    const variances = {
      transactions: [],
      summary: {
        totalPayroll: {
          current: 0,
          previous: 0,
          variance: 0,
          percentageChange: 0
        },
        headcount: {
          current: 0,
          previous: 0,
          variance: 0,
          percentageChange: 0
        }
      }
    };

    // Process payroll data for both periods
    const processPayrollData = (payrolls, isPrevious = false) => {
      const periodTotals = {
        totalPayroll: 0,
        headcount: new Set()
      };

      payrolls.forEach(payRun => {
        // Process each payslip in the payrun
        payRun.payslips.forEach(payslip => {
          // Track unique employees for headcount
          periodTotals.headcount.add(payslip.employee.toString());

          // Basic salary and totals
          const basicSalary = payslip.totals?.basicSalary || 0;
          const grossPay = payslip.totals?.grossPay || 0;
          const netPay = payslip.totals?.netPay || 0;
          const totalDeductions = payslip.totals?.totalDeductions || 0;
          const employerContributions = payslip.totals?.employerContributions || 0;

          // Add to total payroll
          periodTotals.totalPayroll += grossPay;

          // Create transaction record
          const transactionRecord = {
            employeeNo: payslip.employeeNumber,
            firstName: payslip.firstName,
            lastName: payslip.lastName,
            transactions: {
              'Basic Salary': basicSalary,
              'Gross Pay': grossPay,
              'Net Pay': netPay,
              'Total Deductions': totalDeductions,
              'Employer Contributions': employerContributions
            }
          };

          // Add transaction record to variances
          if (!isPrevious) {
            variances.transactions.push({
              ...transactionRecord,
              previousAmount: 0,
              currentAmount: grossPay,
              variance: grossPay
            });
          } else {
            // Find matching current period transaction
            const matchingTransaction = variances.transactions.find(
              t => t.employeeNo === payslip.employeeNumber
            );
            if (matchingTransaction) {
              matchingTransaction.previousAmount = grossPay;
              matchingTransaction.variance = matchingTransaction.currentAmount - grossPay;
            }
          }
        });
      });

      return periodTotals;
    };

    // Process both periods
    const currentTotals = processPayrollData(currentPeriod, false);
    const previousTotals = processPayrollData(previousPeriod, true);

    // Calculate summary variances
    variances.summary.totalPayroll = {
      current: currentTotals.totalPayroll,
      previous: previousTotals.totalPayroll,
      variance: currentTotals.totalPayroll - previousTotals.totalPayroll,
      percentageChange: previousTotals.totalPayroll ? 
        ((currentTotals.totalPayroll - previousTotals.totalPayroll) / previousTotals.totalPayroll) * 100 : 100
    };

    variances.summary.headcount = {
      current: currentTotals.headcount.size,
      previous: previousTotals.headcount.size,
      variance: currentTotals.headcount.size - previousTotals.headcount.size,
      percentageChange: previousTotals.headcount.size ? 
        ((currentTotals.headcount.size - previousTotals.headcount.size) / previousTotals.headcount.size) * 100 : 100
    };

    // Sort transactions by employee number
    variances.transactions.sort((a, b) => a.employeeNo.localeCompare(b.employeeNo));

    return variances;
  }

  static async generatePayrollVarianceExcel(variances, metadata) {
    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Payroll Variance");

    // Set default font and freeze panes
    worksheet.views = [{ state: 'frozen', ySplit: 8 }];

    // Row 1 - Company Name
    worksheet.mergeCells('A1:H1');
    const companyNameCell = worksheet.getCell('A1');
    companyNameCell.value = metadata.companyName;
    companyNameCell.font = { name: 'Arial', size: 14, bold: true };
    companyNameCell.alignment = { vertical: 'middle', horizontal: 'center' };

    // Row 2 - Report Title
    worksheet.mergeCells('A2:H2');
    const reportTitleCell = worksheet.getCell('A2');
    reportTitleCell.value = metadata.reportName;
    reportTitleCell.font = { name: 'Arial', size: 12, bold: true };
    reportTitleCell.alignment = { vertical: 'middle', horizontal: 'center' };

    // Row 3 - Period
    worksheet.mergeCells('A3:H3');
    const periodCell = worksheet.getCell('A3');
    periodCell.value = `Comparing: ${metadata.period.current} vs ${metadata.period.previous}`;
    periodCell.font = { name: 'Arial', size: 10, bold: true };
    periodCell.alignment = { vertical: 'middle', horizontal: 'center' };

    // Row 4 - Generated Date
    worksheet.mergeCells('A4:H4');
    const dateCell = worksheet.getCell('A4');
    dateCell.value = `Generated on: ${new Date().toLocaleDateString()}`;
    dateCell.font = { name: 'Arial', size: 10 };
    dateCell.alignment = { vertical: 'middle', horizontal: 'center' };

    // Row 5 - Summary Section Header
    worksheet.mergeCells('A5:H5');
    const summaryHeaderCell = worksheet.getCell('A5');
    summaryHeaderCell.value = 'Summary';
    summaryHeaderCell.font = { name: 'Arial', size: 12, bold: true };
    summaryHeaderCell.alignment = { vertical: 'middle', horizontal: 'left' };
    summaryHeaderCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Row 6 - Summary Data Headers
    const summaryHeaders = ['Metric', 'Previous Period', 'Current Period', 'Variance', 'Change %', 'Status'];
    worksheet.getRow(6).values = summaryHeaders;
    worksheet.getRow(6).font = { bold: true };

    // Row 7-8 - Summary Data
    const summaryData = [
      {
        metric: 'Total Payroll',
        previous: variances.summary.totalPayroll.previous,
        current: variances.summary.totalPayroll.current,
        variance: variances.summary.totalPayroll.variance,
        percentageChange: variances.summary.totalPayroll.percentageChange
      },
      {
        metric: 'Headcount',
        previous: variances.summary.headcount.previous,
        current: variances.summary.headcount.current,
        variance: variances.summary.headcount.variance,
        percentageChange: variances.summary.headcount.percentageChange
      }
    ];

    summaryData.forEach((data, index) => {
      const row = worksheet.getRow(7 + index);
      row.values = [
        data.metric,
        data.previous,
        data.current,
        data.variance,
        data.percentageChange,
        this.getVarianceStatus(data.percentageChange)
      ];

      // Format numbers
      row.getCell(2).numFmt = '#,##0.00';
      row.getCell(3).numFmt = '#,##0.00';
      row.getCell(4).numFmt = '#,##0.00';
      row.getCell(5).numFmt = '0.00%';

      // Apply conditional formatting
      this.applyVarianceFormatting(row, data.percentageChange);
    });

    // Add spacing
    worksheet.addRow([]);

    // Row 10 - Detail Section Header
    worksheet.mergeCells('A10:H10');
    const detailHeaderCell = worksheet.getCell('A10');
    detailHeaderCell.value = 'Employee Details';
    detailHeaderCell.font = { name: 'Arial', size: 12, bold: true };
    detailHeaderCell.alignment = { vertical: 'middle', horizontal: 'left' };
    detailHeaderCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Define columns for transaction details
    const transactionColumns = [
      { header: "Employee No.", key: "employeeNo", width: 15 },
      { header: "First Name", key: "firstName", width: 20 },
      { header: "Last Name", key: "lastName", width: 20 },
      { header: "Previous Amount", key: "previousAmount", width: 20 },
      { header: "Current Amount", key: "currentAmount", width: 20 },
      { header: "Variance", key: "variance", width: 20 },
      { header: "Change %", key: "percentageChange", width: 15 },
      { header: "Status", key: "status", width: 20 }
    ];

    // Set column widths without creating duplicate headers
    transactionColumns.forEach((col, index) => {
      worksheet.getColumn(index + 1).width = col.width;
    });

    // Add header row at row 11 only
    const headerValues = transactionColumns.map(col => col.header);
    const varianceHeaderRow = worksheet.getRow(11);
    headerValues.forEach((header, index) => {
      varianceHeaderRow.getCell(index + 1).value = header;
    });

    // Style the transaction header row
    const styledVarianceHeaderRow = worksheet.getRow(11);
    styledVarianceHeaderRow.font = { name: 'Arial', size: 10, bold: true };
    styledVarianceHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledVarianceHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add transaction data
    variances.transactions.forEach(transaction => {
      const percentageChange = transaction.previousAmount ? 
        ((transaction.variance / transaction.previousAmount) * 100) : 100;

      const row = worksheet.addRow({
        employeeNo: transaction.employeeNo,
        firstName: transaction.firstName,
        lastName: transaction.lastName,
        previousAmount: transaction.previousAmount,
        currentAmount: transaction.currentAmount,
        variance: transaction.variance,
        percentageChange: percentageChange,
        status: this.getVarianceStatus(percentageChange)
      });

      // Format number cells
      row.getCell('previousAmount').numFmt = '#,##0.00';
      row.getCell('currentAmount').numFmt = '#,##0.00';
      row.getCell('variance').numFmt = '#,##0.00';
      row.getCell('percentageChange').numFmt = '0.00%';

      // Apply conditional formatting
      this.applyVarianceFormatting(row, percentageChange);

      // Style the row
      row.eachCell({ includeEmpty: true }, cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        cell.alignment = { vertical: 'middle' };
        if (typeof cell.value === 'number') {
          cell.alignment.horizontal = 'right';
        } else {
          cell.alignment.horizontal = 'left';
        }
      });
    });

    // Add auto-filter for the data range
    const lastDataRow = 11 + variances.transactions.length;
    worksheet.autoFilter = {
      from: { row: 11, column: 1 },
      to: { row: lastDataRow, column: transactionColumns.length }
    };

    return await workbook.xlsx.writeBuffer();
  }

  static getVarianceStatus(percentageChange) {
    const absChange = Math.abs(percentageChange);
    if (absChange <= 5) return '✓ Normal';
    if (absChange <= 10) return '⚠️ Notable';
    return '❗ Significant';
  }

  static getVarianceNote(metric, percentageChange) {
    const absChange = Math.abs(percentageChange);
    const direction = percentageChange > 0 ? 'increase' : 'decrease';
    
    if (absChange <= 5) {
      return 'Within normal variation range';
    }
    
    if (metric.toLowerCase().includes('headcount')) {
      return `${direction === 'increase' ? 'Growth' : 'Reduction'} in workforce`;
    }
    
    if (metric.toLowerCase().includes('average')) {
      return `${direction === 'increase' ? 'Higher' : 'Lower'} per-employee cost`;
    }
    
    return `Significant ${direction} requires review`;
  }

  static applyVarianceFormatting(row, percentageChange) {
    const absChange = Math.abs(percentageChange);
    let fillColor = 'FFFFFF'; // Default white

    if (absChange > 10) {
      fillColor = percentageChange > 0 ? 'FFE6B8B7' : 'FFB8E6B9'; // Red for increase, Green for decrease
    } else if (absChange > 5) {
      fillColor = 'FFFFD700'; // Yellow for notable changes
    }

    row.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: fillColor }
    };
  }

  // Generate Employee Basic Info Report
  static async generateEmployeeBasicInfo(companyId, format, settings, selectedEmployees = null) {
    console.log("\n=== Generating Employee Basic Info Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees });
    console.log("Full settings received:", settings);

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build employee query
      let query = { company: companyId };
      if (selectedEmployees && selectedEmployees.length > 0) {
        query._id = { $in: selectedEmployees };
      }

      // Get employees with all fields
      const employees = await Employee.find(query)
        .populate("payFrequency")
        .populate("role")
        .sort("lastName");

      console.log(`Found ${employees.length} employees`);

      // Prepare report metadata
      console.log("Building report metadata...");
      console.log("Company details:", {
        companyId: company._id,
        companyName: company.name,
        employerDetailsExists: !!employerDetails,
        tradingName: employerDetails?.tradingName
      });

      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Employee Basic Information Report",
        generatedDate: new Date().toISOString(),
        employeeCount: employees.length
      };

      console.log("Final report metadata:", reportMetadata);

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          console.log("Generating Excel format...");
          reportData = await this.generateEmployeeBasicInfoExcel(employees, reportMetadata, settings);
          break;
        case "pdf":
          console.log("Generating PDF format...");
          reportData = await this.generateEmployeeBasicInfoPDF(employees, reportMetadata, settings);
          break;
        case "csv":
          console.log("Generating CSV format...");
          reportData = await this.generateEmployeeBasicInfoCSV(employees, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating employee basic info report:", error);
      throw error;
    }
  }

  // Generate Excel format for Employee Basic Info
  static async generateEmployeeBasicInfoExcel(employees, metadata, settings) {
    console.log("\n=== Generating Excel Format ===");
    console.log("Settings for Excel generation:", JSON.stringify(settings, null, 2));

    // Handle null/undefined settings gracefully
    const safeSettings = settings || {};
    console.log("Safe settings:", safeSettings);

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Employee Basic Info");

    // Set default font
    worksheet.views = [{ state: 'frozen', ySplit: 6 }];

    // Add metadata rows
    console.log("Adding metadata rows...");
    worksheet.mergeCells('A1:E1');
    const companyNameCell = worksheet.getCell('A1');
    companyNameCell.value = metadata.companyName;
    companyNameCell.font = { name: 'Arial', size: 12, bold: true };
    companyNameCell.alignment = { vertical: 'middle' };

    // Row 2 - Report Name
    worksheet.mergeCells('A2:E2');
    const reportNameCell = worksheet.getCell('A2');
    reportNameCell.value = 'Employee Basic Info';
    reportNameCell.font = { name: 'Arial', size: 8, bold: true };
    reportNameCell.alignment = { vertical: 'middle' };

    // Row 3 - Period
    worksheet.mergeCells('A3:E3');
    const periodCell = worksheet.getCell('A3');
    periodCell.value = `Generated on: ${new Date().toLocaleDateString()}`;
    periodCell.font = { name: 'Arial', size: 8, bold: true };
    periodCell.alignment = { vertical: 'middle' };

    // Row 4 - Number of Employees
    worksheet.mergeCells('A4:E4');
    const employeeCountCell = worksheet.getCell('A4');
    employeeCountCell.value = `Number of Employees: ${metadata.employeeCount}`;
    employeeCountCell.font = { name: 'Arial', size: 8, bold: true };
    employeeCountCell.alignment = { vertical: 'middle' };

    // Row 5 - Blank
    worksheet.addRow([]);

    // Define columns based on selected fields
    console.log("Defining columns based on selected fields...");
    console.log("Selected fields:", safeSettings.selectedFields);

    // Comprehensive field mappings for Excel columns based on Employee model
    const fieldMappings = {
      // Essential Information
      "companyEmployeeNumber": { header: "Employee #", key: "companyEmployeeNumber", width: 15 },
      "firstName": { header: "First Name", key: "firstName", width: 20 },
      "lastName": { header: "Last Name", key: "lastName", width: 20 },
      "globalEmployeeId": { header: "Global Employee ID", key: "globalEmployeeId", width: 20 },

      // Personal Information
      "dob": { header: "Date of Birth", key: "dob", width: 15 },
      "gender": { header: "Gender", key: "gender", width: 10 },
      "race": { header: "Race", key: "race", width: 15 },
      "disabled": { header: "Disabled", key: "disabled", width: 10 },
      "foreignNational": { header: "Foreign National", key: "foreignNational", width: 15 },
      "notRSACitizen": { header: "Not RSA Citizen", key: "notRSACitizen", width: 15 },

      // Contact Information
      "email": { header: "Email", key: "email", width: 30 },
      "phone": { header: "Phone", key: "phone", width: 15 },
      "mobileNumber": { header: "Mobile Number", key: "mobileNumber", width: 15 },

      // Employment Information
      "jobTitle": { header: "Job Title", key: "jobTitle", width: 25 },
      "department": { header: "Department", key: "department", width: 20 },
      "costCentre": { header: "Cost Centre", key: "costCentre", width: 20 },
      "workingHours": { header: "Working Hours", key: "workingHours", width: 15 },
      "payFrequency": { header: "Pay Frequency", key: "payFrequency", width: 15 },
      "doa": { header: "Date of Appointment", key: "doa", width: 15 },
      "status": { header: "Employment Status", key: "status", width: 15 },
      "employmentStatus": { header: "Active Status", key: "employmentStatus", width: 15 },
      "isDirector": { header: "Is Director", key: "isDirector", width: 12 },
      "typeOfDirector": { header: "Director Type", key: "typeOfDirector", width: 25 },
      "isContractor": { header: "Is Contractor", key: "isContractor", width: 15 },

      // Identification Information
      "idType": { header: "ID Type", key: "idType", width: 15 },
      "idNumber": { header: "ID Number", key: "idNumber", width: 20 },
      "passportNumber": { header: "Passport Number", key: "passportNumber", width: 20 },
      "incomeTaxNumber": { header: "Income Tax Number", key: "incomeTaxNumber", width: 20 },

      // Banking Information
      "paymentMethod": { header: "Payment Method", key: "paymentMethod", width: 15 },
      "bank": { header: "Bank Name", key: "bank", width: 20 },
      "accountType": { header: "Account Type", key: "accountType", width: 15 },
      "accountNumber": { header: "Account Number", key: "accountNumber", width: 20 },
      "branchCode": { header: "Branch Code", key: "branchCode", width: 15 },
      "accountHolder": { header: "Account Holder", key: "accountHolder", width: 15 },
      "holderRelationship": { header: "Holder Relationship", key: "holderRelationship", width: 20 },

      // Address Information
      "streetAddress": { header: "Street Address", key: "streetAddress", width: 30 },
      "suburb": { header: "Suburb", key: "suburb", width: 20 },
      "city": { header: "City", key: "city", width: 20 },
      "postalCode": { header: "Postal Code", key: "postalCode", width: 10 },
      "province": { header: "Province", key: "province", width: 20 },
      "unitNumber": { header: "Unit Number", key: "unitNumber", width: 15 },
      "complex": { header: "Complex", key: "complex", width: 20 },
      "streetNumber": { header: "Street Number", key: "streetNumber", width: 15 },
      "street": { header: "Street", key: "street", width: 25 },
      "code": { header: "Postal Code", key: "code", width: 10 },
      "line1": { header: "Address Line 1", key: "line1", width: 30 },
      "line2": { header: "Address Line 2", key: "line2", width: 30 },
      "line3": { header: "Address Line 3", key: "line3", width: 30 },

      // Employment Tax Incentive (ETI)
      "etiStatus": { header: "ETI Status", key: "etiStatus", width: 25 },
      "etiEffectiveFrom": { header: "ETI Effective From", key: "etiEffectiveFrom", width: 18 },
      "isETIEligible": { header: "ETI Eligible", key: "isETIEligible", width: 12 },
      "monthlyRemuneration": { header: "Monthly Remuneration", key: "monthlyRemuneration", width: 20 },
      "ageAtEmployment": { header: "Age at Employment", key: "ageAtEmployment", width: 18 },
      "employmentStartDate": { header: "Employment Start Date", key: "employmentStartDate", width: 20 },

      // Occupation Information
      "occupationLevel": { header: "Occupation Level", key: "occupationLevel", width: 30 },
      "occupationCategory": { header: "Occupation Category", key: "occupationCategory", width: 35 },
      "jobValue": { header: "Job Value", key: "jobValue", width: 20 },

      // UIF Information
      "isUifExempt": { header: "UIF Exempt", key: "isUifExempt", width: 12 },
      "uifExemptReason": { header: "UIF Exempt Reason", key: "uifExemptReason", width: 30 },
      "uifStatusCode": { header: "UIF Status Code", key: "uifStatusCode", width: 15 },

      // Working Hours Details
      "hoursPerDay": { header: "Hours Per Day", key: "hoursPerDay", width: 15 },

      // Termination Information
      "terminationNoticeDate": { header: "Termination Notice Date", key: "terminationNoticeDate", width: 22 },
      "lastDayOfService": { header: "Last Day of Service", key: "lastDayOfService", width: 20 },
      "rehireEligibility": { header: "Rehire Eligible", key: "rehireEligibility", width: 15 },
      "exitInterviewDate": { header: "Exit Interview Date", key: "exitInterviewDate", width: 20 },
      "lastPayrollDate": { header: "Last Payroll Date", key: "lastPayrollDate", width: 18 },

      // OID Information
      "oidEligible": { header: "OID Eligible", key: "oidEligible", width: 12 },
      "oidExemptReason": { header: "OID Exempt Reason", key: "oidExemptReason", width: 25 },

      // Self Service
      "selfServiceEnabled": { header: "Self Service Enabled", key: "selfServiceEnabled", width: 20 },
      "lastActivity": { header: "Last Activity", key: "lastActivity", width: 18 },
      "essEmailSent": { header: "ESS Email Sent", key: "essEmailSent", width: 15 }
    };

    // Use selected fields or default to essential fields if none selected
    const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
      ? safeSettings.selectedFields
      : ["companyEmployeeNumber", "firstName", "lastName"];

    console.log("Using fields for Excel:", selectedFields);

    // Build columns array from selected fields
    const columns = selectedFields.map(field => {
      const mapping = fieldMappings[field];
      if (!mapping) {
        console.warn(`No mapping found for field: ${field}`);
        return { header: field, key: field, width: 15 };
      }
      return mapping;
    });

    // Log all column definitions
    console.log("Final column definitions:", columns.map(col => col.header));

    // Set column widths without creating duplicate headers
    columns.forEach((col, index) => {
      worksheet.getColumn(index + 1).width = col.width;
    });

    // Add header row at row 6 only
    const headerValues = columns.map(col => col.header);
    console.log("Adding header row with values:", headerValues);
    const basicInfoHeaderRow = worksheet.getRow(6);
    headerValues.forEach((header, index) => {
      basicInfoHeaderRow.getCell(index + 1).value = header;
    });

    // Add employee data starting from row 7
    console.log("\nProcessing employee data...");
    employees.forEach((emp, index) => {
      console.log(`\nProcessing employee ${index + 1}/${employees.length} (${emp.firstName} ${emp.lastName})`);
      const rowData = [];

      // Process only selected fields in the same order as headers
      selectedFields.forEach(field => {
        let value = '';

        // Use a more efficient approach - try direct property access first
        if (emp[field] !== undefined && emp[field] !== null) {
          // Handle special cases for complex fields
          switch (field) {
            case 'dob':
            case 'doa':
            case 'etiEffectiveFrom':
            case 'employmentStartDate':
            case 'terminationNoticeDate':
            case 'lastDayOfService':
            case 'exitInterviewDate':
            case 'lastPayrollDate':
            case 'lastActivity':
              value = emp[field] ? new Date(emp[field]).toLocaleDateString() : '';
              break;
            case 'payFrequency':
              value = emp.payFrequency?.name || '';
              break;
            case 'employmentStatus':
            case 'isDirector':
            case 'isContractor':
            case 'disabled':
            case 'foreignNational':
            case 'notRSACitizen':
            case 'isUifExempt':
            case 'isETIEligible':
            case 'oidEligible':
            case 'selfServiceEnabled':
            case 'essEmailSent':
            case 'rehireEligibility':
              value = emp[field] ? 'Yes' : 'No';
              break;
            case 'bank':
              // Handle both 'bank' and 'bankName' fields
              value = emp.bank || emp.bankName || '';
              break;
            case 'mobileNumber':
              // Handle both 'mobileNumber' and 'phone' fields
              value = emp.mobileNumber || emp.phone || '';
              break;
            case 'postalCode':
              // Handle both 'postalCode' and 'code' fields
              value = emp.postalCode || emp.code || '';
              break;
            default:
              value = emp[field] || '';
          }
        } else {
          // Fallback for fields that might not exist or are null
          value = '';
        }

        rowData.push(value);
      });

      console.log(`Row data for ${emp.firstName} ${emp.lastName}:`, rowData);

      // Add data row starting from row 7
      const dataRowNumber = 7 + index;
      const dataRow = worksheet.getRow(dataRowNumber);
      rowData.forEach((value, colIndex) => {
        dataRow.getCell(colIndex + 1).value = value;
      });

      // Style the data row
      dataRow.eachCell({ includeEmpty: true }, cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        cell.alignment = { vertical: 'middle', horizontal: 'left' };
        cell.font = { name: 'Arial', size: 8 };
      });
    });

    // Style the header row (row 6)
    const styledHeaderRow = worksheet.getRow(6);
    styledHeaderRow.font = { name: 'Arial', size: 9, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Add borders and background to header row - only for columns with data
    for (let i = 1; i <= columns.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.font = { name: 'Arial', size: 9, bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    }

    // Add auto-filter for the data range
    const lastDataRow = 6 + employees.length;
    worksheet.autoFilter = {
      from: { row: 6, column: 1 },
      to: { row: lastDataRow, column: columns.length }
    };

    console.log("Excel generation completed");
    return await workbook.xlsx.writeBuffer();
  }

  // Generate CSV format for Employee Basic Info
  static async generateEmployeeBasicInfoCSV(employees, metadata, settings) {
    console.log("\n=== Starting CSV Generation ===");
    console.log("Settings received:", settings);

    // Handle null/undefined settings gracefully
    const safeSettings = settings || {};
    console.log("Safe settings:", safeSettings);
    console.log("Selected fields:", safeSettings.selectedFields);
    
    // Comprehensive field mappings for CSV - standardized with Excel
    const fieldMappings = {
      // Essential Information
      "companyEmployeeNumber": { header: "Employee #", key: "companyEmployeeNumber" },
      "firstName": { header: "First Name", key: "firstName" },
      "lastName": { header: "Last Name", key: "lastName" },
      "globalEmployeeId": { header: "Global Employee ID", key: "globalEmployeeId" },

      // Personal Information
      "dob": { header: "Date of Birth", key: "dob" },
      "gender": { header: "Gender", key: "gender" },
      "race": { header: "Race", key: "race" },
      "disabled": { header: "Disabled", key: "disabled" },
      "foreignNational": { header: "Foreign National", key: "foreignNational" },
      "notRSACitizen": { header: "Not RSA Citizen", key: "notRSACitizen" },

      // Contact Information
      "email": { header: "Email", key: "email" },
      "phone": { header: "Phone", key: "phone" },
      "mobileNumber": { header: "Mobile Number", key: "mobileNumber" },

      // Employment Information
      "jobTitle": { header: "Job Title", key: "jobTitle" },
      "department": { header: "Department", key: "department" },
      "costCentre": { header: "Cost Centre", key: "costCentre" },
      "workingHours": { header: "Working Hours", key: "workingHours" },
      "payFrequency": { header: "Pay Frequency", key: "payFrequency.name" },
      "doa": { header: "Date of Appointment", key: "doa" },
      "status": { header: "Employment Status", key: "status" },
      "employmentStatus": { header: "Active Status", key: "employmentStatus" },
      "isDirector": { header: "Is Director", key: "isDirector" },
      "typeOfDirector": { header: "Director Type", key: "typeOfDirector" },
      "isContractor": { header: "Is Contractor", key: "isContractor" },

      // Identification Information
      "idType": { header: "ID Type", key: "idType" },
      "idNumber": { header: "ID Number", key: "idNumber" },
      "passportNumber": { header: "Passport Number", key: "passportNumber" },
      "incomeTaxNumber": { header: "Income Tax Number", key: "incomeTaxNumber" },

      // Banking Information
      "paymentMethod": { header: "Payment Method", key: "paymentMethod" },
      "bank": { header: "Bank Name", key: "bank" },
      "accountType": { header: "Account Type", key: "accountType" },
      "accountNumber": { header: "Account Number", key: "accountNumber" },
      "branchCode": { header: "Branch Code", key: "branchCode" },
      "accountHolder": { header: "Account Holder", key: "accountHolder" },
      "holderRelationship": { header: "Holder Relationship", key: "holderRelationship" },

      // Address Information
      "streetAddress": { header: "Street Address", key: "streetAddress" },
      "suburb": { header: "Suburb", key: "suburb" },
      "city": { header: "City", key: "city" },
      "postalCode": { header: "Postal Code", key: "postalCode" },
      "province": { header: "Province", key: "province" },
      "unitNumber": { header: "Unit Number", key: "unitNumber" },
      "complex": { header: "Complex", key: "complex" },
      "streetNumber": { header: "Street Number", key: "streetNumber" },
      "street": { header: "Street", key: "street" },
      "code": { header: "Postal Code", key: "code" },
      "line1": { header: "Address Line 1", key: "line1" },
      "line2": { header: "Address Line 2", key: "line2" },
      "line3": { header: "Address Line 3", key: "line3" },

      // Employment Tax Incentive (ETI)
      "etiStatus": { header: "ETI Status", key: "etiStatus" },
      "etiEffectiveFrom": { header: "ETI Effective From", key: "etiEffectiveFrom" },
      "isETIEligible": { header: "ETI Eligible", key: "isETIEligible" },
      "monthlyRemuneration": { header: "Monthly Remuneration", key: "monthlyRemuneration" },
      "ageAtEmployment": { header: "Age at Employment", key: "ageAtEmployment" },
      "employmentStartDate": { header: "Employment Start Date", key: "employmentStartDate" },

      // Occupation Information
      "occupationLevel": { header: "Occupation Level", key: "occupationLevel" },
      "occupationCategory": { header: "Occupation Category", key: "occupationCategory" },
      "jobValue": { header: "Job Value", key: "jobValue" },

      // UIF Information
      "isUifExempt": { header: "UIF Exempt", key: "isUifExempt" },
      "uifExemptReason": { header: "UIF Exempt Reason", key: "uifExemptReason" },
      "uifStatusCode": { header: "UIF Status Code", key: "uifStatusCode" },

      // Working Hours Details
      "hoursPerDay": { header: "Hours Per Day", key: "hoursPerDay" },

      // Termination Information
      "terminationNoticeDate": { header: "Termination Notice Date", key: "terminationNoticeDate" },
      "lastDayOfService": { header: "Last Day of Service", key: "lastDayOfService" },
      "rehireEligibility": { header: "Rehire Eligible", key: "rehireEligibility" },
      "exitInterviewDate": { header: "Exit Interview Date", key: "exitInterviewDate" },
      "lastPayrollDate": { header: "Last Payroll Date", key: "lastPayrollDate" },

      // OID Information
      "oidEligible": { header: "OID Eligible", key: "oidEligible" },
      "oidExemptReason": { header: "OID Exempt Reason", key: "oidExemptReason" },

      // Self Service
      "selfServiceEnabled": { header: "Self Service Enabled", key: "selfServiceEnabled" },
      "lastActivity": { header: "Last Activity", key: "lastActivity" },
      "essEmailSent": { header: "ESS Email Sent", key: "essEmailSent" }
    };

    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Total Employees,${metadata.employeeCount}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Use selected fields or default to essential fields if none selected
    const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
      ? safeSettings.selectedFields
      : ["companyEmployeeNumber", "firstName", "lastName"];

    console.log("Using fields:", selectedFields);

    // Create headers array from selected fields
    const headers = selectedFields.map(field => {
      const mapping = fieldMappings[field];
      console.log(`Processing field: ${field} -> Mapping:`, mapping);
      return mapping?.header || field;
    });

    // Convert employee data to CSV rows
    const employeeRows = employees.map((emp, index) => {
      console.log(`\nProcessing employee ${index + 1}/${employees.length} (${emp.firstName} ${emp.lastName})`);
      
      const fields = selectedFields.map(field => {
        const mapping = fieldMappings[field];
        if (!mapping) {
          console.log(`No mapping found for field: ${field}`);
          return '';
        }

        // Handle nested properties (e.g., payFrequency.name)
        if (mapping.key.includes('.')) {
          const [obj, prop] = mapping.key.split('.');
          const value = emp[obj]?.[prop];
          console.log(`Accessing nested property ${mapping.key}: ${value}`);
          return value || '';
        }
        
        let value = emp[mapping.key];
        
        // Format dates
        if (value && (mapping.key === 'dob' || mapping.key === 'doa')) {
          value = new Date(value).toLocaleDateString();
        }
        
        console.log(`Accessing property ${mapping.key}: ${value}`);
        return value || '';
      });

      return fields.map(field => {
        if (field === null || field === undefined) return '""';
        if (typeof field === 'string' && field.includes(',')) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      }).join(",");
    });

    // Combine all parts
    const csvContent = [
      metadataRows,
      headers.join(","),
      ...employeeRows
    ].join("\n");

    console.log("CSV Headers:", headers.join(","));
    console.log("Number of data rows:", employeeRows.length);
    console.log("=== CSV Generation Complete ===\n");

    return Buffer.from(csvContent);
  }

  // Generate PDF format for Employee Basic Info
  static async generateEmployeeBasicInfoPDF(employees, metadata, settings) {
    console.log("\n=== Generating PDF Format ===");
    console.log("Settings for PDF generation:", JSON.stringify(settings, null, 2));

    // Handle null/undefined settings gracefully
    const safeSettings = settings || {};
    console.log("Safe settings:", safeSettings);

    const doc = new PDFDocument({ margin: 50 });
    const buffers = [];
    doc.on("data", buffers.push.bind(buffers));

    // Add metadata
    doc.fontSize(18).text(metadata.companyName, { align: "center" });
    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: "center" });
    doc.moveDown();

    if (metadata.physicalAddress) {
      doc.fontSize(10).text(`Physical Address: ${metadata.physicalAddress}`);
    }
    if (metadata.postalAddress) {
      doc.fontSize(10).text(`Postal Address: ${metadata.postalAddress}`);
    }

    doc.fontSize(10).text(`Generated: ${new Date().toLocaleString()}`);
    doc.text(`Total Employees: ${metadata.employeeCount}`);
    doc.moveDown();

    // Use selected fields or default to essential fields if none selected
    const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
      ? safeSettings.selectedFields
      : ["companyEmployeeNumber", "firstName", "lastName"];

    console.log("Using fields for PDF:", selectedFields);

    // Comprehensive field mappings for PDF labels
    const fieldLabels = {
      // Essential Information
      "companyEmployeeNumber": "Employee #",
      "firstName": "First Name",
      "lastName": "Last Name",
      "globalEmployeeId": "Global Employee ID",

      // Personal Information
      "dob": "Date of Birth",
      "gender": "Gender",
      "race": "Race",
      "disabled": "Disabled",
      "foreignNational": "Foreign National",
      "notRSACitizen": "Not RSA Citizen",

      // Contact Information
      "email": "Email",
      "phone": "Phone",
      "mobileNumber": "Mobile Number",

      // Employment Information
      "jobTitle": "Job Title",
      "department": "Department",
      "costCentre": "Cost Centre",
      "workingHours": "Working Hours",
      "payFrequency": "Pay Frequency",
      "doa": "Date of Appointment",
      "status": "Employment Status",
      "employmentStatus": "Active Status",
      "isDirector": "Is Director",
      "typeOfDirector": "Director Type",
      "isContractor": "Is Contractor",

      // Identification Information
      "idType": "ID Type",
      "idNumber": "ID Number",
      "passportNumber": "Passport Number",
      "incomeTaxNumber": "Income Tax Number",

      // Banking Information
      "paymentMethod": "Payment Method",
      "bank": "Bank Name",
      "accountType": "Account Type",
      "accountNumber": "Account Number",
      "branchCode": "Branch Code",
      "accountHolder": "Account Holder",
      "holderRelationship": "Holder Relationship",

      // Address Information
      "streetAddress": "Street Address",
      "suburb": "Suburb",
      "city": "City",
      "postalCode": "Postal Code",
      "province": "Province",
      "unitNumber": "Unit Number",
      "complex": "Complex",
      "streetNumber": "Street Number",
      "street": "Street",
      "code": "Postal Code",
      "line1": "Address Line 1",
      "line2": "Address Line 2",
      "line3": "Address Line 3",

      // Employment Tax Incentive (ETI)
      "etiStatus": "ETI Status",
      "etiEffectiveFrom": "ETI Effective From",
      "isETIEligible": "ETI Eligible",
      "monthlyRemuneration": "Monthly Remuneration",
      "ageAtEmployment": "Age at Employment",
      "employmentStartDate": "Employment Start Date",

      // Occupation Information
      "occupationLevel": "Occupation Level",
      "occupationCategory": "Occupation Category",
      "jobValue": "Job Value",

      // UIF Information
      "isUifExempt": "UIF Exempt",
      "uifExemptReason": "UIF Exempt Reason",
      "uifStatusCode": "UIF Status Code",

      // Working Hours Details
      "hoursPerDay": "Hours Per Day",

      // Termination Information
      "terminationNoticeDate": "Termination Notice Date",
      "lastDayOfService": "Last Day of Service",
      "rehireEligibility": "Rehire Eligible",
      "exitInterviewDate": "Exit Interview Date",
      "lastPayrollDate": "Last Payroll Date",

      // OID Information
      "oidEligible": "OID Eligible",
      "oidExemptReason": "OID Exempt Reason",

      // Self Service
      "selfServiceEnabled": "Self Service Enabled",
      "lastActivity": "Last Activity",
      "essEmailSent": "ESS Email Sent"
    };

    // Process each employee
    employees.forEach((emp, index) => {
      if (index > 0) doc.addPage();

      doc.fontSize(12).text("Employee Information", { underline: true });
      doc.moveDown();

      const details = [];

      // Process only selected fields
      selectedFields.forEach(field => {
        const label = fieldLabels[field] || field;
        let value = '';

        // Use the same logic as Excel generation for consistency
        if (emp[field] !== undefined && emp[field] !== null) {
          // Handle special cases for complex fields
          switch (field) {
            case 'dob':
            case 'doa':
            case 'etiEffectiveFrom':
            case 'employmentStartDate':
            case 'terminationNoticeDate':
            case 'lastDayOfService':
            case 'exitInterviewDate':
            case 'lastPayrollDate':
            case 'lastActivity':
              value = emp[field] ? new Date(emp[field]).toLocaleDateString() : '';
              break;
            case 'payFrequency':
              value = emp.payFrequency?.name || '';
              break;
            case 'employmentStatus':
            case 'isDirector':
            case 'isContractor':
            case 'disabled':
            case 'foreignNational':
            case 'notRSACitizen':
            case 'isUifExempt':
            case 'isETIEligible':
            case 'oidEligible':
            case 'selfServiceEnabled':
            case 'essEmailSent':
            case 'rehireEligibility':
              value = emp[field] ? 'Yes' : 'No';
              break;
            case 'bank':
              // Handle both 'bank' and 'bankName' fields
              value = emp.bank || emp.bankName || '';
              break;
            case 'mobileNumber':
              // Handle both 'mobileNumber' and 'phone' fields
              value = emp.mobileNumber || emp.phone || '';
              break;
            case 'postalCode':
              // Handle both 'postalCode' and 'code' fields
              value = emp.postalCode || emp.code || '';
              break;
            default:
              value = emp[field] || '';
          }
        } else {
          // Fallback for fields that might not exist or are null
          value = '';
        }

        if (value) {
          details.push([label, value]);
        }
      });

      details.forEach(([label, value]) => {
        doc.fontSize(10).text(`${label}: ${value}`);
      });
    });

    doc.end();

    return new Promise((resolve) => {
      doc.on("end", () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  static async generateTransactionHistory(companyId, format, settings, selectedEmployees = null, dateRange = null) {
    console.log("\n=== Generating Transaction History Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees, dateRange });

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build employee query - include all employees that might have payroll data
      let query = { company: companyId, status: { $in: ["Active", "Inactive", "Serving Notice"] } };
      if (selectedEmployees && selectedEmployees.length > 0) {
        query._id = { $in: selectedEmployees };
      }

      // Get employees with all fields and proper population
      const employees = await Employee.find(query)
        .populate("payFrequency")
        .populate("role")
        .populate("company")
        .sort({ lastName: 1, firstName: 1 });

      console.log(`Found ${employees.length} employees for transaction history report`);
      console.log("Employee details:", employees.map(emp => ({
        id: emp._id.toString(),
        name: `${emp.firstName} ${emp.lastName}`,
        companyEmployeeNumber: emp.companyEmployeeNumber,
        status: emp.status
      })));

      // Build payroll query with proper date filtering
      const payrollQuery = {
        company: companyId,
        employee: { $in: employees.map(emp => emp._id) }
        // Removed status filter to include all payroll records with data
      };

      // Apply date range filter if provided
      if (dateRange && dateRange.startDate && dateRange.endDate) {
        payrollQuery.month = {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate)
        };
        console.log(`Filtering payroll data from ${dateRange.startDate} to ${dateRange.endDate}`);
      } else {
        // Default to last 12 months if no date range specified
        const twelveMonthsAgo = new Date();
        twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
        payrollQuery.month = { $gte: twelveMonthsAgo };
        console.log(`No date range specified, using last 12 months from ${twelveMonthsAgo.toISOString()}`);
      }

      console.log("Payroll query:", JSON.stringify(payrollQuery, null, 2));

      // Get payroll data with proper population
      let payrollData = await Payroll.find(payrollQuery)
        .populate("employee", "companyEmployeeNumber firstName lastName jobTitle department costCentre")
        .sort({ month: -1, "employee.lastName": 1 });

      console.log(`Found ${payrollData.length} payroll records for transaction history report`);

      // Debug: Log sample payroll data to understand structure
      if (payrollData.length > 0) {
        console.log("Sample payroll record with employee data:", {
          id: payrollData[0]._id,
          employee: payrollData[0].employee,
          month: payrollData[0].month,
          basicSalary: payrollData[0].basicSalary
        });
      }

      // If no payroll data found, try a broader query to understand what's available
      if (payrollData.length === 0) {
        console.log("No payroll data found with current query, checking for any payroll records...");
        const allPayrollCount = await Payroll.countDocuments({ company: companyId });
        console.log(`Total payroll records in database for company: ${allPayrollCount}`);

        if (allPayrollCount > 0) {
          // Get sample records to understand the data structure
          const samplePayroll = await Payroll.findOne({ company: companyId })
            .populate("employee", "companyEmployeeNumber firstName lastName")
            .lean();
          console.log("Sample payroll record:", JSON.stringify(samplePayroll, null, 2));

          // Try to get any payroll records for these employees, regardless of status
          console.log("Attempting to retrieve any payroll records for selected employees...");
          const fallbackQuery = {
            company: companyId,
            employee: { $in: employees.map(emp => emp._id) }
          };

          // Apply same date filter
          if (payrollQuery.month) {
            fallbackQuery.month = payrollQuery.month;
          }

          payrollData = await Payroll.find(fallbackQuery)
            .populate("employee", "companyEmployeeNumber firstName lastName jobTitle department costCentre")
            .sort({ month: -1, "employee.lastName": 1 });

          console.log(`Fallback query found ${payrollData.length} payroll records`);
        }
      }

      // Ensure all payroll records have properly populated employee data
      console.log("Verifying employee population in payroll data...");
      const missingEmployeeIds = [];
      payrollData.forEach((payroll, index) => {
        if (!payroll.employee || typeof payroll.employee !== 'object' || !payroll.employee._id) {
          console.warn(`Payroll record ${index} has invalid employee population:`, {
            payrollId: payroll._id,
            employee: payroll.employee
          });
          // Collect employee IDs that need to be fetched
          if (payroll.employee && !missingEmployeeIds.includes(payroll.employee.toString())) {
            missingEmployeeIds.push(payroll.employee.toString());
          }
        }
      });

      // If we have missing employee data, fetch those employees separately
      if (missingEmployeeIds.length > 0) {
        console.log(`Fetching ${missingEmployeeIds.length} missing employees:`, missingEmployeeIds);
        const missingEmployees = await Employee.find({
          _id: { $in: missingEmployeeIds },
          company: companyId
        }).select("_id firstName lastName companyEmployeeNumber jobTitle department costCentre status");

        console.log(`Found ${missingEmployees.length} missing employees`);
        // Add missing employees to the employees array
        missingEmployees.forEach(emp => {
          if (!employees.find(existing => existing._id.toString() === emp._id.toString())) {
            employees.push(emp);
          }
        });

        console.log(`Total employees after adding missing: ${employees.length}`);
      }

      // Debug: Log sample payroll data before filtering
      if (payrollData.length > 0) {
        console.log("Sample payroll record before filtering:", {
          id: payrollData[0]._id,
          basicSalary: payrollData[0].basicSalary,
          commission: payrollData[0].commission,
          calculations: payrollData[0].calculations,
          status: payrollData[0].status,
          month: payrollData[0].month,
          allFields: Object.keys(payrollData[0].toObject ? payrollData[0].toObject() : payrollData[0])
        });
      }

      // Apply more lenient filtering - include records that have any payroll data or are valid payroll records
      const filteredPayrollData = payrollData.filter((payroll, index) => {
        // Debug each record being filtered
        const hasBasicSalary = payroll.basicSalary > 0;
        const hasCommission = payroll.commission > 0;
        const hasCalculations = payroll.calculations && (
          payroll.calculations.grossIncome > 0 ||
          payroll.calculations.netPay > 0 ||
          payroll.calculations.periodTax > 0
        );

        // More lenient criteria - include if:
        // 1. Has any basic salary (even 0 for unpaid leave scenarios)
        // 2. Has any calculations object (even if values are 0)
        // 3. Has a valid employee reference
        // 4. Has a valid month
        const shouldInclude = payroll.employee && payroll.month && (
               hasBasicSalary ||
               hasCommission ||
               payroll.lossOfIncome > 0 ||
               hasCalculations ||
               payroll.calculations || // Include if calculations object exists, even if empty
               payroll.basicSalary >= 0 || // Include even 0 salary records
               // Include if any allowances are present
               payroll.toolAllowance > 0 ||
               payroll.phoneAllowance > 0 ||
               payroll.computerAllowance > 0 ||
               payroll.uniformAllowance > 0 ||
               payroll.accommodationBenefit > 0 ||
               // Include if any deductions are present
               payroll.unionMembershipFee > 0 ||
               payroll.garnishee > 0 ||
               payroll.maintenanceOrder > 0 ||
               // Include if nested objects have values
               (payroll.travelAllowance && payroll.travelAllowance.calculationDetails && payroll.travelAllowance.calculationDetails.totalAllowance > 0) ||
               (payroll.medical && payroll.medical.medicalAid > 0) ||
               (payroll.pensionFund && (payroll.pensionFund.fixedContributionEmployee > 0 || payroll.pensionFund.employeeContribution > 0)) ||
               (payroll.providentFund && payroll.providentFund.employeeContribution > 0) ||
               (payroll.retirementAnnuityFund && payroll.retirementAnnuityFund.employeeContribution > 0) ||
               (payroll.employerLoan && payroll.employerLoan.regularRepayment > 0) ||
               (payroll.savings && payroll.savings.regularDeduction > 0)
        );

        // Log first few records for debugging
        if (index < 3) {
          console.log(`Payroll record ${index} filtering:`, {
            id: payroll._id,
            hasBasicSalary,
            basicSalaryValue: payroll.basicSalary,
            hasCommission,
            commissionValue: payroll.commission,
            hasCalculations,
            calculationsValue: payroll.calculations,
            hasEmployee: !!payroll.employee,
            hasMonth: !!payroll.month,
            shouldInclude,
            status: payroll.status
          });
        }

        return shouldInclude;
      });

      console.log(`Filtered to ${filteredPayrollData.length} payroll records with transaction data`);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Transaction History Report",
        generatedDate: new Date().toISOString(),
        dateRange: dateRange ? {
          start: new Date(dateRange.startDate).toLocaleDateString(),
          end: new Date(dateRange.endDate).toLocaleDateString()
        } : {
          start: payrollQuery.month.$gte.toLocaleDateString(),
          end: new Date().toLocaleDateString()
        },
        employeeCount: employees.length,
        transactionCount: filteredPayrollData.length
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generateTransactionHistoryExcel(employees, filteredPayrollData, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generateTransactionHistoryPDF(employees, filteredPayrollData, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generateTransactionHistoryCSV(employees, filteredPayrollData, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating transaction history report:", error);
      throw error;
    }
  }

  // Generate Excel format for Transaction History
  static async generateTransactionHistoryExcel(employees, payrollData, metadata, settings) {
    console.log("\n=== Generating Transaction History Excel ===");
    console.log(`Processing ${employees.length} employees and ${payrollData.length} payroll records`);

    // Log sample data for debugging
    if (payrollData.length > 0) {
      console.log("Sample payroll record structure:", {
        id: payrollData[0]._id,
        employee: payrollData[0].employee,
        month: payrollData[0].month,
        basicSalary: payrollData[0].basicSalary,
        status: payrollData[0].status,
        hasCalculations: !!payrollData[0].calculations
      });
    }

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Transaction History");

    // Set default font and freeze panes
    worksheet.views = [{ state: 'frozen', ySplit: 6 }];

    // Row 1 - Company Trading Name
    worksheet.mergeCells('A1:E1');
    const companyNameCell = worksheet.getCell('A1');
    companyNameCell.value = metadata.companyName;
    companyNameCell.font = { name: 'Arial', size: 12, bold: true };
    companyNameCell.alignment = { vertical: 'middle' };
    console.log("Set company trading name in row 1:", companyNameCell.value);

    // Row 2 - Report Name
    worksheet.mergeCells('A2:E2');
    const reportNameCell = worksheet.getCell('A2');
    reportNameCell.value = 'Transaction History Report';
    reportNameCell.font = { name: 'Arial', size: 8, bold: true };
    reportNameCell.alignment = { vertical: 'middle' };

    // Row 3 - Period
    worksheet.mergeCells('A3:E3');
    const periodCell = worksheet.getCell('A3');
    periodCell.value = metadata.dateRange 
      ? `Period: ${metadata.dateRange.start} to ${metadata.dateRange.end}`
      : `Generated on: ${new Date().toLocaleDateString()}`;
    periodCell.font = { name: 'Arial', size: 8, bold: true };
    periodCell.alignment = { vertical: 'middle' };

    // Row 4 - Counts
    worksheet.mergeCells('A4:E4');
    const countCell = worksheet.getCell('A4');
    countCell.value = `Employees: ${metadata.employeeCount} | Transactions: ${metadata.transactionCount}`;
    countCell.font = { name: 'Arial', size: 8, bold: true };
    countCell.alignment = { vertical: 'middle' };

    // Row 5 - Blank
    worksheet.addRow([]);

    // Define columns - combining employee and payroll fields
    const columns = [
      // Employee Details
      { header: "Month", key: "month", width: 15 },
      { header: "Employee #", key: "companyEmployeeNumber", width: 15 },
      { header: "First Name", key: "firstName", width: 20 },
      { header: "Last Name", key: "lastName", width: 20 },
      { header: "Job Title", key: "jobTitle", width: 25 },
      { header: "Department", key: "department", width: 20 },
      { header: "Cost Centre", key: "costCentre", width: 20 },

      // Payroll Components - Basic
      { header: "Basic Salary", key: "basicSalary", width: 15 },
      { header: "Gross Income", key: "grossIncome", width: 15 },
      { header: "Net Pay", key: "netPay", width: 15 },
      { header: "PAYE", key: "paye", width: 15 },

      // Income Items
      { header: "Commission", key: "commission", width: 15 },
      { header: "Loss of Income", key: "lossOfIncome", width: 15 },
      { header: "Annual Bonus", key: "annualBonus", width: 15 },
      { header: "Annual Payment", key: "annualPayment", width: 15 },

      // Allowances
      { header: "Travel Allowance", key: "travelAllowance", width: 15 },
      { header: "Tool Allowance", key: "toolAllowance", width: 15 },
      { header: "Phone Allowance", key: "phoneAllowance", width: 15 },
      { header: "Computer Allowance", key: "computerAllowance", width: 15 },
      { header: "Uniform Allowance", key: "uniformAllowance", width: 15 },

      // Benefits
      { header: "Accommodation Benefit", key: "accommodationBenefit", width: 15 },
      { header: "Company Car Benefit", key: "companyCar", width: 15 },
      { header: "Medical Aid", key: "medical", width: 15 },

      // Deductions
      { header: "Pension Fund", key: "pensionFund", width: 15 },
      { header: "Provident Fund", key: "providentFund", width: 15 },
      { header: "Retirement Annuity", key: "retirementAnnuityFund", width: 15 },
      { header: "Union Fee", key: "unionMembershipFee", width: 15 },
      { header: "Garnishee Order", key: "garnishee", width: 15 },
      { header: "Maintenance Order", key: "maintenanceOrder", width: 15 },

      // Loans and Savings
      { header: "Employer Loan", key: "employerLoan", width: 15 },
      { header: "Savings", key: "savings", width: 15 },

      // Status and Metadata
      { header: "Status", key: "status", width: 15 },
      { header: "Finalised Date", key: "finalisedDate", width: 20 }
    ];

    // Set up worksheet columns properly for ExcelJS
    worksheet.columns = columns;
    console.log("Excel worksheet columns configured:", columns.length, "columns");

    // Add header row at row 6 only
    const headerValues = columns.map(col => col.header);
    const transactionHeaderRow = worksheet.getRow(6);
    headerValues.forEach((header, index) => {
      transactionHeaderRow.getCell(index + 1).value = header;
    });

    // Style the header row (row 6)
    const styledHeaderRow = worksheet.getRow(6);
    styledHeaderRow.font = { name: 'Arial', size: 9, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Helper function to safely get nested values
    const safeGet = (obj, path, defaultValue = 0) => {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : defaultValue;
      }, obj);
    };

    // Helper function to format currency values
    const formatCurrency = (value) => {
      const num = parseFloat(value) || 0;
      return Math.round(num * 100) / 100; // Round to 2 decimal places
    };

    // Add transaction data with improved data mapping
    if (payrollData.length === 0) {
      console.log("No payroll data found - adding 'no data' message row");
      // Add a message row when no data is found
      const noDataRow = worksheet.addRow([
        "No transaction data found for the selected criteria",
        "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""
      ]);
      noDataRow.font = { name: 'Arial', size: 10, italic: true };
      noDataRow.getCell(1).alignment = { horizontal: 'center' };
      worksheet.mergeCells(`A7:AG7`);
    } else {
      console.log(`Starting to process ${payrollData.length} payroll records for Excel generation`);
      let rowsAdded = 0;

      payrollData.forEach((payroll, index) => {
        // Get employee data - handle both populated and non-populated cases
        let employee;
        if (payroll.employee && typeof payroll.employee === 'object' && payroll.employee._id) {
          // Employee is populated
          employee = payroll.employee;
          console.log(`Using populated employee data for payroll ${payroll._id}:`, {
            id: employee._id,
            firstName: employee.firstName,
            lastName: employee.lastName,
            companyEmployeeNumber: employee.companyEmployeeNumber
          });
        } else {
          // Employee is not populated, find in employees array
          const employeeId = payroll.employee?.toString() || payroll.employee;
          employee = employees.find(emp => emp._id.toString() === employeeId);
          console.log(`Looking up employee ${employeeId} in employees array:`, {
            found: !!employee,
            employeeData: employee ? {
              id: employee._id,
              firstName: employee.firstName,
              lastName: employee.lastName,
              companyEmployeeNumber: employee.companyEmployeeNumber
            } : null
          });
        }

        if (!employee) {
          console.warn(`Employee not found for payroll record: ${payroll._id}`, {
            payrollEmployee: payroll.employee,
            availableEmployees: employees.map(emp => ({ id: emp._id.toString(), name: `${emp.firstName} ${emp.lastName}` }))
          });
          return;
        }

        console.log(`Processing payroll record ${index + 1}/${payrollData.length} for employee: ${employee.firstName} ${employee.lastName} (${employee.companyEmployeeNumber})`);

        const rowData = {
          month: payroll.month ? new Date(payroll.month).toLocaleDateString('en-ZA') : "",
          companyEmployeeNumber: employee.companyEmployeeNumber || "",
          firstName: employee.firstName || "",
          lastName: employee.lastName || "",
          jobTitle: employee.jobTitle || "",
          department: employee.department || "",
          costCentre: employee.costCentre || "",

          // Basic payroll components
          basicSalary: formatCurrency(payroll.basicSalary),
          grossIncome: formatCurrency(safeGet(payroll, 'calculations.grossIncome')),
          netPay: formatCurrency(safeGet(payroll, 'calculations.netPay')),
          paye: formatCurrency(safeGet(payroll, 'calculations.periodTax')),

          // Income items
          commission: formatCurrency(payroll.commission),
          lossOfIncome: formatCurrency(payroll.lossOfIncome),
          annualBonus: formatCurrency(safeGet(payroll, 'annualBonus.amount')),
          annualPayment: formatCurrency(safeGet(payroll, 'annualPayment.amount')),

          // Allowances
          travelAllowance: formatCurrency(safeGet(payroll, 'travelAllowance.calculationDetails.totalAllowance')),
          toolAllowance: formatCurrency(payroll.toolAllowance),
          phoneAllowance: formatCurrency(payroll.phoneAllowance),
          computerAllowance: formatCurrency(payroll.computerAllowance),
          uniformAllowance: formatCurrency(payroll.uniformAllowance),

          // Benefits
          accommodationBenefit: formatCurrency(payroll.accommodationBenefit),
          companyCar: formatCurrency(safeGet(payroll, 'companyCar.deemedValue')),
          medical: formatCurrency(safeGet(payroll, 'medical.medicalAid')),

          // Deductions
          pensionFund: formatCurrency(safeGet(payroll, 'pensionFund.fixedContributionEmployee') || safeGet(payroll, 'pensionFund.employeeContribution')),
          providentFund: formatCurrency(safeGet(payroll, 'providentFund.employeeContribution')),
          retirementAnnuityFund: formatCurrency(safeGet(payroll, 'retirementAnnuityFund.employeeContribution')),
          unionMembershipFee: formatCurrency(payroll.unionMembershipFee),
          garnishee: formatCurrency(payroll.garnishee),
          maintenanceOrder: formatCurrency(payroll.maintenanceOrder),

          // Loans and Savings
          employerLoan: formatCurrency(safeGet(payroll, 'employerLoan.regularRepayment')),
          savings: formatCurrency(safeGet(payroll, 'savings.regularDeduction')),

          // Status and metadata
          status: payroll.status || "draft",
          finalisedDate: payroll.finalisedDate ? new Date(payroll.finalisedDate).toLocaleDateString('en-ZA') : ""
        };

        console.log(`Adding row ${index + 1} with data:`, {
          employee: `${rowData.firstName} ${rowData.lastName} (${rowData.companyEmployeeNumber})`,
          month: rowData.month,
          basicSalary: rowData.basicSalary,
          status: rowData.status
        });

        console.log(`Full rowData object for row ${index + 1}:`, JSON.stringify(rowData, null, 2));

        const row = worksheet.addRow(rowData);
        rowsAdded++;

        console.log(`Successfully added row ${rowsAdded} to worksheet. Row number: ${row.number}`);
        console.log(`Row values after adding:`, row.values);

        // Verify the row was actually added with data
        const verifyRow = worksheet.getRow(row.number);
        console.log(`Verification - Row ${row.number} cell values:`, {
          month: verifyRow.getCell(1).value,
          employeeNumber: verifyRow.getCell(2).value,
          firstName: verifyRow.getCell(3).value,
          lastName: verifyRow.getCell(4).value,
          basicSalary: verifyRow.getCell(8).value
        });

        // Style data row
        row.eachCell({ includeEmpty: true }, cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
          cell.alignment = { vertical: 'middle', horizontal: 'left' };
          cell.font = { name: 'Arial', size: 8 };

          // Format number cells
          if (typeof cell.value === 'number') {
            cell.numFmt = '#,##0.00';
            cell.alignment = { horizontal: 'right' };
          }
        });
      });

      console.log(`Completed Excel generation: ${rowsAdded} rows added to worksheet`);
    }

    // Add borders and background to header row - only for columns with data
    for (let i = 1; i <= columns.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.font = { name: 'Arial', size: 9, bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    }

    // Add auto-filter for the data range
    const lastDataRow = 6 + payrollData.length;
    worksheet.autoFilter = {
      from: { row: 6, column: 1 },
      to: { row: lastDataRow, column: columns.length }
    };

    return await workbook.xlsx.writeBuffer();
  }

  // Generate CSV format for Transaction History
  static async generateTransactionHistoryCSV(employees, payrollData, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      metadata.dateRange ? `Period,${metadata.dateRange.start} to ${metadata.dateRange.end}` : "",
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Total Employees,${metadata.employeeCount}`,
      `Total Transactions,${metadata.transactionCount}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Define headers (same as Excel columns)
    const headers = [
      "Month",
      "Employee #",
      "First Name",
      "Last Name",
      "Job Title",
      "Department",
      "Cost Centre",
      "Basic Salary",
      "Gross",
      "Net Pay",
      "PAYE",
      "Commission",
      "Loss of Income",
      "Annual Bonus",
      "Annual Payment",
      "Travel Allowance",
      "Tool Allowance",
      "Phone Allowance",
      "Computer Allowance",
      "Uniform Allowance",
      "Accommodation Benefit",
      "Company Car Benefit",
      "Medical Aid",
      "Pension Fund",
      "Provident Fund",
      "Retirement Annuity",
      "Union Fee",
      "Garnishee Order",
      "Maintenance Order",
      "Employer Loan",
      "Savings",
      "Status",
      "Finalised Date"
    ].join(",");

    // Convert transaction data to CSV rows with improved data mapping
    const transactionRows = payrollData.length === 0 ?
      ["No transaction data found for the selected criteria"] :
      payrollData.map(payroll => {
      // Get employee data - handle both populated and non-populated cases
      let employee;
      if (payroll.employee && typeof payroll.employee === 'object' && payroll.employee._id) {
        // Employee is populated
        employee = payroll.employee;
      } else {
        // Employee is not populated, find in employees array
        const employeeId = payroll.employee?.toString() || payroll.employee;
        employee = employees.find(emp => emp._id.toString() === employeeId);
      }

      if (!employee) {
        console.warn(`Employee not found for payroll record in CSV generation: ${payroll._id}`, {
          payrollEmployee: payroll.employee,
          availableEmployees: employees.map(emp => ({ id: emp._id.toString(), name: `${emp.firstName} ${emp.lastName}` }))
        });
        return null;
      }

      // Helper function to safely get nested values
      const safeGet = (obj, path, defaultValue = 0) => {
        return path.split('.').reduce((current, key) => {
          return current && current[key] !== undefined ? current[key] : defaultValue;
        }, obj);
      };

      // Helper function to format currency values
      const formatCurrency = (value) => {
        const num = parseFloat(value) || 0;
        return Math.round(num * 100) / 100; // Round to 2 decimal places
      };

      return [
        payroll.month ? new Date(payroll.month).toLocaleDateString('en-ZA') : "",
        employee.companyEmployeeNumber || "",
        employee.firstName || "",
        employee.lastName || "",
        employee.jobTitle || "",
        employee.department || "",
        employee.costCentre || "",
        formatCurrency(payroll.basicSalary),
        formatCurrency(safeGet(payroll, 'calculations.grossIncome')),
        formatCurrency(safeGet(payroll, 'calculations.netPay')),
        formatCurrency(safeGet(payroll, 'calculations.periodTax')),
        formatCurrency(payroll.commission),
        formatCurrency(payroll.lossOfIncome),
        formatCurrency(safeGet(payroll, 'annualBonus.amount')),
        formatCurrency(safeGet(payroll, 'annualPayment.amount')),
        formatCurrency(safeGet(payroll, 'travelAllowance.calculationDetails.totalAllowance')),
        formatCurrency(payroll.toolAllowance),
        formatCurrency(payroll.phoneAllowance),
        formatCurrency(payroll.computerAllowance),
        formatCurrency(payroll.uniformAllowance),
        formatCurrency(payroll.accommodationBenefit),
        formatCurrency(safeGet(payroll, 'companyCar.deemedValue')),
        formatCurrency(safeGet(payroll, 'medical.medicalAid')),
        formatCurrency(safeGet(payroll, 'pensionFund.fixedContributionEmployee') || safeGet(payroll, 'pensionFund.employeeContribution')),
        formatCurrency(safeGet(payroll, 'providentFund.employeeContribution')),
        formatCurrency(safeGet(payroll, 'retirementAnnuityFund.employeeContribution')),
        formatCurrency(payroll.unionMembershipFee),
        formatCurrency(payroll.garnishee),
        formatCurrency(payroll.maintenanceOrder),
        formatCurrency(safeGet(payroll, 'employerLoan.regularRepayment')),
        formatCurrency(safeGet(payroll, 'savings.regularDeduction')),
        payroll.status || "draft",
        payroll.finalisedDate ? new Date(payroll.finalisedDate).toLocaleDateString('en-ZA') : ""
      ].map(field => {
        if (field === null || field === undefined) return '""';
        if (typeof field === 'string' && field.includes(',')) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      }).join(",");
    }).filter(Boolean);

    // Combine all parts
    const csvContent = [metadataRows, headers, ...transactionRows].join("\n");

    return Buffer.from(csvContent);
  }

  // Generate Employment Tax Incentive Report
  static async generateEmploymentTaxIncentive(companyId, format, settings, dateRange = null) {
    try {
      console.log("\n=== Generating Employment Tax Incentive Report ===");
      console.log("Parameters:", { companyId, format, dateRange });

      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Get all employees with ETI status
      const employees = await Employee.find({
        company: companyId,
        status: { $in: ["Active", "Inactive"] }
      }).populate("payFrequency");

      console.log(`Found ${employees.length} employees for ETI report`);

      // Filter employees with ETI status and calculate ETI data
      const etiEmployees = [];
      for (const employee of employees) {
        if (employee.etiStatus && employee.etiStatus !== "Disqualified") {
          // Get latest payroll data for ETI calculations
          const latestPayroll = await Payroll.findOne({
            employee: employee._id,
            company: companyId
          }).sort({ month: -1 });

          // Calculate employment duration in months
          const employmentStartDate = employee.doa || employee.employmentStartDate;
          const employmentDuration = employmentStartDate ?
            Math.floor((new Date() - new Date(employmentStartDate)) / (1000 * 60 * 60 * 24 * 30.44)) : 0;

          // Calculate age at employment
          const ageAtEmployment = employee.dob && employmentStartDate ?
            Math.floor((new Date(employmentStartDate) - new Date(employee.dob)) / (1000 * 60 * 60 * 24 * 365.25)) : null;

          // Get monthly remuneration
          const monthlyRemuneration = latestPayroll?.basicSalary || employee.basicSalary || 0;

          // Calculate ETI amount based on current rules
          let etiAmount = 0;
          let etiPhase = null;
          let eligibilityStatus = "Eligible";

          if (employee.etiStatus === "Qualified - Claiming") {
            if (employmentDuration <= 24) { // ETI is available for 24 months
              if (monthlyRemuneration >= 2000 && monthlyRemuneration <= 6500) {
                if (monthlyRemuneration <= 4500) {
                  // Phase 1: Full ETI amount
                  etiAmount = employmentDuration <= 12 ? 1000 : 500;
                  etiPhase = employmentDuration <= 12 ? "Phase 1 (Months 1-12)" : "Phase 2 (Months 13-24)";
                } else {
                  // Phase 2: Reduced ETI amount
                  const baseAmount = employmentDuration <= 12 ? 1000 : 500;
                  etiAmount = baseAmount - (baseAmount * (monthlyRemuneration - 4500) / 2000);
                  etiPhase = employmentDuration <= 12 ? "Phase 1 (Months 1-12)" : "Phase 2 (Months 13-24)";
                }
              } else {
                eligibilityStatus = monthlyRemuneration < 2000 ? "Below minimum wage" : "Above maximum wage";
              }
            } else {
              eligibilityStatus = "Exceeded 24-month limit";
            }
          }

          etiEmployees.push({
            employeeNumber: employee.companyEmployeeNumber,
            firstName: employee.firstName,
            lastName: employee.lastName,
            idNumber: employee.idNumber,
            employmentStartDate: employmentStartDate,
            etiStatus: employee.etiStatus,
            etiEffectiveFrom: employee.etiEffectiveFrom,
            monthlyRemuneration: monthlyRemuneration,
            employmentDuration: employmentDuration,
            ageAtEmployment: ageAtEmployment,
            etiAmount: Math.round(etiAmount * 100) / 100,
            etiPhase: etiPhase,
            eligibilityStatus: eligibilityStatus,
            totalMonthsClaimed: employee.etiCalculation?.totalMonthsClaimed || 0,
            yearToDateETI: employee.etiCalculation?.yearToDate || 0
          });
        }
      }

      console.log(`Found ${etiEmployees.length} employees with ETI status`);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Employment Tax Incentive Report",
        generatedDate: new Date().toISOString(),
        dateRange: dateRange,
        totalEmployees: employees.length,
        etiEmployeeCount: etiEmployees.length,
        totalETIAmount: etiEmployees.reduce((sum, emp) => sum + emp.etiAmount, 0),
        claimingEmployees: etiEmployees.filter(emp => emp.etiStatus === "Qualified - Claiming").length,
        notClaimingEmployees: etiEmployees.filter(emp => emp.etiStatus === "Qualified - Not Claiming").length
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generateEmploymentTaxIncentiveExcel(etiEmployees, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generateEmploymentTaxIncentivePDF(etiEmployees, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generateEmploymentTaxIncentiveCSV(etiEmployees, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating Employment Tax Incentive report:", error);
      throw error;
    }
  }

  // Generate PDF format for Transaction History
  static async generateTransactionHistoryPDF(employees, payrollData, metadata, settings) {
    console.log("\n=== Generating Transaction History PDF ===");
    console.log(`Processing ${employees.length} employees and ${payrollData.length} payroll records`);

    const doc = new PDFDocument({ margin: 50, size: 'A3', layout: 'landscape' });
    const buffers = [];
    doc.on('data', buffers.push.bind(buffers));

    // Add metadata
    doc.fontSize(18).text(metadata.companyName, { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: 'center' });
    doc.moveDown();

    if (metadata.dateRange) {
      doc.fontSize(10).text(`Period: ${metadata.dateRange.start} to ${metadata.dateRange.end}`);
    }

    doc.fontSize(10).text(`Generated: ${new Date().toLocaleString()}`);
    doc.text(`Employees: ${metadata.employeeCount} | Transactions: ${metadata.transactionCount}`);
    doc.moveDown();

    // Helper function to safely get nested values
    const safeGet = (obj, path, defaultValue = 0) => {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : defaultValue;
      }, obj);
    };

    // Define table headers
    const headers = [
      "Month",
      "Employee #",
      "Name",
      "Basic Salary",
      "Gross",
      "Net Pay",
      "PAYE",
      "Benefits",
      "Deductions",
      "Status"
    ];

    // Calculate column widths for A3 landscape
    const pageWidth = doc.page.width - 100;
    const columnWidths = [
      pageWidth * 0.08,  // Month
      pageWidth * 0.08,  // Employee #
      pageWidth * 0.15,  // Name
      pageWidth * 0.12,  // Basic Salary
      pageWidth * 0.12,  // Gross
      pageWidth * 0.12,  // Net Pay
      pageWidth * 0.10,  // PAYE
      pageWidth * 0.11,  // Benefits
      pageWidth * 0.11,  // Deductions
      pageWidth * 0.08   // Status
    ];

    // Draw headers
    let x = 50;
    let y = doc.y;
    headers.forEach((header, i) => {
      doc.fontSize(8)
         .text(header, x, y, {
           width: columnWidths[i],
           align: 'center'
         });
      x += columnWidths[i];
    });

    doc.moveDown();
    y = doc.y;

    // Add transaction data with improved employee handling
    if (payrollData.length === 0) {
      doc.fontSize(10).text("No transaction data found for the selected criteria", { align: 'center' });
    } else {
      console.log(`Starting to process ${payrollData.length} payroll records for PDF generation`);
      let rowsAdded = 0;

      payrollData.forEach((payroll, index) => {
        // Get employee data - handle both populated and non-populated cases
        let employee;
        if (payroll.employee && typeof payroll.employee === 'object' && payroll.employee._id) {
          // Employee is populated
          employee = payroll.employee;
        } else {
          // Employee is not populated, find in employees array
          const employeeId = payroll.employee?.toString() || payroll.employee;
          employee = employees.find(emp => emp._id.toString() === employeeId);
        }

        if (!employee) {
          console.warn(`Employee not found for payroll record in PDF generation: ${payroll._id}`);
          return;
        }

        console.log(`Processing PDF row ${index + 1}/${payrollData.length} for employee: ${employee.firstName} ${employee.lastName}`);

        // Calculate totals
        const benefits = (payroll.accommodationBenefit || 0) +
                        (safeGet(payroll, 'companyCar.deemedValue') || 0) +
                        (safeGet(payroll, 'medical.medicalAid') || 0);

        const deductions = (safeGet(payroll, 'pensionFund.fixedContributionEmployee') || safeGet(payroll, 'pensionFund.employeeContribution') || 0) +
                          (safeGet(payroll, 'providentFund.employeeContribution') || 0) +
                          (payroll.unionMembershipFee || 0) +
                          (payroll.garnishee || 0) +
                          (payroll.maintenanceOrder || 0);

        // Add row data
        x = 50;
        const rowData = [
          payroll.month ? new Date(payroll.month).toLocaleDateString('en-ZA') : "",
          employee.companyEmployeeNumber || "",
          `${employee.firstName || ""} ${employee.lastName || ""}`.trim(),
          (payroll.basicSalary || 0).toFixed(2),
          (safeGet(payroll, 'calculations.grossIncome') || 0).toFixed(2),
          (safeGet(payroll, 'calculations.netPay') || 0).toFixed(2),
          (safeGet(payroll, 'calculations.periodTax') || 0).toFixed(2),
          benefits.toFixed(2),
          deductions.toFixed(2),
          payroll.status || "draft"
        ];

        // Draw row
        rowData.forEach((text, i) => {
          doc.fontSize(7)
             .text(text, x, y, {
               width: columnWidths[i],
               align: i > 2 && i < 9 ? 'right' : 'left' // Right align amounts
             });
          x += columnWidths[i];
        });

        y += 15;
        rowsAdded++;

        // Add new page if needed
        if (y > doc.page.height - 100) {
          doc.addPage({ margin: 50, size: 'A3', layout: 'landscape' });
          y = 50;

          // Redraw headers on new page
          x = 50;
          headers.forEach((header, i) => {
            doc.fontSize(8)
               .text(header, x, y, {
                 width: columnWidths[i],
                 align: 'center'
               });
            x += columnWidths[i];
          });

          y += 20;
        }
      });

      console.log(`Completed PDF generation: ${rowsAdded} rows added to document`);
    }

    doc.end();

    return new Promise((resolve) => {
      doc.on('end', () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  static async generatePayrollVarianceCSV(variances, metadata) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Period,${metadata.period.current} vs ${metadata.period.previous}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Headers
    const headers = [
      "Employee No.",
      "First Name",
      "Last Name",
      "Wage Type",
      "Transaction Type",
      "Previous Period Amount",
      "Current Period Amount",
      "Variance"
    ].join(",");

    // Transaction rows
    const transactionRows = variances.transactions.map(t => 
      this.formatCSVRow([
        t.employeeNo,
        t.firstName,
        t.lastName,
        t.wageType,
        t.type,
        t.previousAmount.toFixed(2),
        t.currentAmount.toFixed(2),
        t.variance.toFixed(2)
      ])
    );

    // Combine all parts
    const csvContent = [
      metadataRows,
      headers,
      ...transactionRows
    ].join("\n");

    return Buffer.from(csvContent);
  }

  // Helper method to format CSV rows
  static formatCSVRow(fields) {
    return fields.map(field => {
      if (field === null || field === undefined) return '""';
      if (typeof field === 'string' && field.includes(',')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    }).join(",");
  }

  // Generate PDF format for Employment Tax Incentive Report
  static async generateEmploymentTaxIncentivePDF(etiEmployees, metadata, settings) {
    const doc = new PDFDocument({ margin: 50, size: 'A4', layout: 'portrait' });
    const buffers = [];
    doc.on('data', buffers.push.bind(buffers));

    // Header
    doc.fontSize(18).text(metadata.companyName, { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Generated: ${new Date(metadata.generatedDate).toLocaleDateString()}`, { align: 'center' });
    doc.moveDown();

    // Summary section
    doc.fontSize(12).text('ETI Summary', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(10);
    doc.text(`Total Employees: ${metadata.totalEmployees}`);
    doc.text(`ETI Eligible Employees: ${metadata.etiEmployeeCount}`);
    doc.text(`Claiming ETI: ${metadata.claimingEmployees}`);
    doc.text(`Not Claiming ETI: ${metadata.notClaimingEmployees}`);
    doc.text(`Total Monthly ETI Amount: R${metadata.totalETIAmount.toFixed(2)}`);
    doc.moveDown();

    // Table headers
    const startY = doc.y;
    const colWidths = [60, 80, 80, 60, 80, 60, 80];
    const headers = ['Emp No', 'Name', 'ID Number', 'ETI Status', 'Monthly Salary', 'ETI Amount', 'Phase'];

    let currentX = 50;
    doc.fontSize(9).fillColor('black');

    // Draw headers
    headers.forEach((header, i) => {
      doc.text(header, currentX, startY, { width: colWidths[i], align: 'left' });
      currentX += colWidths[i];
    });

    doc.moveDown();
    let currentY = doc.y;

    // Draw employee data
    etiEmployees.forEach((employee, index) => {
      if (currentY > 700) { // Start new page if needed
        doc.addPage();
        currentY = 50;
      }

      currentX = 50;
      const rowData = [
        employee.employeeNumber || 'N/A',
        `${employee.firstName} ${employee.lastName}`,
        employee.idNumber || 'N/A',
        employee.etiStatus,
        `R${employee.monthlyRemuneration.toFixed(2)}`,
        `R${employee.etiAmount.toFixed(2)}`,
        employee.etiPhase || 'N/A'
      ];

      rowData.forEach((data, i) => {
        doc.text(data, currentX, currentY, { width: colWidths[i], align: 'left' });
        currentX += colWidths[i];
      });

      currentY += 15;
      doc.y = currentY;
    });

    // Footer
    doc.fontSize(8).fillColor('gray');
    doc.text(`Report generated on ${new Date().toLocaleString()}`, 50, doc.page.height - 50);

    return new Promise((resolve) => {
      doc.end();
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers);
        resolve(pdfBuffer);
      });
    });
  }

  // Generate Excel format for Employment Tax Incentive Report
  static async generateEmploymentTaxIncentiveExcel(etiEmployees, metadata, settings) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('ETI Report');

    // Add metadata
    worksheet.addRow([metadata.companyName]);
    worksheet.addRow([metadata.reportName]);
    worksheet.addRow([`Generated: ${new Date(metadata.generatedDate).toLocaleDateString()}`]);
    worksheet.addRow([]); // Empty row

    // Add summary
    worksheet.addRow(['ETI Summary']);
    worksheet.addRow(['Total Employees:', metadata.totalEmployees]);
    worksheet.addRow(['ETI Eligible Employees:', metadata.etiEmployeeCount]);
    worksheet.addRow(['Claiming ETI:', metadata.claimingEmployees]);
    worksheet.addRow(['Not Claiming ETI:', metadata.notClaimingEmployees]);
    worksheet.addRow(['Total Monthly ETI Amount:', `R${metadata.totalETIAmount.toFixed(2)}`]);
    worksheet.addRow([]); // Empty row

    // Add headers
    const headers = [
      'Employee Number',
      'First Name',
      'Last Name',
      'ID Number',
      'Employment Start Date',
      'ETI Status',
      'ETI Effective From',
      'Monthly Remuneration',
      'Employment Duration (Months)',
      'Age at Employment',
      'ETI Amount',
      'ETI Phase',
      'Eligibility Status',
      'Total Months Claimed',
      'Year to Date ETI'
    ];

    const etiHeaderRow = worksheet.addRow(headers);
    etiHeaderRow.font = { bold: true };
    etiHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add employee data
    etiEmployees.forEach(employee => {
      worksheet.addRow([
        employee.employeeNumber || '',
        employee.firstName || '',
        employee.lastName || '',
        employee.idNumber || '',
        employee.employmentStartDate ? new Date(employee.employmentStartDate).toLocaleDateString() : '',
        employee.etiStatus || '',
        employee.etiEffectiveFrom ? new Date(employee.etiEffectiveFrom).toLocaleDateString() : '',
        employee.monthlyRemuneration || 0,
        employee.employmentDuration || 0,
        employee.ageAtEmployment || '',
        employee.etiAmount || 0,
        employee.etiPhase || '',
        employee.eligibilityStatus || '',
        employee.totalMonthsClaimed || 0,
        employee.yearToDateETI || 0
      ]);
    });

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  // Generate CSV format for Employment Tax Incentive Report
  static async generateEmploymentTaxIncentiveCSV(etiEmployees, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date(metadata.generatedDate).toLocaleDateString()}`,
      "", // Empty line for spacing
      "ETI Summary",
      `Total Employees,${metadata.totalEmployees}`,
      `ETI Eligible Employees,${metadata.etiEmployeeCount}`,
      `Claiming ETI,${metadata.claimingEmployees}`,
      `Not Claiming ETI,${metadata.notClaimingEmployees}`,
      `Total Monthly ETI Amount,R${metadata.totalETIAmount.toFixed(2)}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Headers
    const headers = [
      "Employee Number",
      "First Name",
      "Last Name",
      "ID Number",
      "Employment Start Date",
      "ETI Status",
      "ETI Effective From",
      "Monthly Remuneration",
      "Employment Duration (Months)",
      "Age at Employment",
      "ETI Amount",
      "ETI Phase",
      "Eligibility Status",
      "Total Months Claimed",
      "Year to Date ETI"
    ].join(",");

    // Employee rows
    const employeeRows = etiEmployees.map(employee =>
      this.formatCSVRow([
        employee.employeeNumber || '',
        employee.firstName || '',
        employee.lastName || '',
        employee.idNumber || '',
        employee.employmentStartDate ? new Date(employee.employmentStartDate).toLocaleDateString() : '',
        employee.etiStatus || '',
        employee.etiEffectiveFrom ? new Date(employee.etiEffectiveFrom).toLocaleDateString() : '',
        employee.monthlyRemuneration || 0,
        employee.employmentDuration || 0,
        employee.ageAtEmployment || '',
        employee.etiAmount || 0,
        employee.etiPhase || '',
        employee.eligibilityStatus || '',
        employee.totalMonthsClaimed || 0,
        employee.yearToDateETI || 0
      ])
    );

    // Combine all parts
    const csvContent = [
      metadataRows,
      headers,
      ...employeeRows
    ].join("\n");

    return Buffer.from(csvContent);
  }

  // ===== LEAVE REPORTS =====

  // Generate Leave Days Report
  static async generateLeaveDaysReport(companyId, format, settings, selectedEmployees = null, selectedLeaveTypes = null) {
    console.log("\n=== Generating Leave Days Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees, selectedLeaveTypes });

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build employee query
      let employeeQuery = { company: companyId, status: { $in: ["Active", "Inactive", "Serving Notice"] } };
      if (selectedEmployees && selectedEmployees.length > 0) {
        employeeQuery._id = { $in: selectedEmployees };
      }

      // Get employees
      const employees = await Employee.find(employeeQuery)
        .sort({ lastName: 1, firstName: 1 });

      console.log(`Found ${employees.length} employees for leave days report`);

      // Build leave balance query
      const currentYear = new Date().getFullYear();
      let leaveBalanceQuery = {
        company: companyId,
        employee: { $in: employees.map(emp => emp._id) },
        year: currentYear
      };

      // Filter by leave types if specified
      if (selectedLeaveTypes && selectedLeaveTypes.length > 0) {
        leaveBalanceQuery.leaveType = { $in: selectedLeaveTypes };
      }

      // Get leave balances with population
      const leaveBalances = await LeaveBalance.find(leaveBalanceQuery)
        .populate("employee", "companyEmployeeNumber firstName lastName jobTitle department costCentre")
        .populate("leaveType", "name category daysPerYear")
        .sort({ "employee.lastName": 1, "leaveType.name": 1 });

      console.log(`Found ${leaveBalances.length} leave balance records`);

      // Get all leave types for the company for reference
      const allLeaveTypes = await LeaveType.find({ company: companyId, active: true })
        .sort({ name: 1 });

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Leave Days Report",
        generatedDate: new Date().toISOString(),
        reportYear: currentYear,
        employeeCount: employees.length,
        leaveBalanceCount: leaveBalances.length,
        leaveTypesCount: allLeaveTypes.length
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generateLeaveDaysReportExcel(employees, leaveBalances, allLeaveTypes, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generateLeaveDaysReportPDF(employees, leaveBalances, allLeaveTypes, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generateLeaveDaysReportCSV(employees, leaveBalances, allLeaveTypes, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating leave days report:", error);
      throw error;
    }
  }

  // Generate Excel format for Leave Days Report
  static async generateLeaveDaysReportExcel(employees, leaveBalances, allLeaveTypes, metadata, settings) {
    console.log("\n=== Generating Leave Days Report Excel ===");
    console.log(`Processing ${employees.length} employees and ${leaveBalances.length} leave balance records`);

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Leave Days Report");

    // Add company header
    worksheet.mergeCells("A1:H1");
    worksheet.getCell("A1").value = metadata.companyName;
    worksheet.getCell("A1").font = { name: 'Inter', size: 16, bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add addresses
    if (metadata.physicalAddress) {
      worksheet.mergeCells("A2:H2");
      worksheet.getCell("A2").value = `Physical Address: ${metadata.physicalAddress}`;
      worksheet.getCell("A2").font = { name: 'Inter', size: 10 };
    }
    if (metadata.postalAddress) {
      worksheet.mergeCells("A3:H3");
      worksheet.getCell("A3").value = `Postal Address: ${metadata.postalAddress}`;
      worksheet.getCell("A3").font = { name: 'Inter', size: 10 };
    }

    // Add report info
    worksheet.mergeCells("A4:H4");
    worksheet.getCell("A4").value = metadata.reportName;
    worksheet.getCell("A4").font = { name: 'Inter', size: 14, bold: true };

    worksheet.mergeCells("A5:H5");
    worksheet.getCell("A5").value = `Generated on: ${new Date().toLocaleDateString()} | Report Year: ${metadata.reportYear}`;
    worksheet.getCell("A5").font = { name: 'Inter', size: 10 };

    // Add summary stats
    worksheet.mergeCells("A6:H6");
    worksheet.getCell("A6").value = `Employees: ${metadata.employeeCount} | Leave Types: ${metadata.leaveTypesCount} | Balance Records: ${metadata.leaveBalanceCount}`;
    worksheet.getCell("A6").font = { name: 'Inter', size: 10 };

    // Add header row at row 8
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Category",
      "Total Allocation",
      "Used",
      "Pending",
      "Remaining"
    ];

    const headerRow = worksheet.getRow(8);
    headers.forEach((header, index) => {
      headerRow.getCell(index + 1).value = header;
    });

    // Set column widths
    const columnWidths = [15, 25, 20, 20, 15, 15, 10, 10, 10];
    columnWidths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });

    // Add leave balance data
    if (leaveBalances.length === 0) {
      const noDataRow = worksheet.addRow([
        "No leave balance data found for the selected criteria",
        "", "", "", "", "", "", "", ""
      ]);
      noDataRow.font = { name: 'Inter', size: 10, italic: true };
      noDataRow.getCell(1).alignment = { horizontal: 'center' };
      worksheet.mergeCells(`A9:I9`);
    } else {
      leaveBalances.forEach(balance => {
        const employee = balance.employee;
        const leaveType = balance.leaveType;

        const row = worksheet.addRow([
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          leaveType?.category || "N/A",
          balance.totalAllocation || 0,
          balance.used || 0,
          balance.pending || 0,
          balance.remaining || 0
        ]);

        row.font = { name: 'Inter', size: 9 };

        // Color code remaining days
        const remainingCell = row.getCell(9);
        if (balance.remaining <= 0) {
          remainingCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } }; // Light red
        } else if (balance.remaining <= 5) {
          remainingCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2E6' } }; // Light orange
        }
      });
    }

    // Style the header row
    const styledHeaderRow = worksheet.getRow(8);
    styledHeaderRow.font = { name: 'Inter', size: 10, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Add borders and background to header row
    for (let i = 1; i <= headers.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF6366F1' } // PandaPayroll primary color
      };
      cell.font = { name: 'Inter', size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
    }

    return await workbook.xlsx.writeBuffer();
  }

  // Generate CSV format for Leave Days Report
  static async generateLeaveDaysReportCSV(employees, leaveBalances, allLeaveTypes, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Report Year,${metadata.reportYear}`,
      `Total Employees,${metadata.employeeCount}`,
      `Leave Types,${metadata.leaveTypesCount}`,
      `Balance Records,${metadata.leaveBalanceCount}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Define headers
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Category",
      "Total Allocation",
      "Used",
      "Pending",
      "Remaining"
    ].join(",");

    // Convert leave balance data to CSV rows
    const leaveBalanceRows = leaveBalances.length === 0 ?
      ["No leave balance data found for the selected criteria"] :
      leaveBalances.map(balance => {
        const employee = balance.employee;
        const leaveType = balance.leaveType;

        return [
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          leaveType?.category || "N/A",
          balance.totalAllocation || 0,
          balance.used || 0,
          balance.pending || 0,
          balance.remaining || 0
        ].map(field => {
          // Escape commas and quotes in fields
          if (field && field.toString().includes(",")) {
            return `"${field.toString().replace(/"/g, '""')}"`;
          }
          return field;
        }).join(",");
      });

    // Combine all parts
    const csvContent = [metadataRows, headers, ...leaveBalanceRows].join("\n");
    return Buffer.from(csvContent);
  }

  // Generate PDF format for Leave Days Report
  static async generateLeaveDaysReportPDF(employees, leaveBalances, allLeaveTypes, metadata, settings) {
    const doc = new PDFDocument({ margin: 50 });
    const buffers = [];

    doc.on("data", (buffer) => buffers.push(buffer));

    // Add header
    doc.fontSize(16).text(metadata.companyName, { align: "center" });
    doc.moveDown(0.5);

    if (metadata.physicalAddress) {
      doc.fontSize(10).text(`Physical Address: ${metadata.physicalAddress}`, { align: "center" });
    }
    if (metadata.postalAddress) {
      doc.fontSize(10).text(`Postal Address: ${metadata.postalAddress}`, { align: "center" });
    }

    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: "center" });
    doc.moveDown(0.5);

    doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()} | Report Year: ${metadata.reportYear}`, { align: "center" });
    doc.text(`Employees: ${metadata.employeeCount} | Leave Types: ${metadata.leaveTypesCount} | Balance Records: ${metadata.leaveBalanceCount}`, { align: "center" });
    doc.moveDown();

    // Add leave balance data
    if (leaveBalances.length === 0) {
      doc.fontSize(12).text("No leave balance data found for the selected criteria", { align: "center" });
    } else {
      // Table headers
      const startY = doc.y;
      doc.fontSize(9);

      leaveBalances.forEach((balance, index) => {
        if (doc.y > 700) { // Start new page if needed
          doc.addPage();
          doc.y = 50;
        }

        const employee = balance.employee;
        const leaveType = balance.leaveType;

        const text = [
          `${employee?.companyEmployeeNumber || "N/A"} - ${employee?.firstName || ""} ${employee?.lastName || ""}`.trim(),
          `Department: ${employee?.department || employee?.costCentre || "N/A"}`,
          `Leave Type: ${leaveType?.name || "N/A"} (${leaveType?.category || "N/A"})`,
          `Allocation: ${balance.totalAllocation || 0} | Used: ${balance.used || 0} | Pending: ${balance.pending || 0} | Remaining: ${balance.remaining || 0}`,
          "---"
        ].join("\n");

        doc.text(text);
        doc.moveDown(0.3);
      });
    }

    doc.end();

    return new Promise((resolve) => {
      doc.on("end", () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  // Generate Leave Expiry Report
  static async generateLeaveExpiryReport(companyId, format, settings, selectedEmployees = null, dateRange = null) {
    console.log("\n=== Generating Leave Expiry Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees, dateRange });

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build employee query
      let employeeQuery = { company: companyId, status: { $in: ["Active", "Inactive", "Serving Notice"] } };
      if (selectedEmployees && selectedEmployees.length > 0) {
        employeeQuery._id = { $in: selectedEmployees };
      }

      // Get employees
      const employees = await Employee.find(employeeQuery)
        .sort({ lastName: 1, firstName: 1 });

      console.log(`Found ${employees.length} employees for leave expiry report`);

      // Calculate date range for expiry check
      let expiryStartDate, expiryEndDate;
      if (dateRange && dateRange.startDate && dateRange.endDate) {
        expiryStartDate = new Date(dateRange.startDate);
        expiryEndDate = new Date(dateRange.endDate);
      } else {
        // Default to next 6 months
        expiryStartDate = new Date();
        expiryEndDate = new Date();
        expiryEndDate.setMonth(expiryEndDate.getMonth() + 6);
      }

      console.log(`Checking for leave expiring between ${expiryStartDate.toLocaleDateString()} and ${expiryEndDate.toLocaleDateString()}`);

      // Get leave types with carry over limits
      const leaveTypes = await LeaveType.find({
        company: companyId,
        active: true,
        carryOverLimit: { $gte: 0 } // Only include leave types that have carry over rules
      }).sort({ name: 1 });

      // Get current year leave balances
      const currentYear = new Date().getFullYear();
      const leaveBalances = await LeaveBalance.find({
        company: companyId,
        employee: { $in: employees.map(emp => emp._id) },
        year: currentYear,
        remaining: { $gt: 0 } // Only balances with remaining days
      })
      .populate("employee", "companyEmployeeNumber firstName lastName jobTitle department costCentre")
      .populate("leaveType", "name category carryOverLimit daysPerYear")
      .sort({ "employee.lastName": 1, "leaveType.name": 1 });

      // Calculate expiring leave
      const expiringLeave = leaveBalances.map(balance => {
        const leaveType = balance.leaveType;
        const carryOverLimit = leaveType.carryOverLimit || 0;
        const expiringAmount = Math.max(0, balance.remaining - carryOverLimit);

        // Calculate expiry date (typically end of year)
        const expiryDate = new Date(currentYear, 11, 31); // December 31st of current year

        return {
          ...balance.toObject(),
          expiringAmount,
          expiryDate,
          isExpiring: expiringAmount > 0 && expiryDate >= expiryStartDate && expiryDate <= expiryEndDate
        };
      }).filter(item => item.isExpiring);

      console.log(`Found ${expiringLeave.length} leave balances with expiring days`);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Leave Expiry Report",
        generatedDate: new Date().toISOString(),
        reportYear: currentYear,
        expiryPeriod: {
          start: expiryStartDate.toLocaleDateString(),
          end: expiryEndDate.toLocaleDateString()
        },
        employeeCount: employees.length,
        expiringRecordsCount: expiringLeave.length,
        totalExpiringDays: expiringLeave.reduce((sum, item) => sum + item.expiringAmount, 0)
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generateLeaveExpiryReportExcel(employees, expiringLeave, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generateLeaveExpiryReportPDF(employees, expiringLeave, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generateLeaveExpiryReportCSV(employees, expiringLeave, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating leave expiry report:", error);
      throw error;
    }
  }

  // Generate Excel format for Leave Expiry Report
  static async generateLeaveExpiryReportExcel(employees, expiringLeave, metadata, settings) {
    console.log("\n=== Generating Leave Expiry Report Excel ===");
    console.log(`Processing ${employees.length} employees and ${expiringLeave.length} expiring leave records`);

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Leave Expiry Report");

    // Add company header
    worksheet.mergeCells("A1:I1");
    worksheet.getCell("A1").value = metadata.companyName;
    worksheet.getCell("A1").font = { name: 'Inter', size: 16, bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add addresses
    if (metadata.physicalAddress) {
      worksheet.mergeCells("A2:I2");
      worksheet.getCell("A2").value = `Physical Address: ${metadata.physicalAddress}`;
      worksheet.getCell("A2").font = { name: 'Inter', size: 10 };
    }
    if (metadata.postalAddress) {
      worksheet.mergeCells("A3:I3");
      worksheet.getCell("A3").value = `Postal Address: ${metadata.postalAddress}`;
      worksheet.getCell("A3").font = { name: 'Inter', size: 10 };
    }

    // Add report info
    worksheet.mergeCells("A4:I4");
    worksheet.getCell("A4").value = metadata.reportName;
    worksheet.getCell("A4").font = { name: 'Inter', size: 14, bold: true };

    worksheet.mergeCells("A5:I5");
    worksheet.getCell("A5").value = `Generated on: ${new Date().toLocaleDateString()} | Report Year: ${metadata.reportYear}`;
    worksheet.getCell("A5").font = { name: 'Inter', size: 10 };

    worksheet.mergeCells("A6:I6");
    worksheet.getCell("A6").value = `Expiry Period: ${metadata.expiryPeriod.start} to ${metadata.expiryPeriod.end}`;
    worksheet.getCell("A6").font = { name: 'Inter', size: 10 };

    // Add summary stats
    worksheet.mergeCells("A7:I7");
    worksheet.getCell("A7").value = `Employees: ${metadata.employeeCount} | Expiring Records: ${metadata.expiringRecordsCount} | Total Expiring Days: ${metadata.totalExpiringDays}`;
    worksheet.getCell("A7").font = { name: 'Inter', size: 10 };

    // Add header row at row 9
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Category",
      "Total Remaining",
      "Carry Over Limit",
      "Expiring Days",
      "Expiry Date"
    ];

    const headerRow = worksheet.getRow(9);
    headers.forEach((header, index) => {
      headerRow.getCell(index + 1).value = header;
    });

    // Set column widths
    const columnWidths = [15, 25, 20, 20, 15, 15, 15, 15, 15];
    columnWidths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });

    // Add expiring leave data
    if (expiringLeave.length === 0) {
      const noDataRow = worksheet.addRow([
        "No expiring leave found for the selected criteria",
        "", "", "", "", "", "", "", ""
      ]);
      noDataRow.font = { name: 'Inter', size: 10, italic: true };
      noDataRow.getCell(1).alignment = { horizontal: 'center' };
      worksheet.mergeCells(`A10:I10`);
    } else {
      expiringLeave.forEach(item => {
        const employee = item.employee;
        const leaveType = item.leaveType;

        const row = worksheet.addRow([
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          leaveType?.category || "N/A",
          item.remaining || 0,
          leaveType?.carryOverLimit || 0,
          item.expiringAmount || 0,
          item.expiryDate ? new Date(item.expiryDate).toLocaleDateString() : "N/A"
        ]);

        row.font = { name: 'Inter', size: 9 };

        // Color code expiring days
        const expiringCell = row.getCell(8);
        if (item.expiringAmount > 10) {
          expiringCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } }; // Light red
        } else if (item.expiringAmount > 5) {
          expiringCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2E6' } }; // Light orange
        } else if (item.expiringAmount > 0) {
          expiringCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF9E6' } }; // Light yellow
        }
      });
    }

    // Style the header row
    const styledHeaderRow = worksheet.getRow(9);
    styledHeaderRow.font = { name: 'Inter', size: 10, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Add borders and background to header row
    for (let i = 1; i <= headers.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF6366F1' } // PandaPayroll primary color
      };
      cell.font = { name: 'Inter', size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
    }

    return await workbook.xlsx.writeBuffer();
  }

  // Generate CSV format for Leave Expiry Report
  static async generateLeaveExpiryReportCSV(employees, expiringLeave, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Report Year,${metadata.reportYear}`,
      `Expiry Period,${metadata.expiryPeriod.start} to ${metadata.expiryPeriod.end}`,
      `Total Employees,${metadata.employeeCount}`,
      `Expiring Records,${metadata.expiringRecordsCount}`,
      `Total Expiring Days,${metadata.totalExpiringDays}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Define headers
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Category",
      "Total Remaining",
      "Carry Over Limit",
      "Expiring Days",
      "Expiry Date"
    ].join(",");

    // Convert expiring leave data to CSV rows
    const expiringLeaveRows = expiringLeave.length === 0 ?
      ["No expiring leave found for the selected criteria"] :
      expiringLeave.map(item => {
        const employee = item.employee;
        const leaveType = item.leaveType;

        return [
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          leaveType?.category || "N/A",
          item.remaining || 0,
          leaveType?.carryOverLimit || 0,
          item.expiringAmount || 0,
          item.expiryDate ? new Date(item.expiryDate).toLocaleDateString() : "N/A"
        ].map(field => {
          // Escape commas and quotes in fields
          if (field && field.toString().includes(",")) {
            return `"${field.toString().replace(/"/g, '""')}"`;
          }
          return field;
        }).join(",");
      });

    // Combine all parts
    const csvContent = [metadataRows, headers, ...expiringLeaveRows].join("\n");
    return Buffer.from(csvContent);
  }

  // Generate PDF format for Leave Expiry Report
  static async generateLeaveExpiryReportPDF(employees, expiringLeave, metadata, settings) {
    const doc = new PDFDocument({ margin: 50 });
    const buffers = [];

    doc.on("data", (buffer) => buffers.push(buffer));

    // Add header
    doc.fontSize(16).text(metadata.companyName, { align: "center" });
    doc.moveDown(0.5);

    if (metadata.physicalAddress) {
      doc.fontSize(10).text(`Physical Address: ${metadata.physicalAddress}`, { align: "center" });
    }
    if (metadata.postalAddress) {
      doc.fontSize(10).text(`Postal Address: ${metadata.postalAddress}`, { align: "center" });
    }

    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: "center" });
    doc.moveDown(0.5);

    doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()} | Report Year: ${metadata.reportYear}`, { align: "center" });
    doc.text(`Expiry Period: ${metadata.expiryPeriod.start} to ${metadata.expiryPeriod.end}`, { align: "center" });
    doc.text(`Employees: ${metadata.employeeCount} | Expiring Records: ${metadata.expiringRecordsCount} | Total Expiring Days: ${metadata.totalExpiringDays}`, { align: "center" });
    doc.moveDown();

    // Add expiring leave data
    if (expiringLeave.length === 0) {
      doc.fontSize(12).text("No expiring leave found for the selected criteria", { align: "center" });
    } else {
      doc.fontSize(9);

      expiringLeave.forEach((item, index) => {
        if (doc.y > 700) { // Start new page if needed
          doc.addPage();
          doc.y = 50;
        }

        const employee = item.employee;
        const leaveType = item.leaveType;

        const text = [
          `${employee?.companyEmployeeNumber || "N/A"} - ${employee?.firstName || ""} ${employee?.lastName || ""}`.trim(),
          `Department: ${employee?.department || employee?.costCentre || "N/A"}`,
          `Leave Type: ${leaveType?.name || "N/A"} (${leaveType?.category || "N/A"})`,
          `Remaining: ${item.remaining || 0} | Carry Over Limit: ${leaveType?.carryOverLimit || 0} | Expiring: ${item.expiringAmount || 0}`,
          `Expiry Date: ${item.expiryDate ? new Date(item.expiryDate).toLocaleDateString() : "N/A"}`,
          "---"
        ].join("\n");

        doc.text(text);
        doc.moveDown(0.3);
      });
    }

    doc.end();

    return new Promise((resolve) => {
      doc.on("end", () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  // Generate Leave Report (Leave Request History)
  static async generateLeaveReport(companyId, format, settings, selectedEmployees = null, dateRange = null) {
    console.log("\n=== Generating Leave Report ===");
    console.log("Parameters:", { companyId, format, selectedEmployees, dateRange });

    try {
      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build employee query
      let employeeQuery = { company: companyId, status: { $in: ["Active", "Inactive", "Serving Notice"] } };
      if (selectedEmployees && selectedEmployees.length > 0) {
        employeeQuery._id = { $in: selectedEmployees };
      }

      // Get employees
      const employees = await Employee.find(employeeQuery)
        .sort({ lastName: 1, firstName: 1 });

      console.log(`Found ${employees.length} employees for leave report`);

      // Build leave request query
      let leaveRequestQuery = {
        company: companyId,
        employee: { $in: employees.map(emp => emp._id) }
      };

      // Apply date range filter if provided
      if (dateRange && dateRange.startDate && dateRange.endDate) {
        leaveRequestQuery.startDate = {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate)
        };
        console.log(`Filtering leave requests from ${dateRange.startDate} to ${dateRange.endDate}`);
      } else {
        // Default to last 12 months if no date range specified
        const twelveMonthsAgo = new Date();
        twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
        leaveRequestQuery.startDate = { $gte: twelveMonthsAgo };
        console.log(`No date range specified, using last 12 months from ${twelveMonthsAgo.toISOString()}`);
      }

      // Get leave requests with population
      const leaveRequests = await LeaveRequest.find(leaveRequestQuery)
        .populate("employee", "companyEmployeeNumber firstName lastName jobTitle department costCentre")
        .populate("leaveType", "name category")
        .sort({ startDate: -1, "employee.lastName": 1 });

      console.log(`Found ${leaveRequests.length} leave requests`);

      // Calculate summary statistics
      const approvedRequests = leaveRequests.filter(req => req.status === "approved");
      const pendingRequests = leaveRequests.filter(req => req.status === "pending");
      const rejectedRequests = leaveRequests.filter(req => req.status === "rejected");
      const totalDaysRequested = leaveRequests.reduce((sum, req) => sum + (req.numberOfDays || 0), 0);
      const totalDaysApproved = approvedRequests.reduce((sum, req) => sum + (req.numberOfDays || 0), 0);

      // Prepare report metadata
      const reportMetadata = {
        companyName: employerDetails?.tradingName || company.name,
        physicalAddress: employerDetails ? this.formatAddress(employerDetails.physicalAddress) : "",
        postalAddress: employerDetails ? this.formatAddress(employerDetails.postalAddress) : "",
        reportName: "Leave Report",
        generatedDate: new Date().toISOString(),
        dateRange: dateRange ? {
          start: new Date(dateRange.startDate).toLocaleDateString(),
          end: new Date(dateRange.endDate).toLocaleDateString()
        } : {
          start: leaveRequestQuery.startDate.$gte.toLocaleDateString(),
          end: new Date().toLocaleDateString()
        },
        employeeCount: employees.length,
        totalRequests: leaveRequests.length,
        approvedRequests: approvedRequests.length,
        pendingRequests: pendingRequests.length,
        rejectedRequests: rejectedRequests.length,
        totalDaysRequested,
        totalDaysApproved
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generateLeaveReportExcel(employees, leaveRequests, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generateLeaveReportPDF(employees, leaveRequests, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generateLeaveReportCSV(employees, leaveRequests, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating leave report:", error);
      throw error;
    }
  }

  // Generate Excel format for Leave Report
  static async generateLeaveReportExcel(employees, leaveRequests, metadata, settings) {
    console.log("\n=== Generating Leave Report Excel ===");
    console.log(`Processing ${employees.length} employees and ${leaveRequests.length} leave requests`);

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Leave Report");

    // Add company header
    worksheet.mergeCells("A1:K1");
    worksheet.getCell("A1").value = metadata.companyName;
    worksheet.getCell("A1").font = { name: 'Inter', size: 16, bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add addresses
    if (metadata.physicalAddress) {
      worksheet.mergeCells("A2:K2");
      worksheet.getCell("A2").value = `Physical Address: ${metadata.physicalAddress}`;
      worksheet.getCell("A2").font = { name: 'Inter', size: 10 };
    }
    if (metadata.postalAddress) {
      worksheet.mergeCells("A3:K3");
      worksheet.getCell("A3").value = `Postal Address: ${metadata.postalAddress}`;
      worksheet.getCell("A3").font = { name: 'Inter', size: 10 };
    }

    // Add report info
    worksheet.mergeCells("A4:K4");
    worksheet.getCell("A4").value = metadata.reportName;
    worksheet.getCell("A4").font = { name: 'Inter', size: 14, bold: true };

    worksheet.mergeCells("A5:K5");
    worksheet.getCell("A5").value = `Generated on: ${new Date().toLocaleDateString()} | Period: ${metadata.dateRange.start} to ${metadata.dateRange.end}`;
    worksheet.getCell("A5").font = { name: 'Inter', size: 10 };

    // Add summary stats
    worksheet.mergeCells("A6:K6");
    worksheet.getCell("A6").value = `Total Requests: ${metadata.totalRequests} | Approved: ${metadata.approvedRequests} | Pending: ${metadata.pendingRequests} | Rejected: ${metadata.rejectedRequests}`;
    worksheet.getCell("A6").font = { name: 'Inter', size: 10 };

    worksheet.mergeCells("A7:K7");
    worksheet.getCell("A7").value = `Total Days Requested: ${metadata.totalDaysRequested} | Total Days Approved: ${metadata.totalDaysApproved}`;
    worksheet.getCell("A7").font = { name: 'Inter', size: 10 };

    // Add header row at row 9
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Start Date",
      "End Date",
      "Days",
      "Status",
      "Reason",
      "Approved By",
      "Approval Date"
    ];

    const headerRow = worksheet.getRow(9);
    headers.forEach((header, index) => {
      headerRow.getCell(index + 1).value = header;
    });

    // Set column widths
    const columnWidths = [15, 25, 20, 20, 12, 12, 8, 12, 30, 20, 12];
    columnWidths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });

    // Add leave request data
    if (leaveRequests.length === 0) {
      const noDataRow = worksheet.addRow([
        "No leave requests found for the selected criteria",
        "", "", "", "", "", "", "", "", "", ""
      ]);
      noDataRow.font = { name: 'Inter', size: 10, italic: true };
      noDataRow.getCell(1).alignment = { horizontal: 'center' };
      worksheet.mergeCells(`A10:K10`);
    } else {
      leaveRequests.forEach(request => {
        const employee = request.employee;
        const leaveType = request.leaveType;

        const row = worksheet.addRow([
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          request.startDate ? new Date(request.startDate).toLocaleDateString() : "N/A",
          request.endDate ? new Date(request.endDate).toLocaleDateString() : "N/A",
          request.numberOfDays || 0,
          request.status || "N/A",
          request.reason || "N/A",
          request.approvedBy ? "Approved" : "N/A", // Simplified since we can't populate User
          request.approvalDate ? new Date(request.approvalDate).toLocaleDateString() : "N/A"
        ]);

        row.font = { name: 'Inter', size: 9 };

        // Color code status
        const statusCell = row.getCell(8);
        switch (request.status) {
          case "approved":
            statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6F7E6' } }; // Light green
            break;
          case "pending":
            statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2E6' } }; // Light orange
            break;
          case "rejected":
            statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } }; // Light red
            break;
          case "cancelled":
            statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F0F0' } }; // Light gray
            break;
        }
      });
    }

    // Style the header row
    const styledHeaderRow = worksheet.getRow(9);
    styledHeaderRow.font = { name: 'Inter', size: 10, bold: true };
    styledHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    styledHeaderRow.height = 30;

    // Add borders and background to header row
    for (let i = 1; i <= headers.length; i++) {
      const cell = styledHeaderRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF6366F1' } // PandaPayroll primary color
      };
      cell.font = { name: 'Inter', size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
    }

    return await workbook.xlsx.writeBuffer();
  }

  // Generate CSV format for Leave Report
  static async generateLeaveReportCSV(employees, leaveRequests, metadata, settings) {
    // Start with metadata
    const metadataRows = [
      `Company Name,${metadata.companyName}`,
      metadata.physicalAddress ? `Physical Address,${metadata.physicalAddress}` : "",
      metadata.postalAddress ? `Postal Address,${metadata.postalAddress}` : "",
      `Report Name,${metadata.reportName}`,
      `Generated Date,${new Date().toLocaleDateString()}`,
      `Period,${metadata.dateRange.start} to ${metadata.dateRange.end}`,
      `Total Requests,${metadata.totalRequests}`,
      `Approved Requests,${metadata.approvedRequests}`,
      `Pending Requests,${metadata.pendingRequests}`,
      `Rejected Requests,${metadata.rejectedRequests}`,
      `Total Days Requested,${metadata.totalDaysRequested}`,
      `Total Days Approved,${metadata.totalDaysApproved}`,
      "", // Empty line for spacing
    ].filter(Boolean).join("\n");

    // Define headers
    const headers = [
      "Employee #",
      "Employee Name",
      "Department",
      "Leave Type",
      "Start Date",
      "End Date",
      "Days",
      "Status",
      "Reason",
      "Approved By",
      "Approval Date"
    ].join(",");

    // Convert leave request data to CSV rows
    const leaveRequestRows = leaveRequests.length === 0 ?
      ["No leave requests found for the selected criteria"] :
      leaveRequests.map(request => {
        const employee = request.employee;
        const leaveType = request.leaveType;

        return [
          employee?.companyEmployeeNumber || "N/A",
          `${employee?.firstName || ""} ${employee?.lastName || ""}`.trim() || "N/A",
          employee?.department || employee?.costCentre || "N/A",
          leaveType?.name || "N/A",
          request.startDate ? new Date(request.startDate).toLocaleDateString() : "N/A",
          request.endDate ? new Date(request.endDate).toLocaleDateString() : "N/A",
          request.numberOfDays || 0,
          request.status || "N/A",
          request.reason || "N/A",
          request.approvedBy ? "Approved" : "N/A", // Simplified since we can't populate User
          request.approvalDate ? new Date(request.approvalDate).toLocaleDateString() : "N/A"
        ].map(field => {
          // Escape commas and quotes in fields
          if (field && field.toString().includes(",")) {
            return `"${field.toString().replace(/"/g, '""')}"`;
          }
          return field;
        }).join(",");
      });

    // Combine all parts
    const csvContent = [metadataRows, headers, ...leaveRequestRows].join("\n");
    return Buffer.from(csvContent);
  }

  // Generate PDF format for Leave Report
  static async generateLeaveReportPDF(employees, leaveRequests, metadata, settings) {
    const doc = new PDFDocument({ margin: 50 });
    const buffers = [];

    doc.on("data", (buffer) => buffers.push(buffer));

    // Add header
    doc.fontSize(16).text(metadata.companyName, { align: "center" });
    doc.moveDown(0.5);

    if (metadata.physicalAddress) {
      doc.fontSize(10).text(`Physical Address: ${metadata.physicalAddress}`, { align: "center" });
    }
    if (metadata.postalAddress) {
      doc.fontSize(10).text(`Postal Address: ${metadata.postalAddress}`, { align: "center" });
    }

    doc.moveDown();
    doc.fontSize(14).text(metadata.reportName, { align: "center" });
    doc.moveDown(0.5);

    doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()}`, { align: "center" });
    doc.text(`Period: ${metadata.dateRange.start} to ${metadata.dateRange.end}`, { align: "center" });
    doc.text(`Total Requests: ${metadata.totalRequests} | Approved: ${metadata.approvedRequests} | Pending: ${metadata.pendingRequests} | Rejected: ${metadata.rejectedRequests}`, { align: "center" });
    doc.text(`Total Days Requested: ${metadata.totalDaysRequested} | Total Days Approved: ${metadata.totalDaysApproved}`, { align: "center" });
    doc.moveDown();

    // Add leave request data
    if (leaveRequests.length === 0) {
      doc.fontSize(12).text("No leave requests found for the selected criteria", { align: "center" });
    } else {
      doc.fontSize(9);

      leaveRequests.forEach((request, index) => {
        if (doc.y > 700) { // Start new page if needed
          doc.addPage();
          doc.y = 50;
        }

        const employee = request.employee;
        const leaveType = request.leaveType;

        const text = [
          `${employee?.companyEmployeeNumber || "N/A"} - ${employee?.firstName || ""} ${employee?.lastName || ""}`.trim(),
          `Department: ${employee?.department || employee?.costCentre || "N/A"}`,
          `Leave Type: ${leaveType?.name || "N/A"}`,
          `Period: ${request.startDate ? new Date(request.startDate).toLocaleDateString() : "N/A"} to ${request.endDate ? new Date(request.endDate).toLocaleDateString() : "N/A"} (${request.numberOfDays || 0} days)`,
          `Status: ${request.status || "N/A"}`,
          `Reason: ${request.reason || "N/A"}`,
          request.approvedBy ? `Approved on ${request.approvalDate ? new Date(request.approvalDate).toLocaleDateString() : "N/A"}` : "",
          "---"
        ].filter(Boolean).join("\n");

        doc.text(text);
        doc.moveDown(0.3);
      });
    }

    doc.end();

    return new Promise((resolve) => {
      doc.on("end", () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  // Generate Payslips Report
  static async generatePayslips(companyId, format, settings, dateRange = null, selectedEmployees = null) {
    try {
      console.log("\n=== Generating Payslips Report ===");
      console.log("Parameters:", { companyId, format, dateRange, selectedEmployees });

      // Get company and employer details
      const company = await Company.findById(companyId);
      const employerDetails = await EmployerDetails.findOne({ company: companyId });

      if (!company) throw new Error("Company not found");

      // Build base query
      let matchQuery = {
        company: companyId,
        isFinalized: true
      };

      // Add date range filter
      if (dateRange && dateRange.startDate && dateRange.endDate) {
        matchQuery.endDate = {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate)
        };
      }

      // Add employee filter if specified
      if (selectedEmployees && selectedEmployees.length > 0) {
        matchQuery.employee = { $in: selectedEmployees.map(id => new mongoose.Types.ObjectId(id)) };
      }

      console.log("Match query:", matchQuery);

      // MongoDB aggregation to get payslip data with employee and company details
      const payslipData = await PayrollPeriod.aggregate([
        {
          $match: matchQuery
        },
        {
          $lookup: {
            from: "employees",
            localField: "employee",
            foreignField: "_id",
            as: "employeeData"
          }
        },
        {
          $unwind: "$employeeData"
        },
        {
          $match: {
            // Filter out employees with inactive status if needed
            "employeeData.status": { $in: ["Active", "Inactive", "Serving Notice"] }
          }
        },
        {
          $lookup: {
            from: "companies",
            localField: "company",
            foreignField: "_id",
            as: "companyData"
          }
        },
        {
          $unwind: "$companyData"
        },
        {
          $lookup: {
            from: "payfrequencies",
            localField: "employeeData.payFrequency",
            foreignField: "_id",
            as: "payFrequencyData"
          }
        },
        {
          $unwind: {
            path: "$payFrequencyData",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            // Payroll Period Data
            startDate: 1,
            endDate: 1,
            frequency: 1,
            basicSalary: 1,
            grossPay: 1,
            totalDeductions: 1,
            netPay: 1,
            PAYE: 1,
            UIF: 1,
            SDL: 1,
            status: 1,
            isFinalized: 1,

            // Employee Data
            "employee.id": "$employeeData._id",
            "employee.companyEmployeeNumber": "$employeeData.companyEmployeeNumber",
            "employee.firstName": "$employeeData.firstName",
            "employee.lastName": "$employeeData.lastName",
            "employee.idNumber": "$employeeData.idNumber",
            "employee.email": "$employeeData.email",
            "employee.jobTitle": "$employeeData.jobTitle",
            "employee.department": "$employeeData.department",
            "employee.costCentre": "$employeeData.costCentre",
            "employee.status": "$employeeData.status",
            "employee.doa": "$employeeData.doa",
            "employee.paymentMethod": "$employeeData.paymentMethod",
            "employee.bankDetails": "$employeeData.bankDetails",

            // Company Data
            "company.name": "$companyData.name",
            "company.tradingName": "$companyData.tradingName",
            "company.companyCode": "$companyData.companyCode",

            // Pay Frequency Data
            "payFrequency.frequency": "$payFrequencyData.frequency",
            "payFrequency.description": "$payFrequencyData.description"
          }
        },
        {
          $sort: {
            endDate: -1,
            "employee.lastName": 1,
            "employee.firstName": 1
          }
        }
      ]);

      console.log(`Found ${payslipData.length} payslip records`);

      // Prepare metadata
      const reportMetadata = {
        companyName: company.tradingName || company.name,
        companyCode: company.companyCode,
        physicalAddress: employerDetails?.physicalAddress,
        postalAddress: employerDetails?.postalAddress,
        generatedAt: new Date(),
        dateRange: dateRange,
        totalRecords: payslipData.length,
        totalEmployees: [...new Set(payslipData.map(p => p.employee.id.toString()))].length
      };

      // Generate report based on format
      let reportData;
      switch (format) {
        case "excel":
          reportData = await this.generatePayslipsExcel(payslipData, reportMetadata, settings);
          break;
        case "pdf":
          reportData = await this.generatePayslipsPDF(payslipData, reportMetadata, settings);
          break;
        case "csv":
          reportData = await this.generatePayslipsCSV(payslipData, reportMetadata, settings);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      return reportData;
    } catch (error) {
      console.error("Error generating payslips report:", error);
      throw error;
    }
  }

  // Generate Excel format for Payslips Report
  static async generatePayslipsExcel(payslipData, metadata, settings) {
    console.log("\n=== Generating Payslips Report Excel ===");
    console.log(`Processing ${payslipData.length} payslip records`);

    const workbook = new excel.Workbook();
    const worksheet = workbook.addWorksheet("Payslips Report");

    // Set default font and freeze panes
    worksheet.views = [{ state: 'frozen', ySplit: 6 }];

    // Row 1 - Company Trading Name
    worksheet.mergeCells('A1:P1');
    const companyNameCell = worksheet.getCell('A1');
    companyNameCell.value = metadata.companyName;
    companyNameCell.font = { name: 'Inter', size: 16, bold: true };
    companyNameCell.alignment = { horizontal: 'center', vertical: 'middle' };
    console.log("Set company trading name in row 1:", companyNameCell.value);

    // Row 2 - Physical Address (if available)
    if (metadata.physicalAddress) {
      worksheet.mergeCells('A2:P2');
      const physicalAddressCell = worksheet.getCell('A2');
      physicalAddressCell.value = `Physical Address: ${metadata.physicalAddress}`;
      physicalAddressCell.font = { name: 'Inter', size: 10 };
      physicalAddressCell.alignment = { horizontal: 'center' };
    }

    // Row 3 - Postal Address (if available)
    if (metadata.postalAddress) {
      worksheet.mergeCells('A3:P3');
      const postalAddressCell = worksheet.getCell('A3');
      postalAddressCell.value = `Postal Address: ${metadata.postalAddress}`;
      postalAddressCell.font = { name: 'Inter', size: 10 };
      postalAddressCell.alignment = { horizontal: 'center' };
    }

    // Row 4 - Report Title
    worksheet.mergeCells('A4:P4');
    const titleCell = worksheet.getCell('A4');
    titleCell.value = 'Payslips Report';
    titleCell.font = { name: 'Inter', size: 14, bold: true };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Row 5 - Date Range and Summary
    worksheet.mergeCells('A5:P5');
    const summaryCell = worksheet.getCell('A5');
    let summaryText = `Generated on ${moment(metadata.generatedAt).format('DD/MM/YYYY HH:mm')}`;
    if (metadata.dateRange && metadata.dateRange.startDate && metadata.dateRange.endDate) {
      summaryText += ` | Period: ${moment(metadata.dateRange.startDate).format('DD/MM/YYYY')} to ${moment(metadata.dateRange.endDate).format('DD/MM/YYYY')}`;
    }
    summaryText += ` | Total Records: ${metadata.totalRecords} | Employees: ${metadata.totalEmployees}`;
    summaryCell.value = summaryText;
    summaryCell.font = { name: 'Inter', size: 10 };
    summaryCell.alignment = { horizontal: 'center' };

    // Row 6 - Headers
    const headers = [
      'Employee Number',
      'Employee Name',
      'ID Number',
      'Department',
      'Cost Centre',
      'Pay Period Start',
      'Pay Period End',
      'Pay Frequency',
      'Basic Salary',
      'Gross Pay',
      'PAYE',
      'UIF',
      'SDL',
      'Total Deductions',
      'Net Pay',
      'Payment Method'
    ];

    // Add headers to row 6
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(6, index + 1);
      cell.value = header;
    });

    // Style the header row
    const headerRow = worksheet.getRow(6);
    headerRow.font = { name: 'Inter', size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
    headerRow.height = 30;

    // Add borders and background to header row
    for (let i = 1; i <= headers.length; i++) {
      const cell = headerRow.getCell(i);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF6366F1' } // PandaPayroll primary color
      };
    }

    // Add data rows starting from row 7
    let currentRow = 7;
    payslipData.forEach((payslip, index) => {
      const row = worksheet.getRow(currentRow);

      // Format employee name
      const employeeName = `${payslip.employee.firstName || ''} ${payslip.employee.lastName || ''}`.trim();

      // Set cell values
      row.getCell(1).value = payslip.employee.companyEmployeeNumber || '';
      row.getCell(2).value = employeeName;
      row.getCell(3).value = payslip.employee.idNumber || '';
      row.getCell(4).value = payslip.employee.department || '';
      row.getCell(5).value = payslip.employee.costCentre || '';
      row.getCell(6).value = moment(payslip.startDate).format('DD/MM/YYYY');
      row.getCell(7).value = moment(payslip.endDate).format('DD/MM/YYYY');
      row.getCell(8).value = payslip.payFrequency?.description || payslip.frequency || '';
      row.getCell(9).value = payslip.basicSalary || 0;
      row.getCell(10).value = payslip.grossPay || 0;
      row.getCell(11).value = payslip.PAYE || 0;
      row.getCell(12).value = payslip.UIF || 0;
      row.getCell(13).value = payslip.SDL || 0;
      row.getCell(14).value = payslip.totalDeductions || 0;
      row.getCell(15).value = payslip.netPay || 0;
      row.getCell(16).value = payslip.employee.paymentMethod || '';

      // Format currency columns
      [9, 10, 11, 12, 13, 14, 15].forEach(colIndex => {
        const cell = row.getCell(colIndex);
        cell.numFmt = 'R #,##0.00';
      });

      // Add borders to data row
      for (let i = 1; i <= headers.length; i++) {
        const cell = row.getCell(i);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }

      // Alternate row colors for better readability
      if (index % 2 === 1) {
        for (let i = 1; i <= headers.length; i++) {
          const cell = row.getCell(i);
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF8FAFC' }
          };
        }
      }

      currentRow++;
    });

    // Set column widths
    const columnWidths = [15, 25, 15, 20, 15, 15, 15, 20, 15, 15, 12, 12, 12, 15, 15, 15];
    columnWidths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });

    // Add auto-filter for the data range
    const lastDataRow = 6 + payslipData.length;
    worksheet.autoFilter = {
      from: { row: 6, column: 1 },
      to: { row: lastDataRow, column: headers.length }
    };

    console.log("Excel generation completed");
    return await workbook.xlsx.writeBuffer();
  }

  // Generate PDF format for Payslips Report
  static async generatePayslipsPDF(payslipData, metadata, settings) {
    console.log("\n=== Generating Payslips Report PDF ===");

    const doc = new PDFDocument({ size: 'A4', margin: 50 });
    const buffers = [];

    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
      // PDF generation complete
    });

    // Header
    doc.fontSize(16).font('Helvetica-Bold').text(metadata.companyName, { align: 'center' });
    doc.moveDown(0.5);
    doc.fontSize(14).font('Helvetica-Bold').text('Payslips Report', { align: 'center' });
    doc.moveDown(0.5);

    // Summary info
    let summaryText = `Generated on ${moment(metadata.generatedAt).format('DD/MM/YYYY HH:mm')}`;
    if (metadata.dateRange && metadata.dateRange.startDate && metadata.dateRange.endDate) {
      summaryText += `\nPeriod: ${moment(metadata.dateRange.startDate).format('DD/MM/YYYY')} to ${moment(metadata.dateRange.endDate).format('DD/MM/YYYY')}`;
    }
    summaryText += `\nTotal Records: ${metadata.totalRecords} | Employees: ${metadata.totalEmployees}`;

    doc.fontSize(10).font('Helvetica').text(summaryText, { align: 'center' });
    doc.moveDown(1);

    // Table headers
    const headers = ['Employee', 'Period', 'Basic Salary', 'Gross Pay', 'Deductions', 'Net Pay'];
    const startY = doc.y;
    const columnWidths = [120, 80, 80, 80, 80, 80];
    let currentX = 50;

    // Draw headers
    doc.fontSize(9).font('Helvetica-Bold');
    headers.forEach((header, index) => {
      doc.text(header, currentX, startY, { width: columnWidths[index], align: 'center' });
      currentX += columnWidths[index];
    });

    doc.moveDown(0.5);
    let currentY = doc.y;

    // Draw data rows
    doc.fontSize(8).font('Helvetica');
    payslipData.forEach((payslip, index) => {
      if (currentY > 700) { // Start new page if needed
        doc.addPage();
        currentY = 50;
      }

      currentX = 50;
      const employeeName = `${payslip.employee.firstName || ''} ${payslip.employee.lastName || ''}`.trim();
      const period = `${moment(payslip.startDate).format('DD/MM')} - ${moment(payslip.endDate).format('DD/MM/YY')}`;

      const rowData = [
        employeeName,
        period,
        `R ${(payslip.basicSalary || 0).toFixed(2)}`,
        `R ${(payslip.grossPay || 0).toFixed(2)}`,
        `R ${(payslip.totalDeductions || 0).toFixed(2)}`,
        `R ${(payslip.netPay || 0).toFixed(2)}`
      ];

      rowData.forEach((data, colIndex) => {
        doc.text(data, currentX, currentY, {
          width: columnWidths[colIndex],
          align: colIndex > 1 ? 'right' : 'left'
        });
        currentX += columnWidths[colIndex];
      });

      currentY += 15;
    });

    doc.end();

    return new Promise((resolve) => {
      doc.on('end', () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  // Generate CSV format for Payslips Report
  static async generatePayslipsCSV(payslipData, metadata, settings) {
    console.log("\n=== Generating Payslips Report CSV ===");

    const headers = [
      'Employee Number',
      'Employee Name',
      'ID Number',
      'Department',
      'Cost Centre',
      'Pay Period Start',
      'Pay Period End',
      'Pay Frequency',
      'Basic Salary',
      'Gross Pay',
      'PAYE',
      'UIF',
      'SDL',
      'Total Deductions',
      'Net Pay',
      'Payment Method'
    ];

    let csvContent = headers.join(',') + '\n';

    payslipData.forEach(payslip => {
      const employeeName = `"${payslip.employee.firstName || ''} ${payslip.employee.lastName || ''}".trim()`;

      const row = [
        payslip.employee.companyEmployeeNumber || '',
        employeeName,
        payslip.employee.idNumber || '',
        payslip.employee.department || '',
        payslip.employee.costCentre || '',
        moment(payslip.startDate).format('DD/MM/YYYY'),
        moment(payslip.endDate).format('DD/MM/YYYY'),
        payslip.payFrequency?.description || payslip.frequency || '',
        (payslip.basicSalary || 0).toFixed(2),
        (payslip.grossPay || 0).toFixed(2),
        (payslip.PAYE || 0).toFixed(2),
        (payslip.UIF || 0).toFixed(2),
        (payslip.SDL || 0).toFixed(2),
        (payslip.totalDeductions || 0).toFixed(2),
        (payslip.netPay || 0).toFixed(2),
        payslip.employee.paymentMethod || ''
      ];

      csvContent += row.join(',') + '\n';
    });

    return Buffer.from(csvContent, 'utf8');
  }

  static async generateReport(req, res) {
    try {
      const { companyCode } = req.params;
      const { format, dateRange, selectedEmployees, selectedFields = [] } = req.body;
      const reportType = req.path.split('/').pop();

      console.log("=== Generate Report Route ===");
      console.log("Report Type:", reportType);
      console.log("Request Body:", req.body);
      console.log("Processing report generation:", {
        companyCode,
        reportType,
        format,
        dateRange,
        selectedFields
      });

      // DEBUGGING: Enhanced backend field selection logging
      if (reportType === "employeeBasicInfo") {
        console.log("=== BACKEND FIELD SELECTION DEBUG ===");
        console.log("Raw selectedFields from request:", selectedFields);
        console.log("selectedFields type:", typeof selectedFields);
        console.log("selectedFields length:", selectedFields ? selectedFields.length : 'undefined');
        console.log("selectedFields is array:", Array.isArray(selectedFields));

        if (Array.isArray(selectedFields)) {
          console.log("Individual selected fields:");
          selectedFields.forEach((field, index) => {
            console.log(`  ${index + 1}. ${field}`);
          });
        }

        // Log all form data keys
        console.log("All request body keys:", Object.keys(req.body));
        console.log("=== END BACKEND DEBUG ===");
      }

      // Get company
      const company = await Company.findOne({ code: companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Get report settings
      const settings = await ReportSettings.findOne({ company: company._id });
      console.log("Found settings:", !!settings);
      console.log("Selected fields from request:", selectedFields);

      // Add selected fields to settings - handle null settings gracefully
      const settingsWithFields = {
        ...(settings ? settings.toObject() : {}),
        selectedFields: selectedFields
      };

      console.log("Settings with fields:", JSON.stringify(settingsWithFields, null, 2));

      // Generate report based on type
      let reportData;
      switch (reportType) {
        case "employeeBasicInfo":
          reportData = await this.generateEmployeeBasicInfo(
            company._id,
            format,
            settingsWithFields,
            selectedEmployees
          );
          break;
        case "payslips":
          // Calculate date range based on selection
          const dates = calculateDateRange(dateRange);
          reportData = await this.generatePayslips(
            company._id,
            format,
            settingsWithFields,
            dates
          );
          break;
        // ... other cases ...
      }

      // Save report
      const report = new Report({
        company: company._id,
        type: reportType,
        format,
        generatedBy: req.user._id,
        data: reportData
      });

      await report.save();
      console.log("Report saved:", report._id);

      res.json({ reportId: report._id });
    } catch (error) {
      console.error("Error generating report:", error);
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = ReportController;
