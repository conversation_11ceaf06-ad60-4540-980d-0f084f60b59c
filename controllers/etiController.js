const Employee = require("../models/Employee");
const { calculateAge, validateETIEligibility } = require("../utils/etiHelpers");

const etiController = {
  // Get ETI page
  getETIPage: async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("eti", {
        employee,
        company: req.company,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading ETI page:", error);
      req.flash("error", "Failed to load ETI configuration");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  },

  // Update ETI Status
  updateETIStatus: async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1); // Set to first of month
      effectiveDate.setHours(0, 0, 0, 0); // Set time to midnight

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}/eti`);
    }
  },
};

module.exports = etiController;
