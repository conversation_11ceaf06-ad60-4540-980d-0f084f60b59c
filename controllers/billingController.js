const Company = require('../models/Company');
const Invoice = require('../models/invoice');
const Employee = require('../models/Employee');
const BillingPreference = require('../models/billingPreference');
const moment = require('moment');
const mongoose = require('mongoose');
const paystackService = require('../services/paystackService');

/**
 * Render billing preferences page
 */
exports.getBillingPreferences = async (req, res) => {
  try {
    const companyId = req.user.currentCompany._id;
    
    // Get billing preferences
    const preferences = await BillingPreference.findOne({ company: companyId });
    
    // Count active employees
    const activeEmployees = await Employee.countDocuments({
      company: companyId,
      employmentStatus: true
    });
    
    // Calculate average rate
    let averageRate = '0.00';
    if (activeEmployees > 0) {
      const totalRate = await Employee.aggregate([
        { $match: { company: mongoose.Types.ObjectId(companyId), employmentStatus: true } },
        { $group: { _id: null, total: { $sum: "$rate" } } }
      ]);
      
      if (totalRate.length > 0) {
        averageRate = (totalRate[0].total / activeEmployees).toFixed(2);
      }
    }
    
    // Calculate total monthly price
    const totalMonthlyPrice = calculateBillingAmount(activeEmployees).toFixed(2);
    
    // Trial period information
    const userCreationDate = moment(req.user.createdAt);
    const trialEndDate = moment(userCreationDate).add(30, 'days');
    const isInTrial = moment().isBefore(trialEndDate);
    const trialDaysLeft = isInTrial ? trialEndDate.diff(moment(), 'days') : 0;
    const billingStartDate = trialEndDate.toDate();
    
    // Date range for statement
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    const startDate = req.query.startDate ? 
      new Date(req.query.startDate) : 
      new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1);
      
    // Format dates for input fields
    endDate.setHours(23, 59, 59, 999); // End of day
    
    // Get invoices in date range
    const invoices = await Invoice.find({
      company: companyId,
      date: { $gte: startDate, $lte: endDate }
    }).sort({ date: -1 });
    
    // Render the page
    res.render('billing/preferences', {
      user: req.user,
      preferences,
      activeEmployees,
      averageRate,
      totalMonthlyPrice,
      isInTrial,
      trialDaysLeft,
      billingStartDate,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      invoices,
      csrfToken: req.csrfToken(),
      paystackPublicKey: process.env.PAYSTACK_PUBLIC_KEY || 'pk_test_placeholder'
    });
  } catch (error) {
    console.error('Error getting billing preferences:', error);
    req.flash('error', 'Failed to load billing preferences');
    res.redirect('/dashboard');
  }
};

/**
 * Update billing preferences
 */
exports.updateBillingPreferences = async (req, res) => {
  try {
    const { paymentMethod } = req.body;
    const companyId = req.user.currentCompany._id;
    
    // Update or create preferences
    await BillingPreference.findOneAndUpdate(
      { company: companyId },
      { 
        company: companyId,
        paymentMethod
      },
      { upsert: true, new: true }
    );
    
    req.flash('success', 'Billing preferences updated successfully');
    res.redirect('/billing/preferences');
  } catch (error) {
    console.error('Error updating billing preferences:', error);
    req.flash('error', 'Failed to update billing preferences');
    res.redirect('/billing/preferences');
  }
};

/**
 * Get statement for download (PDF or Excel)
 */
exports.getStatement = async (req, res) => {
  try {
    const { format } = req.params;
    
    // Implementation for PDF and Excel generation
    // ... (not implemented in this example)
    
    res.send(`Statement download in ${format} format not implemented yet`);
  } catch (error) {
    console.error('Error generating statement:', error);
    req.flash('error', 'Failed to generate statement');
    res.redirect('/billing/preferences');
  }
};

/**
 * Calculate billing amount based on number of employees
 * @param {number} activeEmployees - Number of active employees
 * @returns {number} - Billing amount
 */
function calculateBillingAmount(activeEmployees) {
  let amount = 0;
  
  if (activeEmployees <= 0) {
    return 0;
  } else if (activeEmployees <= 10) {
    amount = 25 + ((activeEmployees - 1) * 11.50); // Starter tier
  } else if (activeEmployees <= 50) {
    amount = 25 + ((activeEmployees - 1) * 7.50); // Growth tier
  } else if (activeEmployees <= 100) {
    amount = 25 + ((activeEmployees - 1) * 6.75); // Business tier
  } else {
    amount = 25 + ((activeEmployees - 1) * 5.75); // Enterprise tier
  }
  
  return Math.max(amount, 25); // Minimum billing amount is R25 (base price)
}

module.exports.calculateBillingAmount = calculateBillingAmount; 