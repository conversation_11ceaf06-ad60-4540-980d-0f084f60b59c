const oidReturnService = require('../services/oidReturnService');

// Generate OID return for a specific year
async function generateReturn(companyId, year) {
    try {
        // Calculate start and end dates for the year
        const startDate = new Date(year, 0, 1);  // January 1st
        const endDate = new Date(year, 11, 31);  // December 31st

        // Generate return data
        const returnData = await oidReturnService.generateOIDReturn(
            companyId,
            startDate,
            endDate
        );

        // Validate return data
        const validation = oidReturnService.validateReturn(returnData);
        if (!validation.isValid) {
            throw new Error('Invalid return data: ' + validation.errors.join(', '));
        }

        return returnData;
    } catch (error) {
        console.error('Error in generateReturn:', error);
        throw error;
    }
}

// Save draft return
async function saveDraft(companyId, returnData) {
    try {
        // Validate return data
        const validation = oidReturnService.validateReturn(returnData);
        if (!validation.isValid) {
            throw new Error('Invalid return data: ' + validation.errors.join(', '));
        }

        // Save draft
        await oidReturnService.saveDraft(companyId, returnData);
        return true;
    } catch (error) {
        console.error('Error in saveDraft:', error);
        throw error;
    }
}

// Get draft return for a specific year
async function getDraft(companyId, year) {
    try {
        const draft = await oidReturnService.getDraft(companyId, year);
        if (!draft) {
            return null;
        }

        // Validate draft data
        const validation = oidReturnService.validateReturn(draft);
        if (!validation.isValid) {
            console.warn('Invalid draft data:', validation.errors);
            return null;
        }

        return draft;
    } catch (error) {
        console.error('Error in getDraft:', error);
        throw error;
    }
}

// Generate downloadable file
async function generateDownload(companyId, returnData) {
    try {
        // Validate return data
        const validation = oidReturnService.validateReturn(returnData);
        if (!validation.isValid) {
            throw new Error('Invalid return data: ' + validation.errors.join(', '));
        }

        // Generate file
        const filePath = await oidReturnService.generateDownloadFile(companyId, returnData);
        return filePath;
    } catch (error) {
        console.error('Error in generateDownload:', error);
        throw error;
    }
}

module.exports = {
    generateReturn,
    saveDraft,
    getDraft,
    generateDownload
};
