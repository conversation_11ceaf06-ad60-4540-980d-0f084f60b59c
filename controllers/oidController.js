const express = require("express");
const router = express.Router();
const OIDReturn = require("../models/oidReturn");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const XLSX = require("xlsx");
const Company = require("../models/Company");
const Injury = require("../models/injury");

const getCurrentOIDReturn = async (req, res) => {
  try {
    const year = parseInt(req.query.year) || new Date().getFullYear();
    let oidReturn = await OIDReturn.findOne({ year });

    if (!oidReturn) {
      // Calculate the annual caps (you may want to adjust these calculations)
      const annualCap = 568959; // Current year cap
      const nextYearAnnualCap = Math.round(annualCap * 1.05); // 5% increase for next year

      oidReturn = new OIDReturn({
        year,
        status: "Draft",
        annualCap: annualCap,
        nextYearAnnualCap: nextYearAnnualCap,
        monthlyData: Array(12).fill({
          employeesNumber: 0,
          employeesEarnings: 0,
          directorsNumber: 0,
          directorsEarnings: 0,
        }),
        totalEmployeesNumber: 0,
        totalEmployeesEarnings: 0,
        totalDirectorsNumber: 0,
        totalDirectorsEarnings: 0,
        averageEmployeesNumber: 0,
        averageEmployeesEarnings: 0,
        averageDirectorsNumber: 0,
        averageDirectorsEarnings: 0,
      });
      await oidReturn.save();
    }

    res.json(oidReturn);
  } catch (error) {
    console.error("Error fetching current OID return:", error);
    res.status(500).json({ message: "Error fetching OID return data" });
  }
};

const generateOIDReturn = async (req, res) => {
  try {
    const { year } = req.body;
    const startDate = new Date(year - 1, 2, 1); // March 1st of previous year
    const endDate = new Date(year, 1, 29); // February 29th (or 28th) of current year

    console.log(
      `Generating OID return for period: ${startDate.toISOString()} to ${endDate.toISOString()}`
    );

    const employees = await Employee.find({ oidEligible: true });
    const payrolls = await Payroll.find({
      employee: { $in: employees.map((e) => e._id) },
      month: { $gte: startDate, $lte: endDate },
    }).populate("employee");

    let totalEarnings = 0;
    const uniqueEmployees = new Set();
    const annualThreshold = 568959; // Update this value annually

    payrolls.forEach((payroll) => {
      uniqueEmployees.add(payroll.employee._id.toString());
      // Add basicSalary to the totalEarnings, respecting the annual threshold
      totalEarnings += Math.min(
        (payroll.basicSalary || 0) + (payroll.oidEarnings || 0),
        annualThreshold
      );
    });

    const numberOfEmployees = uniqueEmployees.size;

    const oidReturn = await OIDReturn.findOneAndUpdate(
      { year },
      {
        year,
        totalEarnings,
        numberOfEmployees,
        status: "Draft",
        annualThreshold,
        // ... other fields ...
      },
      { new: true, upsert: true }
    );

    console.log(`OID return generated for year ${year}`);
    res.json(oidReturn);
  } catch (error) {
    console.error("Error generating OID return:", error);
    res.status(500).json({ message: "Error generating OID return" });
  }
};

const submitOIDReturn = async (req, res) => {
  try {
    const { year } = req.body;
    const currentDate = new Date();
    const deadline = new Date(year, 5, 30); // June 30th
    const isLateSubmission = currentDate > deadline;

    let penaltyAmount = 0;
    if (isLateSubmission) {
      const oidReturn = await OIDReturn.findOne({ year });
      const totalEarnings =
        oidReturn.totalEmployeesEarnings + oidReturn.totalDirectorsEarnings;
      penaltyAmount = totalEarnings * 0.1; // 10% penalty
    }

    const updatedOidReturn = await OIDReturn.findOneAndUpdate(
      { year },
      {
        status: "Submitted",
        submissionDate: currentDate,
        isLateSubmission,
        penaltyAmount,
      },
      { new: true }
    );

    if (!updatedOidReturn) {
      return res.status(404).json({ message: "OID return not found" });
    }

    res.json(updatedOidReturn);
  } catch (error) {
    console.error("Error submitting OID return:", error);
    res.status(500).json({ message: "Error submitting OID return" });
  }
};

const downloadOIDReturn = async (req, res) => {
  try {
    const { year } = req.params;
    const company = await Company.findById(req.company._id)
      .select('name tradingName registrationNumber businessType companyRegNumber taxNumber physicalAddress postalAddress telephone cellphone email chiefExecutive generalManager')
      .lean();

    // Get all employees for the company
    const employees = await Employee.find({ 
      company: req.company._id,
      oidEligible: true 
    }).lean();

    // Get all directors
    const directors = await Employee.find({ 
      company: req.company._id,
      isDirector: true 
    }).lean();

    // Calculate assessment period
    const startDate = new Date(year - 1, 2, 1); // March 1st of previous year
    const endDate = new Date(year, 1, 29); // February 29th of current year

    // Get payroll data for the period
    const payrollData = await Payroll.find({
      company: req.company._id,
      date: { $gte: startDate, $lte: endDate }
    }).populate('employee').lean();

    // Get injuries data
    const injuries = await Injury.find({
      company: req.company._id,
      date: { $gte: startDate, $lte: endDate }
    }).populate('employee').lean();

    // Process employee earnings
    const employeeEarnings = employees.map(employee => {
      const employeePayrolls = payrollData.filter(p => 
        p.employee._id.toString() === employee._id.toString()
      );

      const totalBasicSalary = employeePayrolls.reduce((sum, p) => sum + (p.basicSalary || 0), 0);
      const totalOvertime = employeePayrolls.reduce((sum, p) => sum + (p.overtime || 0), 0);
      const totalAllowances = employeePayrolls.reduce((sum, p) => sum + (p.allowances || 0), 0);
      const totalEarnings = totalBasicSalary + totalOvertime + totalAllowances;
      
      // Apply annual threshold
      const annualThreshold = 568959; // Update this value annually
      const assessableAmount = Math.min(totalEarnings, annualThreshold);
      const excludedEarnings = Math.max(0, totalEarnings - annualThreshold);

      return {
        employeeNumber: employee.employeeNumber,
        idNumber: employee.idNumber,
        firstName: employee.firstName,
        lastName: employee.lastName,
        startDate: employee.startDate,
        endDate: employee.endDate,
        basicSalary: totalBasicSalary,
        overtime: totalOvertime,
        allowances: totalAllowances,
        totalEarnings,
        excludedEarnings,
        assessableAmount
      };
    });

    // Process director earnings
    const directorEarnings = directors.map(director => {
      const directorPayrolls = payrollData.filter(p => 
        p.employee._id.toString() === director._id.toString()
      );

      const annualEarnings = directorPayrolls.reduce((sum, p) => 
        sum + (p.basicSalary || 0) + (p.allowances || 0), 0
      );

      // Apply annual threshold
      const annualThreshold = 568959; // Update this value annually
      const assessableAmount = Math.min(annualEarnings, annualThreshold);
      const excludedEarnings = Math.max(0, annualEarnings - annualThreshold);

      return {
        directorNumber: director.employeeNumber,
        idNumber: director.idNumber,
        firstName: director.firstName,
        lastName: director.lastName,
        appointmentDate: director.startDate,
        annualEarnings,
        excludedEarnings,
        assessableAmount
      };
    });

    // Process injuries data
    const processedInjuries = injuries.map(injury => ({
      employeeNumber: injury.employee.employeeNumber,
      employeeFirstName: injury.employee.firstName,
      employeeLastName: injury.employee.lastName,
      injuryDate: injury.date,
      injuryType: injury.type,
      bodyPart: injury.bodyPart,
      daysOff: injury.daysOffWork,
      medicalExpenses: injury.medicalExpenses,
      status: injury.status
    }));

    // Get submitter info (current user)
    const submitter = {
      name: req.user.firstName + ' ' + req.user.lastName,
      position: req.user.position
    };

    // Create Excel file
    const OIDExcelTemplate = require('../templates/oid_return_template');
    const template = new OIDExcelTemplate({
      company,
      employees: employeeEarnings,
      directors: directorEarnings,
      injuries: processedInjuries,
      submitter
    });

    const workbook = await template.generate();

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=OID_Return_${year}.xlsx`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('Error generating OID return Excel:', error);
    res.status(500).json({ message: 'Error generating OID return Excel file' });
  }
};

// New function to check submission deadline
const checkSubmissionPeriod = (req, res) => {
  const currentDate = new Date();
  const startDate = new Date(currentDate.getFullYear(), 3, 1); // April 1st
  const endDate = new Date(currentDate.getFullYear(), 5, 30); // June 30th
  const isWithinSubmissionPeriod =
    currentDate >= startDate && currentDate <= endDate;

  res.json({ isWithinSubmissionPeriod });
};

// New function to calculate late submission penalty
const calculatePenalty = (submissionDate) => {
  const deadline = new Date(submissionDate.getFullYear(), 5, 30); // June 30th
  if (submissionDate > deadline) {
    // Calculate 10% penalty
    // You'll need to implement the actual calculation based on the total amount
  }
};

// Helper function to generate OID Excel file
async function generateOIDExcel(oidReturn) {
  try {
    // Get required data
    const company = await Company.findById(oidReturn.company)
      .select('name tradingName registrationNumber businessType companyRegNumber taxNumber physicalAddress postalAddress telephone cellphone email chiefExecutive generalManager')
      .lean();

    // Get all employees and directors
    const employees = await Employee.find({ 
      company: oidReturn.company,
      oidEligible: true 
    }).lean();

    const directors = await Employee.find({ 
      company: oidReturn.company,
      isDirector: true 
    }).lean();

    // Calculate assessment period
    const startDate = new Date(oidReturn.year - 1, 2, 1); // March 1st of previous year
    const endDate = new Date(oidReturn.year, 1, 28); // February 28th/29th of current year

    // Get payroll data for the period
    const payrollData = await Payroll.find({
      company: oidReturn.company,
      date: { $gte: startDate, $lte: endDate }
    }).populate('employee').lean();

    // Get injuries data
    const injuries = await Injury.find({
      company: oidReturn.company,
      date: { $gte: startDate, $lte: endDate }
    }).populate('employee').lean();

    // Process employee earnings
    const employeeEarnings = employees.map(employee => {
      const employeePayrolls = payrollData.filter(p => 
        p.employee._id.toString() === employee._id.toString()
      );

      const totalBasicSalary = employeePayrolls.reduce((sum, p) => sum + (p.basicSalary || 0), 0);
      const totalOvertime = employeePayrolls.reduce((sum, p) => sum + (p.overtime || 0), 0);
      const totalAllowances = employeePayrolls.reduce((sum, p) => sum + (p.allowances || 0), 0);
      const totalEarnings = totalBasicSalary + totalOvertime + totalAllowances;
      
      // Apply annual threshold
      const annualThreshold = 568959; // Update this value annually
      const assessableAmount = Math.min(totalEarnings, annualThreshold);
      const excludedEarnings = Math.max(0, totalEarnings - annualThreshold);

      return {
        employeeNumber: employee.employeeNumber,
        idNumber: employee.idNumber,
        firstName: employee.firstName,
        lastName: employee.lastName,
        startDate: employee.startDate,
        endDate: employee.endDate,
        basicSalary: totalBasicSalary,
        overtime: totalOvertime,
        allowances: totalAllowances,
        totalEarnings,
        excludedEarnings,
        assessableAmount
      };
    });

    // Process director earnings
    const directorEarnings = directors.map(director => {
      const directorPayrolls = payrollData.filter(p => 
        p.employee._id.toString() === director._id.toString()
      );

      const annualEarnings = directorPayrolls.reduce((sum, p) => 
        sum + (p.basicSalary || 0) + (p.allowances || 0), 0
      );

      // Apply annual threshold
      const annualThreshold = 568959; // Update this value annually
      const assessableAmount = Math.min(annualEarnings, annualThreshold);
      const excludedEarnings = Math.max(0, annualEarnings - annualThreshold);

      return {
        directorNumber: director.employeeNumber,
        idNumber: director.idNumber,
        firstName: director.firstName,
        lastName: director.lastName,
        appointmentDate: director.startDate,
        annualEarnings,
        excludedEarnings,
        assessableAmount
      };
    });

    // Process injuries data
    const processedInjuries = injuries.map(injury => ({
      employeeNumber: injury.employee.employeeNumber,
      employeeFirstName: injury.employee.firstName,
      employeeLastName: injury.employee.lastName,
      injuryDate: injury.date,
      injuryType: injury.type,
      bodyPart: injury.bodyPart,
      daysOff: injury.daysOffWork,
      medicalExpenses: injury.medicalExpenses,
      status: injury.status
    }));

    // Create Excel file
    const OIDExcelTemplate = require('../templates/oid_return_template');
    const template = new OIDExcelTemplate({
      company,
      employees: employeeEarnings,
      directors: directorEarnings,
      injuries: processedInjuries,
      year: oidReturn.year,
      status: oidReturn.status,
      submissionDate: oidReturn.submissionDate
    });

    return await template.generate();
  } catch (error) {
    console.error('Error generating OID Excel:', error);
    throw new Error('Failed to generate OID Excel file');
  }
}

const oidController = {
  getCurrentOIDReturn,
  generateOIDReturn,
  submitOIDReturn,
  downloadOIDReturn,
  checkSubmissionPeriod,
  calculatePenalty,
  generateOIDExcel
};

module.exports = oidController;
