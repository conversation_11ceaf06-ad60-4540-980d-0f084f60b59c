const Company = require("../models/Company");
const RFIConfig = require("../models/rfiConfig");
const PayComponent = require("../models/payComponent");

exports.getRFIConfig = async (req, res) => {
  try {
    const companyId = req.user.currentCompany;
    const company = await Company.findById(companyId);

    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/dashboard");
    }

    let rfiConfig = await RFIConfig.findOne({ company: companyId });
    if (!rfiConfig) {
      rfiConfig = new RFIConfig({
        company: companyId,
        option: "selectedIncome", // Default option
        selectedIncomes: [],
        percentageIncomes: new Map(),
      });
    }

    const payComponents = await PayComponent.find({ company: companyId });

    res.render("defineRFI", {
      rfiConfig,
      payComponents,
      company, // Pass the company object to the view
    });
  } catch (error) {
    console.error("Error in getRFIConfig:", error);
    req.flash("error", "An error occurred while loading the RFI configuration");
    res.redirect("/dashboard");
  }
};

exports.updateRFIConfig = async (req, res) => {
  const { rfiOption, selectedIncomes, percentageIncomes, percentageValues } =
    req.body;

  let config = (await RFIConfig.findOne()) || new RFIConfig();
  config.option = rfiOption;

  if (rfiOption === "selectedIncome") {
    config.selectedIncomes = selectedIncomes || [];
    config.percentageIncomes = {};
  } else if (rfiOption === "percentagePerIncome") {
    config.selectedIncomes = [];
    config.percentageIncomes = {};
    percentageIncomes.forEach((income, index) => {
      config.percentageIncomes.set(
        income,
        parseFloat(percentageValues[income])
      );
    });
  }

  await config.save();
  res.redirect("/admin/defineRFI");
};

// In controllers/adminController.js

exports.getRFIWizard = async (req, res) => {
  try {
    const companyId = req.user.currentCompany;
    const company = await Company.findById(companyId);

    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/dashboard");
    }

    const payComponents = await PayComponent.find({ company: companyId });
    const existingConfig = await RFIConfig.findOne({ company: companyId });

    res.render("rfiWizard", {
      payComponents,
      existingConfig,
      step: req.query.step || "1",
      company: company, // Add this line to pass the company to the view
    });
  } catch (error) {
    console.error("Error in getRFIWizard:", error);
    req.flash("error", "An error occurred while loading the RFI wizard");
    res.redirect("/dashboard");
  }
};

exports.postRFIWizard = async (req, res) => {
  try {
    const companyId = req.user.currentCompany;
    const company = await Company.findById(companyId);

    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/dashboard");
    }

    const {
      step,
      rfiOption,
      selectedIncomes,
      percentageIncomes,
      effectiveDate,
    } = req.body;

    let config =
      (await RFIConfig.findOne({ company: companyId })) ||
      new RFIConfig({ company: companyId });

    // Always set the option, regardless of the step
    if (rfiOption) {
      config.option = rfiOption;
    }

    switch (step) {
      case "1":
        // Option is already set above
        break;
      case "2":
        if (config.option === "selectedIncome") {
          config.selectedIncomes = selectedIncomes || [];
          config.percentageIncomes = {};
        } else if (config.option === "percentagePerIncome") {
          config.selectedIncomes = [];
          config.percentageIncomes = new Map(
            Object.entries(percentageIncomes || {})
          );
        }
        break;
      case "3":
        if (effectiveDate) {
          config.effectiveDate = new Date(effectiveDate);
        }
        await config.save();
        req.flash("success", "RFI configuration completed successfully");
        return res.redirect("/admin/rfi-dashboard");
      default:
        throw new Error("Invalid step");
    }

    // Save after each step
    await config.save();

    // Move to the next step or finish
    const nextStep = parseInt(step) + 1;
    if (nextStep > 3) {
      req.flash("success", "RFI configuration completed successfully");
      return res.redirect("/admin/rfi-dashboard");
    } else {
      res.redirect(`/admin/rfi-wizard?step=${nextStep}`);
    }
  } catch (error) {
    console.error("Error in postRFIWizard:", error);
    req.flash("error", "An error occurred while saving the RFI configuration");
    res.redirect("/admin/rfi-wizard");
  }
};
