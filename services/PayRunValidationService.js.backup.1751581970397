const PayRun = require("../models/payRun");
const PayrollPeriod = require("../models/PayrollPeriod");
const Payslip = require("../models/Payslip");
const moment = require("moment-timezone");

/**
 * PayRunValidationService - <PERSON>les pay run deletion validation and constraints
 * Ensures safe deletion of pay runs and proper period unlocking
 */
class PayRunValidationService {
  /**
   * Comprehensive validation for pay run deletion
   * @param {string} payRunId - Pay Run ID
   * @param {string} userId - User performing the action
   * @returns {Object} Validation result with isValid, errors, warnings
   */
  static async validateDeletion(payRunId, userId) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      payRun: null,
    };

    try {
      // 1. Find the pay run
      const payRun = await PayRun.findById(payRunId)
        .populate("payrollPeriods")
        .populate("payslips");

      if (!payRun) {
        validationResult.errors.push("Pay run not found");
        validationResult.isValid = false;
        return validationResult;
      }

      validationResult.payRun = payRun;

      // 2. Check if pay run is already deleted
      if (payRun.status === "deleted" || payRun.deletedAt) {
        validationResult.errors.push("Pay run is already deleted");
        validationResult.isValid = false;
        return validationResult;
      }

      // 3. Check pay run status constraints
      if (payRun.status === "released") {
        validationResult.warnings.push(
          "Pay run has been released - deletion may affect employee self-service access"
        );
      }

      if (payRun.status === "finalized") {
        validationResult.warnings.push(
          "Pay run is finalized - ensure this deletion is authorized and necessary"
        );
      }

      // 4. Check for banking/EFT processing
      if (payRun.eftSettings) {
        validationResult.warnings.push(
          "Pay run has EFT settings configured - ensure banking processes are handled appropriately"
        );
      }

      // 5. Check for Xero integration
      if (payRun.xeroSynced) {
        validationResult.warnings.push(
          "Pay run has been synced to Xero - consider accounting implications"
        );
      }

      // 6. Validate associated payroll periods
      if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
        const lockedPeriods = payRun.payrollPeriods.filter(p => p.status === "locked");
        if (lockedPeriods.length > 0) {
          validationResult.warnings.push(
            `${lockedPeriods.length} payroll periods will be unlocked and returned to finalized status`
          );
        }
      }

      // 7. Check for dependent pay runs
      const dependentPayRuns = await PayRun.find({
        relatedPayRun: payRunId,
        status: { $nin: ["deleted", "cancelled"] },
      });

      if (dependentPayRuns.length > 0) {
        validationResult.warnings.push(
          `${dependentPayRuns.length} related pay runs exist - review dependencies`
        );
      }

      // 8. Time-based validations
      const daysSinceCreation = moment().diff(moment(payRun.createdAt), 'days');
      if (daysSinceCreation > 30) {
        validationResult.warnings.push(
          "Pay run is older than 30 days - ensure deletion is appropriate for audit trail"
        );
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating pay run deletion:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Validation error occurred");
      return validationResult;
    }
  }

  /**
   * Check if pay run can be safely deleted (quick check)
   * @param {string} payRunId - Pay Run ID
   * @returns {Object} Result with canDelete boolean and reason
   */
  static async canDelete(payRunId) {
    try {
      const payRun = await PayRun.findById(payRunId);
      if (!payRun) {
        return { canDelete: false, reason: "Pay run not found" };
      }

      if (payRun.status === "deleted" || payRun.deletedAt) {
        return { canDelete: false, reason: "Pay run is already deleted" };
      }

      // Allow deletion for most statuses, but warn for critical ones
      const criticalStatuses = ["released"];
      if (criticalStatuses.includes(payRun.status)) {
        return { 
          canDelete: true, 
          reason: "Deletion allowed but requires confirmation",
          requiresConfirmation: true 
        };
      }

      return { canDelete: true, reason: "Pay run can be deleted" };
    } catch (error) {
      console.error("Error checking deletion eligibility:", error);
      return { canDelete: false, reason: "Error checking eligibility" };
    }
  }

  /**
   * Get all entities that would be affected by pay run deletion
   * @param {string} payRunId - Pay Run ID
   * @returns {Object} Analysis of affected entities
   */
  static async getAffectedEntities(payRunId) {
    try {
      const payRun = await PayRun.findById(payRunId)
        .populate("payrollPeriods")
        .populate("payslips");

      if (!payRun) {
        return { error: "Pay run not found" };
      }

      // Get affected payroll periods
      const affectedPeriods = payRun.payrollPeriods || [];
      
      // Get affected payslips
      const affectedPayslips = payRun.payslips || [];

      // Get dependent pay runs
      const dependentPayRuns = await PayRun.find({
        relatedPayRun: payRunId,
        status: { $nin: ["deleted", "cancelled"] },
      });

      return {
        payRun: {
          _id: payRun._id,
          reference: payRun.reference,
          status: payRun.status,
          period: `${moment(payRun.startDate).format('DD/MM/YYYY')} - ${moment(payRun.endDate).format('DD/MM/YYYY')}`,
        },
        affectedPeriods: affectedPeriods.map(period => ({
          _id: period._id,
          employee: period.employee,
          startDate: period.startDate,
          endDate: period.endDate,
          status: period.status,
          isFinalized: period.isFinalized,
        })),
        affectedPayslips: affectedPayslips.map(slip => ({
          _id: slip._id,
          employee: slip.employee,
          status: slip.status,
        })),
        dependentPayRuns: dependentPayRuns.map(run => ({
          _id: run._id,
          reference: run.reference,
          status: run.status,
        })),
        summary: {
          periodsCount: affectedPeriods.length,
          payslipsCount: affectedPayslips.length,
          dependentPayRunsCount: dependentPayRuns.length,
        }
      };
    } catch (error) {
      console.error("Error getting affected entities:", error);
      return { error: "Failed to analyze affected entities" };
    }
  }

  /**
   * Perform the actual pay run deletion with all safety checks
   * @param {string} payRunId - Pay Run ID
   * @param {string} userId - User performing the action
   * @param {string} reason - Reason for deletion
   * @param {boolean} force - Force deletion even with warnings
   * @returns {Object} Result with success boolean and message
   */
  static async deletePayRun(payRunId, userId, reason = "Manual deletion", force = false) {
    try {
      // Validate before proceeding
      const validation = await this.validateDeletion(payRunId, userId);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join("; "),
          errors: validation.errors,
        };
      }

      // Check for warnings if not forcing
      if (!force && validation.warnings.length > 0) {
        return {
          success: false,
          message: "Deletion requires confirmation due to warnings",
          warnings: validation.warnings,
          requiresConfirmation: true,
        };
      }

      const payRun = validation.payRun;

      // Perform soft deletion using the model method
      await payRun.softDelete(userId, reason);

      return {
        success: true,
        message: "Pay run deleted successfully",
        payRun: {
          _id: payRun._id,
          reference: payRun.reference,
          deletedAt: payRun.deletedAt,
        },
        warnings: validation.warnings,
      };
    } catch (error) {
      console.error("Error deleting pay run:", error);
      return {
        success: false,
        message: "Failed to delete pay run",
        error: error.message,
      };
    }
  }
}

module.exports = PayRunValidationService;
