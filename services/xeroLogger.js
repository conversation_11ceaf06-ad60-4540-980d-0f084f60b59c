const debug = require("debug")("xero:logger");

/**
 * Comprehensive logging utility for Xero operations
 * Provides structured logging with correlation IDs and context
 */
class XeroLogger {
  constructor() {
    this.correlationIds = new Map(); // requestId -> correlationId
    this.logLevels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    this.currentLevel = process.env.XERO_LOG_LEVEL || 'INFO';
  }

  /**
   * Generate a correlation ID for tracking related operations
   * @returns {string} Unique correlation ID
   */
  generateCorrelationId() {
    return `xero_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Set correlation ID for a request
   * @param {string} requestId - Request identifier
   * @param {string} correlationId - Correlation ID
   */
  setCorrelationId(requestId, correlationId) {
    this.correlationIds.set(requestId, correlationId);
  }

  /**
   * Get correlation ID for a request
   * @param {string} requestId - Request identifier
   * @returns {string|null} Correlation ID
   */
  getCorrelationId(requestId) {
    return this.correlationIds.get(requestId) || null;
  }

  /**
   * Create structured log entry
   * @param {string} level - Log level
   * @param {string} operation - Operation name
   * @param {string} message - Log message
   * @param {Object} context - Additional context
   * @returns {Object} Structured log entry
   */
  createLogEntry(level, operation, message, context = {}) {
    const timestamp = new Date().toISOString();
    const correlationId = context.requestId ? this.getCorrelationId(context.requestId) : null;

    return {
      timestamp,
      level,
      operation,
      message,
      correlationId,
      context: {
        ...context,
        environment: process.env.NODE_ENV || 'development'
      }
    };
  }

  /**
   * Check if log level should be output
   * @param {string} level - Log level to check
   * @returns {boolean} True if should log
   */
  shouldLog(level) {
    const levelValue = this.logLevels[level.toUpperCase()] || 0;
    const currentLevelValue = this.logLevels[this.currentLevel.toUpperCase()] || 2;
    return levelValue <= currentLevelValue;
  }

  /**
   * Output log entry
   * @param {Object} logEntry - Structured log entry
   */
  output(logEntry) {
    if (!this.shouldLog(logEntry.level)) {
      return;
    }

    const logString = JSON.stringify(logEntry, null, 2);
    
    switch (logEntry.level.toLowerCase()) {
      case 'error':
        console.error(logString);
        break;
      case 'warn':
        console.warn(logString);
        break;
      case 'info':
        console.info(logString);
        break;
      case 'debug':
        debug(logString);
        break;
      default:
        console.log(logString);
    }
  }

  /**
   * Log token operation
   * @param {string} operation - Token operation (refresh, validate, etc.)
   * @param {Object} context - Operation context
   * @param {string} level - Log level (default: INFO)
   */
  logTokenOperation(operation, context = {}, level = 'INFO') {
    const logEntry = this.createLogEntry(
      level,
      `token_${operation}`,
      `Token ${operation} operation`,
      {
        ...context,
        category: 'token_management'
      }
    );
    this.output(logEntry);
  }

  /**
   * Log API operation
   * @param {string} operation - API operation (accounts, contacts, etc.)
   * @param {Object} context - Operation context
   * @param {string} level - Log level (default: INFO)
   */
  logApiOperation(operation, context = {}, level = 'INFO') {
    const logEntry = this.createLogEntry(
      level,
      `api_${operation}`,
      `Xero API ${operation} operation`,
      {
        ...context,
        category: 'api_call'
      }
    );
    this.output(logEntry);
  }

  /**
   * Log error with enhanced context
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   */
  logError(error, context = {}) {
    const logEntry = this.createLogEntry(
      'ERROR',
      context.operation || 'unknown_operation',
      error.message,
      {
        ...context,
        category: 'error',
        errorType: error.constructor.name,
        stack: error.stack,
        errorCode: error.code,
        httpStatus: error.response?.status
      }
    );
    this.output(logEntry);
  }

  /**
   * Log integration status change
   * @param {string} companyId - Company ID
   * @param {string} oldStatus - Previous status
   * @param {string} newStatus - New status
   * @param {Object} context - Additional context
   */
  logIntegrationStatusChange(companyId, oldStatus, newStatus, context = {}) {
    const logEntry = this.createLogEntry(
      'INFO',
      'integration_status_change',
      `Integration status changed from ${oldStatus} to ${newStatus}`,
      {
        ...context,
        companyId,
        oldStatus,
        newStatus,
        category: 'integration_management'
      }
    );
    this.output(logEntry);
  }

  /**
   * Log performance metrics
   * @param {string} operation - Operation name
   * @param {number} duration - Duration in milliseconds
   * @param {Object} context - Additional context
   */
  logPerformance(operation, duration, context = {}) {
    const logEntry = this.createLogEntry(
      'INFO',
      `performance_${operation}`,
      `Operation ${operation} completed in ${duration}ms`,
      {
        ...context,
        duration,
        category: 'performance'
      }
    );
    this.output(logEntry);
  }

  /**
   * Log user action
   * @param {string} action - User action
   * @param {string} userId - User ID
   * @param {Object} context - Additional context
   */
  logUserAction(action, userId, context = {}) {
    const logEntry = this.createLogEntry(
      'INFO',
      `user_${action}`,
      `User ${userId} performed ${action}`,
      {
        ...context,
        userId,
        category: 'user_action'
      }
    );
    this.output(logEntry);
  }

  /**
   * Create a performance timer
   * @param {string} operation - Operation name
   * @param {Object} context - Operation context
   * @returns {Function} Timer function to call when operation completes
   */
  startTimer(operation, context = {}) {
    const startTime = Date.now();
    
    return () => {
      const duration = Date.now() - startTime;
      this.logPerformance(operation, duration, context);
      return duration;
    };
  }

  /**
   * Clean up old correlation IDs to prevent memory leaks
   * @param {number} maxAge - Maximum age in milliseconds (default: 1 hour)
   */
  cleanupCorrelationIds(maxAge = 60 * 60 * 1000) {
    const cutoff = Date.now() - maxAge;
    
    for (const [requestId, correlationId] of this.correlationIds.entries()) {
      // Extract timestamp from correlation ID
      const timestamp = parseInt(correlationId.split('_')[1]);
      if (timestamp < cutoff) {
        this.correlationIds.delete(requestId);
      }
    }
  }
}

// Create singleton instance
const xeroLogger = new XeroLogger();

// Clean up correlation IDs every 30 minutes
setInterval(() => {
  xeroLogger.cleanupCorrelationIds();
}, 30 * 60 * 1000);

module.exports = xeroLogger;
