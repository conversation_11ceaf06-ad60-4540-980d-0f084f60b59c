const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");

class RFICalculationService {
  async calculateRFI(employeeId) {
    console.log("\n=== RFI Calculation Service ===");
    console.log("Starting calculation for employeeId:", employeeId);

    try {
      // Find employee and verify existence
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found");
        throw new Error("Employee not found");
      }
      console.log("Employee found:", employee._id);
      console.log("RFI Config:", employee.rfiConfig);

      // Find payroll and verify existence
      const payroll = await Payroll.findOne({ employee: employeeId });
      if (!payroll) {
        console.log("Payroll not found");
        throw new Error("Payroll record not found");
      }
      console.log("Payroll found:", payroll._id);
      console.log("Payroll data:", {
        basicSalary: payroll.basicSalary,
        commission: payroll.commission,
        travelAllowance: payroll.travelAllowance,
      });

      // Verify RFI configuration
      if (!employee.rfiConfig || !employee.rfiConfig.option) {
        console.log("No RFI configuration found");
        throw new Error("RFI configuration not found");
      }

      let totalRFI = 0;
      const components = [];

      // Get available components from payroll
      const availableComponents = [];
      console.log("Building available components...");

      // Add basic salary if exists
      if (payroll.basicSalary) {
        console.log("Adding basic salary:", payroll.basicSalary);
        availableComponents.push({
          name: "Basic Salary",
          amount: payroll.basicSalary,
        });
      }

      // Add commission if exists
      if (payroll.commission?.amount) {
        console.log("Adding commission:", payroll.commission.amount);
        availableComponents.push({
          name: "Commission",
          amount: payroll.commission.amount,
        });
      }

      // Add travel allowance if exists
      if (payroll.travelAllowance?.amount) {
        console.log("Adding travel allowance:", payroll.travelAllowance.amount);
        availableComponents.push({
          name: "Travel Allowance",
          amount: payroll.travelAllowance.amount,
        });
      }

      console.log("Available components:", availableComponents);

      // Calculate based on selected method
      if (employee.rfiConfig.option === "selectedIncome") {
        console.log("Using selected income method");
        console.log("Selected incomes:", employee.rfiConfig.selectedIncomes);

        employee.rfiConfig.selectedIncomes.forEach((selected) => {
          const component = availableComponents.find(
            (c) => c.name === selected.name
          );
          if (component) {
            const includedAmount = component.amount;
            totalRFI += includedAmount;
            components.push({
              name: component.name,
              amount: component.amount,
              percentage: 100,
              includedAmount,
            });
            console.log(`Added ${component.name}:`, {
              amount: component.amount,
              includedAmount,
            });
          }
        });
      } else if (employee.rfiConfig.option === "percentagePerIncome") {
        console.log("Using percentage per income method");
        console.log(
          "Percentage incomes:",
          employee.rfiConfig.percentageIncomes
        );

        employee.rfiConfig.percentageIncomes.forEach((income) => {
          const component = availableComponents.find(
            (c) => c.name === income.name
          );
          if (component) {
            const includedAmount = (component.amount * income.percentage) / 100;
            totalRFI += includedAmount;
            components.push({
              name: component.name,
              amount: component.amount,
              percentage: income.percentage,
              includedAmount,
            });
            console.log(`Added ${component.name}:`, {
              amount: component.amount,
              percentage: income.percentage,
              includedAmount,
            });
          }
        });
      }

      console.log("Final calculation results:", {
        totalRFI,
        components,
      });

      // Store calculation result
      await Employee.findByIdAndUpdate(employeeId, {
        "rfiConfig.lastCalculation": {
          date: new Date(),
          amount: totalRFI,
          components: components,
        },
      });

      return {
        total: totalRFI,
        components: components,
        calculationDate: new Date(),
      };
    } catch (error) {
      console.error("RFI Calculation Error:", error);
      console.error("Stack:", error.stack);
      throw new Error(`RFI calculation failed: ${error.message}`);
    }
  }
}

module.exports = new RFICalculationService();
