const redisService = require("./redisService");
const speakeasy = require("speakeasy");
const crypto = require("crypto");

class AuthService {
  async initiate2FA(user, sessionId) {
    const twoFactorState = {
      userId: user._id.toString(),
      email: user.email,
      timestamp: Date.now(),
      secret: user.twoFactorSecret,
      redirectUrl:
        user.companies?.length > 0
          ? user.currentCompany
            ? `/clients/${user.currentCompany}/dashboard`
            : "/companies/select"
          : "/companies/create",
    };

    const key = `2fa:${sessionId}`;
    await redisService.set(key, twoFactorState);

    return twoFactorState;
  }

  async verify2FA(sessionId, token) {
    const key = `2fa:${sessionId}`;
    const state = await redisService.get(key);

    if (!state) return null;

    const verified = speakeasy.totp.verify({
      secret: state.secret,
      encoding: "base32",
      token: token,
      window: 1,
    });

    if (verified) {
      await redisService.del(key);
      return {
        userId: state.userId,
        redirectUrl: state.redirectUrl,
      };
    }

    return null;
  }

  async get2FAState(sessionId) {
    const key = `2fa:${sessionId}`;
    return await redisService.get(key);
  }
}

module.exports = new AuthService();
