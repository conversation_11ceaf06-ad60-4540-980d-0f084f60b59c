const Employee = require("../models/Employee");
const ReportSettings = require("../models/ReportSettings");
const Excel = require("exceljs");
const PDFDocument = require("pdfkit");
const moment = require("moment");
const { Parser } = require("json2csv");

class EmployeeListReportService {
  static async generateReport(companyId, format, settings) {
    try {
      console.log("Starting report generation:", {
        companyId,
        format,
        settings,
      });

      // Get employees with necessary fields based on settings
      const employees = await this.getEmployeeData(companyId, settings);
      console.log(`Found ${employees.length} employees`);

      // Generate report in requested format
      console.log("Generating report in format:", format);
      let reportBuffer;
      switch (format) {
        case "pdf":
          reportBuffer = await this.generatePDF(employees, settings);
          break;
        case "excel":
          reportBuffer = await this.generateExcel(employees, settings);
          break;
        case "csv":
          reportBuffer = await this.generateCSV(employees, settings);
          break;
        default:
          throw new Error("Unsupported format");
      }
      console.log("Report buffer generated successfully");

      return reportBuffer;
    } catch (error) {
      console.error("Error in generateReport:", error);
      console.error("Stack trace:", error.stack);
      throw error;
    }
  }

  static async getEmployeeData(companyId, settings) {
    try {
      console.log("Getting employee data with settings:", settings);

      // Build query based on settings
      const query = { company: companyId };

      // Define fields to select based on settings
      let selectFields =
        "firstName lastName companyEmployeeNumber jobTitle department";

      if (settings.includePersonalInfo) {
        selectFields += " email phone dob gender race";
      }

      if (settings.includeBankDetails) {
        selectFields += " bankDetails";
      }

      if (settings.includePayrollInfo) {
        selectFields += " payFrequency workingHours regularHours.hoursPerDay";
      }

      console.log("Query:", query);
      console.log("Select fields:", selectFields);

      // Fetch employees with selected fields
      const employees = await Employee.find(query)
        .select(selectFields)
        .sort({ lastName: 1, firstName: 1 });

      console.log(`Found ${employees.length} employees`);
      return employees;
    } catch (error) {
      console.error("Error in getEmployeeData:", error);
      console.error("Stack trace:", error.stack);
      throw error;
    }
  }

  static async generatePDF(employees, settings) {
    const doc = new PDFDocument({ margin: 50 });
    const buffers = [];

    // Collect PDF data chunks
    doc.on("data", (buffer) => buffers.push(buffer));
    doc.on("end", () => {});

    // Add header
    doc.fontSize(20).text("Employee List Report", { align: "center" });
    doc.moveDown();
    doc.fontSize(12).text(`Generated on: ${moment().format("MMMM D, YYYY")}`);
    doc.moveDown();

    // Add table headers
    const headers = ["Employee No.", "Name", "Job Title", "Department"];
    if (settings.includePersonalInfo) {
      headers.push("Email", "Phone");
    }
    if (settings.includeBankDetails) {
      headers.push("Bank", "Account No.");
    }

    // Add employee data
    let yPosition = 150;
    const rowHeight = 20;

    // Draw headers
    headers.forEach((header, i) => {
      doc.text(header, 50 + i * 100, yPosition);
    });
    yPosition += rowHeight;

    // Draw employee rows
    employees.forEach((employee) => {
      const row = [
        employee.companyEmployeeNumber,
        `${employee.firstName} ${employee.lastName}`,
        employee.jobTitle,
        employee.department,
      ];

      if (settings.includePersonalInfo) {
        row.push(employee.email, employee.phone);
      }
      if (settings.includeBankDetails) {
        row.push(
          employee.bankDetails?.bankName,
          employee.bankDetails?.accountNumber
        );
      }

      row.forEach((cell, i) => {
        doc.text(cell || "", 50 + i * 100, yPosition);
      });
      yPosition += rowHeight;

      // Add new page if needed
      if (yPosition > 700) {
        doc.addPage();
        yPosition = 50;
      }
    });

    doc.end();

    // Return promise that resolves with PDF buffer
    return new Promise((resolve) => {
      doc.on("end", () => {
        resolve(Buffer.concat(buffers));
      });
    });
  }

  static async generateExcel(employees, settings) {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet("Employee List");

    // Define columns based on settings
    const columns = [
      { header: "Employee No.", key: "employeeNo", width: 15 },
      { header: "First Name", key: "firstName", width: 20 },
      { header: "Last Name", key: "lastName", width: 20 },
      { header: "Job Title", key: "jobTitle", width: 25 },
      { header: "Department", key: "department", width: 20 },
    ];

    // Add optional columns based on settings
    if (settings.includePersonalInfo) {
      columns.push(
        { header: "Email", key: "email", width: 30 },
        { header: "Phone", key: "phone", width: 15 },
        { header: "Date of Birth", key: "dob", width: 15 },
        { header: "Gender", key: "gender", width: 10 },
        { header: "Race", key: "race", width: 15 }
      );
    }

    if (settings.includeBankDetails) {
      columns.push(
        { header: "Bank Name", key: "bankName", width: 20 },
        { header: "Account Number", key: "accountNumber", width: 20 },
        { header: "Branch Code", key: "branchCode", width: 15 },
        { header: "Account Type", key: "accountType", width: 15 }
      );
    }

    if (settings.includePayrollInfo) {
      columns.push(
        { header: "Pay Frequency", key: "payFrequency", width: 15 },
        { header: "Working Hours", key: "workingHours", width: 15 },
        { header: "ETI Status", key: "etiStatus", width: 20 }
      );
    }

    worksheet.columns = columns;

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "F3F4F6" },
    };

    // Add employee data
    employees.forEach((employee) => {
      const row = {
        employeeNo: employee.companyEmployeeNumber,
        firstName: employee.firstName,
        lastName: employee.lastName,
        jobTitle: employee.jobTitle,
        department: employee.department,
      };

      if (settings.includePersonalInfo) {
        row.email = employee.email;
        row.phone = employee.phone;
        row.dob = employee.dob ? moment(employee.dob).format("YYYY-MM-DD") : "";
        row.gender = employee.gender;
        row.race = employee.race;
      }

      if (settings.includeBankDetails) {
        row.bankName = employee.bankDetails?.bankName;
        row.accountNumber = employee.bankDetails?.accountNumber;
        row.branchCode = employee.bankDetails?.branchCode;
        row.accountType = employee.bankDetails?.accountType;
      }

      if (settings.includePayrollInfo) {
        row.payFrequency = employee.payFrequency?.name;
        row.workingHours = employee.workingHours;
        row.etiStatus = employee.etiStatus;
      }

      worksheet.addRow(row);
    });

    // Add borders and alignment
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.alignment = { vertical: "middle", horizontal: "left" };
      });
    });

    // Auto-filter
    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: worksheet.columns.length },
    };

    return await workbook.xlsx.writeBuffer();
  }

  static async generateCSV(employees, settings) {
    // Define fields based on settings
    const fields = [
      { label: "Employee No.", value: "companyEmployeeNumber" },
      { label: "First Name", value: "firstName" },
      { label: "Last Name", value: "lastName" },
      { label: "Job Title", value: "jobTitle" },
      { label: "Department", value: "department" },
    ];

    if (settings.includePersonalInfo) {
      fields.push(
        { label: "Email", value: "email" },
        { label: "Phone", value: "phone" },
        {
          label: "Date of Birth",
          value: (row) => (row.dob ? moment(row.dob).format("YYYY-MM-DD") : ""),
        },
        { label: "Gender", value: "gender" },
        { label: "Race", value: "race" }
      );
    }

    if (settings.includeBankDetails) {
      fields.push(
        { label: "Bank Name", value: "bankDetails.bankName" },
        { label: "Account Number", value: "bankDetails.accountNumber" },
        { label: "Branch Code", value: "bankDetails.branchCode" },
        { label: "Account Type", value: "bankDetails.accountType" }
      );
    }

    if (settings.includePayrollInfo) {
      fields.push(
        { label: "Pay Frequency", value: "payFrequency.name" },
        { label: "Working Hours", value: "workingHours" },
        { label: "ETI Status", value: "etiStatus" }
      );
    }

    const opts = {
      fields,
      delimiter: ",",
      quote: '"',
      header: true,
      includeEmptyRows: false,
    };

    try {
      const parser = new Parser(opts);
      return parser.parse(employees);
    } catch (err) {
      console.error("Error generating CSV:", err);
      throw new Error("Failed to generate CSV file");
    }
  }

  // Helper method to format currency
  static formatCurrency(amount) {
    return new Intl.NumberFormat("en-ZA", {
      style: "currency",
      currency: "ZAR",
    }).format(amount);
  }

  // Helper method to format dates
  static formatDate(date) {
    return date ? moment(date).format("DD/MM/YYYY") : "";
  }
}

module.exports = EmployeeListReportService;
