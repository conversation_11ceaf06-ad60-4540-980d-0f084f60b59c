const mongoose = require('mongoose');
const { XeroClient } = require('xero-node');
const Integration = require('../models/Integration');
const Company = require('../models/Company');
const fs = require('fs');
const path = require('path');

// Log file path
const LOG_DIR = path.join(__dirname, '..', 'logs');
const LOG_FILE = path.join(LOG_DIR, 'xero-token-refresh-service.log');

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Helper function to log to file and console
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  
  
  // Append to log file
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Helper function to format date
const formatDate = (date) => {
  return new Date(date).toLocaleString();
};

class XeroTokenRefreshService {
  constructor() {
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    // Check for required environment variables
    if (!process.env.XERO_CLIENT_ID || !process.env.XERO_CLIENT_SECRET) {
      throw new Error('XERO_CLIENT_ID and XERO_CLIENT_SECRET environment variables must be set');
    }

    this.isInitialized = true;
    log('XeroTokenRefreshService initialized');
  }

  getXeroClient() {
    return new XeroClient({
      clientId: process.env.XERO_CLIENT_ID,
      clientSecret: process.env.XERO_CLIENT_SECRET,
      redirectUris: [`${process.env.PROTOCOL || 'https'}://${process.env.HOST || 'payroll.pss-group.co.za'}/xero/callback`],
      scopes: 'offline_access openid profile email accounting.transactions accounting.journals accounting.settings accounting.contacts'.split(' ')
    });
  }

  async refreshTokenIfNeeded(integration, daysBeforeExpiry = 7) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const companyName = integration.company ? integration.company.name : 'Unknown';
    const companyCode = integration.company ? integration.company.companyCode : 'Unknown';
    
    log(`Checking token for ${companyName} (${companyCode})...`);
    
    // Extract credentials
    const accessToken = integration.credentials.get('accessToken');
    const refreshToken = integration.credentials.get('refreshToken');
    const tenantId = integration.credentials.get('tenantId');
    const expiresAt = integration.credentials.get('expiresAt');
    
    if (!accessToken || !refreshToken || !tenantId) {
      log(`Missing required credentials for ${companyName}`, 'ERROR');
      return {
        success: false,
        message: 'Missing required credentials',
        refreshed: false
      };
    }
    
    // Check token expiry
    const now = new Date();
    const expiryDate = new Date(expiresAt);
    
    // Calculate days until expiry
    let daysUntilExpiry = 0;
    if (expiryDate > now) {
      daysUntilExpiry = Math.floor((expiryDate - now) / (1000 * 60 * 60 * 24));
    }
    
    log(`Token status for ${companyName}:`);
    log(`  Current Date: ${formatDate(now)}`);
    log(`  Expiry Date: ${formatDate(expiryDate)}`);
    log(`  Days Until Expiry: ${daysUntilExpiry}`);
    
    // Determine if token needs refresh
    const needsRefresh = daysUntilExpiry <= daysBeforeExpiry || expiryDate <= now;
    
    if (!needsRefresh) {
      log(`Token for ${companyName} does not need refresh yet (expires in ${daysUntilExpiry} days)`);
      return {
        success: true,
        message: `Token valid for ${daysUntilExpiry} more days`,
        refreshed: false,
        daysUntilExpiry
      };
    }
    
    // Token needs refresh
    log(`Token for ${companyName} needs refresh (expires in ${daysUntilExpiry} days)`);
    
    try {
      // Initialize Xero client
      const xero = this.getXeroClient();
      
      // Initialize the client
      await xero.initialize();
      
      // Set the token set with the correct format
      xero.setTokenSet({
        id_token: integration.credentials.get('idToken') || '',
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: 'Bearer',
        scope: 'offline_access openid profile email accounting.transactions accounting.journals accounting.settings accounting.contacts',
        expires_at: Math.floor(new Date(expiresAt).getTime() / 1000)
      });
      
      // Refresh the token
      log(`Refreshing token for ${companyName}...`);
      const newTokenSet = await xero.refreshToken();
      
      // Update integration with new tokens
      integration.credentials.set('accessToken', newTokenSet.access_token);
      integration.credentials.set('refreshToken', newTokenSet.refresh_token);
      integration.credentials.set('expiresAt', new Date(
        Date.now() + newTokenSet.expires_in * 1000
      ).toString());
      
      await integration.save();
      
      const newExpiryDate = new Date(Date.now() + newTokenSet.expires_in * 1000);
      const newDaysUntilExpiry = Math.floor((newExpiryDate - now) / (1000 * 60 * 60 * 24));
      
      log(`✓ Token refreshed successfully for ${companyName}`);
      log(`  New Access Token: ${newTokenSet.access_token.substring(0, 10)}...`);
      log(`  New Refresh Token: ${newTokenSet.refresh_token.substring(0, 10)}...`);
      log(`  New Expiry: ${formatDate(newExpiryDate)} (${newDaysUntilExpiry} days)`);
      
      return {
        success: true,
        message: 'Token refreshed successfully',
        refreshed: true,
        oldExpiryDate: expiryDate,
        newExpiryDate,
        newDaysUntilExpiry
      };
      
    } catch (error) {
      log(`✗ Token refresh failed for ${companyName}: ${error.message}`, 'ERROR');
      
      let errorDetails = {
        message: error.message,
        needsReauthentication: false
      };
      
      if (error.message && error.message.includes('invalid_grant')) {
        log('⚠ Invalid grant error detected. This typically means:', 'WARNING');
        log('  1. The refresh token has expired (after 60 days of inactivity)');
        log('  2. The refresh token has been revoked by Xero');
        log('  3. The integration credentials are invalid or corrupted');
        log('\nRecommendation: Re-authenticate with Xero using reset-xero-integration.js');
        
        errorDetails.needsReauthentication = true;
      }
      
      return {
        success: false,
        message: `Token refresh failed: ${error.message}`,
        refreshed: false,
        error: errorDetails
      };
    }
  }

  async refreshAllTokens(daysBeforeExpiry = 7) {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    log('=== STARTING AUTOMATIC TOKEN REFRESH FOR ALL INTEGRATIONS ===');
    
    try {
      // Find all Xero integrations
      const integrations = await Integration.find({ 
        provider: 'xero',
        status: 'active'
      }).populate('company', 'name companyCode');
      
      if (integrations.length === 0) {
        log('No active Xero integrations found', 'WARNING');
        return {
          success: true,
          message: 'No active integrations found',
          results: []
        };
      }
      
      log(`Found ${integrations.length} active Xero integrations`);
      
      // Process each integration
      const results = [];
      
      for (const integration of integrations) {
        const companyName = integration.company ? integration.company.name : 'Unknown';
        const companyCode = integration.company ? integration.company.companyCode : 'Unknown';
        
        log(`Processing integration for ${companyName} (${companyCode})...`);
        
        const result = await this.refreshTokenIfNeeded(integration, daysBeforeExpiry);
        
        results.push({
          company: {
            name: companyName,
            code: companyCode,
            id: integration.company ? integration.company._id : null
          },
          integrationId: integration._id,
          result
        });
        
        log(`Completed processing for ${companyName}\n`);
      }
      
      log('=== AUTOMATIC TOKEN REFRESH COMPLETED ===');
      
      return {
        success: true,
        message: 'Token refresh process completed',
        results
      };
      
    } catch (error) {
      log(`Automatic token refresh failed: ${error}`, 'ERROR');
      console.error(error);
      
      return {
        success: false,
        message: `Automatic token refresh failed: ${error.message}`,
        error
      };
    }
  }
}

module.exports = new XeroTokenRefreshService(); 