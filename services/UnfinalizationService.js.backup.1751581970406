const PayrollPeriod = require("../models/PayrollPeriod");
const PayRun = require("../models/payRun");
const Employee = require("../models/Employee");
const moment = require("moment-timezone");

/**
 * UnfinalizationService - Handles all unfinalization validation and logic
 * This service works alongside PayrollService without modifying existing functionality
 */
class UnfinalizationService {
  /**
   * Comprehensive validation for period unfinalization
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Object} Validation result with isValid, errors, warnings
   */
  static async validateUnfinalization(employeeId, company, periodEndDate) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // 1. Find the period to unfinalize
      const period = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: new Date(periodEndDate),
      });

      if (!period) {
        validationResult.errors.push("Period not found");
        validationResult.isValid = false;
        return validationResult;
      }

      // 2. Check if period is already unfinalized
      if (!period.isFinalized) {
        validationResult.errors.push("This period is already unfinalized and can be edited");
        validationResult.isValid = false;
        return validationResult;
      }

      // 3. Check if period is locked by pay run
      if (period.status === "locked") {
        validationResult.errors.push(
          "This period is locked by an active pay run. You must delete the pay run before unfinalizing the period."
        );
        validationResult.isValid = false;
        return validationResult;
      }

      // 4. Check for newer finalized periods (sequential constraint)
      // Find periods that end AFTER the current period (these are truly "newer")
      // Use the actual period endDate instead of the parsed periodEndDate to avoid timezone issues
      const targetEndDate = period.endDate;

      const newerFinalized = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        _id: { $ne: period._id }, // Exclude the current period itself
        endDate: { $gt: targetEndDate },
        isFinalized: true,
      });

      if (newerFinalized) {
        validationResult.errors.push(
          `Cannot unfinalize this period because a newer period (ending ${moment(newerFinalized.endDate).format('DD/MM/YYYY')}) is already finalized. Please unfinalize newer periods first to maintain chronological order.`
        );
        validationResult.isValid = false;
      }

      // 5. Check for associated pay runs
      const associatedPayRun = await PayRun.findOne({
        payrollPeriods: period._id,
        company: company,
        status: { $nin: ["deleted", "cancelled"] },
      });

      if (associatedPayRun) {
        validationResult.errors.push(
          `Cannot unfinalize this period because it's associated with pay run "${associatedPayRun.reference}". Please delete the pay run first, then try unfinalizing the period.`
        );
        validationResult.isValid = false;
      }

      // 6. Employee status validation
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        validationResult.errors.push("Employee not found");
        validationResult.isValid = false;
        return validationResult;
      }

      if (employee.status === "Terminated") {
        validationResult.warnings.push(
          "This employee is terminated. Please ensure unfinalization is appropriate for payroll adjustments."
        );
      }

      // 7. Check for termination date constraints
      if (employee.lastDayOfService) {
        const terminationDate = moment(employee.lastDayOfService).endOf("day");
        const periodEnd = moment(periodEndDate).endOf("day");

        if (periodEnd.isAfter(terminationDate)) {
          validationResult.warnings.push(
            `This period ends after the employee's termination date (${moment(employee.lastDayOfService).format('DD/MM/YYYY')}). Please verify this is intentional.`
          );
        }
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating unfinalization:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Validation error occurred");
      return validationResult;
    }
  }

  /**
   * Check if a period can be unfinalized (quick check)
   * @param {string} periodId - PayrollPeriod ID
   * @returns {Object} Result with canUnfinalize boolean and reason
   */
  static async canUnfinalize(periodId) {
    try {
      const period = await PayrollPeriod.findById(periodId);
      if (!period) {
        return { canUnfinalize: false, reason: "Period not found" };
      }

      if (!period.isFinalized) {
        return { canUnfinalize: false, reason: "Period is not finalized" };
      }

      if (period.status === "locked") {
        return { canUnfinalize: false, reason: "Period is locked by pay run" };
      }

      const validation = await this.validateUnfinalization(
        period.employee,
        period.company,
        period.endDate
      );

      return {
        canUnfinalize: validation.isValid,
        reason: validation.errors.join("; "),
        warnings: validation.warnings,
      };
    } catch (error) {
      console.error("Error checking unfinalization eligibility:", error);
      return { canUnfinalize: false, reason: "Error checking eligibility" };
    }
  }

  /**
   * Get all periods that would be affected by unfinalization
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Array} Array of affected periods
   */
  static async getAffectedPeriods(employeeId, company, periodEndDate) {
    try {
      // Find all periods after the target period
      const affectedPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company,
        endDate: { $gt: new Date(periodEndDate) },
      }).sort({ endDate: 1 });

      return affectedPeriods;
    } catch (error) {
      console.error("Error getting affected periods:", error);
      return [];
    }
  }

  /**
   * Perform the actual unfinalization with all safety checks
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @param {string} userId - User performing the action
   * @returns {Object} Result with success boolean and message
   */
  static async unfinalizePeriod(employeeId, company, periodEndDate, userId) {
    try {
      // Validate before proceeding
      const validation = await this.validateUnfinalization(employeeId, company, periodEndDate);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join("; "),
          errors: validation.errors,
        };
      }

      // Find and update the period
      const period = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: new Date(periodEndDate),
      });

      if (!period) {
        return {
          success: false,
          message: "Period not found",
        };
      }

      // Update period status
      period.isFinalized = false;
      period.status = "open";
      period.unfinalizedAt = new Date();
      period.unfinalizedBy = userId;

      await period.save();

      return {
        success: true,
        message: "Period unfinalized successfully",
        period: period,
        warnings: validation.warnings,
      };
    } catch (error) {
      console.error("Error unfinalizing period:", error);
      return {
        success: false,
        message: "Failed to unfinalize period",
        error: error.message,
      };
    }
  }
}

module.exports = UnfinalizationService;
