const User = require("../models/user");

class NotificationService {
  constructor() {
    this.notificationTypes = {
      SECURITY_ALERT: "security_alert",
      SESSION_WARNING: "session_warning",
      DEVICE_CHANGE: "device_change",
      LOGIN_ALERT: "login_alert",
      LOGOUT_ALERT: "logout_alert",
    };
  }

  /**
   * Send security notification to user
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {String} message - Notification message
   * @param {Object} data - Additional notification data
   * @returns {Object} Notification result
   */
  async sendSecurityNotification(userId, type, message, data = {}) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        return {
          success: false,
          reason: "User not found",
        };
      }

      // Check user preferences
      const preferences = user.securityPreferences || {};
      
      if (!this.shouldSendNotification(type, preferences)) {
        console.log(`Notification skipped for user ${userId} due to preferences: ${type}`);
        return {
          success: true,
          skipped: true,
          reason: "User preferences",
        };
      }

      // Log the notification (in a real implementation, this would send email/SMS/push notification)
      console.log(`Security notification for user ${userId}:`, {
        type,
        message,
        data,
        timestamp: new Date().toISOString(),
      });

      // Store notification in user's notification history (if you have a notifications model)
      // await this.storeNotification(userId, type, message, data);

      return {
        success: true,
        message: "Notification sent successfully",
      };
    } catch (error) {
      console.error("Error sending security notification:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send session expiry warning
   * @param {String} userId - User ID
   * @param {Number} minutesRemaining - Minutes until session expires
   * @returns {Object} Notification result
   */
  async sendSessionExpiryWarning(userId, minutesRemaining) {
    const message = `Your session will expire in ${minutesRemaining} minutes. Please save your work.`;
    
    return this.sendSecurityNotification(
      userId,
      this.notificationTypes.SESSION_WARNING,
      message,
      { minutesRemaining }
    );
  }

  /**
   * Send new device login alert
   * @param {String} userId - User ID
   * @param {Object} deviceInfo - Device information
   * @returns {Object} Notification result
   */
  async sendNewDeviceAlert(userId, deviceInfo) {
    const message = `New device login detected: ${deviceInfo.name} from ${deviceInfo.location}`;
    
    return this.sendSecurityNotification(
      userId,
      this.notificationTypes.DEVICE_CHANGE,
      message,
      deviceInfo
    );
  }

  /**
   * Send suspicious activity alert
   * @param {String} userId - User ID
   * @param {String} activity - Description of suspicious activity
   * @param {Object} details - Activity details
   * @returns {Object} Notification result
   */
  async sendSuspiciousActivityAlert(userId, activity, details = {}) {
    const message = `Suspicious activity detected: ${activity}`;
    
    return this.sendSecurityNotification(
      userId,
      this.notificationTypes.SECURITY_ALERT,
      message,
      { activity, ...details }
    );
  }

  /**
   * Send logout notification
   * @param {String} userId - User ID
   * @param {String} reason - Logout reason
   * @param {Object} details - Logout details
   * @returns {Object} Notification result
   */
  async sendLogoutNotification(userId, reason, details = {}) {
    const message = `You have been logged out: ${reason}`;
    
    return this.sendSecurityNotification(
      userId,
      this.notificationTypes.LOGOUT_ALERT,
      message,
      { reason, ...details }
    );
  }

  /**
   * Check if notification should be sent based on user preferences
   * @param {String} type - Notification type
   * @param {Object} preferences - User security preferences
   * @returns {Boolean} Whether to send notification
   */
  shouldSendNotification(type, preferences) {
    switch (type) {
      case this.notificationTypes.SECURITY_ALERT:
        return preferences.notifyOnSuspiciousActivity !== false;
      case this.notificationTypes.SESSION_WARNING:
        return preferences.notifyOnSessionExpiry !== false;
      case this.notificationTypes.DEVICE_CHANGE:
        return preferences.notifyOnNewDevice !== false;
      case this.notificationTypes.LOGIN_ALERT:
        return preferences.notifyOnNewDevice !== false;
      case this.notificationTypes.LOGOUT_ALERT:
        return true; // Always send logout notifications for security
      default:
        return true;
    }
  }

  /**
   * Store notification in database (placeholder for future implementation)
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {String} message - Notification message
   * @param {Object} data - Additional data
   */
  async storeNotification(userId, type, message, data) {
    // Placeholder for storing notifications in database
    // This would create a notification record for user's notification history
    console.log(`Storing notification for user ${userId}:`, {
      type,
      message,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * Send email notification (placeholder for future implementation)
   * @param {String} email - User email
   * @param {String} subject - Email subject
   * @param {String} message - Email message
   * @param {Object} data - Additional data
   */
  async sendEmailNotification(email, subject, message, data = {}) {
    // Placeholder for email notification
    console.log(`Email notification to ${email}:`, {
      subject,
      message,
      data,
    });
  }

  /**
   * Send SMS notification (placeholder for future implementation)
   * @param {String} phoneNumber - User phone number
   * @param {String} message - SMS message
   */
  async sendSMSNotification(phoneNumber, message) {
    // Placeholder for SMS notification
    console.log(`SMS notification to ${phoneNumber}: ${message}`);
  }

  /**
   * Send push notification (placeholder for future implementation)
   * @param {String} userId - User ID
   * @param {String} title - Notification title
   * @param {String} message - Notification message
   * @param {Object} data - Additional data
   */
  async sendPushNotification(userId, title, message, data = {}) {
    // Placeholder for push notification
    console.log(`Push notification for user ${userId}:`, {
      title,
      message,
      data,
    });
  }
}

module.exports = new NotificationService();
