const Employee = require('../models/employee');
const { calculateOIDEarnings, checkOIDEligibility } = require('../utils/oidCalculator');
const fs = require('fs').promises;
const path = require('path');

class OIDReturnService {
    /**
     * Generate OID return data for a specific period
     * @param {string} companyId - Company ID
     * @param {Date} startDate - Period start date
     * @param {Date} endDate - Period end date
     * @returns {Object} OID return data
     */
    async generateOIDReturn(companyId, startDate, endDate) {
        try {
            // Get all employees for the company
            const employees = await Employee.find({ 
                companyId,
                status: 'ACTIVE'
            });

            const returnData = {
                periodStart: startDate,
                periodEnd: endDate,
                totalEmployees: employees.length,
                eligibleEmployees: 0,
                totalEarnings: 0,
                employeeDetails: []
            };

            // Process each employee
            for (const employee of employees) {
                const eligibility = checkOIDEligibility(employee);
                
                if (!eligibility.eligible) {
                    continue;
                }

                returnData.eligibleEmployees++;

                // Get payroll items for the period
                const payrollItems = await this.getPayrollItems(
                    employee._id,
                    startDate,
                    endDate
                );

                // Calculate OID earnings
                const oidEarnings = calculateOIDEarnings(payrollItems);
                returnData.totalEarnings += oidEarnings.amount;

                // Add employee details
                returnData.employeeDetails.push({
                    employeeId: employee._id,
                    firstName: employee.firstName,
                    lastName: employee.lastName,
                    idNumber: employee.idNumber,
                    earnings: oidEarnings
                });
            }

            return returnData;
        } catch (error) {
            console.error('Error generating OID return:', error);
            throw error;
        }
    }

    /**
     * Get payroll items for an employee within a period
     * @param {string} employeeId - Employee ID
     * @param {Date} startDate - Period start date
     * @param {Date} endDate - Period end date
     * @returns {Array} Payroll items
     */
    async getPayrollItems(employeeId, startDate, endDate) {
        // TODO: Implement payroll items retrieval
        // This should be replaced with actual payroll data retrieval
        return [];
    }

    /**
     * Save OID return draft
     * @param {string} companyId - Company ID
     * @param {Object} returnData - Return data to save
     */
    async saveDraft(companyId, returnData) {
        try {
            // Create drafts directory if it doesn't exist
            const draftsDir = path.join(__dirname, '..', 'data', 'oid-drafts');
            await fs.mkdir(draftsDir, { recursive: true });

            // Save draft file
            const draftPath = path.join(draftsDir, `${companyId}_${returnData.periodStart.getFullYear()}.json`);
            await fs.writeFile(draftPath, JSON.stringify(returnData, null, 2));

            return true;
        } catch (error) {
            console.error('Error saving draft:', error);
            throw error;
        }
    }

    /**
     * Get OID return draft
     * @param {string} companyId - Company ID
     * @param {number} year - Year to get draft for
     * @returns {Object} Draft data
     */
    async getDraft(companyId, year) {
        try {
            const draftPath = path.join(__dirname, '..', 'data', 'oid-drafts', `${companyId}_${year}.json`);
            
            try {
                const draftContent = await fs.readFile(draftPath, 'utf8');
                return JSON.parse(draftContent);
            } catch (err) {
                if (err.code === 'ENOENT') {
                    return null;
                }
                throw err;
            }
        } catch (error) {
            console.error('Error getting draft:', error);
            throw error;
        }
    }

    /**
     * Generate downloadable file
     * @param {string} companyId - Company ID
     * @param {Object} returnData - Return data to generate file from
     * @returns {string} Path to generated file
     */
    async generateDownloadFile(companyId, returnData) {
        try {
            // Create downloads directory if it doesn't exist
            const downloadsDir = path.join(__dirname, '..', 'data', 'oid-downloads');
            await fs.mkdir(downloadsDir, { recursive: true });

            // Ensure dates are Date objects
            const periodStart = new Date(returnData.periodStart);
            const periodEnd = new Date(returnData.periodEnd);

            // Generate file name with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `OID_Return_${companyId}_${periodStart.getFullYear()}_${timestamp}.csv`;
            const filePath = path.join(downloadsDir, fileName);

            // Convert dates in return data
            const processedData = {
                ...returnData,
                periodStart,
                periodEnd
            };

            // Generate CSV content
            const csvContent = this.generateCSV(processedData);
            await fs.writeFile(filePath, csvContent);

            return filePath;
        } catch (error) {
            console.error('Error generating download file:', error);
            throw error;
        }
    }

    /**
     * Generate CSV content from return data
     * @param {Object} returnData - Return data to generate CSV from
     * @returns {string} CSV content
     */
    generateCSV(returnData) {
        const headers = [
            'Employee ID',
            'First Name',
            'Last Name',
            'ID Number',
            'Earnings Amount'
        ].join(',');

        const rows = returnData.employeeDetails.map(employee => [
            employee.employeeId,
            employee.firstName,
            employee.lastName,
            employee.idNumber,
            employee.earnings.amount
        ].join(','));

        return [headers, ...rows].join('\n');
    }

    /**
     * Validate OID return data
     * @param {Object} returnData - OID return data
     * @returns {Object} Validation result
     */
    validateReturn(returnData) {
        const errors = [];

        // Basic validation checks
        if (!returnData.periodStart || !returnData.periodEnd) {
            errors.push('Missing period dates');
        }

        if (!Array.isArray(returnData.employeeDetails)) {
            errors.push('Invalid employee details format');
        }

        // Validate employee details
        returnData.employeeDetails?.forEach((employee, index) => {
            if (!employee.idNumber) {
                errors.push(`Missing ID number for employee at index ${index}`);
            }
            if (!employee.earnings || typeof employee.earnings.amount !== 'number') {
                errors.push(`Invalid earnings for employee at index ${index}`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

module.exports = new OIDReturnService();
