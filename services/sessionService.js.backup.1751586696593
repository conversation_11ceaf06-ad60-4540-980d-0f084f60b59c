const Redis = require('ioredis');
const { promisify } = require('util');

class SessionService {
  constructor() {
    if (process.env.REDIS_URL) {
      const Redis = require('ioredis');
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        keyPrefix: 'whatsapp:session:'
      });
      this.useRedis = true;
    } else {
      this.memoryStore = new Map();
      this.useRedis = false;
    }
  }

  async setSession(phoneNumber, employeeData, expiryHours = 24) {
    const key = this.normalizePhoneNumber(phoneNumber);
    const value = JSON.stringify(employeeData);
    if (this.useRedis) {
      try {
        await this.redis.set(key, value, 'EX', expiryHours * 3600);
        return true;
      } catch (error) {
        console.error('Error setting session:', error);
        return false;
      }
    } else {
      this.memoryStore.set(key, { value, expires: Date.now() + expiryHours * 3600 * 1000 });
      setTimeout(() => this.memoryStore.delete(key), expiryHours * 3600 * 1000);
      return true;
    }
  }

  async getSession(phoneNumber) {
    const key = this.normalizePhoneNumber(phoneNumber);
    if (this.useRedis) {
      try {
        const value = await this.redis.get(key);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        console.error('Error getting session:', error);
        return null;
      }
    } else {
      const entry = this.memoryStore.get(key);
      if (!entry) return null;
      if (Date.now() > entry.expires) {
        this.memoryStore.delete(key);
        return null;
      }
      return JSON.parse(entry.value);
    }
  }

  async removeSession(phoneNumber) {
    const key = this.normalizePhoneNumber(phoneNumber);
    if (this.useRedis) {
      try {
        await this.redis.del(key);
        return true;
      } catch (error) {
        console.error('Error removing session:', error);
        return false;
      }
    } else {
      this.memoryStore.delete(key);
      return true;
    }
  }

  async refreshSession(phoneNumber, expiryHours = 24) {
    const key = this.normalizePhoneNumber(phoneNumber);
    if (this.useRedis) {
      try {
        const exists = await this.redis.exists(key);
        if (exists) {
          await this.redis.expire(key, expiryHours * 3600);
          return true;
        }
        return false;
      } catch (error) {
        console.error('Error refreshing session:', error);
        return false;
      }
    } else {
      const entry = this.memoryStore.get(key);
      if (!entry) return false;
      entry.expires = Date.now() + expiryHours * 3600 * 1000;
      return true;
    }
  }

  async cleanup() {
    if (this.useRedis) {
      try {
        await this.redis.quit();
      } catch (error) {
        console.error('Error closing Redis connection:', error);
      }
    } else {
      this.memoryStore.clear();
    }
  }

  normalizePhoneNumber(phoneNumber) {
    return String(phoneNumber).replace(/[\s+]/g, '');
  }
}

module.exports = new SessionService(); 