const moment = require("moment-timezone");

class PayPeriodService {
  static generatePeriods(frequency, startDate, endDate) {
    switch (frequency) {
      case "weekly":
        return this.generateWeeklyPeriods(startDate, endDate);
      case "bi-weekly":
        return this.generateBiWeeklyPeriods(startDate, endDate);
      case "monthly":
        return this.generateMonthlyPeriods(startDate, endDate);
    }
  }

  static generateWeeklyPeriods(startDate, endDate) {
    // Generate weekly periods
  }

  static generateBiWeeklyPeriods(startDate, endDate) {
    // Generate bi-weekly periods
  }

  static generateMonthlyPeriods(startDate, endDate) {
    // Generate monthly periods
  }
}

module.exports = PayPeriodService;
