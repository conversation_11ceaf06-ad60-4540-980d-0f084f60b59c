const Payroll = require('../models/Payroll');
const PayrollPeriod = require('../models/PayrollPeriod');
const moment = require('moment');

class IRP5Service {
  /**
   * Aggregates payroll data for an employee for a specific tax year
   * @param {string} employeeId - The employee ID
   * @param {string} companyId - The company ID
   * @param {string} taxYear - The tax year (e.g., "2025" for 2024/2025 tax year)
   * @param {boolean} includeNonFinalized - Whether to include non-finalized periods (for testing)
   * @returns {Promise<object>} - Aggregated payroll data
   */
  static async getEmployeePayrollDataForTaxYear(employeeId, companyId, taxYear, includeNonFinalized = false) {
    try {
      console.log('\n=== IRP5Service: Fetching Payroll Data ===');
      console.log('Input parameters:', {
        employeeId,
        companyId,
        taxYear,
        includeNonFinalized
      });

      // Calculate tax year date range (March 1 to February 28)
      const taxYearStart = moment(`${parseInt(taxYear) - 1}-03-01`).startOf('day');
      const taxYearEnd = moment(`${taxYear}-02-28`).endOf('day');

      console.log('Tax year range:', taxYearStart.format('YYYY-MM-DD'), 'to', taxYearEnd.format('YYYY-MM-DD'));

      // Find all payroll periods within the tax year for this specific employee
      const query = {
        employee: employeeId,
        company: companyId,
        startDate: { $gte: taxYearStart.toDate() },
        endDate: { $lte: taxYearEnd.toDate() }
      };

      // Only filter by finalized status if not including non-finalized periods
      if (!includeNonFinalized) {
        query.status = 'finalized';
      }

      console.log('📋 Query criteria:', query);
      console.log('🔧 Include non-finalized periods:', includeNonFinalized);

      const payrollPeriods = await PayrollPeriod.find(query).sort({ startDate: 1 });

      console.log('📊 Found payroll periods:', payrollPeriods.length);
      console.log('📋 Period details:', payrollPeriods.map(p => ({
        id: p._id,
        startDate: p.startDate,
        endDate: p.endDate,
        finalised: p.finalised,
        status: p.status
      })));

      if (payrollPeriods.length === 0) {
        console.log('❌ No finalized payroll periods found for tax year');
        const emptyResult = {
          grossPay: 0,
          taxableIncome: 0,
          totalPAYE: 0,
          totalUIF: 0,
          totalSDL: 0,
          pensionContributions: 0,
          medicalAidContributions: 0,
          retirementAnnuity: 0,
          etiAmount: 0,
          periods: []
        };
        console.log('📤 Returning empty result:', emptyResult);
        return emptyResult;
      }

      // Get payroll records for the employee across all periods
      console.log('🔍 Searching for payroll records...');

      // Primary query: Try to find payroll records linked to payroll periods
      const payrollQuery = {
        employee: employeeId,
        company: companyId,
        payrollPeriod: { $in: payrollPeriods.map(p => p._id) }
      };
      console.log('📋 Primary payroll query (with payrollPeriod references):', payrollQuery);

      let payrollRecords = await Payroll.find(payrollQuery).populate('payrollPeriod');
      console.log('📊 Found payroll records with payrollPeriod references:', payrollRecords.length);

      // Fallback query: If no records found with payrollPeriod references,
      // search by date range (many existing records don't have payrollPeriod set)
      if (payrollRecords.length === 0) {
        console.log('🔄 No records found with payrollPeriod references, trying date-based fallback...');

        const fallbackQuery = {
          employee: employeeId,
          company: companyId,
          month: {
            $gte: taxYearStart.toDate(),
            $lte: taxYearEnd.toDate()
          }
        };
        console.log('📋 Fallback payroll query (date-based):', fallbackQuery);

        payrollRecords = await Payroll.find(fallbackQuery);
        console.log('📊 Found payroll records with date-based query:', payrollRecords.length);

        // For fallback records, manually match them to periods based on dates
        if (payrollRecords.length > 0) {
          console.log('🔗 Manually linking payroll records to periods based on dates...');
          payrollRecords = payrollRecords.map(payroll => {
            // Find matching period based on date overlap
            const matchingPeriod = payrollPeriods.find(period => {
              const payrollMonth = moment(payroll.month);
              const periodStart = moment(period.startDate);
              const periodEnd = moment(period.endDate);

              // Check if payroll month falls within or overlaps with period
              return payrollMonth.isBetween(periodStart, periodEnd, 'day', '[]') ||
                     payrollMonth.isSame(periodEnd, 'month');
            });

            if (matchingPeriod) {
              // Simulate populated payrollPeriod for consistency
              payroll.payrollPeriod = matchingPeriod;
              console.log(`🔗 Linked payroll ${payroll._id} to period ${matchingPeriod._id}`);
            }

            return payroll;
          });
        }
      }

      console.log('📋 Final payroll record details:', payrollRecords.map(p => ({
        id: p._id,
        employee: p.employee,
        month: p.month,
        periodId: p.payrollPeriod?._id,
        periodStart: p.payrollPeriod?.startDate,
        periodEnd: p.payrollPeriod?.endDate,
        grossPay: p.grossPay,
        totalPAYE: p.totalPAYE,
        basicSalary: p.basicSalary
      })));

      // Aggregate the data
      const aggregatedData = {
        grossPay: 0,
        taxableIncome: 0,
        totalPAYE: 0,
        totalUIF: 0,
        totalSDL: 0,
        pensionContributions: 0,
        medicalAidContributions: 0,
        retirementAnnuity: 0,
        etiAmount: 0,
        periods: []
      };

      payrollRecords.forEach((record, index) => {
        console.log(`📋 Processing payroll record ${index + 1}:`, {
          id: record._id,
          month: record.month,
          grossPay: record.grossPay,
          basicSalary: record.basicSalary,
          totalPAYE: record.totalPAYE,
          PAYE: record.PAYE
        });

        // Basic income (try multiple fields for backward compatibility)
        const grossPay = record.grossPay || record.basicSalary || 0;
        const taxableIncome = record.taxableIncome || grossPay;

        aggregatedData.grossPay += grossPay;
        aggregatedData.taxableIncome += taxableIncome;

        // Tax and statutory deductions (try multiple field names)
        const paye = record.totalPAYE || record.PAYE || 0;
        const uif = record.totalUIF || record.UIF || 0;
        const sdl = record.totalSDL || record.SDL || 0;

        aggregatedData.totalPAYE += paye;
        aggregatedData.totalUIF += uif;
        aggregatedData.totalSDL += sdl;

        // Retirement and medical contributions
        if (record.deductions) {
          // Look for pension/retirement fund contributions
          const pensionDeduction = record.deductions.find(d =>
            d.name && (
              d.name.toLowerCase().includes('pension') ||
              d.name.toLowerCase().includes('retirement') ||
              d.name.toLowerCase().includes('provident')
            )
          );
          if (pensionDeduction) {
            aggregatedData.pensionContributions += pensionDeduction.amount || 0;
          }

          // Look for medical aid contributions
          const medicalDeduction = record.deductions.find(d =>
            d.name && (
              d.name.toLowerCase().includes('medical') ||
              d.name.toLowerCase().includes('health')
            )
          );
          if (medicalDeduction) {
            aggregatedData.medicalAidContributions += medicalDeduction.amount || 0;
          }

          // Look for retirement annuity contributions
          const raDeduction = record.deductions.find(d =>
            d.name && (
              d.name.toLowerCase().includes('retirement annuity') ||
              d.name.toLowerCase().includes('ra ')
            )
          );
          if (raDeduction) {
            aggregatedData.retirementAnnuity += raDeduction.amount || 0;
          }
        }

        // ETI (Employment Tax Incentive) if applicable
        if (record.eti) {
          aggregatedData.etiAmount += record.eti || 0;
        }

        // Store period information for reference (handle both linked and unlinked scenarios)
        const periodInfo = {
          payrollId: record._id,
          periodId: record.payrollPeriod?._id,
          startDate: record.payrollPeriod?.startDate,
          endDate: record.payrollPeriod?.endDate,
          month: record.month, // Fallback for unlinked records
          grossPay: grossPay,
          paye: paye,
          uif: uif,
          sdl: sdl
        };

        aggregatedData.periods.push(periodInfo);
        console.log(`✅ Added period data:`, periodInfo);
      });

      console.log('Aggregated payroll data:', {
        grossPay: aggregatedData.grossPay,
        totalPAYE: aggregatedData.totalPAYE,
        periods: aggregatedData.periods.length
      });

      console.log('✅ Final aggregated data summary:', {
        grossPay: aggregatedData.grossPay,
        taxableIncome: aggregatedData.taxableIncome,
        totalPAYE: aggregatedData.totalPAYE,
        totalUIF: aggregatedData.totalUIF,
        totalSDL: aggregatedData.totalSDL,
        pensionContributions: aggregatedData.pensionContributions,
        medicalAidContributions: aggregatedData.medicalAidContributions,
        retirementAnnuity: aggregatedData.retirementAnnuity,
        etiAmount: aggregatedData.etiAmount,
        periodsCount: aggregatedData.periods.length
      });
      console.log('=== IRP5Service: Payroll Data Fetch Complete ===\n');

      return aggregatedData;

    } catch (error) {
      console.error('❌ Error fetching payroll data for IRP5:', {
        error: error.message,
        stack: error.stack,
        employeeId,
        companyId,
        taxYear,
        includeNonFinalized
      });
      throw error;
    }
  }

  /**
   * Gets available tax years for an employee based on their payroll history
   * @param {string} employeeId - The employee ID
   * @param {string} companyId - The company ID
   * @returns {Promise<Array>} - Array of available tax years
   */
  static async getAvailableTaxYears(employeeId, companyId) {
    try {
      console.log('\n=== IRP5Service: Getting Available Tax Years ===');
      console.log('Input parameters:', { employeeId, companyId });

      // Primary approach: Find payroll records with payrollPeriod references
      let payrollRecords = await Payroll.find({
        employee: employeeId,
        company: companyId,
        payrollPeriod: { $exists: true, $ne: null }
      }).populate('payrollPeriod').sort({ 'payrollPeriod.startDate': 1 });

      console.log('📊 Found payroll records with payrollPeriod references:', payrollRecords.length);

      // Fallback approach: If no records with payrollPeriod references, use date-based approach
      if (payrollRecords.length === 0) {
        console.log('🔄 No records with payrollPeriod references, trying date-based fallback...');

        // Get all payroll records for this employee and company
        const allPayrollRecords = await Payroll.find({
          employee: employeeId,
          company: companyId
        }).sort({ month: 1 });

        console.log('📊 Found payroll records with date-based query:', allPayrollRecords.length);

        if (allPayrollRecords.length === 0) {
          console.log('❌ No payroll records found');
          return [];
        }

        // For fallback, we'll determine tax years based on payroll months
        // and create a synthetic structure similar to the populated approach
        payrollRecords = allPayrollRecords.map(record => ({
          ...record.toObject(),
          payrollPeriod: {
            startDate: moment(record.month).startOf('month').toDate(),
            endDate: moment(record.month).endOf('month').toDate()
          }
        }));

        console.log('🔗 Created synthetic payrollPeriod data for fallback records');
      }

      if (payrollRecords.length === 0) {
        console.log('❌ No payroll records found after both approaches');
        return [];
      }

      const earliestDate = moment(payrollRecords[0].payrollPeriod.startDate);
      const latestDate = moment(payrollRecords[payrollRecords.length - 1].payrollPeriod.endDate);

      console.log('📅 Payroll date range:', {
        earliest: earliestDate.format('YYYY-MM-DD'),
        latest: latestDate.format('YYYY-MM-DD')
      });

      // Calculate tax years
      const taxYears = [];
      let currentTaxYearEnd = moment().month(1).date(28); // February 28 of current year

      // Go back to find the first tax year that includes the earliest payroll
      while (currentTaxYearEnd.isAfter(earliestDate)) {
        const taxYearStart = moment(currentTaxYearEnd).subtract(1, 'year').month(2).date(1); // March 1 of previous year

        if (taxYearStart.isSameOrBefore(latestDate)) {
          const taxYear = {
            year: currentTaxYearEnd.year().toString(),
            label: `${currentTaxYearEnd.year() - 1}/${currentTaxYearEnd.year()}`,
            startDate: taxYearStart.format('YYYY-MM-DD'),
            endDate: currentTaxYearEnd.format('YYYY-MM-DD')
          };

          taxYears.unshift(taxYear);
          console.log('📅 Added tax year:', taxYear);
        }

        currentTaxYearEnd.subtract(1, 'year');
      }

      console.log(`✅ Found ${taxYears.length} available tax years`);
      console.log('=== IRP5Service: Available Tax Years Complete ===\n');

      return taxYears;

    } catch (error) {
      console.error('Error getting available tax years:', error);
      return [];
    }
  }

  /**
   * Checks if IRP5 data is available for an employee for a specific tax year
   * @param {string} employeeId - The employee ID
   * @param {string} companyId - The company ID
   * @param {string} taxYear - The tax year (e.g., "2025" for 2024/2025 tax year)
   * @returns {Promise<boolean>} - True if IRP5 data is available
   */
  static async isIRP5Available(employeeId, companyId, taxYear) {
    try {
      console.log('\n=== IRP5Service: Checking IRP5 Availability ===');
      console.log('Input parameters:', { employeeId, companyId, taxYear });

      // Calculate tax year date range (March 1 to February 28)
      const taxYearStart = moment(`${parseInt(taxYear) - 1}-03-01`).startOf('day');
      const taxYearEnd = moment(`${taxYear}-02-28`).endOf('day');

      console.log('Tax year range:', taxYearStart.format('YYYY-MM-DD'), 'to', taxYearEnd.format('YYYY-MM-DD'));

      // First, check for payroll periods in the tax year
      const payrollPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: companyId,
        startDate: { $gte: taxYearStart.toDate() },
        endDate: { $lte: taxYearEnd.toDate() }
      });

      console.log('📊 Found payroll periods:', payrollPeriods.length);

      if (payrollPeriods.length === 0) {
        console.log('❌ No payroll periods found for tax year');
        return false;
      }

      // Check for payroll records using the same fallback strategy as getEmployeePayrollDataForTaxYear
      // Primary query: Try to find payroll records linked to payroll periods
      let payrollRecords = await Payroll.find({
        employee: employeeId,
        company: companyId,
        payrollPeriod: { $in: payrollPeriods.map(p => p._id) }
      });

      console.log('📊 Found payroll records with payrollPeriod references:', payrollRecords.length);

      // Fallback query: If no records found with payrollPeriod references,
      // search by date range (many existing records don't have payrollPeriod set)
      if (payrollRecords.length === 0) {
        console.log('🔄 No records found with payrollPeriod references, trying date-based fallback...');

        payrollRecords = await Payroll.find({
          employee: employeeId,
          company: companyId,
          month: {
            $gte: taxYearStart.toDate(),
            $lte: taxYearEnd.toDate()
          }
        });

        console.log('📊 Found payroll records with date-based query:', payrollRecords.length);
      }

      const isAvailable = payrollRecords.length > 0;
      console.log(`✅ IRP5 availability result: ${isAvailable}`);
      console.log('=== IRP5Service: Availability Check Complete ===\n');

      return isAvailable;

    } catch (error) {
      console.error('❌ Error checking IRP5 availability:', {
        error: error.message,
        stack: error.stack,
        employeeId,
        companyId,
        taxYear
      });
      return false;
    }
  }

  /**
   * Gets the current tax year
   * @returns {string} - Current tax year (e.g., "2025" for 2024/2025)
   */
  static getCurrentTaxYear() {
    const now = moment();
    // Tax year runs from March 1 to February 28
    // If we're in March or later, it's the next calendar year's tax year
    // If we're in January or February, it's the current calendar year's tax year
    if (now.month() >= 2) { // March (month 2) or later
      return (now.year() + 1).toString();
    } else {
      return now.year().toString();
    }
  }
}

module.exports = IRP5Service;