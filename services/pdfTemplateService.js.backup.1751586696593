const PDFDocument = require("pdfkit");
const moment = require("moment");

class PDFTemplateService {
  static async createEmployeeListTemplate(doc, company, employees, settings) {
    // Header
    doc
      .font("Helvetica-Bold")
      .fontSize(20)
      .text("Employee List Report", { align: "center" })
      .moveDown(0.5);

    // Company Info & Report Details
    doc
      .fontSize(10)
      .font("Helvetica")
      .text(`Company: ${company.name}`)
      .text(`Generated on: ${moment().format("MMMM D, YYYY, h:mm A")}`)
      .text(`Total Employees: ${employees.length}`)
      .moveDown(1);

    // Table Header
    const startX = 50;
    let currentY = doc.y;
    const rowHeight = 20;
    const columns = this.getColumns(settings);

    // Draw header background
    doc
      .rect(startX - 5, currentY - 5, doc.page.width - 100, rowHeight)
      .fill("#f3f4f6");

    // Draw header text
    doc.font("Helvetica-Bold").fontSize(8);
    columns.forEach((column) => {
      doc.text(column.header, column.x, currentY, {
        width: column.width,
        align: column.align || "left",
      });
    });

    // Table Rows
    currentY += rowHeight;
    doc.font("Helvetica").fontSize(8);

    employees.forEach((employee, index) => {
      // Add new page if needed
      if (currentY > doc.page.height - 50) {
        doc.addPage();
        currentY = 50;
      }

      // Alternate row background
      if (index % 2 === 0) {
        doc
          .rect(startX - 5, currentY - 2, doc.page.width - 100, rowHeight)
          .fill("#f8fafc");
      }

      // Draw row data
      columns.forEach((column) => {
        const value = this.getEmployeeValue(employee, column.field);
        doc.text(value, column.x, currentY, {
          width: column.width,
          align: column.align || "left",
        });
      });

      currentY += rowHeight;
    });

    // Footer
    doc
      .fontSize(8)
      .text(
        `Generated by ${company.name} HR System`,
        50,
        doc.page.height - 50,
        { align: "center" }
      );
  }

  static getColumns(settings) {
    const baseColumns = [
      {
        header: "Employee No.",
        field: "companyEmployeeNumber",
        x: 50,
        width: 80,
      },
      { header: "Name", field: "fullName", x: 130, width: 120 },
      { header: "Job Title", field: "jobTitle", x: 250, width: 100 },
      { header: "Department", field: "department", x: 350, width: 100 },
    ];

    if (settings.includePersonalInfo) {
      baseColumns.push(
        { header: "Email", field: "email", x: 450, width: 120 },
        { header: "Phone", field: "phone", x: 570, width: 100 }
      );
    }

    if (settings.includeBankDetails) {
      baseColumns.push(
        { header: "Bank", field: "bankDetails.bankName", x: 670, width: 80 },
        {
          header: "Account No.",
          field: "bankDetails.accountNumber",
          x: 750,
          width: 100,
        }
      );
    }

    return baseColumns;
  }

  static getEmployeeValue(employee, field) {
    if (field === "fullName") {
      return `${employee.firstName} ${employee.lastName}`;
    }

    if (field.includes(".")) {
      const [obj, prop] = field.split(".");
      return employee[obj] ? employee[obj][prop] || "" : "";
    }

    return employee[field] || "";
  }

  static addPageNumbers(doc) {
    const pages = doc.bufferedPageRange();
    for (let i = 0; i < pages.count; i++) {
      doc.switchToPage(i);
      doc
        .fontSize(8)
        .text(`Page ${i + 1} of ${pages.count}`, 50, doc.page.height - 50, {
          align: "right",
        });
    }
  }
}

module.exports = PDFTemplateService;
