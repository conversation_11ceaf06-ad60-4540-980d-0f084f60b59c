const paystack = require('../config/paystackConfig');
const Invoice = require('../models/invoice');
const Company = require('../models/Company');
const axios = require('axios');

class PaystackService {
  /**
   * Initialize a transaction and get an authorization URL
   * @param {Object} data - Payment data
   * @returns {Promise<Object>} - Paystack response with authorization URL
   */
  async initializeTransaction(data) {
    try {
      const response = await paystack.initializeTransaction({
        amount: data.amount * 100, // Convert to kobo/cents (Paystack uses the smallest currency unit)
        email: data.email,
        reference: data.reference,
        callback_url: data.callbackUrl,
        metadata: {
          invoice_id: data.invoiceId,
          company_id: data.companyId,
          custom_fields: [
            {
              display_name: "Invoice Number",
              variable_name: "invoice_number",
              value: data.invoiceNumber
            },
            {
              display_name: "Company Name",
              variable_name: "company_name",
              value: data.companyName
            }
          ]
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error initializing Paystack transaction:', error);
      throw error;
    }
  }

  /**
   * Verify a Paystack transaction
   * @param {string} reference - Transaction reference
   * @returns {Promise<Object>} - Verification result
   */
  async verifyTransaction(reference) {
    try {
      const response = await paystack.verifyTransaction({ reference });
      return response.data;
    } catch (error) {
      console.error('Error verifying Paystack transaction:', error);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   * @param {string} id - Transaction ID
   * @returns {Promise<Object>} - Transaction data
   */
  async getTransaction(id) {
    try {
      const response = await paystack.getTransaction({ id });
      return response.data;
    } catch (error) {
      console.error('Error getting Paystack transaction:', error);
      throw error;
    }
  }

  /**
   * Process payment for an invoice
   * @param {string} invoiceId - Invoice ID
   * @param {string} userId - User ID
   * @param {string} callbackUrl - URL to redirect after payment
   * @returns {Promise<string>} - Authorization URL
   */
  async processInvoicePayment(invoiceId, userId, callbackUrl) {
    try {
      // Get invoice details
      const invoice = await Invoice.findById(invoiceId).populate({
        path: 'company',
        populate: {
          path: 'owner',
          select: 'email'
        }
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Create a unique reference
      const reference = `INV-${invoice.number}-${Date.now()}`;

      // Initialize transaction
      const transactionData = await this.initializeTransaction({
        amount: invoice.amount,
        email: invoice.company.owner.email,
        reference,
        callbackUrl,
        invoiceId: invoice._id,
        companyId: invoice.company._id,
        invoiceNumber: invoice.number,
        companyName: invoice.company.name
      });

      // Update invoice with payment reference
      invoice.paymentReference = reference;
      await invoice.save();

      return transactionData.authorization_url;
    } catch (error) {
      console.error('Error processing invoice payment:', error);
      throw error;
    }
  }

  /**
   * Handle webhook events from Paystack
   * @param {Object} event - Webhook event data
   * @returns {Promise<void>}
   */
  async handleWebhook(event) {
    try {
      const { event: eventType, data } = event;

      switch (eventType) {
        case 'charge.success':
          await this.handleSuccessfulPayment(data);
          break;
        case 'charge.failed':
          await this.handleFailedPayment(data);
          break;
        // Add more event handlers as needed
      }
    } catch (error) {
      console.error('Error handling Paystack webhook:', error);
      throw error;
    }
  }

  /**
   * Handle successful payment
   * @param {Object} data - Payment data
   * @returns {Promise<void>}
   */
  async handleSuccessfulPayment(data) {
    try {
      const { reference, metadata } = data;
      
      if (metadata && metadata.invoice_id) {
        const invoice = await Invoice.findById(metadata.invoice_id);
        
        if (invoice) {
          invoice.status = 'paid';
          invoice.paymentDate = new Date();
          invoice.paymentMethod = 'card';
          invoice.paymentDetails = {
            gateway: 'paystack',
            reference,
            amount: data.amount / 100, // Convert from kobo/cents
            currency: data.currency,
            transactionDate: new Date(data.paid_at),
            cardType: data.authorization.card_type,
            last4: data.authorization.last4
          };
          
          await invoice.save();
          
          console.log(`Invoice ${invoice.number} marked as paid via Paystack`);
        }
      }
    } catch (error) {
      console.error('Error handling successful payment:', error);
      throw error;
    }
  }

  /**
   * Handle failed payment
   * @param {Object} data - Payment data
   * @returns {Promise<void>}
   */
  async handleFailedPayment(data) {
    try {
      const { reference, metadata } = data;
      
      if (metadata && metadata.invoice_id) {
        const invoice = await Invoice.findById(metadata.invoice_id);
        
        if (invoice) {
          invoice.paymentAttempts = (invoice.paymentAttempts || 0) + 1;
          invoice.lastPaymentAttempt = {
            date: new Date(),
            status: 'failed',
            gateway: 'paystack',
            reference,
            error: data.gateway_response
          };
          
          await invoice.save();
          
          console.log(`Failed payment attempt for invoice ${invoice.number} via Paystack`);
        }
      }
    } catch (error) {
      console.error('Error handling failed payment:', error);
      throw error;
    }
  }
}

module.exports = new PaystackService(); 