const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
const User = require('../models/user');
const Company = require('../models/Company');
const transporter = require('../config/emailConfig');
const ejs = require('ejs');
const path = require('path');
const moment = require('moment');

class PayrollReminderService {
  constructor() {
    this.transporter = transporter;
  }

  /**
   * Process all pending email reminders
   * This method should be called by the cron job
   */
  async processEmailReminders() {
    try {
      console.log('=== Processing Payroll Email Reminders ===');
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of day

      // Find events that need reminders
      const eventsNeedingReminders = await this.findEventsNeedingReminders(today);
      
      console.log(`Found ${eventsNeedingReminders.length} events needing reminders`);

      let successCount = 0;
      let failureCount = 0;

      for (const event of eventsNeedingReminders) {
        try {
          await this.sendEventReminder(event, today);
          successCount++;
        } catch (error) {
          console.error(`Failed to send reminder for event ${event._id}:`, error);
          failureCount++;
        }
      }

      console.log(`Email reminder processing complete: ${successCount} sent, ${failureCount} failed`);
      return { successCount, failureCount, totalProcessed: eventsNeedingReminders.length };
    } catch (error) {
      console.error('Error processing email reminders:', error);
      throw error;
    }
  }

  /**
   * Find events that need reminders sent today
   */
  async findEventsNeedingReminders(today) {
    const events = await PayrollCalendarEvent.find({
      'reminderSettings.enabled': true,
      'reminderSettings.emailReminder': true,
      status: { $ne: 'completed' },
      date: { $gte: today } // Only future events
    })
    .populate('company')
    .populate('assignedTo', 'firstName lastName email')
    .populate('createdBy', 'firstName lastName email');

    const eventsNeedingReminders = [];

    for (const event of events) {
      const daysUntilEvent = Math.ceil((event.date - today) / (1000 * 60 * 60 * 24));
      
      // Check each reminder day setting
      for (const reminderDay of event.reminderSettings.daysBeforeEvent) {
        if (daysUntilEvent === reminderDay) {
          // Check if reminder already sent for this day
          const alreadySent = event.emailReminders.some(
            reminder => reminder.daysBeforeEvent === reminderDay
          );
          
          if (!alreadySent) {
            eventsNeedingReminders.push({
              ...event.toObject(),
              reminderDaysBeforeEvent: reminderDay
            });
          }
        }
      }
    }

    return eventsNeedingReminders;
  }

  /**
   * Send email reminder for a specific event
   */
  async sendEventReminder(event, today) {
    try {
      // Get recipients
      const recipients = await this.getEventRecipients(event);
      
      if (recipients.length === 0) {
        console.log(`No recipients found for event ${event._id}`);
        return;
      }

      // Send personalized emails to each recipient
      const sentTo = [];
      let messageId = null;

      for (const recipient of recipients) {
        try {
          // Generate personalized email content for each recipient
          const emailContent = await this.generateReminderEmail(event, recipient);

          const mailOptions = {
            from: {
              name: process.env.EMAIL_FROM_NAME || "PandaPayroll Compliance",
              address: process.env.EMAIL_USER,
            },
            to: recipient.email,
            subject: emailContent.subject,
            html: emailContent.html,
            headers: {
              "X-Priority": this.getPriorityHeader(event.priority),
              "X-MSMail-Priority": this.getPriorityHeader(event.priority),
              "Importance": this.getPriorityImportance(event.priority),
              "Message-ID": `<${Date.now()}.${Math.random().toString(36).substring(2)}@pss-group.co.za>`,
              "X-Mailer": "PandaPayroll Reminder System/1.0",
              "X-Event-Type": event.type,
              "X-Event-Priority": event.priority,
              "X-Company-ID": event.company?._id || event.company,
            },
          };

          console.log(`Sending personalized reminder to ${recipient.email} (${recipient.role || 'assigned'}) for event: ${event.title}`);
          const info = await this.transporter.sendMailWithRetry(mailOptions);
          messageId = info.messageId;

          sentTo.push({
            email: recipient.email,
            userId: recipient.userId,
            name: recipient.name,
            role: recipient.role
          });

          console.log(`✅ Reminder sent successfully to ${recipient.email} for event ${event._id}`);
        } catch (emailError) {
          console.error(`❌ Failed to send email to ${recipient.email}:`, emailError);
          // Continue with other recipients
        }
      }

      // Record the sent reminder
      if (sentTo.length > 0) {
        await this.recordSentReminder(event._id, event.reminderDaysBeforeEvent, sentTo, messageId);
      }

    } catch (error) {
      console.error(`Error sending reminder for event ${event._id}:`, error);
      throw error;
    }
  }

  /**
   * Get recipients for an event
   */
  async getEventRecipients(event) {
    const recipients = [];
    const addedEmails = new Set(); // Prevent duplicate emails

    console.log(`Getting recipients for event: ${event.title} (Company: ${event.company?._id || event.company})`);

    // Add assigned users first (if any)
    if (event.assignedTo && event.assignedTo.length > 0) {
      console.log(`Found ${event.assignedTo.length} assigned users`);

      for (const user of event.assignedTo) {
        if (user.email && !addedEmails.has(user.email.toLowerCase())) {
          recipients.push({
            email: user.email,
            userId: user._id,
            name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email
          });
          addedEmails.add(user.email.toLowerCase());
          console.log(`Added assigned user: ${user.email}`);
        }
      }
    }

    // Always include company administrators (even if there are assigned users)
    // This ensures compliance notifications reach management
    if (event.company) {
      const companyId = event.company._id || event.company;
      console.log(`Looking for company administrators for company: ${companyId}`);

      try {
        // Find all users associated with this company
        const companyUsers = await User.find({
          companies: companyId,
          isVerified: true, // Only verified users
          email: { $exists: true, $ne: null, $ne: '' }, // Must have email
          $or: [
            { roleName: 'owner' },
            { roleName: 'admin' },
            { roleName: 'manager' },
            { roleName: 'hr' }, // Include HR users
            { roleName: 'payroll' } // Include payroll users
          ]
        }).select('email firstName lastName roleName');

        console.log(`Found ${companyUsers.length} potential company administrators`);

        for (const admin of companyUsers) {
          if (admin.email && !addedEmails.has(admin.email.toLowerCase())) {
            recipients.push({
              email: admin.email,
              userId: admin._id,
              name: `${admin.firstName || ''} ${admin.lastName || ''}`.trim() || admin.email,
              role: admin.roleName
            });
            addedEmails.add(admin.email.toLowerCase());
            console.log(`Added company ${admin.roleName}: ${admin.email}`);
          }
        }

        // If still no recipients, get the company owner as fallback
        if (recipients.length === 0) {
          console.log('No administrators found, looking for company owner...');

          const Company = require('../models/Company');
          const company = await Company.findById(companyId).populate('owner', 'email firstName lastName');

          if (company && company.owner && company.owner.email) {
            recipients.push({
              email: company.owner.email,
              userId: company.owner._id,
              name: `${company.owner.firstName || ''} ${company.owner.lastName || ''}`.trim() || company.owner.email,
              role: 'owner'
            });
            console.log(`Added company owner as fallback: ${company.owner.email}`);
          }
        }

      } catch (error) {
        console.error('Error finding company administrators:', error);
      }
    }

    console.log(`Final recipient list: ${recipients.length} recipients`);
    recipients.forEach(r => console.log(`  - ${r.email} (${r.role || 'assigned'})`));

    return recipients;
  }

  /**
   * Generate email content for reminder
   */
  async generateReminderEmail(event, recipient) {
    const templatePath = path.join(__dirname, '../views/emails/payroll-reminder.ejs');

    const daysText = event.reminderDaysBeforeEvent === 1 ? 'tomorrow' :
                     event.reminderDaysBeforeEvent === 0 ? 'today' :
                     `in ${event.reminderDaysBeforeEvent} days`;

    const priorityText = this.getPriorityText(event.priority);
    const typeText = this.getEventTypeText(event.type);

    // Get company name from populated company or fallback
    let companyName = 'Your Company';
    if (event.company) {
      if (typeof event.company === 'object' && event.company.name) {
        companyName = event.company.name;
      } else {
        // If company is just an ID, try to get the name
        try {
          const Company = require('../models/Company');
          const company = await Company.findById(event.company).select('name');
          if (company) {
            companyName = company.name;
          }
        } catch (error) {
          console.error('Error fetching company name:', error);
        }
      }
    }

    const templateData = {
      event,
      recipient,
      daysText,
      priorityText,
      typeText,
      eventDate: moment(event.date).format('dddd, MMMM Do YYYY'),
      companyName,
      dashboardUrl: `${process.env.BASE_URL || process.env.PROD_BASE_URL || 'http://localhost:3002'}/dashboard`,
      currentYear: new Date().getFullYear(),
      recipientName: recipient?.name || 'Team Member'
    };

    const html = await ejs.renderFile(templatePath, templateData);

    // Personalize subject line
    const subjectPrefix = event.priority === 'critical' ? '🚨 URGENT' :
                         event.priority === 'high' ? '⚠️ IMPORTANT' :
                         '📋 REMINDER';

    const subject = `${subjectPrefix}: ${event.title} - Due ${daysText} | ${companyName}`;

    return { subject, html };
  }

  /**
   * Record sent reminder in database
   */
  async recordSentReminder(eventId, daysBeforeEvent, sentTo, messageId) {
    await PayrollCalendarEvent.findByIdAndUpdate(eventId, {
      $push: {
        emailReminders: {
          daysBeforeEvent,
          sentAt: new Date(),
          sentTo,
          emailStatus: 'sent',
          messageId
        }
      }
    });
  }

  /**
   * Get priority header value
   */
  getPriorityHeader(priority) {
    switch (priority) {
      case 'critical': return '1';
      case 'high': return '2';
      case 'medium': return '3';
      case 'low': return '4';
      default: return '3';
    }
  }

  /**
   * Get priority importance value
   */
  getPriorityImportance(priority) {
    switch (priority) {
      case 'critical': return 'high';
      case 'high': return 'high';
      case 'medium': return 'normal';
      case 'low': return 'low';
      default: return 'normal';
    }
  }

  /**
   * Get priority text for display
   */
  getPriorityText(priority) {
    switch (priority) {
      case 'critical': return '🚨 CRITICAL';
      case 'high': return '⚠️ HIGH PRIORITY';
      case 'medium': return '📋 REMINDER';
      case 'low': return '📝 NOTICE';
      default: return '📋 REMINDER';
    }
  }

  /**
   * Get event type text for display
   */
  getEventTypeText(type) {
    const typeMap = {
      'emp201': 'EMP201 Monthly Submission',
      'emp501': 'EMP501 Reconciliation',
      'irp5': 'IRP5/IT3(a) Submission',
      'it3a': 'IT3(a) Submission',
      'uif': 'UIF Submission',
      'sdl': 'SDL Submission',
      'eti': 'Employment Tax Incentive',
      'paye': 'PAYE Related',
      'compliance': 'Compliance Requirement',
      'cutoff': 'Payroll Cut-off',
      'processing': 'Processing Deadline',
      'payment': 'Payment Date',
      'custom': 'Custom Event',
      'reminder': 'Reminder'
    };
    
    return typeMap[type] || 'Payroll Event';
  }
}

module.exports = new PayrollReminderService();
