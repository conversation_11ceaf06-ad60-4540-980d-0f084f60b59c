async function generateUI19PDF(certificate) {
  try {
    const { company, employee, signature, formData } = certificate;


    // Load the PDF template
    const templatePath = path.join(__dirname, "../templates/UI19.pdf");

    if (!fs.existsSync(templatePath)) {
      throw new Error(`PDF template not found at: ${templatePath}`);
    }

    const pdfDoc = await PDFDocument.load(fs.readFileSync(templatePath));
    const form = pdfDoc.getForm();


    // Fill form fields
    await fillUI19FormFields(form, certificate);

    // Handle signature
    if (signature?.data) {

      try {
        // Convert base64 to image
        const signatureImage = await pdfDoc.embedPng(signature.data);
        const signatureDims = signatureImage.scale(0.5);


        // Get the first page
        const page = pdfDoc.getPages()[0];
        const { width, height } = page.getSize();

        // Calculate signature position (adjust these values as needed)
        const signatureX = width * 0.6;
        const signatureY = height * 0.2;


        // Draw the signature
        page.drawImage(signatureImage, {
          x: signatureX,
          y: signatureY,
          width: signatureDims.width,
          height: signatureDims.height,
        });

      } catch (error) {
        console.error("Error processing signature:", {
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
        });
        throw error;
      }
    } else {
    }

    // Flatten form
    form.flatten();

    // Save the PDF
    const pdfBytes = await pdfDoc.save();


    return pdfBytes;
  } catch (error) {
    console.error("Error generating PDF:", {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
}

// Helper function to fill UI19 form fields
async function fillUI19FormFields(form, certificate) {

  // Get all available fields first
  const fields = form.getFields();

  // Map certificate data to form fields with updated mappings
  const fieldsToFill = {
    // Updated mappings
    "dhFormfield-5439335179": certificate.formData.employerName, // From UI19 model
    "dhFormfield-5439335226": certificate.formData.employerIdNumber, // From UI19 model
    "dhFormfield-5439335230":
      certificate.company.employerDetails?.tradingName || "", // From employerDetails model
    "dhFormfield-5439335241":
      certificate.company.employerDetails?.payeNumber || "", // From employerDetails model
    "dhFormfield-5439335328": certificate.formData.employerSDL, // Leave as is
    "dhFormfield-5439335360": certificate.formData.employerUIF, // Leave as is
    "dhFormfield-5439335743": certificate.formData.employerAddress, // Leave as is
    "dhFormfield-5439335841": certificate.formData.employerPostal, // Leave as is
    "dhFormfield-5439335842": certificate.employee.lastName, // From Employee model
    "dhFormfield-5439336902": certificate.employee.firstName
      ? certificate.employee.firstName[0]
      : "", // First letter of firstName
    "dhFormfield-5439336903": certificate.employee.passportNumber || "", // From Employee model

    // Keep rest of the existing mappings
    employeeAddress: certificate.employee.residentialAddress,
    employeePostal: certificate.employee.postalAddress,
    employeePhone: certificate.employee.phone,
    employeeEmail: certificate.employee.email,
    employeeTaxNumber: certificate.employee.taxNumber,
    employmentStartDate: certificate.formData.startDate
      ? new Date(certificate.formData.startDate).toLocaleDateString()
      : "",
    employmentEndDate: certificate.formData.endDate
      ? new Date(certificate.formData.endDate).toLocaleDateString()
      : "",
    terminationReason: certificate.formData.terminationReason,
    occupation: certificate.formData.occupation,
    basicSalary: certificate.formData.basicSalary,
    totalHours: certificate.formData.totalHours,
    averageEarnings: certificate.formData.averageEarnings,
    leavePayOut: certificate.formData.leavePayOut,
    noticePayOut: certificate.formData.noticePayOut,
    otherPayments: certificate.formData.otherPayments,
    signatureDate: certificate.signature?.date
      ? new Date(certificate.signature.date).toLocaleDateString()
      : new Date().toLocaleDateString(),
  };


  // Fill each field
  for (const [fieldName, value] of Object.entries(fieldsToFill)) {
    try {
      const field = form.getTextField(fieldName);
      if (field) {
        const stringValue = value?.toString() || "";
        field.setText(stringValue);

      }
    } catch (error) {
      console.error(`Error filling field '${fieldName}':`, {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Verify filled fields
  const filledFields = form.getFields().map((field) => ({
    name: field.getName(),
    type: field.constructor.name,
    hasValue: field.hasValue(),
    value: field.getText(),
  }));

}
