const speakeasy = require("speakeasy");
const crypto = require("crypto");
const { ensureConnection } = require("../config/redis");

const memoryStore = new Map();
const useRedis = !!process.env.REDIS_URL;

class TwoFactorService {
  static async createVerification(user, redirectUrl) {
    const verificationToken = crypto.randomBytes(32).toString("hex");
    const verificationData = {
      userId: user._id.toString(),
      email: user.email,
      redirectUrl,
      status: "pending",
      timestamp: Date.now(),
    };
    if (useRedis) {
      const redisClient = await ensureConnection();
      await redisClient.setEx(`2fa:${verificationToken}`, 300, JSON.stringify(verificationData));
    } else {
      memoryStore.set(verificationToken, { value: verificationData, expires: Date.now() + 300 * 1000 });
      setTimeout(() => memoryStore.delete(verificationToken), 300 * 1000);
    }
    return { token: verificationToken, verification: verificationData };
  }

  static async getVerification(token) {
    if (useRedis) {
      const redisClient = await ensureConnection();
      const data = await redisClient.get(`2fa:${token}`);
      return data ? JSON.parse(data) : null;
    } else {
      const item = memoryStore.get(token);
      if (!item) return null;
      if (item.expires < Date.now()) {
        memoryStore.delete(token);
        return null;
      }
      return item.value;
    }
  }

  static async cleanup(token) {
    if (useRedis) {
      const redisClient = await ensureConnection();
      await redisClient.del(`2fa:${token}`);
    } else {
      memoryStore.delete(token);
    }
  }
}

module.exports = TwoFactorService;
