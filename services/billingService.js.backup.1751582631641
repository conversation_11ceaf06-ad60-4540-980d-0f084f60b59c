const cron = require('node-cron');
const moment = require('moment');
const User = require('../models/user');
const Company = require('../models/Company');
const Employee = require('../models/Employee');
const Invoice = require('../models/invoice');
const PDFDocument = require('pdfkit');
const transporter = require('../config/emailConfig');
const path = require('path');
const ejs = require('ejs');
const BillingPreference = require('../models/billingPreference');

class BillingService {
  constructor() {
    // Schedule daily billing check
    this.scheduleBillingTasks();
  }

  scheduleBillingTasks() {
    // Run daily at midnight to check for invoices and trial expirations
    cron.schedule('0 0 * * *', () => {
      this.processDailyBilling();
    });
  }

  async processDailyBilling() {
    console.log('Starting daily billing process:', new Date());
    
    try {
      // Get all companies with populated owner field
      const companies = await Company.find()
        .populate({
          path: 'owner',
          select: 'email firstName lastName createdAt'
        });
      
      const today = moment().startOf('day');
      
      for (const company of companies) {
        try {
          // Skip if owner is not found
          if (!company.owner) {
            console.log(`Skipping company ${company.name}: No owner found`);
            continue;
          }

          const trialExpiryDate = moment(company.owner.createdAt).add(30, 'days').startOf('day');
          
          console.log('Debug Billing Details:', {
            today: today.format('YYYY-MM-DD'),
            trialExpiryDate: trialExpiryDate.format('YYYY-MM-DD'),
            isSameDay: today.isSame(trialExpiryDate, 'day'),
            isAfter: today.isAfter(trialExpiryDate),
          });

          // If trial expires today, send first invoice
          if (today.isSame(trialExpiryDate)) {
            console.log(`Trial expires today for company ${company.name}. Sending first invoice.`);
            await this.generateAndSendInvoice(company);
            continue;
          }

          // If trial expired, check if today is the monthly billing date
          if (today.isAfter(trialExpiryDate)) {
            const subscriptionDate = moment(trialExpiryDate).date();
            if (today.date() === subscriptionDate) {
              console.log(`Monthly billing date for company ${company.name}`);
              await this.generateAndSendInvoice(company);
            }
          }
          
          // Send trial expiry notice one day before
          if (today.isSame(moment(trialExpiryDate).subtract(1, 'day'))) {
            console.log(`Sending trial expiry notice for company ${company.name}`);
            await this.sendTrialExpiryNotice(company);
          }
        } catch (error) {
          console.error(`Error processing billing for company ${company.name}:`, error);
        }
      }
    } catch (error) {
      console.error('Error in daily billing process:', error);
    }
  }

  async generateAndSendInvoice(company, isTrialExpiry = false) {
    console.log(`Generating invoice for company ${company.name}`);
    
    try {
      // Calculate billing details
      const activeEmployees = await Employee.countDocuments({
        company: company._id,
        employmentStatus: true,
      });

      const amount = this.calculateBillingAmount(activeEmployees);
      
      // Create invoice record
      const invoice = new Invoice({
        company: company._id,
        number: `INV-${Date.now().toString().slice(-6)}`,
        date: new Date(),
        amount: amount,
        status: 'pending',
        dueDate: moment().add(14, 'days').toDate(),
      });

      await invoice.save();

      // Generate PDF
      const pdfBuffer = await this.generateInvoicePDF(company, invoice, activeEmployees);

      // Send email
      await this.sendInvoiceEmail(company, invoice, pdfBuffer, isTrialExpiry);

      console.log(`Invoice generated and sent for company ${company.name}`);
      return invoice;
    } catch (error) {
      console.error(`Error generating invoice for company ${company.name}:`, error);
      throw error;
    }
  }

  calculateBillingAmount(activeEmployees) {
    let amount = 0;
    if (activeEmployees <= 10) {
      amount = 25 + ((activeEmployees - 1) * 11.50); // Starter tier
    } else if (activeEmployees <= 50) {
      amount = 25 + ((activeEmployees - 1) * 7.50); // Growth tier
    } else if (activeEmployees <= 100) {
      amount = 25 + ((activeEmployees - 1) * 6.75); // Business tier
    } else {
      amount = 25 + ((activeEmployees - 1) * 5.75); // Enterprise tier
    }
    return Math.max(amount, 25); // Minimum billing amount is R25 (base price)
  }

  getTierName(activeEmployees) {
    if (activeEmployees <= 10) return 'Starter';
    if (activeEmployees <= 50) return 'Growth';
    if (activeEmployees <= 100) return 'Business';
    return 'Enterprise';
  }

  async generateInvoicePDF(company, invoice, activeEmployees) {
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
    });

    // Collect the PDF buffer
    const chunks = [];
    doc.on('data', chunk => chunks.push(chunk));
    const pdfPromise = new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
    });

    // Define theme colors
    const colors = {
      primary: '#2B3674',  // Updated to match email template
      secondary: '#4C6FF5',
      background: '#f8fafc',
      cardBg: '#ffffff',
      textPrimary: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0',
      accent: '#FFA500'    // Orange accent color
    };

    // Try to add the panda logo
    try {
      doc.image(path.join(__dirname, '../public/images/pandalogo1.svg'), 50, 50, { width: 100 });
    } catch (error) {
      console.warn('Panda logo not found, trying fallback logo...');
      try {
        doc.image(path.join(__dirname, '../public/assets/logo.png'), 50, 50, { width: 100 });
      } catch (innerError) {
        console.warn('No logo found, skipping logo...');
      }
    }

    // Add company details (From section)
    doc
      .fontSize(20)
      .fillColor(colors.primary)
      .text('Panda Software Solutions', 50, 50, { align: 'right' })
      .fontSize(10)
      .fillColor(colors.textSecondary)
      .text('<EMAIL>', { align: 'right' })
      .text('VAT: *********', { align: 'right' })
      .moveDown(0.5);

    // Add a colored band for the invoice header
    doc
      .fillColor(colors.primary)
      .rect(50, 140, 495, 40)
      .fill();

    // Add invoice header text
    doc
      .fillColor('#FFFFFF')
      .fontSize(28)
      .text('INVOICE', 50, 146, { align: 'center' });

    // Add invoice details
    doc
      .fontSize(10)
      .fillColor(colors.textSecondary);

    // Create a table for invoice details
    const invoiceDetails = [
      ['Invoice Number:', invoice.number],
      ['Invoice Date:', moment(invoice.date).format('DD/MM/YYYY')],
      ['Due Date:', moment(invoice.dueDate).format('DD/MM/YYYY')],
      ['Status:', invoice.status.toUpperCase()]
    ];

    let yPos = 200;
    invoiceDetails.forEach(([label, value]) => {
      doc
        .fillColor(colors.textSecondary)
        .text(label, 50, yPos)
        .fillColor(colors.textPrimary)
        .text(value, 150, yPos);
      yPos += 20;
    });

    // Add billing details (To section)
    doc
      .moveDown(2)
      .fontSize(12)
      .fillColor(colors.primary)
      .text('BILL TO')
      .moveDown(0.5)
      .fontSize(10)
      .fillColor(colors.textPrimary)
      .text(company.name)
      .fillColor(colors.textSecondary)
      .text(company.address || '')
      .text(company.email || '')
      .text(company.phone || '')
      .moveDown(2);

    // Add table headers with themed background
    const tableTop = doc.y;
    doc
      .fillColor(colors.primary)
      .rect(50, tableTop, 495, 30)
      .fill();

    doc
      .fontSize(10)
      .fillColor('#FFFFFF')
      .text('Description', 60, tableTop + 10)
      .text('Quantity', 300, tableTop + 10)
      .text('Rate', 380, tableTop + 10)
      .text('Amount', 460, tableTop + 10);

    // Add table rows with alternating background
    let rowTop = tableTop + 40;

    // Base price row
    doc
      .fillColor('#F8FAFC')
      .rect(50, rowTop - 10, 495, 30)
      .fill();

    doc
      .fontSize(10)
      .fillColor(colors.textSecondary)
      .text('Base Price (First Employee)', 60, rowTop)
      .text('1', 300, rowTop)
      .text('R 25.00', 380, rowTop)
      .text('R 25.00', 460, rowTop);

    // Additional employees row
    const additionalEmployees = activeEmployees - 1;
    if (additionalEmployees > 0) {
      rowTop += 30;
      const rate = this.calculateRatePerEmployee(activeEmployees);
      const amount = (additionalEmployees * rate).toFixed(2);
      
      doc
        .text(`Additional Employees (${this.getTierName(activeEmployees)} Tier)`, 60, rowTop)
        .text(additionalEmployees.toString(), 300, rowTop)
        .text(`R ${rate.toFixed(2)}`, 380, rowTop)
        .text(`R ${amount}`, 460, rowTop);
    }

    // Add subtotal and total
    const totalTop = rowTop + 50;
    
    // Add line above totals
    doc
      .strokeColor(colors.border)
      .lineWidth(1)
      .moveTo(50, totalTop)
      .lineTo(545, totalTop)
      .stroke();

    // Subtotal
    doc
      .fontSize(10)
      .fillColor(colors.textPrimary)
      .text('Subtotal:', 380, totalTop + 20)
      .text(`R ${invoice.amount.toFixed(2)}`, 460, totalTop + 20);

    // VAT (if applicable)
    doc
      .text('VAT (0%):', 380, totalTop + 40)
      .text('R 0.00', 460, totalTop + 40);

    // Total with accent background
    const totalBoxTop = totalTop + 65;
    doc
      .fillColor(colors.primary)
      .rect(370, totalBoxTop, 175, 30)
      .fill();

    doc
      .fontSize(12)
      .fillColor('#FFFFFF')
      .text('Total:', 380, totalBoxTop + 8)
      .text(`R ${invoice.amount.toFixed(2)}`, 460, totalBoxTop + 8);

    // Add payment details with bordered section
    const paymentY = totalBoxTop + 60;
    
    // Draw payment details box
    doc
      .rect(50, paymentY, 495, 120)
      .fillAndStroke('#FFFFFF', colors.border);
    
    // Payment heading
    doc
      .fillColor(colors.primary)
      .rect(50, paymentY, 495, 30)
      .fill();
    
    doc
      .fontSize(12)
      .fillColor('#FFFFFF')
      .text('PAYMENT DETAILS', 70, paymentY + 10);

    // Banking details with improved formatting
    const bankingDetailsY = paymentY + 40;
    const bankingDetails = [
      ['Bank Name:', 'First National Bank'],
      ['Account Number:', '628 311 47120'],
      ['Branch Code:', '250655'],
      ['Reference:', invoice.number]
    ];

    bankingDetails.forEach(([label, value], i) => {
      doc
        .fillColor(colors.textSecondary)
        .text(label, 70, bankingDetailsY + (i * 20))
        .fillColor(colors.textPrimary)
        .text(value, 200, bankingDetailsY + (i * 20));
    });

    // Add footer
    const pageHeight = doc.page.height;
    doc
      .fontSize(8)
      .fillColor(colors.textSecondary)
      .text('Thank you for your business!', 50, pageHeight - 50, { align: 'center' })
      .text(`Generated on ${moment().format('DD/MM/YYYY')} by Panda Software Solutions`, { align: 'center' });

    // Finalize the document
    doc.end();

    return pdfPromise;
  }

  calculateRatePerEmployee(activeEmployees) {
    if (activeEmployees <= 10) return 11.50; // Starter tier
    if (activeEmployees <= 50) return 7.50;  // Growth tier
    if (activeEmployees <= 100) return 6.75; // Business tier
    return 5.75; // Enterprise tier
  }

  async sendInvoiceEmail(company, invoice, pdfBuffer, isTrialExpiry = false) {
    try {
      // Render email template
      const emailTemplate = await ejs.renderFile(
        path.join(__dirname, '../views/emails/invoice.ejs'),
        {
          companyName: company.name,
          invoiceNumber: invoice.number,
          amount: invoice.amount.toFixed(2),
          dueDate: moment(invoice.dueDate).format('DD/MM/YYYY'),
          isTrialExpiry
        }
      );

      // Send email with PDF attachment and embedded logo
      await transporter.sendMailWithRetry({
        to: company.owner.email,
        subject: isTrialExpiry ? 
          'Trial Period Ended - Your First Invoice' : 
          `Invoice ${invoice.number} for ${company.name}`,
        html: emailTemplate,
        attachments: [
          {
            filename: `invoice-${invoice.number}.pdf`,
            content: pdfBuffer,
            contentType: 'application/pdf'
          },
          {
            filename: 'pandalogo.svg',
            path: path.join(__dirname, '../public/images/pandalogo1.svg'),
            cid: 'pandalogo' // same cid value as in the html img src
          }
        ]
      });

      console.log(`Invoice email sent to ${company.owner.email}`);
    } catch (error) {
      console.error('Error sending invoice email:', error);
      throw error;
    }
  }

  async sendTrialExpiryNotice(company) {
    try {
      const emailTemplate = await ejs.renderFile(
        path.join(__dirname, '../views/emails/trial-expiry.ejs'),
        {
          companyName: company.name,
          userName: company.owner.firstName || 'Valued Customer'
        }
      );

      await transporter.sendMailWithRetry({
        to: company.owner.email,
        subject: 'Your Trial Period Ends Tomorrow',
        html: emailTemplate,
        attachments: [
          {
            filename: 'pandalogo.svg',
            path: path.join(__dirname, '../public/images/pandalogo1.svg'),
            cid: 'pandalogo' // same cid value as in the html img src
          }
        ]
      });

      console.log(`Trial expiry notice sent to ${company.owner.email}`);
    } catch (error) {
      console.error('Error sending trial expiry notice:', error);
      throw error;
    }
  }
}

module.exports = new BillingService();
