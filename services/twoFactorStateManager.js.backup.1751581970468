const Redis = require("redis");
const { ensureConnection } = require("../config/redis");

const memoryStore = new Map();
const useRedis = !!process.env.REDIS_URL;

class TwoFactorStateManager {
  static async saveState(sessionId, data) {
    if (useRedis) {
      const redisClient = await ensureConnection();
      const key = `2fa_state:${sessionId}`;
      await redisClient.setEx(
        key,
        300, // 5 minutes
        JSON.stringify(data)
      );
    } else {
      memoryStore.set(sessionId, { value: data, expires: Date.now() + 300 * 1000 });
      setTimeout(() => memoryStore.delete(sessionId), 300 * 1000);
    }
  }

  static async getState(sessionId) {
    if (useRedis) {
      const redisClient = await ensureConnection();
      const key = `2fa_state:${sessionId}`;
      const data = await redisClient.get(key);
      return data ? JSON.parse(data) : null;
    } else {
      const item = memoryStore.get(sessionId);
      if (!item) return null;
      if (item.expires < Date.now()) {
        memoryStore.delete(sessionId);
        return null;
      }
      return item.value;
    }
  }

  static async clearState(sessionId) {
    if (useRedis) {
      const redisClient = await ensureConnection();
      const key = `2fa_state:${sessionId}`;
      await redisClient.del(key);
    } else {
      memoryStore.delete(sessionId);
    }
  }
}

module.exports = TwoFactorStateManager;
