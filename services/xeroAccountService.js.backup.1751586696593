const Integration = require("../models/Integration");
const XeroAccountMapping = require("../models/xeroAccountMapping");
const { XeroClient } = require("xero-node");
const XeroTokenManager = require("./xeroTokenManager");
const debug = require("debug")("x-pay:xero");

class XeroAccountService {
  constructor(companyId) {
    this.companyId = companyId;
    this.integration = null;
    this.xero = null;
    this.tenantId = null;
    this.tokenManager = new XeroTokenManager();
  }

  async initialize() {
    this.integration = await Integration.findOne({
      company: this.companyId,
      provider: "xero",
    });

    debug("Found integration:", this.integration);

    if (!this.integration) {
      throw new Error("Xero integration not found or not connected");
    }

    // Initialize Xero client using token manager
    this.xero = this.tokenManager.createXeroClient();

    debug("Initialized Xero client with standardized config");

    // In test environment, we don't need to actually set the token
    if (process.env.NODE_ENV !== "test") {
      const tokenData = this.tokenManager.getTokenData(this.integration);

      if (!tokenData.accessToken) {
        throw new Error("Xero integration not connected");
      }

      await this.setTokenWithRefresh();
    }

    // Get tenant ID using standardized token manager
    const tokenData = this.tokenManager.getTokenData(this.integration);

    debug("Checking tenant ID:", {
      tenantId: tokenData.tenantId,
      integrationId: this.integration._id,
    });

    if (!tokenData.tenantId) {
      throw new Error("No Xero tenant ID found");
    }

    this.tenantId = tokenData.tenantId;
    debug("Initialized Xero client successfully", {
      tenantId: this.tenantId,
      hasAccessToken: !!tokenData.accessToken,
      expiresAt: tokenData.expiresAt
    });

    return this;
  }

  async setTokenWithRefresh() {
    const companyId = this.companyId.toString();

    // Check if token needs refresh using standardized logic
    if (this.tokenManager.needsRefresh(this.integration)) {
      debug("Token expired or expiring soon, attempting refresh...");

      // Acquire mutex to prevent concurrent refresh operations
      const shouldRefresh = await this.tokenManager.acquireRefreshMutex(companyId);

      if (shouldRefresh) {
        try {
          // Get current token data
          const currentTokenData = this.tokenManager.getTokenData(this.integration);

          // Set current token in Xero client for refresh
          const xeroTokenSet = this.tokenManager.toXeroTokenSet(currentTokenData);
          await this.xero.setTokenSet(xeroTokenSet);

          debug("Refreshing token...");
          const newTokenSet = await this.xero.refreshToken();

          // Convert to standardized format and update integration
          const newTokenData = this.tokenManager.fromXeroTokenSet(newTokenSet);
          this.tokenManager.setTokenData(this.integration, newTokenData);

          await this.integration.save();

          debug("Token refreshed successfully", {
            newExpiresAt: newTokenData.expiresAt,
            timeUntilExpiry: this.tokenManager.getTimeUntilExpiry(this.integration)
          });
        } catch (error) {
          debug("Error refreshing token:", error);
          throw new Error(`Failed to refresh Xero token: ${error.message}`);
        } finally {
          // Always release the mutex
          this.tokenManager.releaseRefreshMutex(companyId);
        }
      } else {
        debug("Token refresh was handled by another process");
        // Reload integration to get updated tokens
        this.integration = await Integration.findById(this.integration._id);
      }
    }

    // Set the current token set for the Xero client
    const tokenData = this.tokenManager.getTokenData(this.integration);
    const xeroTokenSet = this.tokenManager.toXeroTokenSet(tokenData);
    await this.xero.setTokenSet(xeroTokenSet);

    debug("Token set in Xero client", {
      hasAccessToken: !!xeroTokenSet.access_token,
      expiresAt: xeroTokenSet.expires_at ? new Date(xeroTokenSet.expires_at * 1000) : null
    });
  }

  async getChartOfAccounts() {
    try {
      if (!this.tenantId) {
        throw new Error("Xero tenant ID not set. Call initialize() first.");
      }

      await this.setTokenWithRefresh(); // Ensure token is fresh before making API call

      debug("Fetching Chart of Accounts from Xero");
      const response = await this.xero.accountingApi.getAccounts(this.tenantId);

      // Filter for relevant account types and sort by code
      const accounts = response.body.accounts
        .filter((account) =>
          [
            "EXPENSE",
            "LIABILITY",
            "CURRENT_LIABILITY",
            "NON_CURRENT_LIABILITY",
          ].includes(account.type)
        )
        .map((account) => ({
          id: account.accountID,
          code: account.code,
          name: account.name,
          type: account.type,
          description: account.description,
          status: account.status,
        }))
        .sort((a, b) => a.code.localeCompare(b.code));

      debug(`Found ${accounts.length} relevant accounts`);
      return accounts;
    } catch (error) {
      debug("Error fetching Chart of Accounts:", error);
      throw new Error(`Failed to fetch Chart of Accounts: ${error.message}`);
    }
  }

  async saveAccountMapping(mappingData) {
    try {
      const mapping = await XeroAccountMapping.findOneAndUpdate(
        {
          company: this.companyId,
          payrollAccountId: mappingData.payrollAccountId,
        },
        {
          ...mappingData,
          company: this.companyId,
          updatedAt: new Date(),
        },
        {
          new: true,
          upsert: true,
        }
      );

      debug("Saved account mapping:", {
        payrollAccountId: mappingData.payrollAccountId,
        xeroAccountCode: mappingData.xeroAccountCode,
      });

      return mapping;
    } catch (error) {
      debug("Error saving account mapping:", error);
      throw new Error(`Failed to save account mapping: ${error.message}`);
    }
  }

  async getAccountMappings() {
    try {
      const mappings = await XeroAccountMapping.find({
        company: this.companyId,
      });

      debug(`Found ${mappings.length} account mappings`);
      return mappings;
    } catch (error) {
      debug("Error fetching account mappings:", error);
      throw new Error(`Failed to fetch account mappings: ${error.message}`);
    }
  }

  async validateMappings() {
    return await XeroAccountMapping.validateCompanyMappings(this.companyId);
  }
}

module.exports = XeroAccountService;
