const PDFDocument = require('pdfkit');
const path = require('path');
const moment = require('moment');

class BillingStatementService {
  static async generatePDF(company, activeEmployees, billing) {
    // Create a new PDF document without custom font initially
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50
    });

    // Collect the PDF buffer
    const chunks = [];
    doc.on('data', chunk => chunks.push(chunk));
    const pdfPromise = new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
    });

    // Define theme colors
    const colors = {
      primary: '#6366f1',
      secondary: '#818cf8',
      background: '#f8fafc',
      cardBg: '#ffffff',
      textPrimary: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0'
    };

    // Add logo
    try {
      doc.image(path.join(__dirname, '../public/images/pandalogo1.svg'), 50, 50, { width: 120 });
    } catch (error) {
    }

    // Add company details (From section)
    doc
      .fontSize(24)
      .fillColor(colors.primary)
      .text('Panda Software Solutions Group', 50, 50, { align: 'right' })
      .fontSize(10)
      .fillColor(colors.textSecondary)
      .text('<EMAIL>', { align: 'right' })
      .moveDown(0.5);

    // Add statement header
    doc
      .fontSize(28)
      .fillColor(colors.textPrimary)
      .text('BILLING STATEMENT', 50, 150)
      .moveDown(0.5);

    // Add statement details
    doc
      .fontSize(10)
      .fillColor(colors.textSecondary);

    // Create a table for statement details
    const statementDetails = [
      ['Date:', moment().format('DD/MM/YYYY')],
      ['Period:', `${moment().startOf('month').format('DD/MM/YYYY')} - ${moment().format('DD/MM/YYYY')}`],
      ['Statement #:', `STMT-${Date.now().toString().slice(-6)}`]
    ];

    let yPos = doc.y;
    statementDetails.forEach(([label, value]) => {
      doc
        .text(label, 50, yPos)
        .text(value, 150, yPos);
      yPos += 20;
    });

    // Add billing details (To section)
    doc
      .moveDown(2)
      .fontSize(12)
      .fillColor(colors.primary)
      .text('BILL TO')
      .moveDown(0.5)
      .fontSize(10)
      .fillColor(colors.textPrimary)
      .text(company.name)
      .fillColor(colors.textSecondary)
      .text(company.address || '')
      .text(company.email || '')
      .text(company.phone || '')
      .moveDown(2);

    // Add table headers with themed background
    const tableTop = doc.y;
    doc
      .fillColor(colors.background)
      .rect(50, tableTop, 495, 30)
      .fill();

    doc
      .fontSize(10)
      .fillColor(colors.textPrimary)
      .text('Description', 60, tableTop + 10)
      .text('Quantity', 300, tableTop + 10)
      .text('Rate', 380, tableTop + 10)
      .text('Amount', 460, tableTop + 10);

    // Add table rows
    let rowTop = tableTop + 40;

    // Base price row
    doc
      .fillColor(colors.textSecondary)
      .text('Base Price (First Employee)', 60, rowTop)
      .text('1', 300, rowTop)
      .text('R 25.00', 380, rowTop)
      .text('R 25.00', 460, rowTop);

    // Additional employees row
    const additionalEmployees = activeEmployees - 1;
    if (additionalEmployees > 0) {
      rowTop += 30;
      const rate = billing.averageRate;
      const amount = (additionalEmployees * rate).toFixed(2);
      
      doc
        .text(`Additional Employees (${billing.getPricingTierText()})`, 60, rowTop)
        .text(additionalEmployees.toString(), 300, rowTop)
        .text(`R ${rate.toFixed(2)}`, 380, rowTop)
        .text(`R ${amount}`, 460, rowTop);
    }

    // Add subtotal and total
    const totalTop = rowTop + 50;
    
    // Add line above totals
    doc
      .strokeColor(colors.border)
      .lineWidth(1)
      .moveTo(50, totalTop)
      .lineTo(545, totalTop)
      .stroke();

    // Subtotal
    doc
      .fillColor(colors.textPrimary)
      .text('Subtotal:', 380, totalTop + 20)
      .text(`R ${billing.totalMonthlyPrice.toFixed(2)}`, 460, totalTop + 20);

    // VAT (if applicable)
    doc
      .text('VAT (0%):', 380, totalTop + 40)
      .text('R 0.00', 460, totalTop + 40);

    // Total with accent background
    const totalBoxTop = totalTop + 65;
    doc
      .fillColor(colors.primary)
      .opacity(0.1)
      .rect(370, totalBoxTop, 175, 30)
      .fill()
      .opacity(1);

    doc
      .fontSize(12)
      .fillColor(colors.primary)
      .text('Total:', 380, totalBoxTop + 8)
      .text(`R ${billing.totalMonthlyPrice.toFixed(2)}`, 460, totalBoxTop + 8);

    // Add payment details
    doc
      .moveDown(4)
      .fontSize(12)
      .fillColor(colors.primary)
      .text('PAYMENT DETAILS', 50)
      .moveDown(0.5)
      .fontSize(10)
      .fillColor(colors.textSecondary)
      .text('Bank: First National Bank')
      .text('Account Name: Panda Software Solutions Group')
      .text('Account Number: *********')
      .text('Branch Code: 250655')
      .text('Reference: Your company code')
      .moveDown(2)
      .text('Due Date: ' + moment().add(14, 'days').format('DD/MM/YYYY'));

    // Add footer
    doc
      .fontSize(8)
      .fillColor(colors.textSecondary)
      .text('This is a computer-generated document. No signature is required.', 50, 750, { align: 'center' });

    doc.end();

    return pdfPromise;
  }
}

module.exports = BillingStatementService;
