const Redis = require("redis");

class RedisService {
  constructor() {
    this.client = null;
    this.memoryStore = new Map();
    this.useRedis = !!process.env.REDIS_URL;
    if (!this.useRedis) {
      console.warn('[RedisService] REDIS_URL not set. Using in-memory store.');
    }
  }

  async connect() {
    if (!this.useRedis) return null;
    if (!this.client) {
      this.client = Redis.createClient({
        url: process.env.REDIS_URL,
      });
      this.client.on("error", (err) => console.error("Redis Error:", err));
      this.client.on("connect", () => console.log("Redis Connected"));
      await this.client.connect();
    }
    return this.client;
  }

  async set(key, value, expireSeconds = 300) {
    if (this.useRedis) {
      const client = await this.connect();
      await client.setEx(key, expireSeconds, JSON.stringify(value));
    } else {
      this.memoryStore.set(key, { value, expires: Date.now() + expireSeconds * 1000 });
      setTimeout(() => this.memoryStore.delete(key), expireSeconds * 1000);
    }
  }

  async get(key) {
    if (this.useRedis) {
      const client = await this.connect();
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } else {
      const item = this.memoryStore.get(key);
      if (!item) return null;
      if (item.expires < Date.now()) {
        this.memoryStore.delete(key);
        return null;
      }
      return item.value;
    }
  }

  async del(key) {
    if (this.useRedis) {
      const client = await this.connect();
      await client.del(key);
    } else {
      this.memoryStore.delete(key);
    }
  }

  async set2FAState(sessionId, state) {
    return this.set(`2fa:${sessionId}`, state);
  }

  async get2FAState(sessionId) {
    return this.get(`2fa:${sessionId}`);
  }

  async clear2FAState(sessionId) {
    return this.del(`2fa:${sessionId}`);
  }
}

module.exports = new RedisService();
