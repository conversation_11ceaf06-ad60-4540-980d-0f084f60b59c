const EmployeeReportAutomation = require('../models/EmployeeReportAutomation');
const EmailService = require('./emailService');
const User = require('../models/user');
const Employee = require('../models/Employee');
const Report = require('../models/Report');
const Company = require('../models/Company');

class EmployeeReportGenerator {
  async generateScheduledReports() {
    try {
      // Find all enabled automation settings due for generation
      const dueReports = await EmployeeReportAutomation.find({
        enabled: true,
        nextScheduledGeneration: { $lte: new Date() }
      }).populate('userId');

      for (const reportSettings of dueReports) {
        try {
          // Generate reports based on selected types
          const reports = await this.generateReports(
            reportSettings.userId._id, 
            reportSettings.reportTypes
          );

          // Send email with reports
          await this.sendReportEmail(
            reportSettings.preferredEmail || reportSettings.userId.email, 
            reports
          );

          // Update last and next generation dates
          reportSettings.lastGeneratedAt = new Date();
          reportSettings.nextScheduledGeneration = this.calculateNextGenerationDate(
            reportSettings.frequency
          );
          await reportSettings.save();
        } catch (error) {
          console.error(
            `Failed to generate report for user ${reportSettings.userId._id}:`, 
            error
          );
        }
      }
    } catch (error) {
      console.error('Error in scheduled report generation:', error);
    }
  }

  async generateReports(userId, reportTypes) {
    const reports = [];

    // Fetch user and company context
    const user = await User.findById(userId).populate('company');
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    for (const type of reportTypes) {
      switch(type) {
        case 'headcount':
          reports.push(await this.generateHeadcountReport(user));
          break;
        case 'performance':
          reports.push(await this.generatePerformanceReport(user));
          break;
        case 'compensation':
          reports.push(await this.generateCompensationReport(user));
          break;
      }
    }

    return reports;
  }

  async generateHeadcountReport(user) {
    // Implement headcount report generation logic
    return {
      type: 'headcount',
      data: {
        companyName: user.company.name,
        totalEmployees: await this.countCompanyEmployees(user.company._id),
        departmentBreakdown: await this.getDepartmentBreakdown(user.company._id)
      }
    };
  }

  async generatePerformanceReport(user) {
    // Implement performance report generation logic
    return {
      type: 'performance',
      data: {
        companyName: user.company.name,
        averagePerformanceScore: await this.calculateAveragePerformance(user.company._id),
        topPerformers: await this.getTopPerformers(user.company._id)
      }
    };
  }

  async generateCompensationReport(user) {
    // Implement compensation report generation logic
    return {
      type: 'compensation',
      data: {
        companyName: user.company.name,
        averageSalary: await this.calculateAverageSalary(user.company._id),
        salaryBands: await this.getSalaryBands(user.company._id)
      }
    };
  }

  async sendReportEmail(email, reports) {
    try {
      await EmailService.sendReportsEmail(email, reports);
    } catch (error) {
      console.error(`Failed to send report email to ${email}:`, error);
    }
  }

  calculateNextGenerationDate(frequency) {
    const now = new Date();
    switch(frequency) {
      case 'weekly':
        return new Date(now.setDate(now.getDate() + 7));
      case 'biWeekly':
        return new Date(now.setDate(now.getDate() + 14));
      case 'monthly':
        return new Date(now.setMonth(now.getMonth() + 1));
      default:
        return null;
    }
  }

  // Placeholder methods for data retrieval - replace with actual database queries
  async countCompanyEmployees(companyId) {
    return await Employee.countDocuments({ company: companyId });
  }

  async getDepartmentBreakdown(companyId) {
    const breakdown = await Employee.aggregate([
      { $match: { company: companyId } },
      { $group: { 
        _id: '$department', 
        count: { $sum: 1 } 
      }}
    ]);

    return breakdown.reduce((acc, dept) => {
      acc[dept._id] = dept.count;
      return acc;
    }, {});
  }

  async calculateAveragePerformance(companyId) {
    const result = await Employee.aggregate([
      { $match: { company: companyId } },
      { $avg: '$performanceScore' }
    ]);

    return result[0] || 0;
  }

  async getTopPerformers(companyId) {
    return await Employee.find({ 
      company: companyId 
    })
    .sort({ performanceScore: -1 })
    .limit(5)
    .select('firstName lastName performanceScore');
  }

  async calculateAverageSalary(companyId) {
    const result = await Employee.aggregate([
      { $match: { company: companyId } },
      { $avg: '$salary' }
    ]);

    return result[0] || 0;
  }

  async getSalaryBands(companyId) {
    const result = await Employee.aggregate([
      { $match: { company: companyId } },
      { $bucket: {
        groupBy: '$salary',
        boundaries: [0, 50000, 100000, 150000, Infinity],
        default: 'Other',
        output: {
          count: { $sum: 1 }
        }
      }}
    ]);

    return result.reduce((acc, band, index) => {
      const bandNames = ['Entry Level', 'Junior', 'Mid-Level', 'Senior', 'Executive'];
      acc[bandNames[index]] = band.count;
      return acc;
    }, {});
  }
}

module.exports = new EmployeeReportGenerator();
