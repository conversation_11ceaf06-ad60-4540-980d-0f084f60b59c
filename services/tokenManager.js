const Redis = require("redis");
const { promisify } = require("util");
const crypto = require("crypto");

class TokenManager {
  constructor() {
    this.useRedis = !!process.env.REDIS_URL;
    this.memoryStore = new Map();
    this.client = null; // Initialize client to null

    if (!this.useRedis) {
    }
  }

  async ensureRedisClient() {
    if (!this.useRedis) {
      return null; // Return null immediately if <PERSON><PERSON> is not configured
    }
    if (this.client && this.client.isOpen) {
      return this.client; // Return existing client if open
    }

    try {
      this.client = Redis.createClient({
        url: process.env.REDIS_URL,
        prefix: "2fa_token:",
      });
      this.client.on("connect", () => {
      });
      this.client.on("error", (err) => {
        console.error("Redis connection error [TokenManager]:", err);
        // If an error occurs, stop trying to use Redis
        this.useRedis = false;
        this.client = null; // Clear client on error
      });
      await this.client.connect();
      // Re-check useRedis in case it was set to false by an error handler during connection
      if (this.useRedis && this.client.isOpen) {
         // Bind methods after successful connection
        this.redisGet = this.client.get.bind(this.client);
        this.redisSet = this.client.set.bind(this.client);
        this.redisDel = this.client.del.bind(this.client);
        return this.client;
      } else {
        // Connection failed or useRedis was set to false
        this.client = null; // Ensure client is null
        return null;
      }
    } catch (error) {
      console.error("Redis initialization error [TokenManager]:", error);
      // If initialization fails, force memory fallback
      this.useRedis = false;
      this.client = null; // Clear client on error
      return null;
    }
  }

  async store2FAToken(token, data) {
    const client = await this.ensureRedisClient();

    try {
      const tokenData = {
        ...data,
        createdAt: Date.now(),
      };

      if (client) { // Use the ensured client
        await client.set(`2fa:${token}`, JSON.stringify(tokenData), {
          EX: 600, // 10 minutes
        });
      } else { // Fallback to memory if no client
        this.memoryStore.set(token, tokenData);
        setTimeout(() => this.memoryStore.delete(token), 600000);
      }

      return true;
    } catch (error) {
      console.error("Error storing 2FA token:", error);
      // Even if ensureRedisClient got a client, operation might fail. Fallback here too.
      this.memoryStore.set(token, { value: data, expires: Date.now() + 600000 });
      setTimeout(() => this.memoryStore.delete(token), 600000);
      console.error("Error storing 2FA token in Redis, fell back to memory.");
      return false;
    }
  }

  async verify2FAToken(token) {
    // verify2FAToken calls get2FAState, which will use ensureRedisClient
    return this.get2FAState(token);
  }

  async createAuthToken(user) {
    const client = await this.ensureRedisClient();

    try {
      const token = crypto.randomBytes(32).toString("hex");
      const tokenData = {
        userId: user._id,
        email: user.email,
        createdAt: Date.now(),
      };

      if (client) { // Use the ensured client
        await client.set(token, JSON.stringify(tokenData), {
          EX: 86400, // 24 hours
        });
      } else { // Fallback to memory if no client
        this.memoryStore.set(token, tokenData);
        setTimeout(() => this.memoryStore.delete(token), 86400000);
      }

      return token;
    } catch (error) {
      console.error("Error creating auth token:", error);
       // Even if ensureRedisClient got a client, operation might fail. Fallback here too.
       const token = crypto.randomBytes(32).toString("hex"); // Generate a new token for memory
       const tokenData = {
        userId: user._id,
        email: user.email,
        createdAt: Date.now(),
      };
      this.memoryStore.set(token, tokenData);
      setTimeout(() => this.memoryStore.delete(token), 86400000);
      console.error("Error creating auth token in Redis, fell back to memory.");
      // Rethrow the original error after attempting fallback, as token creation failed in Redis
      throw error;
    }
  }

  // Cleanup method
  async close() {
     if (this.client && this.client.isOpen) { // Check if client exists and is open
      try {
        await this.client.quit();
        this.client = null; // Clear client reference after quitting
      } catch (error) {
        console.error("Error closing Redis client [TokenManager]:", error);
      }
    }
    this.memoryStore.clear();
  }

  async remove2FAState(token) {
     const client = await this.ensureRedisClient();

    try {
      if (client) { // Use the ensured client
        await client.del(`2fa:${token}`);
      } else { // Fallback to memory if no client
        this.memoryStore.delete(token);
      }
      return true;
    } catch (error) {
      console.error("Error removing 2FA state:", error);
       // Even if ensureRedisClient got a client, operation might fail. Fallback here too.
      this.memoryStore.delete(token);
      console.error("Error removing 2FA state in Redis, fell back to memory.");
      return false;
    }
  }

  async get2FAState(token) {
    const client = await this.ensureRedisClient();

    try {
      let state;

      if (client) { // Use the ensured client
        const data = await client.get(`2fa:${token}`);
        state = data ? JSON.parse(data) : null;
      } else { // Fallback to memory if no client
        const item = this.memoryStore.get(token);
        // Manually check expiry for memory store items
         if (item && item.expires > Date.now()) {
             state = item.value;
         } else if (item) {
             // Item expired in memory, clean up
             this.memoryStore.delete(token);
             state = null;
         }
         else {
             state = null;
         }
      }

      return state;
    } catch (error) {
      console.error("Error getting 2FA state:", error);
      // Even if ensureRedisClient got a client, operation might fail. Fallback here too.
      const item = this.memoryStore.get(token);
         if (item && item.expires > Date.now()) {
             state = item.value;
         } else if (item) {
             // Item expired in memory, clean up
             this.memoryStore.delete(token);
             state = null;
         }
         else {
             state = null;
         }
       console.error("Error getting 2FA state from Redis, fell back to memory.");
      return state;
    }
  }
}

// Create and export a singleton instance
const tokenManager = new TokenManager();
module.exports = tokenManager;
