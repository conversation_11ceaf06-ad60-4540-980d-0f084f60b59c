const cron = require('node-cron');
const TokenService = require('./TokenService');
const SessionManagementService = require('./SessionManagementService');
const DeviceManagementService = require('./DeviceManagementService');
const TrustedDevice = require('../models/TrustedDevice');
const RememberMeToken = require('../models/RememberMeToken');
const Session = require('../models/session');

class CleanupService {
  constructor() {
    this.isRunning = false;
    this.cleanupJobs = new Map();
    this.defaultSchedules = {
      // Daily cleanup at 2 AM
      daily: '0 2 * * *',
      // Weekly cleanup on Sunday at 3 AM
      weekly: '0 3 * * 0',
      // Monthly cleanup on 1st day at 4 AM
      monthly: '0 4 1 * *',
    };
  }

  /**
   * Initialize cleanup service with scheduled jobs
   */
  initialize() {
    if (this.isRunning) {
      console.log('Cleanup service is already running');
      return;
    }

    console.log('Initializing Remember Me cleanup service...');

    // Daily cleanup
    this.scheduleJob('daily', this.defaultSchedules.daily, async () => {
      await this.dailyCleanup();
    });

    // Weekly cleanup
    this.scheduleJob('weekly', this.defaultSchedules.weekly, async () => {
      await this.weeklyCleanup();
    });

    // Monthly cleanup
    this.scheduleJob('monthly', this.defaultSchedules.monthly, async () => {
      await this.monthlyCleanup();
    });

    this.isRunning = true;
    console.log('Remember Me cleanup service initialized successfully');
  }

  /**
   * Stop cleanup service and cancel all scheduled jobs
   */
  stop() {
    if (!this.isRunning) {
      console.log('Cleanup service is not running');
      return;
    }

    console.log('Stopping Remember Me cleanup service...');

    // Cancel all scheduled jobs
    for (const [name, job] of this.cleanupJobs) {
      job.stop();
      console.log(`Stopped cleanup job: ${name}`);
    }

    this.cleanupJobs.clear();
    this.isRunning = false;
    console.log('Remember Me cleanup service stopped');
  }

  /**
   * Schedule a cleanup job
   * @param {String} name - Job name
   * @param {String} schedule - Cron schedule
   * @param {Function} task - Task function to execute
   */
  scheduleJob(name, schedule, task) {
    if (this.cleanupJobs.has(name)) {
      console.log(`Cleanup job ${name} already exists, stopping previous job`);
      this.cleanupJobs.get(name).stop();
    }

    const job = cron.schedule(schedule, async () => {
      console.log(`\n=== Starting ${name} cleanup ===`);
      const startTime = Date.now();

      try {
        await task();
        const duration = Date.now() - startTime;
        console.log(`=== Completed ${name} cleanup in ${duration}ms ===\n`);
      } catch (error) {
        console.error(`Error in ${name} cleanup:`, error);
      }
    }, {
      scheduled: false, // Don't start immediately
      timezone: process.env.TZ || 'UTC',
    });

    this.cleanupJobs.set(name, job);
    job.start();
  }

  /**
   * Daily cleanup tasks
   */
  async dailyCleanup() {
    const results = {
      expiredTokens: 0,
      expiredSessions: 0,
      inactiveDevices: 0,
      errors: [],
    };

    try {
      // Cleanup expired tokens
      const tokenResult = await TokenService.cleanupExpiredTokens();
      if (tokenResult.success) {
        results.expiredTokens = tokenResult.deletedCount;
      } else {
        results.errors.push(`Token cleanup failed: ${tokenResult.error}`);
      }
    } catch (error) {
      console.error('Error cleaning up tokens:', error);
      results.errors.push(`Token cleanup error: ${error.message}`);
    }

    try {
      // Cleanup expired sessions
      const sessionResult = await SessionManagementService.cleanupExpiredSessions();
      if (sessionResult.success) {
        results.expiredSessions = sessionResult.deletedCount;
      } else {
        results.errors.push(`Session cleanup failed: ${sessionResult.error}`);
      }
    } catch (error) {
      console.error('Error cleaning up sessions:', error);
      results.errors.push(`Session cleanup error: ${error.message}`);
    }

    return results;
  }

  /**
   * Weekly cleanup tasks
   */
  async weeklyCleanup() {
    const results = {
      inactiveDevices: 0,
      suspiciousDevices: 0,
      errors: [],
    };

    try {
      // Cleanup devices inactive for 30 days
      console.log('Cleaning up inactive devices (30 days)...');
      const deviceResult = await DeviceManagementService.cleanupOldDevices(30);
      if (deviceResult.success) {
        results.inactiveDevices = deviceResult.cleanedCount;
        console.log(`Cleaned up ${results.inactiveDevices} inactive devices`);
      } else {
        results.errors.push(`Device cleanup failed: ${deviceResult.error}`);
      }
    } catch (error) {
      console.error('Error cleaning up devices:', error);
      results.errors.push(`Device cleanup error: ${error.message}`);
    }

    try {
      // Review and cleanup suspicious devices
      console.log('Reviewing suspicious devices...');
      const suspiciousCount = await this.reviewSuspiciousDevices();
      results.suspiciousDevices = suspiciousCount;
      console.log(`Reviewed ${suspiciousCount} suspicious devices`);
    } catch (error) {
      console.error('Error reviewing suspicious devices:', error);
      results.errors.push(`Suspicious device review error: ${error.message}`);
    }

    return results;
  }

  /**
   * Monthly cleanup tasks
   */
  async monthlyCleanup() {
    const results = {
      oldDevices: 0,
      oldTokens: 0,
      oldSessions: 0,
      statistics: {},
      errors: [],
    };

    try {
      // Cleanup very old devices (90 days)
      const deviceResult = await DeviceManagementService.cleanupOldDevices(90);
      if (deviceResult.success) {
        results.oldDevices = deviceResult.cleanedCount;
      } else {
        results.errors.push(`Old device cleanup failed: ${deviceResult.error}`);
      }
    } catch (error) {
      console.error('Error cleaning up old devices:', error);
      results.errors.push(`Old device cleanup error: ${error.message}`);
    }

    try {
      // Generate and log statistics
      console.log('Generating monthly statistics...');
      const statistics = await this.generateStatistics();
      results.statistics = statistics;
      console.log('Monthly statistics:', statistics);
    } catch (error) {
      console.error('Error generating statistics:', error);
      results.errors.push(`Statistics generation error: ${error.message}`);
    }

    try {
      // Optimize database indexes
      console.log('Optimizing database indexes...');
      await this.optimizeIndexes();
      console.log('Database indexes optimized');
    } catch (error) {
      console.error('Error optimizing indexes:', error);
      results.errors.push(`Index optimization error: ${error.message}`);
    }

    // Log monthly cleanup summary
    console.log('Monthly cleanup summary:', results);
    return results;
  }

  /**
   * Review and handle suspicious devices
   * @returns {Number} Number of suspicious devices reviewed
   */
  async reviewSuspiciousDevices() {
    try {
      const suspiciousDevices = await TrustedDevice.find({
        'securityFlags.suspiciousActivity': true,
        isActive: true,
      });

      let reviewedCount = 0;

      for (const device of suspiciousDevices) {
        // Check if device has been suspicious for more than 7 days
        const suspiciousDate = device.metadata.get('suspiciousDate');
        if (suspiciousDate) {
          const daysSinceSuspicious = (Date.now() - new Date(suspiciousDate).getTime()) / (1000 * 60 * 60 * 24);
          
          if (daysSinceSuspicious > 7) {
            // Auto-revoke devices that have been suspicious for more than 7 days
            await DeviceManagementService.revokeDevice(
              device._id,
              device.user,
              'Automatic revocation - suspicious activity for 7+ days'
            );
            reviewedCount++;
            console.log(`Auto-revoked suspicious device: ${device.deviceId}`);
          }
        }
      }

      return reviewedCount;
    } catch (error) {
      console.error('Error reviewing suspicious devices:', error);
      return 0;
    }
  }

  /**
   * Generate comprehensive statistics
   * @returns {Object} Statistics object
   */
  async generateStatistics() {
    try {
      const [sessionStats, tokenStats, deviceStats] = await Promise.all([
        SessionManagementService.getSessionStatistics(),
        TokenService.getTokenStatistics(),
        DeviceManagementService.getDeviceStatistics(),
      ]);

      // Additional custom statistics
      const customStats = await this.generateCustomStatistics();

      return {
        sessions: sessionStats,
        tokens: tokenStats,
        devices: deviceStats,
        custom: customStats,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error generating statistics:', error);
      return {};
    }
  }

  /**
   * Generate custom statistics
   * @returns {Object} Custom statistics
   */
  async generateCustomStatistics() {
    try {
      // Average session duration
      const avgSessionDuration = await Session.aggregate([
        { $match: { isValid: false, invalidatedAt: { $exists: true } } },
        {
          $project: {
            duration: { $subtract: ['$invalidatedAt', '$createdAt'] },
          },
        },
        {
          $group: {
            _id: null,
            avgDuration: { $avg: '$duration' },
          },
        },
      ]);

      // Token usage patterns
      const tokenUsagePatterns = await RememberMeToken.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$usageCount',
            count: { $sum: 1 },
          },
        },
        { $sort: { _id: 1 } },
      ]);

      // Device trust distribution
      const deviceTrustDistribution = await TrustedDevice.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$trustLevel',
            count: { $sum: 1 },
          },
        },
      ]);

      return {
        avgSessionDurationMs: avgSessionDuration[0]?.avgDuration || 0,
        tokenUsagePatterns,
        deviceTrustDistribution,
      };
    } catch (error) {
      console.error('Error generating custom statistics:', error);
      return {};
    }
  }

  /**
   * Optimize database indexes
   */
  async optimizeIndexes() {
    try {
      // This is a placeholder for index optimization
      // In a real implementation, you might want to:
      // 1. Analyze query patterns
      // 2. Create/drop indexes based on usage
      // 3. Rebuild fragmented indexes
      
      console.log('Index optimization completed (placeholder)');
    } catch (error) {
      console.error('Error optimizing indexes:', error);
    }
  }

  /**
   * Run manual cleanup (for testing or immediate cleanup needs)
   * @param {String} type - Type of cleanup (daily, weekly, monthly, all)
   */
  async runManualCleanup(type = 'daily') {
    console.log(`\n=== Manual ${type} cleanup started ===`);
    const startTime = Date.now();

    try {
      let result;
      
      switch (type) {
        case 'daily':
          result = await this.dailyCleanup();
          break;
        case 'weekly':
          result = await this.weeklyCleanup();
          break;
        case 'monthly':
          result = await this.monthlyCleanup();
          break;
        case 'all':
          const dailyResult = await this.dailyCleanup();
          const weeklyResult = await this.weeklyCleanup();
          const monthlyResult = await this.monthlyCleanup();
          result = { daily: dailyResult, weekly: weeklyResult, monthly: monthlyResult };
          break;
        default:
          throw new Error(`Unknown cleanup type: ${type}`);
      }

      const duration = Date.now() - startTime;
      console.log(`=== Manual ${type} cleanup completed in ${duration}ms ===\n`);
      
      return result;
    } catch (error) {
      console.error(`Error in manual ${type} cleanup:`, error);
      throw error;
    }
  }

  /**
   * Get cleanup service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      scheduledJobs: Array.from(this.cleanupJobs.keys()),
      nextRuns: Array.from(this.cleanupJobs.entries()).map(([name, job]) => ({
        name,
        nextRun: job.nextDate()?.toISOString() || null,
      })),
    };
  }
}

// Create singleton instance
const cleanupService = new CleanupService();

module.exports = cleanupService;
