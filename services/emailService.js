const nodemailer = require('nodemailer');
const transporter = require('../config/emailConfig');

class EmailService {
  constructor() {
    this.transporter = transporter;
  }

  async sendReportsEmail(email, reports) {
    try {
      // Prepare email content
      const emailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: email,
        subject: 'Automated Employee Reports',
        html: this.generateEmailBody(reports),
        attachments: this.prepareReportAttachments(reports)
      };

      // Send email using the sendMailWithRetry method
      const info = await this.transporter.sendMailWithRetry(emailOptions);
      
      return info;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  generateEmailBody(reports) {
    let htmlContent = `
      <h1>Automated Employee Reports</h1>
      <p>The following reports have been generated:</p>
      <ul>
    `;

    reports.forEach(report => {
      htmlContent += `
        <li>
          <strong>${this.getReportTitle(report.type)}</strong>
          <pre>${JSON.stringify(report.data, null, 2)}</pre>
        </li>
      `;
    });

    htmlContent += `
      </ul>
      <p>Please find the detailed reports attached.</p>
    `;

    return htmlContent;
  }

  prepareReportAttachments(reports) {
    return reports.map(report => ({
      filename: `${report.type}_report.json`,
      content: JSON.stringify(report.data, null, 2)
    }));
  }

  getReportTitle(reportType) {
    const titles = {
      'headcount': 'Headcount Report',
      'performance': 'Performance Overview',
      'compensation': 'Compensation Summary'
    };
    return titles[reportType] || 'Employee Report';
  }
}

module.exports = new EmailService();
