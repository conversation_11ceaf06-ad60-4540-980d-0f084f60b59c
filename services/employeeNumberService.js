const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Employee = require("../models/Employee");
const mongoose = require("mongoose");

class EmployeeNumberService {
  static async generateEmployeeNumber(companyId) {
    try {
      // First, get the settings without a session to avoid circular references
      let settings = await EmployeeNumberSettings.findOne({ company: companyId });

      // If settings don't exist, create them with proper initialization
      if (!settings) {
        settings = new EmployeeNumberSettings({
          company: companyId,
          mode: "automatic",
          firstCompanyEmployeeNumber: "0001"
        });

        // Initialize pattern and sequence before saving
        try {
          settings.parsePattern();
        } catch (parseError) {
          console.error("Error parsing pattern during creation:", parseError);
          // Set default values if parsing fails
          settings.pattern = {
            prefix: "",
            numericPart: "0001",
            suffix: "",
            separator: ""
          };
          settings.sequence = {
            current: 1,
            start: 1,
            step: 1,
            paddingLength: 4
          };
        }

        await settings.save();
      }

      // Check if manual mode
      if (settings.mode === "manual") {
        throw new Error("Employee number generation is set to manual mode");
      }

      // Make sure pattern is parsed and valid
      if (!settings.pattern || !settings.pattern.numericPart || !settings.sequence) {
        try {
          settings.parsePattern();
          await settings.save();
        } catch (parseError) {
          console.error("Error re-parsing pattern:", parseError);
          throw new Error("Invalid employee number pattern configuration");
        }
      }

      // Use findOneAndUpdate with atomic operation to increment sequence
      // This eliminates the need for a session completely
      const updatedSettings = await EmployeeNumberSettings.findOneAndUpdate(
        { company: companyId },
        { $inc: { 'sequence.current': 1 } },
        { new: true }
      );

      if (!updatedSettings) {
        throw new Error("Failed to update employee number sequence");
      }

      // Generate number from updated settings
      const numericPart = String(updatedSettings.sequence.current).padStart(
        updatedSettings.sequence.paddingLength || 4, "0"
      );

      const employeeNumber = `${updatedSettings.pattern.prefix || ""}${updatedSettings.pattern.separator || ""}${numericPart}${updatedSettings.pattern.suffix || ""}`;


      // Check if number already exists in this company
      const existingEmployee = await Employee.findOne({
        company: companyId,
        companyEmployeeNumber: employeeNumber,
      });

      if (existingEmployee) {
        // Recursively try again with another number (with max retry limit)
        return this.generateEmployeeNumber(companyId);
      }

      // Update last generated number
      updatedSettings.lastGeneratedNumber = employeeNumber;
      await updatedSettings.save();

      return employeeNumber;
    } catch (error) {
      // Create a clean error without circular references
      const errorMessage = error.message || "Unknown error";
      console.error("Employee number generation error:", errorMessage);
      throw new Error(`Employee number generation failed: ${errorMessage}`);
    }
  }

  static async validateEmployeeNumber(companyId, employeeNumber) {
    try {
      // Check if the number matches the company's pattern
      const settings = await EmployeeNumberSettings.getOrCreateSettings(companyId);
      const pattern = settings.parsePattern();
      
      // Create a regex pattern based on the company's format
      const regexPattern = new RegExp(
        `^${pattern.prefix}${pattern.separator}\\d{${pattern.numericPart.length}}${pattern.suffix}$`
      );
      
      if (!regexPattern.test(employeeNumber)) {
        return {
          isValid: false,
          message: "Employee number does not match the required format"
        };
      }

      // Check for uniqueness
      const existingEmployee = await Employee.findOne({
        company: companyId,
        companyEmployeeNumber: employeeNumber,
      });

      return {
        isValid: !existingEmployee,
        message: existingEmployee ? "Employee number already exists" : "Valid employee number"
      };
    } catch (error) {
      return {
        isValid: false,
        message: `Validation error: ${error.message}`
      };
    }
  }

  static async updateSettings(companyId, settings) {
    try {
      const employeeNumberSettings = await EmployeeNumberSettings.getOrCreateSettings(companyId);
      
      // Update mode and first number
      employeeNumberSettings.mode = settings.mode;
      employeeNumberSettings.firstCompanyEmployeeNumber = settings.firstCompanyEmployeeNumber;
      
      // Parse the new pattern
      employeeNumberSettings.parsePattern();
      
      // If updating existing employees is requested
      if (settings.updateExistingEmployees && settings.mode === 'automatic') {
        await employeeNumberSettings.updateExistingEmployees();
      }
      
      return employeeNumberSettings;
    } catch (error) {
      throw new Error(`Failed to update employee number settings: ${error.message}`);
    }
  }
}

module.exports = EmployeeNumberService;
