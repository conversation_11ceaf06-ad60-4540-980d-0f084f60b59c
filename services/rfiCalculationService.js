const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");

class RFICalculationService {
  async calculateRFI(employeeId) {

    try {
      // Find employee and verify existence
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find payroll and verify existence
      const payroll = await Payroll.findOne({ employee: employeeId });
      if (!payroll) {
        throw new Error("Payroll record not found");
      }

      // Verify RFI configuration
      if (!employee.rfiConfig || !employee.rfiConfig.option) {
        throw new Error("RFI configuration not found");
      }

      let totalRFI = 0;
      const components = [];

      // Get available components from payroll
      const availableComponents = [];

      // Add basic salary if exists
      if (payroll.basicSalary) {
        availableComponents.push({
          name: "Basic Salary",
          amount: payroll.basicSalary,
        });
      }

      // Add commission if exists
      if (payroll.commission?.amount) {
        availableComponents.push({
          name: "Commission",
          amount: payroll.commission.amount,
        });
      }

      // Add travel allowance if exists
      if (payroll.travelAllowance?.amount) {
        availableComponents.push({
          name: "Travel Allowance",
          amount: payroll.travelAllowance.amount,
        });
      }


      // Calculate based on selected method
      if (employee.rfiConfig.option === "selectedIncome") {

        employee.rfiConfig.selectedIncomes.forEach((selected) => {
          const component = availableComponents.find(
            (c) => c.name === selected.name
          );
          if (component) {
            const includedAmount = component.amount;
            totalRFI += includedAmount;
            components.push({
              name: component.name,
              amount: component.amount,
              percentage: 100,
              includedAmount,
            });
          }
        });
      } else if (employee.rfiConfig.option === "percentagePerIncome") {

        employee.rfiConfig.percentageIncomes.forEach((income) => {
          const component = availableComponents.find(
            (c) => c.name === income.name
          );
          if (component) {
            const includedAmount = (component.amount * income.percentage) / 100;
            totalRFI += includedAmount;
            components.push({
              name: component.name,
              amount: component.amount,
              percentage: income.percentage,
              includedAmount,
            });
          }
        });
      }


      // Store calculation result
      await Employee.findByIdAndUpdate(employeeId, {
        "rfiConfig.lastCalculation": {
          date: new Date(),
          amount: totalRFI,
          components: components,
        },
      });

      return {
        total: totalRFI,
        components: components,
        calculationDate: new Date(),
      };
    } catch (error) {
      console.error("RFI Calculation Error:", error);
      console.error("Stack:", error.stack);
      throw new Error(`RFI calculation failed: ${error.message}`);
    }
  }
}

module.exports = new RFICalculationService();
