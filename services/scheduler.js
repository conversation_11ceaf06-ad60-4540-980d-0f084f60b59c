const cron = require('node-cron');
const payrollReminderService = require('./payrollReminderService');

class SchedulerService {
  constructor() {
    this.jobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize all scheduled jobs
   */
  init() {
    if (this.isInitialized) {
      return;
    }

    
    try {
      // Schedule email reminders to run daily at 8:00 AM
      this.scheduleEmailReminders();
      
      // Schedule cleanup tasks to run weekly
      this.scheduleCleanupTasks();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize scheduler:', error);
      throw error;
    }
  }

  /**
   * Schedule daily email reminders
   */
  scheduleEmailReminders() {
    // Run every day at 8:00 AM
    const emailReminderJob = cron.schedule('0 8 * * *', async () => {
      
      try {
        const result = await payrollReminderService.processEmailReminders();
      } catch (error) {
        console.error('Email reminder job failed:', error);
      }
    }, {
      scheduled: false, // Don't start immediately
      timezone: 'Africa/Johannesburg' // South African timezone
    });

    this.jobs.set('emailReminders', emailReminderJob);
    emailReminderJob.start();
    
  }

  /**
   * Schedule cleanup tasks
   */
  scheduleCleanupTasks() {
    // Run every Sunday at 2:00 AM
    const cleanupJob = cron.schedule('0 2 * * 0', async () => {
      
      try {
        await this.runCleanupTasks();
      } catch (error) {
        console.error('Cleanup job failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.jobs.set('cleanup', cleanupJob);
    cleanupJob.start();
    
  }

  /**
   * Run cleanup tasks
   */
  async runCleanupTasks() {
    const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
    
    // Clean up old email reminder records (older than 1 year)
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    try {
      const result = await PayrollCalendarEvent.updateMany(
        {
          'emailReminders.sentAt': { $lt: oneYearAgo }
        },
        {
          $pull: {
            emailReminders: {
              sentAt: { $lt: oneYearAgo }
            }
          }
        }
      );
      
    } catch (error) {
      console.error('Failed to clean up old email reminder records:', error);
    }
  }

  /**
   * Manually trigger email reminders (for testing or admin use)
   */
  async triggerEmailReminders() {
    try {
      const result = await payrollReminderService.processEmailReminders();
      return result;
    } catch (error) {
      console.error('Manual email reminder trigger failed:', error);
      throw error;
    }
  }

  /**
   * Get status of all scheduled jobs
   */
  getJobStatus() {
    const status = {};
    
    for (const [jobName, job] of this.jobs) {
      status[jobName] = {
        running: job.running,
        scheduled: job.scheduled,
        destroyed: job.destroyed
      };
    }
    
    return {
      initialized: this.isInitialized,
      totalJobs: this.jobs.size,
      jobs: status
    };
  }

  /**
   * Stop a specific job
   */
  stopJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      return true;
    }
    return false;
  }

  /**
   * Start a specific job
   */
  startJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.start();
      return true;
    }
    return false;
  }

  /**
   * Stop all scheduled jobs
   */
  stopAll() {
    for (const [jobName, job] of this.jobs) {
      job.stop();
    }
  }

  /**
   * Destroy all scheduled jobs
   */
  destroyAll() {
    for (const [jobName, job] of this.jobs) {
      job.destroy();
    }
    this.jobs.clear();
    this.isInitialized = false;
  }
}

// Export singleton instance
module.exports = new SchedulerService();
