const Excel = require("exceljs");
const PDFDocument = require("pdfkit");
const moment = require("moment");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");

class PayrollReportService {
  // Generate Payslips Report
  static async generatePayslipsReport(companyId, dateRange, format, settings) {
    try {
      const payrolls = await Payroll.find({
        company: companyId,
        date: {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate),
        },
      }).populate("employee");

      const reportData = payrolls.map((payroll) => ({
        employeeName: `${payroll.employee.firstName} ${payroll.employee.lastName}`,
        employeeNumber: payroll.employee.companyEmployeeNumber,
        period: moment(payroll.date).format("MMMM YYYY"),
        grossPay: payroll.grossPay.toFixed(2),
        totalDeductions: payroll.totalDeductions.toFixed(2),
        netPay: payroll.netPay.toFixed(2),
        paymentDate: moment(payroll.paymentDate).format("DD/MM/YYYY"),
        bankDetails: settings.showBankDetails
          ? {
              bankName: payroll.employee.bankDetails?.bankName,
              accountNumber: settings.maskSensitiveData
                ? this.maskAccountNumber(
                    payroll.employee.bankDetails?.accountNumber
                  )
                : payroll.employee.bankDetails?.accountNumber,
              branchCode: payroll.employee.bankDetails?.branchCode,
            }
          : null,
        ytdTotals: settings.includeYTDTotals
          ? {
              grossPay: this.calculateYTDTotal(payroll, "grossPay"),
              totalDeductions: this.calculateYTDTotal(
                payroll,
                "totalDeductions"
              ),
              netPay: this.calculateYTDTotal(payroll, "netPay"),
            }
          : null,
      }));

      switch (format) {
        case "pdf":
          return await this.generatePayslipsPDF(reportData, settings);
        case "excel":
          return await this.generatePayslipsExcel(reportData, settings);
        case "csv":
          return await this.generatePayslipsCSV(reportData, settings);
        default:
          throw new Error("Unsupported format");
      }
    } catch (error) {
      console.error("Error generating payslips report:", error);
      throw error;
    }
  }

  // Generate Payroll Summary Report
  static async generatePayrollSummaryReport(
    companyId,
    dateRange,
    format,
    settings
  ) {
    try {
      const payrolls = await Payroll.find({
        company: companyId,
        date: {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate),
        },
      }).populate("employee");

      const summary = {
        totalEmployees: payrolls.length,
        totalGrossPay: payrolls.reduce((sum, p) => sum + p.grossPay, 0),
        totalDeductions: payrolls.reduce(
          (sum, p) => sum + p.totalDeductions,
          0
        ),
        totalNetPay: payrolls.reduce((sum, p) => sum + p.netPay, 0),
        periodBreakdown: this.calculatePeriodBreakdown(payrolls),
      };

      switch (format) {
        case "pdf":
          return await this.generateSummaryPDF(summary, settings);
        case "excel":
          return await this.generateSummaryExcel(summary, settings);
        case "csv":
          return await this.generateSummaryCSV(summary, settings);
        default:
          throw new Error("Unsupported format");
      }
    } catch (error) {
      console.error("Error generating payroll summary:", error);
      throw error;
    }
  }

  // Generate Banking Report
  static async generateBankingReport(companyId, dateRange, format, settings) {
    try {
      const payrolls = await Payroll.find({
        company: companyId,
        date: {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate),
        },
      }).populate("employee");

      const bankingData = payrolls.map((payroll) => ({
        employeeName: `${payroll.employee.firstName} ${payroll.employee.lastName}`,
        employeeNumber: payroll.employee.companyEmployeeNumber,
        bankName: payroll.employee.bankDetails?.bankName || "",
        accountNumber: payroll.employee.bankDetails?.accountNumber || "",
        branchCode: payroll.employee.bankDetails?.branchCode || "",
        accountType: payroll.employee.bankDetails?.accountType || "",
        amount: payroll.netPay.toFixed(2),
      }));

      switch (format) {
        case "pdf":
          return await this.generateBankingPDF(bankingData, settings);
        case "excel":
          return await this.generateBankingExcel(bankingData, settings);
        case "csv":
          return await this.generateBankingCSV(bankingData, settings);
        default:
          throw new Error("Unsupported format");
      }
    } catch (error) {
      console.error("Error generating banking report:", error);
      throw error;
    }
  }

  // Helper methods for formatting reports
  static calculatePeriodBreakdown(payrolls) {
    const breakdown = {};
    payrolls.forEach((payroll) => {
      const period = moment(payroll.date).format("MMMM YYYY");
      if (!breakdown[period]) {
        breakdown[period] = {
          count: 0,
          grossPay: 0,
          deductions: 0,
          netPay: 0,
        };
      }
      breakdown[period].count++;
      breakdown[period].grossPay += payroll.grossPay;
      breakdown[period].deductions += payroll.totalDeductions;
      breakdown[period].netPay += payroll.netPay;
    });
    return breakdown;
  }

  static maskAccountNumber(accountNumber) {
    if (!accountNumber) return "";
    const visible = 4; // Show last 4 digits
    return (
      "*".repeat(accountNumber.length - visible) + accountNumber.slice(-visible)
    );
  }

  static async calculateYTDTotal(payroll, field) {
    const startOfYear = moment(payroll.date).startOf("year").toDate();
    const ytdPayrolls = await Payroll.find({
      employee: payroll.employee._id,
      date: { $gte: startOfYear, $lte: payroll.date },
    });
    return ytdPayrolls.reduce((sum, p) => sum + p[field], 0).toFixed(2);
  }
}

module.exports = PayrollReportService;
