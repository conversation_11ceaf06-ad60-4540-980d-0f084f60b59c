const { XeroClient } = require("xero-node");
const Integration = require("../models/Integration");
const XeroErrorHandler = require("./xeroErrorHandler");
const debug = require("debug")("xero:token-manager");

/**
 * Centralized Xero Token Manager
 * Provides consistent token field naming and management across all components
 */
class XeroTokenManager {
  constructor() {
    // Standardized field names for token storage
    this.FIELD_NAMES = {
      ACCESS_TOKEN: 'accessToken',
      REFRESH_TOKEN: 'refreshToken',
      EXPIRES_AT: 'expiresAt',
      TENANT_ID: 'tenantId',
      ID_TOKEN: 'idToken',
      TOKEN_TYPE: 'tokenType',
      SCOPE: 'scope'
    };

    // Token refresh timing configuration
    this.REFRESH_THRESHOLD_MINUTES = 10; // Refresh 10 minutes before expiry
    this.REFRESH_THRESHOLD_MS = this.REFRESH_THRESHOLD_MINUTES * 60 * 1000;

    // Mutex tracking for preventing concurrent refreshes
    this.refreshMutex = new Map(); // companyId -> { isRefreshing: boolean, promise: Promise }

    // Error handler for enhanced error processing
    this.errorHandler = new XeroErrorHandler();
  }

  /**
   * Get standardized token data from integration credentials
   * @param {Object} integration - Integration document
   * @returns {Object} Standardized token data
   */
  getTokenData(integration) {
    if (!integration || !integration.credentials) {
      throw new Error("Integration or credentials not found");
    }

    const credentials = integration.credentials;
    
    // Handle both Map and Object formats for backward compatibility
    const getValue = (key) => {
      if (credentials instanceof Map) {
        return credentials.get(key);
      }
      return credentials[key];
    };

    // Try multiple field name variations for backward compatibility
    const accessToken = getValue(this.FIELD_NAMES.ACCESS_TOKEN) || 
                       getValue('access_token');
    
    const refreshToken = getValue(this.FIELD_NAMES.REFRESH_TOKEN) || 
                        getValue('refresh_token');
    
    const expiresAt = getValue(this.FIELD_NAMES.EXPIRES_AT) || 
                     getValue('tokenExpiry') || 
                     getValue('expires_at');

    const tenantId = getValue(this.FIELD_NAMES.TENANT_ID);
    const idToken = getValue(this.FIELD_NAMES.ID_TOKEN);
    const tokenType = getValue(this.FIELD_NAMES.TOKEN_TYPE) || 'Bearer';
    const scope = getValue(this.FIELD_NAMES.SCOPE);

    debug("Retrieved token data:", {
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      expiresAt: expiresAt,
      tenantId: tenantId,
      tokenType: tokenType
    });

    return {
      accessToken,
      refreshToken,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      tenantId,
      idToken,
      tokenType,
      scope
    };
  }

  /**
   * Set standardized token data in integration credentials
   * @param {Object} integration - Integration document
   * @param {Object} tokenData - Token data to store
   */
  setTokenData(integration, tokenData) {
    if (!integration) {
      throw new Error("Integration not found");
    }

    // Ensure credentials is a Map for consistent storage
    if (!(integration.credentials instanceof Map)) {
      integration.credentials = new Map();
    }

    // Store with standardized field names
    if (tokenData.accessToken) {
      integration.credentials.set(this.FIELD_NAMES.ACCESS_TOKEN, tokenData.accessToken);
    }
    
    if (tokenData.refreshToken) {
      integration.credentials.set(this.FIELD_NAMES.REFRESH_TOKEN, tokenData.refreshToken);
    }
    
    if (tokenData.expiresAt) {
      const expiresAt = tokenData.expiresAt instanceof Date ? 
                       tokenData.expiresAt : new Date(tokenData.expiresAt);
      integration.credentials.set(this.FIELD_NAMES.EXPIRES_AT, expiresAt.toISOString());
    }
    
    if (tokenData.tenantId) {
      integration.credentials.set(this.FIELD_NAMES.TENANT_ID, tokenData.tenantId);
    }
    
    if (tokenData.idToken) {
      integration.credentials.set(this.FIELD_NAMES.ID_TOKEN, tokenData.idToken);
    }
    
    if (tokenData.tokenType) {
      integration.credentials.set(this.FIELD_NAMES.TOKEN_TYPE, tokenData.tokenType);
    }
    
    if (tokenData.scope) {
      integration.credentials.set(this.FIELD_NAMES.SCOPE, tokenData.scope);
    }

    debug("Set token data with standardized fields:", {
      accessTokenSet: !!tokenData.accessToken,
      refreshTokenSet: !!tokenData.refreshToken,
      expiresAt: tokenData.expiresAt,
      tenantId: tokenData.tenantId
    });
  }

  /**
   * Check if token needs refresh based on expiration time
   * @param {Object} integration - Integration document
   * @returns {boolean} True if token needs refresh
   */
  needsRefresh(integration) {
    try {
      const tokenData = this.getTokenData(integration);
      
      if (!tokenData.expiresAt) {
        debug("No expiration date found, assuming refresh needed");
        return true;
      }

      const now = new Date();
      const expiresAt = new Date(tokenData.expiresAt);
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();

      const needsRefresh = timeUntilExpiry <= this.REFRESH_THRESHOLD_MS;
      
      debug("Token refresh check:", {
        now: now.toISOString(),
        expiresAt: expiresAt.toISOString(),
        timeUntilExpiryMinutes: Math.floor(timeUntilExpiry / (60 * 1000)),
        thresholdMinutes: this.REFRESH_THRESHOLD_MINUTES,
        needsRefresh
      });

      return needsRefresh;
    } catch (error) {
      debug("Error checking token expiration:", error.message);
      return true; // Assume refresh needed if we can't check
    }
  }

  /**
   * Get time until token expiry in seconds
   * @param {Object} integration - Integration document
   * @returns {number} Seconds until expiry, or 0 if expired/invalid
   */
  getTimeUntilExpiry(integration) {
    try {
      const tokenData = this.getTokenData(integration);

      if (!tokenData.expiresAt) {
        return 0;
      }

      const now = new Date();
      const expiresAt = new Date(tokenData.expiresAt);
      const timeUntilExpiry = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000));

      return timeUntilExpiry;
    } catch (error) {
      debug("Error calculating time until expiry:", error.message);
      return 0;
    }
  }

  /**
   * Check if a refresh operation is currently in progress for a company
   * @param {string} companyId - Company ID
   * @returns {boolean} True if refresh is in progress
   */
  isRefreshInProgress(companyId) {
    const mutex = this.refreshMutex.get(companyId);
    return mutex && mutex.isRefreshing;
  }

  /**
   * Acquire refresh mutex for a company
   * @param {string} companyId - Company ID
   * @returns {Promise} Promise that resolves when mutex is acquired or existing refresh completes
   */
  async acquireRefreshMutex(companyId) {
    const existingMutex = this.refreshMutex.get(companyId);

    if (existingMutex && existingMutex.isRefreshing) {
      debug(`Refresh already in progress for company ${companyId}, waiting...`);
      try {
        await existingMutex.promise;
        debug(`Existing refresh completed for company ${companyId}`);
        return false; // Indicate that we didn't perform the refresh
      } catch (error) {
        debug(`Existing refresh failed for company ${companyId}:`, error.message);
        // Continue to attempt our own refresh
      }
    }

    // Create new mutex
    const mutex = {
      isRefreshing: true,
      promise: null
    };

    this.refreshMutex.set(companyId, mutex);
    debug(`Acquired refresh mutex for company ${companyId}`);
    return true; // Indicate that we should perform the refresh
  }

  /**
   * Release refresh mutex for a company
   * @param {string} companyId - Company ID
   */
  releaseRefreshMutex(companyId) {
    this.refreshMutex.delete(companyId);
    debug(`Released refresh mutex for company ${companyId}`);
  }

  /**
   * Create Xero client with standardized configuration
   * @returns {XeroClient} Configured Xero client
   */
  createXeroClient() {
    const protocol = process.env.PROTOCOL || 'http';
    const host = process.env.HOST || 'localhost:3002';
    const redirectUri = `${protocol}://${host}/xero/callback`;

    return new XeroClient({
      clientId: process.env.XERO_CLIENT_ID,
      clientSecret: process.env.XERO_CLIENT_SECRET,
      redirectUris: [redirectUri],
      scopes: process.env.XERO_SCOPES ? process.env.XERO_SCOPES.split(' ') : [
        'offline_access',
        'openid',
        'profile',
        'email',
        'accounting.transactions',
        'accounting.settings',
        'accounting.contacts'
      ],
      httpTimeout: 30000 // 30 seconds timeout
    });
  }

  /**
   * Convert token data to Xero SDK format
   * @param {Object} tokenData - Standardized token data
   * @returns {Object} Token set in Xero SDK format
   */
  toXeroTokenSet(tokenData) {
    return {
      access_token: tokenData.accessToken,
      refresh_token: tokenData.refreshToken,
      expires_at: tokenData.expiresAt ? Math.floor(new Date(tokenData.expiresAt).getTime() / 1000) : null,
      id_token: tokenData.idToken,
      token_type: tokenData.tokenType || 'Bearer',
      scope: tokenData.scope
    };
  }

  /**
   * Convert Xero SDK token set to standardized format
   * @param {Object} xeroTokenSet - Token set from Xero SDK
   * @returns {Object} Standardized token data
   */
  fromXeroTokenSet(xeroTokenSet) {
    return {
      accessToken: xeroTokenSet.access_token,
      refreshToken: xeroTokenSet.refresh_token,
      expiresAt: xeroTokenSet.expires_at ? new Date(xeroTokenSet.expires_at * 1000) :
                 xeroTokenSet.expires_in ? new Date(Date.now() + xeroTokenSet.expires_in * 1000) : null,
      idToken: xeroTokenSet.id_token,
      tokenType: xeroTokenSet.token_type || 'Bearer',
      scope: xeroTokenSet.scope
    };
  }

  /**
   * Refresh token with enhanced error handling and recovery
   * @param {Object} integration - Integration document
   * @param {string} companyId - Company ID for mutex management
   * @returns {Object} Result object with success status and details
   */
  async refreshTokenWithErrorHandling(integration, companyId) {
    const context = {
      operation: 'token_refresh',
      companyId: companyId,
      integrationId: integration._id
    };

    try {
      // Check if refresh is already in progress
      const shouldRefresh = await this.acquireRefreshMutex(companyId);

      if (!shouldRefresh) {
        debug("Token refresh handled by another process");
        return {
          success: true,
          handled_by_other_process: true,
          message: "Token refresh was handled by another process"
        };
      }

      // Get current token data
      const tokenData = this.getTokenData(integration);

      if (!tokenData.refreshToken) {
        throw new Error("No refresh token available");
      }

      // Create Xero client and set token
      const xero = this.createXeroClient();
      await xero.initialize();

      const xeroTokenSet = this.toXeroTokenSet(tokenData);
      await xero.setTokenSet(xeroTokenSet);

      debug("Attempting token refresh...");
      const newTokenSet = await xero.refreshToken();

      // Update integration with new tokens
      const newTokenData = this.fromXeroTokenSet(newTokenSet);
      this.setTokenData(integration, newTokenData);
      await integration.save();

      debug("Token refresh successful", {
        newExpiresAt: newTokenData.expiresAt,
        timeUntilExpiry: this.getTimeUntilExpiry(integration)
      });

      return {
        success: true,
        refreshed: true,
        expiresAt: newTokenData.expiresAt,
        timeUntilExpiry: this.getTimeUntilExpiry(integration)
      };

    } catch (error) {
      // Analyze error using enhanced error handler
      const errorInfo = this.errorHandler.analyzeError(error, context);
      this.errorHandler.logError(errorInfo);

      // Handle specific error types
      if (this.errorHandler.shouldDeactivateIntegration(errorInfo)) {
        debug("Deactivating integration due to authentication error");
        integration.status = 'inactive';
        integration.lastError = errorInfo.message;
        await integration.save();
      }

      return {
        success: false,
        error: errorInfo,
        needsReauthentication: errorInfo.needsReauthentication,
        isRetryable: errorInfo.isRetryable,
        retryAfter: errorInfo.retryAfter
      };

    } finally {
      // Always release the mutex
      this.releaseRefreshMutex(companyId);
    }
  }
}

module.exports = XeroTokenManager;
