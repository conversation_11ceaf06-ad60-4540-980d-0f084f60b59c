const debug = require("debug")("xero:error-handler");

/**
 * Enhanced error handling for Xero operations
 * Provides structured error responses and recovery strategies
 */
class XeroErrorHandler {
  constructor() {
    // Error types that require re-authentication
    this.REAUTH_REQUIRED_ERRORS = [
      'invalid_grant',
      'invalid_token',
      'token_expired',
      'unauthorized',
      'forbidden'
    ];
    
    // Error types that can be retried
    this.RETRYABLE_ERRORS = [
      'network_error',
      'timeout',
      'rate_limit',
      'server_error',
      'service_unavailable'
    ];
    
    // HTTP status codes that indicate specific error types
    this.STATUS_CODE_MAPPING = {
      400: 'bad_request',
      401: 'unauthorized',
      403: 'forbidden',
      404: 'not_found',
      429: 'rate_limit',
      500: 'server_error',
      502: 'bad_gateway',
      503: 'service_unavailable',
      504: 'timeout'
    };
  }

  /**
   * Analyze error and determine error type and recovery strategy
   * @param {Error} error - The error to analyze
   * @param {Object} context - Additional context about the operation
   * @returns {Object} Structured error information
   */
  analyzeError(error, context = {}) {
    const errorInfo = {
      originalError: error,
      type: 'unknown',
      category: 'unknown',
      message: error.message || 'Unknown error occurred',
      needsReauthentication: false,
      isRetryable: false,
      retryAfter: null,
      userMessage: null,
      technicalDetails: {},
      context: context,
      timestamp: new Date().toISOString()
    };

    // Analyze based on error message
    const errorMessage = error.message?.toLowerCase() || '';

    if (errorMessage.includes('invalid_grant') || errorMessage.includes('invalid grant') ||
        errorMessage.includes('authorization grant is invalid')) {
      errorInfo.type = 'invalid_grant';
      errorInfo.category = 'authentication';
      errorInfo.needsReauthentication = true;
      errorInfo.userMessage = 'Your Xero connection has expired. Please reconnect to Xero.';
      errorInfo.technicalDetails.reason = 'Refresh token expired or revoked';
    } else if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
      errorInfo.type = 'unauthorized';
      errorInfo.category = 'authentication';
      errorInfo.needsReauthentication = true;
      errorInfo.userMessage = 'Authentication failed. Please reconnect to Xero.';
    } else if (errorMessage.includes('forbidden') || errorMessage.includes('403')) {
      errorInfo.type = 'forbidden';
      errorInfo.category = 'authorization';
      errorInfo.userMessage = 'Access denied. Please check your Xero permissions.';
    } else if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
      errorInfo.type = 'rate_limit';
      errorInfo.category = 'throttling';
      errorInfo.isRetryable = true;
      errorInfo.retryAfter = this.extractRetryAfter(error);
      errorInfo.userMessage = 'Too many requests. Please try again in a few minutes.';
    } else if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
      errorInfo.type = 'timeout';
      errorInfo.category = 'network';
      errorInfo.isRetryable = true;
      errorInfo.retryAfter = 30; // 30 seconds
      errorInfo.userMessage = 'Request timed out. Please try again.';
    } else if (errorMessage.includes('network') || errorMessage.includes('ECONNRESET')) {
      errorInfo.type = 'network_error';
      errorInfo.category = 'network';
      errorInfo.isRetryable = true;
      errorInfo.retryAfter = 10; // 10 seconds
      errorInfo.userMessage = 'Network error. Please check your connection and try again.';
    } else if (errorMessage.includes('server error') || errorMessage.includes('500')) {
      errorInfo.type = 'server_error';
      errorInfo.category = 'server';
      errorInfo.isRetryable = true;
      errorInfo.retryAfter = 60; // 1 minute
      errorInfo.userMessage = 'Xero server error. Please try again later.';
    }

    // Analyze based on HTTP status code if available
    if (error.response?.status) {
      const statusCode = error.response.status;
      const mappedType = this.STATUS_CODE_MAPPING[statusCode];
      
      if (mappedType && errorInfo.type === 'unknown') {
        errorInfo.type = mappedType;
        errorInfo.technicalDetails.httpStatus = statusCode;
      }
    }

    // Set category based on type if not already set
    if (errorInfo.category === 'unknown') {
      if (this.REAUTH_REQUIRED_ERRORS.includes(errorInfo.type)) {
        errorInfo.category = 'authentication';
        errorInfo.needsReauthentication = true;
      } else if (this.RETRYABLE_ERRORS.includes(errorInfo.type)) {
        errorInfo.category = 'temporary';
        errorInfo.isRetryable = true;
      }
    }

    // Set default user message if not set
    if (!errorInfo.userMessage) {
      errorInfo.userMessage = this.getDefaultUserMessage(errorInfo.type);
    }

    debug("Error analysis complete:", {
      type: errorInfo.type,
      category: errorInfo.category,
      needsReauth: errorInfo.needsReauthentication,
      isRetryable: errorInfo.isRetryable,
      context: context
    });

    return errorInfo;
  }

  /**
   * Extract retry-after value from error response
   * @param {Error} error - The error object
   * @returns {number|null} Retry after seconds
   */
  extractRetryAfter(error) {
    if (error.response?.headers?.['retry-after']) {
      const retryAfter = parseInt(error.response.headers['retry-after']);
      return isNaN(retryAfter) ? null : retryAfter;
    }
    return null;
  }

  /**
   * Get default user message for error type
   * @param {string} errorType - The error type
   * @returns {string} User-friendly error message
   */
  getDefaultUserMessage(errorType) {
    const messages = {
      invalid_grant: 'Your Xero connection has expired. Please reconnect.',
      unauthorized: 'Authentication failed. Please reconnect to Xero.',
      forbidden: 'Access denied. Please check your Xero permissions.',
      rate_limit: 'Too many requests. Please try again later.',
      timeout: 'Request timed out. Please try again.',
      network_error: 'Network error. Please check your connection.',
      server_error: 'Xero server error. Please try again later.',
      unknown: 'An unexpected error occurred. Please try again.'
    };
    
    return messages[errorType] || messages.unknown;
  }

  /**
   * Create a standardized error response for API endpoints
   * @param {Object} errorInfo - Analyzed error information
   * @returns {Object} Standardized error response
   */
  createErrorResponse(errorInfo) {
    return {
      success: false,
      error: {
        type: errorInfo.type,
        category: errorInfo.category,
        message: errorInfo.userMessage,
        needsReauthentication: errorInfo.needsReauthentication,
        isRetryable: errorInfo.isRetryable,
        retryAfter: errorInfo.retryAfter,
        timestamp: errorInfo.timestamp,
        ...(process.env.NODE_ENV === 'development' && {
          technicalDetails: errorInfo.technicalDetails,
          originalMessage: errorInfo.originalError.message
        })
      }
    };
  }

  /**
   * Log error with appropriate level and context
   * @param {Object} errorInfo - Analyzed error information
   */
  logError(errorInfo) {
    const logData = {
      type: errorInfo.type,
      category: errorInfo.category,
      message: errorInfo.message,
      context: errorInfo.context,
      timestamp: errorInfo.timestamp
    };

    if (errorInfo.needsReauthentication) {
      debug("Authentication error:", logData);
    } else if (errorInfo.isRetryable) {
      debug("Retryable error:", logData);
    } else {
      debug("Non-retryable error:", logData);
    }
  }

  /**
   * Determine if integration should be marked as inactive
   * @param {Object} errorInfo - Analyzed error information
   * @returns {boolean} True if integration should be deactivated
   */
  shouldDeactivateIntegration(errorInfo) {
    return errorInfo.needsReauthentication && 
           ['invalid_grant', 'unauthorized'].includes(errorInfo.type);
  }

  /**
   * Get recommended action for the error
   * @param {Object} errorInfo - Analyzed error information
   * @returns {string} Recommended action
   */
  getRecommendedAction(errorInfo) {
    if (errorInfo.needsReauthentication) {
      return 'reconnect';
    } else if (errorInfo.isRetryable) {
      return 'retry';
    } else {
      return 'contact_support';
    }
  }
}

module.exports = XeroErrorHandler;
