const EmailService = require('./emailService');
const ReportSettings = require('../models/ReportSettings');
const Company = require('../models/Company');
const User = require('../models/user');
const path = require('path');
const ejs = require('ejs');

/**
 * ReportEmailService - Bridges ReportSettings email automation with actual email sending
 * Integrates the ReportSettings emailSettings with the existing email infrastructure
 */
class ReportEmailService {
  constructor() {
    this.emailService = EmailService;
  }

  /**
   * Send report via email based on ReportSettings configuration
   * @param {Object} reportData - Generated report data
   * @param {String} reportType - Type of report (payslips, employeeList, etc.)
   * @param {String} companyId - Company ID
   * @param {Object} user - User who generated the report
   * @param {String} format - Report format (pdf, excel, csv)
   */
  async sendReportEmail(reportData, reportType, companyId, user, format = 'pdf') {
    try {
      // Get report settings for the company
      const settings = await ReportSettings.findOne({ company: companyId });

      if (!settings) {
        console.log('No settings found for this company');
        return { sent: false, reason: 'No settings found' };
      }

      // Get report-type-specific email settings or fall back to global settings
      const emailSettings = this.getEmailSettingsForReportType(settings, reportType);

      if (!emailSettings?.autoEmailReports) {
        console.log(`Email automation not enabled for report type: ${reportType}`);
        return { sent: false, reason: `Email automation not enabled for ${reportType}` };
      }

      if (!emailSettings.emailRecipients || emailSettings.emailRecipients.length === 0) {
        console.log(`No email recipients configured for report type: ${reportType}`);
        return { sent: false, reason: `No email recipients configured for ${reportType}` };
      }

      // Get company information
      const company = await Company.findById(companyId);
      if (!company) {
        throw new Error('Company not found');
      }

      // Prepare email content
      const emailContent = await this.generateReportEmailContent(
        reportType, 
        company, 
        user, 
        format
      );

      // Prepare attachments
      const attachments = this.prepareReportAttachments(reportData, reportType, format);

      // Send emails to all configured recipients
      const emailResults = [];
      for (const recipient of emailSettings.emailRecipients) {
        try {
          const emailOptions = {
            from: process.env.EMAIL_FROM || '<EMAIL>',
            to: recipient.trim(),
            subject: emailContent.subject,
            html: emailContent.html,
            attachments: attachments
          };

          const info = await this.emailService.transporter.sendMailWithRetry(emailOptions);
          
          emailResults.push({
            recipient: recipient.trim(),
            success: true,
            messageId: info.messageId
          });

          console.log(`Report email sent to ${recipient}. Message ID: ${info.messageId}`);
        } catch (error) {
          console.error(`Failed to send report email to ${recipient}:`, error);
          emailResults.push({
            recipient: recipient.trim(),
            success: false,
            error: error.message
          });
        }
      }

      return {
        sent: true,
        results: emailResults,
        totalRecipients: emailSettings.emailRecipients.length,
        successfulSends: emailResults.filter(r => r.success).length,
        reportType: reportType
      };

    } catch (error) {
      console.error('Error in sendReportEmail:', error);
      throw error;
    }
  }

  /**
   * Generate email content for report
   * @param {String} reportType - Type of report
   * @param {Object} company - Company object
   * @param {Object} user - User object
   * @param {String} format - Report format
   */
  async generateReportEmailContent(reportType, company, user, format) {
    const reportTitles = {
      'payslips': 'Payslips Report',
      'payrollSummary': 'Payroll Summary Report',
      'bankingReport': 'Banking Report',
      'employeeList': 'Employee List Report',
      'leaveReport': 'Leave Report',
      'leaveDaysReport': 'Leave Days Report',
      'leaveExpiryReport': 'Leave Expiry Report',
      'benefitsReport': 'Benefits Report',
      'emp201': 'EMP201 Report',
      'uifDeclaration': 'UIF Declaration Report',
      'etiReport': 'ETI Report',
      'employmentTaxIncentive': 'Employment Tax Incentive Report',
      'payrollVariance': 'Payroll Variance Report',
      'employeeBasicInfo': 'Employee Basic Information Report',
      'transactionHistory': 'Transaction History Report'
    };

    const reportTitle = reportTitles[reportType] || 'Report';
    const subject = `${reportTitle} - ${company.name}`;

    // Generate HTML content
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #1e293b; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center; }
          .content { background: #ffffff; padding: 30px; border: 1px solid #e2e8f0; }
          .footer { background: #f8fafc; padding: 20px; border-radius: 0 0 12px 12px; text-align: center; font-size: 14px; color: #64748b; }
          .report-info { background: #f1f5f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .report-info h3 { margin-top: 0; color: #6366f1; }
          .button { display: inline-block; background: #6366f1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 ${reportTitle}</h1>
            <p>Automated Report Delivery</p>
          </div>
          
          <div class="content">
            <h2>Hello,</h2>
            <p>Your requested <strong>${reportTitle}</strong> has been generated and is attached to this email.</p>
            
            <div class="report-info">
              <h3>Report Details</h3>
              <ul>
                <li><strong>Company:</strong> ${company.name}</li>
                <li><strong>Report Type:</strong> ${reportTitle}</li>
                <li><strong>Format:</strong> ${format.toUpperCase()}</li>
                <li><strong>Generated By:</strong> ${user.firstName} ${user.lastName}</li>
                <li><strong>Generated On:</strong> ${new Date().toLocaleString()}</li>
              </ul>
            </div>
            
            <p>Please find the detailed report attached to this email. If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Best regards,<br>
            <strong>PandaPayroll Team</strong></p>
          </div>
          
          <div class="footer">
            <p>This is an automated email from PandaPayroll. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Panda Software Solutions Group. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  /**
   * Prepare report attachments for email
   * @param {Buffer|Object} reportData - Report data
   * @param {String} reportType - Type of report
   * @param {String} format - Report format
   */
  prepareReportAttachments(reportData, reportType, format) {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${reportType}_${timestamp}.${format}`;

    let contentType;
    switch (format) {
      case 'pdf':
        contentType = 'application/pdf';
        break;
      case 'excel':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case 'csv':
        contentType = 'text/csv';
        break;
      default:
        contentType = 'application/octet-stream';
    }

    return [{
      filename: filename,
      content: reportData,
      contentType: contentType
    }];
  }

  /**
   * Test email configuration for a company
   * @param {String} companyId - Company ID
   * @param {String} testEmail - Email to send test to
   */
  async sendTestEmail(companyId, testEmail) {
    try {
      const company = await Company.findById(companyId);
      if (!company) {
        throw new Error('Company not found');
      }

      const emailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: testEmail,
        subject: `Test Email - Report Automation for ${company.name}`,
        html: `
          <h2>Test Email Successful!</h2>
          <p>This is a test email to verify that your report email automation is configured correctly.</p>
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Test sent at:</strong> ${new Date().toLocaleString()}</p>
          <p>If you received this email, your email automation is working properly.</p>
        `
      };

      const info = await this.emailService.transporter.sendMailWithRetry(emailOptions);
      
      return {
        success: true,
        messageId: info.messageId,
        recipient: testEmail
      };

    } catch (error) {
      console.error('Error sending test email:', error);
      throw error;
    }
  }

  /**
   * Get email settings for a specific report type
   * @param {Object} settings - ReportSettings object
   * @param {String} reportType - Type of report
   */
  getEmailSettingsForReportType(settings, reportType) {
    // Map report types to their settings sections
    const reportTypeMapping = {
      'payslips': 'payslips',
      'payrollSummary': 'payslips', // Use payslips settings for payroll summary
      'bankingReport': 'bankingReport',
      'employeeList': 'employeeReports',
      'employeeBasicInfo': 'employeeReports',
      'leaveReport': 'employeeReports',
      'leaveDaysReport': 'employeeReports',
      'leaveExpiryReport': 'employeeReports',
      'benefitsReport': 'employeeReports',
      'transactionHistory': 'employeeReports',
      'emp201': 'payslips', // Use payslips settings for statutory reports
      'uifDeclaration': 'payslips',
      'etiReport': 'payslips',
      'employmentTaxIncentive': 'payslips',
      'payrollVariance': 'payslips'
    };

    const settingsSection = reportTypeMapping[reportType];

    // Try to get report-type-specific email settings
    if (settingsSection && settings[settingsSection]?.emailSettings) {
      const specificSettings = settings[settingsSection].emailSettings;
      // Only use specific settings if they are actually configured
      if (specificSettings.autoEmailReports === true ||
          (specificSettings.emailRecipients && specificSettings.emailRecipients.length > 0)) {
        return specificSettings;
      }
    }

    // Fall back to global email settings
    return settings.emailSettings;
  }

  /**
   * Get email automation status for a company
   * @param {String} companyId - Company ID
   */
  async getEmailAutomationStatus(companyId) {
    try {
      const settings = await ReportSettings.findOne({ company: companyId });
      
      if (!settings) {
        return {
          enabled: false,
          configured: false,
          recipients: []
        };
      }

      return {
        enabled: settings.emailSettings?.autoEmailReports || false,
        configured: !!(settings.emailSettings?.emailRecipients && settings.emailSettings.emailRecipients.length > 0),
        recipients: settings.emailSettings?.emailRecipients || [],
        recipientCount: settings.emailSettings?.emailRecipients?.length || 0
      };

    } catch (error) {
      console.error('Error getting email automation status:', error);
      throw error;
    }
  }
}

module.exports = new ReportEmailService();
