const mongoose = require('mongoose');
const crypto = require('crypto');

// Load environment variables
require('dotenv').config();

// Import models
const Company = require('./models/Company');
const User = require('./models/user');
const SupportAccess = require('./models/SupportAccess');

async function createTestSupportToken() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Find a test company (or create one)
    let company = await Company.findOne({ companyCode: 'TEST001' });
    
    if (!company) {
      console.log('Creating test company...');
      company = new Company({
        name: 'Test Company',
        companyCode: 'TEST001',
        owner: new mongoose.Types.ObjectId() // Dummy owner ID
      });
      await company.save();
      console.log('Test company created:', company.companyCode);
    } else {
      console.log('Found existing test company:', company.companyCode);
    }

    // Generate a support token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 48); // 48 hours from now

    // Create support access record
    const supportAccess = new SupportAccess({
      company: company._id,
      grantedBy: new mongoose.Types.ObjectId(), // Dummy user ID
      token,
      expiresAt,
      isRevoked: false
    });

    await supportAccess.save();

    console.log('\n=== Support Token Created ===');
    console.log('Company:', company.name);
    console.log('Company Code:', company.companyCode);
    console.log('Token:', token);
    console.log('Expires At:', expiresAt);
    console.log('\n=== Test URL ===');
    console.log(`http://localhost:3002/clients/${company.companyCode}/dashboard?supportToken=${token}`);
    console.log('\n=== Alternative Test URL ===');
    console.log(`http://localhost:3002/clients/${company.companyCode}?supportToken=${token}`);

  } catch (error) {
    console.error('Error creating test support token:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\nMongoDB connection closed');
  }
}

// Run the script
createTestSupportToken();
