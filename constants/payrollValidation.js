// Input Type Categories and Definitions
const INPUT_CATEGORIES = {
  INCOME: {
    id: "income",
    label: "Income",
    components: [
      {
        id: "basicSalary",
        name: "Basic Salary",
        required: true,
        isBase: true,
      },
      {
        id: "commission",
        name: "Commission",
        requiresBaseSalary: true,
      },
      {
        id: "lossOfIncome",
        name: "Loss of Income",
        requiresBaseSalary: true,
      },
    ],
  },
  ALLOWANCE: {
    id: "allowance",
    label: "Allowance",
    components: [
      {
        id: "travel_allowance",
        name: "Travel Allowance",
        requiresBaseSalary: true,
        maxPercentOfBasic: 80,
      },
    ],
  },
  BENEFIT: {
    id: "benefit",
    label: "Benefit",
    components: [
      {
        id: "accommodation_benefit",
        name: "Accommodation Benefit",
        requiresBaseSalary: true,
      },
      {
        id: "bursaries_and_scholarships",
        name: "Bursaries and Scholarships",
        hasExemptPortion: true,
      },
      {
        id: "company_car",
        name: "Company Car",
        requiresBaseSalary: true,
      },
      {
        id: "company_car_under_operating_lease",
        name: "Company Car Under Operating Lease",
        requiresBaseSalary: true,
      },
    ],
  },
  DEDUCTION: {
    id: "deduction",
    label: "Deduction",
    components: [
      {
        id: "medical_aid",
        name: "Medical Aid",
        requiresBaseSalary: true,
        maxPercentOfBasic: 33.33,
        hasTaxCredit: true,
      },
      {
        id: "pension_fund",
        name: "Pension Fund",
        requiresBaseSalary: true,
        maxPercentOfBasic: 27.5,
        hasEmployerPortion: true,
      },
      {
        id: "provident_fund",
        name: "Provident Fund",
        requiresBaseSalary: true,
        maxPercentOfBasic: 27.5,
        hasEmployerPortion: true,
      },
      {
        id: "retirement_annuity_fund",
        name: "Retirement Annuity Fund",
        requiresBaseSalary: true,
        maxPercentOfBasic: 27.5,
      },
      {
        id: "garnishee",
        name: "Garnishee",
        requiresBaseSalary: true,
        maxPercentOfBasic: 25,
        requiresBeneficiary: true,
      },
    ],
  },
};

// Validation Rules
const VALIDATION_RULES = {
  LIMITS: {
    MIN_BASIC_SALARY: 0,
    MAX_DEDUCTION_PERCENT: 75, // Maximum total deductions as % of gross
    MIN_NET_PAY_PERCENT: 25, // Minimum net pay as % of gross
    PROCESSING_WINDOW_DAYS: 7, // Days before/after period end
  },

  DEPENDENCIES: {
    REQUIRES_BASIC_SALARY: [
      "commission",
      "travel_allowance",
      "medical_aid",
      "pension_fund",
      "provident_fund",
      "garnishee",
    ],
    REQUIRES_BENEFICIARY: [
      "garnishee",
      "medical_aid",
      "pension_fund",
      "provident_fund",
      "retirement_annuity_fund",
    ],
  },

  PERIOD_RULES: {
    MIN_DAYS_IN_PERIOD: 28,
    MAX_DAYS_IN_PERIOD: 31,
    WEEKLY_DAYS: 7,
    BIWEEKLY_DAYS: 14,
  },
};

// Status Definitions
const PERIOD_STATUSES = {
  OPEN: "open",
  PROCESSING: "processing",
  LOCKED: "locked",
  FINALIZED: "finalized",
};

// Error and Warning Codes
const VALIDATION_CODES = {
  ERRORS: {
    INVALID_AMOUNT: "ERR_INVALID_AMOUNT",
    EXCEEDS_LIMIT: "ERR_EXCEEDS_LIMIT",
    MISSING_DEPENDENCY: "ERR_MISSING_DEPENDENCY",
    INVALID_PERIOD: "ERR_INVALID_PERIOD",
    MISSING_BENEFICIARY: "ERR_MISSING_BENEFICIARY",
  },
  WARNINGS: {
    HIGH_AMOUNT: "WARN_HIGH_AMOUNT",
    LOW_NET_PAY: "WARN_LOW_NET_PAY",
    OUTSIDE_WINDOW: "WARN_OUTSIDE_WINDOW",
  },
};

module.exports = {
  INPUT_CATEGORIES,
  VALIDATION_RULES,
  PERIOD_STATUSES,
  VALIDATION_CODES,
};
