const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const validator = require("validator");
const moment = require("moment-timezone");

const EmployeeSchema = new mongoose.Schema({
  globalEmployeeId: {
    type: String,
    required: true,
    unique: true,
  },
  companyEmployeeNumber: {
    type: String,
    required: true,
  },
  firstName: String,
  lastName: String,
  costCentre: String,
  dob: {
    type: Date,
    set: function (val) {
      return val ? moment.utc(val).startOf("day").toDate() : null;
    },
    // Remove getter to allow raw date access for proper template formatting
  },
  idType: String,
  idNumber: {
    type: String,
    validate: {
      validator: function (v) {
        // Enhanced ID validation
        if (this.idType === 'rsa') {
          // Luhn algorithm implementation
          const validateLuhn = (id) => {
            if (!/^\d{13}$/.test(id)) return false;
            
            let sum = 0;
            let isEven = false;
            
            // Loop through values starting from the rightmost side
            for (let i = id.length - 1; i >= 0; i--) {
              let digit = parseInt(id[i]);
              
              if (isEven) {
                digit *= 2;
                if (digit > 9) {
                  digit -= 9;
                }
              }
              
              sum += digit;
              isEven = !isEven;
            }
            
            return (sum % 10) === 0;
          };
          
          return validateLuhn(v);
        }
        return true;
      },
      message: 'Invalid ID number format'
    }
  },
  passportNumber: String,
  email: String,
  mobileNumber: {
    type: String,
    required: false,
    validate: {
      validator: function(v) {
        // Basic phone number validation
        return !v || /^\d{10,15}$/.test(v.replace(/[+\s-]/g, ''));
      },
      message: 'Invalid mobile number format. Must be 10-15 digits.'
    }
  },
  payFrequency: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayFrequency",
    required: false,
    validate: {
      validator: function (v) {
        // Allow null/undefined values since it's not required
        if (!v) return true;
        // Check if it's a valid ObjectId
        return mongoose.Types.ObjectId.isValid(v);
      },
      message: (props) =>
        `${props.value} is not a valid PayFrequency reference!`,
    },
  },
  lastDayOfPeriod: {
    type: String,
    required: true,
  },
  interimDay: {
    type: Number,
  },
  firstPayrollPeriodEndDate: {
    type: Date,
    required: true,
  },
  doa: {
    type: Date,
    set: function (val) {
      return val ? moment.utc(val).startOf("day").toDate() : null;
    },
    // Remove getter to allow raw date access for proper template formatting
  },
  endOfMonthDate: {
    type: Date,
    set: function (val) {
      return val
        ? moment.utc(val).tz("Africa/Johannesburg").endOf("month").toDate()
        : null;
    },
    get: function (val) {
      return val
        ? moment.utc(val).tz("Africa/Johannesburg").format("YYYY/MM/DD")
        : null;
    },
  },

  jobTitle: String,

  password: {
    type: String,
  },
  selfServiceEnabled: {
    type: Boolean,
    default: false,
  },
  lastActivity: {
    type: Date,
    default: Date.now,
  },
  essEmailSent: {
    type: Boolean,
    default: false,
  },
  incomeTaxNumber: String,
  paymentMethod: String,
  // bank: String,
  // accountType: String,
  // accountNumber: String,
  // branchCode: String,
  // holderRelationship: String,
  unitNumber: String,
  complex: String,
  streetNumber: String,
  street: String,
  suburb: String,
  city: String,
  code: String,
  line1: String,
  line2: String,
  line3: String,
  hoursPerDay: Number,
  latestSelectedMonth: {
    type: Date,
    default: null,
  },
  payroll: {
    type: Object,
    required: true,
    default: {},
    // Define nested fields within payroll as needed
    fixedAllowance: { type: Number, default: 0 },
    travelPercentageAmount: { type: Number, default: 0 },
    // other payroll fields...
  },
  selectedMonth: {
    type: Date,
    default: Date.now,
  },
  paidMonths: [
    {
      type: Date,
    },
  ],
  employmentStatus: {
    type: Boolean,
    default: true,
  },

  reinstatementDate: {
    type: Date,
    default: null,
  },
  reinstatementReason: {
    type: String,
    default: null,
  },
  status: {
    type: String,
    enum: ["Active", "Inactive", "Serving Notice"],
    default: "Active",
  },
  terminationNoticeDate: {
    type: Date,
    default: null,
  },
  lastDayOfService: {
    type: Date,
    default: null,
  },
  uifStatusCode: {
    type: String,
    enum: [
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      null,
    ],
    default: null,
  },
  rehireEligibility: {
    type: Boolean,
    default: true,
  },
  exitInterviewDate: {
    type: Date,
    default: null,
  },
  lastPayrollDate: {
    type: Date,
    default: null,
  },
  servicePeriods: [
    {
      startDate: { type: Date, required: true },
      endDate: { type: Date },
      terminationReason: String,
    },
  ],
  oidEligible: {
    type: Boolean,
    default: true,
    description: "Whether the employee is eligible for OID returns",
  },
  oidExemptReason: {
    type: String,
    validate: {
      validator: function (v) {
        return this.oidEligible || (!this.oidEligible && v && v.trim().length > 0);
      },
      message: "OID exempt reason is required when employee is not OID eligible",
    },
  },
  oidEarnings: {
    type: [
      {
        period: {
          type: String,
          required: true,
        },
        amount: {
          type: Number,
          required: true,
          min: 0,
        },
        items: [
          {
            itemType: String, // e.g. 'basic_salary', 'overtime', 'allowance', 'commission'
            amount: Number,
            description: String,
          },
        ],
      },
    ],
    default: [],
  },
  oidInjuries: [
    {
      date: Date,
      description: String,
      severity: { type: String, enum: ["Minor", "Serious", "Fatal"] },
    },
  ],
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  employer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "EmployerDetails",
  },
  selfServiceToken: String,
  selfServiceTokenExpiration: Date,
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  phone: String,

  // New fields
  isDirector: {
    type: Boolean,
    default: false,
  },
  typeOfDirector: {
    type: String,
    enum: [
      "Executive Director/ Member of a CC",
      "RSA Resident Non-Executive Director",
      "Non Resident Non-Executive Director",
      null,
      "",
    ],
    default: null,
    validate: {
      validator: function (v) {
        // Allow null value when isDirector is false
        if (!this.isDirector) return true;
        // Must have a valid value when isDirector is true
        return v != null;
      },
      message: "Director type is required when isDirector is true",
    },
  },
  workingHours: {
    type: String,
    enum: ["Full Time", "Less Than 22 Hours a Week", ""],
    default: "Full Time",
  },
  regularHours: {
    hourlyPaid: {
      type: Boolean,
      default: false,
    },
    hoursPerDay: {
      type: Number,
      min: 0,
      max: 24,
      default: 8.0,
      validate: {
        validator: function (v) {
          return !isNaN(v) && v >= 0 && v <= 24;
        },
        message: "Hours per day must be between 0 and 24",
      },
    },
    schedule: {
      type: String,
      enum: ["Fixed", "Casual/Temp"],
      default: "Fixed",
    },
    workingDays: [
      {
        type: String,
        enum: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
    ],
    dayTypes: {
      Mon: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Tue: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Wed: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Thu: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Fri: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Sat: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
      Sun: {
        type: String,
        enum: ["Normal Day", "Half Day"],
        default: "Normal Day",
      },
    },
    fullDaysPerWeek: {
      type: Number,
      min: 0,
      max: 7,
      default: 5.0,
      validate: {
        validator: function (v) {
          return !isNaN(v) && v >= 0 && v <= 7;
        },
        message: "Full days per week must be between 0 and 7",
      },
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
  },
  isContractor: {
    type: Boolean,
    default: false,
  },
  isUifExempt: {
    type: Boolean,
    default: false,
  },
  uifExemptReason: {
    type: String,
    enum: [
      "Works less than 24 hours per month",
      "Public Servant",
      "Earns Commission Only",
      "Learner in terms of the SDL act",
      "Foreigner - Leaving South Africa at end of service",
      null,
    ],
    default: null,
    validate: {
      validator: function (v) {
        // Allow null value when isUifExempt is false
        if (!this.isUifExempt) return true;
        // Must have a valid value when isUifExempt is true
        return v != null;
      },
      message: "UIF exempt reason is required when isUifExempt is true",
    },
  },

  etiStatus: {
    type: String,
    enum: ["Qualified - Claiming", "Qualified - Not Claiming", "Disqualified"],
    default: "Disqualified",
  },
  etiEffectiveFrom: {
    type: Date,
    required: true,
    default: Date.now,
  },
  etiHistory: [
    {
      status: {
        type: String,
        enum: [
          "Qualified - Claiming",
          "Qualified - Not Claiming",
          "Disqualified",
        ],
      },
      effectiveDate: {
        type: Date,
        required: true,
      },
      updatedAt: {
        type: Date,
        default: Date.now,
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      reason: String, // Optional reason for status change
    },
  ],
  // ETI Qualification Criteria
  etiQualification: {
    monthlyRemuneration: {
      type: Number,
      default: 0,
    },
    ageAtEmployment: {
      type: Number,
      min: 0,
      max: 100,
    },
    employmentStartDate: Date,
    isEligible: {
      type: Boolean,
      default: false,
    },
    disqualificationReason: String,
    lastAssessmentDate: {
      type: Date,
      default: Date.now,
    },
  },
  // ETI Calculation Details
  etiCalculation: {
    monthlyAmount: {
      type: Number,
      default: 0,
    },
    yearToDate: {
      type: Number,
      default: 0,
    },
    totalMonthsClaimed: {
      type: Number,
      default: 0,
      min: 0,
      max: 24, // Maximum ETI claim period is 24 months
    },
    currentClaimPeriod: {
      startDate: Date,
      endDate: Date,
    },
  },
  gender: {
    type: String,
    enum: ["Male", "Female", ""],
  },
  race: {
    type: String,
    enum: ["African", "Coloured", "Indian", "White", ""],
  },
  occupationLevel: {
    type: String,
    enum: [
      "Top Management",
      "Senior Management",
      "Prof. specialists and mid-management",
      "Skilled workers, junior management, supervisors",
      "Semi-skilled and discretionary decision making",
      "Unskilled and defined decision making",
      "",
    ],
  },
  occupationCategory: {
    type: String,
    enum: [
      "Legislators, senior officials and managers",
      "Professionals",
      "Technicians and associate professionals",
      "Clerks",
      "Service and sales workers",
      "Skilled agricultural and fishery workers",
      "Craft and related trades workers",
      "Plant and machine operators and assemblers",
      "Elementary occupations",
      "",
    ],
  },
  jobValue: {
    type: String,
    enum: ["Operational / Core Function", "Support Function", ""],
  },
  province: {
    type: String,
    enum: [
      "Gauteng",
      "Eastern Cape",
      "Free State",
      "Kwa-Zulu Natal",
      "Mpumalanga",
      "Northern Cape",
      "Limpopo",
      "North West",
      "Western Cape",
      "",
    ],
  },
  disabled: Boolean,
  foreignNational: Boolean,
  notRSACitizen: Boolean,
  // uifExemptReason: {
  //   type: String,
  //   enum: [
  //     "Works less than 24 hours per month",
  //     "Public Servant",
  //     "Earns Commission Only",
  //     "Learner in terms of the SDL act - Not UIF exempt from 01 March 2018, but still SDL exempt",
  //     "Foreigner - Leaving South Africa at end of service - not applicable from 01 March 2018",
  //     null,
  //   ],
  //   default: null,
  // },
  providentFund: [
    {
      contributionCalculation: String,
      fixedContributionEmployee: Number,
      fixedContributionEmployer: Number,
      rfiEmployee: Number,
      rfiEmployer: Number,
      rfiComponents: [String],
      calculatedRFI: Number,
      effectiveDate: Date,
    },
  ],

  // Add this new field
  initialPayrollPeriodEndDate: {
    type: Date,
  },
  selfServiceSetup: {
    token: String,
    expires: Date,
  },
  role: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Role",
  },
  rfiConfig: {
    option: {
      type: String,
      enum: ["selectedIncome", "percentagePerIncome", ""],
      default: "",
    },
    selectedIncomes: [
      {
        componentId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "PayComponent",
        },
        name: String,
        includeInCalculation: {
          type: Boolean,
          default: true,
        },
      },
    ],
    percentageIncomes: [
      {
        componentId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "PayComponent",
        },
        name: String,
        percentage: {
          type: Number,
          min: 0,
          max: 100,
          default: 100,
        },
      },
    ],
    lastCalculation: {
      date: Date,
      amount: Number,
      components: [
        {
          name: String,
          amount: Number,
          percentage: Number,
          includedAmount: Number,
        },
      ],
    },
    history: [
      {
        date: Date,
        calculatedAmount: Number,
        components: [
          {
            name: String,
            amount: Number,
            percentage: Number,
            includedAmount: Number,
          },
        ],
      },
    ],
  },
  payrollSettings: {
    hourlyRate: {
      type: Number,
      min: 0,
      default: 0,
    },
    dontAutoPayPublicHolidays: {
      type: Boolean,
      default: false,
    },
    paidForAdditionalHours: {
      type: Boolean,
      default: false,
    },
    overrideCalculatedHourlyRate: {
      type: Boolean,
      default: false,
    },
    rateOverride: {
      type: Number,
      min: 0,
      default: 0,
    },
  },
  // ETI Related Fields
  etiStatus: {
    type: String,
    enum: ["Qualified - Claiming", "Qualified - Not Claiming", "Disqualified"],
    default: "Disqualified",
  },
  etiEffectiveFrom: {
    type: Date,
    required: true,
    default: Date.now,
  },
  etiHistory: [
    {
      status: {
        type: String,
        enum: [
          "Qualified - Claiming",
          "Qualified - Not Claiming",
          "Disqualified",
        ],
      },
      effectiveDate: {
        type: Date,
        required: true,
      },
      updatedAt: {
        type: Date,
        default: Date.now,
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },
  ],
  // ETI Qualification Criteria
  monthlyRemuneration: Number,
  ageAtEmployment: Number,
  employmentStartDate: Date,
  isETIEligible: {
    type: Boolean,
    default: false,
  },
  temporaryAbsence: {
    expectedReturnDate: {
      type: Date,
      default: null,
    },
    paidDuringAbsence: {
      type: Boolean,
      default: false,
    },
  },
  department: {
    type: String,
    trim: true,
  },
  accountHolder: {
    type: String,
    enum: ["Own", "Joint", "Third Party"],
    default: "Own",
  },
  streetAddress: {
    type: String,
    trim: true,
  },
  suburb: {
    type: String,
    trim: true,
  },
  city: {
    type: String,
    trim: true,
  },
  postalCode: {
    type: String,
    trim: true,
  },
  bank: {
    type: String,
    trim: true,
  },
  accountType: {
    type: String,
    enum: ["Current", "Savings", "Transmission"],
  },
  accountNumber: {
    type: String,
    trim: true,
  },
  branchCode: {
    type: String,
    trim: true,
  },
  holderRelationship: {
    type: String,
    trim: true,
  },

  personalDetails: {
    type: {
      idType: {
        type: String,
        enum: ['rsa', 'passport', 'other', 'none'],
        required: true
      },
      idNumber: {
        type: String,
        validate: {
          validator: function(v) {
            // Enhanced ID validation
            if (this.idType === 'rsa') {
              // Luhn algorithm implementation
              const validateLuhn = (id) => {
                if (!/^\d{13}$/.test(id)) return false;
                
                let sum = 0;
                let isEven = false;
                
                // Loop through values starting from the rightmost side
                for (let i = id.length - 1; i >= 0; i--) {
                  let digit = parseInt(id[i]);
                  
                  if (isEven) {
                    digit *= 2;
                    if (digit > 9) {
                      digit -= 9;
                    }
                  }
                  
                  sum += digit;
                  isEven = !isEven;
                }
                
                return (sum % 10) === 0;
              };
              
              return validateLuhn(v);
            }
            return true;
          },
          message: 'Invalid ID number format'
        }
      },
      mobileNumber: {
        type: String,
        required: false,
        validate: {
          validator: function(v) {
            // Basic phone number validation - allow empty or 10-15 digits
            return !v || /^\d{10,15}$/.test(v.replace(/[+\s-]/g, ''));
          },
          message: 'Invalid mobile number format. Must be 10-15 digits.'
        }
      },
      nationality: String,
      countryOfBirth: String,
      taxResidence: String
    },
    default: {}
  },

  // bankDetails: {
  //   type: {
  //     bank: {
  //       type: String,
  //       required: [true, 'Bank name is required']
  //     },
  //     accountNumber: {
  //       type: String,
  //       required: [true, 'Account number is required'],
  //       validate: {
  //         validator: function(v) {
  //           // Basic account number validation
  //           return /^\d{7,12}$/.test(v);
  //         },
  //         message: 'Invalid account number format'
  //       }
  //     },
  //     branchCode: {
  //       type: String,
  //       required: [true, 'Branch code is required'],
  //       validate: {
  //         validator: function(v) {
  //           // Basic branch code validation
  //           return /^\d{6}$/.test(v);
  //         },
  //         message: 'Invalid branch code format'
  //       }
  //     },
  //     accountType: {
  //       type: String,
  //       enum: ['cheque', 'savings', 'transmission', 'other'],
  //       required: [true, 'Account type is required']
  //     },
  //     holderName: {
  //       type: String,
  //       required: [true, 'Account holder name is required']
  //     },
  //     holderRelationship: {
  //       type: String,
  //       enum: ['self', 'spouse', 'parent', 'other'],
  //       default: 'self'
  //     },
  //     verificationStatus: {
  //       type: String,
  //       enum: ['unverified', 'pending', 'verified', 'invalid'],
  //       default: 'unverified'
  //     },
  //     lastVerifiedDate: Date,
  //     notes: String
  //   },
  //   default: {}
  // },

  whatsapp: {
    enabled: {
      type: Boolean,
      default: false
    },
    verified: {
      type: Boolean,
      default: false
    },
    lastInteraction: {
      type: Date,
      default: null
    },
    preferences: {
      receivePayslipNotifications: {
        type: Boolean,
        default: true
      },
      receiveLeaveUpdates: {
        type: Boolean,
        default: true
      },
      receiveGeneralAnnouncements: {
        type: Boolean,
        default: true
      }
    }
  }
});

// Remove any existing indexes
// EmployeeSchema.index({ employeeNumber: 1 }, { unique: true, sparse: true });

// Update the compound index section in the Employee model
// Remove any existing single index on companyEmployeeNumber
EmployeeSchema.index(
  { companyEmployeeNumber: 1 },
  { unique: false }
);

// Add the compound unique index for company + companyEmployeeNumber
EmployeeSchema.index(
  { company: 1, companyEmployeeNumber: 1 },
  { unique: true }
);

EmployeeSchema.pre("save", function (next) {
  if (this.isModified("doa")) {
    // CRITICAL FIX: Use UTC-first approach to prevent double timezone conversion
    this.endOfMonthDate = moment.utc(this.doa).tz("Africa/Johannesburg").endOf("month").toDate();
  }
  next();
});

// Method to calculate age
EmployeeSchema.methods.calculateAge = function () {
  const today = new Date();
  const birthDate = new Date(this.dob);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// Add a new method to check and update employee status
EmployeeSchema.methods.updateStatusBasedOnLastDayOfService = function () {
  const today = new Date();
  if (
    this.lastDayOfService &&
    this.lastDayOfService <= today &&
    this.status !== "Inactive"
  ) {
    this.status = "Inactive";
    return true; // Status was updated
  }
  return false; // No update was necessary
};

// Modify the existing pre-save middleware
EmployeeSchema.pre("save", function (next) {
  if (
    this.isModified("lastDayOfService") &&
    this.lastDayOfService > new Date()
  ) {
    this.status = "Serving Notice";
  }
  this.updateStatusBasedOnLastDayOfService();
  next();
});

EmployeeSchema.virtual("payFrequencyName").get(function () {
  return this.payFrequency ? this.payFrequency.name : "N/A";
});

// Make sure to include virtuals when you're converting to JSON
EmployeeSchema.set("toJSON", { virtuals: true });
EmployeeSchema.set("toObject", { virtuals: true });

// Pre-save middleware to update lastUpdated
EmployeeSchema.pre("save", function (next) {
  if (this.isModified("regularHours")) {
    this.regularHours.lastUpdated = new Date();
  }
  next();
});

// Method to calculate actual working hours
EmployeeSchema.methods.calculateWorkingHours = function () {
  let totalHours = 0;

  this.regularHours.workingDays.forEach((day) => {
    if (day.active) {
      if (day.type === "Normal Day") {
        totalHours += this.regularHours.hoursPerDay;
      } else if (day.type === "Half Day") {
        totalHours += this.regularHours.hoursPerDay / 2;
      }
    }
  });

  return totalHours;
};

// Method to add history entry
EmployeeSchema.methods.addRegularHoursHistory = function (userId) {
  const historyEntry = {
    hourlyPaid: this.regularHours.hourlyPaid,
    hoursPerDay: this.regularHours.hoursPerDay,
    schedule: this.regularHours.schedule,
    workingDays: this.regularHours.workingDays,
    fullDaysPerWeek: this.regularHours.fullDaysPerWeek,
    effectiveDate: new Date(),
    updatedBy: userId,
  };

  if (!this.regularHours.history) {
    this.regularHours.history = [];
  }

  this.regularHours.history.push(historyEntry);
};

// Add this near your other pre-save middleware
EmployeeSchema.pre("save", function (next) {
  next();
});

// Add a virtual property to get the termination reason
EmployeeSchema.virtual("terminationReason").get(function () {
  const reasons = {
    2: "Deceased",
    3: "Retired",
    4: "Dismissed",
    5: "Contract Expired",
    6: "Resigned",
    7: "Constructive Dismissal",
    8: "Insolvency/Liquidation",
    9: "Maternity/Adoption",
    10: "Long-term illness",
    11: "Retrenched/Staff Reduction",
    12: "Transfer to another Branch",
    13: "Absconded",
    14: "Business Closed",
    15: "Death of Domestic Employer",
    16: "Voluntary Severance Package (VSP)",
    17: "Reduced Working Time",
    18: "Commissioning Parental",
    19: "Parental",
  };
  return this.uifStatusCode ? reasons[this.uifStatusCode] : null;
});

// Add a method to check if a UIF code is for temporary absence
EmployeeSchema.methods.isTemporaryAbsence = function () {
  const temporaryAbsenceCodes = ["9", "10", "18", "19"];
  return temporaryAbsenceCodes.includes(this.uifStatusCode);
};

// Add these methods to the Employee model
EmployeeSchema.methods.isDateBeyondTermination = function (date) {
  if (!this.lastDayOfService) {
    return false;
  }

  const checkDate = new Date(date);
  const terminationDate = new Date(this.lastDayOfService);


  const isBeyond = checkDate > terminationDate;

  return isBeyond;
};

EmployeeSchema.methods.getLastValidPayrollDate = function () {
  if (!this.lastDayOfService) {
    return null;
  }

  const terminationDate = new Date(this.lastDayOfService);

  // If terminated mid-month, return end of that month as last valid date
  const lastValid = new Date(
    terminationDate.getFullYear(),
    terminationDate.getMonth() + 1,
    0
  );

  return lastValid;
};

// Add a method to check if payroll can be processed
EmployeeSchema.methods.canProcessPayroll = function (periodEndDate) {

  // If employee is active, allow processing
  if (this.status === "Active") {
    return true;
  }

  // If no termination date, allow processing
  if (!this.lastDayOfService) {
    return true;
  }

  const endDate = new Date(periodEndDate);
  const terminationDate = new Date(this.lastDayOfService);
  const lastValidDate = this.getLastValidPayrollDate();


  // Check if period end date is within allowed range
  const isValid = endDate <= lastValidDate;

  return isValid;
};

// Add a method to get payroll processing status message
EmployeeSchema.methods.getPayrollProcessingStatus = function (periodEndDate) {
  if (!this.lastDayOfService) {
    return {
      canProcess: true,
      message: "Employee is active",
    };
  }

  const endDate = new Date(periodEndDate);
  const terminationDate = new Date(this.lastDayOfService);

  if (endDate > terminationDate) {
    return {
      canProcess: false,
      message: `Cannot process payroll after termination date (${terminationDate.toLocaleDateString()})`,
    };
  }

  return {
    canProcess: true,
    message: "Period is within valid range",
  };
};

// Add a method to validate bank details
EmployeeSchema.methods.validateBankDetails = function() {
  const bankDetails = this.bankDetails || {};
  const requiredFields = [
    'bank', 
    'accountNumber', 
    'branchCode', 
    'accountType', 
    'holderName'
  ];

  const missingFields = requiredFields.filter(field => 
    !bankDetails[field] || bankDetails[field].trim() === ''
  );

  return {
    isValid: missingFields.length === 0,
    missingFields: missingFields,
    verificationStatus: bankDetails.verificationStatus || 'unverified'
  };
};

// Add a method to update bank details verification status
EmployeeSchema.methods.updateBankDetailsVerification = function(status, notes = '') {
  if (!this.bankDetails) {
    this.bankDetails = {};
  }

  this.bankDetails.verificationStatus = status;
  this.bankDetails.lastVerifiedDate = new Date();
  
  if (notes) {
    this.bankDetails.notes = notes;
  }

  return this.save();
};

module.exports =
  mongoose.models.Employee || mongoose.model("Employee", EmployeeSchema);
