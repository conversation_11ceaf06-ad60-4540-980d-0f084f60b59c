const mongoose = require("mongoose");
const moment = require("moment-timezone");
const BusinessDate = require("../utils/BusinessDate");

const payrollPeriodSchema = new mongoose.Schema({
  // Legacy Date fields - maintained for backward compatibility
  startDate: {
    type: Date,
    required: true,
    set: function (val) {
      // SIMPLE FIX: Store dates as-is without timezone conversion to prevent +1 day issues
      if (typeof val === 'string') {
        // For YYYY-MM-DD strings, create date at midnight UTC
        return new Date(val + 'T00:00:00.000Z');
      }
      return new Date(val);
    },
    validate: {
      validator: function (v) {
        return moment(v).isValid();
      },
      message: "Invalid start date",
    },
  },
  endDate: {
    type: Date,
    required: true,
    set: function (val) {
      // SIMPLE FIX: Store dates as-is without timezone conversion to prevent +1 day issues
      if (typeof val === 'string') {
        // For YYYY-MM-DD strings, create date at end of day UTC
        return new Date(val + 'T23:59:59.999Z');
      }
      return new Date(val);
    },
    validate: {
      validator: function (v) {
        return moment(v).isValid() && moment(v).isSameOrAfter(this.startDate);
      },
      message: "End date must be valid and after start date",
    },
  },

  // New Business Date fields - timezone-independent YYYY-MM-DD strings
  startDateBusiness: {
    type: String,
    validate: {
      validator: function (v) {
        return !v || BusinessDate.isValid(v);
      },
      message: "Invalid business start date format. Expected YYYY-MM-DD",
    },
  },
  endDateBusiness: {
    type: String,
    validate: {
      validator: function (v) {
        if (!v) return true;
        if (!BusinessDate.isValid(v)) return false;
        if (this.startDateBusiness) {
          return BusinessDate.isSameOrAfter(v, this.startDateBusiness);
        }
        return true;
      },
      message: "Invalid business end date format or end date before start date",
    },
  },
  frequency: {
    type: String,
    required: true,
    enum: ["weekly", "biweekly", "monthly"],
    default: "monthly",
    set: function (val) {
      // Normalize bi-weekly to biweekly
      return val === "bi-weekly" ? "biweekly" : val;
    },
  },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  payslip: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Payslip"
  },
  status: {
    type: String,
    enum: ["open", "processing", "finalized", "locked"],
    default: "open",
  },
  isFinalized: {
    type: Boolean,
    default: false,
  },
  basicSalary: {
    type: Number,
    default: 0,
  },
  grossPay: {
    type: Number,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    default: 0,
  },
  netPay: {
    type: Number,
    default: 0,
  },
  PAYE: {
    type: Number,
    default: 0,
  },
  UIF: {
    type: Number,
    default: 0,
  },
  SDL: {
    type: Number,
    default: 0,
  },
  // Finalization tracking fields
  finalizedAt: {
    type: Date,
  },
  finalizedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  // Unfinalization tracking fields
  unfinalizedAt: {
    type: Date,
  },
  unfinalizedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  // Pay run relationship
  payRun: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayRun",
  },
});

// Middleware to sync between Date and BusinessDate fields
payrollPeriodSchema.pre('save', function(next) {
  // Sync Date fields to BusinessDate fields
  if (this.startDate && !this.startDateBusiness) {
    this.startDateBusiness = BusinessDate.fromDate(this.startDate);
  }
  if (this.endDate && !this.endDateBusiness) {
    this.endDateBusiness = BusinessDate.fromDate(this.endDate);
  }

  // Sync BusinessDate fields to Date fields (for backward compatibility)
  if (this.startDateBusiness && !this.startDate) {
    this.startDate = BusinessDate.toDate(this.startDateBusiness);
  }
  if (this.endDateBusiness && !this.endDate) {
    this.endDate = BusinessDate.toDate(this.endDateBusiness, true);
  }

  next();
});

// Virtual getters for business dates (preferred interface)
payrollPeriodSchema.virtual('businessStartDate').get(function() {
  return this.startDateBusiness || BusinessDate.fromDate(this.startDate);
});

payrollPeriodSchema.virtual('businessEndDate').get(function() {
  return this.endDateBusiness || BusinessDate.fromDate(this.endDate);
});

payrollPeriodSchema.index(
  {
    employee: 1,
    startDate: 1,
    endDate: 1,
    frequency: 1,
  },
  {
    unique: true,
  }
);

// Add index for business date fields
payrollPeriodSchema.index(
  {
    employee: 1,
    startDateBusiness: 1,
    endDateBusiness: 1,
    frequency: 1,
  }
);

payrollPeriodSchema.statics.createInitialPeriod = async function (employee) {

  // Validate required data
  if (!employee.doa) {
    throw new Error("Employee must have date of appointment (doa) set");
  }
  if (!employee.payFrequency) {
    throw new Error("Employee must have pay frequency set");
  }
  if (!employee.payFrequency.frequency) {
    throw new Error("Pay frequency must have frequency set");
  }
  if (!employee.payFrequency.lastDayOfPeriod) {
    console.error("Pay Frequency Validation Error:", {
      payFrequencyId: employee.payFrequency._id,
      frequency: employee.payFrequency.frequency,
      lastDayOfPeriod: employee.payFrequency.lastDayOfPeriod,
    });
    throw new Error("Pay frequency must have lastDayOfPeriod set");
  }

  // Use BusinessDate for timezone-independent calculations
  const startDateBusiness = BusinessDate.normalize(employee.doa);
  let endDateBusiness;

  if (employee.payFrequency.frequency === "weekly") {

    // Use BusinessDate for timezone-independent weekly calculation
    endDateBusiness = BusinessDate.calculatePeriodEndDate(startDateBusiness, employee.payFrequency);

  } else if (employee.payFrequency.frequency === "monthly") {
    endDateBusiness = BusinessDate.calculatePeriodEndDate(startDateBusiness, employee.payFrequency);
  } else {
    // biweekly
    endDateBusiness = BusinessDate.addDays(startDateBusiness, 13);
  }


  // Calculate tax deductions for initial period
  // Get basic salary from the most recent Payroll record, not from employee object
  const Payroll = require('./Payroll');
  const latestPayroll = await Payroll.findOne({
    employee: employee._id,
    company: employee.company,
    basicSalary: { $exists: true, $gt: 0 }
  }).sort({ month: -1 });

  const basicSalary = latestPayroll?.basicSalary || employee.basicSalary || 0;
  let paye = 0;
  let uif = 0;
  let sdl = 0;
  let totalDeductions = 0;
  let netPay = basicSalary;

  // Calculate gross pay including all income components
  let grossPay = basicSalary;

  if (latestPayroll) {
    // Add travel allowance if exists
    if (latestPayroll.travelAllowance?.fixedAllowanceAmount) {
      grossPay += latestPayroll.travelAllowance.fixedAllowanceAmount;
    }

    // Add commission if exists
    if (latestPayroll.commission) {
      grossPay += latestPayroll.commission;
    }

    // Add loss of income if exists
    if (latestPayroll.lossOfIncome) {
      grossPay += latestPayroll.lossOfIncome;
    }

    // Add accommodation benefit if exists
    if (latestPayroll.accommodationBenefit) {
      grossPay += parseFloat(latestPayroll.accommodationBenefit);
    }
  }


  if (basicSalary > 0) {
    try {
      // Import calculation functions
      const { calculateEnhancedPAYE, calculatePeriodUIF } = require('../utils/payrollCalculations');
      const payrollCalculations = require('../utils/payrollCalculations');

      // Calculate PAYE
      const annualSalary = basicSalary * 12;
      const payeResult = await calculateEnhancedPAYE({
        annualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency.frequency,
      });
      paye = Number(payeResult.monthlyPAYE || 0);

      // Calculate UIF
      uif = calculatePeriodUIF(basicSalary, employee.payFrequency.frequency);

      // Calculate SDL (Skills Development Levy) - 1% of basic salary
      sdl = basicSalary * 0.01;

      // Calculate totals
      totalDeductions = paye + uif + sdl;
      netPay = grossPay - totalDeductions;

    } catch (error) {
      console.error("Error calculating initial period taxes:", error);
      // Continue with zero values if calculation fails
    }
  }

  // Create and return the period with both Date and BusinessDate fields
  const period = await this.create({
    employee: employee._id,
    company: employee.company,
    // Legacy Date fields for backward compatibility
    startDate: BusinessDate.toDate(startDateBusiness),
    endDate: BusinessDate.toDate(endDateBusiness, true),
    // New BusinessDate fields (preferred)
    startDateBusiness: startDateBusiness,
    endDateBusiness: endDateBusiness,
    frequency: employee.payFrequency.frequency,
    basicSalary: basicSalary,
    grossPay: grossPay,
    PAYE: paye,
    UIF: uif,
    SDL: sdl,
    totalDeductions: totalDeductions,
    netPay: netPay,
    status: "open",
    isFinalized: false,
  });


  return period;
};

payrollPeriodSchema.statics.generateNextPeriod = async function (
  employee,
  lastPeriod
) {
  // Validate inputs
  if (!employee || !lastPeriod) {
    throw new Error("Employee and last period are required");
  }

  // Check if employee is terminating
  if (
    employee.terminationDate &&
    moment(employee.terminationDate).isSameOrBefore(lastPeriod.endDate)
  ) {
    return null;
  }

  // Use BusinessDate for timezone-independent calculations
  const lastPeriodEndBusiness = lastPeriod.businessEndDate;
  const startDateBusiness = BusinessDate.addDays(lastPeriodEndBusiness, 1);
  let endDateBusiness;

  // Ensure pay frequency is populated
  if (!employee.payFrequency || !employee.payFrequency.frequency) {
    throw new Error(
      `Pay frequency not set for employee ${employee.firstName} ${employee.lastName}`
    );
  }

  // Use BusinessDate for all frequency calculations
  endDateBusiness = BusinessDate.calculateNextPeriodEndDate(lastPeriodEndBusiness, employee.payFrequency);


  // Check for duplicate period using BusinessDate fields
  const existingPeriod = await this.findOne({
    employee: employee._id,
    $or: [
      // Check using BusinessDate fields (preferred)
      {
        startDateBusiness: startDateBusiness,
        endDateBusiness: endDateBusiness,
      },
      // Fallback to legacy Date fields for backward compatibility
      {
        startDate: BusinessDate.toDate(startDateBusiness),
        endDate: BusinessDate.toDate(endDateBusiness, true),
      }
    ]
  });

  if (existingPeriod) {
    return existingPeriod;
  }

  // Calculate tax deductions for next period
  // Get basic salary from the most recent Payroll record or last period
  const Payroll = require('./Payroll');
  const latestPayroll = await Payroll.findOne({
    employee: employee._id,
    company: employee.company,
    basicSalary: { $exists: true, $gt: 0 }
  }).sort({ month: -1 });

  const basicSalary = latestPayroll?.basicSalary || lastPeriod.basicSalary || employee.basicSalary || 0;
  let paye = 0;
  let uif = 0;
  let sdl = 0;
  let totalDeductions = 0;
  let netPay = basicSalary;

  // Calculate gross pay including all income components
  let grossPay = basicSalary;

  if (latestPayroll) {
    // Add travel allowance if exists
    if (latestPayroll.travelAllowance?.fixedAllowanceAmount) {
      grossPay += latestPayroll.travelAllowance.fixedAllowanceAmount;
    }

    // Add commission if exists
    if (latestPayroll.commission) {
      grossPay += latestPayroll.commission;
    }

    // Add loss of income if exists
    if (latestPayroll.lossOfIncome) {
      grossPay += latestPayroll.lossOfIncome;
    }

    // Add accommodation benefit if exists
    if (latestPayroll.accommodationBenefit) {
      grossPay += parseFloat(latestPayroll.accommodationBenefit);
    }
  } else if (lastPeriod.grossPay && lastPeriod.grossPay > lastPeriod.basicSalary) {
    // If no payroll record but last period had additional income components, use that gross pay
    grossPay = lastPeriod.grossPay;
  }


  if (basicSalary > 0) {
    try {
      // Import calculation functions
      const { calculateEnhancedPAYE, calculatePeriodUIF } = require('../utils/payrollCalculations');
      const payrollCalculations = require('../utils/payrollCalculations');

      // Calculate PAYE
      const annualSalary = basicSalary * 12;
      const payeResult = await calculateEnhancedPAYE({
        annualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency.frequency,
      });
      paye = Number(payeResult.monthlyPAYE || 0);

      // Calculate UIF
      uif = calculatePeriodUIF(basicSalary, employee.payFrequency.frequency);

      // Calculate SDL (Skills Development Levy) - 1% of basic salary
      sdl = basicSalary * 0.01;

      // Calculate totals
      totalDeductions = paye + uif + sdl;
      netPay = grossPay - totalDeductions;

    } catch (error) {
      console.error("Error calculating next period taxes:", error);
      // Continue with zero values if calculation fails
    }
  }

  // Create and return the new period with both Date and BusinessDate fields
  return this.create({
    employee: employee._id,
    company: employee.company,
    // Legacy Date fields for backward compatibility
    startDate: BusinessDate.toDate(startDateBusiness),
    endDate: BusinessDate.toDate(endDateBusiness, true),
    // New BusinessDate fields (preferred)
    startDateBusiness: startDateBusiness,
    endDateBusiness: endDateBusiness,
    frequency: employee.payFrequency.frequency,
    basicSalary: basicSalary,
    grossPay: grossPay,
    PAYE: paye,
    UIF: uif,
    SDL: sdl,
    totalDeductions: totalDeductions,
    netPay: netPay,
    status: "open",
    isFinalized: false,
  });
};

// Static method to validate unfinalization constraints
payrollPeriodSchema.statics.validateUnfinalization = async function(employeeId, company, periodEndDate) {
  const validationResult = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  try {
    // Check if any newer periods are finalized
    const newerFinalized = await this.findOne({
      employee: employeeId,
      company: company,
      endDate: { $gt: new Date(periodEndDate) },
      isFinalized: true,
    });

    if (newerFinalized) {
      validationResult.errors.push(
        "Cannot unfinalize period - newer periods are finalized. Unfinalize newer periods first."
      );
      validationResult.isValid = false;
    }

    // Check if period has an associated pay run
    const PayRun = mongoose.model("PayRun");
    const associatedPayRun = await PayRun.findOne({
      payrollPeriods: { $in: [periodEndDate] },
      company: company,
      status: { $nin: ["deleted", "cancelled"] },
    });

    if (associatedPayRun) {
      validationResult.errors.push(
        "Cannot unfinalize period - pay run exists. Delete the pay run first."
      );
      validationResult.isValid = false;
    }

    return validationResult;
  } catch (error) {
    console.error("Error validating unfinalization:", error);
    validationResult.isValid = false;
    validationResult.errors.push("Validation error occurred");
    return validationResult;
  }
};

// Static method to check if period can be unfinalized
payrollPeriodSchema.statics.canUnfinalize = async function(periodId) {
  try {
    const period = await this.findById(periodId);
    if (!period || !period.isFinalized) {
      return { canUnfinalize: false, reason: "Period not found or not finalized" };
    }

    if (period.status === "locked") {
      return { canUnfinalize: false, reason: "Period is locked by pay run" };
    }

    const validation = await this.validateUnfinalization(
      period.employee,
      period.company,
      period.endDate
    );

    return {
      canUnfinalize: validation.isValid,
      reason: validation.errors.join("; "),
      warnings: validation.warnings,
    };
  } catch (error) {
    console.error("Error checking unfinalization eligibility:", error);
    return { canUnfinalize: false, reason: "Error checking eligibility" };
  }
};

module.exports =
  mongoose.models.PayrollPeriod ||
  mongoose.model("PayrollPeriod", payrollPeriodSchema);
