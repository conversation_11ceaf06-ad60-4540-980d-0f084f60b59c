const mongoose = require("mongoose");

const payrollCalculationSchema = new mongoose.Schema({
  payroll: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Payroll",
    required: true,
  },
  calculationType: {
    type: String,
    required: true,
  },
  result: mongoose.Schema.Types.Mixed
});

module.exports = mongoose.models.PayrollCalculation || 
  mongoose.model("PayrollCalculation", payrollCalculationSchema);
