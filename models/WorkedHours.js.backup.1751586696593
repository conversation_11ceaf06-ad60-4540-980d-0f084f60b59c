const mongoose = require("mongoose");

const workedHoursSchema = new mongoose.Schema({
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  period: {
    startDate: Date,
    endDate: Date,
  },
  normalHours: {
    type: Number,
    default: 0,
  },
  overtimeHours: {
    type: Number,
    default: 0,
  },
  dailyHours: [
    {
      date: Date,
      hours: Number,
      type: {
        type: String,
        enum: ["Normal", "Overtime", "Public Holiday", "Sunday"],
      },
    },
  ],
  status: {
    type: String,
    enum: ["Draft", "Submitted", "Approved"],
    default: "Draft",
  },
});

module.exports = mongoose.model("WorkedHours", workedHoursSchema);
