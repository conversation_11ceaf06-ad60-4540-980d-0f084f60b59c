const mongoose = require('mongoose');

const EmployeeReportAutomationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  enabled: {
    type: Boolean,
    default: false
  },
  frequency: {
    type: String,
    enum: ['weekly', 'biWeekly', 'monthly'],
    default: 'monthly'
  },
  reportTypes: [{
    type: String,
    enum: ['headcount', 'performance', 'compensation']
  }],
  preferredEmail: {
    type: String,
    required: function() { return this.enabled; }
  },
  lastGeneratedAt: {
    type: Date
  },
  nextScheduledGeneration: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Ensure unique automation settings per user
EmployeeReportAutomationSchema.index({ userId: 1 }, { unique: true });

// Update timestamp on save
EmployeeReportAutomationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Validate report types
EmployeeReportAutomationSchema.pre('save', function(next) {
  if (this.enabled && (!this.reportTypes || this.reportTypes.length === 0)) {
    return next(new Error('At least one report type must be selected when automation is enabled'));
  }
  next();
});

module.exports = mongoose.model('EmployeeReportAutomation', EmployeeReportAutomationSchema);
