const mongoose = require("mongoose");

const employerDetailsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  tradingName: {
    type: String,
    required: true,
    trim: true,
  },
  physicalAddress: {
    unitNumber: {
      type: String,
      trim: true,
    },
    complex: {
      type: String,
      trim: true,
    },
    streetNumber: {
      type: String,
      trim: true,
    },
    street: {
      type: String,
      trim: true,
    },
    suburbDistrict: {
      type: String,
      trim: true,
    },
    cityTown: {
      type: String,
      trim: true,
    },
    code: {
      type: String,
      trim: true,
    },
  },
  postalAddress: {
    line1: {
      type: String,
      trim: true,
    },
    line2: {
      type: String,
      trim: true,
    },
    line3: {
      type: String,
      trim: true,
    },
    code: {
      type: String,
      trim: true,
    },
  },
  logo: {
    type: String,
    trim: true,
    get: function (v) {
      return v ? `/uploads/${v.split("/").pop()}` : null;
    },
  },
});

// Update the 'updatedAt' field on save
employerDetailsSchema.pre("save", function (next) {
  this.updatedAt = Date.now();
  next();
});

// Add pre-save hook to update company name
employerDetailsSchema.pre("save", async function (next) {
  if (this.isModified("tradingName")) {
    try {
      await mongoose
        .model("Company")
        .findByIdAndUpdate(this.company, { name: this.tradingName });
    } catch (error) {
      console.error("Error updating company name:", error);
    }
  }
  next();
});

const EmployerDetails =
  mongoose.models.EmployerDetails ||
  mongoose.model("EmployerDetails", employerDetailsSchema);
module.exports = EmployerDetails;
