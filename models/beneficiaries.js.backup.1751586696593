const mongoose = require("mongoose");

const beneficiarySchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: [
      "medical-aid",
      "pension-fund",
      "provident-fund",
      "garnishee",
      "maintenance-order",
    ],
    required: true,
  },
  registrationNumber: String,
  accountDetails: {
    bank: String,
    accountNumber: String,
    branchCode: String,
    accountType: String,
  },
  active: {
    type: Boolean,
    default: true,
  },
});

module.exports =
  mongoose.models.Beneficiary ||
  mongoose.model("Beneficiary", beneficiarySchema);
