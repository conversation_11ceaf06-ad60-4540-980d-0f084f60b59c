const mongoose = require("mongoose");

const terminationCertificateSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  periodIndex: {
    type: Number,
    required: true,
  },
  basicSalary: {
    type: Number,
    default: 0,
  },
  totalHours: {
    type: Number,
    default: 173.33,
  },
  employerName: {
    type: String,
    default: "",
  },
  employerIdNumber: {
    type: String,
    default: "",
  },
  signature: {
    type: {
      type: String,
      enum: ["draw", "type", "upload"],
      default: "draw",
    },
    data: String,
    date: Date,
  },
  stamp: {
    data: String,
    uploadDate: Date,
  },
  lastModified: {
    type: Date,
    default: Date.now,
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
});

// Ensure one record per employee per service period
terminationCertificateSchema.index(
  { company: 1, employee: 1, periodIndex: 1 },
  { unique: true }
);

module.exports = mongoose.model(
  "TerminationCertificate",
  terminationCertificateSchema
);
