const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const filingSchema = new Schema(
  {
    company: {
      type: Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    submissionType: {
      type: String,
      required: true,
      enum: ["EMP201", "EMP501", "OID"],
    },
    submissionPeriod: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: ["Draft", "Submitted", "Overdue"],
      default: "Draft",
    },
    submissionDeadline: {
      type: Date,
      required: true,
    },
    totalPAYE: {
      type: Number,
      required: true,
      default: 0,
    },
    totalUIF: {
      type: Number,
      required: true,
      default: 0,
    },
    totalSDL: {
      type: Number,
      required: true,
      default: 0,
    },
    employeeCount: {
      type: Number,
      required: true,
      default: 0,
    },
    submissionData: {
      type: Schema.Types.Mixed,
      required: true,
    },
    certificateNumber: {
      type: String,
      required: true,
      unique: true,
    },
    submittedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Add method to calculate totals
filingSchema.methods.calculateTotals = function () {
  if (this.submissionData && this.submissionData.consolidatedTotals) {
    this.totalPAYE = this.submissionData.consolidatedTotals.PAYE || 0;
    this.totalUIF = this.submissionData.consolidatedTotals.UIF || 0;
    this.totalSDL = this.submissionData.consolidatedTotals.SDL || 0;
  }
};

// Pre-save middleware to ensure totals are calculated
filingSchema.pre("save", function (next) {
  this.calculateTotals();
  next();
});

module.exports = mongoose.model("Filing", filingSchema);
