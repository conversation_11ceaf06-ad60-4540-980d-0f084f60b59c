const mongoose = require("mongoose");

const integrationSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  type: {
    type: String,
    required: true,
    enum: ["accounting", "banking", "hr"],
  },
  provider: String,
  credentials: {
    type: Map,
    of: String,
  },
  lastSync: Date,
  status: {
    type: String,
    enum: ["active", "inactive", "error"],
    default: "inactive",
  },
});

module.exports =
  mongoose.models.Integration ||
  mongoose.model("Integration", integrationSchema);
