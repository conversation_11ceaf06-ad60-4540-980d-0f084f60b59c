const mongoose = require('mongoose');

const payrollCalendarEventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  date: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  type: {
    type: String,
    enum: [
      'cutoff',           // Payroll cut-off dates
      'processing',       // Payroll processing deadlines
      'payment',          // Payment dates
      'emp201',           // EMP201 submission deadlines
      'emp501',           // EMP501 reconciliation deadlines
      'irp5',             // IRP5 submission deadlines
      'it3a',             // IT3a submission deadlines
      'uif',              // UIF submissions
      'sdl',              // SDL submissions
      'paye',             // PAYE submissions
      'eti',              // Employment Tax Incentive
      'custom',           // Custom payroll events
      'reminder',         // General reminders
      'compliance'        // Other compliance deadlines
    ],
    required: true,
    default: 'custom'
  },
  category: {
    type: String,
    enum: [
      'payroll',          // General payroll activities
      'tax_compliance',   // SARS tax compliance
      'statutory',        // Statutory submissions
      'custom'            // Custom categories
    ],
    required: true,
    default: 'custom'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'overdue'],
    default: 'pending'
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurrencePattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
      default: 'monthly'
    },
    interval: {
      type: Number,
      default: 1
    },
    dayOfMonth: {
      type: Number,
      min: 1,
      max: 31
    },
    dayOfWeek: {
      type: Number,
      min: 0,
      max: 6
    },
    endDate: {
      type: Date
    }
  },
  reminderSettings: {
    enabled: {
      type: Boolean,
      default: true
    },
    daysBeforeEvent: {
      type: [Number],
      default: [7, 3, 1]
    },
    emailReminder: {
      type: Boolean,
      default: true
    },
    dashboardReminder: {
      type: Boolean,
      default: true
    }
  },
  assignedTo: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  completedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  completedAt: {
    type: Date
  },
  notes: {
    type: String,
    trim: true
  },
  attachments: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    source: {
      type: String,
      enum: ['manual', 'system_generated', 'imported', 'comprehensive_compliance'],
      default: 'manual'
    },
    tags: [String],
    customFields: mongoose.Schema.Types.Mixed
  },
  emailReminders: [{
    daysBeforeEvent: {
      type: Number,
      required: true
    },
    sentAt: {
      type: Date,
      required: true
    },
    sentTo: [{
      email: String,
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }],
    emailStatus: {
      type: String,
      enum: ['sent', 'failed', 'bounced'],
      default: 'sent'
    },
    messageId: String,
    errorMessage: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
payrollCalendarEventSchema.index({ company: 1, date: 1 });
payrollCalendarEventSchema.index({ company: 1, type: 1 });
payrollCalendarEventSchema.index({ company: 1, status: 1 });
payrollCalendarEventSchema.index({ date: 1, status: 1 });
payrollCalendarEventSchema.index({ assignedTo: 1, status: 1 });

// Virtual for checking if event is overdue
payrollCalendarEventSchema.virtual('isOverdue').get(function() {
  return this.status !== 'completed' && this.date < new Date();
});

// Virtual for days until event
payrollCalendarEventSchema.virtual('daysUntilEvent').get(function() {
  const today = new Date();
  const eventDate = new Date(this.date);
  const diffTime = eventDate - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Method to mark as completed
payrollCalendarEventSchema.methods.markCompleted = function(userId) {
  this.status = 'completed';
  this.completedBy = userId;
  this.completedAt = new Date();
  return this.save();
};

// Method to generate next occurrence for recurring events
payrollCalendarEventSchema.methods.generateNextOccurrence = function() {
  if (!this.isRecurring) return null;
  
  const pattern = this.recurrencePattern;
  const nextDate = new Date(this.date);
  
  switch (pattern.frequency) {
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + pattern.interval);
      if (pattern.dayOfMonth) {
        nextDate.setDate(pattern.dayOfMonth);
      }
      break;
    case 'quarterly':
      nextDate.setMonth(nextDate.getMonth() + (3 * pattern.interval));
      break;
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + pattern.interval);
      break;
    default:
      return null;
  }
  
  // Check if we've exceeded the end date
  if (pattern.endDate && nextDate > pattern.endDate) {
    return null;
  }
  
  return {
    ...this.toObject(),
    _id: undefined,
    date: nextDate,
    status: 'pending',
    completedBy: undefined,
    completedAt: undefined,
    createdAt: undefined,
    updatedAt: undefined
  };
};

// Static method to create default South African compliance events
payrollCalendarEventSchema.statics.createDefaultComplianceEvents = async function(companyId, year) {
  const events = [];
  
  // Monthly EMP201 submissions (7th of each month)
  for (let month = 1; month <= 12; month++) {
    events.push({
      title: `EMP201 Submission - ${new Date(year, month - 1).toLocaleString('default', { month: 'long' })} ${year}`,
      description: 'Monthly employer tax return submission to SARS',
      date: new Date(year, month, 7), // 7th of following month
      type: 'emp201',
      category: 'tax_compliance',
      priority: 'high',
      isRecurring: false,
      company: companyId,
      metadata: {
        source: 'system_generated',
        tags: ['SARS', 'EMP201', 'monthly']
      }
    });
  }
  
  // Annual IRP5 submission (end of May)
  events.push({
    title: `IRP5 Annual Submission - ${year}`,
    description: 'Annual employee tax certificate submission to SARS',
    date: new Date(year + 1, 4, 31), // May 31st of following year
    type: 'irp5',
    category: 'tax_compliance',
    priority: 'critical',
    isRecurring: false,
    company: companyId,
    metadata: {
      source: 'system_generated',
      tags: ['SARS', 'IRP5', 'annual']
    }
  });
  
  return events;
};

// Static method to create comprehensive South African compliance events
payrollCalendarEventSchema.statics.createComprehensiveComplianceEvents = async function(companyId, year) {
  const events = [];

  // Monthly EMP201 submissions (7th of each month) - PAYE, UIF, SDL
  for (let month = 1; month <= 12; month++) {
    const monthName = new Date(year, month - 1).toLocaleString('default', { month: 'long' });
    const submissionDate = new Date(year, month, 7); // 7th of following month

    events.push({
      title: `EMP201 Monthly Submission - ${monthName} ${year}`,
      description: 'Monthly employer tax return submission to SARS including PAYE, UIF, and SDL contributions. Due by 7th of each month.',
      date: submissionDate,
      type: 'emp201',
      category: 'tax_compliance',
      priority: 'high',
      isRecurring: false,
      company: companyId,
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [7, 3, 1],
        emailReminder: true,
        dashboardReminder: true
      },
      metadata: {
        source: 'comprehensive_compliance',
        tags: ['SARS', 'EMP201', 'PAYE', 'UIF', 'SDL', 'monthly']
      }
    });
  }

  // Bi-annual EMP501 Reconciliation (Interim - October 31st)
  events.push({
    title: `EMP501 Interim Reconciliation - ${year}`,
    description: 'Interim employer reconciliation declaration for the first 6 months (March to August). Submission deadline: 31 October.',
    date: new Date(year, 9, 31), // October 31st
    type: 'emp501',
    category: 'tax_compliance',
    priority: 'critical',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [14, 7, 3, 1],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['SARS', 'EMP501', 'reconciliation', 'interim']
    }
  });

  // Annual EMP501 Reconciliation (May 31st of following year)
  events.push({
    title: `EMP501 Annual Reconciliation - ${year}`,
    description: 'Annual employer reconciliation declaration for the full tax year (1 March to 28 February). Submission deadline: 31 May.',
    date: new Date(year + 1, 4, 31), // May 31st of following year
    type: 'emp501',
    category: 'tax_compliance',
    priority: 'critical',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [30, 14, 7, 3, 1],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['SARS', 'EMP501', 'reconciliation', 'annual']
    }
  });

  // Annual IRP5/IT3(a) Submission (May 31st of following year)
  events.push({
    title: `IRP5/IT3(a) Annual Submission - ${year}`,
    description: 'Annual employee tax certificate submission to SARS. Must be submitted together with EMP501 reconciliation by 31 May.',
    date: new Date(year + 1, 4, 31), // May 31st of following year
    type: 'irp5',
    category: 'tax_compliance',
    priority: 'critical',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [30, 14, 7, 3, 1],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['SARS', 'IRP5', 'IT3a', 'annual', 'employee_certificates']
    }
  });

  // UIF Quarterly Returns (if applicable - some companies may need quarterly submissions)
  const quarters = [
    { name: 'Q1', month: 3, day: 31, period: 'January - March' },
    { name: 'Q2', month: 6, day: 30, period: 'April - June' },
    { name: 'Q3', month: 9, day: 30, period: 'July - September' },
    { name: 'Q4', month: 0, day: 31, period: 'October - December', nextYear: true }
  ];

  quarters.forEach(quarter => {
    const dueDate = quarter.nextYear ?
      new Date(year + 1, quarter.month, quarter.day) :
      new Date(year, quarter.month, quarter.day);

    events.push({
      title: `UIF Quarterly Review - ${quarter.name} ${year}`,
      description: `Quarterly review of UIF contributions and compliance for ${quarter.period}. Ensure all monthly EMP201 submissions are up to date.`,
      date: dueDate,
      type: 'uif',
      category: 'statutory',
      priority: 'medium',
      isRecurring: false,
      company: companyId,
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [7, 3],
        emailReminder: true,
        dashboardReminder: true
      },
      metadata: {
        source: 'comprehensive_compliance',
        tags: ['UIF', 'quarterly', 'review']
      }
    });
  });

  // SDL Annual Review (February 28th of following year)
  events.push({
    title: `SDL Annual Review - ${year}`,
    description: 'Annual review of Skills Development Levy contributions. Ensure all monthly SDL payments via EMP201 are accurate and up to date.',
    date: new Date(year + 1, 1, 28), // February 28th of following year
    type: 'sdl',
    category: 'statutory',
    priority: 'medium',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [14, 7, 3],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['SDL', 'Skills Development Levy', 'annual', 'review']
    }
  });

  // Employment Tax Incentive (ETI) Quarterly Reviews
  quarters.forEach(quarter => {
    const dueDate = quarter.nextYear ?
      new Date(year + 1, quarter.month, quarter.day) :
      new Date(year, quarter.month, quarter.day);

    events.push({
      title: `ETI Quarterly Review - ${quarter.name} ${year}`,
      description: `Quarterly review of Employment Tax Incentive claims for ${quarter.period}. Verify ETI eligibility and compliance requirements.`,
      date: dueDate,
      type: 'eti',
      category: 'tax_compliance',
      priority: 'medium',
      isRecurring: false,
      company: companyId,
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [7, 3],
        emailReminder: true,
        dashboardReminder: true
      },
      metadata: {
        source: 'comprehensive_compliance',
        tags: ['ETI', 'Employment Tax Incentive', 'quarterly']
      }
    });
  });

  // Year-end Compliance Preparation (February 15th of following year)
  events.push({
    title: `Year-end Compliance Preparation - ${year}`,
    description: 'Prepare for year-end compliance submissions. Review all payroll records, reconcile monthly submissions, and prepare for EMP501 and IRP5 submissions.',
    date: new Date(year + 1, 1, 15), // February 15th of following year
    type: 'compliance',
    category: 'tax_compliance',
    priority: 'high',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [7, 3, 1],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['year-end', 'preparation', 'compliance']
    }
  });

  // PAYE Registration Renewal Reminder (if applicable - annually)
  events.push({
    title: `PAYE Registration Review - ${year}`,
    description: 'Annual review of PAYE registration status and company details with SARS. Update any changes in company information.',
    date: new Date(year, 11, 31), // December 31st
    type: 'paye',
    category: 'tax_compliance',
    priority: 'low',
    isRecurring: false,
    company: companyId,
    reminderSettings: {
      enabled: true,
      daysBeforeEvent: [30, 14],
      emailReminder: true,
      dashboardReminder: true
    },
    metadata: {
      source: 'comprehensive_compliance',
      tags: ['PAYE', 'registration', 'annual', 'review']
    }
  });

  return events;
};

module.exports = mongoose.model('PayrollCalendarEvent', payrollCalendarEventSchema);
