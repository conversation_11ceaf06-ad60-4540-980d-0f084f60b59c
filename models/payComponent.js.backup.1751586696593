const mongoose = require("mongoose");
const {
  INPUT_CATEGORIES,
  VALIDATION_RULES,
} = require("../constants/payrollValidation");

const payComponentSchema = new mongoose.Schema({
  // Basic Component Info
  name: {
    type: String,
    required: true,
    trim: true,
  },

  code: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },

  category: {
    type: String,
    required: true,
    enum: Object.keys(INPUT_CATEGORIES),
    validate: {
      validator: function (value) {
        return INPUT_CATEGORIES[value] !== undefined;
      },
      message: (props) => `${props.value} is not a valid category`,
    },
  },

  // Component Configuration
  configuration: {
    requiresBaseSalary: {
      type: Boolean,
      default: false,
    },

    maxPercentOfBasic: {
      type: Number,
      min: 0,
      max: 100,
      validate: {
        validator: function (value) {
          return (
            !this.configuration.requiresBaseSalary ||
            (value > 0 && value <= 100)
          );
        },
        message:
          "Percentage must be between 0 and 100 when basic salary is required",
      },
    },

    hasEmployerPortion: {
      type: Boolean,
      default: false,
    },

    hasTaxImplication: {
      type: Boolean,
      default: true,
    },

    hasExemptPortion: {
      type: Boolean,
      default: false,
    },

    requiresBeneficiary: {
      type: Boolean,
      default: false,
    },
  },

  // Validation Rules
  validationRules: {
    minimumAmount: {
      type: Number,
      default: 0,
    },

    maximumAmount: {
      type: Number,
      default: null,
    },

    dependencies: [
      {
        component: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "PayComponent",
        },
        required: {
          type: Boolean,
          default: true,
        },
      },
    ],

    calculationMethod: {
      type: String,
      enum: ["fixed", "percentage", "formula"],
      default: "fixed",
    },

    calculationFormula: {
      type: String,
      validate: {
        validator: function (v) {
          return this.validationRules.calculationMethod !== "formula" || v;
        },
        message: "Formula is required when calculation method is formula",
      },
    },
  },

  // Company Reference
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },

  // Status and Metadata
  isActive: {
    type: Boolean,
    default: true,
  },

  effectiveDate: {
    type: Date,
    required: true,
    default: Date.now,
  },

  endDate: {
    type: Date,
    validate: {
      validator: function (value) {
        return !value || value > this.effectiveDate;
      },
      message: "End date must be after effective date",
    },
  },

  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
    },
  },
});

// Pre-save middleware
payComponentSchema.pre("save", function (next) {
  this.metadata.updatedAt = new Date();
  next();
});

// Methods
payComponentSchema.methods.validateAmount = function (amount, basicSalary) {
  const errors = [];

  // Basic amount validation
  if (amount < this.validationRules.minimumAmount) {
    errors.push(
      `Amount cannot be less than ${this.validationRules.minimumAmount}`
    );
  }

  if (
    this.validationRules.maximumAmount &&
    amount > this.validationRules.maximumAmount
  ) {
    errors.push(`Amount cannot exceed ${this.validationRules.maximumAmount}`);
  }

  // Basic salary percentage validation
  if (this.configuration.requiresBaseSalary && basicSalary) {
    const maxAmount =
      (basicSalary * this.configuration.maxPercentOfBasic) / 100;
    if (amount > maxAmount) {
      errors.push(
        `Amount cannot exceed ${this.configuration.maxPercentOfBasic}% of basic salary`
      );
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Static methods
payComponentSchema.statics.findActiveComponents = function (companyId) {
  return this.find({
    company: companyId,
    isActive: true,
    $or: [{ endDate: { $exists: false } }, { endDate: { $gt: new Date() } }],
  });
};

// Indexes
payComponentSchema.index({ company: 1, code: 1 }, { unique: true });
payComponentSchema.index({ company: 1, category: 1 });
payComponentSchema.index({ effectiveDate: 1, endDate: 1 });

const PayComponent =
  mongoose.models.PayComponent ||
  mongoose.model("PayComponent", payComponentSchema);

module.exports = PayComponent;
