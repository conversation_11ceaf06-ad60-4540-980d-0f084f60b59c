const mongoose = require("mongoose");

const leaveTypeSchema = new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    category: {
      type: String,
      enum: ["annual", "sick", "family", "unpaid", "custom"],
      required: true,
    },
    daysPerYear: {
      type: Number,
      required: true,
      min: 0,
    },
    accrualRate: {
      type: String,
      enum: ["monthly", "yearly", "none"],
      default: "yearly",
    },
    accrualSettings: {
      monthlyAmount: {
        type: Number,
        default: function () {
          return this.accrualRate === "monthly"
            ? Number((this.daysPerYear / 12).toFixed(2))
            : 0;
        },
      },
      allowAdvance: {
        type: Boolean,
        default: true,
      },
      maxAdvanceDays: {
        type: Number,
        default: function () {
          return this.daysPerYear;
        },
      },
      startAccrualFromMonth: {
        type: Number,
        default: 1, // 1-12 representing the month to start accrual
      },
      proRateFirstYear: {
        type: Boolean,
        default: true,
      },
      waitingPeriodMonths: {
        type: Number,
        default: 0, // Number of months before leave can be taken
      },
    },
    carryOverLimit: {
      type: Number,
      default: 0,
    },
    paidLeave: {
      type: Boolean,
      default: true,
    },
    requiresApproval: {
      type: Boolean,
      default: true,
    },
    requiresDocument: {
      type: Boolean,
      default: false,
    },
    documentRequiredAfterDays: {
      type: Number,
      default: 2,
    },
    minDaysNotice: {
      type: Number,
      default: 0,
    },
    maxConsecutiveDays: {
      type: Number,
    },
    gender: {
      type: String,
      enum: ["all", "male", "female"],
      default: "all",
    },
    active: {
      type: Boolean,
      default: true,
    },
    allowEmployeeSpecificAllocation: {
      type: Boolean,
      default: false,
    },
    employeeAllocations: [
      {
        employee: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Employee",
          required: true,
        },
        daysPerYear: {
          type: Number,
          required: true,
          min: 0,
        },
        reason: {
          type: String,
          trim: true,
        },
        startDate: {
          type: Date,
          required: true,
        },
        endDate: {
          type: Date,
        },
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes
leaveTypeSchema.index({ company: 1, name: 1 }, { unique: true });
leaveTypeSchema.index({ company: 1, category: 1 });

// Add a method to calculate accrued days
leaveTypeSchema.methods.calculateAccruedDays = function (
  employeeStartDate,
  asOfDate = new Date()
) {
  if (this.accrualRate === "yearly") {
    return this.daysPerYear;
  }

  const startDate = new Date(employeeStartDate);
  const currentDate = new Date(asOfDate);

  // If not yet reached waiting period, return 0
  const monthsEmployed =
    (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
    (currentDate.getMonth() - startDate.getMonth());
  if (monthsEmployed < this.accrualSettings.waitingPeriodMonths) {
    return 0;
  }

  // Calculate months to accrue for
  let monthsToAccrue = monthsEmployed;

  // Handle pro-rating for first year
  if (this.accrualSettings.proRateFirstYear && monthsEmployed < 12) {
    monthsToAccrue = currentDate.getMonth() - startDate.getMonth() + 1;
  }

  // Calculate accrued days
  const accruedDays = Number(
    (monthsToAccrue * this.accrualSettings.monthlyAmount).toFixed(2)
  );

  // Don't exceed annual allowance
  return Math.min(accruedDays, this.daysPerYear);
};

const LeaveType = mongoose.model("LeaveType", leaveTypeSchema);
module.exports = LeaveType;
