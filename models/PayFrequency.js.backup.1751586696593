const mongoose = require("mongoose");
const moment = require("moment-timezone");

const PayFrequencySchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  frequency: {
    type: String,
    enum: ["monthly", "weekly", "bi-weekly"],
    required: true,
  },
  lastDayOfPeriod: {
    type: String,
    validate: {
      validator: function (v) {
        // If frequency is not set yet, skip validation (will be validated on save)
        if (!this.frequency) {
          return true;
        }

        const weekDays = [
          "sunday",
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
        ];

        if (this.frequency === "weekly") {
          return weekDays.includes(v.toLowerCase());
        }
        if (this.frequency === "monthly") {
          return (
            v === "monthend" ||
            (!isNaN(v) && parseInt(v) >= 1 && parseInt(v) <= 31)
          );
        }
        if (this.frequency === "bi-weekly") {
          return (
            v === "monthend" ||
            (!isNaN(v) && parseInt(v) >= 1 && parseInt(v) <= 31)
          );
        }
        return false;
      },
      message: (props) => {
        if (props.value === null || props.value === undefined) {
          return "lastDayOfPeriod must be set";
        }
        if (props.value === "") {
          return "lastDayOfPeriod cannot be empty";
        }
        return `${props.value} is not a valid lastDayOfPeriod for frequency ${this.frequency || 'unknown'}`;
      },
    },
    required: [true, "lastDayOfPeriod is required"],
  },
  lastDayOfMonth: {
    type: String,
    default: "monthend",
  },
  biWeeklyLastDay: {
    type: String,
    default: "monthend",
  },
  biWeeklyInterimDay: {
    type: String,
    default: "15",
  },
  firstPayrollPeriodEndDate: {
    type: Date,
    required: true,
  },
  goFurtherBack: {
    type: Boolean,
    default: false,
  },
  goBackYears: {
    type: Number,
    min: 1,
    max: 2,
    default: 1,
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  name: {
    type: String,
    required: true,
    default: function () {
      const frequency =
        this.frequency.charAt(0).toUpperCase() + this.frequency.slice(1);
      const lastDay =
        this.lastDayOfPeriod.charAt(0).toUpperCase() +
        this.lastDayOfPeriod.slice(1);
      return `${frequency} ${lastDay}`;
    },
  },
});

PayFrequencySchema.pre("save", function (next) {
  this.updatedAt = new Date();

  // Ensure lastDayOfPeriod is lowercase for consistency
  if (this.lastDayOfPeriod) {
    this.lastDayOfPeriod = this.lastDayOfPeriod.toLowerCase();
  }

  // If this pay frequency is being set as default, unset any other defaults for this company
  if (this.isDefault && this.isModified('isDefault')) {
    this.constructor.updateMany(
      { company: this.company, _id: { $ne: this._id } },
      { $set: { isDefault: false } }
    ).exec();
  }

  next();
});

// Add a method to validate the lastDayOfPeriod matches the firstPayrollPeriodEndDate
PayFrequencySchema.methods.validatePeriodAlignment = function () {
  if (this.frequency === "weekly") {
    const firstPeriodEnd = moment(this.firstPayrollPeriodEndDate);
    const dayOfWeek = firstPeriodEnd.format("dddd").toLowerCase();
    if (dayOfWeek !== this.lastDayOfPeriod) {
      throw new Error(
        `First payroll period end date (${firstPeriodEnd.format(
          "YYYY-MM-DD"
        )}) must fall on the configured last day of period (${
          this.lastDayOfPeriod
        })`
      );
    }
  }
};

// Pre-save validation hook to ensure all fields are properly validated together
PayFrequencySchema.pre('save', function(next) {
  try {
    // Validate that frequency and lastDayOfPeriod are compatible
    if (this.frequency && this.lastDayOfPeriod) {
      const weekDays = [
        "sunday", "monday", "tuesday", "wednesday",
        "thursday", "friday", "saturday"
      ];

      if (this.frequency === "weekly" && !weekDays.includes(this.lastDayOfPeriod.toLowerCase())) {
        return next(new Error(`For weekly frequency, lastDayOfPeriod must be a valid day of the week. Got: ${this.lastDayOfPeriod}`));
      }

      if ((this.frequency === "monthly" || this.frequency === "bi-weekly")) {
        const isValidMonthlyDay = this.lastDayOfPeriod === "monthend" ||
          (!isNaN(this.lastDayOfPeriod) && parseInt(this.lastDayOfPeriod) >= 1 && parseInt(this.lastDayOfPeriod) <= 31);

        if (!isValidMonthlyDay) {
          return next(new Error(`For ${this.frequency} frequency, lastDayOfPeriod must be 'monthend' or a number between 1-31. Got: ${this.lastDayOfPeriod}`));
        }
      }
    }

    // Validate bi-weekly specific fields
    if (this.frequency === "bi-weekly") {
      if (this.interimDay && this.lastDayOfPeriod && this.interimDay === this.lastDayOfPeriod) {
        return next(new Error("For bi-weekly frequency, interimDay and lastDayOfPeriod must be different"));
      }
    }

    next();
  } catch (error) {
    next(error);
  }
});

module.exports =
  mongoose.models.PayFrequency ||
  mongoose.model("PayFrequency", PayFrequencySchema);
