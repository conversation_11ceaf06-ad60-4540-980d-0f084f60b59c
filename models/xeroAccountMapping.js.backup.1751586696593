const mongoose = require("mongoose");

const xeroAccountMappingSchema = new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    payrollAccountId: {
      type: String,
      required: true,
      enum: [
        "BASIC_SALARY",
        "PUBLIC_HOLIDAY_NOT_WORKED",
        "BASIC_HOURLY_PAY",
        "MEDICAL_AID_EMPLOYER",
        "PENSION_FUND_EMPLOYER",
        "SDL_EMPLOYER",
        "UIF_EMPLOYER",
        "MEDICAL_AID_LIABILITY",
        "PENSION_FUND_TOTAL",
        "SDL_LIABILITY",
        "PAYE_LIABILITY",
        "UIF_TOTAL",
      ],
    },
    xeroAccountId: {
      type: String,
      required: true,
    },
    xeroAccountCode: {
      type: String,
      required: true,
    },
    xeroAccountName: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ["DEBIT", "CREDIT"],
    },
    category: {
      type: String,
      required: true,
      enum: ["SALARY_EXPENSE", "EXPENSE", "LIABILITY"],
    },
    lastSyncedAt: {
      type: Date,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create a compound unique index
xeroAccountMappingSchema.index(
  { company: 1, payrollAccountId: 1 },
  { unique: true }
);

// Add a method to check if all required mappings exist for a company
xeroAccountMappingSchema.statics.validateCompanyMappings = async function (
  companyId
) {
  const mappings = await this.find({ company: companyId });
  const requiredMappings = [
    "BASIC_SALARY",
    "PUBLIC_HOLIDAY_NOT_WORKED",
    "BASIC_HOURLY_PAY",
    "MEDICAL_AID_EMPLOYER",
    "PENSION_FUND_EMPLOYER",
    "SDL_EMPLOYER",
    "UIF_EMPLOYER",
    "MEDICAL_AID_LIABILITY",
    "PENSION_FUND_TOTAL",
    "SDL_LIABILITY",
    "PAYE_LIABILITY",
    "UIF_TOTAL",
  ];

  const missingMappings = requiredMappings.filter(
    (required) =>
      !mappings.some((mapping) => mapping.payrollAccountId === required)
  );

  return {
    isValid: missingMappings.length === 0,
    missingMappings,
  };
};

// Check if the model already exists before compiling
module.exports =
  mongoose.models.XeroAccountMapping ||
  mongoose.model("XeroAccountMapping", xeroAccountMappingSchema);
