const mongoose = require("mongoose");

const employeeNumberSettingsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  mode: {
    type: String,
    enum: ["manual", "automatic"],
    default: "automatic",
  },
  firstCompanyEmployeeNumber: {
    type: String,
    default: "0001",
  },
  pattern: {
    prefix: {
      type: String,
      default: "",
    },
    numericPart: {
      type: String,
      default: "0001",
    },
    suffix: {
      type: String,
      default: "",
    },
    separator: {
      type: String,
      default: "",
    }
  },
  sequence: {
    current: {
      type: Number,
      default: 1,
    },
    start: {
      type: Number,
      default: 1,
    },
    step: {
      type: Number,
      default: 1,
    },
    paddingLength: {
      type: Number,
      default: 4,
    }
  },
  lastGeneratedNumber: {
    type: String,
    default: "0001",
  },
});

// Helper method to parse pattern from firstCompanyEmployeeNumber
employeeNumberSettingsSchema.methods.parsePattern = function() {
  try {
    const pattern = this.firstCompanyEmployeeNumber || "0001";

    // More flexible regex to handle various patterns
    const parts = pattern.match(/^([A-Za-z-]*)?(\d+)([A-Za-z-]*)?$/);

    if (!parts) {
      // Set default pattern if parsing fails
      this.pattern = {
        prefix: '',
        numericPart: '0001',
        suffix: '',
        separator: ''
      };
      this.sequence = {
        current: 1,
        start: 1,
        step: 1,
        paddingLength: 4
      };
      return this.pattern;
    }

    const numericPart = parts[2];
    const startNumber = parseInt(numericPart, 10);

    if (isNaN(startNumber)) {
      throw new Error("Invalid numeric part in employee number pattern");
    }

    this.pattern = {
      prefix: parts[1] || '',
      numericPart: numericPart,
      suffix: parts[3] || '',
      separator: pattern.includes('-') && !parts[1] && !parts[3] ? '-' : ''
    };

    this.sequence = {
      current: startNumber,
      start: startNumber,
      step: 1,
      paddingLength: numericPart.length
    };


    return this.pattern;
  } catch (error) {
    console.error("Error in parsePattern:", error);
    // Set safe defaults
    this.pattern = {
      prefix: '',
      numericPart: '0001',
      suffix: '',
      separator: ''
    };
    this.sequence = {
      current: 1,
      start: 1,
      step: 1,
      paddingLength: 4
    };
    return this.pattern;
  }
};

// Method to generate the next employee number
employeeNumberSettingsSchema.methods.generateNextNumber = async function () {
  try {
    // Parse pattern if not already set
    if (!this.pattern.numericPart) {
      this.parsePattern();
    }

    // Increment the sequence
    this.sequence.current += this.sequence.step;

    // Format the numeric part with proper padding
    const numericPart = String(this.sequence.current).padStart(
      this.sequence.paddingLength,
      "0"
    );

    // Combine all parts to create the new number
    const fullNumber = `${this.pattern.prefix}${this.pattern.separator}${numericPart}${this.pattern.suffix}`;

    // Save the generated number
    this.lastGeneratedNumber = fullNumber;
    await this.save();

    return fullNumber;
  } catch (error) {
    throw new Error(
      `Failed to generate next employee number: ${error.message}`
    );
  }
};

// Method to update existing employee numbers
employeeNumberSettingsSchema.methods.updateExistingEmployees = async function() {
  const Employee = mongoose.model('Employee');
  
  try {
    // Parse the new pattern
    this.parsePattern();
    
    // Reset sequence to start
    this.sequence.current = this.sequence.start;
    await this.save();
    
    // Find all employees for this company
    const employees = await Employee.find({ company: this.company }).sort('createdAt');
    
    
    // Update each employee's number
    for (const employee of employees) {
      // Atomically increment the sequence using findOneAndUpdate
      const updatedSettings = await this.constructor.findOneAndUpdate(
        { _id: this._id },
        { $inc: { 'sequence.current': 1 } },
        { new: true }
      );
      
      // Generate the new employee number
      const numericPart = String(updatedSettings.sequence.current).padStart(
        updatedSettings.sequence.paddingLength, "0"
      );
      
      const newNumber = `${updatedSettings.pattern.prefix}${updatedSettings.pattern.separator}${numericPart}${updatedSettings.pattern.suffix}`;
      
      // Update the employee number
      employee.companyEmployeeNumber = newNumber;
      await employee.save();
      
      // Update lastGeneratedNumber
      updatedSettings.lastGeneratedNumber = newNumber;
      await updatedSettings.save();
      
    }
    
  } catch (error) {
    // Create a clean error without circular references
    const errorMessage = error.message || "Unknown error";
    throw new Error(`Failed to update existing employee numbers: ${errorMessage}`);
  }
};

// Static method to get or create settings for a company
employeeNumberSettingsSchema.statics.getOrCreateSettings = async function (
  companyId
) {
  try {
    let settings = await this.findOne({ company: companyId });

    if (!settings) {
      settings = new this({
        company: companyId,
        mode: "automatic",
        firstCompanyEmployeeNumber: "0001",
      });

      // Initialize pattern and sequence with error handling
      try {
        settings.parsePattern();
      } catch (parseError) {
        console.error("Error parsing pattern during creation:", parseError);
        // Set safe defaults if parsing fails
        settings.pattern = {
          prefix: '',
          numericPart: '0001',
          suffix: '',
          separator: ''
        };
        settings.sequence = {
          current: 1,
          start: 1,
          step: 1,
          paddingLength: 4
        };
      }

      await settings.save();
    } else {
      // Ensure existing settings have valid pattern and sequence
      if (!settings.pattern || !settings.sequence) {
        try {
          settings.parsePattern();
          await settings.save();
        } catch (parseError) {
          console.error("Error re-parsing existing settings:", parseError);
        }
      }
    }

    return settings;
  } catch (error) {
    console.error("Error in getOrCreateSettings:", error);
    throw new Error(
      `Failed to get/create employee number settings: ${error.message}`
    );
  }
};

// Add compound index for company and lastGeneratedNumber
employeeNumberSettingsSchema.index(
  { company: 1, lastGeneratedNumber: 1 },
  { unique: true }
);

module.exports =
  mongoose.models.EmployeeNumberSettings ||
  mongoose.model("EmployeeNumberSettings", employeeNumberSettingsSchema);
