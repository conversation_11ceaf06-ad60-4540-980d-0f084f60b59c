const mongoose = require('mongoose');

const CustomBenefitSchema = new mongoose.Schema({
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  inputType: {
    type: String,
    required: true,
    enum: ['fixedAmount', 'amountPerEmployee', 'differentEveryPayslip', 'hourlyRateFactor', 'customRateQuantity', 'percentageOfIncome', 'monthlyForNonMonthly']
  },
  amount: {
    type: Number,
    required: function() {
      return this.inputType === 'fixedAmount';
    }
  },
  enableProRata: {
    type: Boolean,
    default: false
  },
  excludeFromAccounting: {
    type: Boolean,
    default: false
  },
  bargainingCouncilItem: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('CustomBenefit', CustomBenefitSchema); 