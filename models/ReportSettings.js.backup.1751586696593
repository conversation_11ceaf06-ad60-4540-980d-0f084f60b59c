const mongoose = require("mongoose");

const reportSettingsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  defaultFormat: {
    type: String,
    enum: ["pdf", "excel", "csv"],
    default: "pdf",
  },
  payslips: {
    maskSensitiveData: { type: Boolean, default: false },
    includeYTDTotals: { type: Boolean, default: true },
    showBankDetails: { type: Boolean, default: true },
    emailSettings: {
      autoEmailReports: { type: Boolean, default: false },
      emailRecipients: [String],
    },
  },
  bankingReport: {
    maskAccountNumbers: { type: Boolean, default: false },
    groupByBank: { type: Boolean, default: true },
    emailSettings: {
      autoEmailReports: { type: Boolean, default: false },
      emailRecipients: [String],
    },
  },
  employeeReports: {
    includePersonalInfo: { type: Boolean, default: true },
    includeBankDetails: { type: Boolean, default: false },
    emailSettings: {
      autoEmailReports: { type: Boolean, default: false },
      emailRecipients: [String],
    },
  },
  // Global email settings (fallback for reports without specific settings)
  emailSettings: {
    autoEmailReports: { type: Boolean, default: false },
    emailRecipients: [String],
  },
  dateRangePresets: {
    payrollReports: {
      default: {
        type: String,
        enum: ["currentMonth", "previousMonth", "currentYear", "custom"],
        default: "currentMonth",
      },
    },
    employeeReports: {
      default: {
        type: String,
        enum: ["allTime", "currentYear", "custom"],
        default: "allTime",
      },
    },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Update timestamp on save
reportSettingsSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model("ReportSettings", reportSettingsSchema);
