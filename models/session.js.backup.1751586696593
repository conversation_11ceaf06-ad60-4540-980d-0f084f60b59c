const mongoose = require("mongoose");

const sessionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
  },
  deviceType: {
    type: String,
    enum: ["mobile", "desktop", "tablet", "other"],
    default: "other",
  },
  deviceName: String,
  userAgent: String,
  ipAddress: String,
  location: String,
  lastActive: {
    type: Date,
    default: Date.now,
  },
  isValid: {
    type: Boolean,
    default: true,
  },
});

const Session =
  mongoose.models.Session || mongoose.model("Session", sessionSchema);
module.exports = Session;
