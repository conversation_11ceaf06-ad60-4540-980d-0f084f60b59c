const mongoose = require("mongoose");

const UI19CertificateSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  signature: {
    type: {
      type: String,
      enum: ["draw", "type", "upload"],
      required: true,
    },
    data: {
      type: String,
      required: true,
    },
    date: {
      type: Date,
      default: Date.now,
    },
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add index for faster lookups
UI19CertificateSchema.index({ company: 1, employee: 1 });

// Update the updatedAt timestamp on save
UI19CertificateSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model("UI19Certificate", UI19CertificateSchema);
