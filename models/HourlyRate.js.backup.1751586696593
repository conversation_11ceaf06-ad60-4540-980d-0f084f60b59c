const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const weeklyHoursSchema = new Schema({
  weekEndingDate: {
    type: Date,
    required: true,
  },
  normalHours: {
    type: Number,
    default: 0,
    min: 0,
  },
  overtimeHours: {
    type: Number,
    default: 0,
    min: 0,
  },
  sundayHours: {
    type: Number,
    default: 0,
    min: 0,
  },
});

const hourlyRateSchema = new Schema(
  {
    employee: {
      type: Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    payrollPeriod: {
      type: Schema.Types.ObjectId,
      ref: "PayrollPeriod",
      required: true,
    },
    normalHours: {
      type: Number,
      default: 0,
    },
    overtimeHours: {
      type: Number,
      default: 0,
    },
    weeks: [weeklyHoursSchema],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes
hourlyRateSchema.index({ employee: 1, payrollPeriod: 1 }, { unique: true });

const HourlyRate =
  mongoose.models.HourlyRate || mongoose.model("HourlyRate", hourlyRateSchema);

module.exports = HourlyRate;
