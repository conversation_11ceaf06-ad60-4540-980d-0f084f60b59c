const mongoose = require("mongoose");

const twoFactorSessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  sessionId: {
    type: String,
    required: true,
    index: true,
  },
  verificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "TwoFactorVerification",
    required: true,
  },
  status: {
    type: String,
    enum: ["active", "completed", "expired"],
    default: "active",
  },
  lastActivity: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 300, // 5 minutes
  },
});

module.exports = mongoose.model("TwoFactorSession", twoFactorSessionSchema);
