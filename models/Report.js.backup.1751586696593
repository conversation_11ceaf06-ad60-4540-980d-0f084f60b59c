const mongoose = require("mongoose");

const reportSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  type: {
    type: String,
    required: true,
    enum: [
      "payslips",
      "payrollSummary",
      "bankingReport",
      "employeeList",
      "leaveReport",
      "leaveDaysReport",
      "leaveExpiryReport",
      "benefitsReport",
      "emp201",
      "uifDeclaration",
      "etiReport",
      "employmentTaxIncentive",
      "payrollVariance",
      "employeeBasicInfo",
      "transactionHistory"
    ],
  },
  format: {
    type: String,
    required: true,
    enum: ["pdf", "excel", "csv"],
  },
  settings: {
    payslips: {
      maskSensitiveData: Boolean,
      includeYTDTotals: Boolean,
      showBankDetails: Boolean,
    },
    bankingReport: {
      maskAccountNumbers: Boolean,
      groupByBank: <PERSON>ole<PERSON>,
    },
    employeeReports: {
      includePersonalInfo: Boolean,
      includeBankDetails: <PERSON>ole<PERSON>,
    },
    emailSettings: {
      autoEmailReports: Boolean,
      emailRecipients: [String],
    },
  },
  dateRange: {
    startDate: Date,
    endDate: Date,
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed"],
    default: "pending",
  },
  data: Buffer,
  error: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

reportSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

reportSchema.index({ company: 1, type: 1, createdAt: -1 });
reportSchema.index({ generatedBy: 1, createdAt: -1 });

const Report = mongoose.model("Report", reportSchema);

module.exports = Report;
