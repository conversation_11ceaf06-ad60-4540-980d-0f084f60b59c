const mongoose = require("mongoose");

const twoFactorVerificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  email: {
    type: String,
    required: true,
  },
  redirectUrl: String,
  token: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  attempts: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ["pending", "completed", "expired"],
    default: "pending",
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 300, // 5 minutes
  },
});

// Ensure indexes are created
twoFactorVerificationSchema.index(
  { createdAt: 1 },
  { expireAfterSeconds: 300 }
);

const TwoFactorVerification = mongoose.model(
  "TwoFactorVerification",
  twoFactorVerificationSchema
);

module.exports = TwoFactorVerification;
