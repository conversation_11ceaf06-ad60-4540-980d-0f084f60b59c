const mongoose = require("mongoose");

const rfiConfigSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  option: {
    type: String,
    enum: ["selectedIncome", "percentagePerIncome"],
    required: true,
  },
  selectedIncomes: [String],
  percentageIncomes: {
    type: Map,
    of: Number,
  },
  effectiveDate: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model("RFIConfig", rfiConfigSchema);
