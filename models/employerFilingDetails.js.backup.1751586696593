const mongoose = require("mongoose");

const employerFilingDetailsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  payeNumber: String,
  sdlNumber: String,
  uifNumber: String,
  tradingName: String,
  registrationNumber: String,
  oidRegistrationNumber: String,
  contactPerson: {
    firstName: String,
    lastName: String,
    telephone: String,
    email: String,
  },
});

module.exports =
  mongoose.models.EmployerFilingDetails ||
  mongoose.model("EmployerFilingDetails", employerFilingDetailsSchema);
