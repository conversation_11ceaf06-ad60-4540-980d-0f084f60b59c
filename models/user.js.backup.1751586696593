const express = require("express");
const mongoose = require("mongoose");
const session = require("express-session");
const passport = require("passport");
const crypto = require("crypto");
const bcrypt = require("bcrypt");
const flash = require("express-flash");
const bodyParser = require("body-parser");
const path = require("path");
const MongoStore = require("connect-mongo");
const Role = require("./role"); // Correct import for the Role model

const SALT_ROUNDS = 12;

const userSchema = new mongoose.Schema(
  {
    firstName: String,
    lastName: String,
    username: {
      type: String,
      unique: true,
      sparse: true, // This allows null values while maintaining uniqueness
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
    },
    bio: {
      type: String,
      default: "Tell us about yourself",
    },
    phone: String, // Add this line
    cellNumber: String, // Added cell number field
    password: { 
      type: String,
      required: function() {
        return this.isVerified; // Only require password if user is verified
      }
    },
    isVerified: { type: Boolean, default: false, required: true },
    verificationToken: String,
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    companies: {
      type: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true,
        validate: {
          validator: function(v) {
            return mongoose.Types.ObjectId.isValid(v);
          },
          message: props => `${props.value} is not a valid company ID!`
        }
      }],
      default: [],
      validate: {
        validator: function(v) {
          return Array.isArray(v);
        },
        message: props => 'Companies must be an array!'
      }
    },
    currentCompany: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company'
    },
    defaultCompany: { type: mongoose.Schema.Types.ObjectId, ref: "Company" }, // Add this line if not already present
    role: { type: mongoose.Schema.Types.ObjectId, ref: "Role", required: true },
    roleName: String,
    createdAt: { type: Date, default: Date.now },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: String,
    backupCodes: [
      {
        code: String,
        used: {
          type: Boolean,
          default: false,
        },
      },
    ],
    passwordLastChanged: {
      type: Date,
      default: Date.now,
    },
    failedLoginAttempts: {
      type: Number,
      default: 0,
    },
    accountLocked: {
      type: Boolean,
      default: false,
    },
    lockUntil: Date,
    onboardingComplete: {
      type: Boolean,
      default: false,
    },
    onboardingStep: {
      type: Number,
      default: 1,
    },
    promotionalCode: {
        type: String,
        default: null
    },
    promotionStartDate: {
        type: Date,
        default: null
    },
    promotionEndDate: {
        type: Date,
        default: null
    },
  },
  {
    timestamps: true,
  }
);

userSchema.pre("save", async function (next) {
  try {
    console.log("\n=== Pre-save Hook START ===");
    console.log("User:", {
      id: this._id,
      email: this.email,
      isNew: this.isNew,
      isVerified: this.isVerified,
      previousIsVerified: this._previousIsVerified,
      isModified: {
        isVerified: this.isModified("isVerified"),
        password: this.isModified("password")
      }
    });

    // Set username to email if not provided
    if (!this.username) {
      this.username = this.email;
      console.log("Username set to email:", this.email);
    }

    // If this is the first save (_previousIsVerified is undefined),
    // initialize it to false to properly track changes
    if (this._previousIsVerified === undefined) {
      this._previousIsVerified = false;
      console.log("Initializing _previousIsVerified to false for first-time tracking");
    }

    // Ensure isVerified is false for new users unless explicitly set otherwise
    if (this.isNew && this.isVerified !== true) {
      console.log("New user - isVerified explicitly set to false");
      this.isVerified = false;
    }

    // Only hash password if it's been modified or is new
    if (this.isModified("password")) {
      console.log("Password modified, hashing with salt rounds:", SALT_ROUNDS);
      const hashedPassword = await bcrypt.hash(this.password, SALT_ROUNDS);
      console.log("Generated password hash prefix:", hashedPassword.substring(0, 10) + "...");
      this.password = hashedPassword;
    }

    // Track if isVerified changed from false to true
    if (this.isModified("isVerified") || (this.isNew && this.isVerified)) {
      console.log("isVerified modified or new user with isVerified=true:", {
        from: this._previousIsVerified,
        to: this.isVerified,
        isNew: this.isNew
      });
      
      if (this.isVerified && !this._previousIsVerified) {
        console.log("User is being verified for the first time - marking for welcome email");
        // Mark for welcome email sending after save
        this._sendWelcomeEmail = true;
      } else {
        console.log("Not a verification event - no welcome email needed");
      }
    } else {
      console.log("isVerified not modified");
    }
    
    // Store current isVerified state for next save operation
    this._previousIsVerified = this.isVerified;
    console.log("Stored _previousIsVerified =", this.isVerified);
    console.log("_sendWelcomeEmail =", this._sendWelcomeEmail);
    console.log("=== Pre-save Hook END ===\n");

    next();
  } catch (error) {
    console.error("Error in pre-save hook:", error);
    next(error);
  }
});

// Post-save hook to send welcome email if needed
userSchema.post("save", async function () {
  try {
    console.log("\n=== Post-save Hook START ===");
    console.log("User:", {
      id: this._id,
      email: this.email,
      isVerified: this.isVerified,
      _sendWelcomeEmail: this._sendWelcomeEmail
    });
    
    // Skip if welcome email doesn't need to be sent
    if (!this._sendWelcomeEmail) {
      console.log("No welcome email flag set - skipping welcome email");
      console.log("=== Post-save Hook END ===\n");
      return;
    }
    
    // Reset the flag to prevent multiple emails
    this._sendWelcomeEmail = false;
    
    console.log("Triggering welcome email for newly verified user:", this.email);
    
    // Emit an event to notify the application to send welcome email
    // This avoids circular dependencies and keeps model logic separate
    let eventEmitted = false;
    if (mongoose.connection.emit) {
      console.log("Emitting 'userVerified' event with data:", {
        userId: this._id,
        email: this.email,
        firstName: this.firstName || this.email.split('@')[0]
      });
      
      // Try both approaches to ensure event emission works
      try {
        // First approach: regular event emission
        mongoose.connection.emit("userVerified", {
          userId: this._id,
          email: this.email,
          firstName: this.firstName || this.email.split('@')[0],
        });
        
        console.log("Event emitted successfully via mongoose.connection.emit");
        eventEmitted = true;
      } catch (emitError) {
        console.error("Error with mongoose.connection.emit:", emitError);
        
        // Second approach: try process.nextTick for reliable event emission
        console.log("Attempting alternative approach with process.nextTick...");
        process.nextTick(() => {
          try {
            mongoose.connection.emit("userVerified", {
              userId: this._id,
              email: this.email,
              firstName: this.firstName || this.email.split('@')[0],
            });
            console.log("Event emitted successfully via process.nextTick approach");
            eventEmitted = true;
          } catch (nextTickError) {
            console.error("Error emitting event via process.nextTick:", nextTickError);
          }
        });
      }
      
      // Third approach: as a fallback, try direct email sending if possible
      try {
        // Only continue with direct email if event emission failed
        if (!eventEmitted) {
          console.log("Event emission failed or uncertain, attempting direct email send...");
          const path = require('path');
          const fs = require('fs');
          const ejs = require('ejs');
          const nodemailer = require('nodemailer');
          
          console.log("Checking for existence of welcome email template...");
          const welcomeTemplatePath = path.join(process.cwd(), "views/emails/welcome.ejs");
          
          // Create directory and template if needed
          const emailsDir = path.join(process.cwd(), "views/emails");
          if (!fs.existsSync(emailsDir)) {
            console.log("Creating emails directory...");
            try {
              fs.mkdirSync(emailsDir, { recursive: true });
              console.log("Created emails directory successfully");
            } catch (dirError) {
              console.error("Error creating emails directory:", dirError);
              throw dirError;
            }
          }
          
          // Create template if it doesn't exist
          if (!fs.existsSync(welcomeTemplatePath)) {
            console.log("Welcome template doesn't exist, creating default template...");
            
            const defaultTemplate = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Welcome to Panda Solutions</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .logo { max-width: 200px; }
    h1 { color: #2c3e50; }
    .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="cid:logo" alt="Panda Solutions Logo" class="logo">
      <h1>Welcome to Panda Solutions!</h1>
    </div>
    
    <p>Hello <%= firstName %>,</p>
    
    <p>Thank you for joining Panda Solutions. Your account has been verified and is now ready to use.</p>
    
    <p>Here's what you need to know to get started:</p>
    
    <ul>
      <li>Our support team is available via <NAME_EMAIL> with a 48-hour turnaround time.</li>
      <li>Check out our <a href="https://www.youtube.com/watch?v=yourvideolink">video guide</a> for help navigating the platform.</li>
      <li>For urgent matters, contact us at +27 00 000 0000.</li>
    </ul>
    
    <p>We're excited to have you on board!</p>
    
    <p>Best regards,<br>
    The Panda Solutions Team</p>
    
    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Panda Solutions. All rights reserved.</p>
      <p>This email was sent to <%= email %>.</p>
    </div>
  </div>
</body>
</html>`;
            
            try {
              fs.writeFileSync(welcomeTemplatePath, defaultTemplate);
              console.log("Default welcome template created successfully");
            } catch (templateError) {
              console.error("Error creating welcome template:", templateError);
              throw templateError;
            }
          } else {
            console.log("Welcome template exists at:", welcomeTemplatePath);
          }
          
          // Helper function to find logo
          const ensureLogoExists = () => {
            try {
              console.log("Searching for logo file...");
              // Check common locations for the logo
              const possiblePaths = [
                path.join(process.cwd(), "public/images/pandalogo.png"),
                path.join(process.cwd(), "public/images/logo.png"),
                path.join(process.cwd(), "public/img/logo.png"),
                path.join(process.cwd(), "public/assets/images/logo.png")
              ];
          
              // Return the first path that exists
              for (const logoPath of possiblePaths) {
                if (fs.existsSync(logoPath)) {
                  console.log("Logo found at:", logoPath);
                  return logoPath;
                } else {
                  console.log("Logo not found at:", logoPath);
                }
              }
          
              // Fallback to the SVG logo if exists
              const svgPath = path.join(process.cwd(), "public/images/pandalogo.svg");
              if (fs.existsSync(svgPath)) {
                console.log("SVG logo found at:", svgPath);
                return svgPath;
              } else {
                console.log("SVG logo not found at:", svgPath);
              }
          
              console.warn("No logo found for welcome email. Creating default logo...");
              
              // Create public/images directory if needed
              const imagesDir = path.join(process.cwd(), "public/images");
              if (!fs.existsSync(imagesDir)) {
                console.log("Creating images directory");
                try {
                  fs.mkdirSync(imagesDir, { recursive: true });
                  console.log("Created images directory successfully");
                } catch (dirError) {
                  console.error("Error creating images directory:", dirError);
                }
              }
              
              // Create a very basic logo as SVG
              const logoSvgPath = path.join(process.cwd(), "public/images/pandalogo.svg");
              const basicSvg = `<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="20" y="35" font-family="Arial" font-size="24" fill="#333">Panda Solutions</text>
</svg>`;
              
              try {
                fs.writeFileSync(logoSvgPath, basicSvg);
                console.log("Created basic SVG logo at:", logoSvgPath);
                return logoSvgPath;
              } catch (logoError) {
                console.error("Error creating basic logo:", logoError);
                return null;
              }
            } catch (error) {
              console.error("Error finding logo:", error);
              return null;
            }
          };
          
          // Render welcome email template
          console.log("Rendering welcome email template...");
          const emailHtml = await ejs.renderFile(
            welcomeTemplatePath,
            {
              firstName: this.firstName || this.email.split('@')[0],
              email: this.email
            }
          );
          console.log("Email template rendered successfully, length:", emailHtml.length);
          
          // Find logo
          const logoPath = ensureLogoExists();
          console.log("Using logo for email:", logoPath);
          
          // Create transporter
          console.log("Creating email transporter...");
          const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST,
            port: process.env.EMAIL_PORT,
            secure: process.env.EMAIL_SECURE === "true",
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASS,
            },
            tls: {
              rejectUnauthorized: false, // Only use this in development
            }
          });
          
          // Prepare mail options
          const mailOptions = {
            from: {
              name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
              address: process.env.EMAIL_USER,
            },
            to: this.email,
            subject: "Welcome to Panda Solutions - Getting Started",
            html: emailHtml,
            attachments: logoPath ? [
              {
                filename: "logo.png",
                path: logoPath,
                cid: "logo",
              },
            ] : [],
            headers: {
              "X-Priority": "1",
              "X-MSMail-Priority": "High",
              Importance: "high",
              "Message-ID": `<${Date.now()}.${Math.random().toString(36).substring(2)}@pss-group.co.za>`,
              "List-Unsubscribe": `<mailto:${process.env.EMAIL_USER}?subject=unsubscribe>`,
              "X-Mailer": "Panda Solutions Mailer/1.0",
              "X-Entity-Ref-ID": require("crypto").randomBytes(32).toString("hex"),
            },
          };
          
          // Send email
          console.log("Sending welcome email directly from model hook...");
          const info = await transporter.sendMail(mailOptions);
          console.log("Welcome email sent successfully via direct method:");
          console.log("MessageId:", info.messageId);
          console.log("Response:", info.response);
        }
      } catch (directError) {
        console.error("Error with direct email sending:", directError);
        console.error("Stack trace:", directError.stack);
      }
    } else {
      console.error("ERROR: mongoose.connection.emit is not available");
      console.log("Details of mongoose.connection:", {
        type: typeof mongoose.connection,
        hasEmit: typeof mongoose.connection.emit === 'function',
        connectionState: mongoose.connection.readyState
      });
      
      // Alternative approach: try event emission via mongoose itself
      if (mongoose.emit) {
        console.log("Attempting to emit event via mongoose.emit...");
        try {
          mongoose.emit("userVerified", {
            userId: this._id,
            email: this.email,
            firstName: this.firstName || this.email.split('@')[0],
          });
          console.log("Event emitted successfully via mongoose.emit");
          eventEmitted = true;
        } catch (mongooseEmitError) {
          console.error("Error with mongoose.emit:", mongooseEmitError);
        }
      } else {
        console.error("ERROR: Both mongoose.connection.emit and mongoose.emit are not available");
        console.log("Direct email sending would be a fallback option here");
      }
    }
    
    console.log("=== Post-save Hook END ===\n");
  } catch (error) {
    console.error("Error in post-save hook for welcome email:", error);
    console.error("Stack trace:", error.stack);
  }
});

userSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    console.log("\n=== Password Comparison ===");
    console.log("Comparing passwords for user:", this.email);
    console.log("Stored password hash:", this.password);
    console.log("Stored password hash length:", this.password ? this.password.length : 0);
    console.log("Candidate password:", candidatePassword);
    console.log("Candidate password length:", candidatePassword ? candidatePassword.length : 0);

    if (!this.password || !candidatePassword) {
      console.log("Missing password data");
      return false;
    }

    // Use bcrypt's built-in compare function
    const isMatch = await bcrypt.compare(candidatePassword, this.password);
    console.log("Password match result:", isMatch);
    
    return isMatch;
  } catch (error) {
    console.error("Password comparison error:", error);
    return false;
  }
};

userSchema.methods.generateResetToken = async function () {
  const token = crypto.randomBytes(20).toString("hex");
  this.resetPasswordToken = token;
  this.resetPasswordExpires = Date.now() + 3600000; // Token expires in 1 hour
  await this.save();
  return token;
};

userSchema.methods.verifyPassword = async function (password) {
  console.log("Verifying password for user:", this.email);
  console.log("Stored hashed password:", this.password);
  console.log("Provided password:", password);
  const isMatch = await bcrypt.compare(password, this.password);
  console.log("Password match result:", isMatch);
  return isMatch;
};

const User = mongoose.models.User || mongoose.model("User", userSchema);
module.exports = User;
