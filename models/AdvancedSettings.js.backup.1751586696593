const mongoose = require("mongoose");

const advancedSettingsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  settings: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports =
  mongoose.models.AdvancedSettings ||
  mongoose.model("AdvancedSettings", advancedSettingsSchema);
