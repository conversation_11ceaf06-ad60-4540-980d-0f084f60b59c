const mongoose = require("mongoose");

const customItemSchema = new mongoose.Schema(
  {
    companyCode: {
      type: String,
      required: true,
      index: true
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Company',
      required: false
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true,
      enum: ['earnings', 'deductions', 'company-contributions', 'fringe-benefits', 'benefit', 'income']
    },
    calculationType: {
      type: String,
      required: function() {
        return this.type !== 'benefit' && this.type !== 'income';
      },
      enum: ['fixed', 'percentage', 'formula']
    },
    value: {
      type: Number,
      required: function() {
        return (this.calculationType !== 'formula' && this.type !== 'benefit' && this.type !== 'income') ||
               (this.type === 'benefit' && this.inputType === 'fixedAmount');
      }
    },
    percentageBase: {
      type: String,
      required: function() {
        return this.calculationType === 'percentage';
      }
    },
    formula: {
      type: String,
      required: function() {
        return this.calculationType === 'formula';
      }
    },
    // Fields for benefits
    inputType: {
      type: String,
      required: function() {
        return this.type === 'benefit' || this.type === 'income';
      },
      enum: ['fixedAmount', 'amountPerEmployee', 'differentEveryPayslip', 'onceOffSpecificPayslips', 'hourlyRateFactor', 'customRateQuantity', 'percentageOfIncome', 'monthlyForNonMonthly']
    },
    enableProRata: {
      type: Boolean,
      default: false
    },
    excludeFromAccounting: {
      type: Boolean,
      default: false
    },
    bargainingCouncilItem: {
      type: Boolean,
      default: false
    },
    // New fields for custom income based on image requirements
    taxedAnnually: {
      type: Boolean,
      default: false
    },
    includeInFluctuatingLeaveRate: {
      type: Boolean,
      default: false
    },
    overtime: {
      type: Boolean,
      default: false
    },
    affectsWageForETI: {
      type: Boolean,
      default: false
    },
    includeInCostToCompany: {
      type: Boolean,
      default: false
    },
    // New conditional fields for custom income
    amount: {
      type: Number,
      required: function() {
        return this.inputType === 'fixedAmount' || this.inputType === 'monthlyForNonMonthly';
      }
    },
    hoursWorkedFactor: {
      type: Number,
      default: 1
    },
    differentRateForEveryEmployee: {
      type: Boolean,
      default: false
    },
    customRate: {
      type: Number,
      required: function() {
        return this.inputType === 'customRateQuantity' && !this.differentRateForEveryEmployee;
      }
    },
    incomeItems: {
      type: [String],
      required: function() {
        return this.inputType === 'percentageOfIncome';
      }
    },
    enableProRata: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
customItemSchema.index({ companyCode: 1, type: 1 });
customItemSchema.index({ companyCode: 1, name: 1 }, { unique: true });

const CustomItem = mongoose.model("CustomItem", customItemSchema);

module.exports = CustomItem;
