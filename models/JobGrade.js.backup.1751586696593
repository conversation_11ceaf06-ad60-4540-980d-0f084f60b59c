const mongoose = require("mongoose");

const jobGradeSchema = new mongoose.Schema(
  {
    companyCode: {
      type: String,
      required: true,
      index: true
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
      index: true
    },
    code: {
      type: String,
      required: true,
      trim: true
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    minSalary: {
      type: Number,
      required: true,
      min: 0
    },
    maxSalary: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function(value) {
          return value >= this.minSalary;
        },
        message: "Maximum salary must be greater than or equal to minimum salary"
      }
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

// Compound index for unique job grade codes per company
jobGradeSchema.index({ companyCode: 1, code: 1 }, { unique: true });

const JobGrade = mongoose.model("JobGrade", jobGradeSchema);

module.exports = JobGrade; 