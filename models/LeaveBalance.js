const mongoose = require("mongoose");

const leaveBalanceSchema = new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    employee: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    leaveType: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LeaveType",
      required: true,
    },
    year: {
      type: Number,
      required: true,
    },
    totalAllocation: {
      type: Number,
      required: true,
      min: 0,
    },
    used: {
      type: Number,
      default: 0,
      min: 0,
    },
    pending: {
      type: Number,
      default: 0,
      min: 0,
    },
    remaining: {
      type: Number,
      required: true,
      min: 0,
    },
    carryOver: {
      type: Number,
      default: 0,
      min: 0,
    },
    adjustments: [
      {
        amount: {
          type: Number,
          required: true,
        },
        reason: {
          type: String,
          required: true,
        },
        date: {
          type: Date,
          default: Date.now,
        },
        adjustedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
      },
    ],
    lastAccrualDate: {
      type: Date,
    },
    nextAccrualDate: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

// Add pre-save hook to ensure data integrity
leaveBalanceSchema.pre('save', function(next) {
  // Ensure no negative values before saving
  this.used = Math.max(0, this.used || 0);
  this.pending = Math.max(0, this.pending || 0);
  this.totalAllocation = Math.max(0, this.totalAllocation || 0);
  this.carryOver = Math.max(0, this.carryOver || 0);

  // Recalculate remaining to ensure consistency
  this.remaining = this.calculateRemaining();

  // Log any corrections made
  if (this.isModified('pending') || this.isModified('used')) {
  }

  next();
});

// Add indexes
leaveBalanceSchema.index(
  { company: 1, employee: 1, leaveType: 1, year: 1 },
  { unique: true }
);
leaveBalanceSchema.index({ company: 1, employee: 1, year: 1 });

// Add a method to calculate remaining balance
leaveBalanceSchema.methods.calculateRemaining = function () {
  return this.totalAllocation + this.carryOver - this.used - this.pending;
};

// Add a method to update balance after leave request with validation
leaveBalanceSchema.methods.updateAfterRequest = function (days, status) {
  if (status === "pending") {
    this.pending += days;
  } else if (status === "approved") {
    // Ensure pending doesn't go negative
    this.pending = Math.max(0, this.pending - days);
    this.used += days;
  } else if (status === "cancelled" || status === "rejected") {
    // Ensure pending doesn't go negative
    this.pending = Math.max(0, this.pending - days);
  }
  this.remaining = this.calculateRemaining();
};

// Add a method to validate balance integrity
leaveBalanceSchema.methods.validateBalance = function () {
  const errors = [];

  // Check for negative values
  if (this.used < 0) errors.push('Used days cannot be negative');
  if (this.pending < 0) errors.push('Pending days cannot be negative');
  if (this.remaining < 0) errors.push('Remaining days cannot be negative');
  if (this.totalAllocation < 0) errors.push('Total allocation cannot be negative');

  // Check balance equation: remaining = totalAllocation + carryOver - used - pending
  const calculatedRemaining = this.calculateRemaining();
  if (Math.abs(this.remaining - calculatedRemaining) > 0.01) {
    errors.push(`Balance equation mismatch: stored remaining (${this.remaining}) != calculated (${calculatedRemaining})`);
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
};

// Add a method to safely update balance with validation
leaveBalanceSchema.methods.safeUpdate = function (updates) {
  // Apply updates
  Object.assign(this, updates);

  // Ensure no negative values
  this.used = Math.max(0, this.used || 0);
  this.pending = Math.max(0, this.pending || 0);
  this.totalAllocation = Math.max(0, this.totalAllocation || 0);
  this.carryOver = Math.max(0, this.carryOver || 0);

  // Recalculate remaining
  this.remaining = this.calculateRemaining();

  // Validate the result
  const validation = this.validateBalance();
  if (!validation.isValid) {
  }

  return validation;
};

const LeaveBalance = mongoose.model("LeaveBalance", leaveBalanceSchema);
module.exports = LeaveBalance;
