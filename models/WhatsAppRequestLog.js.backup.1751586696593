const mongoose = require('mongoose');
const { Schema } = mongoose;

const WhatsAppRequestLogSchema = new Schema({
  employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
  company: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  mobileNumber: { type: String, required: true },
  requestType: { type: String, enum: ['payslip', 'irp5', 'leave'], required: true },
  requestText: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  status: { type: String, enum: ['success', 'failed'], default: 'success' },
  response: { type: String },
  leaveRequest: { type: Schema.Types.ObjectId, ref: 'LeaveRequest' },
});

module.exports = mongoose.model('WhatsAppRequestLog', WhatsAppRequestLogSchema); 