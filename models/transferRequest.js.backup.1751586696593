const mongoose = require("mongoose");

const transferRequestSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  currentOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  recipientEmail: {
    type: String,
    required: true,
  },
  otp: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "accepted", "rejected"],
    default: "pending",
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: "24h", // Automatically delete after 24 hours if not accepted
  },
});

const TransferRequest =
  mongoose.models.TransferRequest ||
  mongoose.model("TransferRequest", transferRequestSchema);
module.exports = TransferRequest;
