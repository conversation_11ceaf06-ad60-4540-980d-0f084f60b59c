const mongoose = require("mongoose");

const eftDetailsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  eftFormat: {
    type: String,
    enum: [
      'ABSA Cash Focus',
      'ABSA Business Integrator (.txt)',
      'ABSA Business Integrator (.csv)',
      'ABSA Business Integrator SAP (beta)',
      'ABSA Business Integrator Online (.csv)',
      'FNB Bankit',
      'FNB CAMS',
      'FNB Enterprise CSV',
      'FNB Online Banking (ACB)',
      'FNB PACS',
      'Standard Bank CATS',
      'Standard Bank Value Dated / EFTS',
      'Standard Bank SSVS',
      'NedBank Business Banking (CSV)',
      'NedInform',
      'Albaraka',
      'Capitec ACB',
      'Investec CSV',
      'Netcash',
      'Sage Pay'
    ]
  },
  bank: String,
  accountNumber: String,
  branchCode: String,
  accountType: String,
  accountHolder: String,
  reference: String,
  additionalBankAccounts: [{
    bankName: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true
    },
    branchCode: {
      type: String,
      required: true
    },
    accountType: {
      type: String,
      enum: ['Current', 'Savings', 'Transmission'],
      required: true
    },
    accountDescription: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
});

module.exports =
  mongoose.models.EFTDetails || mongoose.model("EFTDetails", eftDetailsSchema);
