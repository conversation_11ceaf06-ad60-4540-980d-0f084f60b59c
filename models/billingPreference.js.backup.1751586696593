const mongoose = require("mongoose");

const billingPreferenceSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  paymentMethod: {
    type: String,
    enum: ["eft", "card"],
    default: "eft",
  },
  // DEPRECATED: This field is no longer used in the UI but kept for backward compatibility
  autoPayEnabled: {
    type: Boolean,
    default: false,
  },
  cardDetails: {
    cardType: String,
    last4: String,
    expiryMonth: String,
    expiryYear: String,
    cardHolderName: String,
    isDefault: {
      type: Boolean,
      default: true,
    },
    brand: String,
    bin: String,
    bank: String,
    reusable: {
      type: Boolean,
      default: false
    },
    signature: String,
    countryCode: String
  },
  bankDetails: {
    bankName: String,
    accountNumber: String,
    accountType: String,
    branchCode: String,
    accountHolderName: String,
    isDefault: {
      type: Boolean,
      default: true,
    },
  },
  paystackCustomerCode: String,
  paystackAuthorizationCode: String,
  paystackEmail: String,
  notificationPreferences: {
    invoiceReminders: {
      type: Boolean,
      default: true,
    },
    paymentReceipts: {
      type: Boolean,
      default: true,
    },
    paymentFailures: {
      type: Boolean,
      default: true,
    },
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  }
});

// Update the updatedAt field on save
billingPreferenceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const BillingPreference = mongoose.models.BillingPreference || mongoose.model("BillingPreference", billingPreferenceSchema);
module.exports = BillingPreference;
