const mongoose = require("mongoose");

// Check if the model exists before defining it
const Beneficiary = mongoose.models.Beneficiary || mongoose.model("Beneficiary", new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ["pension-fund", "provident-fund", "retirement-annuity-fund", "medical-aid"],
    },
    status: {
      type: String,
      required: true,
      enum: ["active", "inactive"],
      default: "active",
    },
    fundName: {
      type: String,
      required: true,
      trim: true,
    },
    membershipNumber: {
      type: String,
      required: true,
      trim: true,
    },
    registrationNumber: {
      type: String,
      required: true,
      trim: true,
    },
    contactPerson: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    address: {
      street: String,
      city: String,
      state: String,
      postalCode: String,
      country: String,
    },
    bankDetails: {
      bankName: String,
      accountNumber: String,
      branchCode: String,
      accountType: {
        type: String,
        enum: ["current", "savings", "transmission"],
      },
    },
    notes: {
      type: String,
      trim: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
));

// Add indexes for frequently queried fields
if (!Beneficiary.schema.indexes().length) {
  Beneficiary.schema.index({ company: 1, type: 1, status: 1 });
  Beneficiary.schema.index({ membershipNumber: 1 });
  Beneficiary.schema.index({ registrationNumber: 1 });
}

module.exports = Beneficiary; 