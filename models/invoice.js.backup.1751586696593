const mongoose = require("mongoose");

const invoiceSchema = new mongoose.Schema({
  number: {
    type: String,
    required: true,
    unique: true,
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  date: {
    type: Date,
    required: true,
    default: Date.now,
  },
  dueDate: {
    type: Date,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  status: {
    type: String,
    enum: ["paid", "pending", "overdue", "cancelled"],
    default: "pending",
  },
  paymentMethod: {
    type: String,
    enum: ["eft", "card", "other"],
    default: "eft",
  },
  paymentDate: {
    type: Date,
  },
  paymentReference: {
    type: String,
  },
  paymentDetails: {
    gateway: String,
    reference: String,
    amount: Number,
    currency: String,
    transactionDate: Date,
    cardType: String,
    last4: String,
  },
  paymentAttempts: {
    type: Number,
    default: 0,
  },
  lastPaymentAttempt: {
    date: Date,
    status: String,
    gateway: String,
    reference: String,
    error: String,
  },
  billingPeriod: {
    start: Date,
    end: Date,
  },
  items: [
    {
      description: String,
      quantity: Number,
      rate: Number,
      amount: Number,
    },
  ],
  remindersSent: [{
    type: Date,
  }],
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  }
});

// Update timestamps
invoiceSchema.pre("save", function(next) {
  this.updatedAt = new Date();
  next();
});

// Check if payment is overdue
invoiceSchema.methods.isOverdue = function() {
  return this.status !== 'paid' && new Date() > this.dueDate;
};

// Check if account should be restricted (60+ days overdue)
invoiceSchema.methods.shouldRestrictAccount = function() {
  if (this.status === 'paid') return false;
  const daysOverdue = Math.floor((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
  return daysOverdue >= 60;
};

// Get days overdue for an invoice
invoiceSchema.methods.getDaysOverdue = function() {
  if (this.status === 'paid') return 0;
  const daysOverdue = Math.floor((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
  return Math.max(0, daysOverdue);
};

const Invoice = mongoose.models.Invoice || mongoose.model("Invoice", invoiceSchema);
module.exports = Invoice;
