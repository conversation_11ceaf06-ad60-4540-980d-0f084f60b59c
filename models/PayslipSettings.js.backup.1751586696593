const mongoose = require("mongoose");

const PayslipSettingsSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
    unique: true,
  },
  selfSealingFormat: {
    type: Boolean,
    default: false,
  },
  maskIDNumber: {
    type: Boolean,
    default: false,
  },
  hideDates: {
    type: Boolean,
    default: false,
  },
  showYTDBalances: {
    type: Boolean,
    default: true,
  },
  showCumulativeYTD: {
    type: Boolean,
    default: false,
  },
  hideEmployerContributions: {
    type: Boolean,
    default: false,
  },
  hideTaxableIncomeDeductions: {
    type: Boolean,
    default: false,
  },
  hideBenefits: {
    type: Boolean,
    default: false,
  },
  showEmployeeNumber: {
    type: Boolean,
    default: true,
  },
  showAdditionalNumber: {
    type: Boolean,
    default: false,
  },
  showIncomeTaxNumber: {
    type: Boolean,
    default: true,
  },
  printBankingDetails: {
    type: Boolean,
    default: true,
  },
  printPayPoint: {
    type: Boolean,
    default: false,
  },
  printResidentialAddress: {
    type: Boolean,
    default: false,
  },
  hideTradingNameHeading: {
    type: Boolean,
    default: false,
  },
  hideLeaveBalances: {
    type: Boolean,
    default: false,
  },
  hideLeaveAdjustments: {
    type: Boolean,
    default: false,
  },
  useOldPayslipFormat: {
    type: Boolean,
    default: false,
  },
  payslipComment: {
    type: String,
    trim: true,
  },
  additionalHeadings: {
    type: String,
    trim: true,
  },
});

module.exports =
  mongoose.models.PayslipSettings ||
  mongoose.model("PayslipSettings", PayslipSettingsSchema);
