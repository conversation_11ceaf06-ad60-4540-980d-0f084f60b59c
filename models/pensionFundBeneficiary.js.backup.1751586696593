const mongoose = require("mongoose");

const pensionFundBeneficiarySchema = new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    fundName: {
      type: String,
      required: true,
      trim: true,
    },
    membershipNumber: {
      type: String,
      required: true,
      trim: true,
    },
    contributionPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound index for unique membership numbers per company
pensionFundBeneficiarySchema.index(
  { company: 1, membershipNumber: 1 },
  { unique: true }
);

// Add any middleware or methods
pensionFundBeneficiarySchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

const PensionFundBeneficiary = mongoose.model(
  "PensionFundBeneficiary",
  pensionFundBeneficiarySchema
);

module.exports = PensionFundBeneficiary;
