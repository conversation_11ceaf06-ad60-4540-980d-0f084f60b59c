const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const companySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  companyCode: {
    type: String,
    required: true,
    unique: true,
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  employees: [
    {
      type: Schema.Types.ObjectId,
      ref: "Employee",
    },
  ],
  employerDetails: {
    type: Schema.Types.ObjectId,
    ref: "EmployerDetails",
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    required: true
  },
  trialStartDate: {
    type: Date,
    default: Date.now,
  },
  trialEndDate: {
    type: Date,
    default: function() {
      // Set trial end date to 30 days from creation
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date;
    }
  },
  billingStartDate: {
    type: Date,
    default: function() {
      // Set billing start date to day after trial ends
      const date = new Date();
      date.setDate(date.getDate() + 31);
      return date;
    }
  }
});

// Add method to check trial status
companySchema.methods.isInTrialPeriod = function() {
  const now = new Date();
  return now <= this.trialEndDate;
};

module.exports =
  mongoose.models.Company || mongoose.model("Company", companySchema);
