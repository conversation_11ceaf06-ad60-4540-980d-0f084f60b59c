const mongoose = require("mongoose");

const payslipSchema = new mongoose.Schema({
  payRun: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayRun"
  },
  payrollPeriod: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayrollPeriod",
    required: true
  },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  earnings: [
    {
      type: {
        type: String,
        enum: ["basic", "overtime", "bonus", "allowance"],
      },
      description: String,
      amount: Number,
    },
  ],
  deductions: [
    {
      type: {
        type: String,
        enum: ["tax", "benefits", "loan"],
      },
      description: String,
      amount: Number,
    },
  ],
  grossPay: Number,
  totalDeductions: Number,
  netPay: Number,
  paymentMethod: {
    type: String,
    enum: ["EFT", "Cash", "Cheque"],
    required: true,
  },
  bankDetails: {
    accountHolder: String,
    accountNumber: String,
    bankName: String,
    branchCode: String,
  },
  status: {
    type: String,
    enum: ["draft", "approved", "paid"],
    default: "draft",
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true
  }
});

const Payslip =
  mongoose.models.Payslip || mongoose.model("Payslip", payslipSchema);
module.exports = Payslip;
