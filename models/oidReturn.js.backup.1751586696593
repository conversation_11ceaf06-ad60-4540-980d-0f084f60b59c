const mongoose = require("mongoose");

const oidReturnSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
  submissionDate: Date,
  totalEarnings: Number,
  totalEmployees: Number,
  status: {
    type: String,
    enum: ["draft", "submitted", "accepted", "rejected"],
    default: "draft",
  },
});

module.exports =
  mongoose.models.OIDReturn || mongoose.model("OIDReturn", oidReturnSchema);
