const mongoose = require("mongoose");

const RolloverSchema = new mongoose.Schema({
  employeeId: { type: mongoose.Schema.Types.ObjectId, ref: "Employee" },
  month: { type: String, required: true },
  data: mongoose.Schema.Types.Mixed, // Define your specific data schema
  createdAt: { type: Date, default: Date.now },
});

const Rollover =
  mongoose.models.Rollover || mongoose.model("Rollover", RolloverSchema);
module.exports = Rollover;
