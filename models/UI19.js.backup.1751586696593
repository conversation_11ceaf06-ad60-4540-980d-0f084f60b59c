const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const UI19Schema = new Schema(
  {
    company: {
      type: Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    employee: {
      type: Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    signature: {
      type: {
        type: String,
        enum: ["draw", "type", "upload"],
        required: true,
      },
      data: {
        type: String,
        required: true,
      },
      date: {
        type: Date,
        default: Date.now,
      },
    },
    formData: {
      employerName: String,
      employerIdNumber: String,
      basicSalary: String,
      totalHours: String,
      startDate: Date,
      endDate: Date,
      terminationReason: String,
    },
    modifiedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for better query performance
UI19Schema.index({ company: 1, employee: 1 });
UI19Schema.index({ createdAt: -1 });

// Create and export the model
module.exports = mongoose.model("UI19", UI19Schema);
