---
description: 
globs: 
alwaysApply: true
---

# Your rule content

# .cursor/rules/global.rules

# Enforce that the AI always considers the full Panda Payroll architecture
- name: EnforceArchitecturalGuidelines
  when: ["any-file"]
  instructions: |
    Always consider the entire Panda Payroll architecture—including all
    models, routes, views, utilities, and tests—before suggesting
    changes or generating code. Reflect on how updates in one module
    may impact others, and validate consistency across the codebase.

# Require AI to summarize context before proposing edits
- name: ContextSummary
  when: ["any-file"]
  instructions: |
    Before making any edits, provide a brief summary of which parts of
    the codebase you reviewed (e.g., models/payroll.js, routes/payslip.js,
    shared utilities) and how those influenced your recommendation.


# .cursor/rules/panda-payroll.rules

# Enforce schema alignment for model changes
- applies_to: ["models/**/*.js"]
  instructions: |
    If modifying any Mongoose schema, ensure fields align with existing
    definitions in models/employee.js and models/payroll.js.
    - Update or add schema fields only if they maintain backward
      compatibility.
    - Bump schema version and update related migration scripts if needed.
    - Run or generate unit tests verifying CRUD operations on payroll
      and employee documents.

# Guardrails for route updates
- applies_to: ["routes/**/*.js"]
  instructions: |
    When updating route handlers:
    - Verify that any added or changed HTTP endpoints respect existing
      URL patterns and middleware (authentication, validation).
    - Ensure that changes are reflected in both web and API clients.
    - Update or create integration tests under tests/routes/ accordingly.

# UI consistency for EJS views
- applies_to: ["views/**/*.ejs"]
  instructions: |
    For changes in the frontend:
    - Maintain consistent styling, flash-message patterns, and layout
      partials.
    - Check that any new data passed from controllers is rendered
      correctly in the EJS templates.
    - Update or add view-level tests to cover new or modified UI elements.

# Finalization workflow integrity
- applies_to: ["routes/payslip.js", "views/employeeProfile.ejs"]
  instructions: |
    Any edits around the “Finalize Payslip” feature must:
    - Preserve the logic that locks/unlocks payslips.
    - Automatically increment or select the next period based on doa
      and payFrequency rules.
    - Ensure flash messages read: “The payslip has been finalised. You
      are now viewing the next period’s payslip.”
    - Include end-to-end tests covering both finalization and viewing
      of past periods.
