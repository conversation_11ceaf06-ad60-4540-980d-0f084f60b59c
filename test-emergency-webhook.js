// Emergency test to verify webhook responds immediately
require('dotenv').config();

const axios = require('axios');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3001';
const WEBHOOK_URL = `${BASE_URL}/api/whatsapp/webhook`;

async function testEmergencyWebhookResponse() {
  console.log('🚨 EMERGENCY WEBHOOK RESPONSE TEST 🚨');
  console.log('Testing that webhook responds within 20 seconds...');
  console.log('URL:', WEBHOOK_URL);

  try {
    const startTime = Date.now();
    
    // Test POST request without signature (production scenario)
    const testPayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
            },
            messages: [{
              from: '***********',
              id: 'emergency-test-message',
              timestamp: Date.now().toString(),
              text: {
                body: 'emergency test'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    console.log('\n--- Testing POST without signature (production scenario) ---');
    const response = await axios.post(WEBHOOK_URL, testPayload, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 25000 // 25 second timeout to test the 20 second requirement
    });

    const responseTime = Date.now() - startTime;
    
    console.log('✅ WEBHOOK RESPONDED SUCCESSFULLY!');
    console.log('Status:', response.status);
    console.log('Response time:', responseTime + 'ms');
    
    if (responseTime < 20000) {
      console.log('✅ Response time is within WhatsApp\'s 20-second requirement');
    } else {
      console.log('❌ Response time exceeds 20 seconds - webhook may be disabled by Meta');
    }

    // Test with invalid signature
    console.log('\n--- Testing POST with invalid signature ---');
    const startTime2 = Date.now();
    
    const response2 = await axios.post(WEBHOOK_URL, testPayload, {
      headers: { 
        'Content-Type': 'application/json',
        'x-hub-signature-256': 'sha256=invalid-signature-test'
      },
      timeout: 25000
    });

    const responseTime2 = Date.now() - startTime2;
    
    console.log('✅ WEBHOOK RESPONDED WITH INVALID SIGNATURE!');
    console.log('Status:', response2.status);
    console.log('Response time:', responseTime2 + 'ms');
    
    if (responseTime2 < 20000) {
      console.log('✅ Response time is within WhatsApp\'s 20-second requirement');
    } else {
      console.log('❌ Response time exceeds 20 seconds - webhook may be disabled by Meta');
    }

    console.log('\n🎉 EMERGENCY FIX SUCCESSFUL!');
    console.log('✅ Webhook responds immediately regardless of signature validation');
    console.log('✅ No more risk of Meta disabling the webhook');
    console.log('✅ Production issue should be resolved');

    return true;

  } catch (error) {
    console.error('❌ EMERGENCY TEST FAILED:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    }
    if (error.code === 'ECONNABORTED') {
      console.error('🚨 TIMEOUT ERROR - Webhook took longer than 25 seconds to respond!');
      console.error('🚨 This will cause Meta to disable the webhook!');
    }
    return false;
  }
}

// Run the emergency test
if (require.main === module) {
  testEmergencyWebhookResponse()
    .then((success) => {
      console.log('\n=== EMERGENCY TEST RESULTS ===');
      if (success) {
        console.log('🎉 EMERGENCY FIX WORKING - Safe to deploy to production');
      } else {
        console.log('❌ EMERGENCY FIX FAILED - DO NOT deploy until fixed');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Emergency test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testEmergencyWebhookResponse };
