const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayFrequency = require("../models/PayFrequency");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/company");
const checkCompany = require("../middleware/checkCompany");
const {
  ensureAuthenticated,
  ensureOwner,
} = require("../config/ensureAuthenticated");
const PayrollPeriod = require("../models/PayrollPeriod");
const beneficiaries = require("../models/beneficiaries");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });
const RFIConfig = require("../models/rfiConfig");
const mongoose = require("mongoose");
const fs = require("fs");
const moment = require("moment-timezone");
const dateUtils = require("../utils/dateUtils");
const transporter = require("../config/emailConfig");
const crypto = require("crypto");
const EmployeeCredential = require("../models/employeeCredential");
const ejs = require("ejs");
const path = require("path");
const PayrollService = require("../services/PayrollService");
const RFICalculationService = require("../services/rfiCalculationService");
const payrollCalculations = require("../utils/payrollCalculations");
const EmployeeNumberService = require("../services/employeeNumberService");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const Role = require("../models/role");
const bcrypt = require("bcrypt");
const EmployeeTemplateGenerator = require("../utils/excel-templates");
const multer = require("multer");
const XLSX = require("xlsx");
const PayComponent = require("../models/payComponent");
const AuditLog = require("../models/auditLog");
const PensionFundBeneficiary = require("../models/pensionFundBeneficiary");
const Beneficiary = require("../models/beneficiaries");
const WorkedHours = require("../models/WorkedHours");
const { calculatePeriodTotals } = require("../services/PayrollService");
const { formatDateForDisplay, isProratedMonth, getEndOfMonthDate, getNextMonthEndDate, formatDateForMongoDB, isLastDayOfMonth, getCurrentOrNextLastDayOfMonth } = dateUtils;
const { isValidObjectId } = mongoose;

// Set the default timezone
const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Add this near the top of the file with other middleware imports
const checkAuth = (req, res, next) => {
  if (!req.isAuthenticated()) {
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }
  next();
};

function isNewEmployee(doa, currentMonth) {
  const doaMoment = moment(doa, "YYYY-MM-DD");
  const currentMoment = moment(currentMonth, "YYYY-MM-DD");

  if (doaMoment.isValid() && currentMoment.isValid()) {
    return doaMoment.isSame(currentMoment, "month");
  }

  console.error("Invalid date format:", { doa, currentMonth });
  return false;
}

// Add age calculation utility function
const calculateAge = (dob) => {
  if (!dob) return null;
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

// Add this middleware to check if the user is authenticated
router.use(ensureAuthenticated);

// Apply the middleware to all routes in this file
router.use(ensureAuthenticated);
router.use(checkCompany);

// Add this new route for /employeeManagement
router.get(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Session ID:", req.sessionID);

    try {
      // Assuming the user has a currentCompany field
      const company = await Company.findById(req.user.currentCompany);

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the current company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

// Keep the existing route for /:companyIdentifier/employeeManagement
router.get(
  "/:companyIdentifier/employeeManagement",
  ensureAuthenticated,
  ensureOwner,
  async (req, res) => {
    console.log("EmployeeManagement route handler called");
    console.log("User object:", req.user);
    console.log("Company identifier:", req.params.companyIdentifier);
    console.log("Session ID:", req.sessionID);

    try {
      const companyIdentifier = req.params.companyIdentifier;
      let company;

      if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
        company = await Company.findById(companyIdentifier);
      } else {
        company = await Company.findOne({ companyCode: companyIdentifier });
      }

      if (!company) {
        console.log("Company not found");
        return res.status(404).send("Company not found");
      }

      console.log("Company found:", company);

      // Fetch only the employees for the selected company
      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status dob doa"
      );

      console.log("Employees found:", employees.length);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

router.post("/setActiveTab", ensureAuthenticated, (req, res) => {
  req.session.activeTab = req.body.tab;
  res.sendStatus(200);
});

// Update the API route to fetch employees for the specific company
router.get(
  "/:companyCode/employeeManagement/api/employees",
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      const employees = await Employee.find({ company: company._id }).select(
        "companyEmployeeNumber firstName lastName costCentre status"
      );
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      res
        .status(500)
        .json({ error: "An error occurred while fetching employees" });
    }
  }
);

// Add this new route to handle the update request for take-on tax totals
router.post("/update-take-on-tax-totals", async (req, res) => {
  try {
    const { employees } = req.body;
    console.log("Received employees data:", employees);
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {
        const updatedEmployee = await Employee.findByIdAndUpdate(
          employeeData._id,
          { $set: employeeData },
          { new: true }
        );

        if (updatedEmployee) {
          results.updated++;
        } else {
          results.errors.push({
            id: employeeData._id,
            error: "Employee not found",
          });
        }
      } catch (error) {
        results.errors.push({
          id: employeeData._id,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Add this new route to handle the update request for employees
router.post("/update-essentials", async (req, res) => {
  try {
    const { employees } = req.body;
    console.log("Received employees data:", employees);
    const results = { updated: 0, created: 0, errors: [] };
    const settings = await EmployeeNumberSettings.findOne();

    for (const employeeData of employees) {
      try {
        console.log("Processing employee data:", employeeData);

        // Handle date fields
        ["dob", "doa"].forEach((dateField) => {
          if (employeeData[dateField]) {
            employeeData[dateField] = moment
              .utc(employeeData[dateField])
              .startOf("day")
              .toDate();
          } else {
            employeeData[dateField] = null;
          }
        });

        console.log("Processed date fields:", employeeData);

        // Set email to null if it's blank
        if (employeeData.email === "") {
          employeeData.email = null;
        }

        if (settings && settings.mode === "automatic") {
          try {
            employeeData.employeeNumber = await settings.generateNextNumber();
          } catch (error) {
            console.error("Error generating employee number:", error);
            results.errors.push(
              `Error generating employee number for ${employeeData.email}: ${error.message}`
            );
            continue;
          }
        }

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          // If there's no _id, create a new employee
          const newEmployee = new Employee(employeeData);
          await newEmployee.save();
          results.created++;
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Add this new route to handle the update request for payment & banking info
router.post("/update-payment-banking-info", async (req, res) => {
  try {
    const { employees } = req.body;
    console.log("Received employees data:", employees);
    const results = { updated: 0, errors: [] };

    for (const employeeData of employees) {
      try {
        console.log("Processing employee data:", employeeData);

        if (employeeData._id) {
          // If the employee has an _id, find and update the existing record
          const updatedEmployee = await Employee.findByIdAndUpdate(
            employeeData._id,
            employeeData,
            { new: true, runValidators: true }
          );
          if (updatedEmployee) {
            results.updated++;
          } else {
            results.errors.push(
              `Employee with id ${employeeData._id} not found`
            );
          }
        } else {
          results.errors.push("Employee ID not provided");
        }
      } catch (error) {
        console.error("Error processing employee:", employeeData, error);
        results.errors.push(
          `Error processing employee ${
            employeeData.email || "without email"
          }: ${error.message}`
        );
      }
    }

    res.json({
      success: true,
      message: "Employees processed",
      results: results,
    });
  } catch (error) {
    console.error("Error updating employees:", error);
    res.status(500).json({
      success: false,
      message: "Error updating employees",
      error: error.message,
    });
  }
});

// Update residential address route
router.post(
  "/:companyCode/employeeManagement/update-residential-address",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employees } = req.body;
      console.log("Received employees data:", employees);
      const results = { updated: 0, errors: [] };

      for (const employeeData of employees) {
        try {
          if (employeeData._id) {
            const updatedEmployee = await Employee.findByIdAndUpdate(
              employeeData._id,
              { residentialAddress: employeeData.residentialAddress },
              { new: true, runValidators: true }
            );
            if (updatedEmployee) {
              results.updated++;
            } else {
              results.errors.push(
                `Employee with id ${employeeData._id} not found`
              );
            }
          } else {
            results.errors.push("Employee ID not provided");
          }
        } catch (error) {
          console.error("Error processing employee:", employeeData, error);
          results.errors.push(
            `Error processing employee ${employeeData._id}: ${error.message}`
          );
        }
      }

      res.json({
        success: true,
        message: "Employees processed",
        results: results,
      });
    } catch (error) {
      console.error("Error updating employees:", error);
      res.status(500).json({
        success: false,
        message: "Error updating employees",
        error: error.message,
      });
    }
  }
);

// Keep existing routes...
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Update the existing skills-equity route
router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});

router.get("/api/employee-status/:employeeId", async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.employeeId);

    if (!employee) {
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    res.json({
      success: true,
      isTerminated: employee.employmentStatus === "Terminated",
      lastDayOfService: employee.endServiceDate,
      employmentStatus: employee.employmentStatus,
      terminationReason: employee.terminationReason,
    });
  } catch (error) {
    console.error("Error fetching employee status:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
});

router.get("/employee-list", ensureAuthenticated, (req, res) => {
  // ... fetch necessary data ...
  res.render("employeeListTab", {
    /* ... pass necessary data ... */
  });
});

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      console.log("Available Pay Frequencies:", {
        count: payFrequencies.length,
        frequencies: payFrequencies.map((pf) => ({
          id: pf._id,
          name: pf.name,
          frequency: pf.frequency,
        })),
      });

      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get("/bulk-actions", ensureAuthenticated, (req, res) => {
  res.render("bulkActionsTab");
});

// Add a new route for company selection
router.get("/select-company", async (req, res) => {
  try {
    console.log("\n=== Select Company GET Route Start ===");

    // Fetch user with populated companies
    const user = await User.findById(req.user._id).populate("companies");
    if (!user) {
      console.error("User not found");
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    console.log("User companies:", user.companies?.length || 0);

    // Generate CSRF token
    const csrfToken = req.csrfToken();
    console.log("Generated CSRF token:", csrfToken);

    // Render with all necessary data
    res.render("select-company", {
      user,
      companies: user.companies || [], // Pass companies array
      messages: req.flash(),
      csrfToken,
      partialsDir: "partials",
    });
  } catch (error) {
    console.error("Error loading select-company page:", error);
    req.flash("error", "Error loading companies");
    res.redirect("/");
  }
});

router.post(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        throw new Error("Company not found");
      }

      const employeeData = req.body;

      // Parse dates
      employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();

      // Format working days properly
      if (employeeData.workingDays) {
        // Convert single value to array if needed
        const workingDaysArray = Array.isArray(employeeData.workingDays)
          ? employeeData.workingDays
          : [employeeData.workingDays];

        // Format each working day as an object
        employeeData.regularHours = {
          ...employeeData.regularHours,
          workingDays: workingDaysArray.map((day) => ({
            day: day,
            type: "Normal Day",
          })),
        };
      }

      // Fetch PayFrequency details
      const payFrequency = await PayFrequency.findById(
        employeeData.payFrequency
      );
      if (!payFrequency) {
        throw new Error("Invalid pay frequency selected");
      }

      // Set PayFrequency related fields
      employeeData.payFrequency = payFrequency._id;
      employeeData.lastDayOfPeriod = payFrequency.lastDayOfPeriod;
      employeeData.firstPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;
      employeeData.initialPayrollPeriodEndDate =
        payFrequency.initialPayrollPeriodEndDate;

      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        throw new Error("Employee number settings not found");
      }

      if (settings.mode === "manual") {
        if (!employeeData.companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }
        // Check for uniqueness in manual mode
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: employeeData.companyEmployeeNumber,
        });

        if (existingEmployee) {
          throw new Error("Employee number already exists");
        }
      } else {
        // Automatic mode
        try {
          employeeData.companyEmployeeNumber =
            await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      }

      const newEmployee = new Employee({
        ...employeeData,
        globalEmployeeId: uuidv4(),
        company: company._id,
      });

      console.log("Employee data before creating model:", employeeData);
      console.log("New employee object:", newEmployee);

      await newEmployee.validate(); // This will throw a ValidationError if there are issues
      await newEmployee.save();

      res.status(200).json({
        message: "Employee added successfully",
        employeeId: newEmployee._id,
      });
    } catch (error) {
      console.error("Error adding new employee:", error);
      res.status(400).json({ error: error.message });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;

      console.log("=== Regular Hours Update ===");
      console.log("Request body:", req.body);

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      console.log("Updated employee regular hours:", employee.regularHours);
      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

async function generateEmployeeNumber(company, settings) {
  settings.lastGeneratedNumber += 1;
  await settings.save();

  const paddedNumber = settings.lastGeneratedNumber.toString().padStart(4, "0");
  return settings.numberPattern.replace("{0000}", paddedNumber);
}

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode: companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const currentDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Set default values if no payroll exists
      const defaultBasicSalary = 0;
      const basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // Get firstPayrollPeriodEndDate from the payFrequency model
      const firstPayrollPeriodEndDate = moment.tz(
        employee.payFrequency.firstPayrollPeriodEndDate, // Use the value from payFrequency model
        DEFAULT_TIMEZONE
      );

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        console.log("Generating periods with PayFrequency settings:", {
          frequency: payFrequency.frequency,
          lastDayOfPeriod: payFrequency.lastDayOfPeriod,
          firstPayrollPeriodEndDate: moment
            .tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE)
            .format("YYYY-MM-DD HH:mm:ss"),
        });

        // Use moment.tz for all dates
        const employeeStartDate = moment
          .tz(employee.doa, DEFAULT_TIMEZONE)
          .startOf("day");
        const currentEndDate = moment
          .tz(currentDate, DEFAULT_TIMEZONE)
          .endOf("day");

        console.log("Period Generation Dates:", {
          employeeStartDate: employeeStartDate.format("YYYY-MM-DD"),
          currentEndDate: currentEndDate.format("YYYY-MM-DD"),
        });

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          const proratedResult = payrollCalculations.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            period.startDate,
            period.endDate
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency
          );

          return {
            ...period.toObject(),
            basicSalary: Number(proratedResult.proratedSalary),
            basicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // When finding the current period
      let currentPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        currentPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the normal logic for current/next period
        currentPeriod =
          payPeriodsWithDetails.find((p) => !p.isFinalized) ||
          payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
      }

      // Calculate initial period values
      const currentPeriodCalculations = await PayrollService.calculatePayrollTotals(
        employeeId,
        currentPeriod?.endDate ||
          moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
        company._id
      );

      console.log("Initial Period Calculations:", currentPeriodCalculations);

      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );
      const totalIncome = Number(
        currentPeriodCalculations.totalIncome || proratedSalary
      );

      console.log("Salary Calculations:", {
        basicSalary,
        proratedSalary,
        totalIncome,
        isProrated: currentPeriodCalculations.proRataDetails?.isProrated,
      });

      // Initialize payroll totals with the correct values
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome,
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0)
        ),
      };

      console.log("Final Payroll Totals:", payrollTotals);

      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(currentPeriodCalculations.basicSalary?.full || currentPeriodCalculations.basicSalary || 0),
          proratedSalary: Number(currentPeriodCalculations.basicSalary?.prorated || currentPeriodCalculations.proratedSalary || 0),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(currentPeriodCalculations.proRataDetails?.percentage || 100),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0),
          netPay: Number(currentPeriodCalculations.netPay || 0)
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const employeeAge = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting 
      const dobFormatted = employee.dob 
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format('DD MMMM YYYY') 
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;
      const pensionFundDeduction =
        payroll?.pensionFund?.fixedContributionEmployee || 0;
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // Find the current payroll period
      let payrollPeriod;
      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        payrollPeriod = payPeriodsWithDetails.find(
          (p) => p.endDate.toISOString() === selectedMonth
        );
      } else {
        // Otherwise, use the current period
        payrollPeriod = currentPeriod;
      }

      // If no period is found, create a default one
      if (!payrollPeriod) {
        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month");
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary,
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };
      }

      // Get current payroll data
      // Get current payroll data
      const taxableIncome = payrollCalculations.calculateTotalTaxableIncome(
        payroll || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly"
      );

      // Calculate all deductions using new function
      const deductionsResult = payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome
      );

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });

      console.log('Detailed Payroll Calculations Debug', {
        paye: {
          amount: currentPeriodCalculations.paye,
          monthlyTax: currentPeriodCalculations.paye,
        },
        uif: {
          amount: currentPeriodCalculations.uif,
        },
        deductions: {
          statutory: {
            paye: currentPeriodCalculations.paye,
            uif: currentPeriodCalculations.uif,
          },
          total: currentPeriodCalculations.totalDeductions,
        },
        fullCalculations: currentPeriodCalculations
      });

      // Travel Allowance Calculation
      console.log('Travel Allowance Debug - Payroll Object', {
        travelAllowance: payroll?.travelAllowance,
        hasFixedAllowance: !!payroll?.travelAllowance?.fixedAllowance,
        hasReimbursedExpenses: !!payroll?.travelAllowance?.reimbursedExpenses
      });
      
      if (payroll?.travelAllowance?.fixedAllowance || payroll?.travelAllowance?.reimbursedExpenses) {
        const travelAllowanceResult = await PayrollService.processTravelAllowance(employee, payroll);
        
        console.log('Travel Allowance Processing Result', travelAllowanceResult);
        
        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;
          
          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: 20
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: 80
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes
          };
        }
      }

      // Log employee age details
      console.log('🎂 Employee Profile Age Debug:', {
        'Employee ID': employeeId,
        'Date of Birth': employee.dob ? moment(employee.dob).format('YYYY-MM-DD') : 'N/A',
        'Calculated Age': calculateAge(employee.dob),
        'Company Code': companyCode
      });

      // Add debug logging
      console.log("Render Data:", {
        currentPeriod: !!currentPeriod,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        ...renderData.payrollTotals,
      });

      // Enhanced Payroll Details Calculation
      const payrollDetails = await PayrollService.calculatePayrollDetails(
        employee, 
        payroll, 
        currentPeriod
      );

      renderData.payrollDetails = payrollDetails;

      res.render("employeeProfile", renderData);
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "error",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// // Helper function to determine if an employee is new in the current month

// // Update the POST route for saving basic salary

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;

      console.log("Processing employee update:", {
        employeeId,
        companyCode,
        data: employeeData,
      });

      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates
      if (employeeData.dob) {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      }
      if (employeeData.doa) {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber: employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        streetAddress: employeeData.streetAddress,
        suburb: employeeData.suburb,
        city: employeeData.city,
        postalCode: employeeData.postalCode,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!updatedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      console.log("Employee updated successfully:", {
        id: updatedEmployee._id,
        firstName: updatedEmployee.firstName,
        lastName: updatedEmployee.lastName,
        department: updatedEmployee.department,
        costCentre: updatedEmployee.costCentre,
      });

      // Send success response with the success flag
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

// In the edit route
router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));

      console.log("Edit Employee - Pay Frequencies:", {
        available: mappedFrequencies,
        current: employee.payFrequency,
        employeePayFreqId: employee.payFrequency?._id,
      });

      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Update the finalize route in employeeManagement.js
router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date
      if (populatedEmployee.lastDayOfService) {
        const terminationDate = moment(
          populatedEmployee.lastDayOfService
        ).endOf("day");
        const periodEndDate = moment(currentPeriodEndDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Finalize current period
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      await currentPeriod.save({ session });

      // Generate next period with comprehensive termination checks
      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          moment(currentPeriodEndDate).isBefore(
            moment(populatedEmployee.lastDayOfService)
          ));

      if (canGenerateNext) {
        try {
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod,
            session
          );
          console.log("Next period generated successfully:", {
            periodId: nextPeriod._id,
            startDate: moment(nextPeriod.startDate).format("YYYY-MM-DD"),
            endDate: moment(nextPeriod.endDate).format("YYYY-MM-DD"),
          });
        } catch (error) {
          console.error("Error generating next period:", error);
          throw new Error(`Failed to generate next period: ${error.message}`);
        }
      } else {
        console.log(
          "Skipping next period generation - employee is terminated or serving notice"
        );
      }

      await session.commitTransaction();
      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    } finally {
      session.endSession();
    }
  }
);

// Add this route after your other employeeManagement routes
router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Inactive",
      });

      res.render("reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the reinstate page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({
        company: company._id,
        status: "Terminated",
      });

      res.render("undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the undo end of service page");
    }
  }
);

// Excel Imports Route
router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      res.render("bulk-add-employees", {
        company,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass the CSRF token
      });
    } catch (error) {
      console.error("Error loading bulk add employees page:", error);
      req.flash(
        "error",
        "An error occurred while loading the bulk add employees page"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Get employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Get employees for this company
      const employees = await Employee.find({ company: company._id })
        .select(
          "companyEmployeeNumber firstName lastName dob doa idType idNumber passportNumber passportCountryCode email"
        )
        .sort({ lastName: 1, firstName: 1 });

      // Render the essentials page with all required data
      res.render("essentials", {
        company,
        companyCode,
        employees,
        employeeNumberSettings,
        user: req.user,
        moment,
        DEFAULT_TIMEZONE,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      req.flash("error", "An error occurred while loading the essentials page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line
      });
    } catch (error) {
      console.error("Error loading payment & banking info page:", error);
      res
        .status(500)
        .send(
          "An error occurred while loading the payment & banking info page"
        );
    }
  }
);

// Add similar routes for other information inputs
router.get(
  "/:companyCode/employeeManagement/residential-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/postal-address",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/identifying-numbers",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/pay-frequency",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Add similar routes for other payroll inputs
router.get(
  "/:companyCode/employeeManagement/regular-inputs",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/clocking-system-import",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/payslip-dates",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/add-once-off-payslips",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

router.get(
  "/:companyCode/employeeManagement/template-mapping",
  ensureAuthenticated,
  async (req, res) => {
    /* Similar implementation */
  }
);

// Take on Inputs Route
router.get(
  "/:companyCode/employeeManagement/take-on-tax-totals",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const employees = await Employee.find({ company: company._id });

      res.render("take-on-tax-totals", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading take-on tax totals page:", error);
      res
        .status(500)
        .send("An error occurred while loading the take-on tax totals page");
    }
  }
);

// Update the addNew route handler
router.get(
  "/:companyCode/employeeManagement/addNew",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch pay frequencies from the database
      const payFrequencies = await PayFrequency.find({}).lean();

      res.render("addNewEmployeeDetails", {
        companyCode,
        company,
        payFrequencies, // Pass payFrequencies to the template
        user: req.user,
        title: "Add New Employee",
      });
    } catch (error) {
      console.error("Error:", error);
      req.flash("error", "Failed to load employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

function incrementEmployeeNumber(currentNumber) {
  try {
    // Remove any leading zeros and convert to number
    const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
    if (isNaN(numericPart)) {
      throw new Error("Invalid employee number format");
    }

    // Increment the number
    const newNumber = (parseInt(numericPart) + 1).toString().padStart(4, "0");
    return newNumber;
  } catch (error) {
    console.error("Error incrementing employee number:", error);
    return "0001"; // Return default number if something goes wrong
  }
}

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        // Manual mode
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          throw new Error("Employee number is required in manual mode");
        }

        // Check for uniqueness
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Create employee data object
      const employeeData = {
        ...req.body,
        globalEmployeeId: uuidv4(),
        companyEmployeeNumber,
        company: company._id,
        status: "Active",
        firstPayrollPeriodEndDate: payFrequency.firstPayrollPeriodEndDate,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,
      };

      // Create and save the new employee
      const newEmployee = new Employee(employeeData);
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement`,
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

// Helper function to increment employee number
function incrementEmployeeNumber(lastNumber) {
  // Ensure lastNumber is a string and handle null/undefined
  const numStr = String(lastNumber || "0000");
  // Convert to number, increment, and pad with zeros
  const nextNum = (parseInt(numStr) + 1).toString().padStart(4, "0");
  return nextNum;
}

// Add this POST route for company selection
router.post("/companies/select", ensureAuthenticated, async (req, res) => {
  try {
    console.log("\n=== Select Company POST Route Start ===");
    const { companyId } = req.body;

    // Validate input
    if (!companyId) {
      req.flash("error", "No company selected");
      return res.redirect("/companies/select");
    }

    // Find user and update current company
    const user = await User.findById(req.user._id);
    if (!user) {
      req.flash("error", "User not found");
      return res.redirect("/login");
    }

    // Verify user has access to this company
    if (!user.companies.includes(companyId)) {
      console.error("User does not have access to this company");
      req.flash("error", "You don't have access to this company");
      return res.redirect("/companies/select");
    }

    // Update user's current company
    user.currentCompany = companyId;
    await user.save();

    // Get company code for redirect
    const company = await Company.findById(companyId);
    if (!company) {
      req.flash("error", "Company not found");
      return res.redirect("/companies/select");
    }

    console.log("Company selected successfully:", {
      userId: user._id,
      companyId: company._id,
      companyCode: company.companyCode,
    });

    req.flash("success", `Switched to ${company.name}`);
    res.redirect(`/clients/${company.companyCode}/dashboard`);
  } catch (error) {
    console.error("Error selecting company:", error);
    req.flash("error", "Error selecting company");
    res.redirect("/companies/select");
  }
});

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Processing delete request for employee:", employeeId);

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      console.log(
        `Successfully deleted employee ${employeeId} and associated records`
      );

      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Delete page route hit:", { companyCode, employeeId });

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// router.post(
//   "/clients/:companyCode/employeeProfile/:employeeId/edit",
//   ensureAuthenticated,
//   async (req, res) => {
//     try {
//       const { companyCode, employeeId } = req.params;
//       const {
//         firstName,
//         lastName,
//         dob,
//         idType,
//         idNumber,
//         passportNumber,
//         payFrequency,
//         doa,
//         jobTitle,
//         email,
//         costCentre,
//         incomeTaxNumber,
//         employeeNumber,
//         paymentMethod,
//         bank,
//         accountType,
//         accountNumber,
//         branchCode,
//         holderRelationship,
//       } = req.body;

//       // Fetch the PayFrequency document
//       const payFrequencyDoc = await PayFrequency.findById(payFrequency);
//       if (!payFrequencyDoc) {
//         throw new Error("Invalid pay frequency selected");
//       }

//       const updateData = {
//         firstName,
//         lastName,
//         dob: parseAndFormatDate(dob),
//         idType,
//         idNumber,
//         passportNumber,
//         payFrequency: payFrequencyDoc._id, // Save the PayFrequency ObjectId
//         doa: parseAndFormatDate(doa),
//         jobTitle,
//         email,
//         costCentre,
//         incomeTaxNumber,
//         companyEmployeeNumber: employeeNumber,
//         paymentMethod,
//         bank,
//         accountType,
//         accountNumber,
//         branchCode,
//         holderRelationship,
//         lastDayOfPeriod: payFrequencyDoc.lastDayOfPeriod,
//         firstPayrollPeriodEndDate: payFrequencyDoc.initialPayrollPeriodEndDate,
//         initialPayrollPeriodEndDate:
//           payFrequencyDoc.initialPayrollPeriodEndDate,
//       };

//       // Calculate endOfMonthDate based on doa
//       if (doa) {
//         updateData.endOfMonthDate = moment(doa)
//           .endOf("month")
//           .format("YYYY-MM-DD");
//       }

//       // Remove undefined fields
//       Object.keys(updateData).forEach(
//         (key) => updateData[key] === undefined && delete updateData[key]
//       );

//       // If payFrequency is provided, validate it
//       if (updateData.payFrequency) {
//         if (!isValidObjectId(updateData.payFrequency)) {
//           // If it's not a valid ObjectId, either remove it or set to null
//           delete updateData.payFrequency;
//           // Or set to null:
//           // updateData.payFrequency = null;
//         }
//       }
//       }

//       const updatedEmployee = await Employee.findByIdAndUpdate(
//         employeeId,
//         { $set: updateData },
//         { new: true, runValidators: true }
//       );

//       if (!updatedEmployee) {
//         return res.status(404).json({ error: "Employee not found" });
//       }

//       res.json(updatedEmployee);
//     } catch (error) {
//       console.error("Error updating payroll data:", error);
//       res
//         .status(500)
//         .json({ error: "Error updating payroll data", details: error.message });
//     }
//   }
// );

//_____________________

// GET route for regular hours page
router.get(
  "/:companyCode/:employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Fetch the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Fetch the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the regular hours page
      res.render("regularHours", {
        company,
        employee,
        csrfToken: req.csrfToken(), // Add CSRF token
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering regular hours form:", error);
      req.flash("error", "Failed to load regular hours form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

//_____________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection, // Make sure this middleware is here
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Generate CSRF token
      const csrfToken = req.csrfToken();
      console.log("Generated CSRF token:", csrfToken); // Debug log

      // Render with all necessary data
      res.render("eti", {
        company,
        employee,
        user: req.user,
        csrfToken, // Pass the token explicitly
        messages: req.flash(),
        moment: require("moment"), // Add moment if needed for date formatting
      });
    } catch (error) {
      console.error("Error rendering ETI page:", error);
      req.flash("error", "An error occurred while loading the ETI page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for ETI updates
router.post(
  "/:companyCode/employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1);
      effectiveDate.setHours(0, 0, 0, 0);

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/eti`
      );
    }
  }
);

//__________________________

router.get(
  "/:companyCode/:employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("skillsEquity", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering Skills Equity page:", error);
      req.flash(
        "error",
        "An error occurred while loading the Skills Equity page"
      );
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// POST route for updating Skills Equity
router.post(
  "/:companyCode/employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        gender,
        race,
        occupationLevel,
        occupationCategory,
        jobValue,
        province,
        disabled,
        foreignNational,
        notRSACitizen,
      } = req.body;

      // First find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find and update employee using company._id
      const employee = await Employee.findOneAndUpdate(
        {
          _id: employeeId,
          company: company._id, // Use company._id instead of req.company._id
        },
        {
          $set: {
            gender,
            race,
            occupationLevel,
            occupationCategory,
            jobValue,
            province,
            disabled: !!disabled,
            foreignNational: !!foreignNational,
            notRSACitizen: !!notRSACitizen,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Success case
      req.flash("success", "Skills & Equity information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating Skills & Equity:", error);
      req.flash("error", "Failed to update Skills & Equity information");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/skillsEquity`
      );
    }
  }
);

// Update basic salary route
router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { employeeId, basicSalary, hourlyPaid } = req.body;
      const { companyCode } = req.params;

      console.log("Updating basic salary:", {
        employeeId,
        basicSalary,
        hourlyPaid,
        companyCode,
      });

      // Find employee and company
      const employee = await Employee.findById(employeeId);
      const company = await Company.findOne({ companyCode });

      if (!employee || !company) {
        throw new Error("Employee or company not found");
      }

      // Find current period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      if (!currentPeriod) {
        throw new Error("No active period found");
      }

      // Update current period
      currentPeriod.basicSalary = parseFloat(basicSalary);
      await currentPeriod.save({ session });

      // Update current payroll
      const currentPayroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          company: company._id,
          month: currentPeriod.endDate,
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        {
          new: true,
          upsert: true,
          session,
        }
      );

      // Update all future periods
      await PayrollPeriod.updateMany(
        {
          employee: employeeId,
          company: company._id,
          startDate: { $gt: currentPeriod.endDate },
          isFinalized: false,
        },
        {
          $set: { basicSalary: parseFloat(basicSalary) },
        },
        { session }
      );

      // Update all future payrolls
      await Payroll.updateMany(
        {
          employee: employeeId,
          company: company._id,
          month: { $gt: currentPeriod.endDate },
          status: "draft",
        },
        {
          $set: {
            basicSalary: parseFloat(basicSalary),
            hourlyPaid: hourlyPaid === "on",
          },
        },
        { session }
      );

      // Update employee's hourly status
      if (!employee.regularHours) {
        employee.regularHours = {};
      }
      employee.regularHours.hourlyPaid = hourlyPaid === "on";
      await employee.save({ session });

      await session.commitTransaction();

      console.log("Basic salary update completed:", {
        currentPeriod: currentPeriod._id,
        basicSalary: parseFloat(basicSalary),
        futurePeriodsUpdated: true,
      });

      req.flash("success", "Basic salary updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      await session.abortTransaction();
      console.error("Error saving basic salary:", error);
      req.flash("error", "Failed to update basic salary");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/addBasicSalary`
      );
    } finally {
      session.endSession();
    }
  }
);

router.get(
  "/:companyCode/addBasicSalary/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Find the most recent unfinalized payroll
      const latestPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // Determine relevant date
      let relevantDate;
      if (latestPayroll && !latestPayroll.finalised) {
        relevantDate = latestPayroll.month;
      } else {
        relevantDate = getCurrentOrNextLastDayOfMonth();
      }

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(relevantDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(relevantDate),
          basicSalary: latestPayroll ? latestPayroll.basicSalary : 0,
        });
        await payroll.save();
      }

      // Render the template with all necessary data
      res.render("addBasicSalary", {
        employee,
        payroll,
        relevantDate,
        company,
        companyCode,
        formatDateForDisplay, // Pass the function to the template
        formattedDate: formatDateForDisplay(relevantDate),
        messages: req.flash(),
        csrfToken: req.csrfToken(), // Add this line to pass CSRF token
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect("/");
    }
  }
);

router.post("/saveBasicSalary", async (req, res) => {
  try {
    const { employeeId, relevantDate, companyId, basicSalary } = req.body;

    if (!employeeId || !relevantDate || !companyId || !basicSalary) {
      console.error("Missing required fields:", req.body);
      req.flash("error", "Missing required fields");
      return res.redirect(
        `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
      );
    }

    const formattedDate = formatDateForMongoDB(new Date(relevantDate));

    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: companyId,
      month: formattedDate,
    });

    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        company: companyId,
        month: formattedDate,
      });
    }

    payroll.basicSalary = parseFloat(basicSalary);
    await payroll.save();

    req.flash("success", "Basic salary updated successfully");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    console.error("Error saving basic salary:", error);
    req.flash("error", "An error occurred while saving the basic salary");
    res.redirect(
      `/clients/${req.body.companyCode}/employeeProfile/${req.body.employeeId}`
    );
  }
});

// In the route that renders addBasicSalary

router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      const currentPeriod = {
        startDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        ),
        endDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        ),
      };

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(currentDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(currentDate),
          basicSalary: 0,
        });
        await payroll.save();
      }

      // Format the date directly
      const formattedDate = currentPeriod.endDate.toLocaleDateString("en-GB");

      console.log("Rendering addBasicSalary with:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll._id,
        currentPeriod,
      });

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod,
        formattedDate,
        payroll,
        relevantDate: currentDate,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add this route to handle hourly status updates
router.post(
  "/api/employee/:employeeId/hourly-status",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const { hourlyPaid } = req.body;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res
          .status(404)
          .json({ success: false, message: "Employee not found" });
      }

      // Update employee's hourly status
      if (!employee.regularHours) {
        employee.regularHours = {};
      }
      employee.regularHours.hourlyPaid =
        hourlyPaid === "true" || hourlyPaid === true;
      await employee.save();

      res.json({
        success: true,
        hourlyPaid: employee.regularHours.hourlyPaid,
      });
    } catch (error) {
      console.error("Error updating hourly status:", error);
      res
        .status(500)
        .json({ success: false, message: "Internal server error" });
    }
  }
);

// In the route that renders addBasicSalary.ejs
router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/companies/select");
      }

      // Find employee with populated company
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company");

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get current payroll period end date
      const currentDate = getCurrentOrNextLastDayOfMonth();
      const currentPeriod = {
        startDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        ),
        endDate: new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        ),
      };

      // Find or create payroll for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: formatDateForMongoDB(currentDate),
      });

      if (!payroll) {
        // Create new payroll if none exists
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: formatDateForMongoDB(currentDate),
          basicSalary: 0,
        });
        await payroll.save();
      }

      // Format the date directly
      const formattedDate = currentPeriod.endDate.toLocaleDateString("en-GB");

      console.log("Rendering addBasicSalary with:", {
        employeeId: employee._id,
        companyId: company._id,
        payrollId: payroll._id,
        currentPeriod,
      });

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod,
        formattedDate,
        payroll,
        relevantDate: currentDate,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req, // Pass the request object
        path: req.path, // Pass the current path
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add this POST route handler for self-service
router.post(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("\n=== Self Service POST Route Start ===");
      console.log("Request Body:", req.body);

      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        console.log("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const updates = [];
      const baseUrl = `${req.protocol}://${req.get("host")}`;
      console.log("Base URL for setup:", baseUrl);

      // Get employee role once for all employees
      console.log("Fetching employee role");
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        throw new Error("Employee role not found in the system");
      }
      console.log("Found employee role:", employeeRole._id);

      // Get all employee IDs from the email fields
      const employeeIds = Object.keys(req.body)
        .filter((key) => key.startsWith("email_"))
        .map((key) => key.split("_")[1]);

      console.log("Processing employees:", employeeIds);

      // Process each employee
      for (const employeeId of employeeIds) {
        try {
          console.log(`\n=== Processing Employee ${employeeId} ===`);

          const updateData = {
            email: req.body[`email_${employeeId}`],
            selfServiceEnabled: req.body[`enabled_${employeeId}`] === true,
          };

          console.log("Update data:", updateData);

          const employee = await Employee.findById(employeeId);
          if (!employee) {
            console.log(`Employee not found: ${employeeId}`);
            continue;
          }

          console.log("Current employee state:", {
            id: employee._id,
            email: employee.email,
            selfServiceEnabled: employee.selfServiceEnabled,
            essEmailSent: employee.essEmailSent,
            hasUser: !!employee.user,
          });

          // Update employee data
          employee.email = updateData.email;
          employee.selfServiceEnabled = updateData.selfServiceEnabled;

          // If enabling self-service
          if (updateData.selfServiceEnabled) {
            console.log("Self-service being enabled - checking user account");

            // Get employee role
            const employeeRole = await Role.findOne({ name: "employee" });
            if (!employeeRole) {
              throw new Error("Employee role not found");
            }
            console.log("Found employee role:", employeeRole._id);

            // Check if user account exists
            let user = await User.findOne({ email: employee.email });

            if (user) {
              console.log("Existing user found:", user._id);
              // Update existing user if needed
              if (
                !user.role ||
                user.role.toString() !== employeeRole._id.toString()
              ) {
                user.role = employeeRole._id;
                await user.save();
                console.log("Updated user role to employee");
              }
            } else {
              console.log(
                "Creating new user account with role:",
                employeeRole._id
              );
              // Create new user with employee role
              user = new User({
                firstName: employee.firstName,
                lastName: employee.lastName,
                email: employee.email,
                phone: employee.phone,
                role: employeeRole._id,
                companies: [company._id],
                currentCompany: company._id,
                isVerified: false,
                password: crypto.randomBytes(20).toString("hex"), // Temporary password
              });

              await user.save();
              console.log("New user created:", user._id);
            }

            // Link user and role to employee
            employee.user = user._id;
            employee.role = employeeRole._id;

            // Generate and send setup email
            console.log("Generating setup token");
            const setupResult =
              await SelfServiceTokenService.handleSelfServiceEnable(
                employee,
                baseUrl
              );
            console.log("Setup result:", setupResult);

            updates.push({
              employee: employee._id,
              status: "setup_initiated",
              user: user._id,
            });
          }

          await employee.save();
          console.log("Employee saved successfully");
        } catch (error) {
          console.error(`Error processing employee ${employeeId}:`, error);
          updates.push({
            employeeId,
            status: "error",
            error: error.message,
          });
        }
      }

      console.log("\nFinal updates summary:", updates);

      res.json({
        success: true,
        message: "Self-service settings updated successfully",
        updates,
      });
    } catch (error) {
      console.error("Error in self-service update:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update self-service settings",
        error: error.message,
      });
    }
  }
);

// Add these routes after your existing routes
router.post(
  "/api/employees/:employeeId/enableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Get base URL for email
      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user if needed
        if (
          !user.role ||
          user.role.toString() !== employeeRole._id.toString()
        ) {
          user.role = employeeRole._id;
          await user.save();
        }
      } else {
        // Create new user account
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          password: crypto.randomBytes(20).toString("hex"), // Temporary password
        });

        await user.save();
      }

      // Link user and role to employee
      employee.user = user._id;
      employee.role = employeeRole._id;
      employee.selfServiceEnabled = true;

      // Generate and send setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      await employee.save();

      res.json({
        success: true,
        message: "Self-service enabled successfully",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/disableSelfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      await employee.save();

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/api/employees/:employeeId/resendInvite",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      const baseUrl = `${req.protocol}://${req.get("host")}`;

      // Resend setup email
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      res.json({
        success: true,
        message: "Invitation resent successfully",
      });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend invite",
        error: error.message,
      });
    }
  }
);

// Self Service Routes
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Disable self-service route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing disable request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();
      console.log("Employee self-service disabled successfully");

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Update the enable self-service route
router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Enable self-service route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing enable request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        console.log("Employee role not found");
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });
      console.log("Existing user found:", user ? "yes" : "no");

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
        });
      }

      await user.save();
      console.log("User saved successfully");

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      await employee.save();
      console.log("Employee updated successfully");

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email sent successfully");

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    console.log("Resend invite route hit");
    try {
      const { employeeId } = req.params;
      console.log("Processing resend request for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Generate new token
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );
      console.log("Generated new token for employee");

      await employee.save();
      console.log("Employee updated with new token");

      // Send new setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      const mailOptions = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_USER}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account setup link has been renewed. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      await transporter.sendMail(mailOptions);
      console.log("Setup email resent successfully");

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);

// Add this POST route for adding new employees
router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      if (!settings) {
        return res.status(400).json({
          success: false,
          error: "Employee number settings not found",
        });
      }

      // Generate or validate employee number
      let companyEmployeeNumber;
      if (settings?.mode === "automatic") {
        try {
          companyEmployeeNumber = await settings.generateNextNumber();
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        // Manual mode - validate provided number
        if (!req.body.companyEmployeeNumber) {
          return res.status(400).json({
            success: false,
            error: "Employee number is required",
          });
        }

        // Check if number already exists
        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            error: "Employee number already exists",
          });
        }

        companyEmployeeNumber = req.body.companyEmployeeNumber;
      }

      // Create employee
      const employeeData = {
        ...req.body,
        company: company._id,
        companyEmployeeNumber,
        status: "Active",
        createdBy: req.user._id,
      };

      const employee = new Employee(employeeData);
      await employee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      return res.json({
        success: true,
        message: "Employee added successfully",
        employee: {
          id: employee._id,
          name: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.companyEmployeeNumber,
        },
      });
    } catch (error) {
      console.error("Error adding employee:", error);
      return res.status(500).json({
        success: false,
        error: "Failed to add employee",
        details: error.message,
      });
    }
  }
);

// Helper function to generate employee number
async function generateEmployeeNumber(company, settings) {
  // Get the last employee number for this company
  const lastEmployee = await Employee.findOne({ company: company._id }).sort({
    companyEmployeeNumber: -1,
  });

  let nextNumber = "0001";
  if (lastEmployee && lastEmployee.companyEmployeeNumber) {
    const currentNumber = parseInt(
      lastEmployee.companyEmployeeNumber.replace(/\D/g, "")
    );
    nextNumber = (currentNumber + 1).toString().padStart(4, "0");
  }

  return nextNumber;
}

function incrementEmployeeNumber(currentNumber) {
  const numericPart = parseInt(currentNumber.replace(/^0+/, "") || "0", 10);
  return (numericPart + 1).toString().padStart(4, "0");
}

// Download Excel Template Route
router.get(
  "/:companyCode/employeeManagement/download-template",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Check user permissions
      if (!req.user.companies.includes(company._id)) {
        req.flash(
          "error",
          "You don't have permission to access this company's data"
        );
        return res.redirect("/clients");
      }

      const generator = new EmployeeTemplateGenerator();
      const workbook = await generator.generateTemplate();

      // Set security headers
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader(
        "Cache-Control",
        "no-store, no-cache, must-revalidate, private"
      );

      // Set download headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=employee-import-template-${companyCode}-${Date.now()}.xlsx`
      );

      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error("Error generating template:", error);
      req.flash("error", "Failed to generate template");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Configure multer for file upload
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type. Only Excel files are allowed."));
    }
  },
});

// Add this new route to handle the essentials updates
router.post(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("=== Processing Essentials Update ===");
      const { companyCode } = req.params;
      const { employees } = req.body;

      console.log("Company Code:", companyCode);
      console.log("Received employees data:", employees);

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          error: "Company not found",
        });
      }

      console.log("Found company:", company._id);

      // Get default pay frequency for the company
      const defaultPayFrequency = await PayFrequency.findOne({
        company: company._id,
      });
      if (!defaultPayFrequency) {
        return res.status(400).json({
          success: false,
          error: "Company pay frequency not configured",
        });
      }

      // Process each employee
      const results = {
        success: [],
        errors: [],
      };

      for (const employeeData of employees) {
        try {
          console.log("Processing employee:", employeeData);

          // Validate required fields
          const requiredFields = ["firstName", "lastName", "email"];
          const missingFields = requiredFields.filter(
            (field) => !employeeData[field]
          );
          if (missingFields.length > 0) {
            throw new Error(
              `Missing required fields: ${missingFields.join(", ")}`
            );
          }

          // Format dates
          if (employeeData.dob) {
            employeeData.dob = moment
              .utc(employeeData.dob)
              .startOf("day")
              .toDate();
          }
          if (employeeData.doa) {
            employeeData.doa = moment
              .utc(employeeData.doa)
              .startOf("day")
              .toDate();
          }

          // Generate global employee ID if not exists
          const globalEmployeeId =
            employeeData.globalEmployeeId ||
            `${company.companyCode}-${
              employeeData.employeeNumber ||
              (await generateEmployeeNumber(company))
            }`;

          const employeeToSave = {
            ...employeeData,
            company: company._id,
            companyEmployeeNumber:
              employeeData.employeeNumber ||
              (await generateEmployeeNumber(company)),
            globalEmployeeId,
            firstPayrollPeriodEndDate:
              defaultPayFrequency.firstPayrollPeriodEndDate,
            lastDayOfPeriod: defaultPayFrequency.lastDayOfPeriod,
          };

          // Remove empty _id field if it exists
          if (employeeToSave._id === "") {
            delete employeeToSave._id;
          }

          console.log("Saving employee data:", employeeToSave);

          if (employeeData._id && employeeData._id !== "") {
            // Update existing employee
            const employee = await Employee.findByIdAndUpdate(
              employeeData._id,
              employeeToSave,
              { new: true, runValidators: true }
            );

            if (employee) {
              console.log("Updated employee:", employee._id);
              results.success.push({
                employeeNumber: employee.companyEmployeeNumber,
                action: "updated",
              });
            }
          } else {
            // Create new employee
            const employee = new Employee(employeeToSave);
            await employee.save();
            console.log("Created new employee:", employee._id);
            results.success.push({
              employeeNumber: employee.companyEmployeeNumber,
              action: "created",
            });
          }
        } catch (error) {
          console.error("Error processing employee:", error);
          results.errors.push({
            employeeNumber: employeeData.employeeNumber,
            error: error.message,
          });
        }
      }

      console.log("Processing complete. Results:", results);

      res.json({
        success: results.errors.length === 0,
        message: `Successfully processed ${results.success.length} employees`,
        errors: results.errors,
        details: results,
      });
    } catch (error) {
      console.error("Error saving essentials:", error);
      res.status(500).json({
        success: false,
        error: "Failed to save changes",
        details: error.message,
      });
    }
  }
);

// Helper function to generate employee number
async function generateEmployeeNumber(company) {
  // Get the last employee number for this company
  const lastEmployee = await Employee.findOne({ company: company._id }).sort({
    companyEmployeeNumber: -1,
  });

  let nextNumber = "0001";
  if (lastEmployee && lastEmployee.companyEmployeeNumber) {
    const currentNumber = parseInt(
      lastEmployee.companyEmployeeNumber.replace(/\D/g, "")
    );
    nextNumber = (currentNumber + 1).toString().padStart(4, "0");
  }

  return nextNumber;
}

// Add this route to handle payment & banking info updates
router.post(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      console.log("=== Processing Payment & Banking Info Update ===");
      const { companyCode } = req.params;
      const { employees } = req.body;

      console.log("Company Code:", companyCode);
      console.log("Received employees data:", employees);

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          error: "Company not found",
        });
      }

      console.log("Found company:", company._id);

      // Process each employee
      const results = {
        success: [],
        errors: [],
      };

      for (const employeeData of employees) {
        try {
          console.log("Processing employee:", employeeData);

          // Validate required fields for bank accounts if payment method is EFT
          if (employeeData.paymentMethod === "EFT") {
            const requiredFields = [
              "bank",
              "accountNumber",
              "branchCode",
              "accountType",
              "holderRelationship",
            ];
            const missingFields = requiredFields.filter(
              (field) => !employeeData[field]
            );

            if (missingFields.length > 0) {
              throw new Error(
                `Missing required banking fields: ${missingFields.join(", ")}`
              );
            }
          }

          if (employeeData._id) {
            // Update existing employee
            const employee = await Employee.findByIdAndUpdate(
              employeeData._id,
              {
                paymentMethod: employeeData.paymentMethod,
                bank: employeeData.bank,
                accountNumber: employeeData.accountNumber,
                branchCode: employeeData.branchCode,
                accountType: employeeData.accountType,
                holderRelationship: employeeData.holderRelationship,
                holderName: employeeData.holderName,
                updatedAt: new Date(),
              },
              { new: true, runValidators: true }
            );
            if (employee) {
              results.success.push({
                employeeNumber: employee.companyEmployeeNumber,
                action: "updated",
              });
            }
          } else {
            throw new Error("Employee ID not provided");
          }
        } catch (error) {
          console.error("Error processing employee:", error);
          results.errors.push({
            employeeNumber: employeeData.employeeNumber || "Unknown",
            error: error.message,
          });
        }
      }

      console.log("Processing complete. Results:", results);

      res.json({
        success: results.errors.length === 0,
        message: `Successfully processed ${results.success.length} employees`,
        errors: results.errors,
        details: results,
      });
    } catch (error) {
      console.error("Error saving payment & banking info:", error);
      res.status(500).json({
        success: false,
        error: "Failed to save changes",
        details: error.message,
      });
    }
  }
);

// POST route to handle employee updates
// router.post(
//   "/:companyCode/employeeProfile/:employeeId/edit",
//   ensureAuthenticated,
//   async (req, res) => {
//     try {
//       const { companyCode, employeeId } = req.params;
//       const employeeData = req.body;

//       // Validate company and employee
//       const company = await Company.findOne({ companyCode });
//       if (!company) {
//         return res.status(404).json({
//           success: false,
//           message: "Company not found",
//         });
//       }

//       // Fetch the PayFrequency document if it's being updated
//       if (employeeData.payFrequency) {
//         const payFrequencyDoc = await PayFrequency.findById(
//           employeeData.payFrequency
//         );
//         if (!payFrequencyDoc) {
//           return res.status(400).json({
//             success: false,
//             message: "Invalid pay frequency selected",
//           });
//         }
//       }

//       // Process dates
//       if (employeeData.dob) {
//         employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
//       }
//       if (employeeData.doa) {
//         employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
//         employeeData.endOfMonthDate = moment.utc(employeeData.doa)
//           .endOf("month")
//           .toDate();
//       }

//       // Update employee data
//       const updatedEmployee = await Employee.findByIdAndUpdate(
//         employeeId,
//         { $set: employeeData },
//         { new: true, runValidators: true }
//       );

//       if (!updatedEmployee) {
//         return res.status(404).json({
//           success: false,
//           message: "Employee not found",
//         });
//       }

//       // Send success response
//       res.json({
//         success: true,
//         message: "Employee updated successfully",
//         data: updatedEmployee,
//         redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
//       });
//     } catch (error) {
//       console.error("Error updating employee:", error);
//       res.status(500).json({
//         success: false,
//         message: error.message || "Error updating employee",
//       });
//     }
//   }
// );

// GET route for classifications
router.get(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      res.render("classifications", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering classifications form:", error);
      req.flash("error", "Failed to load classifications form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for classifications
router.post(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        workingHours,
        isDirector,
        typeOfDirector,
        isContractor,
        isUifExempt,
        uifExemptReason,
      } = req.body;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update employee
      const employee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        {
          $set: {
            workingHours,
            isDirector: !!isDirector,
            typeOfDirector: isDirector ? typeOfDirector : null,
            isContractor: !!isContractor,
            isUifExempt: !!isUifExempt,
            uifExemptReason: isUifExempt ? uifExemptReason : null,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      req.flash("success", "Classifications updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating classifications:", error);
      req.flash("error", "Failed to update classifications");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/classifications`
      );
    }
  }
);

// RFI Configuration Constants
const RFI_VALIDATION = {
  MIN_PERCENTAGE: 0,
  MAX_PERCENTAGE: 100,
  MIN_COMPONENTS: 1,
  REQUIRED_COMPONENTS: ["basicSalary"], // Components that must be included
};

// RFI Validation Middleware
const validateRFIConfig = (req, res, next) => {
  const { rfiOption, selectedIncomes, percentageValues } = req.body;
  const errors = [];

  try {
    // Validate RFI Option
    if (!["selectedIncome", "percentagePerIncome"].includes(rfiOption)) {
      errors.push("Invalid RFI calculation method");
    }

    // Validate Selected Incomes
    if (rfiOption === "selectedIncome") {
      if (
        !selectedIncomes ||
        !Array.isArray(selectedIncomes) ||
        selectedIncomes.length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be selected`
        );
      }

      // Check for required components
      const hasRequiredComponents = RFI_VALIDATION.REQUIRED_COMPONENTS.every(
        (comp) => selectedIncomes.includes(comp)
      );
      if (!hasRequiredComponents) {
        errors.push("Basic salary must be included in RFI calculation");
      }
    }

    // Validate Percentage Values
    if (rfiOption === "percentagePerIncome") {
      if (
        !percentageValues ||
        Object.keys(percentageValues).length < RFI_VALIDATION.MIN_COMPONENTS
      ) {
        errors.push(
          `At least ${RFI_VALIDATION.MIN_COMPONENTS} income component must be configured`
        );
      }

      // Validate percentage ranges
      Object.entries(percentageValues).forEach(([component, percentage]) => {
        const value = parseFloat(percentage);
        if (
          isNaN(value) ||
          value < RFI_VALIDATION.MIN_PERCENTAGE ||
          value > RFI_VALIDATION.MAX_PERCENTAGE
        ) {
          errors.push(
            `Invalid percentage value for ${component}. Must be between ${RFI_VALIDATION.MIN_PERCENTAGE} and ${RFI_VALIDATION.MAX_PERCENTAGE}`
          );
        }
      });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("RFI Validation Error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating RFI configuration",
    });
  }
};

// GET route for RFI configuration
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId)
        .populate("rfiConfig.selectedIncomes.componentId")
        .populate("rfiConfig.percentageIncomes.componentId");

      // Get the employee's payroll record with basic salary
      const payroll = await Payroll.findOne({ employee: employeeId });
      const assignedComponents = [];

      // Add basic salary if it exists in payroll
      if (payroll?.basicSalary) {
        assignedComponents.push({
          id: "basicSalary",
          name: "Basic Salary",
          amount: payroll.basicSalary,
          type: "income",
          isSelected:
            employee.rfiConfig?.selectedIncomes?.some(
              (inc) => inc.name === "Basic Salary"
            ) || false,
        });
      }

      // Add other components from payroll if they exist and have values
      if (payroll) {
        // Commission
        if (payroll.commission && payroll.commission.amount) {
          assignedComponents.push({
            id: "commission",
            name: "Commission",
            amount: payroll.commission.amount,
            type: "income",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Commission"
              ) || false,
          });
        }

        // Travel Allowance
        if (payroll.travelAllowance && payroll.travelAllowance.amount) {
          assignedComponents.push({
            id: "travelAllowance",
            name: "Travel Allowance",
            amount: payroll.travelAllowance.amount,
            type: "allowance",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Travel Allowance"
              ) || false,
          });
        }

        // Add other allowances if they exist
        if (payroll.allowances && payroll.allowances.length > 0) {
          payroll.allowances.forEach((allowance) => {
            if (allowance.amount) {
              assignedComponents.push({
                id: allowance._id.toString(),
                name: allowance.name,
                amount: allowance.amount,
                type: "allowance",
                isSelected:
                  employee.rfiConfig?.selectedIncomes?.some(
                    (inc) => inc.name === allowance.name
                  ) || false,
              });
            }
          });
        }

        // Add any other relevant components
        // ... add other components as needed
      }

      // Sort components by type and name
      assignedComponents.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === "income" ? -1 : 1;
      });

      res.render("defineRFI", {
        company,
        employee,
        payComponents: assignedComponents, // Pass the assigned components
        rfiConfig: employee.rfiConfig || {
          option: "",
          selectedIncomes: [],
          percentageIncomes: {},
        },
        validation: RFI_VALIDATION,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI configuration form:", error);
      req.flash(
        "error",
        "An error occurred while loading the RFI configuration"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for RFI configuration
router.post(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { option, selectedIncomes, percentageValues } = req.body;

      // Get company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Validate required fields
      if (!option) {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: "RFI calculation method is required",
        });
      }

      // Validate based on selected option
      if (option === "selectedIncome") {
        if (
          !selectedIncomes ||
          !Array.isArray(selectedIncomes) ||
          selectedIncomes.length === 0
        ) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: "At least one income component must be selected",
          });
        }
      } else if (option === "percentagePerIncome") {
        if (!percentageValues || Object.keys(percentageValues).length === 0) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message:
              "Percentage values are required for at least one component",
          });
        }

        // Validate percentage values
        for (const [component, percentage] of Object.entries(
          percentageValues
        )) {
          const value = parseFloat(percentage);
          if (isNaN(value) || value < 0 || value > 100) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Invalid percentage value for ${component}. Must be between 0 and 100`,
            });
          }
        }
      }

      // Update employee RFI configuration
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        {
          $set: {
            "rfiConfig.option": option,
            "rfiConfig.selectedIncomes":
              option === "selectedIncome"
                ? selectedIncomes.map((name) => ({
                    name,
                    includeInCalculation: true,
                  }))
                : [],
            "rfiConfig.percentageIncomes":
              option === "percentagePerIncome"
                ? Object.entries(percentageValues).map(
                    ([name, percentage]) => ({
                      name,
                      percentage: parseFloat(percentage),
                    })
                  )
                : [],
          },
        },
        { new: true, session }
      );

      if (!updatedEmployee) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Add audit log with company ID
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id, // Add company ID here
            action: "UPDATE_RFI_CONFIG",
            entity: "Employee",
            entityId: employeeId,
            details: {
              option,
              selectedIncomes:
                option === "selectedIncome" ? selectedIncomes : [],
              percentageValues:
                option === "percentagePerIncome" ? percentageValues : {},
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "RFI configuration updated successfully",
        data: updatedEmployee.rfiConfig,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating RFI configuration:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update RFI configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

// Add these validation rules at the top of the file
const FUND_VALIDATION = {
  FIXED_AMOUNT: {
    MIN: 0,
    MAX: 1000000000, // 1 billion
    DECIMALS: 2,
  },
  PERCENTAGE: {
    MIN: 0,
    MAX: 100,
    DECIMALS: 2,
  },
  CATEGORY_FACTOR: {
    MIN: 0.01,
    MAX: 10,
    DECIMALS: 2,
  },
};

// Add validation middleware
const validateFundInput = (req, res, next) => {
  const {
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
  } = req.body;

  const errors = [];

  try {
    // Validate contribution method
    if (!["fixedAmount", "percentageRFI"].includes(contributionCalculation)) {
      errors.push("Invalid contribution calculation method");
    }

    // Validate fixed amounts if that method is selected
    if (contributionCalculation === "fixedAmount") {
      const employeeAmount = parseFloat(fixedContributionEmployee);
      const employerAmount = parseFloat(fixedContributionEmployer);

      if (
        isNaN(employeeAmount) ||
        employeeAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employeeAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employee fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }

      if (
        isNaN(employerAmount) ||
        employerAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employerAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employer fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }
    }

    // Validate RFI percentages if that method is selected
    if (contributionCalculation === "percentageRFI") {
      const employeePercentage = parseFloat(rfiEmployee);
      const employerPercentage = parseFloat(rfiEmployer);

      if (
        isNaN(employeePercentage) ||
        employeePercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employeePercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employee RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }

      if (
        isNaN(employerPercentage) ||
        employerPercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employerPercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employer RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }
    }

    // Validate category factor
    if (categoryFactor) {
      const factor = parseFloat(categoryFactor);
      if (
        isNaN(factor) ||
        factor < FUND_VALIDATION.CATEGORY_FACTOR.MIN ||
        factor > FUND_VALIDATION.CATEGORY_FACTOR.MAX
      ) {
        errors.push(
          `Category factor must be between ${FUND_VALIDATION.CATEGORY_FACTOR.MIN} and ${FUND_VALIDATION.CATEGORY_FACTOR.MAX}`
        );
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("Fund validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating fund configuration",
    });
  }
};

// Update the POST route for pension fund
router.post(
  "/:companyCode/employeeProfile/:employeeId/pension-fund",
  ensureAuthenticated,
  csrfProtection,
  validateFundInput,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const fundData = req.body;
      const company = await Company.findOne({ companyCode });

      // Format numbers to fixed decimals
      if (fundData.contributionCalculation === "fixedAmount") {
        fundData.fixedContributionEmployee = parseFloat(
          fundData.fixedContributionEmployee
        ).toFixed(FUND_VALIDATION.FIXED_AMOUNT.DECIMALS);
        fundData.fixedContributionEmployer = parseFloat(
          fundData.fixedContributionEmployer
        ).toFixed(FUND_VALIDATION.FIXED_AMOUNT.DECIMALS);
      } else {
        fundData.rfiEmployee = parseFloat(fundData.rfiEmployee).toFixed(
          FUND_VALIDATION.PERCENTAGE.DECIMALS
        );
        fundData.rfiEmployer = parseFloat(fundData.rfiEmployer).toFixed(
          FUND_VALIDATION.PERCENTAGE.DECIMALS
        );
      }

      if (fundData.categoryFactor) {
        fundData.categoryFactor = parseFloat(fundData.categoryFactor).toFixed(
          FUND_VALIDATION.CATEGORY_FACTOR.DECIMALS
        );
      }

      // Update or create payroll record
      const payroll = await Payroll.findOneAndUpdate(
        { employee: employeeId },
        {
          $set: {
            pensionFund: fundData,
            updatedAt: new Date(),
          },
        },
        {
          new: true,
          upsert: true,
          session,
        }
      );

      // Add audit log
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id,
            action: "UPDATE_PENSION_FUND",
            entity: "Employee",
            entityId: employeeId,
            details: {
              changes: fundData,
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "Pension fund configuration saved successfully",
        data: payroll.pensionFund,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating pension fund:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update pension fund configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

// Inside the GET route for defineRFI
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      // Get current period and payroll data
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: currentPeriod?.endDate,
      });

      // Get pay components with current values
      const payComponents = [
        {
          name: "Basic Salary",
          amount:
            currentPeriod?.calculations?.basicSalary ||
            payroll?.basicSalary ||
            0,
          type: "earnings",
        },
        // Add other components as needed
      ];

      res.render("defineRFI", {
        company,
        employee,
        payComponents,
        currentPeriod,
        payroll,
        rfiConfig: employee.rfiConfig || {},
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI form:", error);
      req.flash("error", "Failed to load RFI form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// Add this near your other route handlers in employeeManagement.js

// RFI Preview Route
router.get(
  "/:companyCode/api/rfi/preview/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    console.log("\n=== RFI Preview Route ===");
    console.log("Params:", req.params);

    try {
      const { employeeId, companyCode } = req.params;

      // First find the company by code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Then find employee using company's ObjectId
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id, // Use company's ObjectId here
      });

      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Get payroll data
      const payroll = await Payroll.findOne({ employee: employeeId });
      console.log("Payroll found:", !!payroll);

      // Check RFI configuration
      if (!employee.rfiConfig || !employee.rfiConfig.option) {
        console.log("No RFI configuration found");
        return res.status(400).json({
          success: false,
          message: "RFI configuration not found. Please configure RFI first.",
        });
      }

      // Calculate RFI
      console.log("Calculating RFI...");
      const calculation = await RFICalculationService.calculateRFI(employeeId);
      console.log("RFI Calculation result:", calculation);

      res.json({
        success: true,
        data: calculation,
      });
    } catch (error) {
      console.error("RFI Preview Error:", error);
      console.error("Stack:", error.stack);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to generate RFI preview",
      });
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/hours-input",
  ensureAuthenticated,
  async (req, res) => {
    console.log("\n=== Hours Input Route Handler ===");
    try {
      const { companyCode, employeeId } = req.params;
      console.log("Route params:", { companyCode, employeeId });

      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      console.log("Found company:", !!company);
      console.log("Found employee:", !!employee);

      if (!company || !employee) {
        console.log("Company or employee not found");
        req.flash("error", "Company or employee not found");
        return res.redirect("/clients");
      }

      // Get current date
      const today = new Date();
      console.log("Current date:", today);

      // Calculate period dates
      const periodStartDate = moment
        .tz(today, DEFAULT_TIMEZONE)
        .startOf("month")
        .toDate();
      const periodEndDate = moment
        .tz(today, DEFAULT_TIMEZONE)
        .endOf("month")
        .toDate();

      console.log("Period dates:", {
        start: periodStartDate,
        end: periodEndDate,
      });

      // Create period object
      const period = {
        startDate: periodStartDate,
        endDate: periodEndDate,
        startDateFormatted: moment
          .tz(periodStartDate, DEFAULT_TIMEZONE)
          .format("DD MMM YYYY"),
        endDateFormatted: moment
          .tz(periodEndDate, DEFAULT_TIMEZONE)
          .format("DD MMM YYYY"),
      };

      console.log("Period object:", period);

      // Get worked hours
      const workedHours = await WorkedHours.findOne({
        employee: employeeId,
        "period.startDate": periodStartDate,
        "period.endDate": periodEndDate,
      });

      console.log("Found worked hours:", !!workedHours);

      const templateData = {
        company,
        employee,
        period,
        workedHours,
        formatDate: (date) =>
          moment.tz(date, DEFAULT_TIMEZONE).format("DD MMM YYYY"),
        getDaysInPeriod: (startDate, endDate) => {
          const days = [];
          let currentDate = moment.tz(startDate, DEFAULT_TIMEZONE);
          while (currentDate <= moment.tz(endDate, DEFAULT_TIMEZONE)) {
            days.push(currentDate.toDate());
            currentDate.add(1, "days");
          }
          return days;
        },
        getDayType: (date) => {
          const day = moment.tz(date, DEFAULT_TIMEZONE).day();
          if (day === 0) return "sunday";
          return "normal";
        },
        getHours: (workedHours, date, type) => {
          if (!workedHours?.hours) return "";
          const dayRecord = workedHours.hours.find(
            (h) =>
              moment.tz(h.date, DEFAULT_TIMEZONE).format("YYYY-MM-DD") ===
              moment.tz(date, DEFAULT_TIMEZONE).format("YYYY-MM-DD")
          );
          return dayRecord ? dayRecord[type] || "" : "";
        },
        isHoliday: (date) => false,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      };

      console.log("Template data prepared:", {
        hasCompany: !!templateData.company,
        hasEmployee: !!templateData.employee,
        hasPeriod: !!templateData.period,
        hasWorkedHours: !!templateData.workedHours,
      });

      res.render("editHoursInput", templateData);
    } catch (error) {
      console.error("Error in hours input route:", error);
      req.flash("error", "Failed to load hours input form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// In your POST route for creating new employee
router.post("/:companyCode/employeeManagement/employee", async (req, res) => {
  try {
    const { companyCode } = req.params;
    console.log("\n=== Creating New Employee ===");

    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    // Get pay frequency
    const payFrequency = await PayFrequency.findById(req.body.payFrequency);
    if (!payFrequency) {
      throw new Error("Pay frequency not found");
    }

    console.log("Pay Frequency:", {
      id: payFrequency._id,
      frequency: payFrequency.frequency,
      lastDayOfMonth: payFrequency.lastDayOfMonth,
      firstPayrollPeriodEndDate: payFrequency.firstPayrollPeriodEndDate,
    });

    // Create employee data object with all required fields
    const employeeData = {
      ...req.body,
      company: company._id,
      payFrequency: payFrequency._id,
      doa: moment.utc(req.body.doa).startOf("day").toDate(),
      status: "Active",
      basicSalary: 0, // Set default basic salary to 0
    };

    // Create and save the new employee
    const newEmployee = new Employee(employeeData);
    await newEmployee.save();

    // Populate employee with pay frequency
    const populatedEmployee = await Employee.findById(newEmployee._id).populate(
      "payFrequency"
    );

    console.log("Creating initial period for:", {
      employeeId: populatedEmployee._id,
      doa: populatedEmployee.doa,
      basicSalary: populatedEmployee.basicSalary,
      payFrequency: {
        id: populatedEmployee.payFrequency._id,
        frequency: populatedEmployee.payFrequency.frequency,
        lastDayOfMonth: populatedEmployee.payFrequency.lastDayOfMonth,
        firstPayrollPeriodEndDate:
          populatedEmployee.payFrequency.firstPayrollPeriodEndDate,
      },
    });

    // Create initial period with basic salary
    const initialPeriod = await PayrollPeriod.createInitialPeriod(
      populatedEmployee
    );

    // Set initial period calculations
    initialPeriod.calculations = {
      basicSalary: {
        full: 0,
        prorated: 0,
      },
      proRataDetails: {
        isProrated: false,
        percentage: 100,
      },
      deductions: {
        statutory: {
          paye: 0,
          uif: 0,
          sdl: 0,
        },
        courtOrders: {
          maintenanceOrder: 0,
        },
        total: 0,
      },
      grossIncome: 0,
      totalDeductions: 0,
      netPay: 0,
    };

    await initialPeriod.save();

    console.log("Initial period created:", {
      periodId: initialPeriod._id,
      startDate: initialPeriod.startDate,
      endDate: initialPeriod.endDate,
      frequency: initialPeriod.frequency,
      calculations: initialPeriod.calculations,
    });

    res.status(201).json({
      success: true,
      message: "Employee created successfully",
      employee: {
        id: newEmployee._id,
        name: `${newEmployee.firstName} ${newEmployee.lastName}`,
      },
    });
  } catch (error) {
    console.error("Error creating employee:", error);
    res.status(400).json({ error: error.message });
  }
});

// In the GET route for employee profile

router.get(
  "/clients/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      // Get current period and payroll data
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: currentPeriod?.endDate,
      });

      // Get pay components with current values
      const payComponents = [
        {
          name: "Basic Salary",
          amount:
            currentPeriod?.calculations?.basicSalary ||
            payroll?.basicSalary ||
            0,
          type: "earnings",
        },
        // Add other components as needed
      ];

      res.render("defineRFI", {
        company,
        employee,
        payComponents,
        currentPeriod,
        payroll,
        rfiConfig: employee.rfiConfig || {},
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI form:", error);
      req.flash("error", "Failed to load RFI form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

module.exports = router;
