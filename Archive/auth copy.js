const express = require("express");
const router = express.Router();
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const passport = require("passport");
const User = require("../models/user");
const nodemailer = require("nodemailer");
const { check, validationResult } = require("express-validator");
const saltRounds = 12;
const successMessages = ["Login successful"];
const errorMessages = ["Invalid email", "Incorrect password"];
const Company = require("../models/company");
const util = require("util");
const ejs = require("ejs");
const path = require("path");
const EmployerDetails = require("../models/employerDetails");
const Role = require("../models/role"); // Add this at the top of the file
const Employee = require("../models/Employee");

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: "<EMAIL>", // Your Gmail email address
    pass: "gkjk cyrp rfxt fuap", // Your Gmail password or an "App Password" if you have two-factor authentication enabled
  },
});

// Promisify the sendMail function
const sendMailAsync = util.promisify(transporter.sendMail).bind(transporter);

// Login route
router.get("/login", (req, res) => {
  res.render("login", {
    errorMessages: req.flash("error"),
    successMessages: req.flash("success"),
  });
});

router.post("/login", async (req, res, next) => {
  try {
    const { email, password } = req.body;
    console.log(`Attempting login for email: ${email}`);

    const user = await User.findOne({ email }).populate("role");

    if (user) {
      const isMatch = await bcrypt.compare(password, user.password);
      if (isMatch) {
        req.login(user, async (err) => {
          if (err) {
            console.error("Login error:", err);
            req.flash("error", "An error occurred during login");
            return res.redirect("/login");
          }
          console.log("Login successful");

          if (user.role.name === "owner") {
            const company = await Company.findOne({ owner: user._id });
            if (company) {
              return res.redirect(`/clients/${company.companyCode}/dashboard`);
            } else {
              return res.redirect("/companies/select");
            }
          } else if (user.role.name === "employee") {
            const employee = await Employee.findOne({ user: user._id });
            if (employee) {
              return res.redirect(`/employee-portal/${employee._id}`);
            } else {
              req.flash("error", "Employee profile not found");
              return res.redirect("/login");
            }
          } else {
            return res.redirect("/dashboard");
          }
        });
      } else {
        console.log("Password incorrect");
        req.flash("error", "Invalid email or password");
        return res.redirect("/login");
      }
    } else {
      console.log("No user found with this email");
      req.flash("error", "Invalid email or password");
      return res.redirect("/login");
    }
  } catch (error) {
    console.error("Login error:", error);
    req.flash("error", "An error occurred during login");
    res.redirect("/login");
  }
});

// Registration route
router.get("/register", (req, res) => {
  res.render("register", {
    errorMessages: req.flash("error"),
    successMessages: req.flash("success"),
  });
});

router.post("/register", async (req, res) => {
  try {
    // Check if the user already exists
    const existingUser = await User.findOne({ email: req.body.email });
    if (existingUser) {
      req.flash("error", "Email already registered.");
      return res.redirect("/register");
    }

    // Find or create the owner role
    let ownerRole = await Role.findOne({ name: "owner" });
    if (!ownerRole) {
      ownerRole = await Role.create({ name: "owner" });
    }

    // Create a new user with the Owner role
    const newUser = new User({
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email.toLowerCase(),
      phone: req.body.phone,
      password: req.body.password,
      verificationToken: crypto.randomBytes(16).toString("hex"),
      isVerified: false,
      role: ownerRole._id, // Assign the ObjectId of the Owner role
    });

    // Generate a unique company code
    const companyCode = generateUniqueCompanyCode();

    // Create a new company
    const company = new Company({
      name: req.body.companyName,
      owner: newUser._id,
      companyCode: companyCode, // Add this line
    });

    await company.save();

    // Associate the company with the user
    newUser.companies.push(company._id);
    newUser.currentCompany = company._id;

    // Save the new user
    await newUser.save();

    // Construct the verification URL
    const verificationUrl = `${req.protocol}://${req.get("host")}/verify/${
      newUser.verificationToken
    }`;

    // Render the email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/register-email.ejs"),
      {
        firstName: newUser.firstName,
        verificationUrl,
      }
    );

    // Compose the email for email verification
    const mailOptions = {
      from: "<EMAIL>",
      to: newUser.email,
      subject: "Welcome to Panda - Email Verification",
      html: emailHtml,
      attachments: [
        {
          filename: "panda.png",
          path: path.join(__dirname, "../public/pandaw.png"),
          cid: "logo",
        },
      ],
    };

    // Send the verification email
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error("Email verification failed: " + error);
        req.flash(
          "error",
          "Registration successful, but failed to send verification email."
        );
        return res.redirect("/register");
      } else {
        console.log("Email verification sent: " + info.response);
      }
    });

    // Registration success
    req.flash("success", "Registration successful. Please verify your email.");
    res.redirect("/login");
  } catch (error) {
    console.error("Registration error:", error);
    req.flash("error", "Registration failed. Please try again.");
    res.redirect("/register");
  }
});

// Add this function to generate a unique company code
function generateUniqueCompanyCode() {
  // This is a simple example. You might want to make this more sophisticated
  // and ensure it's truly unique in your database.
  return "COM" + Math.random().toString(36).substr(2, 5).toUpperCase();
}

// Email verification route
router.get("/verify/:token", async (req, res) => {
  try {
    const token = req.params.token;

    // Find the user by verification token
    const user = await User.findOne({ verificationToken: token });

    if (!user) {
      return res.status(404).send("Verification token not found.");
    }

    // Update the user's isVerified field to true
    user.isVerified = true;
    user.verificationToken = undefined; // Remove the verification token

    // Save the updated user
    await user.save();

    req.flash("success", "Email verified. You can now log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Email verification error:", error);
    res.status(500).send("Internal Server Error");
  }
});
//forgot_password Route

router.get("/forgot_password", (req, res) => {
  res.render("forgot_password", { message: "", error: "", errors: [] });
});

router.post(
  "/forgot_password",
  [check("email").isEmail().withMessage("Invalid email format")],
  async (req, res) => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.render("forgot_password", {
        message: "",
        error: "",
        errors: errors.array(),
      });
    }

    const { email } = req.body;

    try {
      const user = await User.findOne({ email });

      if (!user) {
        return res.render("forgot_password", {
          message: "",
          error: "User not found",
          errors: [],
        });
      }

      const resetToken = await user.generateResetToken();

      const resetLink = `${req.protocol}://${req.get(
        "host"
      )}/reset_password?token=${resetToken}`;

      // Render the email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/reset_password_email.ejs"),
        {
          resetLink,
        }
      );

      const mailOptions = {
        from: "<EMAIL>",
        to: email,
        subject: "Password Reset Link",
        html: emailHtml,
      };

      await sendMailAsync(mailOptions);

      console.log("Reset email sent");
      res.render("forgot_password", {
        message: "An email has been sent to reset your password.",
        error: "",
        errors: [],
      });
    } catch (err) {
      console.error(err);
      res.render("forgot_password", {
        message: "",
        error: "Failed to send reset email",
        errors: [],
      });
    }
  }
);

router.get("/reset_password", async (req, res) => {
  try {
    const token = req.query.token; // Get the token from the query parameters

    // Find the user by reset token and ensure it's not expired
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      // If the token is invalid or has expired, redirect to an error message
      return res.status(400).send("Invalid or expired token");
    }

    // Render the reset password form with the token passed as a parameter
    res.render("reset_password", { token });
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
});

router.post("/reset_password", async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    // Check if passwords match
    if (password !== confirmPassword) {
      return res.render("reset_password", {
        token,
        error: "Passwords do not match",
      });
    }

    // Find the user by the reset password token
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).send("Invalid or expired token");
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user's password and clear reset token fields
    user.password = hashedPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    // Flash success message
    req.flash("success", "Your password has been successfully updated.");

    // Redirect to the login page
    res.redirect("/login");
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
});

// Route to handle the password reset form submission
router.post("/reset/:token", async (req, res) => {
  try {
    const token = req.params.token;
    const { password, confirmPassword } = req.body;

    // Check if passwords match
    if (password !== confirmPassword) {
      return res.render("reset_password", {
        token,
        error: "Passwords do not match",
      });
    }

    // Find the user by the reset password token
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });
    if (!user) {
      return res.render("error", { message: "Invalid or expired token" });
    }

    // Update the user's password and clear the reset password fields
    const hashedPassword = await bcrypt.hash(password, 10);
    user.password = hashedPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    // Redirect to the login page or any other relevant page
    res.redirect("/login");
  } catch (error) {
    console.error(error);
    res.status(500).render("error", { message: "Internal Server Error" });
  }
});

// Logout route
router.get("/logout", (req, res) => {
  req.logout((err) => {
    if (err) {
      console.error("Error during logout:", err);
      return res.redirect("/");
    }
    req.flash("success_msg", "You have been logged out");
    res.redirect("/login");
  });
});

router.get("/check-session", (req, res) => {
  console.log("Session:", req.session);
  console.log("User:", req.user);
  res.json({
    isAuthenticated: req.isAuthenticated(),
    user: req.user ? { id: req.user._id, email: req.user.email } : null,
  });
});

// Route to render the set password form
router.get("/set-password/:token", async (req, res) => {
  try {
    const user = await User.findOne({
      verificationToken: req.params.token,
      isVerified: false,
    });

    if (!user) {
      req.flash("error", "Invalid or expired verification token");
      return res.redirect("/login");
    }

    res.render("set-password", { token: req.params.token });
  } catch (error) {
    console.error("Error rendering set password form:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Route to handle password setting
router.post("/set-password", async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect(`/set-password/${token}`);
    }

    const user = await User.findOne({
      verificationToken: token,
      isVerified: false,
    });

    if (!user) {
      req.flash("error", "Invalid or expired verification token");
      return res.redirect("/login");
    }

    user.password = password;
    user.isVerified = true;
    user.verificationToken = undefined;
    await user.save();

    req.flash("success", "Password set successfully. You can now log in.");
    res.redirect("/login");
  } catch (error) {
    console.error("Error setting password:", error);
    res.status(500).send("Internal Server Error");
  }
});

<% if (payroll && ((payroll.bursariesAndScholarships.taxablePortion || 0) > 0 || (payroll.bursariesAndScholarships.exemptPortion || 0) > 0)) { %>
  <div class="section income">
    <div class="row tax">
      <a
        href="/regularInputs/<%= employee._id %>/edit/bursariesAndScholarships/<%= thisMonth.toISOString().split('T')[0] %>"
        id="bursariesAndScholarships"
      >
        Bursaries And Scholarships
      </a>

      <!-- Display the total taxable and exempt portions or adjust as needed -->
      <span id="bursariesAndScholarshipsAmount">
        Taxable Portion: R <%= (payroll.bursariesAndScholarships.taxablePortion || 0).toLocaleString(undefined, {
          minimumFractionDigits: 2, 
          maximumFractionDigits: 2 
        }) %>,
        Exempt Portion: R <%= (payroll.bursariesAndScholarships.exemptPortion || 0).toLocaleString(undefined, {
          minimumFractionDigits: 2, 
          maximumFractionDigits: 2 
        }) %>
      </span>
    </div>
  </div>
<% } %>


module.exports = router;

//____________________________________________________________
rsync -avz --exclude 'node_modules' --exclude '.git' \
-e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
. <EMAIL>:/home/<USER>/app

ssh -i "johannesburg-jay-mbp.pem" <EMAIL>
npm install
sudo systemctl restart myapp.service
sudo systemctl restart caddy


sudo systemctl daemon-reload
sudo systemctl enable myapp.service

sudo systemctl start myapp.service
sudo systemctl start caddy


sudo systemctl status myapp.service
sudo systemctl status caddy

cat /home/<USER>/app/public/auth.css

sudo journalctl -u myapp.service
sudo journalctl -fu myapp.service

sudo systemctl stop myapp.service
sudo systemctl stop caddy
sudo journalctl -u caddy -n 50 --no-pager


sudo chown -R ubuntu:ubuntu /home/<USER>/app && \
  sudo chmod -R 755 /home/<USER>/app && \
  sudo systemctl restart myapp.service && \
  sudo systemctl restart caddy

  ssh -i ~/Downloads/jaimonmbpcpt.pem <EMAIL> "
  rm -f /home/<USER>/app/routes/auth.js && \
  rm -f /home/<USER>/app/routes/employee-profile.js && \
  rm -f /home/<USER>/app/views/login.ejs && \
  rm -f /home/<USER>/app/views/register.ejs && \
  rm -f /home/<USER>/app/views/forgot_password.ejs && \
  rm -f /home/<USER>/app/views/employee-profile.ejs && \
  rm -f /home/<USER>/app/views/partials/_alerts.ejs && \
  rm -f /home/<USER>/app/public/auth.css && \
  rm -f /home/<USER>/app/public/css/alerts.css

ssh -i "jaimonmbpcpt.pem" <EMAIL> 

cd ~/x-pay  # First, ensure you're in your project root directory

# Create a single rsync command that preserves directory structure
rsync -avz --progress \
  --exclude 'node_modules' \
  --exclude '.git' \
  --relative \
  ./routes/auth.js \
  ./routes/employee-profile.js \
  ./views/login.ejs \
  ./views/register.ejs \
  ./views/forgot_password.ejs \
  ./views/employee-profile.ejs \
  ./views/partials/_alerts.ejs \
  ./public/auth.css \
  ./public/css/alerts.css \
  -e "ssh -i ~/Downloads/jaimonmbpcpt.pem" \
  <EMAIL>:/home/<USER>/app/

//____________________________________________________

rsync -avz --exclude 'node_modules' --exclude '.git' \
-e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
--include '.env' \
--include 'app.js' \
--exclude '*' \
. <EMAIL>:/home/<USER>/app


rsync -avzP --delete \
    -e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
    . <EMAIL>:/home/<USER>/app/
    
    rsync -avz --progress \
  --exclude 'node_modules' \
  --exclude '.git' \
  routes/employee-profile.js \
  views/employee-profile.ejs \
  models/user.js \
  -e "ssh -i ~/Downloads/jaimonmbpcpt.pem" \
  <EMAIL>:/home/<USER>/app/




    16-171-148-54
//_____________________________________________

rsync -avzP --delete \
    --exclude '.git' \
    --exclude 'node_modules' \
    --exclude '.env' \
    --exclude 'tmp' \
    --exclude '.DS_Store' \
    --chmod=D755,F644 \
    -e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
    . <EMAIL>:/home/<USER>/app/
//______________________________

rsync -avz --exclude 'node_modules' --exclude '.git' \
-e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
./ <EMAIL>:/home/<USER>/app/
//____________________________________________________________

rsync -avz --progress --delete --exclude 'node_modules' --exclude '.git' \
-e "ssh -i ~/Downloads/johannesburg-jay-mbp.pem" \
./ <EMAIL>:/home/<USER>/app/

//____________________________________________________________

sudo systemctl restart myapp.service

//___________________________________________


sudo systemctl stop myapp.service
//____________________________________________________________
# Navigate to your app directory
cd ~/app
//____________________________________________________________
# Install any new dependencies
npm install
//____________________________________________________________
# Restart the service
sudo systemctl restart myapp.service
//____________________________________________________________
# Check the status
sudo systemctl status myapp.service
//____________________________________________________________
# If you need to see the logs
sudo journalctl -u myapp.service -f
//____________________________________________________________



JWT_SECRET=xxx-xxx-xxx
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=xxxx xxxx xxxx xxx
# Database
MONGODB_URI=mongodb+srv://<EMAIL>:<EMAIL>/mydatabase?retryWrites=true&w=majority

# Session
SESSION_SECRET=xxxxxxxx

# Server
PORT=3002

# Other sensitive data
BCRYPT_SALT_ROUNDS=xx



Connected to MongoDB
GET /register
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
POST /register
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
Raw password received during registration: 12345678
Hashed password stored during registration: $2b$12$XU5nt6MszGHUMX7DdjeCDelDnFyCTytY8FcSRZFAJGjdrm4me4Lrq
Pre-save hook triggered for user: <EMAIL>
New user, setting username to email: <EMAIL>
Password modified, hashing password for user: <EMAIL>
Role name set for user: <EMAIL> Role: owner
User saved successfully: new ObjectId("6708389f476345ca6ff77dcd")
Stored hashed password for new user: $2b$12$ea5Pf7CUJjNWsICNOoaHJu.XLrdgIcdYqo/RJDjiUfVvXuu78dMOu
Verification URL: http://localhost:3002/verify-email/38fe9e9fa5d313ec2cfaf7095dfc4fc31a725b3c
Email HTML generated successfully
Attempting to send email to: <EMAIL>
Verification email sent successfully
GET /login
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
GET /login
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
GET /verify-email/38fe9e9fa5d313ec2cfaf7095dfc4fc31a725b3c
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
Received verification token: 38fe9e9fa5d313ec2cfaf7095dfc4fc31a725b3c
User found: {
  _id: new ObjectId("6708389f476345ca6ff77dcd"),
  firstName: 'Tsiki',
  lastName: 'Liki',
  email: '<EMAIL>',
  phone: '0784590329',
  password: '$2b$12$ea5Pf7CUJjNWsICNOoaHJu.XLrdgIcdYqo/RJDjiUfVvXuu78dMOu',
  isVerified: false,
  verificationToken: '38fe9e9fa5d313ec2cfaf7095dfc4fc31a725b3c',
  companies: [ new ObjectId("6708389f476345ca6ff77dce") ],
  role: new ObjectId("6705a547cbd35b1e55770688"),
  roleName: 'owner',
  createdAt: 2024-10-10T20:27:11.221Z,
  currentCompany: new ObjectId("6708389f476345ca6ff77dce"),
  defaultCompany: new ObjectId("6708389f476345ca6ff77dce"),
  username: '<EMAIL>',
  __v: 0
}
User before saving: {
  _id: new ObjectId("6708389f476345ca6ff77dcd"),
  firstName: 'Tsiki',
  lastName: 'Liki',
  email: '<EMAIL>',
  phone: '0784590329',
  password: '$2b$12$ea5Pf7CUJjNWsICNOoaHJu.XLrdgIcdYqo/RJDjiUfVvXuu78dMOu',
  isVerified: true,
  companies: [ new ObjectId("6708389f476345ca6ff77dce") ],
  role: new ObjectId("6705a547cbd35b1e55770688"),
  roleName: 'owner',
  createdAt: 2024-10-10T20:27:11.221Z,
  currentCompany: new ObjectId("6708389f476345ca6ff77dce"),
  defaultCompany: new ObjectId("6708389f476345ca6ff77dce"),
  username: '<EMAIL>',
  __v: 0
}
Pre-save hook triggered for user: <EMAIL>
User after saving: {
  _id: new ObjectId("6708389f476345ca6ff77dcd"),
  firstName: 'Tsiki',
  lastName: 'Liki',
  email: '<EMAIL>',
  phone: '0784590329',
  password: '$2b$12$ea5Pf7CUJjNWsICNOoaHJu.XLrdgIcdYqo/RJDjiUfVvXuu78dMOu',
  isVerified: true,
  companies: [ new ObjectId("6708389f476345ca6ff77dce") ],
  role: new ObjectId("6705a547cbd35b1e55770688"),
  roleName: 'owner',
  createdAt: 2024-10-10T20:27:11.221Z,
  currentCompany: new ObjectId("6708389f476345ca6ff77dce"),
  defaultCompany: new ObjectId("6708389f476345ca6ff77dce"),
  username: '<EMAIL>',
  __v: 0
}
GET /login
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
POST /login
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined
Login attempt for email: <EMAIL>
Raw password received: 12345678
Passport local strategy - Attempting login for email: <EMAIL>
User found: <EMAIL>
Verifying password for user: <EMAIL>
Stored hashed password: $2b$12$ea5Pf7CUJjNWsICNOoaHJu.XLrdgIcdYqo/RJDjiUfVvXuu78dMOu
Provided password: 12345678
Password match result: false
Incorrect password for user: <EMAIL>
Authentication failed: Incorrect email or password.
GET /login
Session ID: 9RIodWXkVyfk01Hmz7Uxk9xR-9qOItr5
Is Authenticated: false
User: undefined



}
Deprecation warning: value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.
Arguments: 
[0] _isAMomentObject: true, _isUTC: false, _useUTC: false, _l: undefined, _i: 2024/10/01, _f: undefined, _strict: undefined, _locale: [object Object]
Error
    at Function.createFromInputFallback (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:324:25)
    at configFromString (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:2613:19)
    at configFromInput (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:3056:13)
    at prepareConfig (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:3039:13)
    at createFromConfig (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:3006:44)
    at createLocalOrUTC (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:3100:16)
    at createLocal (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:3104:16)
    at hooks (/Users/<USER>/Documents/GitHub/x-pay/node_modules/moment/moment.js:16:29)
    at isNewEmployee (/Users/<USER>/Documents/GitHub/x-pay/routes/employeeManagement.js:1408:10)
    at /Users/<USER>/Documents/GitHub/x-pay/routes/employeeManagement.js:1375:26



    <!-- <script>
  window.addEventListener('error', function(event) {
      if (event.error instanceof TypeError && event.error.message.includes("Cannot read properties of null (reading 'contains')")) {
          console.error('Caught TypeError:', event.error);
          console.error('Error occurred in:', event.filename, 'at line:', event.lineno, 'column:', event.colno);
          console.error('Stack trace:', event.error.stack);
          event.preventDefault();
      }
  });
  </script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const monthsList = document.getElementById('monthsList');
  const companyCode = '<%= companyCode %>';
  const employeeId = '<%= employee._id %>';

  monthsList.addEventListener('click', function(event) {
    const monthItem = event.target.closest('.month-item');
    if (monthItem) {
      const selectedMonth = monthItem.dataset.month;
      window.location.href = `/clients/${companyCode}/employeeProfile/${employeeId}?selectedMonth=${selectedMonth}`;
    }
  });
});
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
      const originalQuerySelector = document.querySelector;
      document.querySelector = function() {
          const result = originalQuerySelector.apply(this, arguments);
          if (result === null) {
              console.warn('querySelector returned null for:', arguments[0]);
          }
          return result;
      }
  
      const originalGetElementById = document.getElementById;
      document.getElementById = function() {
          const result = originalGetElementById.apply(this, arguments);
          if (result === null) {
              console.warn('getElementById returned null for:', arguments[0]);
          }
          return result;
      }
  });
  </script>
<script>
  document.addEventListener('click', function(event) {
      console.log('Click event triggered');
      console.log('Target:', event.target);
      console.log('Current target:', event.currentTarget);
  });
  </script>
  <script>
    window.addEventListener('error', function(event) {
        if (event.error instanceof TypeError && event.error.message.includes("Cannot read properties of null (reading 'contains')")) {
            console.error('Caught TypeError:', event.error);
            console.error('Error occurred in:', event.filename, 'at line:', event.lineno, 'column:', event.colno);
            console.error('Stack trace:', event.error.stack);
            event.preventDefault();
        }
    });
    </script> -->





<!-- <script>
        window.addEventListener('load', function() {
          const formSubmitted = localStorage.getItem('formSubmitted');
          
          if (formSubmitted) {
            // Remove the formSubmitted flag
            localStorage.removeItem('formSubmitted');
            
            // Store a flag in sessionStorage to indicate that a refresh occurred after form submission
            sessionStorage.setItem('pageReloaded', 'true');
            
            // Show flash message after the page has reloaded
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(flashMessage => {
              flashMessage.style.display = 'block';
              flashMessage.style.opacity = '1';
              setTimeout(() => {
                flashMessage.style.opacity = '0';
                setTimeout(() => {
                  flashMessage.style.display = 'none';
                }, 900); // Delay to match fade-out duration
              }, 5000); // Time for the message to stay visible
            });
      
            // Ensure the page does not reload again
            sessionStorage.removeItem('pageReloaded');
          } else {
            // Check if the page was reloaded after form submission
            const pageReloaded = sessionStorage.getItem('pageReloaded');
            if (pageReloaded) {
              // Remove the pageReloaded flag
              sessionStorage.removeItem('pageReloaded');
            }
          }
        });
      
        function handleSubmit() {
          // Store a flag in localStorage to indicate that form submission occurred
          localStorage.setItem('formSubmitted', 'true');
          return true; // Proceed with the form submission
        }
      </script>



      <!-- <script>
    function downloadPayslip(employeeId) {
        window.location.href = `/payslip/download/${employeeId}/<%= moment(thisMonth).format('YYYY-MM-DD') %>`;
    }
    </script> --> 


    <!-- <script>
        document.addEventListener('DOMContentLoaded', function() {
          const cancelTerminationBtn = document.getElementById('cancelTermination');
          
          if (cancelTerminationBtn) {
            cancelTerminationBtn.addEventListener('click', function() {
              if (confirm('Are you sure you want to cancel this termination? This action cannot be undone.')) {
                fetch('/employeeProfile/<%= employee._id %>/cancel-termination', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                })
                .then(response => {
                  if (!response.ok) {
                    throw new Error('Network response was not ok');
                  }
                  return response.json();
                })
                .then(data => {
                  if (data.success) {
                    alert('Termination cancelled successfully');
                    location.reload(); // Reload the page to reflect the changes
                  } else {
                    alert('Failed to cancel termination: ' + data.message);
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  alert('An error occurred while cancelling the termination');
                });
              }
            });
          }
        });
        </script> -->

<!-- <script>
  document.addEventListener('DOMContentLoaded', function() {
    const updateTerminationBtn = document.getElementById('updateTermination');
    const updateTerminationModal = document.getElementById('updateTerminationModal');
    const closeBtn = updateTerminationModal.querySelector('.close');
    const updateTerminationForm = document.getElementById('updateTerminationForm');
    
    // Open modal
    updateTerminationBtn.addEventListener('click', function() {
      updateTerminationModal.style.display = 'block';
    });
  
    // Close modal
    closeBtn.addEventListener('click', function() {
      updateTerminationModal.style.display = 'none';
    });
  
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target == updateTerminationModal) {
        updateTerminationModal.style.display = 'none';
      }
    });
  
    // Form submission
    updateTerminationForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const newLastDayOfService = document.getElementById('newLastDayOfService').value;
      const newTerminationReason = document.getElementById('newTerminationReason').value;
  
      fetch('/employeeProfile/<%= employee._id %>/update-termination', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lastDayOfService: newLastDayOfService,
          terminationReason: newTerminationReason
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Termination details updated successfully');
          location.reload();
        } else {
          alert('Failed to update termination details: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating termination details');
      });
    });
  });
  </script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const endServiceForm = document.getElementById('endServiceForm');
    const lastDayOfServiceInput = document.getElementById('lastDayOfService');
    // Check if employee and employee.doa exist, and ensure it's properly formatted
    const doa = <%= employee && employee.doa ? `new Date("${formatDateForDisplay(employee.doa)}")` : 'null' %>;

    if (endServiceForm && lastDayOfServiceInput && doa) {
      // Set the minimum date for the input only if doa is not null
      lastDayOfServiceInput.min = doa.toISOString().split('T')[0];

      endServiceForm.addEventListener('submit', function(e) {
        const lastDayOfService = new Date(lastDayOfServiceInput.value);
        
        if (lastDayOfService < doa) {
          e.preventDefault();
          alert("Last day of service cannot be before the date of appointment (" + doa.toISOString().split('T')[0] + ").");
        }
      });
    } else {
      console.warn('Some required elements are missing or employee data is incomplete.');
    }
  });
</script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const deleteEmployeeBtn = document.getElementById('deleteEmployeeBtn');
        const employeeId = '<%= employee._id %>'; // Get the employee ID from the server-side
      
        console.log('Delete functionality initialized');
        console.log('Employee ID:', employeeId);
      
        deleteEmployeeBtn.addEventListener('click', function(event) {
          event.preventDefault();
          console.log('Delete button clicked');
          
          if (confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
            console.log('Sending DELETE request to:', `/employeeProfile/${employeeId}/delete`);
            
            fetch(`/employeeProfile/${employeeId}/delete`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
            })
            .then(response => {
              console.log('Response status:', response.status);
              return response.json();
            })
            .then(data => {
              console.log('Response data:', data);
              if (data.success) {
                alert('Employee deleted successfully');
                window.location.href = '/employeeManagement';
              } else {
                alert('Failed to delete employee: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Fetch error:', error);
              alert('An error occurred while deleting the employee');
            });
          }
        });
      });
    </script> -->


    <!-- <script src="/tab.js"></script>
    <script src="/script.js"></script> -->
    <!-- <script scr="/editInfo.js"></script> -->





    