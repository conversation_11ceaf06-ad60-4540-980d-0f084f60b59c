import { subMonths, endOfMonth, subWeeks, format, isBefore, isEqual, subDays,
addDays, endOfWeek, startOfWeek, setDate, getDaysInMonth, max, differenceInDays
} from 'date-fns'; import flatpickr from "flatpickr"; const PayrollDateState = {
currentDate: new Date(), frequency: 'monthly', lastDayOfPeriod: 'monthend',
interimDay: null, employeeDoa: null, // Change this to store the employee's date
of appointment setFrequency(freq) { this.frequency = freq; this.updateDates();
}, setLastDayOfPeriod(day) { this.lastDayOfPeriod = day; this.updateDates(); },
setInterimDay(day) { this.interimDay = day; this.updateDates(); },
setEmployeeDoa(date) { // Change this method this.employeeDoa = new Date(date);
this.updateDates(); }, updateDates() { const dates = this.calculateDates();
this.renderDates(dates); }, calculateDates() { const dates = []; let currentDate
= this.currentDate; const employeeDoa = this.employeeDoa ? new
Date(this.employeeDoa) : null; // Determine the start date (either DOA or 3
months ago, whichever is later) let startDate = subMonths(currentDate, 3); if
(employeeDoa && employeeDoa > startDate) { startDate = employeeDoa; } // Adjust
startDate to the beginning of its week startDate = startOfWeek(startDate, {
weekStartsOn: this.lastDayOfPeriod }); // Generate periods until we have one
future period let hasFuturePeriod = false; while (!hasFuturePeriod) { let
periodEndDate = endOfWeek(startDate, { weekStartsOn: this.lastDayOfPeriod });
const proRata = this.calculateProRata(startDate, periodEndDate); dates.push({
startDate: startDate, endDate: periodEndDate, proRata }); if (periodEndDate >
currentDate) { hasFuturePeriod = true; } startDate = addDays(periodEndDate, 1);
} return dates; }, calculateProRata(periodStartDate, periodEndDate) { if
(!this.employeeDoa) return 1; const startDate = max([new Date(this.employeeDoa),
periodStartDate]); const endDate = periodEndDate; const totalDays =
differenceInDays(endDate, periodStartDate) + 1; const workedDays =
differenceInDays(endDate, startDate) + 1; return workedDays / totalDays; },
renderDates(dates) { firstPayrollPeriodEndDateSelect.innerHTML = dates.map(({
endDate, proRata }) => `
<option value="${formatDateYYYYMMDD(endDate)}">
  ${formatDateDDMMYYYY(endDate)} (Pro-rata: ${(proRata * 100).toFixed(2)}%)
</option>
`).join(''); } }; // Event listeners frequencySelect.addEventListener('change',
(e) => PayrollDateState.setFrequency(e.target.value));
lastDayOfPeriodSelect.addEventListener('change', (e) =>
PayrollDateState.setLastDayOfPeriod(e.target.value));
biWeeklyInterimDaySelect.addEventListener('change', (e) =>
PayrollDateState.setInterimDay(e.target.value)); // Add this new event listener
for the employee's date of appointment
document.getElementById('employeeDoa').addEventListener('change', (e) => {
PayrollDateState.setEmployeeDoa(e.target.value); }); const datePickerConfig = {
mode: "single", dateFormat: "Y-m-d", minDate: "today", maxDate: new
Date().fp_incr(90), // 3 months from now disable: [ function(date) { // Disable
dates based on frequency and last day of period // Implementation depends on
your specific requirements } ], onChange: function(selectedDates, dateStr,
instance) { // Handle date selection console.log(selectedDates[0]); } };
flatpickr("#firstPayrollPeriodEndDate", datePickerConfig); // Initial setup
PayrollDateState.updateDates();
