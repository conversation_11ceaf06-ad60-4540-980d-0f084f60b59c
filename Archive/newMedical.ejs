<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Keep existing head content -->
    <title>
      Add Medical Aid - <%= employee.firstName %> <%= employee.lastName %>
    </title>
  </head>
  <body>
    <%- include('partials/header') %>
    <nav><%- include('partials/sidebar') %></nav>

    <main>
      <h1>
        Add Medical Aid - <%= employee.firstName %> <%= employee.lastName %>
        Fill Form for Date: <%= formattedDate %>
      </h1>

      <% if (messages.error) { %>
      <div class="alert alert-danger"><%= messages.error %></div>
      <% } %> <% if (messages.success) { %>
      <div class="alert alert-success"><%= messages.success %></div>
      <% } %>

      <!-- Updated form action to use forms route -->
      <form
        action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/forms/medical"
        method="POST"
        class="pay-component-form"
      >
        <!-- CSRF Token -->
        <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
        <input type="hidden" name="relevantDate" value="<%= relevantDate %>" />

        <label for="amount">Total amount per period:</label>
        <input
          type="number"
          id="amount"
          name="amount"
          placeholder="Enter Amount"
          required
        />
        <p>
          This is the total of the medical aid premium, including the employer
          and employee's contribution.
        </p>

        <label for="employerContribution"
          >Portion contributed by employer:</label
        >
        <input
          type="number"
          id="employerContribution"
          name="employerContribution"
          placeholder="Enter Amount"
        />
        <p>
          The system deducts the employer portion from the Total amount per
          period to determine the employee contribution automatically.
        </p>

        <label for="employeeHandlesPayment">
          <input
            type="checkbox"
            id="employeeHandlesPayment"
            name="employeeHandlesPayment"
          />
          Employee handles the payment
        </label>

        <div id="dontApplyTaxCreditsContainer" style="display: none">
          <label>
            <input type="checkbox" name="dontApplyTaxCredits" />
            Don't apply tax credits
          </label>
        </div>

        <label class="label-members" for="members">
          Members (incl. employee):
        </label>
        <input
          type="number"
          id="members"
          name="members"
          placeholder="Enter Number of Members"
          required
        />

        <div class="form-actions">
          <a
            href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>"
            class="button-links"
          >
            Back
          </a>
          <button type="submit">Submit</button>
        </div>
      </form>

      <script>
        const employeeHandlesPaymentCheckbox = document.getElementById(
          "employeeHandlesPayment"
        );
        const dontApplyTaxCreditsContainer = document.getElementById(
          "dontApplyTaxCreditsContainer"
        );

        employeeHandlesPaymentCheckbox.addEventListener("change", function () {
          dontApplyTaxCreditsContainer.style.display = this.checked
            ? "block"
            : "none";
        });
      </script>
    </main>
  </body>
</html>
