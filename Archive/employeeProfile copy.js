const express = require("express");
const router = express.Router();
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const Company = require("../models/company");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const formatDate = require("../routes/helpers");
const PDFDocument = require("pdfkit");
const fs = require("fs");
const moment = require("moment-timezone");

const {
  getEndOfMonthDate,
  getSelectMonthDate,
  getNextMonth,
  getLastDayOfMonth,
  determineUnfinalizedMonth,
} = require("../utils/dateUtils");

const calculateDaysWorked = (doa) => {
  const dateOfAppointment = new Date(doa);
  const year = dateOfAppointment.getFullYear();
  const month = dateOfAppointment.getMonth();
  const lastDayOfMonth = new Date(year, month + 1, 0).getDate(); // Get the last day of the month
  const daysWorked = lastDayOfMonth - dateOfAppointment.getDate() + 1;
  return daysWorked;
};

const calculateTotalDaysInMonth = (doa) => {
  const dateOfAppointment = new Date(doa);
  const year = dateOfAppointment.getFullYear();
  const month = dateOfAppointment.getMonth();
  const lastDayOfMonth = new Date(year, month + 1, 0).getDate(); // Get the last day of the month
  return lastDayOfMonth;
};

const calculateProratedPercentage = (daysWorked, totalDaysInMonth) => {
  return (daysWorked / totalDaysInMonth) * 100;
};

const calculateProratedSalary = (basicSalary, doa) => {
  const dateOfAppointment = new Date(doa);
  const today = new Date();

  // Check if the employee started this month
  const isSameMonth =
    dateOfAppointment.getFullYear() === today.getFullYear() &&
    dateOfAppointment.getMonth() === today.getMonth();

  if (isSameMonth) {
    const daysWorked = calculateDaysWorked(doa);
    const totalDaysInMonth = calculateTotalDaysInMonth(doa);
    const proratedPercentage = calculateProratedPercentage(
      daysWorked,
      totalDaysInMonth
    );
    const proratedSalary = (basicSalary * proratedPercentage) / 100;
    return { proratedSalary, isProrated: true };
  }

  return { proratedSalary: basicSalary, isProrated: false };
};

//______________________________________

// Add this function at the top of your routes/employeeProfile.js file
function isPayrollFinalized(payroll) {
  return payroll && payroll.finalised;
}

//_______________________

//________________________
router.get("/:employeeId/edit/:componentName", async (req, res) => {
  try {
    const { employeeId, componentName } = req.params;
    const { thisMonth, amount } = req.body;
    const payroll = await Payroll.findOne({
      employee: employeeId,
      month: thisMonth,
    });

    if (!payroll) {
      return res.status(404).send("Payroll data not found");
    }

    payroll[componentName] = { amount: parseFloat(amount) };
    await payroll.save();

    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    res.status(500).send("Server Error");
  }
});

//________________________

router.post("/:employeeId/edit/basicInfo", async (req, res) => {
  const { employeeId } = req.params;
  const {
    firstName,
    lastName,
    dob,
    idType,
    idNumber,
    passportNumber,
    payFrequency,
    doa,
    jobTitle,
    email,
    costCentre,
    incomeTaxNumber,
    employeeNumber,
    paymentMethod,
    bank,
    accountType,
    accountNumber,
    branchCode,
    holderRelationship,
    unitNumber,
    complex,
    streetNumber,
    street,
    suburb,
    city,
    code,
    line1,
    line2,
    line3,
    hoursPerDay,
  } = req.body;

  try {
    // Update the employee's basic info in MongoDB
    await Employee.findByIdAndUpdate(employeeId, {
      firstName,
      lastName,
      dob,
      idType,
      idNumber,
      passportNumber,
      payFrequency,
      doa,
      jobTitle,
      email,
      costCentre,
      incomeTaxNumber,
      employeeNumber,
      paymentMethod,
      bank,
      accountType,
      accountNumber,
      branchCode,
      holderRelationship,
      unitNumber,
      complex,
      streetNumber,
      street,
      suburb,
      city,
      code,
      line1,
      line2,
      line3,
      hoursPerDay,
    });

    // Redirect back to the employee's profile page after success
    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    // Handle errors
    res
      .status(500)
      .json({ success: false, message: "Failed to update employee." });
  }
});

// Route to edit payComponents for a specific month
router.post(
  "/:employeeId/edit/:componentName",
  ensureAuthenticated,
  async (req, res) => {
    const { employeeId, componentName } = req.params;
    const { thisMonth, amount } = req.body;

    try {
      const employee = await Employee.findById(employeeId).populate("company");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }
      if (!employee.company) {
        return res.status(404).send("Company not found for this employee");
      }

      const company = employee.company; // Get the company from the employee

      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: moment(thisMonth).format("YYYY-MM-DD"),
      });

      if (!payroll) {
        // If payroll doesn't exist, create a new one
        payroll = new Payroll({
          employee: employeeId,
          company: company._id,
          month: moment(thisMonth).format("YYYY-MM-DD"),
        });
      }
      // Update the payroll data based on the component name
      payroll[componentName] = { amount: parseFloat(amount) };
      payroll.company = company._id; // Ensure the company is included

      await payroll.save();

      res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating payroll data:", error);
      res.status(500).send("Error updating payroll data");
    }
  }
);

//_____________________________

//______________________________
router.get("/:employeeId/regularInputs/new", async (req, res) => {
  const { employeeId } = req.params;
  const { regularInputs_name } = req.query;

  if (!regularInputs_name) {
    req.flash("error", "Component name is missing");
    return res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  }

  try {
    // Fetch employee details
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    // Retrieve or set thisMonth to the last day of the month from employee's date of appointment
    const thisMonth = new Date(
      req.session.thisMonth || getLastDayOfMonth(new Date(employee.doa))
    );

    // Fetch payroll for the specific employee and month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      month: thisMonth,
    });

    // If no payroll exists, create an empty payroll object for form purposes
    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: thisMonth,
      });
    }

    // Render the appropriate view based on regularInputs_name
    switch (regularInputs_name) {
      case "garnishee":
        return res.render("newGarnishee", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "accommodation_benefit":
        return res.render("newAccommodationBenefitInput", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "income_protection":
        return res.render("newIncomeProtection", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "travel_allowance":
        return res.render("newTravelAllowanceInput", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "maintenance_order":
        return res.render("newMaintenanceOrder", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "medical-aid":
        return res.render("newMedicalAid", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "pension_fund":
        return res.render("newPensionFund", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "provident_fund":
        return res.render("newProvidentFund", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "retirement_annuity_fund":
        return res.render("newRetirementAnnuityFund", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "union_membership_fee":
        return res.render("newUnionMembershipFee", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "voluntary_tax_over_deduction":
        return res.render("newVoluntaryTaxOverDeduction", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "savings":
        return res.render("newSavingsInput", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "company_car":
        return res.render("newCompanyCar", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "bursaries_and_scholarships":
        return res.render("newBursariesAndScholarships", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });

      case "commission":
        return res.render("newCommission", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "lossOfIncome":
        return res.render("newLossOfIncomeInput", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      case "company_car_under_operating_lease":
        return res.render("newCompanyCarUnderOperatingLease", {
          employee,
          payroll,
          thisMonth,
          messages: req.flash(),
        });
      // Add other cases as needed
      default:
        req.flash("error", `Unknown component name: ${regularInputs_name}`);
        return res.redirect(
          `/clients/${company.companyCode}/employeeProfile/${employeeId}`
        );
    }
  } catch (error) {
    console.error("Error fetching employee or payroll:", error);
    req.flash("error", "Error fetching employee or payroll data");
    return res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  }
});

//_______________________________

router.post("/:employeeId/regularInputs/new", async (req, res) => {
  const { employeeId } = req.params;
  const {
    componentName,
    amount,
    employerContribution,
    employeeHandlesPayment,
    deemedValue,
    includesMaintenancePlan,
    taxablePercentage,
    members, // Medical-related
    thisMonth,
    fixedAllowance,
    fixedAmount,
    reimbursedExpenses,
    companyPetrolCard,
    reimbursedPerKmTravelled,
    ratePerKm,
    only20PercentTax,
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
    beneficiary,
    employeeContribution,
    fixedAllowancePaidRegularly,
  } = req.body;

  try {
    // Find the employee and populate the company
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }
    if (!employee.company) {
      req.flash("error", "Company information not found for this employee");
      return res.redirect("/");
    }

    const company = employee.company;

    if (!componentName) {
      req.flash("error", "Component name is required");
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Parse necessary values
    const parsedAmount = parseFloat(amount) || 0;
    const parsedFixedAmount = parseFloat(fixedAmount) || 0;
    const parsedRatePerKm = parseFloat(ratePerKm) || 0;
    const parsedFixedContributionEmployee =
      parseFloat(fixedContributionEmployee) || 0;
    const parsedFixedContributionEmployer =
      parseFloat(fixedContributionEmployer) || 0;
    const parsedRfiEmployee = parseFloat(rfiEmployee) || 0;
    const parsedRfiEmployer = parseFloat(rfiEmployer) || 0;
    const parsedMembers = parseInt(members, 10) || 0;
    const paidMonth = moment
      .tz(thisMonth, "Africa/Johannesburg")
      .endOf("month");

    let payroll = await Payroll.findOne({
      employee: employeeId,
      month: paidMonth.toDate(),
      company: company._id,
    });

    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: paidMonth.toDate(),
        company: company._id,
      });
    }

    // Update payroll with component-specific data
    switch (componentName) {
      case "medical":
        payroll.medical = {
          medicalAid: parsedAmount,
          employerContribution: parseFloat(employerContribution) || 0,
          members: parsedMembers,
          employeeHandlesPayment: employeeHandlesPayment === "on",
        };
        break;
      case "companyCar":
        payroll.companyCar = {
          deemedValue: parseFloat(deemedValue) || 0,
          includesMaintenancePlan: includesMaintenancePlan === "on",
          taxablePercentage: taxablePercentage || "80%",
        };
        break;
      case "companyCarUnderOperatingLease":
        payroll.companyCarUnderOperatingLease = {
          amount: parsedAmount,
          taxablePercentage: taxablePercentage || "80%",
        };
        break;
      case "providentFund":
        payroll.providentFund = {
          amount: parsedAmount,
          employerContribution: employerContribution === "on",
          employeeHandlesPayment: employeeHandlesPayment === "on",
          contributionCalculation,
          fixedContributionEmployee: parsedFixedContributionEmployee,
          fixedContributionEmployer: parsedFixedContributionEmployer,
          rfiEmployee: parsedRfiEmployee,
          rfiEmployer: parsedRfiEmployer,
          categoryFactor: categoryFactor || null,
          beneficiary: beneficiary || null,
        };
        break;
      case "pensionFund":
        payroll.pensionFund = {
          amount: parsedAmount,
          employerContribution: employerContribution === "on",
          contributionCalculation,
          fixedContributionEmployee: parsedFixedContributionEmployee,
          fixedContributionEmployer: parsedFixedContributionEmployer,
          rfiEmployee: parsedRfiEmployee,
          rfiEmployer: parsedRfiEmployer,
          categoryFactor: categoryFactor || null,
          beneficiary: beneficiary || null,
        };
        break;
      case "retirementAnnuityFund":
        payroll.retirementAnnuityFund = {
          employeeContribution: parseFloat(employeeContribution) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
          employeeHandlesPayment: employeeHandlesPayment === "on",
          beneficiary: beneficiary || "",
        };
        break;
      case "bursariesAndScholarships":
        payroll.bursariesAndScholarships = {
          taxablePortion: parseFloat(req.body.taxablePortion) || 0,
          exemptPortion: parseFloat(req.body.exemptPortion) || 0,
          type: req.body.type || "",
          employeeHandlesPayment: req.body.employeeHandlesPayment === "on",
          toDisabledPerson: req.body.toDisabledPerson === "on",
        };
        break;
      case "commission":
        payroll.commission = { amount: parsedAmount };
        break;
      case "garnishee":
        payroll.garnishee = parsedAmount;
        break;
      case "travelAllowance":
        payroll.travelAllowance = {
          fixedAllowance: fixedAllowancePaidRegularly === "on",
          fixedAllowanceAmount: parsedFixedAmount,
          reimbursedExpenses: reimbursedExpenses === "on",
          companyPetrolCard: companyPetrolCard === "on",
          reimbursedPerKm: reimbursedPerKmTravelled === "on",
          ratePerKm: parsedRatePerKm,
          only20PercentTax: only20PercentTax === "on",
        };
        break;
      case "maintenanceOrder":
        payroll.maintenanceOrder = parsedAmount || 0;
        break;
      case "lossOfIncome":
        payroll.lossOfIncome = parsedAmount || 0;
        break;
      case "unionMembershipFee":
        payroll.unionMembershipFee = parsedAmount || 0;
        break;
      case "voluntaryTaxOverDeduction":
        payroll.voluntaryTaxOverDeduction = parsedAmount || 0;
        break;
      case "accommodationBenefit":
        payroll.accommodationBenefit = parsedAmount || 0;
        break;
      default:
        req.flash("error", "Unknown component name");
        return res.redirect(
          `/clients/${company.companyCode}/employeeProfile/${employeeId}`
        );
    }

    await payroll.save();
    req.flash("success", "Employee payroll data updated successfully");
    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}?nextMonth=${thisMonth}`
    );
  } catch (error) {
    console.error("Error updating payroll data:", error);
    req.flash("error", "Error updating payroll data");
    // Use a safe fallback if company is not available
    res.redirect(`/employeeProfile/${employeeId}`);
  }
});

//_______________________________

router.post("/:employeeId", async (req, res) => {
  const { employeeId } = req.params;
  const { finalise, ...formData } = req.body;

  try {
    const employee = await Employee.findOne({
      _id: employeeId,
      company: req.user.currentCompany,
    });

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    let currentMonth = req.body.currentMonth || employee.doa;
    currentMonth = new Date(currentMonth);

    // Find existing payroll data for the current month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: currentMonth,
    });

    if (payroll) {
      payroll.finalised = !!finalise;
    } else {
      payroll = new Payroll({
        employee: employeeId,
        company: req.user.currentCompany,
        month: currentMonth,
        finalised: !!finalise,
      });
    }

    // Save the current payroll record
    await payroll.save();

    // Calculate the next month
    const nextMonth = getNextMonth(currentMonth);

    // Check if payroll for the next month already exists
    let nextPayroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: nextMonth,
    });

    // If no payroll exists for the next month, create it and carry forward the basicSalary
    if (!nextPayroll) {
      nextPayroll = new Payroll({
        employee: employeeId,
        company: req.user.currentCompany,
        month: nextMonth,
        basicSalary: payroll.basicSalary, // Carry forward basicSalary
        medical: payroll.medical, // Carry forward medical
        finalised: false,
      });
    } else {
      // Ensure that if payroll for next month exists, we only carry forward if basicSalary is not set
      if (nextPayroll.basicSalary === 0) {
        nextPayroll.basicSalary = payroll.basicSalary;
      }
      if (nextPayroll.medical === 0) {
        nextPayroll.medical = payroll.medical;
      }
    }

    // Save the next month's payroll
    await nextPayroll.save();

    // Find the most recent unfinalized payroll
    const unfinalizedPayroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      finalised: false,
    }).sort({ month: -1 });

    const unfinalizedMonth = unfinalizedPayroll
      ? unfinalizedPayroll.month
      : null;

    // Store the flash message only if finalise is true
    if (finalise) {
      req.flash("success", "Employee payroll data updated successfully");
    }

    // Redirect with nextMonth as a query parameter
    const nextMonthString =
      nextMonth instanceof Date
        ? nextMonth.toISOString().split("T")[0]
        : moment(nextMonth).format("YYYY-MM-DD");

    res.redirect(
      `/clients/${
        req.user.currentCompany.companyCode
      }/employeeProfile/${employeeId}?nextMonth=${nextMonthString}&unfinalizedMonth=${
        unfinalizedMonth ? unfinalizedMonth.toISOString().split("T")[0] : ""
      }`
    );
  } catch (error) {
    console.error("Error updating payroll data:", error);
    req.flash("error", "Error updating payroll data");
    res.redirect(
      `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
    );
  }
});

//______________________________________

//______________
router.post("/updatePayroll/:id", async (req, res) => {
  const employeeId = req.params.id;
  const { componentName, amount, month } = req.body;
  console.log("Received form data:", req.body);

  try {
    // Validate and parse inputs
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount)) {
      return res.status(400).send("Invalid amount provided");
    }

    // Fetch the payroll document for the employee
    let payroll = await Payroll.findOne({
      employee: employeeId,
      finalised: false,
    });

    // Create a new payroll entry if none exists
    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: new Date(month), // Use the provided month
        payComponents: [],
      });
    } else {
      // Update the month if payroll exists and is not finalised
      payroll.month = new Date(month);
    }

    // Add a new pay component
    payroll.payComponents.push({
      componentType: componentName,
      amount: parsedAmount, // Save the parsed amount
    });

    // Save the updated or newly created payroll document
    await payroll.save();

    res.status(200).send("Payroll updated successfully");
  } catch (error) {
    console.error("Error updating payroll:", error);
    res.status(500).send("Internal server error");
  }
});

// Route to fetch payroll data for a specific month
router.get("/:employeeId/payroll/:month", async (req, res) => {
  try {
    const { employeeId, month } = req.params;
    const employee = await Employee.findById(employeeId).populate("payroll");
    const payroll = await Payroll.findOne({ employee: employeeId, month });

    if (!employee || !payroll) {
      return res.status(404).send("Payroll data not found");
    }

    res.render("employeeProfile", { employee, payroll, selectedMonth: month });
  } catch (error) {
    res.status(500).send("Server Error");
  }
});

//______________________________________________

// GET route to render the defineRFI page
router.get("/:employeeId/defineRFI", async (req, res) => {
  const { employeeId } = req.params;
  try {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).send("Employee not found");
    }
    res.render("defineRFI", { employeeId });
  } catch (error) {
    res.status(500).send("Server Error");
  }
});

// POST route to save RFI determination
router.post("/:employeeId/defineRFI", async (req, res) => {
  const { employeeId } = req.params;
  const { rfiOption, selectedIncomes, percentageIncomes, percentageValues } =
    req.body;

  try {
    // Find employee and save the selected RFI determination logic
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Logic to update payroll based on RFI selection
    // Save the RFI determination in payroll for the employee or perform other required calculations
    // Add your logic here

    // Redirect to the employee profile or another page after saving
    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    res.status(500).send("Server Error");
  }
});

// DELETE route to remove an employee

router.delete("/:employeeId/delete", async (req, res) => {
  const { employeeId } = req.params;

  console.log(`Attempting to delete employee with ID: ${employeeId}`);

  try {
    // Delete the employee
    const deletedEmployee = await Employee.findByIdAndDelete(employeeId);

    if (!deletedEmployee) {
      console.log(`Employee with ID ${employeeId} not found`);
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    console.log(`Employee deleted: ${deletedEmployee._id}`);

    // Delete associated payroll data
    const deletedPayrolls = await Payroll.deleteMany({ employee: employeeId });
    console.log(`Deleted ${deletedPayrolls.deletedCount} payroll records`);

    res.json({ success: true, message: "Employee deleted successfully" });
  } catch (error) {
    console.error("Error deleting employee:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete employee",
      error: error.message,
    });
  }
});

//__________________________________________________

router.post("/:employeeId/edit/:componentName/:month", async (req, res) => {
  try {
    console.log("Update route hit");
    console.log(req.params);
    console.log(req.body);
    const { employeeId, componentName, month } = req.params;
    const {
      amount,
      employerContribution,
      employeeHandlesPayment,
      members,
      dontApplyTaxCredits,
    } = req.body;

    // Convert month parameter to a Date object
    const selectedMonth = new Date(month);

    // Find the specific payroll document for the selected month
    const currentPayroll = await Payroll.findOne({
      employee: employeeId,
      month: selectedMonth,
    });

    if (!currentPayroll) {
      return res.status(404).send("Payroll for the selected month not found");
    }

    // Update the specific pay component in the payroll document
    switch (componentName) {
      case "medical":
        currentPayroll.medical = {
          medicalAid: parseFloat(amount) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
          members: parseInt(members, 10) || 0,
          employeeHandlesPayment: employeeHandlesPayment === "on",
          dontApplyTaxCredits: dontApplyTaxCredits === "on",
        };
        break;

      case "bursariesAndScholarships":
        const parsedTaxablePortion = parseFloat(req.body.taxablePortion) || 0;
        const parsedExemptPortion = parseFloat(req.body.exemptPortion) || 0;
        const type = req.body.type || "";
        const employeeHandlesPayment = req.body.employeeHandlesPayment === "on";
        const toDisabledPerson = req.body.toDisabledPerson === "on";

        payroll.bursariesAndScholarships = {
          taxablePortion: parsedTaxablePortion,
          exemptPortion: parsedExemptPortion,
          type: type,
          employeeHandlesPayment: employeeHandlesPayment,
          toDisabledPerson: toDisabledPerson,
        };
        break;

      case "retirementAnnuityFund":
        const parsedEmployeeContribution =
          parseFloat(employeeContribution) || 0;
        const parsedEmployerContribution =
          parseFloat(employerContribution) || 0;

        payroll.retirementAnnuityFund = {
          employeeContribution: parsedEmployeeContribution,
          employerContribution: parsedEmployerContribution,
          employeeHandlesPayment: employeeHandlesPayment === "on",
          beneficiary: beneficiary || "",
        };
        break;

      case "providentFund":
        currentPayroll.providentFund = {
          amount: parseFloat(amount) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
          employeeHandlesPayment: employeeHandlesPayment === "on",
        };
        break;

      case "commission":
        currentPayroll.commission = {
          amount: parseFloat(amount) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
        };
        break;
      case "maintenanceOrder":
        currentPayroll.maintenanceOrder = parseFloat(amount) || 0; // Or any logic to combine the values
        break;
      case "lossOfIncome":
        currentPayroll.lossOfIncome = parseFloat(amount) || 0; // Or any logic to combine the values
        break;
      case "voluntaryTaxOverDeduction":
        currentPayroll.voluntaryTaxOverDeduction = parseFloat(amount) || 0; // Or any logic to combine the values
        break;
      case "accommodationBenefit":
        currentPayroll.accommodationBenefit = parseFloat(amount) || 0; // Or any logic to combine the values
        break;

      case "travelAllowance":
        currentPayroll.travelAllowance = {
          amount: parseFloat(amount) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
          employeeHandlesPayment: employeeHandlesPayment === "on",
        };
        break;

      // Add cases for other component types if needed

      default:
        return res.status(404).send("Unknown component name");
    }

    // Save the updated payroll document
    await currentPayroll.save();

    // Redirect back to the same month after saving
    res.redirect(
      `/clients/${
        company.companyCode
      }/employeeProfile/${employeeId}?nextMonth=${selectedMonth.toISOString()}`
    );
  } catch (error) {
    console.error(error);
    res.status(500).send("An error occurred while updating the pay component.");
  }
});

//____________________________________________________________

router.post("/:employeeId/remove/:componentName/:month", async (req, res) => {
  try {
    const { employeeId, componentName, month } = req.params;

    // Convert month parameter to a Date object
    const selectedMonth = new Date(month);

    // Find the specific payroll document for the selected month
    const currentPayroll = await Payroll.findOne({
      employee: employeeId,
      month: selectedMonth,
    });

    if (!currentPayroll) {
      return res.status(404).send("Payroll for the selected month not found");
    }

    // Remove the specified component from the payroll document
    switch (componentName) {
      case "medical":
        currentPayroll.medical = undefined; // or set to {}
        break;

      case "companyCar":
        if (currentPayroll.companyCar) {
          currentPayroll.companyCar.deemedValue = undefined;
          currentPayroll.companyCar.includesMaintenancePlan = undefined;
          currentPayroll.companyCar.taxablePercentage = undefined;
        }
        break;
      case "providentFund":
        currentPayroll.providentFund = undefined; // or set to {}
        break;
      case "retirementAnnuityFund":
        currentPayroll.retirementAnnuityFund = undefined; // or set to {}
        break;

      case "lossOfIncome":
        currentPayroll.lossOfIncome = undefined; // or set to {}
        break;
      case "pensionFund":
        currentPayroll.pensionFund = undefined; // or set to {}
        break;
      case "travelAllowance":
        currentPayroll.travelAllowance = undefined; // or set to {}
        break;
      case "maintenanceOrder":
        currentPayroll.maintenanceOrder = undefined;
        break;
      // Remove the specified component from the payroll document
      case "bursariesAndScholarships":
        // Clear individual fields instead of setting entire object to undefined
        currentPayroll.bursariesAndScholarships.taxablePortion = undefined;
        currentPayroll.bursariesAndScholarships.exemptPortion = undefined;
        currentPayroll.bursariesAndScholarships.type = undefined;
        currentPayroll.bursariesAndScholarships.employeeHandlesPayment =
          undefined;
        currentPayroll.bursariesAndScholarships.toDisabledPerson = undefined;
        break;
      case "accommodationBenefit":
        currentPayroll.accommodationBenefit = undefined; // or set to {}
        break;
      case "unionMembershipFee":
        currentPayroll.unionMembershipFee = undefined; // or set to {}
        break;
      case "voluntaryTaxOverDeduction":
        currentPayroll.voluntaryTaxOverDeduction = undefined; // or set to {}
        break;
      case "incomeProtection":
        currentPayroll.incomeProtection = undefined; // or set to {}
        break;
      case "accommodationBenefit":
        currentPayroll.accommodationBenefit = undefined; // or set to {}
        break;

      case "commission":
        currentPayroll.commission = undefined; // or set to {}
        break;
      case "companyCarUnderOperatingLease":
        if (currentPayroll.companyCarUnderOperatingLease) {
          currentPayroll.companyCarUnderOperatingLease.amount = undefined;
          currentPayroll.companyCarUnderOperatingLease.taxablePercentage =
            undefined;
        }
        break;

      // Add cases for other component types if needed

      default:
        return res.status(404).send("Unknown component name");
    }

    // Save the updated payroll document
    await currentPayroll.save();

    // Flash message for success
    req.flash(
      "success",
      `Pay component '${componentName}' removed successfully!`
    );
    console.log(
      "success",
      `Pay component '${componentName}' removed successfully!`
    );

    // Redirect back to the same month after removing the pay component
    res.redirect(
      `/clients/${
        company.companyCode
      }/employeeProfile/${employeeId}?nextMonth=${selectedMonth.toISOString()}`
    );
  } catch (error) {
    console.error(error);
    res.status(500).send("An error occurred while removing the pay component.");
  }
});

//----------------------

router.get("/:employeeId/payslipInput", async (req, res) => {
  const { employeeId } = req.params;

  try {
    const employee = await Employee.findById(employeeId).populate("payroll");

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    // Retrieve thisMonth from session and ensure it's a Date object
    const thisMonth = req.session.thisMonth || getLastDayOfMonth(new Date());
    console.log("thisMonth:", thisMonth);

    res.render("payslipInput", {
      employee,
      thisMonth: new Date(thisMonth),
      payroll: employee.payroll,
      messages: req.flash(),
    });
  } catch (error) {
    console.error("Error fetching employee:", error);
    req.flash("error", "Error fetching employee");
    res.redirect("/");
  }
});

router.get(
  "/:employeeId/termination-certificate/:periodIndex",
  async (req, res) => {
    try {
      const { employeeId, periodIndex } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      const servicePeriod = employee.servicePeriods[periodIndex];
      if (!servicePeriod) {
        return res.status(404).send("Service period not found");
      }

      // Create a new PDF document
      const doc = new PDFDocument();

      // Set the response headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=termination_certificate_${employeeId}_${periodIndex}.pdf`
      );

      // Pipe the PDF document to the response
      doc.pipe(res);

      // Add content to the PDF
      doc.fontSize(18).text("Termination Certificate", { align: "center" });
      doc.moveDown();
      doc
        .fontSize(12)
        .text(`Employee: ${employee.firstName} ${employee.lastName}`);
      doc.text(`Employee Number: ${employee.employeeNumber}`);
      doc.text(
        `Service Period: ${servicePeriod.startDate.toLocaleDateString()} to ${
          servicePeriod.endDate
            ? servicePeriod.endDate.toLocaleDateString()
            : "Present"
        }`
      );
      doc.text(
        `Reason for Termination: ${servicePeriod.terminationReason || "N/A"}`
      );
      // Add more details as needed

      // Finalize the PDF and end the stream
      doc.end();
    } catch (error) {
      console.error("Error generating termination certificate:", error);
      res.status(500).send("Error generating termination certificate");
    }
  }
);

router.get(
  "/:employeeId/edit-termination-certificate/:periodIndex",
  async (req, res) => {
    try {
      const { employeeId, periodIndex } = req.params;
      const employee = await Employee.findById(employeeId);

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      const servicePeriod = employee.servicePeriods[periodIndex];
      if (!servicePeriod) {
        return res.status(404).send("Service period not found");
      }

      res.render("editTerminationCertificate", {
        employee,
        servicePeriod,
        periodIndex,
      });
    } catch (error) {
      console.error(
        "Error rendering edit termination certificate page:",
        error
      );
      res.status(500).send("Error rendering edit termination certificate page");
    }
  }
);

// // POST route to handle form submissions
// router.post("/:employeeId", async (req, res) => {
//   const { employeeId } = req.params;
//   const { componentName, amount, month, ...formData } = req.body;

//   console.log("Received form data:", req.body);

//   console.log("Received month:", month);

//   const paidMonth = new Date(month);
//   if (isNaN(paidMonth.getTime())) {
//     console.error("Invalid month date:", month);
//     req.flash("error", "Invalid month date provided");
//     return res.redirect(`/employeeProfile/${employeeId}`);
//   }

//   try {
//     let employee = await Employee.findById(employeeId);

//     if (!employee) {
//       req.flash("error", "Employee not found");
//       return res.redirect("/");
//     }

//     const payrollData = {};

//     switch (componentName) {
//       case "garnishee":
//         payrollData.garnishee = parseFloat(amount);
//         break;
//       case "bursaries_and_scholarships":
//         payrollData.bursariesAndScholarships = parseFloat(amount);
//         break;
//       case "company_car":
//         payrollData.companyCar = parseFloat(amount);
//         break;
//       case "savings":
//         payrollData.savings = parseFloat(amount);
//         break;
//       case "company_car_under_operating_lease":
//         payrollData.companyCarUnderOperatingLease = parseFloat(amount);
//         break;
//       case "income_protection":
//         payrollData.incomeProtection = parseFloat(amount);
//         break;
//       case "maintenance_order":
//         payrollData.maintenanceOrder = parseFloat(amount);
//         break;
//       case "medical_aid":
//         payrollData.medical = {
//           medicalAid: parseFloat(formData.medicalAid),
//           members: parseInt(formData.members),
//           employerContribution: parseFloat(formData.employerContribution),
//         };
//         break;
//       case "pension_fund":
//         payrollData.pensionFund = parseFloat(amount);
//         break;
//       case "provident_fund":
//         payrollData.providentFund = parseFloat(amount);
//         break;
//       case "retirement_annuity_fund":
//         payrollData.retirementAnnuityFund = parseFloat(amount);
//         break;
//       case "union_membership_fee":
//         payrollData.unionMembershipFee = parseFloat(amount);
//         break;
//       case "voluntary_tax_over_deduction":
//         payrollData.voluntaryTaxOverDeduction = parseFloat(amount);
//         break;
//       case "employer_loan":
//         payrollData.employerLoan = parseFloat(amount);
//         break;
//       case "foreign_service_income":
//         payrollData.foreignServiceIncome = parseFloat(amount);
//         break;
//       case "tax_directive":
//         payrollData.taxDirective = parseFloat(amount);
//         break;
//       case "loss_of_income":
//         payrollData.lossOfIncome = parseFloat(amount);
//         break;
//       case "accommodation_benefit":
//         payrollData.accommodationBenefit = parseFloat(amount);
//         break;
//       case "travel_allowance":
//         payrollData.travelAllowance = {
//           fixedAllowance: formData.fixedAllowance === "true",
//           fixedAllowanceAmount:
//             parseFloat(formData.fixedAllowanceAmount) || null,
//           reimbursedExpenses: formData.reimbursedExpenses === "true",
//           companyPetrolCard: formData.companyPetrolCard === "true",
//           reimbursedPerKm: formData.reimbursedPerKm === "true",
//           ratePerKm: parseFloat(formData.ratePerKm) || null,
//           only20PercentTax: formData.only20PercentTax === "true",
//         };
//         break;
//       default:
//         break;
//     }

//     // Check if a payroll document exists for the employee and the selected month
//     let payroll = await Payroll.findOne({
//       employee: employeeId,
//       month: paidMonth,
//     });

//     if (!payroll) {
//       // Create a new payroll document if it does not exist
//       payroll = new Payroll({
//         month: paidMonth,
//         employee: employeeId,
//       });
//     }

//     // Merge existing payroll data with the new data
//     Object.assign(payroll, payrollData);

//     // Add pay components to the payroll document
//     if (!payroll.payComponents) {
//       payroll.payComponents = [];
//     }

//     payroll.payComponents.push({
//       componentType: componentName,
//       amount: parseFloat(amount),
//     });

//     // Save the updated payroll document
//     await payroll.save();

//     // Flash message to notify success
//     req.flash("success", "Employee payroll data updated successfully");

//     // Update thisMonth to the next month after finalisation
//     const thisMonth = getNextMonth(month);
//     res.redirect(`/employeeProfile/${employeeId}?thisMonth=${thisMonth}`);
//   } catch (error) {
//     console.error("Error updating payroll data:", error);
//     req.flash("error", "Error updating payroll data");
//     res.redirect(`/employeeProfile/${employeeId}`);
//   }
// });
//______________________________________________

// Route to display employee profile
// router.get("/:employeeId", async (req, res) => {
//   const { employeeId } = req.params;

//   try {
//     console.log("Received request for employee profile with ID:", employeeId);

//     // Check if employeeId is valid ObjectId
//     if (!mongoose.Types.ObjectId.isValid(employeeId)) {
//       console.error("Invalid employee ID format");
//       req.flash("error", "Invalid employee ID format");
//       return res.redirect("/");
//     }

//     // Find employee and populate payroll data
//     const employee = await Employee.findById(employeeId).populate("payroll");
//     if (!employee) {
//       console.error("Employee Not Found");
//       req.flash("error", "Employee Not Found");
//       return res.redirect("/");
//     }

//     // Calculate end of DOA month
//     const endOfDoaMonth = getLastDayOfMonth(employee.doa);

//     // Find payroll data for the employee
//     let payroll = await Payroll.findOne({ employee: employeeId });
//     if (!payroll) {
//       console.log(
//         "Payroll data not found for this employee, creating new payroll entry"
//       );

//       // Create a new Payroll entry if it doesn't exist
//       payroll = new Payroll({
//         month: endOfDoaMonth,
//         employee: employeeId,
//       });

//       // Save the new Payroll entry
//       await payroll.save();
//     }

//     console.log("Employee and payroll data found:", { employee, payroll });

//     // Determine the selected month date
//     const paidMonth = getLastDayOfMonth(employee.doa);

//     // Set thisMonth to the last day of the current month
//     const thisMonth = getLastDayOfMonth(new Date());

//     // Check if request accepts JSON
//     const acceptJson =
//       req.headers.accept && req.headers.accept.includes("application/json");

//     if (acceptJson) {
//       return res.json({ employee, payroll, thisMonth });
//     } else {
//       return res.render("employeeProfile", {
//         employee,
//         payroll,
//         thisMonth: thisMonth,
//         endOfDoaMonth: endOfDoaMonth,
//         messages: req.flash(),
//       });
//     }
//   } catch (error) {
//     console.error("Error fetching employee or payroll data:", error);
//     req.flash("error", "Error fetching employee or payroll data");
//     return res.redirect("/");
//   }
// });

// //__________________________________________________________

router.get("/:employeeId/regularInputs", async (req, res) => {
  const { employeeId } = req.params;

  try {
    const employee = await Employee.findById(employeeId).populate("payroll");

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    // Retrieve thisMonth from session and ensure it's a Date object
    const thisMonth = req.session.thisMonth || getLastDayOfMonth(new Date());
    console.log("thisMonth:", thisMonth);

    res.render("regularInputs", {
      employee,
      thisMonth: new Date(thisMonth),
      payroll: employee.payroll,
      messages: req.flash(),
    });
  } catch (error) {
    console.error("Error fetching employee:", error);
    req.flash("error", "Error fetching employee");
    res.redirect("/");
  }
});

// ///////////////////////////////////////

// // Route to get the form page
// router.get("/:employeeId/regularInputs/new", async (req, res) => {
//   const { employeeId } = req.params;
//   const { regularInputs_name, nextMonth } = req.query;

//   if (!regularInputs_name) {
//     req.flash("error", "Component name is missing");
//     return res.redirect(`/employeeProfile/${employeeId}`);
//   }

//   try {
//     const employee = await Employee.findById(employeeId);

//     if (!employee) {
//       req.flash("error", "Employee not found");
//       return res.redirect("/");
//     }

//     // Fetch payroll data for the employee
//     const payrolls = await Payroll.find({ employee: employeeId });

//     // Calculate thisMonth based on employee.doa or nextMonth if provided
//     let thisMonth = nextMonth
//       ? new Date(nextMonth)
//       : getLastDayOfMonth(new Date(employee.doa));

//     // Render the appropriate view based on regularInputs_name
//     switch (regularInputs_name) {
//       case "garnishee":
//         return res.render("newGarnishee", {
//           employee,
//           payrolls,
//           thisMonth,
//           messages: req.flash(),
//         });
//       // other cases ...
//       default:
//         req.flash("error", `Unknown component name: ${regularInputs_name}`);
//         return res.redirect(`/employeeProfile/${employeeId}`);
//     }
//   } catch (error) {
//     console.error("Error fetching employee:", error);
//     req.flash("error", "Error fetching employee");
//     return res.redirect(`/employeeProfile/${employeeId}`);
//   }
// });

// Route to handle finalizing the month
router.post("/:employeeId/finalise", async (req, res) => {
  const { employeeId } = req.params;
  const { currentMonth } = req.body;

  try {
    const employee = await Employee.findById(employeeId);

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    // Calculate the next month's end date
    const currentMonthDate = new Date(currentMonth);
    const nextMonthDate = new Date(
      currentMonthDate.getFullYear(),
      currentMonthDate.getMonth() + 1,
      1
    );
    const nextMonthEnd = getLastDayOfMonth(nextMonthDate);

    // Update the employee's latestSelectedMonth
    employee.latestSelectedMonth = nextMonthEnd;
    await employee.save();

    req.flash(
      "success",
      "The payslip has been finalised. You are now viewing the next period's payslip."
    );
    res.redirect(
      `/employeeProfile/${employeeId}/regularInputs/new?regularInputs_name=garnishee&nextMonth=${nextMonthEnd.toISOString()}`
    );
  } catch (error) {
    console.error("Error finalising month:", error);
    req.flash("error", "Error finalising month");
    return res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  }
});
// //________________________________________

router.post("/:employeeId/updateMonth", async (req, res) => {
  const { employeeId } = req.params;
  const { month } = req.body;

  try {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res
        .status(404)
        .json({ success: false, message: "Employee not found" });
    }

    const currentMonth = new Date(month);
    if (isNaN(currentMonth.getTime())) {
      throw new Error("Invalid current month date");
    }

    const nextMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      1
    );
    if (isNaN(nextMonth.getTime())) {
      throw new Error("Invalid next month date");
    }

    if (!employee.paidMonths) {
      employee.paidMonths = [];
    }
    employee.paidMonths.push(nextMonth);

    await employee.save();

    return res
      .status(200)
      .json({ success: true, message: "Payroll month finalized", nextMonth });
  } catch (error) {
    console.error("Error finalizing payroll month:", error.message);
    return res
      .status(500)
      .json({ success: false, message: "Failed to finalize payroll month" });
  }
});

// Fetch previous months for an employee
router.get("/:employeeId/months", async (req, res) => {
  const { employeeId } = req.params;

  try {
    const payrolls = await Payroll.find({ employee: employeeId })
      .select("month")
      .sort({ month: -1 });
    const months = payrolls.map((p) => p.month);

    res.json(months);
  } catch (error) {
    console.error("Error fetching previous months:", error);
    res.status(500).json({ message: "Failed to fetch previous months" });
  }
});

// Fetch payroll data for a specific month
router.get("/:employeeId/payroll/:month", async (req, res) => {
  const { employeeId, month } = req.params;

  try {
    const payrollData = await Payroll.findOne({
      employee: employeeId,
      month: new Date(month),
    });

    if (!payrollData) {
      return res
        .status(404)
        .json({ error: "Payroll data not found for the selected period" });
    }

    res.json(payrollData);
  } catch (error) {
    console.error("Error fetching payroll data:", error);
    res.status(500).json({ message: "Failed to fetch payroll data" });
  }
});

// Get Route payroll

router.get("/:employeeId/payroll", async (req, res) => {
  const { employeeId } = req.params;
  const { month } = req.query;

  try {
    const payroll = await Payroll.findOne({ employee: employeeId, month });
    if (!payroll) {
      return res.status(404).json({ error: "Payroll data not found" });
    }
    res.json(payroll);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch payroll data" });
  }
});

// Fetch selected month for an employee
router.get("/:employeeId/selectedMonth", async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.employeeId);
    if (!employee) {
      return res.status(404).send("Employee not found");
    }
    const selectedMonth = employee.latestSelectedMonth || employee.doa;
    res.json({ selectedMonth });
  } catch (error) {
    res.status(500).send("Server error");
  }
});

// POST route to update selected month
router.post("/:employeeId/selectedMonth", async (req, res) => {
  try {
    const employeeId = req.params.employeeId;
    const { selectedMonth } = req.body;
    const employee = await Employee.findByIdAndUpdate(
      employeeId,
      { latestSelectedMonth: new Date(selectedMonth) },
      { new: true }
    );
    if (!employee) {
      return res.status(404).send("Employee not found");
    }
    res.json({ selectedMonth: employee.latestSelectedMonth });
  } catch (error) {
    console.error("Error updating selected month:", error.message);
    res.status(500).send("Server error");
  }
});
// Get date of appointment (DOA) for an employee

// Route to retrieve DOA and update payroll entry
router.get("/:employeeId/doa", async (req, res) => {
  const { employeeId } = req.params;

  try {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return res.status(404).json({ error: "Employee not found" });
    }

    const endOfDoaMonth = getEndOfMonthDate(employee.doa);

    // Find or create payroll data for the employee
    let payroll = await Payroll.findOne({ employee: employeeId });
    if (!payroll) {
      console.log(
        "Payroll data not found for this employee, creating new payroll entry"
      );
      payroll = new Payroll({
        month: endOfDoaMonth,
        employee: employeeId,
      });
    } else {
      // Update existing payroll entry
      payroll.month = endOfDoaMonth;
    }

    await payroll.save();

    res.status(200).json({ doa: employee.doa, payroll });
  } catch (error) {
    console.error(
      "Error fetching employee DOA or updating payroll:",
      error.message
    );
    res
      .status(500)
      .json({ error: "Failed to fetch employee DOA or update payroll" });
  }
});

//___________________________

//     // Save the payroll document
//     await payroll.save();
//     console.log("Payroll updated successfully:", payroll);
//     res.status(200).send("Payroll updated successfully");
//   } catch (error) {
//     console.error("Error updating payroll data:", error);
//     res.status(500).send("Internal Server Error");
//   }
// });

// //____________________________

// router.post("/:employeeId/updatePayroll", async (req, res) => {
//   const { employeeId } = req.params;
//   const { month, payrollData } = req.body;

//   try {
//     const payroll = await Payroll.findOne({ employee: employeeId, month });

//     if (!payroll) {
//       // If no payroll found, create a new one
//       payroll = new Payroll({
//         employee: employeeId,
//         month,
//         ...payrollData,
//       });
//     } else {
//       // If payroll found, update the existing data with new data
//       Object.keys(payrollData).forEach((key) => {
//         payroll[key] = payrollData[key] || payroll[key];
//       });
//     }

//     await payroll.save();

//     return res.status(200).json({ success: true, payroll });
//   } catch (error) {
//     console.error("Error updating payroll data:", error.message);
//     return res
//       .status(500)
//       .json({ success: false, message: "Failed to update payroll data" });
//   }
// });

// __________________________

// Add this new route to handle the end service form submission
router.post("/:employeeId/end-service", async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { lastDayOfService, uifStatusCode, terminationReason } = req.body;

    const employee = await Employee.findById(employeeId);

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/employeeManagement");
    }

    // Server-side date validation
    const lastDayOfServiceDate = new Date(lastDayOfService);
    const doaDate = new Date(employee.doa);

    if (isNaN(lastDayOfServiceDate.getTime())) {
      req.flash("error", "Invalid last day of service date");
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }

    if (lastDayOfServiceDate < doaDate) {
      req.flash(
        "error",
        `Last day of service (${lastDayOfService}) cannot be before the date of appointment (${
          employee.doa.toISOString().split("T")[0]
        })`
      );
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }

    employee.lastDayOfService = lastDayOfServiceDate;
    employee.uifStatusCode = uifStatusCode;
    employee.terminationReason = terminationReason;
    employee.employmentStatus = false;
    employee.status = "Inactive";

    await employee.save();

    req.flash("success", "Employee service ended successfully");
    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}`
    );
  } catch (error) {
    console.error("Error ending employee service:", error);
    req.flash("error", "Failed to end employee service");
    res.redirect(`/employeeProfile/${req.params.employeeId}`);
  }
}); // Make sure this closing brace is present

// If there are any other route handlers, they should be here

module.exports = router; // This should be the last line of the file
// GET route to display the annual bonus form
router.get("/:employeeId/onceOff/new", async (req, res) => {
  try {
    const { employeeId } = req.params;
    const employee = await Employee.findById(employeeId);

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Retrieve or set thisMonth to the last day of the month from employee's date of appointment
    const thisMonth = new Date(
      req.session.thisMonth || getLastDayOfMonth(new Date(employee.doa))
    );

    // Fetch payroll for the specific employee and month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      month: thisMonth,
    });

    // If no payroll exists, create an empty payroll object for form purposes
    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: thisMonth,
      });
    }

    // Render the annual bonus form
    res.render("onceOffAnnualBonus", {
      employee,
      payroll,
      thisMonth,
      messages: req.flash(),
    });
  } catch (error) {
    console.error("Error displaying annual bonus form:", error);
    req.flash("error", "An error occurred while loading the form");
    res.redirect(`/employeeProfile/${req.params.employeeId}`);
  }
});

router.post("/:employeeId/payslipInput/new", async (req, res) => {
  const { employeeId } = req.params;
  const {
    componentName,
    amount,
    employerContribution,
    employeeHandlesPayment,
    deemedValue,
    includesMaintenancePlan,
    taxablePercentage,
    members, // Medical-related
    thisMonth,
    fixedAllowance,
    fixedAmount,
    reimbursedExpenses,
    companyPetrolCard,
    reimbursedPerKmTravelled,
    ratePerKm,
    only20PercentTax,
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
    beneficiary,
    employeeContribution,
    fixedAllowancePaidRegularly,
  } = req.body;

  try {
    // Find the employee and populate the company
    const employee = await Employee.findById(employeeId).populate("company");
    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    if (!employee.company) {
      req.flash("error", "Company information not found for this employee");
      return res.redirect("/");
    }

    const company = employee.company;

    if (!componentName) {
      req.flash("error", "Component name is required");
      return res.redirect(
        `/clients/${company.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Parse necessary values
    const parsedAmount = parseFloat(amount) || 0;
    const parsedFixedAmount = parseFloat(fixedAmount) || 0;
    const parsedRatePerKm = parseFloat(ratePerKm) || 0;
    const parsedFixedContributionEmployee =
      parseFloat(fixedContributionEmployee) || 0;
    const parsedFixedContributionEmployer =
      parseFloat(fixedContributionEmployer) || 0;
    const parsedRfiEmployee = parseFloat(rfiEmployee) || 0;
    const parsedRfiEmployer = parseFloat(rfiEmployer) || 0;
    const parsedMembers = parseInt(members, 10) || 0;
    const paidMonth = new Date(thisMonth);

    // Fetch or create payroll for the employee for the given month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      month: paidMonth,
      company: company._id,
    });

    if (!payroll) {
      payroll = new Payroll({
        employee: employeeId,
        month: paidMonth,
        company: company._id,
      });
    }

    // Update payroll with component-specific data
    switch (componentName) {
      case "medical":
        payroll.medical = {
          medicalAid: parsedAmount,
          employerContribution: parseFloat(employerContribution) || 0,
          members: parsedMembers,
          employeeHandlesPayment: employeeHandlesPayment === "on",
        };
        break;
      case "annualBonus":
        payroll.annualBonus = {
          annualBonus: parsedAmount,
        };
        break;
      case "companyCar":
        payroll.companyCar = {
          deemedValue: parseFloat(deemedValue) || 0,
          includesMaintenancePlan: includesMaintenancePlan === "on",
          taxablePercentage: taxablePercentage || "80%",
        };
        break;
      case "companyCarUnderOperatingLease":
        payroll.companyCarUnderOperatingLease = {
          amount: parsedAmount,
          taxablePercentage: taxablePercentage || "80%",
        };
        break;
      case "providentFund":
        payroll.providentFund = {
          amount: parsedAmount,
          employerContribution: employerContribution === "on",
          employeeHandlesPayment: employeeHandlesPayment === "on",
          contributionCalculation,
          fixedContributionEmployee: parsedFixedContributionEmployee,
          fixedContributionEmployer: parsedFixedContributionEmployer,
          rfiEmployee: parsedRfiEmployee,
          rfiEmployer: parsedRfiEmployer,
          categoryFactor: categoryFactor || null,
          beneficiary: beneficiary || null,
        };
        break;
      case "pensionFund":
        payroll.pensionFund = {
          amount: parsedAmount,
          employerContribution: employerContribution === "on",
          contributionCalculation,
          fixedContributionEmployee: parsedFixedContributionEmployee,
          fixedContributionEmployer: parsedFixedContributionEmployer,
          rfiEmployee: parsedRfiEmployee,
          rfiEmployer: parsedRfiEmployer,
          categoryFactor: categoryFactor || null,
          beneficiary: beneficiary || null,
        };
        break;
      case "retirementAnnuityFund":
        payroll.retirementAnnuityFund = {
          employeeContribution: parseFloat(employeeContribution) || 0,
          employerContribution: parseFloat(employerContribution) || 0,
          employeeHandlesPayment: employeeHandlesPayment === "on",
          beneficiary: beneficiary || "",
        };
        break;
      case "bursariesAndScholarships":
        payroll.bursariesAndScholarships = {
          taxablePortion: parseFloat(req.body.taxablePortion) || 0,
          exemptPortion: parseFloat(req.body.exemptPortion) || 0,
          type: req.body.type || "",
          employeeHandlesPayment: req.body.employeeHandlesPayment === "on",
          toDisabledPerson: req.body.toDisabledPerson === "on",
        };
        break;
      case "commission":
        payroll.commission = { amount: parsedAmount };
        break;
      case "garnishee":
        payroll.garnishee = parsedAmount;
        break;
      case "travelAllowance":
        payroll.travelAllowance = {
          fixedAllowance: fixedAllowancePaidRegularly === "on",
          fixedAllowanceAmount: parsedFixedAmount,
          reimbursedExpenses: reimbursedExpenses === "on",
          companyPetrolCard: companyPetrolCard === "on",
          reimbursedPerKm: reimbursedPerKmTravelled === "on",
          ratePerKm: parsedRatePerKm,
          only20PercentTax: only20PercentTax === "on",
        };
        break;
      case "maintenanceOrder":
        payroll.maintenanceOrder = parsedAmount || 0;
        break;
      case "lossOfIncome":
        payroll.lossOfIncome = parsedAmount || 0;
        break;
      case "unionMembershipFee":
        payroll.unionMembershipFee = parsedAmount || 0;
        break;
      case "voluntaryTaxOverDeduction":
        payroll.voluntaryTaxOverDeduction = parsedAmount || 0;
        break;
      case "accommodationBenefit":
        payroll.accommodationBenefit = parsedAmount || 0;
        break;
      default:
        req.flash("error", "Unknown component name");
        return res.redirect(
          `/clients/${company.companyCode}/employeeProfile/${employeeId}`
        );
    }

    // Save the updated payroll
    await payroll.save();
    req.flash("success", "Employee payroll data updated successfully");
    res.redirect(
      `/clients/${company.companyCode}/employeeProfile/${employeeId}?nextMonth=${thisMonth}`
    );
  } catch (error) {
    console.error("Error updating payroll data:", error);
    req.flash("error", "Error updating payroll data");
    // Use a safe fallback if company is not available
    res.redirect(`/employeeProfile/${employeeId}`);
  }
});
//________________________________________________________

router.post("/:employeeId/unfinalise", async (req, res) => {
  const { employeeId } = req.params;
  const { month } = req.body;

  try {
    const employee = await Employee.findOne({
      _id: employeeId,
      company: req.user.currentCompany,
    });

    if (!employee) {
      req.flash("error", "Employee not found");
      return res.redirect("/");
    }

    const payrollMonth = new Date(month);

    // Find the payroll for the specified month
    let payroll = await Payroll.findOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: payrollMonth,
    });

    if (!payroll) {
      req.flash("error", "Payroll not found for the specified month");
      return res.redirect(
        `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
      );
    }

    // Unfinalise the payroll
    payroll.finalised = false;
    await payroll.save();

    // Delete the next month's payroll if it exists and is not finalised
    const nextMonth = new Date(
      payrollMonth.getFullYear(),
      payrollMonth.getMonth() + 1,
      1
    );
    await Payroll.deleteOne({
      employee: employeeId,
      company: req.user.currentCompany,
      month: nextMonth,
      finalised: false,
    });

    req.flash("success", "Payroll has been unfinalised successfully");
    res.redirect(
      `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}?nextMonth=${month}`
    );
  } catch (error) {
    console.error("Error unfinalising payroll:", error);
    req.flash("error", "Error unfinalising payroll");
    res.redirect(
      `/clients/${req.user.currentCompany.companyCode}/employeeProfile/${employeeId}`
    );
  }
});

//__________________________________________________________
module.exports = router;
