const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const path = require('path');
const ejs = require('ejs');

// Import the main app or create a test app
const app = require('../../app');

describe('Pre-validation Integration Tests', () => {
  let testCompany;
  let testUser;
  let authCookie;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/test_db');
    }
  });

  afterAll(async () => {
    // Clean up test database
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create test user and company
    const User = require('../../models/User');
    const Company = require('../../models/Company');
    const Employee = require('../../models/Employee');

    // Clean up existing test data
    await User.deleteMany({ email: '<EMAIL>' });
    await Company.deleteMany({ companyCode: 'TESTPV' });

    // Create test user
    testUser = await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      username: '<EMAIL>',
      password: 'hashedpassword',
      isVerified: true
    });

    // Create test company
    testCompany = await Company.create({
      name: 'Test Pre-validation Company',
      companyCode: 'TESTPV',
      owner: testUser._id,
      taxNumber: '1234567890',
      uifNumber: 'U123456',
      payelNumber: 'P123456'
    });

    // Update user with company
    testUser.companies = [testCompany._id];
    testUser.currentCompany = testCompany._id;
    await testUser.save();

    // Create test employees
    await Employee.create([
      {
        firstName: 'John',
        lastName: 'Doe',
        companyEmployeeNumber: 'EMP001',
        company: testCompany._id,
        idNumber: '8001015009087',
        taxNumber: '1234567890'
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        companyEmployeeNumber: 'EMP002',
        company: testCompany._id,
        idNumber: null, // Missing for validation testing
        taxNumber: null
      }
    ]);

    // Authenticate user (simplified for testing)
    authCookie = await authenticateUser(testUser);
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({ email: '<EMAIL>' });
    await Company.deleteMany({ companyCode: 'TESTPV' });
    await Employee.deleteMany({ company: testCompany._id });
  });

  describe('Complete Pre-validation Flow', () => {
    test('should load bi-annual filing page successfully', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual')
        .set('Cookie', authCookie)
        .expect(200);

      expect(response.text).toContain('Pre-validate Data');
      expect(response.text).toContain('TESTPV');
    });

    test('should access pre-validation page with valid parameters', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      expect(response.text).toContain('e@syFile Pre-Validation');
      expect(response.text).toContain('Test Pre-validation Company');
    });

    test('should display validation warnings for missing company data', async () => {
      // Update company to have missing data
      await Company.findByIdAndUpdate(testCompany._id, {
        taxNumber: null,
        uifNumber: null,
        payelNumber: null
      });

      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      expect(response.text).toContain('Company tax number is missing');
      expect(response.text).toContain('UIF number is missing');
      expect(response.text).toContain('PAYE number is missing');
    });

    test('should display validation warnings for employees with missing data', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      // Should show warnings for Jane Smith who has missing data
      expect(response.text).toContain('Employee Validation Warnings');
      expect(response.text).toContain('EMP002'); // Jane's employee number
    });

    test('should redirect when period parameter is missing', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate')
        .set('Cookie', authCookie)
        .expect(302);

      expect(response.headers.location).toBe('/clients/TESTPV/filing/bi-annual');
    });

    test('should redirect when company is not found', async () => {
      const response = await request(app)
        .get('/clients/INVALID/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(302);

      expect(response.headers.location).toBe('/dashboard');
    });

    test('should handle both February and August periods correctly', async () => {
      // Test February period
      const febResponse = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Feb')
        .set('Cookie', authCookie)
        .expect(200);

      expect(febResponse.text).toContain('e@syFile Pre-Validation');

      // Test August period
      const augResponse = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      expect(augResponse.text).toContain('e@syFile Pre-Validation');
    });

    test('should include back link to bi-annual filing page', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      expect(response.text).toContain('Back to tax certificate page');
      expect(response.text).toContain(`/clients/${testCompany.companyCode}/filing/bi-annual`);
    });

    test('should display IRP5 validation warnings when no finalized payrolls exist', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(200);

      expect(response.text).toContain('IRP5 Validation Warnings');
      expect(response.text).toContain('No finalized payrolls found for this period');
    });
  });

  describe('Authentication and Authorization', () => {
    test('should redirect to login when not authenticated', async () => {
      const response = await request(app)
        .get('/clients/TESTPV/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(302);

      expect(response.headers.location).toContain('/login');
    });

    test('should deny access to company not owned by user', async () => {
      // Create another company not owned by test user
      const otherCompany = await Company.create({
        name: 'Other Company',
        companyCode: 'OTHER',
        owner: new mongoose.Types.ObjectId()
      });

      const response = await request(app)
        .get('/clients/OTHER/filing/bi-annual/pre-validate?period=2025-Aug')
        .set('Cookie', authCookie)
        .expect(302);

      expect(response.headers.location).toBe('/dashboard');

      await Company.findByIdAndDelete(otherCompany._id);
    });
  });

  // Helper function to authenticate user
  async function authenticateUser(user) {
    const loginResponse = await request(app)
      .post('/auth/login')
      .send({
        email: user.email,
        password: 'testpassword'
      });

    return loginResponse.headers['set-cookie'];
  }
});
