const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Import services
const DeviceFingerprintService = require('../services/DeviceFingerprintService');
const TokenService = require('../services/TokenService');
const DeviceManagementService = require('../services/DeviceManagementService');
const SessionManagementService = require('../services/SessionManagementService');

// Import models
const User = require('../models/user');
const TrustedDevice = require('../models/TrustedDevice');
const RememberMeToken = require('../models/RememberMeToken');
const Session = require('../models/session');

describe('Remember Me System Tests', () => {
  let mongoServer;
  let testUser;
  let mockReq;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await TrustedDevice.deleteMany({});
    await RememberMeToken.deleteMany({});
    await Session.deleteMany({});

    // Create test user
    testUser = new User({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isVerified: true,
      role: new mongoose.Types.ObjectId(),
    });
    await testUser.save();

    // Mock request object
    mockReq = {
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'accept-language': 'en-US,en;q=0.9',
        'accept-encoding': 'gzip, deflate, br',
      },
      ip: '*************',
      connection: { remoteAddress: '*************' },
    };
  });

  describe('DeviceFingerprintService', () => {
    test('should generate device fingerprint', () => {
      const clientFingerprint = {
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
      };

      const result = DeviceFingerprintService.generateFingerprint(mockReq, clientFingerprint);

      expect(result).toHaveProperty('fingerprint');
      expect(result).toHaveProperty('components');
      expect(result).toHaveProperty('deviceInfo');
      expect(result).toHaveProperty('securityScore');
      expect(typeof result.fingerprint).toBe('string');
      expect(result.fingerprint).toHaveLength(64); // SHA-256 hash
    });

    test('should extract device information', () => {
      const result = DeviceFingerprintService.generateFingerprint(mockReq);
      
      expect(result.deviceInfo).toHaveProperty('name');
      expect(result.deviceInfo).toHaveProperty('type');
      expect(result.deviceInfo).toHaveProperty('browser');
      expect(result.deviceInfo).toHaveProperty('os');
    });

    test('should calculate security score', () => {
      const result = DeviceFingerprintService.generateFingerprint(mockReq);
      
      expect(typeof result.securityScore).toBe('number');
      expect(result.securityScore).toBeGreaterThanOrEqual(0);
      expect(result.securityScore).toBeLessThanOrEqual(100);
    });
  });

  describe('DeviceManagementService', () => {
    test('should register new device', async () => {
      const result = await DeviceManagementService.registerDevice(
        testUser._id,
        mockReq,
        { screenResolution: '1920x1080' }
      );

      expect(result.success).toBe(true);
      expect(result.isNew).toBe(true);
      expect(result.device).toHaveProperty('deviceId');
      expect(result.device).toHaveProperty('deviceFingerprint');
    });

    test('should update existing device', async () => {
      // Register device first time
      const firstResult = await DeviceManagementService.registerDevice(
        testUser._id,
        mockReq
      );

      // Register same device again
      const secondResult = await DeviceManagementService.registerDevice(
        testUser._id,
        mockReq
      );

      expect(secondResult.success).toBe(true);
      expect(secondResult.isNew).toBe(false);
      expect(secondResult.device._id.toString()).toBe(firstResult.device._id.toString());
    });

    test('should get user devices', async () => {
      await DeviceManagementService.registerDevice(testUser._id, mockReq);
      
      const devices = await DeviceManagementService.getUserDevices(testUser._id);
      
      expect(Array.isArray(devices)).toBe(true);
      expect(devices).toHaveLength(1);
      expect(devices[0]).toHaveProperty('deviceId');
    });

    test('should revoke device', async () => {
      const registerResult = await DeviceManagementService.registerDevice(testUser._id, mockReq);
      
      const revokeResult = await DeviceManagementService.revokeDevice(
        registerResult.device._id,
        testUser._id,
        'Test revocation'
      );

      expect(revokeResult.success).toBe(true);
      
      // Check device is revoked
      const device = await TrustedDevice.findById(registerResult.device._id);
      expect(device.isActive).toBe(false);
      expect(device.revokedReason).toBe('Test revocation');
    });
  });

  describe('TokenService', () => {
    let testDevice;

    beforeEach(async () => {
      const deviceResult = await DeviceManagementService.registerDevice(testUser._id, mockReq);
      testDevice = deviceResult.device;
    });

    test('should create remember me token', async () => {
      const result = await TokenService.createRememberMeToken(
        testUser._id,
        testDevice._id,
        '*************',
        'test-user-agent'
      );

      expect(result.success).toBe(true);
      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('selector');
      expect(result).toHaveProperty('validator');
      expect(result).toHaveProperty('combinedToken');
      expect(result.combinedToken).toContain(':');
    });

    test('should validate remember me token', async () => {
      const createResult = await TokenService.createRememberMeToken(
        testUser._id,
        testDevice._id,
        '*************',
        'test-user-agent'
      );

      const validateResult = await TokenService.validateRememberMeToken(
        createResult.combinedToken,
        '*************',
        'test-user-agent'
      );

      expect(validateResult.valid).toBe(true);
      expect(validateResult).toHaveProperty('token');
      expect(validateResult).toHaveProperty('user');
      expect(validateResult).toHaveProperty('trustedDevice');
    });

    test('should reject invalid token', async () => {
      const validateResult = await TokenService.validateRememberMeToken(
        'invalid:token',
        '*************',
        'test-user-agent'
      );

      expect(validateResult.valid).toBe(false);
      expect(validateResult).toHaveProperty('reason');
    });

    test('should refresh token', async () => {
      const createResult = await TokenService.createRememberMeToken(
        testUser._id,
        testDevice._id,
        '*************',
        'test-user-agent'
      );

      const refreshResult = await TokenService.refreshRememberMeToken(createResult.token._id);

      expect(refreshResult.success).toBe(true);
      expect(refreshResult).toHaveProperty('validator');
      expect(refreshResult).toHaveProperty('combinedToken');
      expect(refreshResult.combinedToken).not.toBe(createResult.combinedToken);
    });

    test('should revoke user tokens', async () => {
      await TokenService.createRememberMeToken(
        testUser._id,
        testDevice._id,
        '*************',
        'test-user-agent'
      );

      const revokeResult = await TokenService.revokeAllUserTokens(
        testUser._id,
        testUser._id,
        'Test revocation'
      );

      expect(revokeResult.success).toBe(true);
      expect(revokeResult.revokedCount).toBe(1);
    });
  });

  describe('SessionManagementService', () => {
    test('should create session', async () => {
      const result = await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq,
        { name: 'Test Device', type: 'desktop' }
      );

      expect(result.success).toBe(true);
      expect(result).toHaveProperty('session');
      expect(result).toHaveProperty('expiresAt');
      expect(result).toHaveProperty('timeoutMinutes');
    });

    test('should update session activity', async () => {
      await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq
      );

      const updateResult = await SessionManagementService.updateSessionActivity(
        'test-session-id',
        mockReq
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult).toHaveProperty('session');
      expect(updateResult).toHaveProperty('timeUntilExpiry');
    });

    test('should check session timeout', async () => {
      await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq
      );

      const timeoutResult = await SessionManagementService.checkSessionTimeout('test-session-id');

      expect(timeoutResult.valid).toBe(true);
      expect(timeoutResult).toHaveProperty('timeUntilExpiry');
    });

    test('should invalidate session', async () => {
      await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq
      );

      const invalidateResult = await SessionManagementService.invalidateSession(
        'test-session-id',
        'Test invalidation'
      );

      expect(invalidateResult.success).toBe(true);

      // Check session is invalidated
      const session = await Session.findOne({ sessionId: 'test-session-id' });
      expect(session.isValid).toBe(false);
      expect(session.invalidationReason).toBe('Test invalidation');
    });

    test('should perform comprehensive logout', async () => {
      // Create session and token
      await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq
      );

      const deviceResult = await DeviceManagementService.registerDevice(testUser._id, mockReq);
      await TokenService.createRememberMeToken(
        testUser._id,
        deviceResult.device._id,
        '*************',
        'test-user-agent'
      );

      const logoutResult = await SessionManagementService.comprehensiveLogout(
        testUser._id,
        'test-session-id',
        true, // all devices
        'Test logout'
      );

      expect(logoutResult.success).toBe(true);
      expect(logoutResult.results).toHaveProperty('sessionInvalidated');
      expect(logoutResult.results).toHaveProperty('tokensRevoked');
    });
  });

  describe('Integration Tests', () => {
    test('should complete full remember me flow', async () => {
      // 1. Register device
      const deviceResult = await DeviceManagementService.registerDevice(testUser._id, mockReq);
      expect(deviceResult.success).toBe(true);

      // 2. Create remember me token
      const tokenResult = await TokenService.createRememberMeToken(
        testUser._id,
        deviceResult.device._id,
        '*************',
        'test-user-agent'
      );
      expect(tokenResult.success).toBe(true);

      // 3. Validate token
      const validateResult = await TokenService.validateRememberMeToken(
        tokenResult.combinedToken,
        '*************',
        'test-user-agent'
      );
      expect(validateResult.valid).toBe(true);

      // 4. Create session
      const sessionResult = await SessionManagementService.createSession(
        testUser._id,
        'test-session-id',
        mockReq
      );
      expect(sessionResult.success).toBe(true);

      // 5. Comprehensive logout
      const logoutResult = await SessionManagementService.comprehensiveLogout(
        testUser._id,
        'test-session-id',
        false,
        'Test logout'
      );
      expect(logoutResult.success).toBe(true);
    });

    test('should handle security scenarios', async () => {
      // Register device and create token
      const deviceResult = await DeviceManagementService.registerDevice(testUser._id, mockReq);
      const tokenResult = await TokenService.createRememberMeToken(
        testUser._id,
        deviceResult.device._id,
        '*************',
        'test-user-agent'
      );

      // Simulate token theft by using wrong validator
      const [selector] = tokenResult.combinedToken.split(':');
      const invalidToken = `${selector}:invalid-validator`;

      const validateResult = await TokenService.validateRememberMeToken(
        invalidToken,
        '*************',
        'test-user-agent'
      );

      expect(validateResult.valid).toBe(false);
      expect(validateResult.reason).toContain('security breach');

      // Check that token series was revoked
      const tokens = await TokenService.getUserActiveTokens(testUser._id);
      expect(tokens).toHaveLength(0);
    });
  });
});

module.exports = {
  // Export for potential use in other test files
  DeviceFingerprintService,
  TokenService,
  DeviceManagementService,
  SessionManagementService,
};
