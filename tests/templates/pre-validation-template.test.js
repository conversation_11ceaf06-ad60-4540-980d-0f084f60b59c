const ejs = require('ejs');
const path = require('path');
const fs = require('fs');

describe('Pre-validation Template Tests', () => {
  let templatePath;
  let mockData;

  beforeAll(() => {
    templatePath = path.join(__dirname, '../../views/pre-validation.ejs');
  });

  beforeEach(() => {
    // Setup mock data for template rendering
    mockData = {
      title: 'e@syFile Pre-Validation',
      company: {
        _id: '507f1f77bcf86cd799439011',
        name: 'Test Company Ltd',
        companyCode: 'TEST123'
      },
      employees: [
        {
          _id: '507f1f77bcf86cd799439012',
          firstName: '<PERSON>',
          lastName: 'Doe',
          companyEmployeeNumber: 'EMP001'
        },
        {
          _id: '507f1f77bcf86cd799439013',
          firstName: 'Jane',
          lastName: 'Smith',
          companyEmployeeNumber: 'EMP002'
        }
      ],
      warnings: {
        irp5: ['No finalized payrolls found for this period'],
        company: ['Company tax number is missing', 'UIF number is missing'],
        employees: {
          '507f1f77bcf86cd799439013': ['ID number is missing', 'Tax number is missing']
        }
      },
      season: new Date('2025-08-01'),
      user: {
        _id: '507f1f77bcf86cd799439014',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>'
      }
    };
  });

  describe('Template Rendering', () => {
    test('should render template without errors with complete data', async () => {
      expect(() => {
        const html = ejs.renderFile(templatePath, mockData);
        expect(html).toBeDefined();
      }).not.toThrow();
    });

    test('should render template with minimal data', async () => {
      const minimalData = {
        title: 'e@syFile Pre-Validation',
        company: {
          name: 'Test Company',
          companyCode: 'TEST'
        },
        employees: [],
        warnings: {
          irp5: [],
          company: [],
          employees: {}
        },
        season: new Date(),
        user: { firstName: 'Test', lastName: 'User' }
      };

      expect(() => {
        const html = ejs.renderFile(templatePath, minimalData);
        expect(html).toBeDefined();
      }).not.toThrow();
    });

    test('should include all required sections in rendered HTML', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      // Check for main sections
      expect(html).toContain('e@syFile Pre-Validation');
      expect(html).toContain('Test Company Ltd');
      expect(html).toContain('IRP5 Validation Warnings');
      expect(html).toContain('Company Validation Warnings');
      expect(html).toContain('Employee Validation Warnings');
      expect(html).toContain('Back to tax certificate page');
    });

    test('should display company warnings correctly', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('Company tax number is missing');
      expect(html).toContain('UIF number is missing');
    });

    test('should display employee warnings correctly', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('Employee Number: EMP002');
      expect(html).toContain('ID number is missing');
      expect(html).toContain('Tax number is missing');
    });

    test('should display IRP5 warnings correctly', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('No finalized payrolls found for this period');
    });

    test('should handle empty warnings gracefully', async () => {
      const dataWithNoWarnings = {
        ...mockData,
        warnings: {
          irp5: [],
          company: [],
          employees: {}
        }
      };

      const html = await ejs.renderFile(templatePath, dataWithNoWarnings);

      expect(html).toContain('IRP5 Validation Warnings');
      expect(html).toContain('Company Validation Warnings');
      expect(html).toContain('Employee Validation Warnings');
    });

    test('should include correct back link', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('/clients/TEST123/filing/bi-annual');
    });

    test('should format season date correctly', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('2025-08-01');
    });

    test('should handle missing employee numbers gracefully', async () => {
      const dataWithMissingEmployeeNumber = {
        ...mockData,
        employees: [
          {
            _id: '507f1f77bcf86cd799439015',
            firstName: 'No',
            lastName: 'Number',
            companyEmployeeNumber: null
          }
        ],
        warnings: {
          ...mockData.warnings,
          employees: {
            '507f1f77bcf86cd799439015': ['Employee number is missing']
          }
        }
      };

      const html = await ejs.renderFile(templatePath, dataWithMissingEmployeeNumber);

      expect(html).toContain('Employee Number: N/A');
    });

    test('should include all required CSS and JS files', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('/css/styles.css');
      expect(html).toContain('/css/filing.css');
      expect(html).toContain('/js/filing.js');
      expect(html).toContain('https://unpkg.com/@phosphor-icons/web');
    });

    test('should be valid HTML structure', async () => {
      const html = await ejs.renderFile(templatePath, mockData);

      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html lang="en">');
      expect(html).toContain('<head>');
      expect(html).toContain('<body>');
      expect(html).toContain('</html>');
    });
  });

  describe('Template Error Handling', () => {
    test('should handle missing company data', async () => {
      const dataWithMissingCompany = {
        ...mockData,
        company: null
      };

      // This should throw an error or handle gracefully
      try {
        await ejs.renderFile(templatePath, dataWithMissingCompany);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should handle missing user data', async () => {
      const dataWithMissingUser = {
        ...mockData,
        user: null
      };

      try {
        await ejs.renderFile(templatePath, dataWithMissingUser);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });
});
