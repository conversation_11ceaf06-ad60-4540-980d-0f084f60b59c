const express = require('express');
const request = require('supertest');
const path = require('path');

describe('Route Analysis and Diagnostics', () => {
  test('should analyze filing.js route structure', () => {
    // Import the filing router
    const filingRouter = require('../../routes/filing');
    
    expect(filingRouter).toBeDefined();
    expect(typeof filingRouter).toBe('function');
    
    // Check if it's an Express router
    expect(filingRouter.stack).toBeDefined();
    
    console.log('📊 Filing Router Analysis:');
    console.log(`Total routes: ${filingRouter.stack.length}`);
    
    // Analyze each route
    const preValidateRoutes = [];
    filingRouter.stack.forEach((layer, index) => {
      if (layer.route) {
        const path = layer.route.path;
        const methods = Object.keys(layer.route.methods);
        
        console.log(`Route ${index + 1}: ${methods.join(', ').toUpperCase()} ${path}`);
        
        if (path.includes('pre-validate')) {
          preValidateRoutes.push({ path, methods, index });
        }
      }
    });
    
    console.log(`\n🎯 Pre-validate routes found: ${preValidateRoutes.length}`);
    preValidateRoutes.forEach(route => {
      console.log(`  - ${route.methods.join(', ').toUpperCase()} ${route.path} (position: ${route.index + 1})`);
    });
    
    expect(preValidateRoutes.length).toBeGreaterThan(0);
  });

  test('should test pre-validation route with mock data', async () => {
    const app = express();
    
    // Mock authentication
    app.use((req, res, next) => {
      req.user = { 
        _id: 'test-user-id',
        companies: ['test-company-id']
      };
      req.isAuthenticated = () => true;
      next();
    });
    
    // Mock database models
    const mockCompany = {
      _id: 'test-company-id',
      companyCode: 'TEST123',
      name: 'Test Company'
    };
    
    // Mock the models before importing the router
    jest.doMock('../../models/Company', () => ({
      findOne: jest.fn().mockResolvedValue(mockCompany)
    }));
    
    jest.doMock('../../models/Employee', () => ({
      find: jest.fn().mockResolvedValue([])
    }));
    
    jest.doMock('../../models/Payroll', () => ({
      find: jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue([])
      })
    }));
    
    jest.doMock('../../models/Filing', () => ({
      findOne: jest.fn().mockResolvedValue(null)
    }));
    
    // Mock EJS rendering
    app.set('view engine', 'ejs');
    app.set('views', path.join(__dirname, '../../views'));
    
    // Import and mount the filing router
    const filingRouter = require('../../routes/filing');
    app.use('/clients', filingRouter);
    
    console.log('\n🧪 Testing pre-validation route...');
    
    const response = await request(app)
      .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
      .expect(res => {
        console.log(`Response status: ${res.status}`);
        console.log(`Response headers:`, res.headers);
        if (res.status === 302) {
          console.log(`Redirect location: ${res.headers.location}`);
        }
      });
    
    // The response should either be 200 (success) or 302 (redirect)
    expect([200, 302]).toContain(response.status);
  });

  test('should check template file existence and basic syntax', () => {
    const fs = require('fs');
    const templatePath = path.join(__dirname, '../../views/pre-validation.ejs');
    
    console.log('\n📄 Template Analysis:');
    console.log(`Template path: ${templatePath}`);
    
    const exists = fs.existsSync(templatePath);
    console.log(`Template exists: ${exists}`);
    expect(exists).toBe(true);
    
    if (exists) {
      const content = fs.readFileSync(templatePath, 'utf8');
      console.log(`Template size: ${content.length} characters`);
      
      // Check for basic EJS syntax
      const ejsTags = content.match(/<%[^%]*%>/g);
      console.log(`EJS tags found: ${ejsTags ? ejsTags.length : 0}`);
      
      // Check for required variables
      const requiredVars = ['company', 'employees', 'warnings', 'season'];
      requiredVars.forEach(varName => {
        const found = content.includes(varName);
        console.log(`Variable '${varName}': ${found ? '✅' : '❌'}`);
      });
      
      expect(ejsTags).toBeTruthy();
      expect(ejsTags.length).toBeGreaterThan(0);
    }
  });

  test('should verify app.js mounting configuration', () => {
    const fs = require('fs');
    const appPath = path.join(__dirname, '../../app.js');
    
    console.log('\n🔧 App.js Mounting Analysis:');
    
    const exists = fs.existsSync(appPath);
    expect(exists).toBe(true);
    
    const content = fs.readFileSync(appPath, 'utf8');
    
    // Check for filing router mounting
    const filingMounts = content.match(/app\.use\([^)]*filing[^)]*\)/gi);
    console.log(`Filing router mounts found: ${filingMounts ? filingMounts.length : 0}`);
    
    if (filingMounts) {
      filingMounts.forEach((mount, index) => {
        console.log(`  ${index + 1}. ${mount}`);
      });
    }
    
    // Check for /clients mounting
    const clientsMounts = content.match(/app\.use\([^)]*['"]\/clients['"][^)]*\)/gi);
    console.log(`/clients mounts found: ${clientsMounts ? clientsMounts.length : 0}`);
    
    if (clientsMounts) {
      clientsMounts.forEach((mount, index) => {
        console.log(`  ${index + 1}. ${mount}`);
      });
    }
    
    expect(filingMounts || clientsMounts).toBeTruthy();
  });
});
