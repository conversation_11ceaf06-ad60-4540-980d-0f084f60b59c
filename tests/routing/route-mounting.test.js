const express = require('express');
const request = require('supertest');
const filingRouter = require('../../routes/filing');

describe('Route Mounting and URL Pattern Tests', () => {
  let app;

  beforeEach(() => {
    app = express();
    
    // Mock authentication middleware
    const mockAuth = (req, res, next) => {
      req.user = { _id: 'test-user-id' };
      req.isAuthenticated = () => true;
      next();
    };

    // Mount the filing router exactly as in the main app
    app.use('/clients', mockAuth, filingRouter);
  });

  describe('Route Pattern Matching', () => {
    test('should match pre-validation route pattern correctly', () => {
      const router = express.Router();
      
      // Test the exact pattern used in filing.js
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({
          matched: true,
          params: req.params,
          query: req.query
        });
      });

      const testApp = express();
      testApp.use('/clients', router);

      return request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(200)
        .then(response => {
          expect(response.body.matched).toBe(true);
          expect(response.body.params.companyCode).toBe('TEST123');
          expect(response.body.query.period).toBe('2025-Aug');
        });
    });

    test('should not match incorrect URL patterns', async () => {
      const router = express.Router();
      
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ matched: true });
      });

      const testApp = express();
      testApp.use('/clients', router);

      // Test incorrect patterns that should not match
      const incorrectUrls = [
        '/clients/TEST123/filing/pre-validate',
        '/clients/TEST123/bi-annual/pre-validate',
        '/clients/filing/bi-annual/pre-validate',
        '/TEST123/filing/bi-annual/pre-validate'
      ];

      for (const url of incorrectUrls) {
        await request(testApp)
          .get(url)
          .expect(404);
      }
    });

    test('should extract company code parameter correctly', async () => {
      const router = express.Router();
      
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ companyCode: req.params.companyCode });
      });

      const testApp = express();
      testApp.use('/clients', router);

      const testCases = [
        { url: '/clients/ABC123/filing/bi-annual/pre-validate', expected: 'ABC123' },
        { url: '/clients/XYZ789/filing/bi-annual/pre-validate', expected: 'XYZ789' },
        { url: '/clients/TEST-COMPANY/filing/bi-annual/pre-validate', expected: 'TEST-COMPANY' }
      ];

      for (const testCase of testCases) {
        const response = await request(testApp)
          .get(testCase.url)
          .expect(200);
        
        expect(response.body.companyCode).toBe(testCase.expected);
      }
    });

    test('should handle query parameters correctly', async () => {
      const router = express.Router();
      
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ query: req.query });
      });

      const testApp = express();
      testApp.use('/clients', router);

      const response = await request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug&debug=true')
        .expect(200);

      expect(response.body.query.period).toBe('2025-Aug');
      expect(response.body.query.debug).toBe('true');
    });
  });

  describe('Route Registration Order', () => {
    test('should prioritize specific routes over general patterns', () => {
      const router = express.Router();
      
      // Add routes in order they appear in filing.js
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ route: 'pre-validate' });
      });
      
      router.get('/:companyCode/filing/bi-annual/employees', (req, res) => {
        res.json({ route: 'employees' });
      });
      
      router.get('/:companyCode/filing/bi-annual/:period', (req, res) => {
        res.json({ route: 'period', period: req.params.period });
      });

      const testApp = express();
      testApp.use('/clients', router);

      return Promise.all([
        request(testApp)
          .get('/clients/TEST123/filing/bi-annual/pre-validate')
          .expect(200)
          .then(response => {
            expect(response.body.route).toBe('pre-validate');
          }),
        
        request(testApp)
          .get('/clients/TEST123/filing/bi-annual/employees')
          .expect(200)
          .then(response => {
            expect(response.body.route).toBe('employees');
          })
      ]);
    });
  });

  describe('Middleware Chain', () => {
    test('should apply authentication middleware correctly', async () => {
      let authCalled = false;
      
      const authMiddleware = (req, res, next) => {
        authCalled = true;
        req.user = { id: 'test-user' };
        next();
      };

      const router = express.Router();
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ authenticated: !!req.user });
      });

      const testApp = express();
      testApp.use('/clients', authMiddleware, router);

      const response = await request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(200);

      expect(authCalled).toBe(true);
      expect(response.body.authenticated).toBe(true);
    });

    test('should handle middleware errors correctly', async () => {
      const errorMiddleware = (req, res, next) => {
        next(new Error('Authentication failed'));
      };

      const router = express.Router();
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ success: true });
      });

      const testApp = express();
      testApp.use('/clients', errorMiddleware, router);
      
      // Add error handler
      testApp.use((err, req, res, next) => {
        res.status(500).json({ error: err.message });
      });

      const response = await request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(500);

      expect(response.body.error).toBe('Authentication failed');
    });
  });

  describe('HTTP Methods', () => {
    test('should only respond to GET requests', async () => {
      const router = express.Router();
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ method: 'GET' });
      });

      const testApp = express();
      testApp.use('/clients', router);

      // GET should work
      await request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(200);

      // Other methods should not work
      await request(testApp)
        .post('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(404);

      await request(testApp)
        .put('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(404);

      await request(testApp)
        .delete('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(404);
    });
  });

  describe('URL Encoding and Special Characters', () => {
    test('should handle URL encoded company codes', async () => {
      const router = express.Router();
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ companyCode: req.params.companyCode });
      });

      const testApp = express();
      testApp.use('/clients', router);

      const response = await request(testApp)
        .get('/clients/TEST%20123/filing/bi-annual/pre-validate')
        .expect(200);

      expect(response.body.companyCode).toBe('TEST 123');
    });

    test('should handle special characters in query parameters', async () => {
      const router = express.Router();
      router.get('/:companyCode/filing/bi-annual/pre-validate', (req, res) => {
        res.json({ query: req.query });
      });

      const testApp = express();
      testApp.use('/clients', router);

      const response = await request(testApp)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug&filter=name%3DJohn')
        .expect(200);

      expect(response.body.query.period).toBe('2025-Aug');
      expect(response.body.query.filter).toBe('name=John');
    });
  });
});
