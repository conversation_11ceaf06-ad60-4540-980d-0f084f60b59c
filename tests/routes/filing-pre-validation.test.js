const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const passport = require('passport');
const flash = require('connect-flash');

// Import models
const Company = require('../../models/Company');
const Employee = require('../../models/Employee');
const Payroll = require('../../models/Payroll');
const Filing = require('../../models/Filing');

// Import the filing router
const filingRouter = require('../../routes/filing');

// Mock authentication middleware
const mockAuth = (req, res, next) => {
  req.user = {
    _id: new mongoose.Types.ObjectId(),
    email: '<EMAIL>',
    companies: [new mongoose.Types.ObjectId()]
  };
  req.isAuthenticated = () => true;
  next();
};

describe('Pre-validation Route Tests', () => {
  let app;
  let mockCompany;
  let mockEmployees;
  let mockPayrolls;

  beforeAll(async () => {
    // Setup test app
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Setup session
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false
    }));
    
    app.use(passport.initialize());
    app.use(passport.session());
    app.use(flash());
    
    // Mock template engine
    app.set('view engine', 'ejs');
    app.set('views', './views');
    
    // Use mock auth instead of real auth
    app.use('/clients', mockAuth, filingRouter);
  });

  beforeEach(() => {
    // Setup mock data
    mockCompany = {
      _id: new mongoose.Types.ObjectId(),
      companyCode: 'TEST123',
      name: 'Test Company',
      taxNumber: '1234567890',
      uifNumber: 'U123456',
      payelNumber: 'P123456'
    };

    mockEmployees = [
      {
        _id: new mongoose.Types.ObjectId(),
        firstName: 'John',
        lastName: 'Doe',
        companyEmployeeNumber: 'EMP001',
        company: mockCompany._id,
        idNumber: '8001015009087',
        taxNumber: '1234567890'
      },
      {
        _id: new mongoose.Types.ObjectId(),
        firstName: 'Jane',
        lastName: 'Smith',
        companyEmployeeNumber: 'EMP002',
        company: mockCompany._id,
        idNumber: null, // Missing ID number for testing
        taxNumber: null // Missing tax number for testing
      }
    ];

    mockPayrolls = [
      {
        _id: new mongoose.Types.ObjectId(),
        employee: mockEmployees[0]._id,
        company: mockCompany._id,
        month: new Date('2025-08-01'),
        finalised: true
      }
    ];

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('GET /:companyCode/filing/bi-annual/pre-validate', () => {
    test('should return 400 when period parameter is missing', async () => {
      // Mock Company.findOne to return our test company
      Company.findOne = jest.fn().mockResolvedValue(mockCompany);

      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate')
        .expect(302); // Redirect

      expect(response.headers.location).toBe('/clients/TEST123/filing/bi-annual');
    });

    test('should return 404 when company is not found', async () => {
      // Mock Company.findOne to return null
      Company.findOne = jest.fn().mockResolvedValue(null);

      const response = await request(app)
        .get('/clients/INVALID/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(302); // Redirect to dashboard

      expect(response.headers.location).toBe('/dashboard');
    });

    test('should render pre-validation page with valid data', async () => {
      // Mock database calls
      Company.findOne = jest.fn().mockResolvedValue(mockCompany);
      Employee.find = jest.fn().mockResolvedValue(mockEmployees);
      Payroll.find = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPayrolls)
      });
      Filing.findOne = jest.fn().mockResolvedValue(null); // No existing submission

      // Mock res.render to capture template data
      const mockRender = jest.fn();
      
      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(200);

      // Verify database calls were made
      expect(Company.findOne).toHaveBeenCalledWith({ companyCode: 'TEST123' });
      expect(Employee.find).toHaveBeenCalledWith({ company: mockCompany._id });
    });

    test('should redirect when existing submission found', async () => {
      // Mock existing submission
      const existingSubmission = {
        _id: new mongoose.Types.ObjectId(),
        submittedAt: new Date()
      };

      Company.findOne = jest.fn().mockResolvedValue(mockCompany);
      Employee.find = jest.fn().mockResolvedValue(mockEmployees);
      Payroll.find = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPayrolls)
      });
      Filing.findOne = jest.fn().mockResolvedValue(existingSubmission);

      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(302);

      expect(response.headers.location).toBe('/clients/TEST123/filing/bi-annual');
    });

    test('should handle different period formats correctly', async () => {
      Company.findOne = jest.fn().mockResolvedValue(mockCompany);
      Employee.find = jest.fn().mockResolvedValue(mockEmployees);
      Payroll.find = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPayrolls)
      });
      Filing.findOne = jest.fn().mockResolvedValue(null);

      // Test February period
      await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Feb')
        .expect(200);

      // Test August period
      await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(200);
    });

    test('should generate validation warnings for missing company data', async () => {
      // Company with missing data
      const incompleteCompany = {
        ...mockCompany,
        taxNumber: null,
        uifNumber: null,
        payelNumber: null
      };

      Company.findOne = jest.fn().mockResolvedValue(incompleteCompany);
      Employee.find = jest.fn().mockResolvedValue(mockEmployees);
      Payroll.find = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPayrolls)
      });
      Filing.findOne = jest.fn().mockResolvedValue(null);

      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(200);

      // The response should contain validation warnings
      // This would be verified by checking the rendered template data
    });

    test('should generate validation warnings for employees with missing data', async () => {
      Company.findOne = jest.fn().mockResolvedValue(mockCompany);
      Employee.find = jest.fn().mockResolvedValue(mockEmployees); // Contains employee with missing data
      Payroll.find = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPayrolls)
      });
      Filing.findOne = jest.fn().mockResolvedValue(null);

      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(200);

      // Should generate warnings for Jane Smith who has missing ID and tax numbers
    });

    test('should handle database errors gracefully', async () => {
      Company.findOne = jest.fn().mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .get('/clients/TEST123/filing/bi-annual/pre-validate?period=2025-Aug')
        .expect(302);

      expect(response.headers.location).toBe('/clients/TEST123/filing/bi-annual');
    });
  });
});
