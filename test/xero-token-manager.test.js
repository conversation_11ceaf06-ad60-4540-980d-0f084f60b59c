const XeroTokenManager = require('../services/xeroTokenManager');

/**
 * Simple test script for XeroTokenManager
 * Run with: node test/xero-token-manager.test.js
 */

function createMockIntegration(tokenData = {}) {
  const credentials = new Map();
  
  // Set default values
  if (tokenData.accessToken) credentials.set('accessToken', tokenData.accessToken);
  if (tokenData.refreshToken) credentials.set('refreshToken', tokenData.refreshToken);
  if (tokenData.expiresAt) credentials.set('expiresAt', tokenData.expiresAt);
  if (tokenData.tenantId) credentials.set('tenantId', tokenData.tenantId);
  
  return {
    _id: 'test-integration-id',
    credentials,
    status: 'active'
  };
}

function runTests() {
  console.log('🧪 Running XeroTokenManager Tests...\n');
  
  const tokenManager = new XeroTokenManager();
  let testsPassed = 0;
  let testsTotal = 0;
  
  function test(name, testFn) {
    testsTotal++;
    try {
      testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  // Test 1: Token field standardization
  test('Token field standardization', () => {
    const integration = createMockIntegration({
      accessToken: 'test-access-token',
      refreshToken: 'test-refresh-token',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes from now
      tenantId: 'test-tenant-id'
    });
    
    const tokenData = tokenManager.getTokenData(integration);
    
    if (!tokenData.accessToken || tokenData.accessToken !== 'test-access-token') {
      throw new Error('Access token not retrieved correctly');
    }
    if (!tokenData.refreshToken || tokenData.refreshToken !== 'test-refresh-token') {
      throw new Error('Refresh token not retrieved correctly');
    }
    if (!tokenData.tenantId || tokenData.tenantId !== 'test-tenant-id') {
      throw new Error('Tenant ID not retrieved correctly');
    }
    if (!tokenData.expiresAt || !(tokenData.expiresAt instanceof Date)) {
      throw new Error('Expires at not retrieved correctly');
    }
  });
  
  // Test 2: Backward compatibility with old field names
  test('Backward compatibility with old field names', () => {
    const integration = {
      _id: 'test-integration-id',
      credentials: new Map([
        ['access_token', 'old-access-token'],
        ['refresh_token', 'old-refresh-token'],
        ['tokenExpiry', new Date(Date.now() + 30 * 60 * 1000).toISOString()]
      ]),
      status: 'active'
    };
    
    const tokenData = tokenManager.getTokenData(integration);
    
    if (tokenData.accessToken !== 'old-access-token') {
      throw new Error('Old access token field not handled correctly');
    }
    if (tokenData.refreshToken !== 'old-refresh-token') {
      throw new Error('Old refresh token field not handled correctly');
    }
  });
  
  // Test 3: Token expiration logic
  test('Token expiration logic - needs refresh', () => {
    const integration = createMockIntegration({
      accessToken: 'test-token',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes from now
    });
    
    const needsRefresh = tokenManager.needsRefresh(integration);
    if (!needsRefresh) {
      throw new Error('Should need refresh when token expires in 5 minutes');
    }
  });
  
  // Test 4: Token expiration logic - no refresh needed
  test('Token expiration logic - no refresh needed', () => {
    const integration = createMockIntegration({
      accessToken: 'test-token',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes from now
    });
    
    const needsRefresh = tokenManager.needsRefresh(integration);
    if (needsRefresh) {
      throw new Error('Should not need refresh when token expires in 30 minutes');
    }
  });
  
  // Test 5: Time until expiry calculation
  test('Time until expiry calculation', () => {
    const expiryTime = Date.now() + 15 * 60 * 1000; // 15 minutes from now
    const integration = createMockIntegration({
      accessToken: 'test-token',
      expiresAt: new Date(expiryTime).toISOString()
    });
    
    const timeUntilExpiry = tokenManager.getTimeUntilExpiry(integration);
    const expectedTime = Math.floor((expiryTime - Date.now()) / 1000);
    
    // Allow for small timing differences (within 5 seconds)
    if (Math.abs(timeUntilExpiry - expectedTime) > 5) {
      throw new Error(`Time until expiry calculation incorrect: got ${timeUntilExpiry}, expected ~${expectedTime}`);
    }
  });
  
  // Test 6: Xero token set conversion
  test('Xero token set conversion', () => {
    const tokenData = {
      accessToken: 'test-access',
      refreshToken: 'test-refresh',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000),
      tenantId: 'test-tenant',
      idToken: 'test-id-token',
      tokenType: 'Bearer',
      scope: 'offline_access openid'
    };
    
    const xeroTokenSet = tokenManager.toXeroTokenSet(tokenData);
    
    if (xeroTokenSet.access_token !== 'test-access') {
      throw new Error('Access token conversion failed');
    }
    if (xeroTokenSet.refresh_token !== 'test-refresh') {
      throw new Error('Refresh token conversion failed');
    }
    if (!xeroTokenSet.expires_at || typeof xeroTokenSet.expires_at !== 'number') {
      throw new Error('Expires at conversion failed');
    }
  });
  
  // Test 7: Mutex functionality
  test('Mutex functionality', async () => {
    const companyId = 'test-company-123';
    
    // Should be able to acquire mutex initially
    const acquired1 = await tokenManager.acquireRefreshMutex(companyId);
    if (!acquired1) {
      throw new Error('Should be able to acquire mutex initially');
    }
    
    // Should not be able to acquire mutex again
    const acquired2 = await tokenManager.acquireRefreshMutex(companyId);
    if (acquired2) {
      throw new Error('Should not be able to acquire mutex when already held');
    }
    
    // Should be able to check if refresh is in progress
    const inProgress = tokenManager.isRefreshInProgress(companyId);
    if (!inProgress) {
      throw new Error('Should report refresh in progress');
    }
    
    // Release mutex
    tokenManager.releaseRefreshMutex(companyId);
    
    // Should be able to acquire again after release
    const acquired3 = await tokenManager.acquireRefreshMutex(companyId);
    if (!acquired3) {
      throw new Error('Should be able to acquire mutex after release');
    }
    
    tokenManager.releaseRefreshMutex(companyId);
  });
  
  // Summary
  console.log(`\n📊 Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All tests passed! Token manager is working correctly.');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
