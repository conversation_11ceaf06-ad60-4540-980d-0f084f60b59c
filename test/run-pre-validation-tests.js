#!/usr/bin/env node

/**
 * Comprehensive test runner for pre-validation functionality
 * This script runs all tests and provides detailed debugging information
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Pre-validation Test Suite');
console.log('=====================================\n');

// Test configuration
const testConfig = {
  timeout: 30000,
  verbose: true,
  detectOpenHandles: true,
  forceExit: true
};

// Test files to run
const testFiles = [
  'test/routing/route-mounting.test.js',
  'test/templates/pre-validation-template.test.js',
  'test/routes/filing-pre-validation.test.js',
  'test/integration/pre-validation-flow.test.js'
];

async function runTests() {
  console.log('📋 Test Plan:');
  console.log('1. Route mounting and URL pattern tests');
  console.log('2. Template rendering tests');
  console.log('3. Unit tests for pre-validation route');
  console.log('4. Integration tests for complete flow\n');

  let allTestsPassed = true;
  const results = [];

  for (const testFile of testFiles) {
    console.log(`\n🧪 Running: ${testFile}`);
    console.log('─'.repeat(50));

    try {
      const result = await runSingleTest(testFile);
      results.push({ file: testFile, ...result });
      
      if (!result.success) {
        allTestsPassed = false;
      }
    } catch (error) {
      console.error(`❌ Failed to run ${testFile}:`, error.message);
      allTestsPassed = false;
      results.push({ file: testFile, success: false, error: error.message });
    }
  }

  // Print summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${path.basename(result.file)}`);
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  console.log(`\n🎯 Overall Result: ${allTestsPassed ? 'PASSED' : 'FAILED'}`);
  
  if (!allTestsPassed) {
    console.log('\n🔧 Debugging Information:');
    await runDiagnostics();
  }

  return allTestsPassed;
}

function runSingleTest(testFile) {
  return new Promise((resolve) => {
    const jest = spawn('npx', ['jest', testFile, '--verbose'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let stdout = '';
    let stderr = '';

    jest.stdout.on('data', (data) => {
      stdout += data.toString();
      process.stdout.write(data);
    });

    jest.stderr.on('data', (data) => {
      stderr += data.toString();
      process.stderr.write(data);
    });

    jest.on('close', (code) => {
      resolve({
        success: code === 0,
        stdout,
        stderr,
        exitCode: code
      });
    });

    jest.on('error', (error) => {
      resolve({
        success: false,
        error: error.message,
        stdout,
        stderr
      });
    });
  });
}

async function runDiagnostics() {
  console.log('\n🔍 Running Diagnostics...');
  
  // Check if required files exist
  console.log('\n📁 File Existence Check:');
  const requiredFiles = [
    'routes/filing.js',
    'views/pre-validation.ejs',
    'models/Company.js',
    'models/Employee.js',
    'models/Payroll.js',
    'models/Filing.js'
  ];

  requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`${exists ? '✅' : '❌'} ${file}`);
  });

  // Check route patterns in filing.js
  console.log('\n🛣️  Route Pattern Analysis:');
  try {
    const filingContent = fs.readFileSync('routes/filing.js', 'utf8');
    
    const preValidateRoutes = filingContent.match(/router\.get\([^)]*pre-validate[^)]*\)/g);
    console.log(`Found ${preValidateRoutes ? preValidateRoutes.length : 0} pre-validate routes:`);
    
    if (preValidateRoutes) {
      preValidateRoutes.forEach((route, index) => {
        console.log(`  ${index + 1}. ${route}`);
      });
    }

    // Check for duplicate routes
    const duplicateCheck = filingContent.match(/\/clients.*pre-validate/g);
    if (duplicateCheck && duplicateCheck.length > 1) {
      console.log('⚠️  Warning: Found potential duplicate route patterns');
    }

  } catch (error) {
    console.log('❌ Could not analyze filing.js:', error.message);
  }

  // Check app.js mounting
  console.log('\n🔧 App Mounting Analysis:');
  try {
    const appContent = fs.readFileSync('app.js', 'utf8');
    
    const filingMounts = appContent.match(/app\.use\([^)]*filing[^)]*\)/gi);
    console.log(`Found ${filingMounts ? filingMounts.length : 0} filing router mounts:`);
    
    if (filingMounts) {
      filingMounts.forEach((mount, index) => {
        console.log(`  ${index + 1}. ${mount}`);
      });
    }

  } catch (error) {
    console.log('❌ Could not analyze app.js:', error.message);
  }

  // Check template syntax
  console.log('\n📄 Template Syntax Check:');
  try {
    const templateContent = fs.readFileSync('views/pre-validation.ejs', 'utf8');
    
    // Basic EJS syntax validation
    const ejsTags = templateContent.match(/<%[^%]*%>/g);
    console.log(`Found ${ejsTags ? ejsTags.length : 0} EJS tags in template`);
    
    // Check for required variables
    const requiredVars = ['company', 'employees', 'warnings', 'season', 'user'];
    requiredVars.forEach(varName => {
      const found = templateContent.includes(varName);
      console.log(`${found ? '✅' : '❌'} Variable '${varName}' referenced in template`);
    });

  } catch (error) {
    console.log('❌ Could not analyze template:', error.message);
  }
}

// Run manual route test
async function testRouteManually() {
  console.log('\n🧪 Manual Route Test');
  console.log('====================');
  
  try {
    // Import and test the route directly
    const express = require('express');
    const filingRouter = require('../routes/filing');
    
    const app = express();
    
    // Mock auth middleware
    app.use((req, res, next) => {
      req.user = { _id: 'test-user' };
      req.isAuthenticated = () => true;
      next();
    });
    
    app.use('/clients', filingRouter);
    
    console.log('✅ Route imported successfully');
    console.log('✅ Express app created');
    console.log('✅ Middleware configured');
    
    // Test route registration
    const routes = [];
    app._router.stack.forEach(layer => {
      if (layer.route) {
        routes.push({
          path: layer.route.path,
          methods: Object.keys(layer.route.methods)
        });
      }
    });
    
    console.log(`✅ Found ${routes.length} registered routes`);
    
  } catch (error) {
    console.log('❌ Manual route test failed:', error.message);
  }
}

// Main execution
if (require.main === module) {
  runTests()
    .then(success => {
      if (!success) {
        console.log('\n🔧 Running additional diagnostics...');
        return testRouteManually();
      }
    })
    .then(() => {
      console.log('\n✨ Test suite completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { runTests, runDiagnostics, testRouteManually };
