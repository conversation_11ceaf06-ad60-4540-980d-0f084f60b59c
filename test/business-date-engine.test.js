/**
 * Comprehensive tests for BusinessDate and SouthAfricaPayrollEngine
 * These tests validate the timezone-independent business logic
 */

const BusinessDate = require('../utils/BusinessDate');
const SouthAfricaPayrollEngine = require('../utils/SouthAfricaPayrollEngine');

// Test data based on production issue
const testEmployee = {
  _id: 'test-employee-id',
  doa: '2025-05-01',
  payFrequency: {
    frequency: 'monthly',
    lastDayOfPeriod: 'monthend'
  }
};

const testEmployeeSpecificDay = {
  _id: 'test-employee-specific-day',
  doa: '2025-05-01',
  payFrequency: {
    frequency: 'monthly',
    lastDayOfPeriod: '25'
  }
};

// Simple test functions without Jest dependencies
function testBusinessDateUtility() {
  console.log('🧪 Testing BusinessDate Utility...');

  // Test date validation
  console.assert(BusinessDate.isValid('2025-05-01') === true, 'Should validate correct date format');
  console.assert(BusinessDate.isValid('25-05-2025') === false, 'Should reject invalid date format');
  console.assert(BusinessDate.isValid('2025-13-01') === false, 'Should reject invalid month');

  // Test date arithmetic
  console.assert(BusinessDate.addDays('2025-05-01', 30) === '2025-05-31', 'Should add days correctly');
  console.assert(BusinessDate.endOfMonth('2025-05-01') === '2025-05-31', 'Should calculate end of month');
  console.assert(BusinessDate.daysBetween('2025-05-01', '2025-05-31') === 31, 'Should calculate days between');

  // Test date comparisons
  console.assert(BusinessDate.isBefore('2025-05-01', '2025-05-02') === true, 'Should compare dates correctly');
  console.assert(BusinessDate.isAfter('2025-05-02', '2025-05-01') === true, 'Should compare dates correctly');

  console.log('✅ BusinessDate utility tests passed');
}

function testMonthlyMonthendPeriods() {
  console.log('🧪 Testing Monthly Monthend Periods...');

  const result = SouthAfricaPayrollEngine.calculateEmployeeFirstPeriod(
    testEmployee.doa,
    testEmployee.payFrequency
  );

  console.assert(result.startDate === '2025-05-01', 'Start date should be 2025-05-01');
  console.assert(result.endDate === '2025-05-31', 'End date should be 2025-05-31');
  console.assert(result.duration === 31, 'Duration should be 31 days');
  console.assert(result.isPartial === true, 'Should be marked as partial');

  console.log('✅ Monthly monthend period tests passed');
}

function testMultiplePeriodGeneration() {
  console.log('🧪 Testing Multiple Period Generation...');

  const periods = SouthAfricaPayrollEngine.generateAllPeriods(
    testEmployee,
    '2025-08-31' // Generate 3 months
  );

  console.assert(periods.length === 3, 'Should generate 3 periods');

  // First period: May 1-31
  console.assert(periods[0].startDate === '2025-05-01', 'First period start should be 2025-05-01');
  console.assert(periods[0].endDate === '2025-05-31', 'First period end should be 2025-05-31');
  console.assert(periods[0].duration === 31, 'First period should be 31 days');

  // Second period: June 1-30
  console.assert(periods[1].startDate === '2025-06-01', 'Second period start should be 2025-06-01');
  console.assert(periods[1].endDate === '2025-06-30', 'Second period end should be 2025-06-30');
  console.assert(periods[1].duration === 30, 'Second period should be 30 days');

  // Third period: July 1-31
  console.assert(periods[2].startDate === '2025-07-01', 'Third period start should be 2025-07-01');
  console.assert(periods[2].endDate === '2025-07-31', 'Third period end should be 2025-07-31');
  console.assert(periods[2].duration === 31, 'Third period should be 31 days');

  console.log('✅ Multiple period generation tests passed');
}

function testSpecificDayPeriods() {
  console.log('🧪 Testing Monthly Specific Day Periods...');

  const result = SouthAfricaPayrollEngine.calculateEmployeeFirstPeriod(
    testEmployeeSpecificDay.doa, // 2025-05-01
    testEmployeeSpecificDay.payFrequency // 25th
  );

  console.assert(result.startDate === '2025-05-01', 'Start date should be 2025-05-01');
  console.assert(result.endDate === '2025-05-25', 'End date should be 2025-05-25');
  console.assert(result.duration === 25, 'Duration should be 25 days');

  console.log('✅ Specific day period tests passed');
}

function testProductionIssueValidation() {
  console.log('🧪 Testing Production Issue Fix...');

  // This test validates the exact scenario from production logs
  const productionEmployee = {
    _id: 'production-employee',
    doa: '2025-05-01',
    payFrequency: {
      frequency: 'monthly',
      lastDayOfPeriod: 'monthend'
    }
  };

  const result = SouthAfricaPayrollEngine.calculateEmployeeFirstPeriod(
    productionEmployee.doa,
    productionEmployee.payFrequency
  );

  // Should NOT be a 1-day period
  console.assert(result.duration > 1, 'Should not be a 1-day period');
  console.assert(result.duration >= 28, 'Should be at least 28 days');
  console.assert(result.duration <= 31, 'Should be at most 31 days');

  // Should be proper month-long period
  console.assert(result.startDate === '2025-05-01', 'Should start on DOA');
  console.assert(result.endDate === '2025-05-31', 'Should end on month end');
  console.assert(result.duration === 31, 'Should be exactly 31 days');

  console.log('✅ Production issue validation tests passed');
}

function testTimezoneConsistency() {
  console.log('🧪 Testing Timezone Consistency...');

  // Test the same calculation multiple times to ensure consistency
  const results = [];

  for (let i = 0; i < 5; i++) {
    const result = SouthAfricaPayrollEngine.calculateEmployeeFirstPeriod(
      '2025-05-01',
      { frequency: 'monthly', lastDayOfPeriod: 'monthend' }
    );
    results.push(result);
  }

  // All results should be identical
  const firstResult = results[0];
  results.forEach((result, index) => {
    console.assert(result.startDate === firstResult.startDate, `Result ${index} start date should match`);
    console.assert(result.endDate === firstResult.endDate, `Result ${index} end date should match`);
    console.assert(result.duration === firstResult.duration, `Result ${index} duration should match`);
  });

  console.log('✅ Timezone consistency tests passed');
}

// Run the tests
console.log('🧪 Running Business Date Engine Tests...\n');

// Comprehensive test runner
function runTests() {
  let passed = 0;
  let failed = 0;

  const tests = [
    testBusinessDateUtility,
    testMonthlyMonthendPeriods,
    testMultiplePeriodGeneration,
    testSpecificDayPeriods,
    testProductionIssueValidation,
    testTimezoneConsistency
  ];

  tests.forEach((test, index) => {
    try {
      test();
      passed++;
    } catch (error) {
      console.log(`❌ Test ${test.name} failed:`, error.message);
      failed++;
    }
  });

  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);

  if (failed === 0) {
    console.log('🎉 All tests passed! The South Africa Business Date strategy is working correctly.');
    console.log('✅ The production issue with 1-day periods should be resolved.');
  } else {
    console.log('❌ Some tests failed. Please review the implementation.');
  }

  return failed === 0;
}

if (require.main === module) {
  runTests();
}

module.exports = { runTests };
