const XeroTokenManager = require('../services/xeroTokenManager');
const XeroErrorHandler = require('../services/xeroErrorHandler');
const xeroLogger = require('../services/xeroLogger');

/**
 * Integration test for Xero token management fixes
 * Tests the complete flow of token management, error handling, and logging
 * Run with: node test/xero-integration.test.js
 */

function createMockIntegration(tokenData = {}) {
  const credentials = new Map();
  
  // Set token data with various field name formats for testing
  if (tokenData.accessToken) credentials.set('accessToken', tokenData.accessToken);
  if (tokenData.refreshToken) credentials.set('refreshToken', tokenData.refreshToken);
  if (tokenData.expiresAt) credentials.set('expiresAt', tokenData.expiresAt);
  if (tokenData.tenantId) credentials.set('tenantId', tokenData.tenantId);
  
  // Add some old format fields for backward compatibility testing
  if (tokenData.access_token) credentials.set('access_token', tokenData.access_token);
  if (tokenData.tokenExpiry) credentials.set('tokenExpiry', tokenData.tokenExpiry);
  
  return {
    _id: 'test-integration-id',
    credentials,
    status: 'active',
    save: async function() {
      console.log('Mock integration saved');
      return this;
    }
  };
}

function createMockError(type, message, statusCode = null) {
  const error = new Error(message);
  if (statusCode) {
    error.response = { status: statusCode };
  }
  return error;
}

async function runIntegrationTests() {
  console.log('🧪 Running Xero Integration Tests...\n');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  function test(name, testFn) {
    testsTotal++;
    try {
      testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  async function asyncTest(name, testFn) {
    testsTotal++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  const tokenManager = new XeroTokenManager();
  const errorHandler = new XeroErrorHandler();
  
  // Test 1: Token field standardization with backward compatibility
  test('Token field standardization with backward compatibility', () => {
    const integration = createMockIntegration({
      access_token: 'old-format-token',
      tokenExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString()
    });
    
    const tokenData = tokenManager.getTokenData(integration);
    
    if (tokenData.accessToken !== 'old-format-token') {
      throw new Error('Backward compatibility failed for access_token');
    }
    
    if (!tokenData.expiresAt || !(tokenData.expiresAt instanceof Date)) {
      throw new Error('Backward compatibility failed for tokenExpiry');
    }
  });
  
  // Test 2: Unified token expiration logic
  test('Unified token expiration logic - 10 minute threshold', () => {
    // Token expiring in 5 minutes - should need refresh
    const integration1 = createMockIntegration({
      accessToken: 'test-token',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString()
    });
    
    if (!tokenManager.needsRefresh(integration1)) {
      throw new Error('Should need refresh when token expires in 5 minutes');
    }
    
    // Token expiring in 15 minutes - should not need refresh
    const integration2 = createMockIntegration({
      accessToken: 'test-token',
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString()
    });
    
    if (tokenManager.needsRefresh(integration2)) {
      throw new Error('Should not need refresh when token expires in 15 minutes');
    }
  });
  
  // Test 3: Mutex functionality prevents race conditions
  await asyncTest('Mutex functionality prevents race conditions', async () => {
    const companyId = 'test-company-race';
    
    // Acquire mutex
    const acquired1 = await tokenManager.acquireRefreshMutex(companyId);
    if (!acquired1) {
      throw new Error('Should be able to acquire mutex initially');
    }
    
    // Try to acquire again - should fail
    const acquired2 = await tokenManager.acquireRefreshMutex(companyId);
    if (acquired2) {
      throw new Error('Should not be able to acquire mutex when already held');
    }
    
    // Check if refresh is in progress
    if (!tokenManager.isRefreshInProgress(companyId)) {
      throw new Error('Should report refresh in progress');
    }
    
    // Release and try again
    tokenManager.releaseRefreshMutex(companyId);
    const acquired3 = await tokenManager.acquireRefreshMutex(companyId);
    if (!acquired3) {
      throw new Error('Should be able to acquire mutex after release');
    }
    
    tokenManager.releaseRefreshMutex(companyId);
  });
  
  // Test 4: Enhanced error handling
  test('Enhanced error handling - invalid_grant error', () => {
    const error = createMockError('invalid_grant', 'The provided authorization grant is invalid');
    const errorInfo = errorHandler.analyzeError(error, { operation: 'token_refresh' });
    
    if (errorInfo.type !== 'invalid_grant') {
      throw new Error('Should identify invalid_grant error type');
    }
    
    if (!errorInfo.needsReauthentication) {
      throw new Error('Should require reauthentication for invalid_grant');
    }
    
    if (!errorInfo.userMessage.includes('reconnect')) {
      throw new Error('Should provide user-friendly reconnect message');
    }
  });
  
  // Test 5: Enhanced error handling - rate limit error
  test('Enhanced error handling - rate limit error', () => {
    const error = createMockError('rate_limit', 'Rate limit exceeded', 429);
    const errorInfo = errorHandler.analyzeError(error, { operation: 'api_call' });
    
    if (errorInfo.type !== 'rate_limit') {
      throw new Error('Should identify rate_limit error type');
    }
    
    if (!errorInfo.isRetryable) {
      throw new Error('Rate limit errors should be retryable');
    }
    
    if (errorInfo.needsReauthentication) {
      throw new Error('Rate limit errors should not require reauthentication');
    }
  });
  
  // Test 6: Error response creation
  test('Structured error response creation', () => {
    const error = createMockError('unauthorized', 'Unauthorized access', 401);
    const errorInfo = errorHandler.analyzeError(error);
    const response = errorHandler.createErrorResponse(errorInfo);
    
    if (!response.error || !response.error.type) {
      throw new Error('Should create structured error response');
    }
    
    if (response.success !== false) {
      throw new Error('Error response should have success: false');
    }
    
    if (!response.error.message) {
      throw new Error('Error response should have user message');
    }
  });
  
  // Test 7: Token conversion between formats
  test('Token conversion between Xero SDK and standardized formats', () => {
    const standardizedToken = {
      accessToken: 'test-access',
      refreshToken: 'test-refresh',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000),
      tenantId: 'test-tenant',
      idToken: 'test-id',
      tokenType: 'Bearer',
      scope: 'offline_access openid'
    };
    
    // Convert to Xero format
    const xeroTokenSet = tokenManager.toXeroTokenSet(standardizedToken);
    
    if (xeroTokenSet.access_token !== 'test-access') {
      throw new Error('Access token conversion to Xero format failed');
    }
    
    if (!xeroTokenSet.expires_at || typeof xeroTokenSet.expires_at !== 'number') {
      throw new Error('Expires at conversion to Xero format failed');
    }
    
    // Convert back to standardized format
    const backConverted = tokenManager.fromXeroTokenSet(xeroTokenSet);
    
    if (backConverted.accessToken !== standardizedToken.accessToken) {
      throw new Error('Round-trip conversion failed for access token');
    }
    
    if (Math.abs(backConverted.expiresAt.getTime() - standardizedToken.expiresAt.getTime()) > 1000) {
      throw new Error('Round-trip conversion failed for expires at');
    }
  });
  
  // Test 8: Logging functionality
  test('Comprehensive logging functionality', () => {
    const correlationId = xeroLogger.generateCorrelationId();
    
    if (!correlationId || !correlationId.startsWith('xero_')) {
      throw new Error('Should generate valid correlation ID');
    }
    
    // Test correlation ID management
    xeroLogger.setCorrelationId('test-request', correlationId);
    const retrieved = xeroLogger.getCorrelationId('test-request');
    
    if (retrieved !== correlationId) {
      throw new Error('Should store and retrieve correlation ID');
    }
    
    // Test log entry creation
    const logEntry = xeroLogger.createLogEntry('INFO', 'test_operation', 'Test message', {
      requestId: 'test-request',
      companyId: 'test-company'
    });
    
    if (!logEntry.timestamp || !logEntry.correlationId) {
      throw new Error('Should create structured log entry with correlation ID');
    }
  });
  
  // Test 9: Integration deactivation logic
  test('Integration deactivation logic', () => {
    const invalidGrantError = createMockError('invalid_grant', 'Invalid grant');
    const invalidGrantInfo = errorHandler.analyzeError(invalidGrantError);
    
    if (!errorHandler.shouldDeactivateIntegration(invalidGrantInfo)) {
      throw new Error('Should deactivate integration for invalid_grant error');
    }
    
    const networkError = createMockError('network_error', 'Network timeout');
    const networkErrorInfo = errorHandler.analyzeError(networkError);
    
    if (errorHandler.shouldDeactivateIntegration(networkErrorInfo)) {
      throw new Error('Should not deactivate integration for network error');
    }
  });
  
  // Test 10: Performance timing
  test('Performance timing functionality', () => {
    const timer = xeroLogger.startTimer('test_operation', { companyId: 'test' });
    
    if (typeof timer !== 'function') {
      throw new Error('Should return timer function');
    }
    
    // Simulate some work
    setTimeout(() => {
      const duration = timer();
      if (typeof duration !== 'number' || duration < 0) {
        throw new Error('Should return valid duration');
      }
    }, 10);
  });
  
  // Summary
  console.log(`\n📊 Integration Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All integration tests passed! The Xero fixes are working correctly.');
    console.log('\n✨ Key improvements implemented:');
    console.log('   • Standardized token field names with backward compatibility');
    console.log('   • Unified 10-minute token refresh threshold');
    console.log('   • Mutex-based race condition prevention');
    console.log('   • Enhanced error handling with structured responses');
    console.log('   • Comprehensive logging with correlation IDs');
    console.log('   • Graceful degradation and recovery mechanisms');
    return true;
  } else {
    console.log('❌ Some integration tests failed. Please check the implementation.');
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { runIntegrationTests };
