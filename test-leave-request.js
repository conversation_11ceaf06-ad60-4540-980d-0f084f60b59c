// Test script to verify complete leave request flow through WhatsApp
require('dotenv').config();

// Set minimal required environment variables
process.env.WHATSAPP_PHONE_NUMBER_ID = process.env.WHATSAPP_PHONE_NUMBER_ID || 'test-phone-id';
process.env.WHATSAPP_ACCESS_TOKEN = process.env.WHATSAPP_ACCESS_TOKEN || 'test-token';
process.env.WHATSAPP_VERIFY_TOKEN = process.env.WHATSAPP_VERIFY_TOKEN || 'test-verify-token';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret';
process.env.SESSION_SECRET = process.env.SESSION_SECRET || 'test-session-secret';
process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/panda_test';

const axios = require('axios');
const mongoose = require('mongoose');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3001';
const WEBHOOK_URL = `${BASE_URL}/api/whatsapp/webhook`;
const TEST_PHONE = '27687841950';

async function setupTestData() {
  console.log('Setting up test data...');
  
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Import models
    const Company = require('./models/Company');
    const Employee = require('./models/Employee');
    const LeaveType = require('./models/LeaveType');

    // Create test company
    const testCompany = await Company.findOneAndUpdate(
      { companyCode: 'TEST' },
      {
        name: 'Test Company',
        companyCode: 'TEST',
        owner: new mongoose.Types.ObjectId(),
        status: 'active'
      },
      { upsert: true, new: true }
    );
    console.log('Test company created/updated:', testCompany._id);

    // Create test leave type
    const testLeaveType = await LeaveType.findOneAndUpdate(
      { name: 'Annual', company: testCompany._id },
      {
        name: 'Annual',
        company: testCompany._id,
        category: 'annual',
        description: 'Annual leave',
        maxDaysPerYear: 21,
        carryOverDays: 5,
        isActive: true
      },
      { upsert: true, new: true }
    );
    console.log('Test leave type created/updated:', testLeaveType._id);

    // Create test employee
    const testEmployee = await Employee.findOneAndUpdate(
      { 'personalDetails.mobileNumber': TEST_PHONE },
      {
        company: testCompany._id,
        firstName: 'Test',
        lastName: 'Employee',
        personalDetails: {
          email: '<EMAIL>',
          mobileNumber: TEST_PHONE,
          idNumber: '1234567890123'
        },
        employmentDetails: {
          employeeNumber: 'EMP001',
          startDate: new Date(),
          status: 'active'
        }
      },
      { upsert: true, new: true }
    );
    console.log('Test employee created/updated:', testEmployee._id);

    return {
      company: testCompany,
      employee: testEmployee,
      leaveType: testLeaveType
    };
  } catch (error) {
    console.error('Error setting up test data:', error);
    throw error;
  }
}

async function testLeaveRequestFlow() {
  console.log('\n=== Testing Leave Request Flow ===');

  try {
    // Test 1: Send "hi" message to trigger welcome menu
    console.log('\n--- Test 1: Welcome message ---');
    const welcomePayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: 'test-phone-id'
            },
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-1',
              timestamp: Date.now().toString(),
              text: {
                body: 'hi'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const welcomeResponse = await axios.post(WEBHOOK_URL, welcomePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Welcome message sent successfully');
    console.log('Status:', welcomeResponse.status);

    // Test 2: Send "3" to select leave application
    console.log('\n--- Test 2: Select leave application ---');
    const selectLeavePayload = {
      ...welcomePayload,
      entry: [{
        ...welcomePayload.entry[0],
        changes: [{
          ...welcomePayload.entry[0].changes[0],
          value: {
            ...welcomePayload.entry[0].changes[0].value,
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-2',
              timestamp: Date.now().toString(),
              text: {
                body: '3'
              },
              type: 'text'
            }]
          }
        }]
      }]
    };

    const selectResponse = await axios.post(WEBHOOK_URL, selectLeavePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Leave selection sent successfully');
    console.log('Status:', selectResponse.status);

    // Test 3: Send legacy leave request format
    console.log('\n--- Test 3: Legacy leave request ---');
    const leaveRequestPayload = {
      ...welcomePayload,
      entry: [{
        ...welcomePayload.entry[0],
        changes: [{
          ...welcomePayload.entry[0].changes[0],
          value: {
            ...welcomePayload.entry[0].changes[0].value,
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-3',
              timestamp: Date.now().toString(),
              text: {
                body: 'Leave request: Annual from 2025-07-01 to 2025-07-05 reason: Family vacation'
              },
              type: 'text'
            }]
          }
        }]
      }]
    };

    const leaveResponse = await axios.post(WEBHOOK_URL, leaveRequestPayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Leave request sent successfully');
    console.log('Status:', leaveResponse.status);

    console.log('\n=== Leave Request Flow Test Completed Successfully ===');
    return true;

  } catch (error) {
    console.error('❌ Leave request flow test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    }
    return false;
  }
}

async function cleanup() {
  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('Error closing MongoDB connection:', error);
  }
}

// Main test function
async function runLeaveRequestTest() {
  try {
    console.log('=== WhatsApp Leave Request Flow Test ===');
    
    // Setup test data
    const testData = await setupTestData();
    console.log('Test data setup completed');

    // Run leave request flow test
    const success = await testLeaveRequestFlow();
    
    if (success) {
      console.log('\n🎉 All tests passed! WhatsApp leave request functionality is working.');
    } else {
      console.log('\n❌ Some tests failed. Please check the logs above.');
    }

    return success;
  } catch (error) {
    console.error('Test setup failed:', error);
    return false;
  } finally {
    await cleanup();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runLeaveRequestTest()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runLeaveRequestTest };
