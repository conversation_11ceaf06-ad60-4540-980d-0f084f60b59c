const moment = require("moment-timezone");
const payrollPeriodEngine = require("./utils/payrollPeriodEngine");

/**
 * Test script to validate the Business Rule Engine for payroll periods
 * This ensures periods follow strict pay frequency rules regardless of timezone
 */

console.log("🧪 BUSINESS RULE ENGINE VALIDATION TEST");
console.log("========================================\n");

// Test scenarios based on different pay frequencies and DOA dates
const testScenarios = [
  {
    name: "Monthly Monthend - DOA Mid-Month",
    employeeDOA: "2024-01-15",
    payFrequency: { frequency: "monthly", lastDayOfPeriod: "monthend" },
    expectedFirstPeriod: "2024-01-31"
  },
  {
    name: "Monthly 25th - DOA Before 25th",
    employeeDOA: "2024-01-10",
    payFrequency: { frequency: "monthly", lastDayOfPeriod: "25" },
    expectedFirstPeriod: "2024-01-25"
  },
  {
    name: "Monthly 25th - DOA After 25th",
    employeeDOA: "2024-01-28",
    payFrequency: { frequency: "monthly", lastDayOfPeriod: "25" },
    expectedFirstPeriod: "2024-02-25"
  },
  {
    name: "Weekly Friday - DOA Monday",
    employeeDOA: "2024-01-15", // Monday
    payFrequency: { frequency: "weekly", lastDayOfPeriod: "friday" },
    expectedFirstPeriod: "2024-01-19" // Friday
  },
  {
    name: "Weekly Friday - DOA Friday",
    employeeDOA: "2024-01-19", // Friday
    payFrequency: { frequency: "weekly", lastDayOfPeriod: "friday" },
    expectedFirstPeriod: "2024-01-19" // Same Friday
  }
];

console.log("📋 Running Test Scenarios:");
console.log("==========================\n");

testScenarios.forEach((scenario, index) => {
  console.log(`🔍 Test ${index + 1}: ${scenario.name}`);
  console.log(`   DOA: ${scenario.employeeDOA}`);
  console.log(`   Pay Frequency: ${scenario.payFrequency.frequency} ${scenario.payFrequency.lastDayOfPeriod}`);
  
  try {
    // Calculate first period using business rule engine
    const calculatedFirstPeriod = payrollPeriodEngine.calculateFirstPeriodEndDate(
      scenario.employeeDOA,
      scenario.payFrequency
    );
    
    const calculatedDate = moment.utc(calculatedFirstPeriod).format('YYYY-MM-DD');
    const expectedDate = scenario.expectedFirstPeriod;
    
    console.log(`   Expected: ${expectedDate}`);
    console.log(`   Calculated: ${calculatedDate}`);
    
    if (calculatedDate === expectedDate) {
      console.log(`   ✅ PASS - Dates match`);
    } else {
      console.log(`   ❌ FAIL - Dates don't match`);
    }
    
    // Validate the period follows business rules
    const validation = payrollPeriodEngine.validatePeriod(
      { endDate: calculatedFirstPeriod },
      scenario.payFrequency,
      scenario.employeeDOA
    );
    
    if (validation.isValid) {
      console.log(`   ✅ PASS - Period follows business rules`);
    } else {
      console.log(`   ❌ FAIL - ${validation.error}`);
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR - ${error.message}`);
  }
  
  console.log("");
});

// Test period generation
console.log("📊 Testing Period Generation:");
console.log("=============================\n");

const testEmployee = {
  doa: "2024-01-15",
  payFrequency: { frequency: "monthly", lastDayOfPeriod: "monthend" }
};

console.log(`Employee DOA: ${testEmployee.doa}`);
console.log(`Pay Frequency: ${testEmployee.payFrequency.frequency} ${testEmployee.payFrequency.lastDayOfPeriod}`);

try {
  const firstPeriodEnd = payrollPeriodEngine.calculateFirstPeriodEndDate(
    testEmployee.doa,
    testEmployee.payFrequency
  );
  
  const generatedPeriods = payrollPeriodEngine.generatePayrollPeriods(
    firstPeriodEnd,
    testEmployee.payFrequency,
    new Date("2024-06-30") // Generate up to June 2024
  );
  
  console.log(`\n📅 Generated ${generatedPeriods.length} periods:`);
  
  generatedPeriods.forEach((period, index) => {
    const startDate = moment.utc(period.startDate).format('YYYY-MM-DD');
    const endDate = moment.utc(period.endDate).format('YYYY-MM-DD');
    
    console.log(`   Period ${index + 1}: ${startDate} to ${endDate}`);
    
    // Validate each period
    const validation = payrollPeriodEngine.validatePeriod(
      period,
      testEmployee.payFrequency,
      testEmployee.doa
    );
    
    if (!validation.isValid) {
      console.log(`     ❌ VALIDATION ERROR: ${validation.error}`);
    }
  });
  
} catch (error) {
  console.log(`❌ ERROR in period generation: ${error.message}`);
}

// Test timezone consistency
console.log("\n🌍 Testing Timezone Consistency:");
console.log("=================================\n");

const testDate = "2024-01-31";
const payFreq = { frequency: "monthly", lastDayOfPeriod: "monthend" };

console.log(`Test Date: ${testDate}`);
console.log(`Pay Frequency: ${payFreq.frequency} ${payFreq.lastDayOfPeriod}`);

// Test with different timezone interpretations
const utcResult = payrollPeriodEngine.calculateFirstPeriodEndDate(
  moment.utc(testDate).toDate(),
  payFreq
);

const localResult = payrollPeriodEngine.calculateFirstPeriodEndDate(
  new Date(testDate),
  payFreq
);

const utcFormatted = moment.utc(utcResult).format('YYYY-MM-DD HH:mm:ss Z');
const localFormatted = moment.utc(localResult).format('YYYY-MM-DD HH:mm:ss Z');

console.log(`UTC interpretation result: ${utcFormatted}`);
console.log(`Local interpretation result: ${localFormatted}`);

if (utcFormatted === localFormatted) {
  console.log(`✅ PASS - Timezone-consistent results`);
} else {
  console.log(`❌ FAIL - Timezone-dependent results detected`);
}

console.log("\n🎯 BUSINESS RULE ENGINE VALIDATION COMPLETE");
console.log("=============================================");
console.log("\nThe Business Rule Engine ensures that payroll periods");
console.log("follow strict pay frequency rules based on employee DOA,");
console.log("regardless of server timezone or environment differences.");
console.log("\nThis should resolve the +1 day discrepancy issue by");
console.log("providing deterministic period calculation that works");
console.log("consistently in both development and production.");
