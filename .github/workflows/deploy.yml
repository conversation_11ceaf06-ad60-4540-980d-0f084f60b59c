name: Deploy to EC2

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Create deployment package
        run: |
          echo "Creating deployment package..."
          zip -r deployment.zip . -x "node_modules/*" ".git/*"

      - name: Deploy to EC2
        env:
          PRIVATE_KEY: ${{ secrets.EC2_SSH_KEY }}
          EC2_HOST: ${{ secrets.EC2_HOST }}
        run: |
          # Create .ssh directory in the runner's home
          mkdir -p $HOME/.ssh
          
          # Write key file to .ssh directory
          echo "$PRIVATE_KEY" > $HOME/.ssh/deploy_key.pem
          chmod 600 $HOME/.ssh/deploy_key.pem
          
          # Transfer files to EC2
          echo "Transferring files to EC2..."
          scp -o StrictHostKeyChecking=no -i $HOME/.ssh/deploy_key.pem deployment.zip ubuntu@$EC2_HOST:/home/<USER>/
          
          # Run initial setup (Node.js, PM2)
          echo "Setting up environment..."
          ssh -o StrictHostKeyChecking=no -i $HOME/.ssh/deploy_key.pem ubuntu@$EC2_HOST '
            echo "Checking Node.js installation..."
            if ! command -v node &> /dev/null; then
              curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
              sudo apt-get install -y nodejs
            fi
          
            echo "Checking PM2 installation..."
            if ! command -v pm2 &> /dev/null; then
              sudo npm install -g pm2
            fi
          
            echo "Ensuring application directory exists..."
            sudo mkdir -p /var/www/app
            sudo chown ubuntu:ubuntu /var/www/app
          '
          
          # Run deployment using nohup to keep it running even if SSH disconnects
          echo "Deploying application..."
          ssh -o StrictHostKeyChecking=no -i $HOME/.ssh/deploy_key.pem ubuntu@$EC2_HOST '
            cd /home/<USER>
            echo "Extracting deployment package..."
            unzip -o deployment.zip -d temp_extract
            
            # Create deployment script
            cat > deploy.sh << "EOL"
            #!/bin/bash
            set -e
            
            # Copy files to the correct app directory
            echo "Copying files to application directory..."
            sudo cp -R /home/<USER>/temp_extract/* /var/www/app/
            sudo chown -R ubuntu:ubuntu /var/www/app
            
            # Install dependencies
            cd /var/www/app
            echo "Installing dependencies..."
            npm install
            
            # Ensure the application restarts properly
            echo "Restarting application..."
            
            # Check if the app is running in PM2
            if pm2 list | grep -q "pandapayroll"; then
              echo "Application exists in PM2, restarting..."
              pm2 reload pandapayroll || pm2 restart pandapayroll
            else
              echo "Application not found in PM2, starting fresh..."
              pm2 start npm --name pandapayroll -- start
            fi
            
            # Save PM2 configuration to ensure it survives server restart
            echo "Saving PM2 configuration..."
            pm2 save
            
            # Set up PM2 to start on boot if not already configured
            if ! pm2 startup | grep -q "already configured"; then
              echo "Setting up PM2 to start on boot..."
              pm2 startup | grep -v "sudo" | sudo bash
              pm2 save
            fi
            
            # Clean up
            echo "Cleaning up temporary files..."
            rm -rf /home/<USER>/temp_extract
            rm /home/<USER>/deployment.zip
            
            echo "Verifying application status..."
            pm2 status
            
            echo "Deployment completed at $(date)" > /home/<USER>/deployment_complete.log
            echo "Application logs can be viewed with: pm2 logs pandapayroll" >> /home/<USER>/deployment_complete.log
            EOL
            
            chmod +x deploy.sh
            
            # Run deployment script in background
            nohup ./deploy.sh > deployment.log 2>&1 &
            
            # Wait a moment to see if it starts
            sleep 5
            echo "Deployment process started in background. Check deployment.log for progress."
          '
          
          echo "Deployment initiated. The process will continue on the server even after this workflow completes."
