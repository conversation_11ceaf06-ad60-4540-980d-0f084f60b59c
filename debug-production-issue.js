const moment = require("moment-timezone");

/**
 * Production Issue Debugger
 * Based on Coolify logs analysis
 */

console.log("🔍 PRODUCTION ISSUE ANALYSIS");
console.log("============================\n");

// Data from Coolify logs
const productionData = {
  employeeDOA: "2025-05-01T00:00:00.000Z",
  currentPeriodEndDate: "2025-06-01T21:59:59.999Z",
  payFrequency: {
    frequency: 'monthly',
    lastDayOfPeriod: 'monthend',
    firstPayrollPeriodEndDate: "2025-03-30T00:00:00.000Z"
  },
  currentDate: "2025-07-31T21:59:59.999Z"
};

console.log("📊 Production Data Analysis:");
console.log("DOA:", productionData.employeeDOA);
console.log("Current Period End:", productionData.currentPeriodEndDate);
console.log("Pay Frequency:", productionData.payFrequency.frequency, productionData.payFrequency.lastDayOfPeriod);
console.log("First Payroll Period End:", productionData.payFrequency.firstPayrollPeriodEndDate);
console.log("");

// Issue 1: Period Duration Analysis
const periodStart = moment.utc("2025-06-01T00:00:00.000Z");
const periodEnd = moment.utc(productionData.currentPeriodEndDate);
const periodDuration = periodEnd.diff(periodStart, 'days') + 1;

console.log("🚨 ISSUE 1: Period Duration Problem");
console.log("Period Start:", periodStart.format('YYYY-MM-DD'));
console.log("Period End:", periodEnd.format('YYYY-MM-DD'));
console.log("Period Duration:", periodDuration, "days");
console.log("Expected for Monthly Monthend: ~30 days");
console.log("❌ This is a 1-day period, not a monthly period!\n");

// Issue 2: DOA vs Period Timeline
const doa = moment.utc(productionData.employeeDOA);
const monthAfterDOA = doa.clone().add(1, 'month').endOf('month');

console.log("🚨 ISSUE 2: DOA vs Period Timeline");
console.log("Employee DOA:", doa.format('YYYY-MM-DD'));
console.log("Expected First Period End (DOA month end):", doa.clone().endOf('month').format('YYYY-MM-DD'));
console.log("Expected Second Period End (next month end):", monthAfterDOA.format('YYYY-MM-DD'));
console.log("Actual Period End:", periodEnd.format('YYYY-MM-DD'));
console.log("❌ Period doesn't follow monthly monthend rules!\n");

// Issue 3: firstPayrollPeriodEndDate Analysis
const firstPayrollEnd = moment.utc(productionData.payFrequency.firstPayrollPeriodEndDate);

console.log("🚨 ISSUE 3: firstPayrollPeriodEndDate Problem");
console.log("Configured First Payroll End:", firstPayrollEnd.format('YYYY-MM-DD'));
console.log("Employee DOA:", doa.format('YYYY-MM-DD'));
console.log("DOA is AFTER configured first payroll end date!");
console.log("❌ This is causing period calculation to break!\n");

// Solution Analysis
console.log("💡 SOLUTION ANALYSIS:");
console.log("===================");

console.log("1. The firstPayrollPeriodEndDate (2025-03-30) is BEFORE employee DOA (2025-05-01)");
console.log("2. This causes the period generation logic to create incorrect periods");
console.log("3. For Monthly Monthend with DOA 2025-05-01, periods should be:");
console.log("   - First Period: 2025-05-01 to 2025-05-31");
console.log("   - Second Period: 2025-06-01 to 2025-06-30");
console.log("   - Third Period: 2025-07-01 to 2025-07-31");
console.log("");

// Correct calculation
console.log("✅ CORRECT CALCULATION:");
console.log("======================");

function calculateCorrectPeriods(employeeDOA, payFrequency) {
  const doa = moment.utc(employeeDOA);
  const periods = [];
  
  // For monthly monthend, first period ends at end of DOA month
  let currentPeriodEnd = doa.clone().endOf('month');
  
  // Generate 6 months of periods
  for (let i = 0; i < 6; i++) {
    const periodStart = i === 0 ? doa.clone() : moment.utc(periods[i-1].endDate).add(1, 'day');

    periods.push({
      periodNumber: i + 1,
      startDate: periodStart.format('YYYY-MM-DD'),
      endDate: currentPeriodEnd.format('YYYY-MM-DD'),
      duration: currentPeriodEnd.diff(periodStart, 'days') + 1
    });

    // Next period ends at end of next month
    currentPeriodEnd = currentPeriodEnd.clone().add(1, 'month').endOf('month');
  }
  
  return periods;
}

const correctPeriods = calculateCorrectPeriods(productionData.employeeDOA, productionData.payFrequency);

correctPeriods.forEach(period => {
  console.log(`Period ${period.periodNumber}: ${period.startDate} to ${period.endDate} (${period.duration} days)`);
});

console.log("");

// Current date analysis
const today = moment.utc();
console.log("📅 CURRENT DATE ANALYSIS:");
console.log("========================");
console.log("Today:", today.format('YYYY-MM-DD'));
console.log("Current period should be:", correctPeriods.find(p => 
  today.isBetween(moment.utc(p.startDate), moment.utc(p.endDate), null, '[]')
)?.periodNumber || "Not found");

console.log("");

console.log("🔧 FIXES NEEDED:");
console.log("================");
console.log("1. Fix PayrollPeriod generation to ignore firstPayrollPeriodEndDate when DOA is after it");
console.log("2. Ensure monthly monthend periods always end on last day of month");
console.log("3. Fix period start/end date calculation to create proper monthly periods");
console.log("4. Add validation to prevent 1-day periods for monthly frequency");
console.log("5. Update current period detection logic to find the correct period for today's date");

console.log("");
console.log("🎯 IMMEDIATE ACTION REQUIRED:");
console.log("============================");
console.log("The issue is in the PayrollPeriod generation logic, not timezone conversion.");
console.log("The system is creating 1-day periods instead of proper monthly periods.");
console.log("This explains why basic salary is 0 and why dates appear wrong.");
