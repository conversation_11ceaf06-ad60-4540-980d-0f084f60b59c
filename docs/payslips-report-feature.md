# Payslips Report Feature Documentation

## Overview

The Payslips Report feature provides comprehensive payslip data extraction and export capabilities for the PandaPayroll system. This feature allows users to generate detailed reports of employee payslips with advanced filtering options and multiple export formats.

## Features Implemented

### 1. Core Functionality
- **MongoDB Aggregation**: Advanced aggregation pipeline joining PayrollPeriod, Employee, Company, and PayFrequency models
- **Multi-format Export**: Support for Excel (.xlsx), PDF, and CSV formats
- **Date Range Filtering**: Flexible date range selection (current month, previous month, current year, last 3/6 months, custom range)
- **Employee Filtering**: Option to filter by specific employees or include all employees
- **Status Filtering**: Automatic filtering for active employees (Active, Inactive, Serving Notice)

### 2. Excel Export Features
- **PandaPayroll Design System**: Consistent styling with Inter font, primary color (#6366F1), and proper formatting
- **Company Branding**: Company trading name in cell A1, physical and postal addresses
- **Professional Layout**: Headers at row 6, frozen panes, auto-filtering, alternating row colors
- **Currency Formatting**: Proper South African Rand (R) formatting for all monetary values
- **Comprehensive Data**: 16 columns including employee details, pay period info, and all deduction breakdowns

### 3. Data Structure
The report includes the following fields:
- Employee Number, Name, ID Number
- Department, Cost Centre
- Pay Period Start/End Dates
- Pay Frequency
- Basic Salary, Gross Pay
- PAYE, UIF, SDL deductions
- Total Deductions, Net Pay
- Payment Method

### 4. Integration Points
- **Reporting Interface**: Integrated into existing reporting.ejs with proper form handling
- **Email Automation**: Compatible with ReportEmailService for scheduled generation and distribution
- **Route Handling**: Added to routes/reporting.js with proper error handling
- **Frontend JavaScript**: Enhanced reporting.js with employee selection for payslips

## Technical Implementation

### Database Aggregation Pipeline
```javascript
PayrollPeriod.aggregate([
  // Match finalized payroll periods with date/employee filters
  { $match: { company: companyId, isFinalized: true, ...filters } },
  
  // Join with employees collection
  { $lookup: { from: "employees", localField: "employee", foreignField: "_id", as: "employeeData" } },
  { $unwind: "$employeeData" },
  
  // Filter employee status
  { $match: { "employeeData.status": { $in: ["Active", "Inactive", "Serving Notice"] } } },
  
  // Join with companies collection
  { $lookup: { from: "companies", localField: "company", foreignField: "_id", as: "companyData" } },
  { $unwind: "$companyData" },
  
  // Join with pay frequencies collection
  { $lookup: { from: "payfrequencies", localField: "employeeData.payFrequency", foreignField: "_id", as: "payFrequencyData" } },
  { $unwind: { path: "$payFrequencyData", preserveNullAndEmptyArrays: true } },
  
  // Project required fields
  { $project: { /* comprehensive field mapping */ } },
  
  // Sort by date and employee name
  { $sort: { endDate: -1, "employee.lastName": 1, "employee.firstName": 1 } }
])
```

### File Structure
- **Controller**: `controllers/reportController.js` - `generatePayslips()`, `generatePayslipsExcel()`, `generatePayslipsPDF()`, `generatePayslipsCSV()`
- **Routes**: `routes/reporting.js` - Added payslips case to switch statement
- **Frontend**: `public/js/reporting.js` - Enhanced employee selection logic
- **Views**: `views/reporting.ejs` - Payslips option already present in dropdown
- **Tests**: `tests/payslips-report.test.js` - Comprehensive unit tests

## Usage Instructions

### Accessing the Feature
1. Navigate to Reports section in PandaPayroll
2. Select "Payslips" from the Report Type dropdown
3. Choose desired date range (current month, previous month, etc.)
4. Optionally select specific employees or leave blank for all employees
5. Select export format (Excel, PDF, or CSV)
6. Click "Generate Report"

### Date Range Options
- **Current Month**: Current calendar month payslips
- **Previous Month**: Previous calendar month payslips
- **Current Year**: All payslips for current year
- **Last 3 Months**: Rolling 3-month period
- **Last 6 Months**: Rolling 6-month period
- **Custom Range**: User-defined start and end dates

### Employee Filtering
- **All Employees**: Include all employees with finalized payroll periods
- **Selected Employees**: Choose specific employees from dropdown list
- **Status Filtering**: Automatically includes Active, Inactive, and Serving Notice employees

## Email Automation

The payslips report is integrated with the existing email automation system:
- **Scheduled Generation**: Can be scheduled for automatic generation
- **Email Distribution**: Supports email distribution to specified recipients
- **Report Attachments**: Generated reports attached to emails in selected format

## Testing

Comprehensive unit tests have been implemented in `tests/payslips-report.test.js`:
- MongoDB aggregation query validation
- Employee filtering functionality
- Date range handling
- Pipeline structure verification

### Running Tests
```bash
npm test -- tests/payslips-report.test.js
```

## Error Handling

The feature includes robust error handling:
- **Database Connection**: Proper error handling for MongoDB operations
- **Data Validation**: Validation of company, date ranges, and employee selections
- **File Generation**: Error handling for Excel/PDF/CSV generation
- **User Feedback**: Meaningful error messages for troubleshooting

## Performance Considerations

- **Efficient Aggregation**: Optimized MongoDB aggregation pipeline
- **Index Usage**: Leverages existing indexes on company, employee, and date fields
- **Memory Management**: Streaming approach for large datasets
- **Caching**: Utilizes existing company and employee data caching

## Security

- **Access Control**: Inherits existing role-based access control
- **Data Filtering**: Company-scoped data access only
- **Input Validation**: Proper validation of all user inputs
- **SQL Injection Prevention**: Uses MongoDB aggregation (NoSQL injection safe)

## Future Enhancements

Potential future improvements:
1. **Advanced Filtering**: Department, cost centre, salary range filters
2. **Custom Fields**: User-configurable report fields
3. **Bulk Operations**: Batch processing for very large datasets
4. **Real-time Updates**: Live data refresh capabilities
5. **Dashboard Integration**: Summary widgets for payslip metrics

## Maintenance

- **Regular Testing**: Run unit tests after any payroll model changes
- **Performance Monitoring**: Monitor aggregation query performance
- **Data Validation**: Verify report accuracy against source data
- **User Feedback**: Collect and address user experience feedback
