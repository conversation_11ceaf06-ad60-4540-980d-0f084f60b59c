# Remember Me System Integration Guide

## Overview

This guide provides comprehensive instructions for integrating the Remember Me system with enhanced security features into your PandaPayroll application. The system includes enterprise-grade session management, device fingerprinting, secure token handling, and comprehensive logout functionality.

## Architecture Components

### Core Services
- **DeviceFingerprintService**: Browser/device identification and fingerprinting
- **TokenService**: Secure remember me token generation and validation using selector/validator pattern
- **DeviceManagementService**: Trusted device registration, management, and revocation
- **SessionManagementService**: Enhanced session management with timeout handling and security monitoring
- **NotificationService**: Security event notifications and user alerts

### Database Models
- **TrustedDevice**: Device registration and trust management
- **RememberMeToken**: Secure token storage with selector/validator pattern
- **Session**: Enhanced session model with expiration and security flags
- **User**: Extended with remember me preferences and security settings

### Middleware
- **enhancedLogout**: Comprehensive logout with token cleanup and security notifications
- **sessionTimeout**: Automatic session timeout detection and handling

## Integration Steps

### 1. Database Setup

The system automatically creates the necessary database models. Ensure your MongoDB connection is properly configured.

### 2. Add Session Management Routes

Add the session management API routes to your main app:

```javascript
// In app.js or your main router file
const sessionManagementRoutes = require('./routes/api/sessionManagement');
app.use('/api/session', sessionManagementRoutes);
```

### 3. Update Authentication Middleware

Add session timeout checking to your authentication middleware:

```javascript
const { checkSessionTimeout } = require('./middleware/sessionTimeout');

// Add to your authentication middleware chain
app.use(checkSessionTimeout);
```

### 4. Update Login Process

Integrate device registration and remember me token creation in your login process:

```javascript
const DeviceManagementService = require('./services/DeviceManagementService');
const TokenService = require('./services/TokenService');

// In your login route after successful authentication
if (req.body.rememberMe) {
  // Register device
  const deviceResult = await DeviceManagementService.registerDevice(
    user._id,
    req,
    req.body.clientFingerprint // Client-side fingerprint data
  );

  if (deviceResult.success) {
    // Create remember me token
    const tokenResult = await TokenService.createRememberMeToken(
      user._id,
      deviceResult.device._id,
      req.ip,
      req.headers['user-agent']
    );

    if (tokenResult.success) {
      // Set remember me cookie
      res.cookie('rememberMe', tokenResult.combinedToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        sameSite: 'lax'
      });
    }
  }
}
```

### 5. Add Remember Me Authentication Check

Add remember me token validation to your authentication middleware:

```javascript
const TokenService = require('./services/TokenService');

// Add this before your standard authentication check
if (!req.isAuthenticated() && req.cookies.rememberMe) {
  const validationResult = await TokenService.validateRememberMeToken(
    req.cookies.rememberMe,
    req.ip,
    req.headers['user-agent']
  );

  if (validationResult.valid) {
    // Log the user in automatically
    req.login(validationResult.user, (err) => {
      if (!err) {
        // Refresh the token
        TokenService.refreshRememberMeToken(validationResult.token._id);
      }
    });
  } else {
    // Clear invalid token
    res.clearCookie('rememberMe');
  }
}
```

### 6. Frontend Integration

#### Session Timeout Warning

Add JavaScript to handle session timeout warnings:

```javascript
// Check for session warnings in response headers
function checkSessionWarnings() {
  // This would be called on AJAX requests
  if (xhr.getResponseHeader('X-Session-Warning')) {
    const minutesRemaining = xhr.getResponseHeader('X-Session-Minutes-Remaining');
    showSessionWarning(minutesRemaining);
  }
}

function showSessionWarning(minutes) {
  // Show user-friendly warning
  const message = `Your session will expire in ${minutes} minutes. Would you like to extend it?`;
  if (confirm(message)) {
    extendSession();
  }
}

function extendSession() {
  fetch('/api/session/extend', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ minutes: 480 }) // 8 hours
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('Session extended successfully');
    }
  });
}
```

#### Device Fingerprinting

Add client-side device fingerprinting:

```javascript
function generateClientFingerprint() {
  return {
    screenResolution: `${screen.width}x${screen.height}`,
    colorDepth: screen.colorDepth,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: navigator.language,
    platform: navigator.platform,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
    hardwareConcurrency: navigator.hardwareConcurrency,
    deviceMemory: navigator.deviceMemory,
    pixelRatio: window.devicePixelRatio,
    touchSupport: 'ontouchstart' in window,
    // Add more fingerprinting data as needed
  };
}

// Send fingerprint data during login
const fingerprintData = generateClientFingerprint();
// Include in login form submission
```

### 7. Security Configuration

Configure security settings in your User model defaults:

```javascript
// Default security settings for new users
const defaultSecuritySettings = {
  rememberMeSettings: {
    enabled: true,
    maxDevices: 5,
    tokenExpirationDays: 30,
  },
  sessionSecurity: {
    maxConcurrentSessions: 3,
    sessionTimeoutMinutes: 480, // 8 hours for payroll security
    inactivityTimeoutMinutes: 60, // 1 hour of inactivity
    requireReauthForSensitive: true,
  },
  securityPreferences: {
    notifyOnNewDevice: true,
    notifyOnSuspiciousActivity: true,
    notifyOnSessionExpiry: true,
    autoLogoutAllDevices: false,
  },
};
```

## Security Features

### 1. Session Timeout Management
- Configurable session expiration (default: 8 hours for payroll security)
- Automatic inactivity logout (default: 1 hour)
- Session extension capabilities
- Warning notifications before expiry

### 2. Device Trust Management
- Device fingerprinting for identification
- Trust level calculation based on usage patterns
- Automatic device cleanup for inactive devices
- Suspicious activity detection

### 3. Token Security
- Selector/validator pattern prevents token theft
- Automatic token rotation on use
- Token series revocation on security breaches
- Configurable token expiration

### 4. Concurrent Session Control
- Configurable maximum concurrent sessions
- Automatic cleanup of oldest sessions when limit exceeded
- Cross-device session invalidation

### 5. Security Monitoring
- Login history tracking
- Suspicious activity detection
- Security event notifications
- Comprehensive audit logging

## API Endpoints

### Session Management
- `GET /api/session/status` - Get current session status
- `POST /api/session/extend` - Extend session expiration
- `GET /api/session/active` - Get all active sessions
- `DELETE /api/session/:sessionId` - Invalidate specific session
- `POST /api/session/logout-all` - Logout from all devices

### Device Management
- `GET /api/session/devices` - Get trusted devices
- `DELETE /api/session/devices/:deviceId` - Revoke device
- `POST /api/session/devices/revoke-all` - Revoke all devices

### Token Management
- `GET /api/session/tokens` - Get active remember me tokens
- `DELETE /api/session/tokens/:tokenId` - Revoke specific token
- `POST /api/session/tokens/revoke-all` - Revoke all tokens

### Security Statistics
- `GET /api/session/security/statistics` - Get security statistics

## Configuration Options

### Environment Variables
```bash
# Session security
SESSION_TIMEOUT_MINUTES=480
INACTIVITY_TIMEOUT_MINUTES=60
MAX_CONCURRENT_SESSIONS=3

# Remember me settings
REMEMBER_ME_EXPIRATION_DAYS=30
MAX_DEVICES_PER_USER=10
MAX_TOKENS_PER_DEVICE=3

# Security thresholds
DEVICE_TRUST_THRESHOLD=50
SUSPICIOUS_ACTIVITY_THRESHOLD=3
```

### User Preferences
Users can configure their security preferences through the user settings:
- Enable/disable remember me functionality
- Set maximum number of trusted devices
- Configure session timeout preferences
- Set notification preferences for security events

## Testing

### Unit Tests
Run the provided unit tests for core services:
```bash
npm test -- --grep "Remember Me"
npm test -- --grep "Session Management"
npm test -- --grep "Device Management"
```

### Integration Tests
Test the complete authentication flow:
```bash
npm test -- --grep "Authentication Integration"
```

### Security Tests
Validate security features:
```bash
npm test -- --grep "Security"
```

## Monitoring and Maintenance

### Regular Cleanup
Set up cron jobs for regular cleanup:
```javascript
// Daily cleanup of expired tokens and sessions
const TokenService = require('./services/TokenService');
const SessionManagementService = require('./services/SessionManagementService');
const DeviceManagementService = require('./services/DeviceManagementService');

async function dailyCleanup() {
  await TokenService.cleanupExpiredTokens();
  await SessionManagementService.cleanupExpiredSessions();
  await DeviceManagementService.cleanupOldDevices(90); // 90 days
}

// Schedule daily at 2 AM
cron.schedule('0 2 * * *', dailyCleanup);
```

### Security Monitoring
Monitor security statistics and alerts:
```javascript
// Get security statistics for monitoring
const stats = await Promise.all([
  SessionManagementService.getSessionStatistics(),
  TokenService.getTokenStatistics(),
  DeviceManagementService.getDeviceStatistics(),
]);

// Log or send to monitoring system
console.log('Security Statistics:', stats);
```

## Troubleshooting

### Common Issues

1. **Session timeout too aggressive**: Adjust `sessionTimeoutMinutes` in user preferences
2. **Remember me not working**: Check cookie settings and HTTPS configuration
3. **Device fingerprinting inconsistent**: Review client-side fingerprinting implementation
4. **Too many security notifications**: Adjust notification preferences

### Debug Logging
Enable debug logging for troubleshooting:
```javascript
// Set environment variable
DEBUG=remember-me:*,session:*,device:*
```

## Security Considerations

1. **HTTPS Required**: Remember me tokens must be transmitted over HTTPS in production
2. **Cookie Security**: Ensure proper cookie flags (httpOnly, secure, sameSite)
3. **Token Rotation**: Tokens are automatically rotated on each use
4. **Device Limits**: Enforce reasonable limits on devices and tokens per user
5. **Session Monitoring**: Monitor for suspicious session patterns
6. **Regular Cleanup**: Implement regular cleanup of expired data

## Support

For issues or questions regarding the Remember Me system integration, please refer to:
- System logs for detailed error information
- Security statistics API for monitoring data
- User feedback for usability improvements

This system provides enterprise-grade security suitable for payroll applications handling sensitive financial data.
