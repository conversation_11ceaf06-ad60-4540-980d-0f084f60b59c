# WhatsApp Integration for Panda Payroll

This document outlines the WhatsApp integration feature for Panda Payroll, which allows employees to interact with the system via WhatsApp for common tasks like requesting payslips, IRP5 documents, and applying for leave.

## Prerequisites

1. WhatsApp Business API account from Meta
2. Redis server for session management
3. Node.js 14+ and npm/yarn
4. Valid SSL certificate (required for webhooks)

## Environment Variables

Add these variables to your `.env` file:

```env
# WhatsApp API Configuration
WHATSAPP_API_VERSION=v17.0
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Security
WHATSAPP_IP_WHITELIST=ip1,ip2,ip3
```

## Features

1. **Authentication**
   - Employees are authenticated using their registered phone numbers
   - Session management with Redis for persistent authentication

2. **Payslip Access**
   - Request latest payslip
   - Request historical payslips by specifying month/year
   - Secure PDF delivery via WhatsApp

3. **IRP5 Documents**
   - Request latest IRP5
   - Request historical IRP5s by specifying year
   - Secure PDF delivery

4. **Leave Management**
   - Apply for leave with start date, end date, and reason
   - Supports all configured leave types
   - Automatic validation against leave balances

## Usage Instructions

### For Employees

1. **Initial Setup**
   - Ensure your phone number is registered in the system
   - Start a chat with the company's WhatsApp business number
   - You'll receive a welcome message with available options

2. **Requesting Documents**
   - For latest payslip: Send "payslip"
   - For specific payslip: Send "payslip YYYY-MM"
   - For IRP5: Send "irp5" or "irp5 YYYY"

3. **Applying for Leave**
   - Format: "Leave request: [type] from YYYY-MM-DD to YYYY-MM-DD reason: [your reason]"
   - Example: "Leave request: Annual from 2024-03-01 to 2024-03-05 reason: Family vacation"

### For Administrators

1. **Setup**
   ```bash
   # Install dependencies
   npm install
   
   # Start Redis server
   redis-server
   
   # Configure webhook URL in Meta Business Dashboard
   https://your-domain.com/api/whatsapp/webhook
   ```

2. **Monitoring**
   - Check logs at `/var/log/whatsapp-service.log`
   - Monitor Redis sessions using Redis CLI
   - View webhook events in Meta Business Dashboard

## Security Considerations

1. **Data Protection**
   - All communication is encrypted using WhatsApp's end-to-end encryption
   - Documents are shared as temporary links that expire
   - Compliance with POPIA regulations

2. **Authentication**
   - Phone number verification against employee records
   - Session management with expiry
   - Rate limiting to prevent abuse

3. **Webhook Security**
   - IP whitelisting
   - Request signature validation
   - HTTPS requirement

## Troubleshooting

1. **Common Issues**
   - Webhook verification failing: Check WHATSAPP_VERIFY_TOKEN
   - Message delivery failing: Verify API credentials
   - Redis connection issues: Check Redis configuration

2. **Error Codes**
   - 1001: Authentication failed
   - 1002: Rate limit exceeded
   - 1003: Invalid message format
   - 1004: Document generation failed

## Development

1. **Running Tests**
   ```bash
   npm test tests/services/whatsappService.test.js
   ```

2. **Adding New Features**
   - Extend WhatsAppService class in services/whatsappService.js
   - Add new message handlers in routes/api/whatsapp.js
   - Update tests accordingly

## Support

For technical support:
- Email: <EMAIL>
- Documentation: https://docs.pandapayroll.com/whatsapp
- GitHub Issues: https://github.com/Jaimon10/PandaPayroll/issues 