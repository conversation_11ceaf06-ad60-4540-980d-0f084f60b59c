# Paystack Payment Integration

This document outlines the Paystack payment integration in the X-Pay application.

## Overview

Paystack is integrated to provide card payment options for invoices. The integration includes:

- Card payment setup on the billing preferences page
- Individual invoice payment
- Webhook handling for payment events
- Secure transaction processing

## Setup Requirements

1. Paystack account with API keys
2. Environment variables configured:
   - `PAYSTACK_PUBLIC_KEY`: Your Paystack public key
   - `PAYSTACK_SECRET_KEY`: Your Paystack secret key
   - `PAYSTACK_BASE_URL`: Paystack API base URL (https://api.paystack.co)

## Implementation Details

### API Endpoints

- **POST /billing/payment/initialize**: Initializes a Paystack payment
- **GET /billing/payment/callback**: Handles the redirect after payment
- **POST /billing/payment/webhook**: Processes Paystack webhook events

### Webhook Events

Paystack sends events to the webhook endpoint. The application handles:

- `charge.success`: Payment successful
- `charge.failed`: Payment failed

### Models

- **BillingPreference**: Stores payment method preferences
- **Invoice**: Updated with payment fields to track payment status

## Testing

To test the integration:

1. Use Paystack test mode
2. Use test card details provided by Paystack:
   - Card Number: ****************
   - Expiry: Any future date
   - CVV: 408
   - PIN: 0000
   - OTP: 123456

## Security Considerations

- API keys are stored in environment variables
- Secret key is only used server-side
- Webhook signatures are verified
- CSRF protection applied to payment endpoints

## Troubleshooting

Common issues:

1. **Payment initialization fails**: Check API key configuration
2. **Webhook not working**: Ensure webhook URL is correctly set in Paystack dashboard
3. **Transaction verification fails**: Check for network issues or invalid references

For support, contact <NAME_EMAIL> or refer to their [documentation](https://paystack.com/docs/api/). 