// Test script to verify the fixed WhatsApp leave request functionality
require('dotenv').config();

const axios = require('axios');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3001';
const WEBHOOK_URL = `${BASE_URL}/api/whatsapp/webhook`;
const TEST_PHONE = '***********'; // <PERSON><PERSON>'s phone number from the error

async function testLeaveRequestFlow() {
  console.log('=== Testing Fixed WhatsApp Leave Request Flow ===');
  console.log('Using API version:', process.env.WHATSAPP_API_VERSION);
  console.log('Webhook URL:', WEBHOOK_URL);
  console.log('Test phone:', TEST_PHONE);

  try {
    // Test 1: Send "hi" to get the main menu
    console.log('\n--- Test 1: Get main menu ---');
    const welcomePayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
            },
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-1',
              timestamp: Date.now().toString(),
              text: {
                body: 'hi'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const welcomeResponse = await axios.post(WEBHOOK_URL, welcomePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Main menu request successful');
    console.log('Status:', welcomeResponse.status);

    // Wait a moment before next test
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Select leave request from interactive menu
    console.log('\n--- Test 2: Select leave request from interactive menu ---');
    const selectLeavePayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
            },
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-2',
              timestamp: Date.now().toString(),
              type: 'interactive',
              interactive: {
                type: 'list_reply',
                list_reply: {
                  id: 'leave_request',
                  title: 'Leave Request'
                }
              }
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const selectResponse = await axios.post(WEBHOOK_URL, selectLeavePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Leave request selection successful');
    console.log('Status:', selectResponse.status);

    // Wait a moment before next test
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 3: Alternative - Send "3" for leave request
    console.log('\n--- Test 3: Alternative text-based leave request trigger ---');
    const textLeavePayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
            },
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-3',
              timestamp: Date.now().toString(),
              text: {
                body: '3'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const textResponse = await axios.post(WEBHOOK_URL, textLeavePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Text-based leave request trigger successful');
    console.log('Status:', textResponse.status);

    // Wait a moment before next test
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 4: Test legacy leave request format
    console.log('\n--- Test 4: Legacy leave request format ---');
    const legacyLeavePayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
            },
            messages: [{
              from: TEST_PHONE,
              id: 'test-message-4',
              timestamp: Date.now().toString(),
              text: {
                body: 'Leave request: Annual from 2025-07-01 to 2025-07-05 reason: Testing the fixed functionality'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const legacyResponse = await axios.post(WEBHOOK_URL, legacyLeavePayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ Legacy leave request format successful');
    console.log('Status:', legacyResponse.status);

    console.log('\n🎉 All leave request tests completed successfully!');
    console.log('\n=== Summary ===');
    console.log('✅ Main menu interaction works');
    console.log('✅ Interactive leave request selection works');
    console.log('✅ Text-based leave request trigger works');
    console.log('✅ Legacy leave request format works');
    console.log('\nThe WhatsApp leave request functionality has been fixed!');

    return true;

  } catch (error) {
    console.error('❌ Leave request test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error data:', error.response.data);
    }
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testLeaveRequestFlow()
    .then((success) => {
      console.log('\n=== Test Results ===');
      if (success) {
        console.log('🎉 All tests PASSED! Leave request functionality is working.');
      } else {
        console.log('❌ Some tests FAILED. Check the logs above for details.');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testLeaveRequestFlow };
