// Load environment variables
require("dotenv").config();
console.log('Environment variables:', process.env);

// --- WhatsApp Phone Number ID Debug ---
const expectedWhatsAppId = '623930367477322';
console.log('WHATSAPP_PHONE_NUMBER_ID at startup:', process.env.WHATSAPP_PHONE_NUMBER_ID);
if (process.env.WHATSAPP_PHONE_NUMBER_ID === expectedWhatsAppId) {
  console.log('✅ Using the correct LIVE WhatsApp Phone Number ID.');
} else {
  console.warn('❌ WARNING: WhatsApp Phone Number ID does NOT match the expected live value!');
}
// --- End WhatsApp Phone Number ID Debug ---


// Output Paystack configuration status at startup
const paystackPublicKey = process.env.PAYSTACK_PUBLIC_KEY;
const paystackSecretKey = process.env.PAYSTACK_SECRET_KEY;
console.log('\n=== Paystack Configuration ===');
console.log('Public Key:', paystackPublicKey ? 'Set' : 'Not set');
console.log('Secret Key:', paystackSecretKey ? 'Set' : 'Not set');
console.log('Mode:', process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'TEST');
console.log('===============================\n');

// Core modules
const express = require("express");
const mongoose = require("mongoose");
const session = require("express-session");
const passport = require("passport");
const flash = require("express-flash");
const { xeroCallbackMiddleware } = require("./middleware/xero");
const MongoStore = require("connect-mongo");
const path = require("path");
const cors = require("cors");
const compression = require("compression");
const helmet = require("helmet");
const csrf = require("csurf");
const cookieParser = require("cookie-parser"); // Make sure this is installed
const rateLimit = require("express-rate-limit");
const moment = require("moment-timezone");

// CRITICAL DEBUG: Add timezone validation function for production debugging
function validateTimezoneConsistency(testDate = new Date()) {
  const utcDate = moment.utc(testDate);
  const saDate = utcDate.tz('Africa/Johannesburg');
  const localDate = moment(testDate);

  console.log('\n=== Timezone Validation Test ===');
  console.log('Test Date Input:', testDate.toISOString());
  console.log('UTC Interpretation:', utcDate.format('YYYY-MM-DD HH:mm:ss Z'));
  console.log('SA Timezone Result:', saDate.format('YYYY-MM-DD HH:mm:ss Z'));
  console.log('Local Interpretation:', localDate.format('YYYY-MM-DD HH:mm:ss Z'));
  console.log('Expected Pattern: UTC -> SA should show +02:00 offset');
  console.log('================================\n');

  return {
    utc: utcDate.format('YYYY-MM-DD'),
    sa: saDate.format('YYYY-MM-DD'),
    local: localDate.format('YYYY-MM-DD'),
    isConsistent: utcDate.format('YYYY-MM-DD') === saDate.format('YYYY-MM-DD')
  };
}

const {
  parseAndFormatDate,
  getEndOfMonthDate,
  getEndOfMonth,
  getLastDayOfMonth,
  getCurrentOrNextLastDayOfMonth,
  getNextMonth,
  formatDateForDisplay,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
} = require("./utils/dateUtils");
const multer = require("multer");
const xlsx = require("xlsx");
const methodOverride = require("method-override");
const mime = require("mime-types");
const { getAppUrl } = require("./utils/url");

// Import all routes
const authRoutes = require("./routes/auth");
const apiAuthRoutes = require("./routes/api/auth"); // Add this line
const employeeServiceRouter = require("./routes/employeeService");
const employeeManagementRouter = require("./routes/employeeManagement");
const securityRoutes = require("./routes/security");
const employeeRoute = require("./routes/employee");
const employeeProfileRoute = require("./routes/employeeProfile");
const employeeProfileRouter = require("./routes/employee-profile");
const filingRouter = require("./routes/filing"); // Only import once
const payrollhubRouter = require("./routes/payrollhub");
const companiesRoute = require("./routes/companies");
const regularInputsRouter = require("./routes/regularInputs");
const payslipFinaliseRouter = require("./routes/payslip");
const settingsRoutes = require("./routes/settings");
const settingsApiRoutes = require("./routes/api/settings");
const dashboardRoutes = require("./routes/dashboard");
const dashboardRouter = require("./routes/dashboard");
const addNewEmployeeRoute = require("./routes/add-new");
const reportingRoutes = require("./routes/reporting");
const payrollhubRoutes = require("./routes/payrollhub");
const reportingRouter = require("./routes/reporting");
const adminRoutes = require("./routes/admin");
const employeeProfileRoutes = require("./routes/employee-profile");
const notificationsRouter = require("./routes/notifications");
const miniEmployeeFormRouter = require("./routes/miniEmployeeForm");
const payFrequencyRoutes = require("./routes/payFrequency");
const formsRouter = require("./routes/forms");
const medicalAidRoutes = require("./routes/medicalAid");
const onceOffFormsRouter = require("./routes/onceOffForms");
const newFormsRouter = require("./routes/newForms");
const securityRouter = require("./routes/security");
const payslipRoutes = require("./routes/payslip");
const selfServiceRoutes = require("./routes/selfService");
const billingRoutes = require("./routes/billingRoutes");
const payslipInputsRoutes = require("./routes/payslipInputs"); // Add this line
const employeeSetupRoutes = require("./routes/employeeSetup");
const supportRoutes = require("./routes/support");
const uploadRouter = require("./routes/upload"); // Add this line
const leaveRoutes = require("./routes/leave");
const leaveApiRoutes = require("./routes/api/leave");
const payrollCalendarApiRoutes = require("./routes/api/payrollCalendar");
const xeroRoutes = require("./routes/xero");
const payRunsRoutes = require("./routes/payRuns");
const testRouter = require("./routes/test"); // Add test router

const internalApiRouter = require("./routes/internalApi"); // NEW: Import internal API routes

// const rfiRoutes = require("./routes/api/rfi");

// Add with other imports
const {
  debugAuth,
  debugTemplateRendering,
  debugTemplateData,
} = require("./middleware/debug");

// Add this with your other imports at the top
const { ensureAuthenticated } = require("./config/auth");

// Add with other imports
const { csrfDebug } = require("./middleware/csrf");

// Add with other imports
const { layoutDebug } = require("./middleware/debug");

// Add this line with your other route imports
const reportSettingsRouter = require("./routes/reportSettings");

// Import CSRF protection at the top with other imports
const csrfProtection = require("./middleware/csrfProtection");

// Import middleware
const checkBillingRestrictions = require("./middleware/billingRestriction");

// Add feature access imports
const { checkFeatureAccess, addFeatureFlagsToTemplates } = require('./middleware/featureAccess');

// Import the support access middleware
const { checkSupportAccess } = require('./middleware/supportAccess');

// Import trial restriction middleware
const checkTrialRestriction = require('./middleware/trialRestriction');

// Add this BEFORE other middleware configurations

// Add JWT middleware
const { jwtMiddleware } = require('./utils/jwtUtils');

// Create Express app
const app = express();

// Mount internal API routes before any session, authentication, or CSRF middleware
// These routes are designed for server-to-server communication and bypass standard authentication
app.use('/internal-api', internalApiRouter);

// Mount public API routes before any session, authentication, or CSRF middleware
const publicApiRouter = require('./routes/publicApi');
app.use('/api', publicApiRouter);

// Configure session with fallback for Vercel serverless environment
const isVercelEnv = process.env.VERCEL || process.env.VERCEL_ENV || 
                  (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL);

// Import connect-mongodb-session for MongoDB session store
const MongoDBStore = require('connect-mongodb-session')(session);

// Session configuration with enhanced production settings
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  name: "sid",  // Changed from connect.sid for shorter cookie
  resave: true, // Force session save even if unmodified
  saveUninitialized: false, // Don't save empty sessions
  rolling: true, // Reset expiration on activity
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: process.env.NODE_ENV === 'production' ? "lax" : "lax", // Consistent sameSite setting
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    path: '/'
  },
  // Add session proxy settings for production
  proxy: process.env.NODE_ENV === 'production'
};

// Configure MongoDB session store for all environments with enhanced error handling
try {
  console.log('Configuring MongoDB session store for production');

  const store = new MongoDBStore({
    uri: process.env.MONGODB_URI,
    collection: 'sessions',
    expires: 24 * 60 * 60 * 1000, // 24 hours
    connectionOptions: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 30000, // Increased timeout for production
      socketTimeoutMS: 60000, // Increased timeout for production
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 5, // Maintain a minimum of 5 socket connections
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      heartbeatFrequencyMS: 10000, // Send a ping every 10 seconds
      // Removed bufferMaxEntries and bufferCommands as they're not supported in newer MongoDB drivers
    },
    databaseName: 'payroll',
    checkExpirationInterval: 900000, // Check expired sessions every 15 minutes
    autoRemove: 'native', // Use MongoDB's TTL for session cleanup
    idField: '_id',
    stringify: true, // Proper serialization
    touchAfter: 24 * 3600, // Lazy session update
    serialize: (session) => {
      try {
        // Ensure we're working with a plain object
        const plainSession = session.toJSON ? session.toJSON() : { ...session };
        return plainSession;
      } catch (err) {
        console.error('Session serialization error:', err);
        return session;
      }
    },
    unserialize: (sessionData) => {
      try {
        return sessionData;
      } catch (err) {
        console.error('Session unserialization error:', err);
        return {};
      }
    }
  });

  // Enhanced error handler with better logging
  store.on('error', function(error) {
    console.error('Session Store Error:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  });

  // Add connection event handlers
  store.on('connected', function() {
    console.log('✓ Session store connected to MongoDB');
  });

  store.on('disconnected', function() {
    console.log('⚠ Session store disconnected from MongoDB');
  });

  // Assign store to session configuration
  sessionConfig.store = store;
  console.log('✓ MongoDB session store configured for production');
} catch (error) {
  console.error('MongoDB session store initialization error:', error);
  throw new Error('Failed to initialize session store: ' + error.message);
}

// Use the session middleware (only once)
app.use(session(sessionConfig));

// Add these lines for body parsing
app.use(express.json()); // To parse JSON request bodies
app.use(express.urlencoded({ extended: true })); // To parse URL-encoded request bodies

// Special direct upload endpoint that bypasses ALL middleware
// This must be defined BEFORE any other middleware
app.post('/direct-upload/:companyCode', (req, res) => {
    console.log('\n=== DIRECT UPLOAD ENDPOINT HIT ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Company Code:', req.params.companyCode);
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Content-Length:', req.headers['content-length']);
    
    const upload = multer({
        storage: multer.memoryStorage(),
        limits: { fileSize: 20 * 1024 * 1024 }
    }).single('file');
    
    upload(req, res, function(err) {
        if (err) {
            console.error('Upload error:', err);
            return res.status(400).json({
                success: false,
                message: err.message || 'File upload failed',
                error: err.toString()
            });
        }
        
        // Log detailed upload info
        console.log('Upload completed. File present:', !!req.file);
        
        if (!req.file) {
            console.error('No file uploaded');
            return res.status(400).json({
                success: false,
                message: 'No file was uploaded'
            });
        }
        
        console.log('File details:', {
            originalname: req.file.originalname,
            mimetype: req.file.mimetype,
            size: req.file.size,
            buffer: req.file.buffer ? 'Present (length: ' + req.file.buffer.length + ')' : 'Missing'
        });
        
        try {
            const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
            const sheetName = workbook.SheetNames[0];
            
            if (!sheetName) {
                return res.status(400).json({
                    success: false,
                    message: 'Excel file has no sheets'
                });
            }
            
            const worksheet = workbook.Sheets[sheetName];
            const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
            
            // Ensure we have headers and data
            if (!data || data.length < 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Excel file must contain a header row and at least one data row'
                });
            }

            // Check if the file exceeds the maximum allowed rows
            const MAX_EMPLOYEES = 1000;
            if (data.length > MAX_EMPLOYEES + 1) { // +1 for header row
                return res.status(400).json({
                    success: false,
                    message: `File contains too many rows. Maximum allowed is ${MAX_EMPLOYEES} employees.`
                });
            }
            
            // Map all rows to JSON with headers (excluding header row)
            const headers = data[0];
            const allEmployees = data.slice(1).map(row => {
                const record = {};
                headers.forEach((header, i) => {
                    record[header] = row[i] !== undefined ? row[i] : '';
                });
                return record;
            });
            
            // For preview, only send first 5 rows but process all
            const previewRows = allEmployees.slice(0, 5);
            
            console.log('Successfully processed Excel file');
            console.log('Headers:', headers);
            console.log('First row data:', allEmployees[0]);
            console.log(`Total rows to process: ${allEmployees.length}`);
            
            // Return success response with data preview and all employees
            return res.json({
                success: true,
                message: 'File uploaded and processed successfully',
                file: {
                    name: req.file.originalname,
                    size: req.file.size,
                    type: req.file.mimetype
                },
                preview: previewRows,
                allEmployees: allEmployees,
                totalRows: allEmployees.length,
                maxEmployees: MAX_EMPLOYEES
            });
        } catch (error) {
            console.error('Excel processing error:', error);
            return res.status(400).json({
                success: false,
                message: 'Could not process Excel file',
                error: error.message
            });
        }
    });
});

// Helper function to map ID types from Excel to system values
function mapIdType(idType) {
    // Map from Excel ID type to system ID type
    if (!idType || idType === 'None' || idType === '') {
        return 'other';
    }
    
    // Normalize to lowercase for case-insensitive matching
    const normalizedType = String(idType).toLowerCase();
    
    // Map to the allowed enum values
    if (normalizedType.includes('rsa') || normalizedType.includes('south africa')) {
        return 'rsa';
    } else if (normalizedType.includes('passport')) {
        return 'passport';
    } else if (normalizedType.includes('foreign')) {
        return 'other';
    }
    
    return 'rsa'; // Default
}

// Helper function to map account types from Excel to system values
function mapAccountType(accountType) {
    if (!accountType || accountType === '') {
        return null; // Allow null if not provided
    }
    
    // Normalize to lowercase for case-insensitive matching
    const normalizedType = String(accountType).toLowerCase();
    
    // Map to the allowed enum values
    if (normalizedType.includes('cheque') || normalizedType.includes('check')) {
        return 'Current';
    } else if (normalizedType.includes('savings') || normalizedType.includes('save')) {
        return 'Savings';
    } else if (normalizedType.includes('transmission') || normalizedType.includes('trans')) {
        return 'Transmission';
    } else {
        // Default to current if we can't determine
        return 'Current';
    }
}

// Helper function to map pay frequency names to database values
function mapPayFrequency(payFrequencyName) {
    if (!payFrequencyName || payFrequencyName === '') {
        return null; // Return null if not provided, will use company default
    }
    
    // Normalize to lowercase for case-insensitive matching
    const normalizedName = String(payFrequencyName).toLowerCase();
    
    // Define the mapping based on the PayFrequency model
    if (normalizedName.includes('monthly') && normalizedName.includes('month end')) {
        return {
            frequency: 'monthly',
            lastDayOfPeriod: 'monthend',
            name: 'Monthly Month End'
        };
    } else if (normalizedName.includes('monthly') && normalizedName.includes('25')) {
        return {
            frequency: 'monthly',
            lastDayOfPeriod: '25',
            name: 'Monthly 25th'
        };
    } else if (normalizedName.includes('weekly') && normalizedName.includes('friday')) {
        return {
            frequency: 'weekly',
            lastDayOfPeriod: 'friday',
            name: 'Weekly Friday'
        };
    } else if (normalizedName.includes('bi-weekly') || normalizedName.includes('biweekly')) {
        return {
            frequency: 'bi-weekly',
            lastDayOfPeriod: 'monthend',
            biWeeklyInterimDay: '15',
            name: 'Bi-Weekly 15th and Month End'
        };
    }
    
    // Default to monthly month end if we can't determine
    return {
        frequency: 'monthly',
        lastDayOfPeriod: 'monthend',
        name: 'Monthly Month End'
    };
}

// Direct endpoint for confirming uploads that bypasses auth and CSRF
app.post('/direct-confirm-upload/:companyCode', express.json(), async (req, res) => {
  console.log('\n=== DIRECT CONFIRM UPLOAD ENDPOINT HIT ===');
  console.log('Timestamp:', new Date().toISOString());
  console.log('Company Code:', req.params.companyCode);
  console.log('Content-Type:', req.get('Content-Type'));
  
  try {
    // Handle both formats: array or object with employees property
    let employees = req.body;
    
    // Check if the data is an object with employees property
    if (req.body && req.body.employees) {
      employees = req.body.employees;
    }
    
    // Ensure employees is an array
    if (!Array.isArray(employees)) {
      console.error('Invalid data format, expected array or object with employees array');
      return res.status(400).json({ 
        error: 'Invalid data format', 
        details: 'Expected array of employee records or object with employees property' 
      });
    }
    
    console.log(`Received data for ${employees.length} employee records`);

    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    console.log(`Found company: ${company.name} (ID: ${company._id})`);

    // Process each employee
    const results = {
      successful: 0,
      failed: 0,
      errors: []
    };

    // Convert Excel dates
    const convertExcelDate = (excelDate) => {
      // Case 1: It's already a JavaScript Date object
      if (excelDate instanceof Date) {
        return excelDate;
      }
      
      // Case 2: It's an Excel serial number (number of days since Dec 30, 1899)
      if (typeof excelDate === 'number') {
        // Excel dates start from December 30, 1899
        const date = new Date(Date.UTC(1899, 11, 30));
        // Add days, accounting for the Excel leap year bug
        date.setUTCDate(date.getUTCDate() + excelDate);
        return date;
      }
      
      // Case 3: It's a string in dd/mm/yyyy format
      if (typeof excelDate === 'string' && excelDate.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        const [day, month, year] = excelDate.split('/').map(Number);
        // Note: Month is 0-indexed in JavaScript Date
        return new Date(year, month - 1, day);
      }
      
      // Case 4: Any other date string format
      if (typeof excelDate === 'string' && excelDate) {
        const parsed = new Date(excelDate);
        if (!isNaN(parsed.getTime())) {
          return parsed;
        }
      }
      
      // Case 5: Unable to parse
      return null;
    };

    // Map ID types
    const mapIdType = (idType) => {
      if (!idType) return 'rsa';
      
      const normalized = idType.toLowerCase();
      if (normalized.includes('south african') || normalized.includes('sa id')) {
        return 'rsa';
      } else if (normalized.includes('passport')) {
        return 'passport';
      } else if (normalized.includes('foreign')) {
        return 'other';
      }
      
      return 'rsa'; // Default
    };

    // Map account types
    const mapAccountType = (accountType) => {
      if (!accountType) return 'Current';
      
      const normalized = String(accountType).toLowerCase();
      if (normalized.includes('cheque') || normalized.includes('check')) {
        return 'Current';
      } else if (normalized.includes('savings')) {
        return 'Savings';
      } else if (normalized.includes('transmission')) {
        return 'Transmission';
      }
      
      return 'Current'; // Default
    };

    for (const rawEmployeeData of employees) {
      try {
        // Transform Excel format to Employee schema format
        let employeeData;
        
        // Check if data is already in the right format
        if (rawEmployeeData.firstName && rawEmployeeData.lastName) {
          employeeData = { ...rawEmployeeData };
        } else {
          // Convert from Excel format to Employee schema format
          const dob = convertExcelDate(rawEmployeeData['Date of Birth *']);
          const doa = convertExcelDate(rawEmployeeData['Date of Appointment *']);
          
          // Calculate end of month date based on date of appointment
          const endOfMonthDate = new Date(doa);
          endOfMonthDate.setDate(1);
          endOfMonthDate.setMonth(endOfMonthDate.getMonth() + 1);
          endOfMonthDate.setDate(0); // Last day of month
          
          // Generate a unique global employee ID
          const globalEmployeeId = `${company.companyCode}-${Date.now().toString().substring(7)}`;
          
          // Basic details
          employeeData = {
            firstName: rawEmployeeData['First Name *'],
            lastName: rawEmployeeData['Last Name *'],
            dob: dob,
            doa: doa,
            email: rawEmployeeData['Email'] || '',
            globalEmployeeId: globalEmployeeId,
            
            // Add required fields
            lastDayOfPeriod: 'monthend',
            firstPayrollPeriodEndDate: endOfMonthDate,
            endOfMonthDate: endOfMonthDate,
            
            // Payment details
            paymentMethod: rawEmployeeData['Payment Method *'] || 'EFT',
            bank: rawEmployeeData['Bank Name'] || '',
            accountType: mapAccountType(rawEmployeeData['Account Type']) || 'Current', // Capitalized
            accountNumber: rawEmployeeData['Account Number'] || '',
            branchCode: rawEmployeeData['Branch Code'] || '',
            accountHolder: rawEmployeeData['Account Holder Name'] || 'Own',
            holderRelationship: rawEmployeeData['Account Holder Name'] ? 'Third Party' : 'Own',
            
            // ID details - use correct enum values
            idType: mapIdType(rawEmployeeData['ID Type *']) || 'rsa',
            idNumber: rawEmployeeData['ID Number'] || '',
            
            // Status
            status: 'Active',
            
            // Create nested personal details with correct enum values
            personalDetails: {
              idType: mapIdType(rawEmployeeData['ID Type *']) || 'rsa',
              idNumber: rawEmployeeData['ID Number'] || '',
              nationality: 'South Africa',
              countryOfBirth: 'South Africa',
              taxResidence: 'South Africa'
            }
          };
          
          // Only add salary if provided
          if (rawEmployeeData['Basic Salary']) {
            employeeData.basicSalary = parseFloat(rawEmployeeData['Basic Salary']);
          }
        }

        // Before creating the employee, ensure PayFrequency is set up
        try {
          const PayFrequency = mongoose.model('PayFrequency');
          
          // Try to find a default pay frequency for this company (monthly month end)
          let payFrequency = await PayFrequency.findOne({
            company: company._id,
            frequency: 'monthly',
            lastDayOfPeriod: 'monthend'
          });
          
          // If not found, create a new pay frequency
          if (!payFrequency) {
            payFrequency = new PayFrequency({
              name: 'Monthly Month End',
              company: company._id,
              frequency: 'monthly',
              lastDayOfPeriod: 'monthend',
              firstPayrollPeriodEndDate: employeeData.endOfMonthDate
            });
            await payFrequency.save();
            console.log('Created new default pay frequency for company');
          }
          
          // Assign pay frequency to employee
          employeeData.payFrequency = payFrequency._id;
        } catch (payFreqError) {
          console.error('Error setting up pay frequency:', payFreqError);
          // Continue despite error, our null check in routes will handle it
        }

        // Create a new employee instance
        const newEmployee = new Employee({
          company: company._id,
          ...employeeData
        });

        // If there's no employee number and company is set to auto-generate
        if (!newEmployee.companyEmployeeNumber) {
          try {
            // Generate an employee number
            const employeeNumberService = require('./services/employeeNumberService');
            newEmployee.companyEmployeeNumber = await employeeNumberService.generateEmployeeNumber(company._id);
          } catch (numError) {
            // Handle number generation error specifically
            console.error('Error generating employee number:', numError.message);
            // Create a simplified error without circular references
            results.errors.push({
              name: `${employeeData.firstName} ${employeeData.lastName}`,
              error: numError.message
            });
            results.failed++;
            continue; // Skip to next employee
          }
        }

        // Save the employee
        try {
          await newEmployee.save();
          console.log(`Created employee: ${employeeData.firstName} ${employeeData.lastName}`);
          results.successful++;
        } catch (empError) {
          // Handle general employee creation error
          console.error('Error creating employee:', empError.message);
          
          // Check if it's a duplicate key error for company+employeeNumber
          if (empError.name === 'MongoServerError' && empError.code === 11000) {
            if (empError.message.includes('companyEmployeeNumber') && empError.message.includes('company')) {
              // It's a duplicate employee number within the same company
              results.errors.push({
                name: `${employeeData.firstName} ${employeeData.lastName}`,
                error: `Employee number ${newEmployee.companyEmployeeNumber} already exists in this company`
              });
            } else {
              // Other duplicate key error
              results.errors.push({
                name: `${employeeData.firstName} ${employeeData.lastName}`,
                error: empError.message
              });
            }
          } else {
            // Other error
            results.errors.push({
              name: `${employeeData.firstName} ${employeeData.lastName}`,
              error: empError.message
            });
          }
          results.failed++;
        }
      } catch (outerError) {
        // Handle data transformation errors
        console.error('Error processing employee data:', outerError.message);
        
        // Get name from either format
        const firstName = rawEmployeeData.firstName || rawEmployeeData['First Name *'] || 'Unknown';
        const lastName = rawEmployeeData.lastName || rawEmployeeData['Last Name *'] || 'Employee';
        
        results.errors.push({
          name: `${firstName} ${lastName}`,
          error: outerError.message
        });
        results.failed++;
      }
    }

    console.log('Import results:', results);
    res.json(results);
  } catch (error) {
    // Handle overall process error
    console.error('Error processing bulk employee import:', error.message);
    res.status(500).json({ 
      error: 'Failed to process employee import', 
      details: error.message 
    });
  }
});

// Define CORS options before using them
// const corsOptions = {
//   origin: process.env.BASE_URL || "http://localhost:3002",
//   credentials: true,
//   methods: ["GET", "POST", "PUT", "DELETE"],
//   allowedHeaders: ["Content-Type", "Authorization"],
// };

const corsOptions = {
  origin: [
    process.env.BASE_URL || "http://localhost:3002",
    "https://payroll.pss-group.co.za",
    "http://payroll.pss-group.co.za",
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization"],
};

// Import Models
const Employee = require("./models/Employee");
const Payroll = require("./models/Payroll");
const PayRun = require("./models/payRun");
const Company = require("./models/Company");
const User = require("./models/user");
const AssignedRole = require("./models/assignedRole");
const EmployeeNumberSettings = require("./models/employeeNumberSettings");
const OIDReturn = require("./models/oidReturn");
const Role = require("./models/role");
const PayFrequency = require("./models/PayFrequency");
const PayrollPeriod = require("./models/PayrollPeriod");

// MongoDB Connection
const connectDB = require("./config/db");
connectDB();

// Once MongoDB is connected, update the Employee index
mongoose.connection.on("connected", async () => {
  console.log("\n=== MongoDB Connection Established ===");
  console.log("MongoDB Connected:", mongoose.connection.host);
  console.log("Connection ID:", mongoose.connection.id);
  console.log("Database Name:", mongoose.connection.name);
  console.log("Connection State:", mongoose.connection.readyState);
  console.log("Connection Options:", JSON.stringify(mongoose.connection._connectionOptions || {}, null, 2));
  
  // Update Employee indexes to ensure we have the right compound index
  try {
    const Employee = require('./models/Employee');
    
    console.log("\n=== Updating Employee Model Indexes ===");
    
    // First, drop any single-field unique index on companyEmployeeNumber
    await Employee.collection.dropIndex('companyEmployeeNumber_1')
      .catch(err => {
        // It's okay if the index doesn't exist
        if (err.code !== 27) {
          console.error("Error dropping index:", err);
        } else {
          console.log("No existing companyEmployeeNumber_1 index found");
        }
      });
    
    // Create the compound unique index
    await Employee.collection.createIndex(
      { company: 1, companyEmployeeNumber: 1 },
      { unique: true }
    );
    
    console.log("Successfully updated Employee indexes");
    
    // List all indexes to verify
    const indexes = await Employee.collection.indexes();
    console.log("Current Employee collection indexes:", JSON.stringify(indexes, null, 2));
  } catch (error) {
    console.error("Error updating indexes:", error);
  }
  
  // Track event listeners
  const eventNames = Object.keys(mongoose.connection._events || {});
  console.log("Registered Event Listeners:", eventNames);
  console.log("userVerified listeners count:", mongoose.connection.listenerCount("userVerified"));
  
  // IMPORTANT FIX: Make sure we add the userVerified listener on connected, not just open
  console.log("\n=== Adding userVerified event listener in connected event ===");
  setupWelcomeEmailListener();

  // Initialize scheduler for automated tasks
  try {
    const scheduler = require('./services/scheduler');
    scheduler.init();
    console.log("✅ Scheduler initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize scheduler:", error);
  }
});

mongoose.connection.on("error", (err) => {
  console.error("\n=== MongoDB Connection Error ===");
  console.error("Error:", err);
});

mongoose.connection.on("disconnected", () => {
  console.log("\n=== MongoDB Connection Disconnected ===");
});

// Debug middleware to log custom events
const originalEmit = mongoose.connection.emit;
mongoose.connection.emit = function(event, ...args) {
  console.log(`\n=== MongoDB Custom Event Emitted: ${event} ===`);

  // Safe JSON stringify with circular reference handling
  const safeStringify = (obj) => {
    const seen = new WeakSet();
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    }, 2);
  };

  console.log("Event Arguments:", args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      try {
        return safeStringify(arg);
      } catch (error) {
        return `[Object - ${arg.constructor?.name || 'Unknown'}]`;
      }
    }
    return arg;
  }));
  console.log("Listener Count:", this.listenerCount(event));
  console.log("Stack Trace:", new Error().stack.split("\n").slice(0, 4).join("\n"));
  return originalEmit.call(this, event, ...args);
};

// Create a function to set up the welcome email listener
function setupWelcomeEmailListener() {
  // First remove any existing listeners to avoid duplicates
  const existingCount = mongoose.connection.listenerCount("userVerified");
  if (existingCount > 0) {
    console.log(`Removing ${existingCount} existing userVerified listeners`);
    mongoose.connection.removeAllListeners("userVerified");
  }
  
  // Add the listener
  console.log("Setting up userVerified event listener");
  
  mongoose.connection.on("userVerified", async (userData) => {
    try {
      console.log("\n=== Welcome Email Processing START ===");
      console.log("User verified event received with data:", JSON.stringify(userData, null, 2));
      console.log("Event timestamp:", new Date().toISOString());
      console.log("Current listener count for userVerified:", mongoose.connection.listenerCount("userVerified"));
      
      // Import modules needed for email sending
      const path = require("path");
      const ejs = require("ejs");
      const fs = require("fs");
      const { getAppUrl } = require("./utils/url");
      
      // Log environment variables related to email
      console.log("Email configuration:", {
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_SECURE === "true",
        user: process.env.EMAIL_USER ? "Set" : "Not set",
        pass: process.env.EMAIL_PASS ? "Set" : "Not set",
        fromName: process.env.EMAIL_FROM_NAME || "Panda Solutions"
      });
      
      // Helper function to ensure logo exists
      const ensureLogoExists = () => {
        try {
          console.log("Searching for logo file...");
          // Check common locations for the logo
          const possiblePaths = [
            path.join(__dirname, "public/images/pandalogo.png"),
            path.join(__dirname, "public/images/logo.png"),
            path.join(__dirname, "public/img/logo.png"),
            path.join(__dirname, "public/assets/images/logo.png")
          ];
      
          // Return the first path that exists
          for (const logoPath of possiblePaths) {
            if (fs.existsSync(logoPath)) {
              console.log("Logo found at:", logoPath);
              return logoPath;
            } else {
              console.log("Logo not found at:", logoPath);
            }
          }
      
          // Fallback to the SVG logo if exists
          const svgPath = path.join(__dirname, "public/images/pandalogo.svg");
          if (fs.existsSync(svgPath)) {
            console.log("SVG logo found at:", svgPath);
            return svgPath;
          } else {
            console.log("SVG logo not found at:", svgPath);
          }
      
          console.warn("No logo found for welcome email. Using default path which may not exist.");
          return path.join(__dirname, "public/images/pandalogo.png");
        } catch (error) {
          console.error("Error finding logo:", error);
          return path.join(__dirname, "public/images/pandalogo.png");
        }
      };
      
      // Import the sendMail function from auth.js or create a new one
      console.log("Initializing nodemailer...");
      const nodemailer = require("nodemailer");
      
      // Create a transporter
      console.log("Creating email transporter...");
      const transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_SECURE === "true",
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
        tls: {
          rejectUnauthorized: false, // Only use this in development
        },
        debug: true, // Enable debug output
        logger: true, // Log information about mail being sent
      });
      
      console.log("Verifying email transporter connection...");
      try {
        const verifyResult = await transporter.verify();
        console.log("Transporter verification result:", verifyResult);
      } catch (verifyError) {
        console.error("Transporter verification failed:", verifyError);
        // Continue anyway as this is just a diagnostic step
      }
      
      // Function to send email
      const sendMail = async (mailOptions) => {
        return new Promise((resolve, reject) => {
          const updatedMailOptions = {
            ...mailOptions,
            from: {
              name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
              address: process.env.EMAIL_USER,
            },
            headers: {
              "X-Priority": "1",
              "X-MSMail-Priority": "High",
              Importance: "high",
              "Message-ID": `<${Date.now()}.${Math.random()
                .toString(36)
                .substring(2)}@pss-group.co.za>`,
              "List-Unsubscribe": `<mailto:${process.env.EMAIL_USER}?subject=unsubscribe>`,
              "X-Mailer": "Panda Solutions Mailer/1.0",
              "X-Entity-Ref-ID": require("crypto").randomBytes(32).toString("hex"),
            },
          };
      
          console.log("Preparing to send welcome email with options:", {
            from: updatedMailOptions.from,
            to: updatedMailOptions.to,
            subject: updatedMailOptions.subject,
            attachments: updatedMailOptions.attachments ? 
              updatedMailOptions.attachments.map(a => ({filename: a.filename, cid: a.cid})) : 
              "None"
          });
      
          transporter.sendMail(updatedMailOptions, (error, info) => {
            if (error) {
              console.error("Send mail error:", error);
              console.error("Error stack:", error.stack);
              if (error.response) console.error("SMTP Response:", error.response);
              reject(error);
            } else {
              console.log("Email sent successfully:", {
                messageId: info.messageId,
                response: info.response,
                envelope: info.envelope,
                accepted: info.accepted,
                rejected: info.rejected
              });
              resolve(info);
            }
          });
        });
      };
      
      // Welcome email template path
      const welcomeTemplatePath = path.join(__dirname, "views/emails/welcome.ejs");
      console.log("Looking for welcome email template at:", welcomeTemplatePath);
      
      // Check if welcome email template exists
      if (!fs.existsSync(welcomeTemplatePath)) {
        console.error("Welcome email template not found at:", welcomeTemplatePath);
        
        // Check if views/emails directory exists, create if needed
        const emailsDirPath = path.join(__dirname, "views/emails");
        if (!fs.existsSync(emailsDirPath)) {
          console.log("Creating views/emails directory...");
          try {
            fs.mkdirSync(emailsDirPath, { recursive: true });
            console.log("views/emails directory created");
          } catch (dirError) {
            console.error("Error creating views/emails directory:", dirError);
          }
        }
        
        // List directories to help debug
        if (fs.existsSync(emailsDirPath)) {
          console.log("Contents of emails directory:", fs.readdirSync(emailsDirPath));
        } else {
          console.log("Emails directory not found at:", emailsDirPath);
          
          // Check for views directory
          const viewsDirPath = path.join(__dirname, "views");
          if (fs.existsSync(viewsDirPath)) {
            console.log("Contents of views directory:", fs.readdirSync(viewsDirPath));
          } else {
            console.log("Views directory not found at:", viewsDirPath);
          }
        }
        
        // Create a basic welcome template if it doesn't exist
        console.log("Creating a default welcome email template...");
        try {
          // Generate application URLs using the utility function
          const supportUrl = getAppUrl(null, "support");
          const videosUrl = getAppUrl(null, "videos");
          const dashboardUrl = getAppUrl(null, "dashboard");
          
          const defaultTemplate = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Welcome to Panda Solutions</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .logo { max-width: 200px; }
    h1 { color: #2c3e50; }
    .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="cid:logo" alt="Panda Solutions Logo" class="logo">
      <h1>Welcome to Panda Solutions!</h1>
    </div>
    
    <p>Hello <%= firstName %>,</p>
    
    <p>Thank you for joining Panda Solutions. Your account has been verified and is now ready to use.</p>
    
    <p>Here's what you need to know to get started:</p>
    
    <ul>
      <li>Our support team is available via email at <a href="mailto:<EMAIL>"><EMAIL></a> with a 48-hour turnaround time.</li>
      <li>Check out our <a href="${videosUrl}">video guides</a> for help navigating the platform.</li>
      <li>For urgent matters, contact us at +27 00 000 0000.</li>
      <li>Visit your <a href="${dashboardUrl}">dashboard</a> to get started.</li>
      <li>Need help? Visit our <a href="${supportUrl}">support center</a>.</li>
    </ul>
    
    <p>We're excited to have you on board!</p>
    
    <p>Best regards,<br>
    The Panda Solutions Team</p>
    
    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Panda Solutions. All rights reserved.</p>
      <p>This email was sent to <%= email %>.</p>
    </div>
  </div>
</body>
</html>`;
          
          fs.writeFileSync(welcomeTemplatePath, defaultTemplate);
          console.log("Default welcome email template created successfully");
        } catch (templateError) {
          console.error("Error creating default welcome template:", templateError);
          throw new Error("Could not create welcome email template");
        }
      } else {
        console.log("Welcome email template found successfully");
      }
      
      // Generate application URLs for the email
      const supportUrl = getAppUrl(null, "support");
      const videosUrl = getAppUrl(null, "videos");
      const dashboardUrl = getAppUrl(null, "dashboard");
      
      // Render welcome email template
      console.log("Rendering welcome email template with data:", {
        firstName: userData.firstName,
        email: userData.email,
        supportUrl,
        videosUrl,
        dashboardUrl
      });
      
      const emailHtml = await ejs.renderFile(
        welcomeTemplatePath,
        {
          firstName: userData.firstName,
          email: userData.email,
          supportUrl,
          videosUrl,
          dashboardUrl
        }
      );
      
      console.log("Email template rendered successfully, length:", emailHtml.length);
      
      // Get logo path
      const logoPath = ensureLogoExists();
      console.log("Using logo for email:", logoPath);
      
      // Prepare and send welcome email
      const mailOptions = {
        to: userData.email,
        subject: "Welcome to Panda Solutions - Getting Started",
        html: emailHtml,
        attachments: [
          {
            filename: "logo.png",
            path: logoPath,
            cid: "logo",
          },
        ],
      };
      
      // Send welcome email
      console.log("Sending welcome email...");
      try {
        const info = await sendMail(mailOptions);
        console.log("Welcome email sent successfully to:", userData.email);
        console.log("Email sending info:", info);
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);
        // Don't rethrow so we can continue with other operations
      }
      
      console.log("=== Welcome Email Processing END ===\n");
    } catch (error) {
      console.error("Error in welcome email event handler:", error);
      console.error("Stack trace:", error.stack);
    }
  });
  
  console.log("userVerified event listener setup complete. Listener count:", 
    mongoose.connection.listenerCount("userVerified"));
}

// Add this after MongoDB connection monitoring
mongoose.connection.on("open", () => {
  console.log("\n=== MongoDB Connection Open ===");
  const db = mongoose.connection.db;

  // Monitor sessions collection
  const sessionsCollection = db.collection("sessions");
  const changeStream = sessionsCollection.watch();

  changeStream.on("change", (change) => {
    console.log("\n=== Session Collection Change ===");
    console.log("Operation Type:", change.operationType);
    console.log("Session ID:", change.documentKey?._id);
    console.log("Updated Fields:", change.updateDescription?.updatedFields);
  });
  
  // Set up event listener for user verification to send welcome emails during open event
  console.log("\n=== Setting up userVerified event listener in open event ===");
  setupWelcomeEmailListener();
});

// Initialize basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser(process.env.COOKIE_SECRET || "your-secret-key"));
app.use(cors(corsOptions));

// Initialize session middleware
app.use(session(sessionConfig));

// Initialize flash after session
app.use(flash());

// Initialize Passport after session
app.use(passport.initialize());
app.use(passport.session());
require("./config/passport")(app);

// Configure CSRF protection after session middleware and before routes
// API routes are excluded from CSRF protection to allow token-based authentication
app.use((req, res, next) => {
  // Exclude API routes from CSRF protection since they use JWT authentication
  // This allows API clients to authenticate without requiring CSRF tokens
  if (req.path.startsWith('/api')) {
    return next();
  }
  
  // Apply CSRF protection to all non-API routes
  csrf({
    cookie: {
      key: "_csrf",
      path: "/",
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    },
  })(req, res, next);
});

// Add CSRF token to all responses (except API routes)
app.use((req, res, next) => {
  // Skip for API routes since they don't use CSRF
  if (req.path.startsWith('/api')) {
    return next();
  }
  
  // Add CSRF token to response locals for template rendering
  res.locals.csrfToken = req.csrfToken();
  next();
});

// Add CSRF error handler
app.use((err, req, res, next) => {
  if (err.code === "EBADCSRFTOKEN") {
    console.log("Invalid CSRF token:", req.path);
    return res.redirect(req.headers.referer || "/");
  }
  next(err);
});

// Add JWT middleware to work alongside session
app.use(jwtMiddleware);

// Enhanced authentication state logging and JWT-to-session bridge
app.use(async (req, res, next) => {
  console.log('\n=== Authentication State Monitor ===');
  console.log('Auth State:', {
    sessionAuth: req.isAuthenticated(),
    hasJWT: !!req.jwtUser,
    sessionID: req.sessionID,
    sessionPassport: req.session?.passport,
    user: req.user ? { id: req.user._id, email: req.user.email } : null
  });

  // If we have JWT but no session, try to bridge them
  if (req.jwtUser && !req.isAuthenticated() && !req.session.passport) {
    console.log('Attempting to bridge JWT to session authentication');
    try {
      // Find the user to ensure they still exist
      const User = require('./models/user');
      const user = await User.findById(req.jwtUser.id).populate('currentCompany');

      if (user) {
        // Create session for JWT user
        req.session.passport = { user: user._id };
        req.user = user; // Set user for immediate use

        console.log('✓ Successfully bridged JWT to session for user:', user.email);
      } else {
        console.log('✗ JWT user not found in database, clearing JWT cookie');
        res.clearCookie('jwt', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/'
        });
      }
    } catch (error) {
      console.error('Error bridging JWT to session:', error);
    }
  }

  next();
});

// View engine setup
app.use(express.static(path.join(__dirname, "public")));
app.use("/uploads", express.static(path.join(__dirname, "uploads")));
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));

// Serve static files from multiple directories
app.use(express.static(path.join(__dirname, "public")));
app.use(express.static(path.join(__dirname, "views")));
app.use(express.static(path.join(__dirname, "assets")));

// Global variables middleware
app.use((req, res, next) => {
  res.locals.user = req.user;
  res.locals.success_msg = req.flash("success_msg");
  res.locals.error_msg = req.flash("error_msg");
  res.locals.error = req.flash("error");
  next();
});

// Debug middleware for all routes
app.use((req, res, next) => {
  console.log("\n=== GLOBAL ROUTE DEBUG ===");
  console.log("Incoming Request:");
  console.log("Full URL:", req.originalUrl);
  console.log("Path:", req.path);
  console.log("Method:", req.method);
  console.log("Base URL:", req.baseUrl);
  next();
});

// Add route registration debug middleware
app.use((req, res, next) => {
  console.log("\n=== Route Registration Order Debug ===");
  console.log({
    path: req.path,
    method: req.method,
    matchedRoute: req.route?.path,
    isAuthenticated: req.isAuthenticated(),
    user: req.user ? { id: req.user._id } : null,
  });
  next();
});

// Add session monitor middleware
app.use((req, res, next) => {
  console.log("\n=== Session Monitor ===");
  console.log({
    path: req.path,
    method: req.method,
    sessionID: req.sessionID,
    session: {
      _id: req.session?.id,
      cookie: req.session?.cookie ? "Present" : "Missing",
      passport: req.session?.passport,
      flash: req.session?.flash,
      lastModified: req.session?.lastModified,
    },
    isAuthenticated: req.isAuthenticated(),
    user: req.user ? { id: req.user._id, email: req.user.email } : null,
  });
  next();
});

// Add CSRF debug middleware
app.use((req, res, next) => {
  console.log("\n=== CSRF Protection Debug ===");
  console.log("Request details:", {
    path: req.path,
    method: req.method,
    hasCSRFCookie: !!req.cookies["_csrf"],
    csrfToken: req.body?._csrf,
  });
  next();
});

// Add this before the authentication middleware
// Middleware to bypass authentication for support token access
app.use((req, res, next) => {
  // Check if there's a support token in the query or session
  if (req.query.supportToken || req.session?.supportAccess) {
    console.log("\n=== SUPPORT TOKEN AUTH BYPASS ===");
    console.log(JSON.stringify({
      path: req.path,
      originalUrl: req.originalUrl,
      supportToken: req.query.supportToken || req.session?.supportAccess?.token,
      sessionSupport: req.session?.supportAccess ? 'Present' : 'Missing'
    }, null, 2));
    
    // Skip authentication for routes with support token
    return next();
  }
  
  // Continue with normal authentication
  next();
});

// Add after other middleware initialization and before routes
app.use(addFeatureFlagsToTemplates);

// API routes should be registered before authentication middleware
// This is important for public API endpoints, like JWT auth routes
console.log("\n=== Registering API Routes ===");
// Register auth API routes first (includes login and health check endpoints)
app.use("/api/auth", apiAuthRoutes);
console.log("Auth API routes registered at /api/auth");

// Register WhatsApp webhook routes before authentication
// These must be accessible without authentication for Meta's webhook verification
const whatsappRoutes = require('./routes/whatsapp');
app.use('/api/whatsapp', whatsappRoutes);
console.log("WhatsApp webhook routes registered at /api/whatsapp");

// Route registrations - auth routes should be registered next
app.use("/", authRoutes);

// Support routes should be registered after auth but before CSRF protection
console.log("\n=== Registering Support Routes ===");
app.use("/support", supportRoutes);
console.log("Support routes registered at /support");

// Mount employee setup routes before CSRF and auth middleware
// since they are accessed before login
app.use("/employee", employeeSetupRoutes);

// Mount employee portal routes early to ensure they take precedence
const employeePortalRoutes = require("./routes/employeePortal");
app.use("/employee-portal", employeePortalRoutes);

// CSRF protection and authentication for other routes
app.use((req, res, next) => {
  // Exclude login, public, API, and specific routes from CSRF
  if (
    req.path === "/login" ||
    req.path.startsWith("/public") ||
    req.path.startsWith("/api") ||
    req.path.startsWith("/support")
  ) {
    return next();
  }
  csrfProtection(req, res, next);
});

app.use((req, res, next) => {
  // Exclude login, public, API, and specific routes from authentication
  if (
    req.path === "/login" ||
    req.path.startsWith("/public") ||
    req.path.startsWith("/api") ||
    req.path.startsWith("/support")
  ) {
    return next();
  }
  ensureAuthenticated(req, res, next);
});

// Special route handler for support token access
// This must be registered before other client routes
app.get("/clients/:companyCode", async (req, res, next) => {
  // Only process if there's a support token
  if (!req.query.supportToken) {
    return next();
  }
  
  console.log("\n=== SUPPORT TOKEN DIRECT ACCESS HANDLER ===");
  console.log(`Path: ${req.path}`);
  console.log(`Original URL: ${req.originalUrl}`);
  console.log(`Company Code: ${req.params.companyCode}`);
  console.log(`Support Token: ${req.query.supportToken}`);
  
  try {
    const supportToken = req.query.supportToken;
    const companyCode = req.params.companyCode;
    
    // Find company by company code
    console.log(`Looking for company with code: ${companyCode}`);
    let company = await Company.findOne({ companyCode });
    
    if (!company) {
      console.log("Trying case-insensitive match");
      company = await Company.findOne({ 
        companyCode: { $regex: new RegExp(`^${companyCode}$`, 'i') } 
      });
    }
    
    if (!company) {
      console.log(`Company not found for code: ${companyCode}`);
      req.flash("error", "Invalid company code");
      return res.redirect("/login");
    }
    
    console.log(`Company found: ${company.name} (${company._id})`);
    
    // Verify support access token
    const supportAccess = await SupportAccess.findOne({
      token: supportToken,
      company: company._id,
      expiresAt: { $gt: new Date() },
      isRevoked: false
    });
    
    if (!supportAccess) {
      console.log("Invalid or expired support token");
      req.flash("error", "Invalid or expired support token");
      return res.redirect("/login");
    }
    
    console.log("Valid support token found");
    
    // Store support access in session
    req.session.supportAccess = {
      token: supportToken,
      companyId: company._id.toString(),
      companyCode: company.companyCode
    };
    
    // Save session before redirecting
    await new Promise((resolve, reject) => {
      req.session.save(err => {
        if (err) {
          console.error("Error saving session:", err);
          reject(err);
        } else {
          console.log("Session saved successfully");
          resolve();
        }
      });
    });
    
    // Redirect to dashboard
    console.log(`Redirecting to dashboard: /clients/${company.companyCode}/dashboard?supportToken=${supportToken}`);
    return res.redirect(`/clients/${company.companyCode}/dashboard?supportToken=${supportToken}`);
  } catch (error) {
    console.error("Error processing support token:", error);
    req.flash("error", "An error occurred while processing your request");
    return res.redirect("/login");
  }
});

// Other route registrations with their existing middleware
app.use("/clients", payslipInputsRoutes); // Move this before forms router
app.use("/clients", regularInputsRouter); // Add regularInputs router
app.use("/clients", employeeServiceRouter);
app.use("/security", securityRouter);
app.use("/clients", employeeManagementRouter);
app.use("/clients", formsRouter);
app.use("/employee-profile", employeeProfileRouter);
app.use("/employeeProfile", employeeProfileRoute);
app.use("/clients", employeeProfileRoute); // Add this line to handle /clients/... routes
app.use("/clients", dashboardRouter);
// Mount settings routes BEFORE payrollhub to ensure specific routes are matched first
app.use("/clients", settingsRoutes);
app.use("/clients", payrollhubRouter);
app.use("/employee", employeeRoute);
app.use("/clients", filingRouter); // Only mount once under /clients
app.use("/payslip", payslipFinaliseRouter);
app.use("/companies", companiesRoute);
app.use("/clients", reportingRoutes);
app.use("/billing", billingRoutes);
app.use("/clients", payslipInputsRoutes);

// Import the payRun routes
const payRunRoutes = require("./routes/payRun");

// Add this with your other route middleware
app.use("/payrun", payRunRoutes);

// Mount API routes
app.use("/api/pay-runs", payRunsRoutes);

// Mount client-facing pay runs routes
app.use("/clients", payRunsRoutes);

// Mount Xero routes before authentication
app.use("/xero", xeroRoutes);

// Add global authentication after Xero routes
app.use((req, res, next) => {
  // Skip authentication for Xero routes
  if (req.path.startsWith("/xero")) {
    return next();
  }

  // Require authentication for all other routes
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  next();
});

// Mount Xero routes before CSRF protection
// Debug middleware for Xero routes
app.use((req, res, next) => {
  if (req.originalUrl.includes("/xero")) {
    console.log("=== Xero Route Debug ===");
    console.log("Path:", req.path);
    console.log("Full URL:", req.originalUrl);
    console.log("Method:", req.method);
    console.log("Query:", req.query);
    console.log("Session ID:", req.sessionID);
    console.log("Session:", req.session);
  }
  next();
});

// Create a dedicated router for Xero routes
const xeroRouter = express.Router();

// Test callback route
xeroRouter.get("/test-callback", (req, res) => {
  console.log("=== Xero Test Callback Route Hit ===");
  console.log("URL:", req.url);
  console.log("Original URL:", req.originalUrl);
  console.log("Base URL:", req.baseUrl);
  console.log("Path:", req.path);
  console.log("Query:", req.query);
  res.json({
    message: "Xero test callback route working",
    requestInfo: {
      url: req.url,
      originalUrl: req.originalUrl,
      baseUrl: req.baseUrl,
      path: req.path,
      query: req.query,
    },
  });
});

// Mount Xero routes
app.use("/xero", xeroRoutes);

// Mount leave API routes with debug middleware
app.use(
  "/api/leave",
  (req, res, next) => {
    console.log("\n=== Leave API Route Debug ===");
    console.log("Path:", req.path);
    console.log("Method:", req.method);
    console.log("Body:", req.body);
    console.log("Headers:", req.headers);
    next();
  },
  leaveApiRoutes
);
app.use("/leave", leaveRoutes);

// Mount payroll calendar API routes
app.use(
  "/api/payroll-calendar",
  (req, res, next) => {
    console.log("\n=== Payroll Calendar API Route Debug ===");
    console.log("Path:", req.path);
    console.log("Method:", req.method);
    console.log("Body:", req.body);
    next();
  },
  payrollCalendarApiRoutes
);

app.use("/clients/:companyCode/reporting", reportingRouter);
app.use(
  "/clients/:companyCode/reporting/settings",
  ensureAuthenticated,
  csrfProtection,
  (req, res, next) => {
    console.log("\n=== Report Settings Route Hit ===");
    console.log({
      path: req.path,
      method: req.method,
      companyCode: req.params.companyCode,
      user: req.user?._id,
      csrfToken: !!req.csrfToken,
      body: req.method === "POST" ? req.body : undefined,
    });
    next();
  },
  reportSettingsRouter
);
app.use("/admin", adminRoutes);
app.use("/employee-profile", employeeProfileRoutes);
app.use("/notifications", notificationsRouter);
app.use("/miniEmployeeForm", miniEmployeeFormRouter);
app.use("/medicalAid", medicalAidRoutes);
app.use("/onceOffForms", onceOffFormsRouter);
app.use("/newForms", newFormsRouter);
app.use("/pay-frequency", payFrequencyRoutes);
app.use("/clients/:companyCode/payslip", payslipRoutes);

// Add this with your other route middleware
const payrollRoutes = require("./routes/payroll");
app.use("/", payrollRoutes);

// Self-service routes with logging middleware
app.use(
  "/self-service",
  (req, res, next) => {
    console.log("\n=== Self Service Route Handler ===");
    console.log("Original URL:", req.originalUrl);
    console.log("Base URL:", req.baseUrl);
    console.log("Path:", req.path);
    console.log("Method:", req.method);
    next();
  },
  csrfProtection,
  selfServiceRoutes
);

// API Routes
app.use(
  "/api/settings",
  (req, res, next) => {
    console.log("\n=== Settings API Route ===");
    console.log("Path:", req.path);
    console.log("Method:", req.method);
    console.log("Body:", req.body);
    next();
  },
  settingsApiRoutes
);

// Debug middleware (after routes, before error handlers)
app.use((req, res, next) => {
  console.log("\n=== Route Debug ===");
  console.log("Request URL:", req.url);
  console.log("Request Method:", req.method);
  console.log("Matched Route:", req.route);
  next();
});

// Error handling middleware (should be after all routes)
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render("error", {
    message: "Something broke!",
    error: process.env.NODE_ENV === "development" ? err : {},
  });
});

// 404 handler (must be last)
app.use((req, res) => {
  console.log("\n=== 404 Not Found ===");
  console.log("URL:", req.url);
  console.log("Method:", req.method);
  res.status(404).send("Not Found");
});

// Static files
app.use("/css", express.static(path.join(__dirname, "public/css")));
app.use("/js", express.static(path.join(__dirname, "public/js")));
app.use("/images", express.static(path.join(__dirname, "public/images")));

const port = process.env.PORT || 8080;
app.listen(process.env.PORT || 8080, "0.0.0.0", () => {
  console.log(`Server running on port ${process.env.PORT || 8080}`);
});

// Add after your session middleware
const { debugSession } = require("./middleware/auth");
app.use(debugSession);

// Apply the trial restriction middleware for all routes
app.use(checkTrialRestriction);

// Register billing restrictions check middleware
app.use(checkBillingRestrictions);

// Add this line with other route imports
const xeroIntegrationRoutes = require("./routes/xeroIntegration");

// Add this before your route registrations
app.use((req, res, next) => {
  console.log("\n=== Route Registration Debug ===");
  console.log("Request URL:", req.originalUrl);
  console.log("Request Method:", req.method);
  console.log("Base URL:", req.baseUrl);
  console.log("Path:", req.path);
  console.log("Params:", req.params);
  next();
});

// Add this after your view engine setup
app.use((req, res, next) => {
  const render = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== View Rendering Debug ===");
    console.log("View:", view);
    console.log("View Path:", path.resolve(app.get("views"), view));
    console.log(
      "View exists:",
      require("fs").existsSync(path.resolve(app.get("views"), view + ".ejs"))
    );
    console.log("Options:", Object.keys(options || {}));
    render.call(this, view, options, callback);
  };
  next();
});

// Add this before your route registrations
app.use((req, res, next) => {
  console.log("\n=== Route Matching Debug ===");
  console.log({
    url: req.url,
    method: req.method,
    path: req.path,
    params: req.params,
    isAuthenticated: req.isAuthenticated(),
    user: req.user?._id,
  });
  next();
});

// Add this before the onboarding routes
app.use((req, res, next) => {
  console.log("\n=== Request Debug ===");
  console.log({
    url: req.url,
    method: req.method,
    params: req.params,
    query: req.query,
    body: req.body,
    headers: req.headers,
    cookies: req.cookies,
    session: req.session,
    isAuthenticated: req.isAuthenticated(),
    user: req.user
      ? {
          id: req.user._id,
          email: req.user.email,
          onboardingStep: req.user.onboardingStep,
        }
      : null,
  });
  next();
});

// Add after view engine setup
app.use((req, res, next) => {
  const originalRender = res.render;
  res.render = function (view, options, callback) {
    console.log("\n=== View Resolution Debug ===");
    console.log({
      view,
      viewPath: path.resolve(app.get("views"), view),
      exists: require("fs").existsSync(
        path.resolve(app.get("views"), view + ".ejs")
      ),
      options: Object.keys(options || {}),
      viewDirs: app.get("views"),
    });
    return originalRender.call(this, view, options, callback);
  };
  next();
});

// Add these session debugging middlewares
app.use((req, res, next) => {
  console.log("\n=== Session Lifecycle Debug ===");
  console.log("Session ID:", req.sessionID);
  console.log("Session Data:", req.session);
  console.log("Is New Session:", req.session.isNew);
  next();
});

app.use((req, res, next) => {
  const originalSave = req.session.save;
  req.session.save = function (cb) {
    console.log("\n=== Session Save Debug ===");
    console.log("Saving session:", req.sessionID);
    console.log("Session data:", req.session);
    return originalSave.call(this, cb);
  };
  next();
});

// Import employee portal routes
// const employeePortalRoutes = require('./routes/employeePortal');

// Add employee portal routes with logging
// app.use('/employee-portal', (req, res, next) => {
//   console.log('\n=== Employee Portal Route ===');
//   console.log('Path:', req.path);
//   console.log('Method:', req.method);
//   console.log('User:', req.user?._id);
//   next();
// }, employeePortalRoutes);

// Add this near the end of your file, before module.exports
const tokenManager = require("./services/tokenManager");
const billingService = require("./services/billingService"); // Initialize billing service

// Add these helpers to your locals
app.locals.helpers = {
  formatDate(date) {
    return moment(date).format("DD/MM/YYYY");
  },
  formatCurrency(amount) {
    return `R ${parseFloat(amount).toFixed(2)}`;
  },
  formatNumber(num) {
    return num.toLocaleString();
  },
};

const cron = require("node-cron");
const EmployeeReportGenerator = require("./services/employeeReportGenerator");
const employeeReportRoutes = require("./routes/employeeReports");

app.use(employeeReportRoutes);

// Set up scheduled job for employee report generation
cron.schedule("0 * * * *", async () => {
  console.log("Running scheduled employee report generation");
  try {
    await EmployeeReportGenerator.generateScheduledReports();
  } catch (error) {
    console.error("Error in scheduled employee report generation:", error);
  }
});

app.use("/support", supportRoutes);

console.log("\n=== Loading Routes ===");
console.log("Loading support routes...");
app.use("/support", supportRoutes);
console.log("Support routes loaded successfully");

console.log("Loading index routes...");
const indexRouter = require("./routes/index");
console.log("Index routes loaded successfully");

console.log("Loading users routes...");
const usersRouter = require("./routes/users");
console.log("Users routes loaded successfully");

console.log("Loading signatures routes...");
const signaturesRouter = require("./routes/signatures");
console.log("Signatures routes loaded successfully");

console.log("Registering routes with app...");
app.use("/", indexRouter);
app.use("/users", usersRouter);
app.use("/signatures", signaturesRouter);
console.log("All routes registered successfully");

app.use("/clients", uploadRouter); // Add this line
// Debug middleware after Xero routes
app.use((req, res, next) => {
  if (req.originalUrl.includes("/xero")) {
    console.log("=== Post-Xero Route Debug ===");
    console.log("Path:", req.path);
    console.log("Original URL:", req.originalUrl);
    console.log("Base URL:", req.baseUrl);
    console.log("Route Found:", req.route ? "Yes" : "No");
    if (req.route) {
      console.log("Route Path:", req.route.path);
      console.log("Route Methods:", Object.keys(req.route.methods));
    }
  }
  next();
});

// Update route definitions to include feature checks
app.use('/auth', authRoutes);
app.use('/api/auth', apiAuthRoutes); // Add this line
app.use('/employee-service', checkFeatureAccess('EMPLOYEE_MANAGEMENT'), employeeServiceRouter);
app.use('/payrollhub', checkFeatureAccess('PAYROLL_PROCESSING'), payrollhubRouter);
app.use('/reporting', checkFeatureAccess('REPORTING'), reportingRoutes);

// Special route handler for support token access
app.get('/clients/:companyCode', async (req, res, next) => {
  // Only handle requests with supportToken
  if (!req.query.supportToken) {
    console.log("\n=== CLIENT ROUTE - NO SUPPORT TOKEN ===");
    console.log(`Path: ${req.path}, No support token, continuing to next middleware`);
    return next();
  }
  
  console.log("\n=== CLIENT ROUTE WITH SUPPORT TOKEN ===");
  console.log(JSON.stringify({
    timestamp: new Date().toISOString(),
    path: req.path,
    originalUrl: req.originalUrl,
    companyCode: req.params.companyCode,
    supportToken: req.query.supportToken,
    session: {
      id: req.sessionID,
      cookie: req.session?.cookie ? 'Present' : 'Missing',
      passport: req.session?.passport ? 'Present' : 'Missing'
    }
  }, null, 2));

  try {
    // Get company code from URL parameters
    const companyCode = req.params.companyCode;
    console.log(`Looking for company with code: ${companyCode}`);
    
    // Try to find the company by company code (case insensitive)
    const Company = require('./models/Company');
    
    // First try exact match
    let company = await Company.findOne({ companyCode: companyCode });
    
    if (company) {
      console.log(`Company found with exact match: ${company.name} (${company._id})`);
    } else {
      console.log(`Company not found with exact match, trying case-insensitive match`);
      company = await Company.findOne({ 
        companyCode: { $regex: new RegExp(`^${companyCode}$`, 'i') } 
      });
      
      if (company) {
        console.log(`Company found with case-insensitive match: ${company.name} (${company._id})`);
      } else {
        console.log(`Company not found with any match method`);
      }
    }
    
    if (!company) {
      console.log(`Company with code ${companyCode} not found, redirecting to login`);
      req.flash('error', 'Company not found');
      return res.redirect('/login');
    }

    // Verify the support access token
    console.log(`Verifying support token: ${req.query.supportToken} for company: ${company._id}`);
    const SupportAccess = require('./models/SupportAccess');
    const supportAccess = await SupportAccess.findOne({
      token: req.query.supportToken,
      company: company._id,
      isRevoked: false,
      expiresAt: { $gt: new Date() }
    });

    if (supportAccess) {
      console.log(`Valid support access token found: ${supportAccess._id}`);
      console.log(`Token expires at: ${supportAccess.expiresAt}`);
      console.log(`Token granted by: ${supportAccess.grantedBy}`);
    } else {
      console.log(`Support token validation failed. Token not found or expired.`);
      console.log(`Queried with: token=${req.query.supportToken}, company=${company._id}, isRevoked=false, expiresAt > ${new Date()}`);
      
      // Check if token exists at all
      const anyToken = await SupportAccess.findOne({ token: req.query.supportToken });
      if (anyToken) {
        console.log(`Token exists but with different parameters:`);
        console.log(`- Company: ${anyToken.company} (expected: ${company._id})`);
        console.log(`- Is Revoked: ${anyToken.isRevoked}`);
        console.log(`- Expires At: ${anyToken.expiresAt} (current time: ${new Date()})`);
        console.log(`- Is Expired: ${anyToken.expiresAt < new Date()}`);
      } else {
        console.log(`Token does not exist in database at all`);
      }
    }

    if (!supportAccess) {
      console.log("Support access token is invalid or expired, redirecting to login");
      req.flash('error', 'Support access token is invalid or expired');
      return res.redirect('/login');
    }

    console.log("Valid support access token found, redirecting to dashboard");

    // Store support access in session for dashboard route
    req.session.supportAccess = {
      token: req.query.supportToken,
      company: company._id,
      companyCode: company.companyCode
    };
    
    // Save session before redirecting
    await new Promise((resolve, reject) => {
      req.session.save(err => {
        if (err) {
          console.error("Error saving session:", err);
          reject(err);
        } else {
          console.log("Session saved successfully with support access");
          resolve();
        }
      });
    });
    
    // Redirect to dashboard with support token
    console.log(`Redirecting to dashboard: /clients/${req.params.companyCode}/dashboard?supportToken=${req.query.supportToken}`);
    return res.redirect(`/clients/${req.params.companyCode}/dashboard?supportToken=${req.query.supportToken}`);
  } catch (error) {
    console.error("Error in support token handler:", error);
    console.error("Error stack:", error.stack);
    return res.redirect('/login');
  }
});

// Register all routes
app.use("/test", testRouter); // Add test route



// Add debug logging middleware (add this after express initialization but before session middleware)
app.use((req, res, next) => {
  console.log('\n=== Request Debug Info ===');
  console.log('Path:', req.path);
  console.log('Method:', req.method);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Cookies:', req.headers.cookie);
  next();
});

// Add this after session middleware
app.use((req, res, next) => {
  console.log('\n=== Session Debug Info ===');
  console.log('Session ID:', req.sessionID);
  console.log('Session Data:', {
    ...req.session,
    cookie: req.session?.cookie ? {
      expires: req.session.cookie.expires,
      maxAge: req.session.cookie.maxAge,
      secure: req.session.cookie.secure,
      httpOnly: req.session.cookie.httpOnly,
      domain: req.session.cookie.domain,
      sameSite: req.session.cookie.sameSite,
      path: req.session.cookie.path
    } : 'No Cookie'
  });
  console.log('Is Authenticated:', req.isAuthenticated?.());
  console.log('User:', req.user ? {
    id: req.user._id,
    email: req.user.email,
    username: req.user.username
  } : 'No User');
  
  // Monitor session save operations
  const originalSave = req.session.save;
  req.session.save = function(cb) {
    console.log('\n=== Session Save Called ===');
    console.log('Save Stack:', new Error().stack);
    console.log('Session Before Save:', {
      id: this.id,
      cookie: this.cookie,
      user: this.passport?.user
    });
    
    return originalSave.call(this, (err) => {
      console.log('\n=== Session Save Completed ===');
      console.log('Save Error:', err || 'None');
      if (cb) cb(err);
    });
  };

  // Monitor response end to log final state
  const originalEnd = res.end;
  res.end = function(...args) {
    console.log('\n=== Response End Debug ===');
    console.log('Final Status Code:', res.statusCode);
    console.log('Final Headers:', res.getHeaders());
    console.log('Session at Response End:', req.session ? {
      id: req.session.id,
      cookie: req.session.cookie,
      user: req.session.passport?.user
    } : 'No Session');
    return originalEnd.apply(this, args);
  };

  next();
});

// Add MongoDB connection state logging
mongoose.connection.on('connected', () => {
  console.log('\n=== MongoDB Connection State ===');
  console.log('Connected to MongoDB');
  console.log('Connection State:', mongoose.connection.readyState);
  console.log('Connection Host:', mongoose.connection.host);

  // CRITICAL DEBUG: Log server timezone information for production debugging
  console.log('\n=== Server Timezone Debug Info ===');
  console.log('Server Timezone (process.env.TZ):', process.env.TZ || 'Not set');
  console.log('Server Local Time:', new Date().toString());
  console.log('Server UTC Time:', new Date().toISOString());
  console.log('Moment Default Timezone:', moment.tz.guess());
  console.log('Node.js Timezone Offset (minutes):', new Date().getTimezoneOffset());
  console.log('Environment:', process.env.NODE_ENV);

  // Run timezone validation test
  const timezoneTest = validateTimezoneConsistency();
  if (!timezoneTest.isConsistent) {
    console.warn('⚠️  TIMEZONE INCONSISTENCY DETECTED - This may cause date display issues!');
  }
});

mongoose.connection.on('error', (err) => {
  console.log('\n=== MongoDB Connection Error ===');
  console.log('Error:', err);
});

// Add error logging middleware (add this after all route handlers)
app.use((err, req, res, next) => {
  console.log('\n=== Error Debug Info ===');
  console.log('Error:', err);
  console.log('Stack:', err.stack);
  console.log('Session at Error:', req.session);
  next(err);
});

// Add this BEFORE any authentication or CSRF middleware
app.get('/jwt-test', (req, res) => {
  try {
    console.log('=== JWT TEST ENDPOINT ===');
    
    // Import JWT utilities
    const { generateToken, verifyToken, extractToken } = require('./utils/jwtUtils');
    
    // Create a test user object
    const testUser = {
      _id: '123456789',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    };
    
    // Generate JWT
    const token = generateToken(testUser);
    console.log('JWT generated successfully');
    
    // Set JWT in cookie
    res.cookie('jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', 
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    console.log('JWT cookie set');
    
    // Return token
    res.json({ 
      success: true, 
      message: 'Test JWT generated',
      token
    });
  } catch (error) {
    console.error('JWT Test Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/jwt-verify', (req, res) => {
  try {
    console.log('=== JWT VERIFY ENDPOINT ===');
    
    // Import JWT utilities
    const { generateToken, verifyToken, extractToken } = require('./utils/jwtUtils');
    
    // Extract JWT from request
    const token = extractToken(req);
    
    if (!token) {
      console.log('No JWT token found');
      return res.status(401).json({ 
        success: false, 
        message: 'No token provided'
      });
    }
    
    // Verify JWT
    const decoded = verifyToken(token);
    console.log('JWT verification result:', decoded ? 'Valid' : 'Invalid');
    
    if (!decoded) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token'
      });
    }
    
    // Return decoded token
    res.json({
      success: true,
      message: 'Token verified',
      user: decoded
    });
  } catch (error) {
    console.error('JWT Verify Error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Note: API routes and authentication middleware are already configured above
// This duplicate middleware block has been removed to prevent conflicts

// Import new MongoDB utils
const { isMongoConnected } = require('./utils/mongoDbUtils');

// Import our new diagnostics and fallback routes (if still needed, though diagnostics can be accessed publicly)
const diagnosticsRoutes = require('./routes/auth/diagnostics');
// const fallbackLoginRoutes = require('./routes/auth/fallback-login'); // Removed as not needed for self-hosting

// Register diagnostic routes - these should come before auth middleware
app.use('/api/auth/diagnostics', diagnosticsRoutes);

// PRODUCTION DEBUGGING ROUTES - TEMPORARY
const debugRoutes = require("./routes/debug");
app.use('/debug', debugRoutes);
console.log("🔍 Production debug routes registered at /debug");

// Remove Vercel-specific environment debug information and fallback routes
// if (isVercelEnv) { ... } block removed

// Route registrations - auth routes should be registered next
app.use("/", authRoutes);

// Export the app
module.exports = app;
