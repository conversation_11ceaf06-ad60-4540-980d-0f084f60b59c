#!/usr/bin/env node

/**
 * Safe Console Logging Cleanup Script for app.js
 * 
 * This script safely removes console.log, console.info, console.warn, and console.debug
 * statements from app.js while preserving console.error statements and maintaining
 * code functionality.
 * 
 * Safety features:
 * - Creates backup before modification
 * - Preserves console.error statements
 * - Maintains code structure and syntax
 * - Validates syntax after changes
 * - Provides rollback capability
 */

const fs = require('fs');
const path = require('path');

class AppJsConsoleLogCleaner {
  constructor() {
    this.appJsPath = path.join(process.cwd(), 'app.js');
    this.backupPath = `${this.appJsPath}.backup.${Date.now()}`;
    this.originalContent = '';
    this.modifiedContent = '';
    this.removedLines = [];
    this.preservedLines = [];
  }

  /**
   * Main execution method
   */
  async run() {
    try {
      console.log('🧹 Starting safe console logging cleanup for app.js...\n');
      
      // Step 1: Validate file exists
      this.validateFileExists();
      
      // Step 2: Read original content
      this.readOriginalContent();
      
      // Step 3: Create backup
      this.createBackup();
      
      // Step 4: Clean console logging
      this.cleanConsoleLogging();
      
      // Step 5: Validate syntax
      await this.validateSyntax();
      
      // Step 6: Write cleaned content
      this.writeCleanedContent();
      
      // Step 7: Report results
      this.reportResults();
      
      console.log('✅ Console logging cleanup completed successfully!');
      console.log(`📁 Backup created at: ${this.backupPath}`);
      
    } catch (error) {
      console.error('❌ Error during cleanup:', error.message);
      await this.rollback();
      process.exit(1);
    }
  }

  /**
   * Validate that app.js exists
   */
  validateFileExists() {
    if (!fs.existsSync(this.appJsPath)) {
      throw new Error(`app.js not found at ${this.appJsPath}`);
    }
    console.log('✓ app.js file found');
  }

  /**
   * Read the original file content
   */
  readOriginalContent() {
    try {
      this.originalContent = fs.readFileSync(this.appJsPath, 'utf8');
      console.log(`✓ Read ${this.originalContent.split('\n').length} lines from app.js`);
    } catch (error) {
      throw new Error(`Failed to read app.js: ${error.message}`);
    }
  }

  /**
   * Create backup of original file
   */
  createBackup() {
    try {
      fs.writeFileSync(this.backupPath, this.originalContent);
      console.log('✓ Backup created successfully');
    } catch (error) {
      throw new Error(`Failed to create backup: ${error.message}`);
    }
  }

  /**
   * Clean console logging statements while preserving console.error
   */
  cleanConsoleLogging() {
    const lines = this.originalContent.split('\n');
    const cleanedLines = [];
    let inMultiLineConsole = false;
    let multiLineBuffer = '';
    let multiLineStartIndex = -1;

    console.log('🔍 Analyzing console statements...\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // Handle multi-line console statements
      if (inMultiLineConsole) {
        multiLineBuffer += '\n' + line;

        // Check if this line closes the console statement
        if (this.isConsoleStatementComplete(multiLineBuffer)) {
          inMultiLineConsole = false;

          // Process the complete multi-line console statement
          if (this.shouldPreserveLine(multiLineBuffer)) {
            // Add all lines of the multi-line statement
            const multiLines = multiLineBuffer.split('\n');
            multiLines.forEach(mLine => cleanedLines.push(mLine));

            this.preservedLines.push({
              lineNumber: multiLineStartIndex,
              content: multiLineBuffer.trim(),
              reason: this.getPreservationReason(multiLineBuffer)
            });
          } else {
            // Remove the multi-line console statement
            this.removedLines.push({
              lineNumber: multiLineStartIndex,
              content: multiLineBuffer.trim()
            });
          }

          multiLineBuffer = '';
          multiLineStartIndex = -1;
          continue;
        } else {
          // Still in multi-line, continue to next line
          continue;
        }
      }

      // Check if line contains console statements
      if (this.containsConsoleStatement(line)) {
        // Check if this starts a multi-line console statement
        if (this.startsMultiLineConsole(line)) {
          inMultiLineConsole = true;
          multiLineBuffer = line;
          multiLineStartIndex = lineNumber;
          continue;
        }

        if (this.shouldPreserveLine(line)) {
          // Preserve console.error and critical console statements
          cleanedLines.push(line);
          this.preservedLines.push({
            lineNumber,
            content: line.trim(),
            reason: this.getPreservationReason(line)
          });
        } else {
          // Remove console.log, console.info, console.warn, console.debug
          this.removedLines.push({
            lineNumber,
            content: line.trim()
          });

          // Check if the line is only whitespace + console statement
          const trimmedLine = line.trim();
          if (trimmedLine.match(/^console\.(log|info|warn|debug)/)) {
            // Skip the line entirely (remove it)
            continue;
          } else {
            // Line has other content, just remove the console part
            const cleanedLine = this.removeConsoleFromLine(line);
            if (cleanedLine.trim()) {
              cleanedLines.push(cleanedLine);
            }
          }
        }
      } else {
        // Keep non-console lines as-is
        cleanedLines.push(line);
      }
    }

    this.modifiedContent = cleanedLines.join('\n');
    console.log(`✓ Console logging analysis complete`);
  }

  /**
   * Check if line contains console statement
   */
  containsConsoleStatement(line) {
    return /console\.(log|info|warn|debug|error)/.test(line);
  }

  /**
   * Determine if line should be preserved
   */
  shouldPreserveLine(line) {
    // Always preserve console.error
    if (/console\.error/.test(line)) {
      return true;
    }
    
    // Preserve console statements in critical error handling contexts
    if (this.isInCriticalContext(line)) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if console statement is in critical context
   */
  isInCriticalContext(line) {
    // Preserve console statements that are part of error handling
    const criticalPatterns = [
      /catch\s*\(/,
      /error/i,
      /fail/i,
      /exception/i
    ];
    
    return criticalPatterns.some(pattern => pattern.test(line));
  }

  /**
   * Get reason for preserving a line
   */
  getPreservationReason(line) {
    if (/console\.error/.test(line)) {
      return 'console.error statement';
    }
    if (this.isInCriticalContext(line)) {
      return 'critical error handling context';
    }
    return 'unknown';
  }

  /**
   * Check if line starts a multi-line console statement
   */
  startsMultiLineConsole(line) {
    const trimmedLine = line.trim();

    // Check if it's a console statement that doesn't end with );
    if (trimmedLine.match(/^console\.(log|info|warn|debug)/)) {
      // Count parentheses to see if statement is complete
      const openParens = (line.match(/\(/g) || []).length;
      const closeParens = (line.match(/\)/g) || []).length;

      // If more open than close parens, it's likely multi-line
      if (openParens > closeParens) {
        return true;
      }

      // Check if line ends with comma or open brace (object literal)
      if (trimmedLine.endsWith(',') || trimmedLine.endsWith('{')) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if multi-line console statement is complete
   */
  isConsoleStatementComplete(multiLineStatement) {
    // Count parentheses
    const openParens = (multiLineStatement.match(/\(/g) || []).length;
    const closeParens = (multiLineStatement.match(/\)/g) || []).length;

    // Statement is complete when parentheses are balanced and ends with );
    return openParens === closeParens && multiLineStatement.trim().endsWith(');');
  }

  /**
   * Remove console statement from line while preserving other content
   */
  removeConsoleFromLine(line) {
    // Handle multi-line console statements more carefully
    const trimmedLine = line.trim();

    // If line starts with console statement, remove entire line
    if (trimmedLine.match(/^console\.(log|info|warn|debug)/)) {
      return '';
    }

    // For inline console statements, try to remove them carefully
    let cleanedLine = line;

    // Remove simple console statements
    cleanedLine = cleanedLine.replace(/console\.(log|info|warn|debug)\([^)]*\);?\s*/g, '');

    // Handle console statements with object literals more carefully
    cleanedLine = cleanedLine.replace(/console\.(log|info|warn|debug)\s*\(\s*['"][^'"]*['"],?\s*\{[^}]*\}\s*\);?\s*/g, '');

    return cleanedLine;
  }

  /**
   * Validate syntax of modified content
   */
  async validateSyntax() {
    console.log('🔍 Validating syntax...');
    
    try {
      // Write to temporary file for syntax validation
      const tempFile = `${this.appJsPath}.temp`;
      fs.writeFileSync(tempFile, this.modifiedContent);
      
      // Try to require the file to check for syntax errors
      delete require.cache[path.resolve(tempFile)];
      
      // Clean up temp file
      fs.unlinkSync(tempFile);
      
      console.log('✓ Syntax validation passed');
    } catch (error) {
      throw new Error(`Syntax validation failed: ${error.message}`);
    }
  }

  /**
   * Write cleaned content to file
   */
  writeCleanedContent() {
    try {
      fs.writeFileSync(this.appJsPath, this.modifiedContent);
      console.log('✓ Cleaned content written to app.js');
    } catch (error) {
      throw new Error(`Failed to write cleaned content: ${error.message}`);
    }
  }

  /**
   * Report cleanup results
   */
  reportResults() {
    console.log('\n📊 Cleanup Results:');
    console.log('==================');
    console.log(`📝 Total lines processed: ${this.originalContent.split('\n').length}`);
    console.log(`🗑️  Console statements removed: ${this.removedLines.length}`);
    console.log(`🛡️  Console statements preserved: ${this.preservedLines.length}`);
    
    if (this.removedLines.length > 0) {
      console.log('\n🗑️  Removed console statements:');
      this.removedLines.slice(0, 10).forEach(item => {
        console.log(`   Line ${item.lineNumber}: ${item.content}`);
      });
      if (this.removedLines.length > 10) {
        console.log(`   ... and ${this.removedLines.length - 10} more`);
      }
    }
    
    if (this.preservedLines.length > 0) {
      console.log('\n🛡️  Preserved console statements:');
      this.preservedLines.forEach(item => {
        console.log(`   Line ${item.lineNumber}: ${item.content} (${item.reason})`);
      });
    }
  }

  /**
   * Rollback changes in case of error
   */
  async rollback() {
    if (fs.existsSync(this.backupPath)) {
      try {
        const backupContent = fs.readFileSync(this.backupPath, 'utf8');
        fs.writeFileSync(this.appJsPath, backupContent);
        console.log('🔄 Rollback completed - original content restored');
      } catch (error) {
        console.error('❌ Rollback failed:', error.message);
      }
    }
  }
}

// Execute if run directly
if (require.main === module) {
  const cleaner = new AppJsConsoleLogCleaner();
  cleaner.run().catch(error => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}

module.exports = AppJsConsoleLogCleaner;
