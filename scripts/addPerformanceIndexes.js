const mongoose = require('mongoose');
require('dotenv').config();

/**
 * <PERSON><PERSON><PERSON> to add performance indexes for PandaPayroll application
 * Run this script to optimize database queries for better performance
 */

async function createIndexSafely(collection, indexSpec, options, description) {
  try {
    await collection.createIndex(indexSpec, options);
    console.log(`✓ Added ${description}`);
  } catch (error) {
    if (error.code === 85) { // IndexOptionsConflict
      console.log(`⚠ Index already exists: ${description}`);
    } else {
      console.log(`✗ Failed to add ${description}: ${error.message}`);
    }
  }
}

async function addPerformanceIndexes() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;

    console.log('\n=== Adding Performance Indexes ===\n');

    // Employee collection indexes
    console.log('Adding indexes for Employee collection...');
    
    // Compound index for company + status (most common query pattern)
    await createIndexSafely(
      db.collection('employees'),
      { company: 1, status: 1 },
      { name: 'company_status_idx', background: true },
      'compound index: company + status'
    );

    // Compound index for company + doa (for joiners queries)
    await createIndexSafely(
      db.collection('employees'),
      { company: 1, doa: 1 },
      { name: 'company_doa_idx', background: true },
      'compound index: company + doa'
    );

    // Compound index for company + endServiceDate (for terminations queries)
    await db.collection('employees').createIndex(
      { company: 1, endServiceDate: 1 },
      { 
        name: 'company_endServiceDate_idx',
        background: true,
        sparse: true // Only index documents that have endServiceDate
      }
    );
    console.log('✓ Added compound index: company + endServiceDate');

    // Compound index for company + costCentre (for cost center distribution)
    await db.collection('employees').createIndex(
      { company: 1, costCentre: 1 },
      { 
        name: 'company_costCentre_idx',
        background: true,
        sparse: true
      }
    );
    console.log('✓ Added compound index: company + costCentre');

    // Index for employee lookup by companyEmployeeNumber within company
    await db.collection('employees').createIndex(
      { company: 1, companyEmployeeNumber: 1 },
      { 
        name: 'company_empNumber_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: company + companyEmployeeNumber');

    // Index for employee name searches within company
    await db.collection('employees').createIndex(
      { company: 1, lastName: 1, firstName: 1 },
      { 
        name: 'company_name_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: company + lastName + firstName');

    // PayrollPeriod collection indexes
    console.log('\nAdding indexes for PayrollPeriod collection...');
    
    // Compound index for employee + period lookups
    await createIndexSafely(
      db.collection('payrollperiods'),
      { employee: 1, startDate: 1, endDate: 1 },
      { name: 'employee_period_idx', background: true },
      'compound index: employee + startDate + endDate'
    );

    // Index for company-wide period queries
    await db.collection('payrollperiods').createIndex(
      { company: 1, startDate: 1 },
      { 
        name: 'company_startDate_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: company + startDate');

    // Payslip collection indexes
    console.log('\nAdding indexes for Payslip collection...');
    
    // Compound index for employee + period + finalized status
    await db.collection('payslips').createIndex(
      { employee: 1, period: 1, isFinalized: 1 },
      { 
        name: 'employee_period_finalized_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: employee + period + isFinalized');

    // Index for company-wide payslip queries
    await db.collection('payslips').createIndex(
      { company: 1, period: 1 },
      { 
        name: 'company_period_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: company + period');

    // Notification collection indexes
    console.log('\nAdding indexes for Notification collection...');
    
    // Index for latest notification queries
    await db.collection('notifications').createIndex(
      { company: 1, createdAt: -1 },
      { 
        name: 'company_createdAt_desc_idx',
        background: true 
      }
    );
    console.log('✓ Added compound index: company + createdAt (desc)');

    // Company collection indexes
    console.log('\nAdding indexes for Company collection...');
    
    // Index for company code lookups
    await db.collection('companies').createIndex(
      { companyCode: 1 },
      { 
        name: 'companyCode_idx',
        background: true,
        unique: true
      }
    );
    console.log('✓ Added unique index: companyCode');

    // User collection indexes
    console.log('\nAdding indexes for User collection...');
    
    // Index for user companies array
    await db.collection('users').createIndex(
      { 'companies._id': 1 },
      { 
        name: 'user_companies_idx',
        background: true 
      }
    );
    console.log('✓ Added index: companies._id');

    // Session collection indexes (if using MongoDB sessions)
    console.log('\nAdding indexes for Session collection...');
    
    // TTL index for session expiration
    await db.collection('sessions').createIndex(
      { expires: 1 },
      { 
        name: 'session_expires_ttl_idx',
        background: true,
        expireAfterSeconds: 0 // MongoDB will use the expires field value
      }
    );
    console.log('✓ Added TTL index: expires');

    console.log('\n=== Performance Indexes Added Successfully ===');
    console.log('\nIndexes created:');
    console.log('- Employee: 6 compound indexes for optimized queries');
    console.log('- PayrollPeriod: 2 compound indexes for period lookups');
    console.log('- Payslip: 2 compound indexes for payslip queries');
    console.log('- Notification: 1 index for latest notification queries');
    console.log('- Company: 1 unique index for company code lookups');
    console.log('- User: 1 index for company associations');
    console.log('- Session: 1 TTL index for automatic cleanup');

    console.log('\nThese indexes will significantly improve query performance for:');
    console.log('- Dashboard data loading');
    console.log('- Employee management operations');
    console.log('- Payroll period calculations');
    console.log('- Company-specific data filtering');
    console.log('- Session management');

  } catch (error) {
    console.error('Error adding performance indexes:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  addPerformanceIndexes();
}

module.exports = addPerformanceIndexes;
