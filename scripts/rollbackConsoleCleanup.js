#!/usr/bin/env node

/**
 * Emergency Rollback Script for Console Cleanup
 * 
 * This script will restore all files that were modified during the console cleanup
 * by finding their backup files and restoring the original content.
 * 
 * Safety features:
 * - Finds all backup files created by the cleanup script
 * - Validates backup files before restoration
 * - Reports all restoration actions
 * - Preserves backup files for safety
 */

const fs = require('fs');
const path = require('path');

class ConsoleCleanupRollback {
  constructor() {
    this.restoredFiles = [];
    this.errors = [];
    this.backupFiles = [];
  }

  /**
   * Main execution method
   */
  async run() {
    try {
      console.log('🔄 Starting emergency rollback of console cleanup changes...\n');
      
      // Step 1: Find all backup files
      this.findBackupFiles();
      
      // Step 2: Restore files from backups
      this.restoreFiles();
      
      // Step 3: Report results
      this.reportResults();
      
      console.log('\n✅ Rollback completed successfully!');
      
    } catch (error) {
      console.error('❌ Error during rollback:', error.message);
      process.exit(1);
    }
  }

  /**
   * Find all backup files created by the cleanup script
   */
  findBackupFiles() {
    console.log('🔍 Searching for backup files...');
    
    const directories = ['routes', 'services', 'utils', '.'];
    
    directories.forEach(dir => {
      const dirPath = path.join(process.cwd(), dir);
      
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        
        files.forEach(file => {
          // Look for backup files with timestamp pattern
          if (file.match(/\.backup\.\d+$/)) {
            const backupPath = path.join(dirPath, file);
            const originalPath = backupPath.replace(/\.backup\.\d+$/, '');
            
            this.backupFiles.push({
              backupPath,
              originalPath,
              directory: dir
            });
          }
        });
      }
    });
    
    console.log(`✓ Found ${this.backupFiles.length} backup files`);
  }

  /**
   * Restore files from their backups
   */
  restoreFiles() {
    console.log('\n🔄 Restoring files from backups...\n');
    
    this.backupFiles.forEach(({ backupPath, originalPath, directory }) => {
      try {
        // Read backup content
        const backupContent = fs.readFileSync(backupPath, 'utf8');
        
        // Validate backup content
        if (backupContent.length === 0) {
          throw new Error('Backup file is empty');
        }
        
        // Restore original file
        fs.writeFileSync(originalPath, backupContent);
        
        const relativePath = path.relative(process.cwd(), originalPath);
        console.log(`✓ Restored: ${relativePath}`);
        
        this.restoredFiles.push({
          originalPath: relativePath,
          backupPath: path.relative(process.cwd(), backupPath),
          directory
        });
        
      } catch (error) {
        const relativePath = path.relative(process.cwd(), originalPath);
        console.error(`❌ Failed to restore ${relativePath}: ${error.message}`);
        
        this.errors.push({
          file: relativePath,
          error: error.message
        });
      }
    });
  }

  /**
   * Report rollback results
   */
  reportResults() {
    console.log('\n📊 Rollback Results:');
    console.log('===================');
    console.log(`✅ Files restored: ${this.restoredFiles.length}`);
    console.log(`❌ Restoration errors: ${this.errors.length}`);
    
    if (this.restoredFiles.length > 0) {
      console.log('\n✅ Successfully restored files:');
      
      // Group by directory
      const byDirectory = {};
      this.restoredFiles.forEach(file => {
        if (!byDirectory[file.directory]) {
          byDirectory[file.directory] = [];
        }
        byDirectory[file.directory].push(file);
      });
      
      Object.keys(byDirectory).forEach(dir => {
        console.log(`\n📁 ${dir}/:`);
        byDirectory[dir].forEach(file => {
          console.log(`   ✓ ${file.originalPath}`);
        });
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Restoration errors:');
      this.errors.forEach(error => {
        console.log(`   ❌ ${error.file}: ${error.error}`);
      });
    }
    
    console.log('\n📝 Note: Backup files have been preserved for safety');
    console.log('💡 You can manually delete backup files once you confirm everything works correctly');
  }
}

// Execute if run directly
if (require.main === module) {
  const rollback = new ConsoleCleanupRollback();
  rollback.run().catch(error => {
    console.error('Rollback script execution failed:', error);
    process.exit(1);
  });
}

module.exports = ConsoleCleanupRollback;
