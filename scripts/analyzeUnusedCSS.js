#!/usr/bin/env node

/**
 * Analyze and identify unused CSS files in PandaPayroll
 * This script will help us achieve the 105 KiB savings from removing unused CSS
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class UnusedCSSAnalyzer {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.cssFiles = new Set();
    this.referencedFiles = new Set();
    this.templateFiles = [];
    this.unusedFiles = [];
    this.totalSavings = 0;
  }

  async analyze() {
    console.log('🎨 Analyzing CSS usage in PandaPayroll...\n');
    
    // Step 1: Find all CSS files
    await this.findAllCSSFiles();
    
    // Step 2: Find all template files
    await this.findTemplateFiles();
    
    // Step 3: Scan templates for CSS references
    await this.scanTemplatesForCSSReferences();
    
    // Step 4: Identify unused files
    await this.identifyUnusedFiles();
    
    // Step 5: Calculate potential savings
    await this.calculateSavings();
    
    // Step 6: Generate report
    this.generateReport();
    
    // Step 7: Create removal script
    await this.createRemovalScript();
  }

  async findAllCSSFiles() {
    const cssPattern = path.join(this.projectRoot, 'public/css/**/*.css');
    const files = glob.sync(cssPattern);
    
    files.forEach(file => {
      const relativePath = path.relative(this.projectRoot, file);
      this.cssFiles.add(relativePath);
    });
    
    console.log(`📁 Found ${this.cssFiles.size} CSS files`);
  }

  async findTemplateFiles() {
    const templatePatterns = [
      path.join(this.projectRoot, 'views/**/*.ejs'),
      path.join(this.projectRoot, 'views/**/*.html'),
      path.join(this.projectRoot, 'public/**/*.html')
    ];
    
    templatePatterns.forEach(pattern => {
      const files = glob.sync(pattern);
      this.templateFiles.push(...files);
    });
    
    console.log(`📄 Found ${this.templateFiles.length} template files`);
  }

  async scanTemplatesForCSSReferences() {
    console.log('🔍 Scanning templates for CSS references...');
    
    for (const templateFile of this.templateFiles) {
      try {
        const content = fs.readFileSync(templateFile, 'utf8');
        
        // Look for link rel="stylesheet" references
        const linkMatches = content.match(/href\s*=\s*["']([^"']*\.css[^"']*)["']/gi);
        
        if (linkMatches) {
          linkMatches.forEach(match => {
            const hrefMatch = match.match(/href\s*=\s*["']([^"']*)["']/i);
            if (hrefMatch) {
              let cssPath = hrefMatch[1];
              
              // Normalize path
              if (cssPath.startsWith('/css/')) {
                cssPath = 'public' + cssPath;
              } else if (cssPath.startsWith('./css/')) {
                cssPath = 'public/css/' + cssPath.substring(6);
              } else if (!cssPath.startsWith('http') && cssPath.includes('.css')) {
                // Local file
                if (!cssPath.startsWith('public/')) {
                  cssPath = 'public/css/' + path.basename(cssPath);
                }
              }
              
              if (!cssPath.startsWith('http')) {
                this.referencedFiles.add(cssPath);
              }
            }
          });
        }
        
        // Look for dynamic CSS loading
        const dynamicMatches = content.match(/loadCSS\s*\(\s*["']([^"']*\.css[^"']*)["']/gi);
        if (dynamicMatches) {
          dynamicMatches.forEach(match => {
            const pathMatch = match.match(/["']([^"']*)["']/);
            if (pathMatch) {
              let cssPath = pathMatch[1];
              if (cssPath.startsWith('/css/')) {
                cssPath = 'public' + cssPath;
              }
              this.referencedFiles.add(cssPath);
            }
          });
        }
        
      } catch (error) {
        console.warn(`⚠️  Could not read ${templateFile}: ${error.message}`);
      }
    }
    
    console.log(`📎 Found ${this.referencedFiles.size} referenced CSS files`);
  }

  async identifyUnusedFiles() {
    // Core files that should never be removed
    const coreFiles = new Set([
      'public/css/styles.css',
      'public/css/dashboard.css',
      'public/css/header.css',
      'public/css/sidebar.css',
      'public/css/critical.css',
      'public/css/toast.css'
    ]);
    
    // Files that are likely unused based on analysis
    const likelyUnused = [
      'public/css/customs.css', // Generic name, likely unused
      'public/css/button.css', // Likely consolidated into styles.css
      'public/css/profile.css', // Generic, may be replaced
      'public/css/deleteEmployee.css', // Specific functionality
      'public/css/endService.css', // Duplicate of end-service.css
      'public/css/enhanced-progress-tracker.css', // Enhanced version exists
      'public/css/improved-progress-tracker.css', // Improved version exists
      'public/css/new-employee-profile.css', // New version, check if old exists
      'public/css/new-forms-design.css', // New design, check if used
      'public/css/new-settings.css', // New settings, check if used
      'public/css/payfrequency.css', // Specific functionality
      'public/css/regularinputs.css', // Specific functionality
      'public/css/reinstate.css', // Duplicate of reinstate-employee.css
      'public/css/syne-font.css', // Font loading, may be handled elsewhere
      'public/css/toggle-switch.css', // Specific component
      'public/css/transfer.css', // Specific functionality
    ];
    
    this.cssFiles.forEach(file => {
      if (!this.referencedFiles.has(file) && !coreFiles.has(file)) {
        this.unusedFiles.push({
          path: file,
          likely: likelyUnused.includes(file),
          size: this.getFileSize(file)
        });
      }
    });
    
    console.log(`🗑️  Identified ${this.unusedFiles.length} potentially unused files`);
  }

  getFileSize(filePath) {
    try {
      const fullPath = path.join(this.projectRoot, filePath);
      const stats = fs.statSync(fullPath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  async calculateSavings() {
    this.totalSavings = this.unusedFiles.reduce((total, file) => total + file.size, 0);
    console.log(`💾 Potential savings: ${(this.totalSavings / 1024).toFixed(2)} KB`);
  }

  generateReport() {
    console.log('\n📊 UNUSED CSS ANALYSIS REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n📁 Total CSS files: ${this.cssFiles.size}`);
    console.log(`📎 Referenced files: ${this.referencedFiles.size}`);
    console.log(`🗑️  Unused files: ${this.unusedFiles.length}`);
    console.log(`💾 Total potential savings: ${(this.totalSavings / 1024).toFixed(2)} KB`);
    
    if (this.unusedFiles.length > 0) {
      console.log('\n🗑️  UNUSED FILES:');
      this.unusedFiles
        .sort((a, b) => b.size - a.size)
        .forEach(file => {
          const sizeKB = (file.size / 1024).toFixed(2);
          const status = file.likely ? '🔴 LIKELY SAFE TO REMOVE' : '🟡 REVIEW NEEDED';
          console.log(`  ${status} - ${file.path} (${sizeKB} KB)`);
        });
    }
    
    console.log('\n📎 REFERENCED FILES:');
    Array.from(this.referencedFiles)
      .sort()
      .forEach(file => {
        console.log(`  ✅ ${file}`);
      });
  }

  async createRemovalScript() {
    const scriptContent = `#!/usr/bin/env node

/**
 * Remove unused CSS files
 * Generated by analyzeUnusedCSS.js
 * 
 * IMPORTANT: Review this script before running!
 */

const fs = require('fs');
const path = require('path');

const filesToRemove = [
${this.unusedFiles
  .filter(file => file.likely)
  .map(file => `  '${file.path}', // ${(file.size / 1024).toFixed(2)} KB`)
  .join('\n')}
];

const filesToReview = [
${this.unusedFiles
  .filter(file => !file.likely)
  .map(file => `  '${file.path}', // ${(file.size / 1024).toFixed(2)} KB - REVIEW NEEDED`)
  .join('\n')}
];

console.log('🗑️  Removing unused CSS files...');

let totalSaved = 0;

filesToRemove.forEach(file => {
  try {
    const fullPath = path.join(__dirname, '..', file);
    const stats = fs.statSync(fullPath);
    fs.unlinkSync(fullPath);
    totalSaved += stats.size;
    console.log(\`✅ Removed: \${file} (\${(stats.size / 1024).toFixed(2)} KB)\`);
  } catch (error) {
    console.warn(\`⚠️  Could not remove \${file}: \${error.message}\`);
  }
});

console.log(\`\\n💾 Total space saved: \${(totalSaved / 1024).toFixed(2)} KB\`);

if (filesToReview.length > 0) {
  console.log('\\n🟡 FILES THAT NEED MANUAL REVIEW:');
  filesToReview.forEach(file => {
    console.log(\`  - \${file}\`);
  });
}
`;

    const scriptPath = path.join(this.projectRoot, 'scripts/removeUnusedCSS.js');
    fs.writeFileSync(scriptPath, scriptContent);
    fs.chmodSync(scriptPath, '755');
    
    console.log(`\n📝 Created removal script: ${scriptPath}`);
    console.log('⚠️  IMPORTANT: Review the script before running it!');
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new UnusedCSSAnalyzer();
  analyzer.analyze().catch(console.error);
}

module.exports = UnusedCSSAnalyzer;
