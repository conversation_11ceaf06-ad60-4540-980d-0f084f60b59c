#!/usr/bin/env node

/**
 * Remove unused CSS files
 * Generated by analyzeUnusedCSS.js
 * 
 * IMPORTANT: Review this script before running!
 */

const fs = require('fs');
const path = require('path');

const filesToRemove = [
  'public/css/syne-font.css', // 1.67 KB
  'public/css/new-forms-design.css', // 1.63 KB
  'public/css/button.css', // 0.88 KB
  'public/css/payfrequency.css', // 0.42 KB
];

const filesToReview = [
  'public/css/add-employee-details.min.css', // 43.82 KB - REVIEW NEEDED
  'public/css/dashboard.min.css', // 34.14 KB - REVIEW NEEDED
  'public/css/payrollhub.min.css', // 25.13 KB - REVIEW NEEDED
  'public/css/new-employee-profile.min.css', // 23.89 KB - REVIEW NEEDED
  'public/css/employeeProfile.min.css', // 21.85 KB - REVIEW NEEDED
  'public/css/payslip-review-modal.min.css', // 18.57 KB - REVIEW NEEDED
  'public/css/reporting.min.css', // 15.20 KB - REVIEW NEEDED
  'public/css/settings.min.css', // 14.48 KB - REVIEW NEEDED
  'public/css/filing.min.css', // 13.46 KB - REVIEW NEEDED
  'public/css/enhanced-dashboard-cards.min.css', // 9.44 KB - REVIEW NEEDED
  'public/css/mobile-add-employee-details.min.css', // 9.17 KB - REVIEW NEEDED
  'public/css/employeeManagement.min.css', // 8.90 KB - REVIEW NEEDED
  'public/css/eft-settings.min.css', // 8.80 KB - REVIEW NEEDED
  'public/css/transfer.min.css', // 8.74 KB - REVIEW NEEDED
  'public/css/enhanced-progress-tracker.min.css', // 8.46 KB - REVIEW NEEDED
  'public/css/accounting-settings.min.css', // 7.34 KB - REVIEW NEEDED
  'public/css/assignRole.min.css', // 7.24 KB - REVIEW NEEDED
  'public/css/profile.min.css', // 7.02 KB - REVIEW NEEDED
  'public/css/employer-details.min.css', // 6.55 KB - REVIEW NEEDED
  'public/css/payrun.min.css', // 5.89 KB - REVIEW NEEDED
  'public/css/custom-items.min.css', // 5.64 KB - REVIEW NEEDED
  'public/css/sidebar.min.css', // 5.59 KB - REVIEW NEEDED
  'public/css/edit-employee-details.min.css', // 5.47 KB - REVIEW NEEDED
  'public/css/onboarding.min.css', // 5.41 KB - REVIEW NEEDED
  'public/css/addBasicSalary.min.css', // 5.30 KB - REVIEW NEEDED
  'public/css/billing.min.css', // 5.04 KB - REVIEW NEEDED
  'public/css/bulk-add-employees.min.css', // 4.93 KB - REVIEW NEEDED
  'public/css/event-modals.min.css', // 4.85 KB - REVIEW NEEDED
  'public/css/event-modals.css', // 4.85 KB - REVIEW NEEDED
  'public/css/employee-forms.min.css', // 4.81 KB - REVIEW NEEDED
  'public/css/employee-numbers.min.css', // 4.75 KB - REVIEW NEEDED
  'public/css/companies.min.css', // 4.43 KB - REVIEW NEEDED
  'public/css/customs.min.css', // 4.40 KB - REVIEW NEEDED
  'public/css/eti.min.css', // 4.24 KB - REVIEW NEEDED
  'public/css/once-off-items.min.css', // 4.15 KB - REVIEW NEEDED
  'public/css/deleteEmployee.min.css', // 4.07 KB - REVIEW NEEDED
  'public/css/billing-summary.min.css', // 3.88 KB - REVIEW NEEDED
  'public/css/billing-summary.css', // 3.88 KB - REVIEW NEEDED
  'public/css/improved-progress-tracker.min.css', // 3.63 KB - REVIEW NEEDED
  'public/css/end-service.min.css', // 3.63 KB - REVIEW NEEDED
  'public/css/mobile-payroll.min.css', // 3.48 KB - REVIEW NEEDED
  'public/css/logo-upload.min.css', // 3.38 KB - REVIEW NEEDED
  'public/css/critical.min.css', // 3.36 KB - REVIEW NEEDED
  'public/css/self-service.min.css', // 3.33 KB - REVIEW NEEDED
  'public/css/self-service.css', // 3.33 KB - REVIEW NEEDED
  'public/css/skillsEquity.min.css', // 3.32 KB - REVIEW NEEDED
  'public/css/header.min.css', // 3.31 KB - REVIEW NEEDED
  'public/css/notifications.min.css', // 3.23 KB - REVIEW NEEDED
  'public/css/fund-forms.min.css', // 3.21 KB - REVIEW NEEDED
  'public/css/fund-forms.css', // 3.21 KB - REVIEW NEEDED
  'public/css/xero-mapping.min.css', // 2.87 KB - REVIEW NEEDED
  'public/css/mobile-basic-salary.min.css', // 2.70 KB - REVIEW NEEDED
  'public/css/mobile-basic-salary.css', // 2.70 KB - REVIEW NEEDED
  'public/css/mobile-employee.min.css', // 2.69 KB - REVIEW NEEDED
  'public/css/employer-filing-details.min.css', // 2.65 KB - REVIEW NEEDED
  'public/css/employer-filing-details.css', // 2.65 KB - REVIEW NEEDED
  'public/css/pay-runs-table.min.css', // 2.63 KB - REVIEW NEEDED
  'public/css/mobile-filing.min.css', // 2.60 KB - REVIEW NEEDED
  'public/css/defineRFI.min.css', // 2.57 KB - REVIEW NEEDED
  'public/css/defineRFI.css', // 2.57 KB - REVIEW NEEDED
  'public/css/progress-tracker.min.css', // 2.54 KB - REVIEW NEEDED
  'public/css/index.min.css', // 2.48 KB - REVIEW NEEDED
  'public/css/payslipInput.min.css', // 2.48 KB - REVIEW NEEDED
  'public/css/payslipInput.css', // 2.48 KB - REVIEW NEEDED
  'public/css/regularinputs.min.css', // 2.45 KB - REVIEW NEEDED
  'public/css/new-settings.min.css', // 2.45 KB - REVIEW NEEDED
  'public/css/payrun-wizard.min.css', // 2.41 KB - REVIEW NEEDED
  'public/css/endService.min.css', // 2.41 KB - REVIEW NEEDED
  'public/css/reinstate.min.css', // 2.22 KB - REVIEW NEEDED
  'public/css/styles.min.css', // 2.17 KB - REVIEW NEEDED
  'public/css/employee-profile.min.css', // 2.14 KB - REVIEW NEEDED
  'public/css/ui19-form.min.css', // 2.08 KB - REVIEW NEEDED
  'public/css/modals.min.css', // 2.04 KB - REVIEW NEEDED
  'public/css/ui19-signature.min.css', // 1.95 KB - REVIEW NEEDED
  'public/css/payslip-export.min.css', // 1.81 KB - REVIEW NEEDED
  'public/css/update-termination.min.css', // 1.79 KB - REVIEW NEEDED
  'public/css/reinstate-employee.min.css', // 1.79 KB - REVIEW NEEDED
  'public/css/pay-runs-card.min.css', // 1.75 KB - REVIEW NEEDED
  'public/css/forms.min.css', // 1.71 KB - REVIEW NEEDED
  'public/css/syne-font.min.css', // 1.67 KB - REVIEW NEEDED
  'public/css/bi-annual-filing.min.css', // 1.65 KB - REVIEW NEEDED
  'public/css/bi-annual-filing.css', // 1.65 KB - REVIEW NEEDED
  'public/css/new-forms-design.min.css', // 1.63 KB - REVIEW NEEDED
  'public/css/mobile-header.min.css', // 1.63 KB - REVIEW NEEDED
  'public/css/mobile-employee-profile.min.css', // 1.62 KB - REVIEW NEEDED
  'public/css/undo-end-of-service.min.css', // 1.48 KB - REVIEW NEEDED
  'public/css/toggle-switch.min.css', // 1.47 KB - REVIEW NEEDED
  'public/css/toast.min.css', // 1.39 KB - REVIEW NEEDED
  'public/css/mobile-self-service.min.css', // 1.34 KB - REVIEW NEEDED
  'public/css/mobile-nav.min.css', // 1.27 KB - REVIEW NEEDED
  'public/css/mobile-bulk-actions.min.css', // 1.25 KB - REVIEW NEEDED
  'public/css/mobile-bulk-actions.css', // 1.25 KB - REVIEW NEEDED
  'public/css/responsive-dashboard.min.css', // 1.23 KB - REVIEW NEEDED
  'public/css/responsive-dashboard.css', // 1.23 KB - REVIEW NEEDED
  'public/css/mobile-reporting.min.css', // 1.15 KB - REVIEW NEEDED
  'public/css/alerts.min.css', // 1.11 KB - REVIEW NEEDED
  'public/css/tabs.min.css', // 1.05 KB - REVIEW NEEDED
  'public/css/signature-pad.min.css', // 0.99 KB - REVIEW NEEDED
  'public/css/button.min.css', // 0.88 KB - REVIEW NEEDED
  'public/css/mobile-companies.min.css', // 0.87 KB - REVIEW NEEDED
  'public/css/feature-status.min.css', // 0.82 KB - REVIEW NEEDED
  'public/css/pagination.min.css', // 0.74 KB - REVIEW NEEDED
  'public/css/payfrequency.min.css', // 0.42 KB - REVIEW NEEDED
  'public/css/oid-return.css', // 0.00 KB - REVIEW NEEDED
];

console.log('🗑️  Removing unused CSS files...');

let totalSaved = 0;

filesToRemove.forEach(file => {
  try {
    const fullPath = path.join(__dirname, '..', file);
    const stats = fs.statSync(fullPath);
    fs.unlinkSync(fullPath);
    totalSaved += stats.size;
    console.log(`✅ Removed: ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
  } catch (error) {
    console.warn(`⚠️  Could not remove ${file}: ${error.message}`);
  }
});

console.log(`\n💾 Total space saved: ${(totalSaved / 1024).toFixed(2)} KB`);

if (filesToReview.length > 0) {
  console.log('\n🟡 FILES THAT NEED MANUAL REVIEW:');
  filesToReview.forEach(file => {
    console.log(`  - ${file}`);
  });
}
