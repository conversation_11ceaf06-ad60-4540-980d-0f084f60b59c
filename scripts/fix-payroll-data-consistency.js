#!/usr/bin/env node

/**
 * Data Consistency Fix Script for PandaPayroll
 * 
 * This script fixes the payroll data consistency issue where:
 * 1. PayrollPeriod records exist but Payroll records lack proper payrollPeriod references
 * 2. Payroll records have incorrect month values
 * 3. Payroll records lack realistic financial data for testing
 * 
 * Usage: node scripts/fix-payroll-data-consistency.js [--dry-run] [--employee-id=ID]
 */

const mongoose = require('mongoose');
const Payroll = require('../models/Payroll');
const PayrollPeriod = require('../models/PayrollPeriod');
const Employee = require('../models/Employee');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://premierstaffingsolutionsgroup:<EMAIL>/mydatabase?retryWrites=true&w=majority';

// Command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const employeeIdArg = args.find(arg => arg.startsWith('--employee-id='));
const targetEmployeeId = employeeIdArg ? employeeIdArg.split('=')[1] : null;

console.log('🔧 PandaPayroll Data Consistency Fix');
console.log('=====================================');
console.log(`Mode: ${isDryRun ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);
console.log(`Target: ${targetEmployeeId ? `Employee ${targetEmployeeId}` : 'All employees'}`);
console.log('');

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    process.exit(1);
  }
}

async function findInconsistentData() {
  console.log('🔍 Scanning for data inconsistencies...');
  
  const query = targetEmployeeId ? { employee: targetEmployeeId } : {};
  
  // Find all payroll records
  const payrollRecords = await Payroll.find(query);
  console.log(`Found ${payrollRecords.length} payroll records`);
  
  // Find all payroll periods
  const payrollPeriods = await PayrollPeriod.find(query);
  console.log(`Found ${payrollPeriods.length} payroll periods`);
  
  const issues = [];
  
  for (const payroll of payrollRecords) {
    const employeeId = payroll.employee.toString();
    const companyId = payroll.company.toString();
    
    // Find matching payroll periods for this employee
    const matchingPeriods = payrollPeriods.filter(period => 
      period.employee.toString() === employeeId && 
      period.company.toString() === companyId
    );
    
    // Check for issues
    const issue = {
      payrollId: payroll._id,
      employeeId,
      companyId,
      issues: [],
      suggestedFixes: []
    };
    
    // Issue 1: Missing payrollPeriod reference
    if (!payroll.payrollPeriod) {
      issue.issues.push('Missing payrollPeriod reference');
      
      // Try to find matching period by date
      const payrollMonth = new Date(payroll.month);
      const matchingPeriod = matchingPeriods.find(period => {
        const periodStart = new Date(period.startDate);
        const periodEnd = new Date(period.endDate);
        return payrollMonth >= periodStart && payrollMonth <= periodEnd;
      });
      
      if (matchingPeriod) {
        issue.suggestedFixes.push({
          type: 'SET_PAYROLL_PERIOD',
          payrollPeriodId: matchingPeriod._id,
          description: `Link to PayrollPeriod ${matchingPeriod._id} (${matchingPeriod.startDate.toISOString().split('T')[0]} - ${matchingPeriod.endDate.toISOString().split('T')[0]})`
        });
      } else if (matchingPeriods.length > 0) {
        // Use the first available period
        const period = matchingPeriods[0];
        issue.suggestedFixes.push({
          type: 'SET_PAYROLL_PERIOD',
          payrollPeriodId: period._id,
          description: `Link to PayrollPeriod ${period._id} (${period.startDate.toISOString().split('T')[0]} - ${period.endDate.toISOString().split('T')[0]})`
        });
        
        // Also suggest updating the month
        issue.suggestedFixes.push({
          type: 'UPDATE_MONTH',
          newMonth: period.endDate,
          description: `Update month to match period end date: ${period.endDate.toISOString().split('T')[0]}`
        });
      }
    }
    
    // Issue 2: No financial data (for testing)
    if (payroll.basicSalary === 0 && payroll.calculations.grossIncome === 0) {
      issue.issues.push('No financial data (zero salary and gross income)');
      issue.suggestedFixes.push({
        type: 'ADD_TEST_FINANCIAL_DATA',
        description: 'Add realistic test financial data for IRP5 generation testing'
      });
    }
    
    if (issue.issues.length > 0) {
      issues.push(issue);
    }
  }
  
  return issues;
}

async function applyFixes(issues) {
  console.log(`\n🔧 Applying fixes to ${issues.length} records...`);
  
  for (const issue of issues) {
    console.log(`\nProcessing Payroll ${issue.payrollId}:`);
    
    if (isDryRun) {
      console.log('  [DRY RUN] Would apply the following fixes:');
      issue.suggestedFixes.forEach(fix => {
        console.log(`    - ${fix.description}`);
      });
      continue;
    }
    
    const payroll = await Payroll.findById(issue.payrollId);
    if (!payroll) {
      console.log(`  ❌ Payroll record not found`);
      continue;
    }
    
    let updated = false;
    
    for (const fix of issue.suggestedFixes) {
      switch (fix.type) {
        case 'SET_PAYROLL_PERIOD':
          payroll.payrollPeriod = fix.payrollPeriodId;
          console.log(`  ✅ Set payrollPeriod to ${fix.payrollPeriodId}`);
          updated = true;
          break;
          
        case 'UPDATE_MONTH':
          payroll.month = fix.newMonth;
          console.log(`  ✅ Updated month to ${fix.newMonth.toISOString().split('T')[0]}`);
          updated = true;
          break;
          
        case 'ADD_TEST_FINANCIAL_DATA':
          // Add realistic test data
          payroll.basicSalary = 25000; // R25,000 basic salary
          payroll.calculations.grossIncome = 25000;
          payroll.calculations.taxableIncome = 25000;
          payroll.calculations.periodTax = 3500; // Approximate PAYE
          payroll.calculations.totalDeductions = 3500;
          payroll.calculations.netPay = 21500;
          console.log(`  ✅ Added test financial data (R25,000 basic salary)`);
          updated = true;
          break;
      }
    }
    
    if (updated) {
      await payroll.save();
      console.log(`  💾 Saved changes to payroll record`);
    }
  }
}

async function main() {
  try {
    await connectToDatabase();
    
    const issues = await findInconsistentData();
    
    if (issues.length === 0) {
      console.log('✅ No data consistency issues found!');
      return;
    }
    
    console.log(`\n📋 Found ${issues.length} records with data consistency issues:`);
    issues.forEach((issue, index) => {
      console.log(`\n${index + 1}. Payroll ${issue.payrollId} (Employee: ${issue.employeeId})`);
      issue.issues.forEach(issueDesc => {
        console.log(`   ❌ ${issueDesc}`);
      });
      issue.suggestedFixes.forEach(fix => {
        console.log(`   🔧 ${fix.description}`);
      });
    });
    
    if (isDryRun) {
      console.log('\n💡 This was a dry run. To apply fixes, run without --dry-run flag.');
    } else {
      await applyFixes(issues);
      console.log('\n✅ Data consistency fixes completed!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { findInconsistentData, applyFixes };
