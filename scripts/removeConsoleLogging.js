#!/usr/bin/env node

/**
 * Script to remove console.log, console.warn, console.info, and console.debug statements
 * while preserving console.error statements for performance optimization
 */

const fs = require('fs');
const path = require('path');

// Files to process
const filesToProcess = [
  'routes/auth.js',
  'app.js'
];

function removeConsoleLogging(filePath) {
  console.log(`\n🔧 Processing ${filePath}...`);
  
  try {
    // Read the file
    const fullPath = path.join(__dirname, '..', filePath);
    let content = fs.readFileSync(fullPath, 'utf8');
    const originalLength = content.length;
    
    // Count original console statements
    const originalLogCount = (content.match(/console\.(log|warn|info|debug)/g) || []).length;
    const originalErrorCount = (content.match(/console\.error/g) || []).length;
    
    // Remove console.log, console.warn, console.info, console.debug statements
    // This regex matches:
    // - console.log(...), console.warn(...), console.info(...), console.debug(...)
    // - Handles multi-line statements
    // - Preserves console.error statements
    
    // First, handle single-line console statements
    content = content.replace(/^\s*console\.(log|warn|info|debug)\([^;]*\);?\s*$/gm, '');
    
    // Handle multi-line console statements (more complex)
    content = content.replace(/^\s*console\.(log|warn|info|debug)\(\s*$/gm, '');
    
    // Handle console statements with string concatenation or template literals
    content = content.replace(/^\s*console\.(log|warn|info|debug)\([^)]*\);\s*$/gm, '');
    
    // Handle console statements that span multiple lines with proper bracket matching
    let lines = content.split('\n');
    let newLines = [];
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // Check if this line starts a console.log/warn/info/debug statement
      if (trimmedLine.match(/^console\.(log|warn|info|debug)\s*\(/)) {
        // Skip this console statement and find its end
        let bracketCount = 0;
        let inString = false;
        let stringChar = '';
        let j = i;
        
        // Find the end of this console statement
        while (j < lines.length) {
          const currentLine = lines[j];
          
          for (let k = 0; k < currentLine.length; k++) {
            const char = currentLine[k];
            
            if (!inString) {
              if (char === '"' || char === "'" || char === '`') {
                inString = true;
                stringChar = char;
              } else if (char === '(') {
                bracketCount++;
              } else if (char === ')') {
                bracketCount--;
                if (bracketCount === 0) {
                  // Found the end of the console statement
                  i = j + 1;
                  break;
                }
              }
            } else {
              if (char === stringChar && currentLine[k-1] !== '\\') {
                inString = false;
                stringChar = '';
              }
            }
          }
          
          if (bracketCount === 0) break;
          j++;
        }
        
        // If we couldn't find the end, just skip this line
        if (bracketCount > 0) {
          i++;
        }
      } else {
        // Keep this line
        newLines.push(line);
        i++;
      }
    }
    
    content = newLines.join('\n');
    
    // Clean up empty lines (remove excessive blank lines)
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // Count remaining console statements
    const finalLogCount = (content.match(/console\.(log|warn|info|debug)/g) || []).length;
    const finalErrorCount = (content.match(/console\.error/g) || []).length;
    
    // Write the file back
    fs.writeFileSync(fullPath, content, 'utf8');
    
    const savedBytes = originalLength - content.length;
    const removedStatements = originalLogCount - finalLogCount;
    
    console.log(`✅ ${filePath} processed successfully:`);
    console.log(`   📊 Removed ${removedStatements} console.log/warn/info/debug statements`);
    console.log(`   🛡️  Preserved ${finalErrorCount} console.error statements`);
    console.log(`   💾 Saved ${savedBytes} bytes`);
    
    if (finalLogCount > 0) {
      console.log(`   ⚠️  ${finalLogCount} console.log/warn/info/debug statements remain (may need manual review)`);
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

// Main execution
console.log('🚀 Starting console logging removal for performance optimization...');

filesToProcess.forEach(removeConsoleLogging);

console.log('\n✨ Console logging removal completed!');
console.log('📈 This should improve JavaScript execution performance and Speed Index.');
