#!/usr/bin/env node

/**
 * Safe Console.log Removal <PERSON> for routes/employeeManagement.js
 * 
 * This script safely removes console.log, console.info, console.warn, and console.debug
 * statements while preserving console.error statements and maintaining code integrity.
 * 
 * Safety Features:
 * - Creates backup before modification
 * - Syntax validation before and after changes
 * - Line-by-line processing to avoid breaking code structure
 * - Preserves all console.error statements
 * - Rollback capability if issues are detected
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const TARGET_FILE = 'routes/employeeManagement.js';
const BACKUP_SUFFIX = '.backup.' + Date.now();
const PROJECT_ROOT = path.join(__dirname, '..');

class SafeConsoleLogRemover {
  constructor() {
    this.targetPath = path.join(PROJECT_ROOT, TARGET_FILE);
    this.backupPath = this.targetPath + BACKUP_SUFFIX;
    this.originalContent = '';
    this.modifiedContent = '';
    this.removedCount = 0;
    this.preservedErrorCount = 0;
  }

  /**
   * Main execution method
   */
  async execute() {
    try {
      console.log('🚀 Starting safe console.log removal for employeeManagement.js');
      console.log('📁 Target file:', this.targetPath);
      
      // Step 1: Validate file exists
      await this.validateFileExists();
      
      // Step 2: Create backup
      await this.createBackup();
      
      // Step 3: Validate original syntax
      await this.validateSyntax('original');
      
      // Step 4: Process file content
      await this.processFile();
      
      // Step 5: Write modified content
      await this.writeModifiedFile();
      
      // Step 6: Validate modified syntax
      await this.validateSyntax('modified');
      
      // Step 7: Test application startup
      await this.testApplicationStartup();
      
      // Step 8: Report results
      this.reportResults();
      
      console.log('✅ Console.log removal completed successfully!');
      console.log(`📊 Removed ${this.removedCount} console.log statements`);
      console.log(`🛡️  Preserved ${this.preservedErrorCount} console.error statements`);
      console.log(`💾 Backup saved as: ${this.backupPath}`);
      
    } catch (error) {
      console.error('❌ Error during processing:', error.message);
      await this.rollback();
      process.exit(1);
    }
  }

  /**
   * Validate that the target file exists
   */
  async validateFileExists() {
    if (!fs.existsSync(this.targetPath)) {
      throw new Error(`Target file not found: ${this.targetPath}`);
    }
    console.log('✅ Target file exists');
  }

  /**
   * Create a backup of the original file
   */
  async createBackup() {
    this.originalContent = fs.readFileSync(this.targetPath, 'utf8');
    fs.writeFileSync(this.backupPath, this.originalContent);
    console.log('✅ Backup created:', this.backupPath);
  }

  /**
   * Validate JavaScript syntax
   */
  async validateSyntax(stage) {
    try {
      execSync(`node -c "${this.targetPath}"`, { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe'
      });
      console.log(`✅ Syntax validation passed (${stage})`);
    } catch (error) {
      throw new Error(`Syntax validation failed (${stage}): ${error.message}`);
    }
  }

  /**
   * Process the file content to remove console.log statements
   */
  async processFile() {
    console.log('🔧 Processing file content...');

    // Use a more robust approach with regex replacement
    let content = this.originalContent;

    // Count original statements
    const originalLogStatements = (content.match(/console\.(log|info|warn|debug)/g) || []).length;
    const originalErrorStatements = (content.match(/console\.error/g) || []).length;

    // Remove single-line console statements first
    content = content.replace(/^\s*console\.(log|info|warn|debug)\([^;]*\);?\s*$/gm, '');

    // Remove multi-line console statements (more conservative approach)
    // This handles console.log with object literals spanning multiple lines
    content = content.replace(/^\s*console\.(log|info|warn|debug)\s*\(\s*[\s\S]*?\)\s*;?\s*$/gm, '');

    // Clean up multiple consecutive empty lines (leave max 2)
    content = content.replace(/\n\s*\n\s*\n\s*\n/g, '\n\n\n');

    // Count final statements
    const finalLogStatements = (content.match(/console\.(log|info|warn|debug)/g) || []).length;
    const finalErrorStatements = (content.match(/console\.error/g) || []).length;

    this.removedCount = originalLogStatements - finalLogStatements;
    this.preservedErrorCount = finalErrorStatements;

    // If we didn't remove many statements, fall back to line-by-line processing
    if (this.removedCount < originalLogStatements * 0.8) {
      console.log('🔄 Falling back to line-by-line processing for safety...');
      await this.processFileLineByLine();
    } else {
      this.modifiedContent = content;
      console.log(`🔧 Processed with regex approach`);
    }

    console.log(`🔧 Removed ${this.removedCount} console statements`);
  }

  /**
   * Fallback line-by-line processing for complex cases
   */
  async processFileLineByLine() {
    const lines = this.originalContent.split('\n');
    const processedLines = [];
    let i = 0;

    this.removedCount = 0;
    this.preservedErrorCount = 0;

    while (i < lines.length) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Simple single-line console statement detection
      if (/^\s*console\.(log|info|warn|debug)\s*\(/.test(trimmedLine)) {
        // Skip this line (remove it)
        this.removedCount++;
      } else if (/^\s*console\.error\s*\(/.test(trimmedLine)) {
        // Preserve console.error
        processedLines.push(line);
        this.preservedErrorCount++;
      } else {
        // Keep all other lines
        processedLines.push(line);
      }

      i++;
    }

    this.modifiedContent = processedLines.join('\n');
  }

  /**
   * Check if a line contains a console statement
   */
  isConsoleStatement(line) {
    return /^\s*console\.\w+\s*\(/.test(line);
  }

  /**
   * Check if a console statement is console.error (should be preserved)
   */
  isConsoleError(line) {
    return /^\s*console\.error\s*\(/.test(line);
  }

  /**
   * Check if a console statement should be removed
   */
  isRemovableConsoleStatement(line) {
    return /^\s*console\.(log|info|warn|debug)\s*\(/.test(line);
  }



  /**
   * Write the modified content to the file
   */
  async writeModifiedFile() {
    fs.writeFileSync(this.targetPath, this.modifiedContent);
    console.log('✅ Modified file written');
  }

  /**
   * Test that the application can start successfully
   */
  async testApplicationStartup() {
    try {
      console.log('🧪 Testing application startup...');
      execSync('node -c app.js', { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe',
        timeout: 10000
      });
      console.log('✅ Application startup test passed');
    } catch (error) {
      throw new Error(`Application startup test failed: ${error.message}`);
    }
  }

  /**
   * Rollback changes if something went wrong
   */
  async rollback() {
    try {
      console.log('🔄 Rolling back changes...');
      fs.writeFileSync(this.targetPath, this.originalContent);
      console.log('✅ Rollback completed - original file restored');
    } catch (rollbackError) {
      console.error('❌ Rollback failed:', rollbackError.message);
      console.log(`💾 Manual restore required from: ${this.backupPath}`);
    }
  }

  /**
   * Report the results of the operation
   */
  reportResults() {
    const originalSize = this.originalContent.length;
    const modifiedSize = this.modifiedContent.length;
    const savedBytes = originalSize - modifiedSize;
    
    console.log('\n📊 Operation Summary:');
    console.log('─'.repeat(50));
    console.log(`📝 Original file size: ${originalSize.toLocaleString()} bytes`);
    console.log(`📝 Modified file size: ${modifiedSize.toLocaleString()} bytes`);
    console.log(`💾 Bytes saved: ${savedBytes.toLocaleString()} bytes`);
    console.log(`🗑️  Console.log statements removed: ${this.removedCount}`);
    console.log(`🛡️  Console.error statements preserved: ${this.preservedErrorCount}`);
    console.log(`📁 Backup location: ${this.backupPath}`);
  }
}

// Execute the script
if (require.main === module) {
  const remover = new SafeConsoleLogRemover();
  remover.execute().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = SafeConsoleLogRemover;
