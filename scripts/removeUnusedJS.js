#!/usr/bin/env node

/**
 * Remove unused JavaScript files
 * Generated by analyzeUnusedJS.js
 * 
 * IMPORTANT: Review this script before running!
 */

const fs = require('fs');
const path = require('path');

const filesToRemove = [
  'public/js/search.js', // 1.59 KB
  'public/js/xero-refresh-worker.js', // 1.20 KB
  'public/js/frequency-tab-helper.js', // 0.95 KB
  'public/js/progress-tab-helper.js', // 0.81 KB
  'public/js/pagination-helper.js', // 0.56 KB
  'public/js/dob-validation.js', // 0.42 KB
];

const filesToReview = [
  'public/js/payrollhub-actions.min.js', // 64.01 KB - REVIEW NEEDED
  'public/js/payrollhub.min.js', // 44.47 KB - REVIEW NEEDED
  'public/js/sic-codes.min.js', // 19.43 KB - REVIEW NEEDED
  'public/js/filing.min.js', // 17.93 KB - REVIEW NEEDED
  'public/js/add-employee-details.min.js', // 17.03 KB - REVIEW NEEDED
  'public/js/reporting.min.js', // 15.72 KB - REVIEW NEEDED
  'public/js/bulk-add-employees.min.js', // 15.41 KB - REVIEW NEEDED
  'public/js/xero-integration.min.js', // 12.31 KB - REVIEW NEEDED
  'public/js/employeeProfile.min.js', // 12.12 KB - REVIEW NEEDED
  'public/js/employeeActions.min.js', // 11.57 KB - REVIEW NEEDED
  'public/js/regularHours.min.js', // 11.23 KB - REVIEW NEEDED
  'public/js/regularHours.js', // 11.23 KB - REVIEW NEEDED
  'public/js/bi-annual-filing.min.js', // 9.41 KB - REVIEW NEEDED
  'public/js/sessionManager.min.js', // 8.25 KB - REVIEW NEEDED
  'public/js/addBasicSalary.min.js', // 7.98 KB - REVIEW NEEDED
  'public/js/payment-restriction.min.js', // 7.42 KB - REVIEW NEEDED
  'public/js/direct-modal-fix.min.js', // 6.95 KB - REVIEW NEEDED
  'public/js/toast-notifications.min.js', // 6.41 KB - REVIEW NEEDED
  'public/js/pensionFund.min.js', // 6.25 KB - REVIEW NEEDED
  'public/js/editEmployeeBasicInfo.min.js', // 6.12 KB - REVIEW NEEDED
  'public/js/logo-upload.min.js', // 5.99 KB - REVIEW NEEDED
  'public/js/billing.min.js', // 5.78 KB - REVIEW NEEDED
  'public/js/self-service.min.js', // 5.75 KB - REVIEW NEEDED
  'public/js/defineRFI.min.js', // 5.33 KB - REVIEW NEEDED
  'public/js/fundForms.min.js', // 4.77 KB - REVIEW NEEDED
  'public/js/fundForms.js', // 4.77 KB - REVIEW NEEDED
  'public/js/trial-restriction.min.js', // 4.50 KB - REVIEW NEEDED
  'public/js/hoursInput.min.js', // 4.25 KB - REVIEW NEEDED
  'public/js/companies.min.js', // 3.76 KB - REVIEW NEEDED
  'public/js/custom-items.min.js', // 3.04 KB - REVIEW NEEDED
  'public/js/employee-number-toggle.min.js', // 2.62 KB - REVIEW NEEDED
  'public/js/employeeManagement.min.js', // 2.56 KB - REVIEW NEEDED
  'public/js/ui19-signature.min.js', // 2.45 KB - REVIEW NEEDED
  'public/js/payrun-wizard.min.js', // 2.29 KB - REVIEW NEEDED
  'public/js/payslip-modal-checkboxes.min.js', // 2.26 KB - REVIEW NEEDED
  'public/js/termination-certificate.min.js', // 2.15 KB - REVIEW NEEDED
  'public/js/termination-certificate.js', // 2.15 KB - REVIEW NEEDED
  'public/js/beneficiary-forms.min.js', // 1.99 KB - REVIEW NEEDED
  'public/js/ui19-form-handler.min.js', // 1.86 KB - REVIEW NEEDED
  'public/js/ui19-form-handler.js', // 1.86 KB - REVIEW NEEDED
  'public/js/payroll/payrun-view.min.js', // 1.80 KB - REVIEW NEEDED
  'public/js/payroll/payrun-view.js', // 1.80 KB - REVIEW NEEDED
  'public/js/classifications.min.js', // 1.75 KB - REVIEW NEEDED
  'public/js/notifications.min.js', // 1.74 KB - REVIEW NEEDED
  'public/js/payslip-export.min.js', // 1.67 KB - REVIEW NEEDED
  'public/js/providentFund.min.js', // 1.66 KB - REVIEW NEEDED
  'public/js/undo-end-of-service.min.js', // 1.64 KB - REVIEW NEEDED
  'public/js/employee-numbers.min.js', // 1.61 KB - REVIEW NEEDED
  'public/js/search.min.js', // 1.59 KB - REVIEW NEEDED
  'public/js/reinstate.min.js', // 1.38 KB - REVIEW NEEDED
  'public/js/lossOfIncome.min.js', // 1.36 KB - REVIEW NEEDED
  'public/js/lossOfIncome.js', // 1.36 KB - REVIEW NEEDED
  'public/js/xero-refresh-worker.min.js', // 1.20 KB - REVIEW NEEDED
  'public/js/settings.min.js', // 1.20 KB - REVIEW NEEDED
  'public/js/signature-pad.min.js', // 1.13 KB - REVIEW NEEDED
  'public/js/beneficiaries.min.js', // 1.12 KB - REVIEW NEEDED
  'public/js/beneficiaries.js', // 1.12 KB - REVIEW NEEDED
  'public/js/sidebar.min.js', // 1.08 KB - REVIEW NEEDED
  'public/js/alerts.min.js', // 1.00 KB - REVIEW NEEDED
  'public/js/frequency-tab-helper.min.js', // 0.95 KB - REVIEW NEEDED
  'public/js/payroll/payrun-edit.min.js', // 0.88 KB - REVIEW NEEDED
  'public/js/progress-tab-helper.min.js', // 0.81 KB - REVIEW NEEDED
  'public/js/ui19-form.min.js', // 0.79 KB - REVIEW NEEDED
  'public/js/ui19-form.js', // 0.79 KB - REVIEW NEEDED
  'public/js/script.min.js', // 0.78 KB - REVIEW NEEDED
  'public/js/responsive-dashboard.min.js', // 0.76 KB - REVIEW NEEDED
  'public/js/responsive-dashboard.js', // 0.76 KB - REVIEW NEEDED
  'public/js/miniEmployeeForm.min.js', // 0.74 KB - REVIEW NEEDED
  'public/js/miniEmployeeForm.js', // 0.74 KB - REVIEW NEEDED
  'public/js/pagination-helper.min.js', // 0.56 KB - REVIEW NEEDED
  'public/js/dob-validation.min.js', // 0.42 KB - REVIEW NEEDED
  'public/js/bulkActions.min.js', // 0.27 KB - REVIEW NEEDED
  'public/js/bulkActions.js', // 0.27 KB - REVIEW NEEDED
];

console.log('🗑️  Removing unused JavaScript files...');

let totalSaved = 0;

filesToRemove.forEach(file => {
  try {
    const fullPath = path.join(__dirname, '..', file);
    const stats = fs.statSync(fullPath);
    fs.unlinkSync(fullPath);
    totalSaved += stats.size;
    console.log(`✅ Removed: ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
  } catch (error) {
    console.warn(`⚠️  Could not remove ${file}: ${error.message}`);
  }
});

console.log(`\n💾 Total space saved: ${(totalSaved / 1024).toFixed(2)} KB`);

if (filesToReview.length > 0) {
  console.log('\n🟡 FILES THAT NEED MANUAL REVIEW:');
  filesToReview.forEach(file => {
    console.log(`  - ${file}`);
  });
}
