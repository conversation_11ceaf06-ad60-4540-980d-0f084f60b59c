#!/usr/bin/env node

/**
 * Ultra-Safe Console Logging Cleanup Script
 * 
 * This script safely removes console.log, console.info, console.warn, and console.debug
 * statements while implementing multiple safety layers to prevent syntax errors.
 * 
 * Enhanced Safety Features:
 * - Dry-run mode by default
 * - Individual file processing with rollback
 * - Advanced syntax validation
 * - Conservative pattern matching
 * - Detailed error reporting
 * - Manual confirmation for each directory
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class SafeConsoleCleanup {
  constructor(options = {}) {
    this.dryRun = options.dryRun !== false; // Default to dry run
    this.interactive = options.interactive !== false; // Default to interactive
    this.processedFiles = [];
    this.errors = [];
    this.stats = {
      filesProcessed: 0,
      statementsRemoved: 0,
      statementsPreserved: 0,
      syntaxErrors: 0
    };
  }

  /**
   * Main execution method
   */
  async run(targetDirectories = ['routes', 'services', 'utils']) {
    try {
      console.log('🛡️  Ultra-Safe Console Logging Cleanup');
      console.log('=====================================');
      
      if (this.dryRun) {
        console.log('🔍 DRY RUN MODE - No files will be modified');
      }
      
      console.log(`📁 Target directories: ${targetDirectories.join(', ')}\n`);
      
      for (const directory of targetDirectories) {
        await this.processDirectory(directory);
      }
      
      this.reportFinalResults();
      
    } catch (error) {
      console.error('❌ Error during cleanup:', error.message);
      process.exit(1);
    }
  }

  /**
   * Process a single directory
   */
  async processDirectory(directory) {
    console.log(`\n📁 Processing directory: ${directory}`);
    console.log('='.repeat(50));
    
    const dirPath = path.join(process.cwd(), directory);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⚠️  Directory ${directory} does not exist, skipping...`);
      return;
    }
    
    const jsFiles = this.findJavaScriptFiles(dirPath);
    console.log(`Found ${jsFiles.length} JavaScript files`);
    
    if (jsFiles.length === 0) {
      console.log('No JavaScript files to process');
      return;
    }
    
    // Interactive confirmation
    if (this.interactive && !this.dryRun) {
      const proceed = await this.askConfirmation(
        `Do you want to process ${jsFiles.length} files in ${directory}? (y/N): `
      );
      
      if (!proceed) {
        console.log('Skipping directory...');
        return;
      }
    }
    
    // Process files one by one
    for (const filePath of jsFiles) {
      await this.processFile(filePath, directory);
    }
  }

  /**
   * Find all JavaScript files in directory
   */
  findJavaScriptFiles(dirPath) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isFile() && item.endsWith('.js')) {
          // Skip minified files and backups
          if (!item.includes('.min.') && !item.includes('.backup.')) {
            files.push(itemPath);
          }
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error.message);
    }
    
    return files.sort();
  }

  /**
   * Process a single file with enhanced safety
   */
  async processFile(filePath, directory) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    try {
      console.log(`\n🔧 Processing: ${relativePath}`);
      
      // Read original content
      const originalContent = fs.readFileSync(filePath, 'utf8');
      console.log(`   ✓ Read ${originalContent.split('\n').length} lines`);
      
      // Analyze console statements
      const analysis = this.analyzeConsoleStatements(originalContent);
      
      if (analysis.removableCount === 0) {
        console.log(`   ℹ️  No removable console statements found`);
        return;
      }
      
      console.log(`   📊 Found: ${analysis.removableCount} removable, ${analysis.preservableCount} preserved`);
      
      // Create backup (even in dry run for safety)
      const backupPath = `${filePath}.backup.${Date.now()}`;
      if (!this.dryRun) {
        fs.writeFileSync(backupPath, originalContent);
        console.log(`   ✓ Backup created`);
      }
      
      // Clean console statements
      const cleanedContent = this.cleanConsoleStatements(originalContent);
      
      // Validate syntax
      const syntaxValid = await this.validateSyntax(cleanedContent, filePath);
      
      if (!syntaxValid) {
        console.log(`   ❌ Syntax validation failed - skipping file`);
        this.stats.syntaxErrors++;
        return;
      }
      
      console.log(`   ✓ Syntax validation passed`);
      
      // Write cleaned content (only if not dry run)
      if (!this.dryRun) {
        fs.writeFileSync(filePath, cleanedContent);
        console.log(`   ✓ File updated`);
      } else {
        console.log(`   🔍 DRY RUN - Would update file`);
      }
      
      // Update statistics
      this.stats.filesProcessed++;
      this.stats.statementsRemoved += analysis.removableCount;
      this.stats.statementsPreserved += analysis.preservableCount;
      
      this.processedFiles.push({
        path: relativePath,
        directory,
        removed: analysis.removableCount,
        preserved: analysis.preservableCount,
        backupPath: this.dryRun ? null : path.relative(process.cwd(), backupPath)
      });
      
    } catch (error) {
      console.log(`   ❌ Error processing file: ${error.message}`);
      this.errors.push({
        file: relativePath,
        error: error.message
      });
    }
  }

  /**
   * Analyze console statements in content
   */
  analyzeConsoleStatements(content) {
    const lines = content.split('\n');
    let removableCount = 0;
    let preservableCount = 0;
    
    for (const line of lines) {
      if (this.containsConsoleStatement(line)) {
        if (this.shouldPreserveLine(line)) {
          preservableCount++;
        } else {
          removableCount++;
        }
      }
    }
    
    return { removableCount, preservableCount };
  }

  /**
   * Check if line contains console statement
   */
  containsConsoleStatement(line) {
    return /console\.(log|info|warn|debug|error)/.test(line);
  }

  /**
   * Determine if line should be preserved (very conservative)
   */
  shouldPreserveLine(line) {
    // Always preserve console.error
    if (/console\.error/.test(line)) {
      return true;
    }
    
    // Preserve console statements in comments
    if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
      return true;
    }
    
    // Preserve console statements in strings
    if (this.isInString(line)) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if console statement is within a string
   */
  isInString(line) {
    const consoleMatch = line.match(/console\.(log|info|warn|debug)/);
    if (!consoleMatch) return false;
    
    const beforeConsole = line.substring(0, consoleMatch.index);
    const singleQuotes = (beforeConsole.match(/'/g) || []).length;
    const doubleQuotes = (beforeConsole.match(/"/g) || []).length;
    const backticks = (beforeConsole.match(/`/g) || []).length;
    
    // If odd number of quotes before console, it's likely in a string
    return (singleQuotes % 2 === 1) || (doubleQuotes % 2 === 1) || (backticks % 2 === 1);
  }

  /**
   * Clean console statements with conservative approach
   */
  cleanConsoleStatements(content) {
    const lines = content.split('\n');
    const cleanedLines = [];
    let inMultiLineConsole = false;
    let braceCount = 0;
    let parenCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Check if this line starts a console statement
      if (!inMultiLineConsole && this.containsConsoleStatement(line) && !this.shouldPreserveLine(line)) {
        // Check if it's a simple single-line console statement
        if (trimmedLine.match(/^console\.(log|info|warn|debug)\s*\(.*\)\s*;?\s*$/)) {
          // Count parentheses and braces to see if it's complete
          const openParens = (trimmedLine.match(/\(/g) || []).length;
          const closeParens = (trimmedLine.match(/\)/g) || []).length;
          const openBraces = (trimmedLine.match(/\{/g) || []).length;
          const closeBraces = (trimmedLine.match(/\}/g) || []).length;

          if (openParens === closeParens && openBraces === closeBraces) {
            // Complete single-line console statement - remove it
            continue;
          } else {
            // Multi-line console statement - start tracking
            inMultiLineConsole = true;
            parenCount = openParens - closeParens;
            braceCount = openBraces - closeBraces;
            continue;
          }
        } else {
          // Complex line with console statement - keep it for safety
          cleanedLines.push(line);
        }
      } else if (inMultiLineConsole) {
        // We're in a multi-line console statement
        const openParens = (line.match(/\(/g) || []).length;
        const closeParens = (line.match(/\)/g) || []).length;
        const openBraces = (line.match(/\{/g) || []).length;
        const closeBraces = (line.match(/\}/g) || []).length;

        parenCount += openParens - closeParens;
        braceCount += openBraces - closeBraces;

        // Check if we've closed all parentheses and braces
        if (parenCount <= 0 && braceCount <= 0) {
          inMultiLineConsole = false;
          parenCount = 0;
          braceCount = 0;
          // Skip this line too (end of multi-line console statement)
          continue;
        }
        // Skip this line (part of multi-line console statement)
        continue;
      } else {
        cleanedLines.push(line);
      }
    }

    return cleanedLines.join('\n');
  }

  /**
   * Enhanced syntax validation
   */
  async validateSyntax(content, originalPath) {
    try {
      // For app.js and other main entry points, only use Function syntax check
      // to avoid executing the entire application
      const fileName = path.basename(originalPath);
      if (fileName === 'app.js' || fileName.includes('server') || fileName.includes('index')) {
        // Use only Function constructor for syntax validation
        new Function(content);
        return true;
      }

      // For other files, use the full validation approach
      const tempPath = `${originalPath}.temp.${Date.now()}.js`;
      fs.writeFileSync(tempPath, content);

      // Try to parse with Node.js
      try {
        delete require.cache[path.resolve(tempPath)];
        require(path.resolve(tempPath));
      } catch (requireError) {
        // If require fails, try basic syntax check
        new Function(content);
      }

      // Clean up temp file
      fs.unlinkSync(tempPath);

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Ask for user confirmation
   */
  async askConfirmation(question) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    return new Promise((resolve) => {
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer.toLowerCase().startsWith('y'));
      });
    });
  }

  /**
   * Process a single file (for app.js specific cleanup)
   */
  async runSingleFile(filePath) {
    try {
      console.log('🛡️  Ultra-Safe Console Logging Cleanup - Single File Mode');
      console.log('========================================================');

      if (this.dryRun) {
        console.log('🔍 DRY RUN MODE - No files will be modified');
      }

      console.log(`📁 Target file: ${filePath}\n`);

      // Check if file exists
      if (!require('fs').existsSync(filePath)) {
        console.log(`❌ File ${filePath} does not exist`);
        return;
      }

      // Process the single file
      await this.processFile(filePath, 'single-file');

      this.reportFinalResults();

    } catch (error) {
      console.error('❌ Error during cleanup:', error.message);
      process.exit(1);
    }
  }

  /**
   * Report final results
   */
  reportFinalResults() {
    console.log('\n📊 Final Results:');
    console.log('=================');
    console.log(`📁 Files processed: ${this.stats.filesProcessed}`);
    console.log(`🗑️  Console statements removed: ${this.stats.statementsRemoved}`);
    console.log(`🛡️  Console statements preserved: ${this.stats.statementsPreserved}`);
    console.log(`❌ Syntax errors prevented: ${this.stats.syntaxErrors}`);
    console.log(`⚠️  Processing errors: ${this.errors.length}`);

    if (this.dryRun) {
      console.log('\n🔍 This was a DRY RUN - no files were actually modified');
      console.log('💡 Run with --execute flag to apply changes');
    }

    if (this.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.errors.forEach(error => {
        console.log(`   ❌ ${error.file}: ${error.error}`);
      });
    }
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const interactive = !args.includes('--no-interactive');

  const cleanup = new SafeConsoleCleanup({ dryRun, interactive });

  // Check if specific file is requested
  const fileArg = args.find(arg => arg.startsWith('--file='));
  if (fileArg) {
    const filePath = fileArg.split('=')[1];
    cleanup.runSingleFile(filePath).catch(error => {
      console.error('Script execution failed:', error);
      process.exit(1);
    });
  } else {
    // Default directories
    const directories = ['routes', 'services', 'utils'];

    cleanup.run(directories).catch(error => {
      console.error('Script execution failed:', error);
      process.exit(1);
    });
  }
}

module.exports = SafeConsoleCleanup;
