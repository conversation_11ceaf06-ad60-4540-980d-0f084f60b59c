#!/usr/bin/env node

/**
 * Comprehensive Console Logging Cleanup Script for PandaPayroll
 * 
 * This script safely removes console.log, console.info, console.warn, and console.debug
 * statements from routes/, services/, utils/, and models/ directories while preserving
 * console.error statements for production monitoring.
 * 
 * SAFETY FEATURES:
 * - Creates timestamped backups before any modifications
 * - Comprehensive syntax validation without execution
 * - Multi-line console statement detection with bracket counting
 * - Preserves all console.error statements
 * - Detailed reporting and rollback capabilities
 * - Processes files incrementally with validation at each step
 */

const fs = require('fs');
const path = require('path');

class ComprehensiveConsoleCleanup {
  constructor() {
    this.targetDirectories = ['routes', 'services', 'utils', 'models'];
    this.backupTimestamp = Date.now();
    this.processedFiles = [];
    this.skippedFiles = [];
    this.totalRemoved = 0;
    this.totalPreserved = 0;
    this.errors = [];
  }

  /**
   * Main execution method
   */
  async run() {
    console.log('🚀 Starting Comprehensive Console Logging Cleanup for PandaPayroll');
    console.log('📊 Performance Impact Analysis:');
    
    // First, analyze the scope
    await this.analyzeScope();
    
    // Ask for confirmation
    if (!await this.confirmExecution()) {
      console.log('❌ Cleanup cancelled by user');
      return;
    }

    // Process each directory
    for (const dir of this.targetDirectories) {
      await this.processDirectory(dir);
    }

    // Generate final report
    this.generateFinalReport();
  }

  /**
   * Analyze the scope of console logging across directories
   */
  async analyzeScope() {
    console.log('\n📋 Analyzing Console Logging Scope:');
    console.log('=====================================');
    
    for (const dir of this.targetDirectories) {
      const files = this.getJavaScriptFiles(dir);
      let totalConsole = 0;
      let totalErrors = 0;
      let removableConsole = 0;
      
      for (const file of files) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          const consoleCount = (content.match(/console\.(log|info|warn|debug)/g) || []).length;
          const errorCount = (content.match(/console\.error/g) || []).length;
          
          totalConsole += consoleCount;
          totalErrors += errorCount;
          removableConsole += consoleCount;
        } catch (error) {
          // Skip files that can't be read
        }
      }
      
      console.log(`📁 ${dir.toUpperCase()}:`);
      console.log(`   📄 Files: ${files.length}`);
      console.log(`   🗑️  Removable console statements: ${removableConsole}`);
      console.log(`   🛡️  console.error statements (preserved): ${totalErrors}`);
      console.log(`   💾 Estimated performance improvement: ${this.estimatePerformanceImpact(removableConsole)}`);
    }
  }

  /**
   * Estimate performance impact
   */
  estimatePerformanceImpact(consoleCount) {
    if (consoleCount === 0) return 'None';
    if (consoleCount < 50) return 'Low';
    if (consoleCount < 200) return 'Medium';
    if (consoleCount < 500) return 'High';
    return 'Very High';
  }

  /**
   * Get all JavaScript files in a directory
   */
  getJavaScriptFiles(directory) {
    const files = [];
    
    if (!fs.existsSync(directory)) {
      return files;
    }

    const traverse = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (item.endsWith('.js') && !item.includes('.backup') && !item.includes('.temp')) {
          files.push(fullPath);
        }
      }
    };

    traverse(directory);
    return files;
  }

  /**
   * Ask for user confirmation
   */
  async confirmExecution() {
    console.log('\n⚠️  SAFETY CONFIRMATION:');
    console.log('This script will:');
    console.log('✅ Create timestamped backups of all files');
    console.log('✅ Remove console.log, console.info, console.warn, console.debug');
    console.log('✅ Preserve all console.error statements');
    console.log('✅ Validate syntax after each file modification');
    console.log('✅ Provide detailed reporting and rollback capabilities');
    
    // For automated execution, return true
    // In interactive mode, you could add readline here
    return true;
  }

  /**
   * Process a directory
   */
  async processDirectory(directory) {
    console.log(`\n🔧 Processing ${directory.toUpperCase()} directory...`);
    
    const files = this.getJavaScriptFiles(directory);
    let processed = 0;
    
    for (const file of files) {
      try {
        await this.processFile(file);
        processed++;
        
        // Progress indicator
        if (processed % 10 === 0) {
          console.log(`   📊 Progress: ${processed}/${files.length} files processed`);
        }
      } catch (error) {
        this.errors.push({ file, error: error.message });
        console.log(`   ❌ Error processing ${file}: ${error.message}`);
      }
    }
    
    console.log(`✅ ${directory.toUpperCase()} completed: ${processed}/${files.length} files processed`);
  }

  /**
   * Process a single file
   */
  async processFile(filePath) {
    // Create backup
    this.createBackup(filePath);
    
    // Read original content
    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // Analyze console statements
    const analysis = this.analyzeConsoleStatements(originalContent);
    
    if (analysis.removableCount === 0) {
      // No console statements to remove
      return;
    }

    // Clean the content
    const cleanedContent = this.cleanConsoleStatements(originalContent);
    
    // Validate syntax
    if (!this.validateSyntax(cleanedContent, filePath)) {
      throw new Error('Syntax validation failed');
    }

    // Write cleaned content
    fs.writeFileSync(filePath, cleanedContent, 'utf8');
    
    // Track results
    this.processedFiles.push({
      path: filePath,
      removed: analysis.removableCount,
      preserved: analysis.preservableCount
    });
    
    this.totalRemoved += analysis.removableCount;
    this.totalPreserved += analysis.preservableCount;
  }

  /**
   * Create backup of file
   */
  createBackup(filePath) {
    const backupPath = `${filePath}.backup.${this.backupTimestamp}`;
    fs.copyFileSync(filePath, backupPath);
  }

  /**
   * Analyze console statements in content
   */
  analyzeConsoleStatements(content) {
    const lines = content.split('\n');
    let removableCount = 0;
    let preservableCount = 0;
    
    for (const line of lines) {
      if (this.containsConsoleStatement(line)) {
        if (this.shouldPreserveLine(line)) {
          preservableCount++;
        } else {
          removableCount++;
        }
      }
    }
    
    return { removableCount, preservableCount };
  }

  /**
   * Check if line contains console statement
   */
  containsConsoleStatement(line) {
    return /console\.(log|info|warn|debug|error)/.test(line);
  }

  /**
   * Determine if line should be preserved
   */
  shouldPreserveLine(line) {
    // Always preserve console.error
    if (/console\.error/.test(line)) {
      return true;
    }
    
    return false;
  }

  /**
   * Clean console statements from content
   */
  cleanConsoleStatements(content) {
    const lines = content.split('\n');
    const cleanedLines = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // Check if this line starts a removable console statement
      if (trimmedLine.match(/^console\.(log|info|warn|debug)\s*\(/)) {
        // Find the complete console statement (handle multi-line)
        const consoleEnd = this.findConsoleStatementEnd(lines, i);
        
        // Skip all lines of this console statement
        i = consoleEnd + 1;
        continue;
      }
      
      // Check if line contains inline console statement
      if (this.containsConsoleStatement(line) && !this.shouldPreserveLine(line)) {
        // Remove inline console statements but keep the rest of the line
        const cleanedLine = this.removeInlineConsole(line);
        if (cleanedLine.trim()) {
          cleanedLines.push(cleanedLine);
        }
      } else {
        // Keep the line as-is
        cleanedLines.push(line);
      }
      
      i++;
    }

    return cleanedLines.join('\n');
  }

  /**
   * Find the end of a multi-line console statement
   */
  findConsoleStatementEnd(lines, startIndex) {
    let bracketCount = 0;
    let inString = false;
    let stringChar = '';
    
    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i];
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        
        if (!inString) {
          if (char === '"' || char === "'" || char === '`') {
            inString = true;
            stringChar = char;
          } else if (char === '(') {
            bracketCount++;
          } else if (char === ')') {
            bracketCount--;
            if (bracketCount === 0) {
              return i;
            }
          }
        } else {
          if (char === stringChar && line[j - 1] !== '\\') {
            inString = false;
            stringChar = '';
          }
        }
      }
    }
    
    return startIndex; // Fallback to single line
  }

  /**
   * Remove inline console statements
   */
  removeInlineConsole(line) {
    return line.replace(/console\.(log|info|warn|debug)\([^)]*\);?/g, '').trim();
  }

  /**
   * Validate JavaScript syntax without execution
   */
  validateSyntax(content, filePath) {
    try {
      // Use Function constructor for syntax validation without execution
      new Function(content);
      return true;
    } catch (error) {
      console.log(`   ⚠️  Syntax validation failed for ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate final report
   */
  generateFinalReport() {
    console.log('\n📊 COMPREHENSIVE CLEANUP REPORT');
    console.log('================================');
    console.log(`📁 Directories processed: ${this.targetDirectories.length}`);
    console.log(`📄 Files processed: ${this.processedFiles.length}`);
    console.log(`🗑️  Total console statements removed: ${this.totalRemoved}`);
    console.log(`🛡️  Total console.error statements preserved: ${this.totalPreserved}`);
    console.log(`❌ Files with errors: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.errors.forEach(error => {
        console.log(`   ${error.file}: ${error.error}`);
      });
    }
    
    console.log('\n🎯 PERFORMANCE IMPACT:');
    console.log(`   📈 Estimated JavaScript execution improvement: ${this.estimatePerformanceImpact(this.totalRemoved)}`);
    console.log(`   🚀 Reduced console output overhead in production`);
    console.log(`   📊 Improved Lighthouse Performance scores`);
    
    console.log('\n🔄 ROLLBACK INSTRUCTIONS:');
    console.log(`   To rollback changes, run:`);
    console.log(`   find . -name "*.backup.${this.backupTimestamp}" -exec bash -c 'mv "$1" "\${1%.backup.*}"' _ {} \\;`);
    
    console.log('\n✨ Cleanup completed successfully!');
  }
}

// Execute if run directly
if (require.main === module) {
  const cleanup = new ComprehensiveConsoleCleanup();
  cleanup.run().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ComprehensiveConsoleCleanup;
