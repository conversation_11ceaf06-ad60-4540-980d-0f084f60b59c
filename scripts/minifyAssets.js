#!/usr/bin/env node

/**
 * Asset Minification Script for PandaPayroll
 * Minifies CSS and JavaScript files to achieve 26 KiB savings
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { minify: minifyJS } = require('terser');
const CleanCSS = require('clean-css');

class AssetMinifier {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.totalSavings = 0;
    this.processedFiles = 0;
    this.cleanCSS = new CleanCSS({
      level: 2, // Advanced optimizations
      returnPromise: true,
      sourceMap: false,
      compatibility: 'ie8',
      format: {
        breaks: {
          afterAtRule: false,
          afterBlockBegins: false,
          afterBlockEnds: false,
          afterComment: false,
          afterProperty: false,
          afterRuleBegins: false,
          afterRuleEnds: false,
          beforeBlockEnds: false,
          betweenSelectors: false
        },
        indentBy: 0,
        indentWith: '',
        spaces: {
          aroundSelectorRelation: false,
          beforeBlockBegins: false,
          beforeValue: false
        },
        wrapAt: false
      }
    });
  }

  async minifyAssets() {
    console.log('🗜️  Starting asset minification...\n');
    
    // Step 1: Minify CSS files
    await this.minifyCSS();
    
    // Step 2: Minify JavaScript files
    await this.minifyJavaScript();
    
    // Step 3: Generate report
    this.generateReport();
    
    // Step 4: Update cache policy
    await this.updateCachePolicy();
  }

  async minifyCSS() {
    console.log('🎨 Minifying CSS files...');
    
    const cssPattern = path.join(this.projectRoot, 'public/css/**/*.css');
    const cssFiles = glob.sync(cssPattern);
    
    for (const file of cssFiles) {
      try {
        // Skip already minified files
        if (file.includes('.min.')) {
          continue;
        }
        
        const originalContent = fs.readFileSync(file, 'utf8');
        const originalSize = originalContent.length;
        
        // Minify CSS
        const result = await this.cleanCSS.minify(originalContent);
        
        if (result.errors.length > 0) {
          console.warn(`⚠️  CSS errors in ${file}:`, result.errors);
          continue;
        }
        
        const minifiedContent = result.styles;
        const minifiedSize = minifiedContent.length;
        const savings = originalSize - minifiedSize;
        
        if (savings > 0) {
          // Create minified version
          const minifiedPath = file.replace('.css', '.min.css');
          fs.writeFileSync(minifiedPath, minifiedContent);
          
          // Update original file with minified content
          fs.writeFileSync(file, minifiedContent);
          
          this.totalSavings += savings;
          this.processedFiles++;
          
          console.log(`  ✅ ${path.basename(file)}: ${(savings / 1024).toFixed(2)} KB saved`);
        }
        
      } catch (error) {
        console.error(`❌ Error minifying ${file}:`, error.message);
      }
    }
  }

  async minifyJavaScript() {
    console.log('\n📜 Minifying JavaScript files...');
    
    const jsPattern = path.join(this.projectRoot, 'public/js/**/*.js');
    const jsFiles = glob.sync(jsPattern);
    
    for (const file of jsFiles) {
      try {
        // Skip already minified files and specific files
        if (file.includes('.min.') || file.includes('lazyLoading.js')) {
          continue;
        }
        
        const originalContent = fs.readFileSync(file, 'utf8');
        const originalSize = originalContent.length;
        
        // Minify JavaScript with modern syntax support
        const result = await minifyJS(originalContent, {
          ecma: 2020, // Support modern JavaScript
          parse: {
            ecma: 2020
          },
          compress: {
            ecma: 2020,
            drop_console: false, // Keep console logs for debugging
            drop_debugger: true,
            pure_funcs: ['console.debug'],
            passes: 2,
            unsafe: false,
            conditionals: true,
            dead_code: true,
            evaluate: true,
            if_return: true,
            join_vars: true,
            reduce_vars: true,
            sequences: true,
            side_effects: true,
            switches: true,
            unused: true
          },
          mangle: {
            reserved: ['$', 'jQuery', 'window', 'document'], // Preserve common globals
            safari10: true
          },
          format: {
            comments: false,
            ecma: 2020
          },
          sourceMap: false,
          toplevel: false
        });
        
        if (result.error) {
          console.warn(`⚠️  JS error in ${file}:`, result.error);
          continue;
        }
        
        const minifiedContent = result.code;
        const minifiedSize = minifiedContent.length;
        const savings = originalSize - minifiedSize;
        
        if (savings > 0) {
          // Create minified version
          const minifiedPath = file.replace('.js', '.min.js');
          fs.writeFileSync(minifiedPath, minifiedContent);
          
          // Update original file with minified content
          fs.writeFileSync(file, minifiedContent);
          
          this.totalSavings += savings;
          this.processedFiles++;
          
          console.log(`  ✅ ${path.basename(file)}: ${(savings / 1024).toFixed(2)} KB saved`);
        }
        
      } catch (error) {
        console.warn(`❌ Error minifying ${file}:`, error.message);
        // Try with more lenient settings for problematic files
        try {
          const lenientResult = await minifyJS(originalContent, {
            ecma: 5, // Fall back to ES5
            compress: {
              drop_console: false,
              passes: 1
            },
            mangle: false,
            format: {
              comments: false
            }
          });

          if (!lenientResult.error) {
            const minifiedContent = lenientResult.code;
            const minifiedSize = minifiedContent.length;
            const savings = originalSize - minifiedSize;

            if (savings > 0) {
              const minifiedPath = file.replace('.js', '.min.js');
              fs.writeFileSync(minifiedPath, minifiedContent);
              fs.writeFileSync(file, minifiedContent);

              this.totalSavings += savings;
              this.processedFiles++;

              console.log(`  ⚠️  ${path.basename(file)}: ${(savings / 1024).toFixed(2)} KB saved (lenient mode)`);
            }
          }
        } catch (lenientError) {
          console.warn(`❌ Failed even with lenient settings for ${file}:`, lenientError.message);
        }
      }
    }
  }

  generateReport() {
    console.log('\n📊 ASSET MINIFICATION REPORT');
    console.log('='.repeat(50));
    console.log(`📁 Files processed: ${this.processedFiles}`);
    console.log(`💾 Total savings: ${(this.totalSavings / 1024).toFixed(2)} KB`);
    console.log(`🎯 Target savings: 26 KB`);
    
    if (this.totalSavings >= 26 * 1024) {
      console.log('✅ Target achieved!');
    } else {
      console.log(`⚠️  Need ${((26 * 1024 - this.totalSavings) / 1024).toFixed(2)} KB more savings`);
    }
  }

  async updateCachePolicy() {
    console.log('\n🗄️  Updating cache policy...');
    
    // Create optimized cache policy middleware
    const cachePolicy = `
/**
 * Optimized Cache Policy for PandaPayroll Assets
 * Generated by minifyAssets.js
 */

const express = require('express');

const optimizedCachePolicy = () => {
  return (req, res, next) => {
    const url = req.url;
    
    // Long-term caching for minified assets
    if (url.match(/\\.(min\\.css|min\\.js)$/)) {
      res.set('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
      res.set('Expires', new Date(Date.now() + 31536000000).toUTCString());
    }
    // Medium-term caching for regular assets
    else if (url.match(/\\.(css|js)$/)) {
      res.set('Cache-Control', 'public, max-age=86400'); // 1 day
      res.set('Expires', new Date(Date.now() + 86400000).toUTCString());
    }
    // Long-term caching for images
    else if (url.match(/\\.(png|jpg|jpeg|gif|svg|ico|webp)$/)) {
      res.set('Cache-Control', 'public, max-age=2592000'); // 30 days
      res.set('Expires', new Date(Date.now() + 2592000000).toUTCString());
    }
    // Medium-term caching for fonts
    else if (url.match(/\\.(woff|woff2|ttf|eot)$/)) {
      res.set('Cache-Control', 'public, max-age=2592000'); // 30 days
      res.set('Expires', new Date(Date.now() + 2592000000).toUTCString());
    }
    
    // Add ETag for all static assets
    if (url.match(/\\.(css|js|png|jpg|jpeg|gif|svg|ico|webp|woff|woff2|ttf|eot)$/)) {
      const etag = require('crypto').createHash('md5').update(url).digest('hex');
      res.set('ETag', '"' + etag + '"');
      
      // Check if client has cached version
      if (req.headers['if-none-match'] === '"' + etag + '"') {
        return res.status(304).end();
      }
    }
    
    next();
  };
};

module.exports = optimizedCachePolicy;
`;

    const cachePolicyPath = path.join(this.projectRoot, 'middleware/optimizedCachePolicy.js');
    fs.writeFileSync(cachePolicyPath, cachePolicy);
    
    console.log(`✅ Created optimized cache policy: ${cachePolicyPath}`);
    
    // Create instructions for updating app.js
    const instructions = `
📝 TO COMPLETE CACHE POLICY OPTIMIZATION:

1. Add to app.js imports:
   const optimizedCachePolicy = require('./middleware/optimizedCachePolicy');

2. Add before static file serving:
   app.use(optimizedCachePolicy());

3. This will provide:
   - 1 year caching for minified assets
   - 1 day caching for regular CSS/JS
   - 30 days caching for images and fonts
   - ETag support for conditional requests
   - 304 Not Modified responses for cached content
`;

    console.log(instructions);
  }
}

// Run the minifier
if (require.main === module) {
  const minifier = new AssetMinifier();
  minifier.minifyAssets().catch(console.error);
}

module.exports = AssetMinifier;
