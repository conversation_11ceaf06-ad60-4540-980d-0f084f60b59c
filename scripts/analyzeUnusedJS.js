#!/usr/bin/env node

/**
 * Analyze and identify unused JavaScript files in PandaPayroll
 * This script will help us achieve the 286 KiB savings from removing unused JS
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class UnusedJSAnalyzer {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.jsFiles = new Set();
    this.referencedFiles = new Set();
    this.templateFiles = [];
    this.unusedFiles = [];
    this.totalSavings = 0;
  }

  async analyze() {
    console.log('🔍 Analyzing JavaScript usage in PandaPayroll...\n');
    
    // Step 1: Find all JS files
    await this.findAllJSFiles();
    
    // Step 2: Find all template files
    await this.findTemplateFiles();
    
    // Step 3: Scan templates for JS references
    await this.scanTemplatesForJSReferences();
    
    // Step 4: Identify unused files
    await this.identifyUnusedFiles();
    
    // Step 5: Calculate potential savings
    await this.calculateSavings();
    
    // Step 6: Generate report
    this.generateReport();
    
    // Step 7: Create removal script
    await this.createRemovalScript();
  }

  async findAllJSFiles() {
    const jsPattern = path.join(this.projectRoot, 'public/js/**/*.js');
    const files = glob.sync(jsPattern);
    
    files.forEach(file => {
      const relativePath = path.relative(this.projectRoot, file);
      this.jsFiles.add(relativePath);
    });
    
    console.log(`📁 Found ${this.jsFiles.size} JavaScript files`);
  }

  async findTemplateFiles() {
    const templatePatterns = [
      path.join(this.projectRoot, 'views/**/*.ejs'),
      path.join(this.projectRoot, 'views/**/*.html'),
      path.join(this.projectRoot, 'public/**/*.html')
    ];
    
    templatePatterns.forEach(pattern => {
      const files = glob.sync(pattern);
      this.templateFiles.push(...files);
    });
    
    console.log(`📄 Found ${this.templateFiles.length} template files`);
  }

  async scanTemplatesForJSReferences() {
    console.log('🔍 Scanning templates for JavaScript references...');
    
    for (const templateFile of this.templateFiles) {
      try {
        const content = fs.readFileSync(templateFile, 'utf8');
        
        // Look for script src references
        const scriptMatches = content.match(/src\s*=\s*["']([^"']*\.js[^"']*)["']/gi);
        
        if (scriptMatches) {
          scriptMatches.forEach(match => {
            const srcMatch = match.match(/src\s*=\s*["']([^"']*)["']/i);
            if (srcMatch) {
              let jsPath = srcMatch[1];
              
              // Normalize path
              if (jsPath.startsWith('/js/')) {
                jsPath = 'public' + jsPath;
              } else if (jsPath.startsWith('./js/')) {
                jsPath = 'public/js/' + jsPath.substring(5);
              } else if (!jsPath.startsWith('http') && jsPath.includes('.js')) {
                // Local file
                if (!jsPath.startsWith('public/')) {
                  jsPath = 'public/js/' + path.basename(jsPath);
                }
              }
              
              if (!jsPath.startsWith('http')) {
                this.referencedFiles.add(jsPath);
              }
            }
          });
        }
        
        // Look for dynamic script loading
        const dynamicMatches = content.match(/loadScript\s*\(\s*["']([^"']*\.js[^"']*)["']/gi);
        if (dynamicMatches) {
          dynamicMatches.forEach(match => {
            const pathMatch = match.match(/["']([^"']*)["']/);
            if (pathMatch) {
              let jsPath = pathMatch[1];
              if (jsPath.startsWith('/js/')) {
                jsPath = 'public' + jsPath;
              }
              this.referencedFiles.add(jsPath);
            }
          });
        }
        
      } catch (error) {
        console.warn(`⚠️  Could not read ${templateFile}: ${error.message}`);
      }
    }
    
    console.log(`📎 Found ${this.referencedFiles.size} referenced JavaScript files`);
  }

  async identifyUnusedFiles() {
    // Core files that should never be removed (even if not directly referenced)
    const coreFiles = new Set([
      'public/js/notifications.js',
      'public/js/lazyLoading.js',
      'public/js/sessionManager.js'
    ]);
    
    // Files that are likely unused based on analysis
    const likelyUnused = [
      'public/js/script.js', // Generic script, likely unused
      'public/js/main.js', // Only has basic sidebar functionality
      'public/js/include.js', // For HTML includes, not used in EJS
      'public/js/direct-modal-fix.js', // Specific fix, may be obsolete
      'public/js/dob-validation.js', // Specific validation, check if used
      'public/js/frequency-tab-helper.js', // Helper, check usage
      'public/js/pagination-helper.js', // Helper, check usage
      'public/js/progress-tab-helper.js', // Helper, check usage
      'public/js/search.js', // Generic search, check if used
      'public/js/sic-codes.js', // Specific functionality, check usage
      'public/js/trial-restriction.js', // May be handled server-side now
      'public/js/xero-refresh-worker.js', // Background worker, check if needed
    ];
    
    this.jsFiles.forEach(file => {
      if (!this.referencedFiles.has(file) && !coreFiles.has(file)) {
        this.unusedFiles.push({
          path: file,
          likely: likelyUnused.includes(file),
          size: this.getFileSize(file)
        });
      }
    });
    
    console.log(`🗑️  Identified ${this.unusedFiles.length} potentially unused files`);
  }

  getFileSize(filePath) {
    try {
      const fullPath = path.join(this.projectRoot, filePath);
      const stats = fs.statSync(fullPath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  async calculateSavings() {
    this.totalSavings = this.unusedFiles.reduce((total, file) => total + file.size, 0);
    console.log(`💾 Potential savings: ${(this.totalSavings / 1024).toFixed(2)} KB`);
  }

  generateReport() {
    console.log('\n📊 UNUSED JAVASCRIPT ANALYSIS REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n📁 Total JS files: ${this.jsFiles.size}`);
    console.log(`📎 Referenced files: ${this.referencedFiles.size}`);
    console.log(`🗑️  Unused files: ${this.unusedFiles.length}`);
    console.log(`💾 Total potential savings: ${(this.totalSavings / 1024).toFixed(2)} KB`);
    
    if (this.unusedFiles.length > 0) {
      console.log('\n🗑️  UNUSED FILES:');
      this.unusedFiles
        .sort((a, b) => b.size - a.size)
        .forEach(file => {
          const sizeKB = (file.size / 1024).toFixed(2);
          const status = file.likely ? '🔴 LIKELY SAFE TO REMOVE' : '🟡 REVIEW NEEDED';
          console.log(`  ${status} - ${file.path} (${sizeKB} KB)`);
        });
    }
    
    console.log('\n📎 REFERENCED FILES:');
    Array.from(this.referencedFiles)
      .sort()
      .forEach(file => {
        console.log(`  ✅ ${file}`);
      });
  }

  async createRemovalScript() {
    const scriptContent = `#!/usr/bin/env node

/**
 * Remove unused JavaScript files
 * Generated by analyzeUnusedJS.js
 * 
 * IMPORTANT: Review this script before running!
 */

const fs = require('fs');
const path = require('path');

const filesToRemove = [
${this.unusedFiles
  .filter(file => file.likely)
  .map(file => `  '${file.path}', // ${(file.size / 1024).toFixed(2)} KB`)
  .join('\n')}
];

const filesToReview = [
${this.unusedFiles
  .filter(file => !file.likely)
  .map(file => `  '${file.path}', // ${(file.size / 1024).toFixed(2)} KB - REVIEW NEEDED`)
  .join('\n')}
];

console.log('🗑️  Removing unused JavaScript files...');

let totalSaved = 0;

filesToRemove.forEach(file => {
  try {
    const fullPath = path.join(__dirname, '..', file);
    const stats = fs.statSync(fullPath);
    fs.unlinkSync(fullPath);
    totalSaved += stats.size;
    console.log(\`✅ Removed: \${file} (\${(stats.size / 1024).toFixed(2)} KB)\`);
  } catch (error) {
    console.warn(\`⚠️  Could not remove \${file}: \${error.message}\`);
  }
});

console.log(\`\\n💾 Total space saved: \${(totalSaved / 1024).toFixed(2)} KB\`);

if (filesToReview.length > 0) {
  console.log('\\n🟡 FILES THAT NEED MANUAL REVIEW:');
  filesToReview.forEach(file => {
    console.log(\`  - \${file}\`);
  });
}
`;

    const scriptPath = path.join(this.projectRoot, 'scripts/removeUnusedJS.js');
    fs.writeFileSync(scriptPath, scriptContent);
    fs.chmodSync(scriptPath, '755');
    
    console.log(`\n📝 Created removal script: ${scriptPath}`);
    console.log('⚠️  IMPORTANT: Review the script before running it!');
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new UnusedJSAnalyzer();
  analyzer.analyze().catch(console.error);
}

module.exports = UnusedJSAnalyzer;
