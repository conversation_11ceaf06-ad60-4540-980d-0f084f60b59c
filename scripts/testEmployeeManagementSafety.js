#!/usr/bin/env node

/**
 * Safety Test Script for Employee Management Route
 * 
 * This script performs comprehensive testing to ensure the employeeManagement.js
 * route continues to function correctly after console.log removal.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.join(__dirname, '..');
const TARGET_FILE = path.join(PROJECT_ROOT, 'routes/employeeManagement.js');

class EmployeeManagementSafetyTester {
  constructor() {
    this.testResults = {
      syntaxCheck: false,
      routeStructure: false,
      functionIntegrity: false,
      errorHandling: false,
      middlewareChains: false,
      exportStructure: false
    };
  }

  /**
   * Run all safety tests
   */
  async runAllTests() {
    console.log('🧪 Running comprehensive safety tests for employeeManagement.js');
    console.log('=' .repeat(60));

    try {
      await this.testSyntaxValidity();
      await this.testRouteStructure();
      await this.testFunctionIntegrity();
      await this.testErrorHandling();
      await this.testMiddlewareChains();
      await this.testExportStructure();
      
      this.reportResults();
      
      if (this.allTestsPassed()) {
        console.log('✅ All safety tests PASSED - File is safe to use');
        return true;
      } else {
        console.log('❌ Some safety tests FAILED - Review required');
        return false;
      }
      
    } catch (error) {
      console.error('❌ Safety testing failed:', error.message);
      return false;
    }
  }

  /**
   * Test JavaScript syntax validity
   */
  async testSyntaxValidity() {
    console.log('🔍 Testing syntax validity...');
    try {
      execSync(`node -c "${TARGET_FILE}"`, { stdio: 'pipe' });
      this.testResults.syntaxCheck = true;
      console.log('✅ Syntax check passed');
    } catch (error) {
      console.log('❌ Syntax check failed:', error.message);
      throw error;
    }
  }

  /**
   * Test route structure integrity
   */
  async testRouteStructure() {
    console.log('🔍 Testing route structure...');
    const content = fs.readFileSync(TARGET_FILE, 'utf8');
    
    // Check for essential route patterns
    const routePatterns = [
      /router\.get\s*\(/g,
      /router\.post\s*\(/g,
      /router\.put\s*\(/g,
      /router\.delete\s*\(/g,
      /module\.exports\s*=\s*router/g
    ];
    
    let routeCount = 0;
    routePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        routeCount += matches.length;
      }
    });
    
    if (routeCount > 0) {
      this.testResults.routeStructure = true;
      console.log(`✅ Route structure intact (${routeCount} routes found)`);
    } else {
      console.log('❌ Route structure compromised');
    }
  }

  /**
   * Test function integrity
   */
  async testFunctionIntegrity() {
    console.log('🔍 Testing function integrity...');
    const content = fs.readFileSync(TARGET_FILE, 'utf8');
    
    // Check for critical function patterns
    const functionPatterns = [
      /async\s+\(/g,
      /function\s+\w+/g,
      /=>\s*{/g,
      /try\s*{/g,
      /catch\s*\(/g
    ];
    
    let functionCount = 0;
    functionPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        functionCount += matches.length;
      }
    });
    
    // Check for balanced braces
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    
    if (functionCount > 0 && openBraces === closeBraces) {
      this.testResults.functionIntegrity = true;
      console.log(`✅ Function integrity maintained (${functionCount} functions, braces balanced)`);
    } else {
      console.log(`❌ Function integrity compromised (braces: ${openBraces}/${closeBraces})`);
    }
  }

  /**
   * Test error handling preservation
   */
  async testErrorHandling() {
    console.log('🔍 Testing error handling preservation...');
    const content = fs.readFileSync(TARGET_FILE, 'utf8');
    
    // Count console.error statements (should be preserved)
    const errorStatements = (content.match(/console\.error/g) || []).length;
    
    // Count try-catch blocks
    const tryBlocks = (content.match(/try\s*{/g) || []).length;
    const catchBlocks = (content.match(/catch\s*\(/g) || []).length;
    
    // Check for error response patterns
    const errorResponses = (content.match(/res\.status\(4\d\d\)|res\.status\(5\d\d\)/g) || []).length;
    
    if (errorStatements > 0 && tryBlocks === catchBlocks && errorResponses > 0) {
      this.testResults.errorHandling = true;
      console.log(`✅ Error handling preserved (${errorStatements} console.error, ${tryBlocks} try-catch blocks)`);
    } else {
      console.log(`❌ Error handling may be compromised`);
    }
  }

  /**
   * Test middleware chain integrity
   */
  async testMiddlewareChains() {
    console.log('🔍 Testing middleware chains...');
    const content = fs.readFileSync(TARGET_FILE, 'utf8');
    
    // Check for middleware patterns
    const middlewarePatterns = [
      /router\.use\s*\(/g,
      /ensureAuthenticated/g,
      /upload\./g,
      /next\(\)/g
    ];
    
    let middlewareCount = 0;
    middlewarePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        middlewareCount += matches.length;
      }
    });
    
    if (middlewareCount > 0) {
      this.testResults.middlewareChains = true;
      console.log(`✅ Middleware chains intact (${middlewareCount} middleware references)`);
    } else {
      console.log('❌ Middleware chains may be compromised');
    }
  }

  /**
   * Test export structure
   */
  async testExportStructure() {
    console.log('🔍 Testing export structure...');
    const content = fs.readFileSync(TARGET_FILE, 'utf8');
    
    // Check for proper module export
    const hasExport = /module\.exports\s*=\s*router/.test(content);
    
    if (hasExport) {
      this.testResults.exportStructure = true;
      console.log('✅ Export structure intact');
    } else {
      console.log('❌ Export structure compromised');
    }
  }

  /**
   * Check if all tests passed
   */
  allTestsPassed() {
    return Object.values(this.testResults).every(result => result === true);
  }

  /**
   * Report test results
   */
  reportResults() {
    console.log('\n📊 Safety Test Results:');
    console.log('─'.repeat(40));
    
    Object.entries(this.testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
      console.log(`${status} ${testName}`);
    });
    
    const passedCount = Object.values(this.testResults).filter(r => r).length;
    const totalCount = Object.keys(this.testResults).length;
    
    console.log('─'.repeat(40));
    console.log(`📈 Overall: ${passedCount}/${totalCount} tests passed`);
  }
}

// Execute if run directly
if (require.main === module) {
  const tester = new EmployeeManagementSafetyTester();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = EmployeeManagementSafetyTester;
