#!/usr/bin/env node

/**
 * Master Safety Script for Employee Management Console.log Cleanup
 * 
 * This script orchestrates the entire process of safely removing console.log
 * statements from routes/employeeManagement.js with comprehensive safety checks.
 * 
 * Process:
 * 1. Pre-cleanup safety tests
 * 2. Console.log removal with backup
 * 3. Post-cleanup safety tests
 * 4. Application startup verification
 * 5. Rollback if any issues detected
 */

const path = require('path');
const SafeConsoleLogRemover = require('./cleanEmployeeManagementLogging');
const EmployeeManagementSafetyTester = require('./testEmployeeManagementSafety');

class MasterSafetyOrchestrator {
  constructor() {
    this.remover = new SafeConsoleLogRemover();
    this.tester = new EmployeeManagementSafetyTester();
    this.backupPath = '';
  }

  /**
   * Execute the complete safe cleanup process
   */
  async execute() {
    console.log('🚀 Starting Master Safety Orchestrator for Employee Management Cleanup');
    console.log('=' .repeat(70));
    console.log('📋 Process Overview:');
    console.log('   1. Pre-cleanup safety verification');
    console.log('   2. Console.log removal with backup');
    console.log('   3. Post-cleanup safety verification');
    console.log('   4. Application startup test');
    console.log('   5. Automatic rollback if issues detected');
    console.log('=' .repeat(70));

    try {
      // Step 1: Pre-cleanup safety tests
      console.log('\n🔍 STEP 1: Pre-cleanup Safety Verification');
      console.log('─'.repeat(50));
      const preTestsPassed = await this.tester.runAllTests();
      
      if (!preTestsPassed) {
        throw new Error('Pre-cleanup safety tests failed. File may already have issues.');
      }
      
      console.log('✅ Pre-cleanup safety verification PASSED');

      // Step 2: Console.log removal
      console.log('\n🔧 STEP 2: Console.log Removal Process');
      console.log('─'.repeat(50));
      await this.remover.execute();
      
      console.log('✅ Console.log removal completed');

      // Step 3: Post-cleanup safety tests
      console.log('\n🔍 STEP 3: Post-cleanup Safety Verification');
      console.log('─'.repeat(50));
      const postTestsPassed = await this.tester.runAllTests();
      
      if (!postTestsPassed) {
        throw new Error('Post-cleanup safety tests failed. Rolling back changes.');
      }
      
      console.log('✅ Post-cleanup safety verification PASSED');

      // Step 4: Final application test
      console.log('\n🧪 STEP 4: Final Application Startup Test');
      console.log('─'.repeat(50));
      await this.testFullApplicationStartup();
      
      console.log('✅ Full application startup test PASSED');

      // Step 5: Success summary
      this.reportSuccess();

    } catch (error) {
      console.error('\n❌ PROCESS FAILED:', error.message);
      await this.handleFailure();
    }
  }

  /**
   * Test full application startup
   */
  async testFullApplicationStartup() {
    const { execSync } = require('child_process');
    const PROJECT_ROOT = path.join(__dirname, '..');
    
    try {
      console.log('🧪 Testing full application startup...');
      
      // Test syntax of main app file
      execSync('node -c app.js', { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe',
        timeout: 15000
      });
      
      console.log('✅ Application syntax validation passed');
      
      // Test that all required modules can be loaded
      execSync('node -e "require(\'./routes/employeeManagement\')"', {
        cwd: PROJECT_ROOT,
        stdio: 'pipe',
        timeout: 10000
      });
      
      console.log('✅ Employee management route module loads successfully');
      
    } catch (error) {
      throw new Error(`Application startup test failed: ${error.message}`);
    }
  }

  /**
   * Handle failure scenarios
   */
  async handleFailure() {
    console.log('\n🔄 FAILURE RECOVERY PROCESS');
    console.log('─'.repeat(50));
    
    try {
      // Attempt to rollback using the remover's rollback method
      await this.remover.rollback();
      
      // Verify rollback worked
      console.log('🔍 Verifying rollback...');
      const rollbackTestsPassed = await this.tester.runAllTests();
      
      if (rollbackTestsPassed) {
        console.log('✅ Rollback successful - original file restored');
        console.log('💡 Recommendation: Review the file manually before retrying');
      } else {
        console.log('❌ Rollback verification failed');
        console.log(`💾 Manual restore required from backup file`);
        console.log(`📁 Backup location: ${this.remover.backupPath}`);
      }
      
    } catch (rollbackError) {
      console.error('❌ Rollback failed:', rollbackError.message);
      console.log('🆘 CRITICAL: Manual intervention required');
      console.log(`📁 Restore from backup: ${this.remover.backupPath}`);
    }
  }

  /**
   * Report successful completion
   */
  reportSuccess() {
    console.log('\n🎉 SUCCESS: Employee Management Cleanup Completed!');
    console.log('=' .repeat(70));
    console.log('📊 Summary:');
    console.log(`   🗑️  Console.log statements removed: ${this.remover.removedCount}`);
    console.log(`   🛡️  Console.error statements preserved: ${this.remover.preservedErrorCount}`);
    console.log(`   💾 Backup saved: ${this.remover.backupPath}`);
    console.log('');
    console.log('✅ All safety checks passed');
    console.log('✅ Application startup verified');
    console.log('✅ Route functionality preserved');
    console.log('');
    console.log('🚀 Your application should now have improved performance!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('   1. Test your application thoroughly');
    console.log('   2. Run your test suite if available');
    console.log('   3. Monitor application logs for any issues');
    console.log('   4. Keep the backup file until you\'re confident everything works');
    console.log('');
    console.log('🔄 To restore from backup if needed:');
    console.log(`   cp "${this.remover.backupPath}" "routes/employeeManagement.js"`);
  }
}

// Execute if run directly
if (require.main === module) {
  const orchestrator = new MasterSafetyOrchestrator();
  orchestrator.execute().catch(error => {
    console.error('Fatal error in master orchestrator:', error);
    process.exit(1);
  });
}

module.exports = MasterSafetyOrchestrator;
