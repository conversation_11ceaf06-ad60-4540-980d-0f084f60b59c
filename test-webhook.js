// Test script to verify WhatsApp webhook endpoint accessibility
require('dotenv').config();

const axios = require('axios');

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const WEBHOOK_URL = `${BASE_URL}/api/whatsapp/webhook`;

async function testWebhookEndpoint() {
  console.log('Testing WhatsApp webhook endpoint...');
  console.log('URL:', WEBHOOK_URL);

  try {
    // Test 1: GET request for webhook verification (should work)
    console.log('\n=== Test 1: GET request (webhook verification) ===');
    try {
      const getResponse = await axios.get(WEBHOOK_URL, {
        params: {
          'hub.mode': 'subscribe',
          'hub.verify_token': process.env.WHATSAPP_VERIFY_TOKEN || 'test-verify-token',
          'hub.challenge': 'test-challenge-123'
        }
      });
      console.log('✅ GET request successful');
      console.log('Status:', getResponse.status);
      console.log('Response:', getResponse.data);
    } catch (error) {
      console.log('❌ GET request failed');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
    }

    // Test 2: POST request without signature (should work in development)
    console.log('\n=== Test 2: POST request without signature ===');
    const testPayload = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '***********',
              phone_number_id: 'test-phone-id'
            },
            messages: [{
              from: '***********',
              id: 'test-message-id',
              timestamp: Date.now().toString(),
              text: {
                body: 'test message'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    try {
      const postResponse = await axios.post(WEBHOOK_URL, JSON.stringify(testPayload), {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ POST request successful');
      console.log('Status:', postResponse.status);
      console.log('Response:', postResponse.data);
    } catch (error) {
      console.log('❌ POST request failed');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
      
      // Check if it's an authentication error
      if (error.response?.status === 401) {
        console.log('🔍 This appears to be an authentication error - the issue we were trying to fix');
      } else if (error.response?.status === 403) {
        console.log('🔍 This appears to be a CSRF protection error');
      }
    }

    // Test 3: POST request with invalid signature (should fail in production)
    console.log('\n=== Test 3: POST request with invalid signature ===');
    try {
      const postWithSigResponse = await axios.post(WEBHOOK_URL, JSON.stringify(testPayload), {
        headers: {
          'Content-Type': 'application/json',
          'x-hub-signature-256': 'sha256=invalid-signature'
        }
      });
      console.log('✅ POST with invalid signature successful (unexpected in production)');
      console.log('Status:', postWithSigResponse.status);
    } catch (error) {
      console.log('✅ POST with invalid signature failed (expected)');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('Test setup error:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testWebhookEndpoint()
    .then(() => {
      console.log('\n=== Test completed ===');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testWebhookEndpoint };
